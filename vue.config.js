const path = require("path");
const settings = require("./src/settings");
const webpack = require("webpack");
// 导入compression-webpack-plugin
const CompressionWebpackPlugin = require("compression-webpack-plugin");

// 定义压缩文件类型
const productionGzipExtensions = ["js", "css"];
// 是否生产环境
const isProduction = process.env.NODE_ENV === "production";

function resolve(dir) {
  return path.join(__dirname, dir);
}

const {
  title,
  svgFiles = ["src/assets/icons/svg/"],
  excludeJs = [],
  port,
  proxy,
  routerMode = "hash",
  publicPath = "/",
  enableMonitoring = false,
} = settings;
const name = title;

let pjson = require("./package.json");

// eslint-disable-next-line no-unused-vars
function getModuleVersion(moduleName) {
  return pjson.dependencies[moduleName].replace("^", "");
}

const devCDN = {
  css: [],
  js: [],
  // css: [`https://cdn.bootcss.com/nprogress/0.2.0/nprogress.min.css`],
  // js: [`https://cdn.bootcss.com/nprogress/0.2.0/nprogress.min.js`],
};
const proCDN = {
  css: [],
  js: [],
  // css: [`https://cdn.bootcss.com/nprogress/0.2.0/nprogress.min.css`],
  // js: [
  //   `https://cdn.bootcss.com/vue/${getModuleVersion("vue")}/vue.min.js`,
  //   `https://cdn.bootcss.com/vue-router/${getModuleVersion(
  //     "vue-router"
  //   )}/vue-router.min.js`,
  //   `https://cdn.bootcss.com/vuex/${getModuleVersion("vuex")}/vuex.min.js`,
  //   `https://cdn.bootcss.com/axios/${getModuleVersion("axios")}/axios.min.js`,
  //   `https://cdn.bootcss.com/element-ui/${getModuleVersion(
  //     "element-ui"
  //   )}/index.js`,
  //   // "https://cdn.bootcss.com/moment.js/${getModuleVersion("moment")}/moment.min.js",
  //   `https://cdn.bootcss.com/nprogress/0.2.0/nprogress.min.js`,
  // ],
};

module.exports = {
  publicPath: isProduction
    ? routerMode == "history"
      ? publicPath
      : "./"
    : "/",
  outputDir: "dist",
  assetsDir: "static",
  lintOnSave: !isProduction,
  productionSourceMap: false,
  configureWebpack: () => {
    let plugins = [
      // new webpack.ContextReplacementPlugin(/src/, /(?<!\.(zip|md|yml|txt))$/i),
      new webpack.IgnorePlugin(/\.(zip|md|yml|txt)$/i),
    ];
    if (isProduction) {
      plugins.push(
        new CompressionWebpackPlugin({
          filename: "[path][base].gz[query]",
          algorithm: "gzip",
          test: new RegExp("\\.(" + productionGzipExtensions.join("|") + ")$"),
          threshold: 10240,
          minRatio: 0.8,
        }),
        // eslint-disable-next-line no-useless-escape
        new webpack.ContextReplacementPlugin(/moment[\/\\]locale$/, /zh-cn/)
      );
    }

    return {
      name: name,
      resolve: {
        alias: {
          "@": resolve("src"),
          "@components": resolve("src/components"),
          "@plugin": resolve("src/plugin"),
          "@visual": resolve("src/plugin/visual"),
          "@processing": resolve("src/plugin/processing"),
          "@collect": resolve("src/plugin/collect"),
        },
      },
      devtool: !isProduction
        ? "cheap-module-eval-source-map"
        : "cheap-module-source-map",
      plugins: plugins,
      // externals: isProduction
      //   ? {
      //       vue: "Vue",
      //       "vue-router": "VueRouter",
      //       vuex: "Vuex",
      //       axios: "axios",
      //       "element-ui": "ELEMENT",
      //       // moment: "moment",
      //       nprogress: "NProgress",
      //     }
      //   : { nprogress: "NProgress" },
    };
  },
  chainWebpack: config => {
    config.plugin("preload").tap(() => [
      {
        rel: "preload",
        // to ignore runtime.js
        // https://github.com/vuejs/vue-cli/blob/dev/packages/@vue/cli-service/lib/config/app.js#L171
        fileBlacklist: [/\.map$/, /hot-update\.js$/, /runtime\..*\.js$/],
        include: "initial",
      },
    ]);
    config.plugins.delete("prefetch"); // TODO: need test
    // set svg-sprite-loader
    let svgLoader = config.module.rule("svg");
    var exclude = svgLoader.exclude;
    svgFiles.forEach(tmp => {
      exclude = exclude.add(resolve(tmp));
    });
    svgLoader.end();
    let svgSpriteLoader = config.module.rule("icons").test(/\.svg$/);
    var include = svgSpriteLoader.include;
    svgFiles.forEach(tmp => {
      include = include.add(resolve(tmp));
    });
    svgSpriteLoader.end();
    svgSpriteLoader
      .use("svg-sprite-loader")
      .loader("svg-sprite-loader")
      .options({
        symbolId: "visual-[name]",
      });
    svgSpriteLoader.end();

    config.plugin("provide").use(webpack.ProvidePlugin, [
      {
        $: "jquery",
        jquery: "jquery",
        jQuery: "jquery",
        "window.jQuery": "jquery",
      },
    ]);

    if (Array.isArray(excludeJs) && excludeJs.length > 0) {
      let jsLoader = config.module.rule("js");
      excludeJs.forEach(tmp => {
        jsLoader.exclude.add(resolve(tmp));
      });
      jsLoader.end();
    }

    config.module
      .rule("images")
      .test(/\.(png|jpe?g|gif|webp|psd|ico)(\?.*)?$/i)
      .use("url-loader")
      .loader("url-loader")
      .end();

    const oneOfsMap = config.module.rule("scss").oneOfs.store;
    oneOfsMap.forEach(item => {
      item
        .use("sass-resources-loader")
        .loader("sass-resources-loader")
        .options({
          // Provide path to the file with resources
          // Or array of paths
          resources: [
            "./src/themes/theme-vars.scss",
            "./src/themes/frame-directives.scss",
          ],
          hoistUseStatements: true,
        })
        .end();
    });

    config.plugin("html").tap(args => {
      args[0].cdn = devCDN;
      args[0].enableMonitoring = isProduction ? enableMonitoring : false;
      return args;
    });

    config.when(isProduction, config => {
      config.plugin("html").tap(args => {
        args[0].cdn = proCDN;
        return args;
      });
      config.optimization.minimizer("terser").tap(args => {
        args[0].terserOptions.compress = Object.assign(
          {},
          args[0].terserOptions.compress,
          {
            drop_console: true,
            pure_funcs: ["console.log"], // 移除console
          }
        );
        return args;
      });

      config
        .plugin("ScriptExtHtmlWebpackPlugin")
        .after("html")
        .use("script-ext-html-webpack-plugin", [
          {
            // `runtime` must same as runtimeChunk name. default is `runtime`
            inline: /runtime\..*\.js$/,
          },
        ])
        .end();
      config.optimization.splitChunks({
        chunks: "all",
        maxSize: 3000000,
        cacheGroups: {
          libs: {
            name: "chunk-libs",
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
            chunks: "initial", // only package third parties that are initially dependent
          },
          elementUI: {
            name: "chunk-elementUI", // split elementUI into a single package
            priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
            test: /[\\/]node_modules[\\/]_?element-ui(.*)/, // in order to adapt to cnpm
          },
          echarts: {
            name: "chunk-echarts",
            priority: 20,
            test: /[\\/]node_modules[\\/]_?echarts(.*)/,
          },
          commons: {
            name: "chunk-commons",
            test: resolve("src/components"), // can customize your rules
            minChunks: 2, //  minimum common number
            priority: 5,
          },
          framework: {
            name: "chunk-framework",
            test: resolve("src/framework"),
            minChunks: 3,
            priority: 5,
            reuseExistingChunk: true,
          },
          pluginModules: {
            name: "plugin-modules",
            test: resolve("src/plugin/visual/modules"),
            minChunks: 2,
            priority: 5,
            reuseExistingChunk: true,
          },
          pluginAlarm: {
            name: "plugin-alarm",
            test: resolve("src/plugin/alarm"),
            minChunks: 2,
            priority: 5,
            reuseExistingChunk: true,
          },
        },
      });
      config.optimization.runtimeChunk("single");
    });
  },
  devServer: {
    port: port,
    open: false,
    overlay: {
      warnings: false,
      errors: true,
    },
    proxy: proxy,
  },
  css: {
    loaderOptions: {
      less: {
        javascriptEnabled: true,
      },
      sass: {
        implementation: require("sass"),
      },
    },
    extract: isProduction
      ? {
          ignoreOrder: true,
        }
      : false,
  },
};
