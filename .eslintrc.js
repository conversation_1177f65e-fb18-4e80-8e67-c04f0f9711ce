module.exports = {
  root: true,
  env: {
    node: true,
  },
  extends: ["plugin:vue/essential", "eslint:recommended", "@vue/prettier"],
  parserOptions: {
    parser: "babel-eslint",
  },
  overrides: [
    {
      files: [
        "**/__tests__/*.{j,t}s?(x)",
        "**/tests/unit/**/*.spec.{j,t}s?(x)",
      ],
      env: {
        mocha: true,
      },
    },
  ],
  rules: {
    "no-console": process.env.NODE_ENV === "production" ? "error" : "off",
    "no-debugger": process.env.NODE_ENV === "production" ? "error" : "off",
    "no-async-promise-executor": 2,
    "no-prototype-builtins": process.env.NODE_ENV === "production" ? 0 : 1,
    "no-unused-vars": [2, { vars: "all", args: "none" }],
    "no-useless-escape": 0,
    "no-dupe-keys": 2,
    "no-empty": [2, { allowEmptyCatch: true }],
    // "space-before-function-paren": 0,
  },
};
