<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="icon" href="<%= BASE_URL %>favicon.ico" />
    <title><%= webpackConfig.name %></title>
    <link rel="stylesheet" type="text/css" href="<%= BASE_URL %>loading.css" />
    <link rel="stylesheet" type="text/css" href="<%= BASE_URL %>browser.css" />
    <!--[if lte IE 9]>
      <style>
        .sys-browser-bar {
          display: block;
        }
      </style>
    <![endif]-->

    <!-- 使用CDN的CSS文件 -->
    <% for (var i in htmlWebpackPlugin.options.cdn &&
    htmlWebpackPlugin.options.cdn.css) { %>
    <link href="<%= htmlWebpackPlugin.options.cdn.css[i] %>" rel="stylesheet" />
    <% } %>
    <script>
      var BASE_URL = "<%= BASE_URL %>".replace(/\/$/, "");
    </script>
  </head>

  <body>
    <noscript>
      <strong
        >We're sorry but dap doesn't work properly without JavaScript enabled.
        Please enable it to continue.</strong
      >
    </noscript>
    <div class="sys-browser-bar">
      <div class="sys-browser-download">
        <span>您的浏览器或版本影响体验效果,请使用以下浏览器</span>
        <a
          href="https://www.google.cn/chrome/"
          target="_black"
          class="sys-browser-chrome sys-browser-a"
          ><i></i><span class="sys-browser-text">谷歌浏览器</span></a
        >
        <a
          href="http://www.firefox.com.cn/"
          target="_black"
          class="sys-browser-firefox sys-browser-a"
          ><i></i><span class="sys-browser-text">火狐浏览器</span></a
        >
      </div>
    </div>
    <div id="app">
      <div id="loader-wrapper">
        <div id="loader"></div>
        <div class="loader-section section-left"></div>
        <div class="loader-section section-right"></div>
        <div class="load_title">
          正在加载
          <b style="font-size: 15px"><%= webpackConfig.name %></b> ,请耐心等待
        </div>
      </div>
    </div>
    <!-- built files will be auto injected -->
    <% for (var i in htmlWebpackPlugin.options.cdn &&
    htmlWebpackPlugin.options.cdn.js) { %>
    <script src="<%= htmlWebpackPlugin.options.cdn.js[i] %>"></script>
    <% } %>
    <script src="<%= BASE_URL %>js/ht/xu8YCYMKNU3t.js"></script>
    <script src="<%= BASE_URL %>js/ht/vBxvwZkYlU1s.js"></script>
    <script src="<%= BASE_URL %>js/ht/ht.js"></script>
    <script src="<%= BASE_URL %>js/ht/buckle.js"></script>
    <script src="<%= BASE_URL %>js/ht/ht-ui.js"></script>
    <script src="<%= BASE_URL %>js/ht/plugin/ht-animation.js"></script>
    <script src="<%= BASE_URL %>js/ht/plugin/BusEdgeType.js"></script>
    <script src="<%= BASE_URL %>js/ht/plugin/ht-autolayout.js"></script>
    <script src="<%= BASE_URL %>js/ht/plugin/ht-menu.js"></script>
    <script src="<%= BASE_URL %>js/ht/plugin/ht-contextmenu.js"></script>
    <script src="<%= BASE_URL %>js/ht/plugin/ht-overview.js"></script>
    <script src="<%= BASE_URL %>js/ht/plugin/ht-telecom.js"></script>
    <script src="<%= BASE_URL %>js/ht/plugin/ht-edgetype.js"></script>
    <script src="<%= BASE_URL %>js/ht/plugin/ht-htmlnode.js"></script>
    <script src="<%= BASE_URL %>js/ht/plugin/ht-cssanimation.js"></script>
    <script src="<%= BASE_URL %>js/ht/plugin/ht-palette.js"></script>
    <script src="<%= BASE_URL %>js/ht/plugin/ht-form.js"></script>
    <script src="<%= BASE_URL %>js/ht/plugin/ht-dialog.js"></script>
    <script src="<%= BASE_URL %>js/ht/plugin/ht-forcelayout.js"></script>
    <script src="<%= BASE_URL %>js/ht/plugin/ht-historymanager.js"></script>
    <script src="<%= BASE_URL %>js/ht/plugin/ht-flow.js"></script>
    <script src="<%= BASE_URL %>js/ht/diyTreeTablePane.js"></script>
    <script src="<%= BASE_URL %>js/ht/ht-ui-framework.js"></script>
    <script src="https://gis.10010.com:8219/dugis-tn/dugis-demo-3d/api/booter.js"></script>
    <script>
      window.BMAP_AUTHENTIC_KEY = "USER_AK";
    </script>
    <% if (htmlWebpackPlugin.options.enableMonitoring) { %>
    <script src="<%= BASE_URL %>js/pagelog/pagelog.js"></script>
    <% } %>
  </body>
</html>
