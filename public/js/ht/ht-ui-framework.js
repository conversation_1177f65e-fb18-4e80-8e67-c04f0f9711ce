!function(a,b,c){"use strict";function d(){function d(a){for(var b,c=[],d=0,e=0,f="";null!=(b=G.exec(a));){var g=b[0],i=b[1],j=b.index;if(f+=a.slice(e,j),e=j+g.length,i)f+=i[1];else{f&&(c.push(f),f="");var k=b[2],l=b[3],m=b[4],n=b[5],o=b[6],p=b[7],q="+"===o||"*"===o,r="?"===o||"*"===o,s=k||"/",t=m||n||(p?".*":"[^"+s+"]+?");c.push({name:l||d++,prefix:k||"",delimiter:s,optional:r,repeat:q,pattern:h(t)})}}return e<a.length&&(f+=a.substr(e)),f&&c.push(f),c}function e(a){return f(d(a))}function f(a){for(var b=new Array(a.length),c=0;c<a.length;c++)"object"==typeof a[c]&&(b[c]=new RegExp("^"+a[c].pattern+"$"));return function(c){for(var d="",e=c||{},f=0;f<a.length;f++){var g=a[f];if("string"!=typeof g){var h,i=e[g.name];if(null==i){if(g.optional)continue;throw new TypeError('Expected "'+g.name+'" to be defined')}if(A(i)){if(!g.repeat)throw new TypeError('Expected "'+g.name+'" to not repeat, but received "'+i+'"');if(0===i.length){if(g.optional)continue;throw new TypeError('Expected "'+g.name+'" to not be empty')}for(var j=0;j<i.length;j++){if(h=encodeURIComponent(i[j]),!b[f].test(h))throw new TypeError('Expected all "'+g.name+'" to match "'+g.pattern+'", but received "'+h+'"');d+=(0===j?g.prefix:g.delimiter)+h}}else{if(h=encodeURIComponent(i),!b[f].test(h))throw new TypeError('Expected "'+g.name+'" to match "'+g.pattern+'", but received "'+h+'"');d+=g.prefix+h}}else d+=g}return d}}function g(a){return a.replace(/([.+*?=^!:${}()[\]|\/])/g,"\\$1")}function h(a){return a.replace(/([=!:$\/()])/g,"\\$1")}function i(a,b){return a.keys=b,a}function j(a){return a.sensitive?"":"i"}function k(a,b){var c=a.source.match(/\((?!\?)/g);if(c)for(var d=0;d<c.length;d++)b.push({name:d,prefix:null,delimiter:null,optional:!1,repeat:!1,pattern:null});return i(a,b)}function l(a,b,c){for(var d=[],e=0;e<a.length;e++)d.push(o(a[e],b,c).source);var f=new RegExp("(?:"+d.join("|")+")",j(c));return i(f,b)}function m(a,b,c){for(var e=d(a),f=n(e,c),g=0;g<e.length;g++)"string"!=typeof e[g]&&b.push(e[g]);return i(f,b)}function n(a,b){b=b||{};for(var c=b.strict,d=b.end!==!1,e="",f=a[a.length-1],h="string"==typeof f&&/\/$/.test(f),i=0;i<a.length;i++){var k=a[i];if("string"==typeof k)e+=g(k);else{var l=g(k.prefix),m=k.pattern;k.repeat&&(m+="(?:"+l+m+")*"),m=k.optional?l?"(?:"+l+"("+m+"))?":"("+m+")?":l+"("+m+")",e+=m}}return c||(e=(h?e.slice(0,-2):e)+"(?:\\/(?=$))?"),e+=d?"$":c&&h?"":"(?=\\/|$)",new RegExp("^"+e,j(b))}function o(a,b,c){return b=b||[],A(b)?c||(c={}):(c=b,b=[]),a instanceof RegExp?k(a,b,c):A(a)?l(a,b,c):m(a,b,c)}function p(a,b){if("function"==typeof a)return p("*",a);if("function"==typeof b)for(var c=new t(a),d=1;d<arguments.length;++d)p.callbacks.push(c.middleware(arguments[d]));else"string"==typeof a?p["string"==typeof b?"redirect":"show"](a,b):p.start(a)}function q(a){if(!a.handled){var b;b=V?Q&&z()+K.location.hash.replace("#!",""):Q&&K.location.pathname+K.location.search,b!==a.canonicalPath&&(p.stop(),a.handled=!1,Q&&(K.location.href=a.canonicalPath))}}function r(a){return"string"!=typeof a?a:S?decodeURIComponent(a.replace(/\+/g," ")):a}function s(a,b){var c=z();"/"===a[0]&&0!==a.indexOf(c)&&(a=c+(V?"#!":"")+a);var d=a.indexOf("?");if(this.canonicalPath=a,this.path=a.replace(c,"")||"/",V&&(this.path=this.path.replace("#!","")||"/"),this.title=L&&K.document.title,this.state=b||{},this.state.path=a,this.querystring=~d?r(a.slice(d+1)):"",this.pathname=r(~d?a.slice(0,d):a),this.params={},this.hash="",!V){if(!~this.path.indexOf("#"))return;var e=this.path.split("#");this.path=this.pathname=e[0],this.hash=r(e[1])||"",this.querystring=this.querystring.split("#")[0]}}function t(a,b){b=b||{},b.strict=b.strict||U,this.path="*"===a?"(.*)":a,this.method="GET",this.regexp=B(this.path,this.keys=[],b)}function u(a){if(1===v(a)&&!(a.metaKey||a.ctrlKey||a.shiftKey||a.defaultPrevented)){var b=a.target,c=a.path||(a.composedPath?a.composedPath():null);if(c)for(var d=0;d<c.length;d++)if(c[d].nodeName&&"A"===c[d].nodeName.toUpperCase()&&c[d].href){b=c[d];break}for(;b&&"A"!==b.nodeName.toUpperCase();)b=b.parentNode;if(b&&"A"===b.nodeName.toUpperCase()){var e="object"==typeof b.href&&"SVGAnimatedString"===b.href.constructor.name;if(!b.hasAttribute("download")&&"external"!==b.getAttribute("rel")){var f=b.getAttribute("href");if((V||!y(b)||!b.hash&&"#"!==f)&&!(f&&f.indexOf("mailto:")>-1)&&(e?!b.target.baseVal:!b.target)&&(e||x(b.href))){var g=e?b.href.baseVal:b.pathname+b.search+(b.hash||"");g="/"!==g[0]?"/"+g:g,O&&g.match(/^\/[a-zA-Z]:\//)&&(g=g.replace(/^\/[a-zA-Z]:\//,"/"));var h=g,i=z();0===g.indexOf(i)&&(g=g.substr(T.length)),V&&(g=g.replace("#!","")),i&&h===g||(a.preventDefault(),p.show(h))}}}}}function v(b){return b=b||M&&a.event,null==b.which?b.button:b.which}function w(a){if("function"==typeof URL&&Q)return new URL(a,location.toString());if(L){var b=document.createElement("a");return b.href=a,b}}function x(a){if(!a||!Q)return!1;var b=w(a),c=K.location;return c.protocol===b.protocol&&c.hostname===b.hostname&&c.port===b.port}function y(a){if(!Q)return!1;var b=K.location;return a.pathname===b.pathname&&a.search===b.search}function z(){if(T)return T;var a=M&&K&&K.location;return M&&V&&a&&"file:"===a.protocol?a.pathname:T}var A=Array.isArray||function(a){return"[object Array]"==b.prototype.toString.call(a)},B=o,C=d,D=e,E=f,F=n,G=new RegExp(["(\\\\.)","([\\/.])?(?:(?:\\:(\\w+)(?:\\(((?:\\\\.|[^()])+)\\))?|\\(((?:\\\\.|[^()])+)\\))([+*?])?|(\\*))"].join("|"),"g");B.parse=C,B.compile=D,B.tokensToFunction=E,B.tokensToRegExp=F;var H=p;p["default"]=p,p.Context=s,p.Route=t,p.sameOrigin=x;var I,J,K,L="undefined"!=typeof document,M="undefined"!=typeof a,N="undefined"!=typeof history,O="undefined"!=typeof process,P=L&&document.ontouchstart?"touchstart":"click",Q=M&&!(!a.history.location&&!a.location),R=!0,S=!0,T="",U=!1,V=!1;p.callbacks=[],p.exits=[],p.current="",p.len=0,p.base=function(a){return 0===arguments.length?T:void(T=a)},p.strict=function(a){return 0===arguments.length?U:void(U=a)},p.start=function(b){if(b=b||{},!I&&(I=!0,K=b.window||M&&a,!1===b.dispatch&&(R=!1),!1===b.decodeURLComponents&&(S=!1),!1!==b.popstate&&M&&K.addEventListener("popstate",W,!1),!1!==b.click&&L&&K.document.addEventListener(P,u,!1),V=!!b.hashbang,V&&M&&!N&&K.addEventListener("hashchange",W,!1),R)){var c;if(Q){var d=K.location;c=V&&~d.hash.indexOf("#!")?d.hash.substr(2)+d.search:V?d.search+d.hash:d.pathname+d.search+d.hash}p.replace(c,null,!0,R)}},p.stop=function(){I&&(p.current="",p.len=0,I=!1,L&&K.document.removeEventListener(P,u,!1),M&&K.removeEventListener("popstate",W,!1),M&&K.removeEventListener("hashchange",W,!1))},p.show=function(a,b,c,d){var e=new s(a,b),f=J;return J=e,p.current=e.path,!1!==c&&p.dispatch(e,f),!1!==e.handled&&!1!==d&&e.pushState(),e},p.back=function(a,b){p.len>0?(N&&K.history.back(),p.len--):a?setTimeout(function(){p.show(a,b)}):setTimeout(function(){p.show(z(),b)})},p.redirect=function(a,b){"string"==typeof a&&"string"==typeof b&&p(a,function(a){setTimeout(function(){p.replace(b)},0)}),"string"==typeof a&&"undefined"==typeof b&&setTimeout(function(){p.replace(a)},0)},p.replace=function(a,b,c,d){var e=new s(a,b),f=J;return J=e,p.current=e.path,e.init=c,e.save(),!1!==d&&p.dispatch(e,f),e},p.dispatch=function(a,b){function c(){var a=p.exits[f++];return a?void a(b,c):d()}function d(){var b=p.callbacks[e++];return a.path!==p.current?void(a.handled=!1):b?void b(a,d):q(a)}var e=0,f=0;b?c():d()},p.exit=function(a,b){if("function"==typeof a)return p.exit("*",a);for(var c=new t(a),d=1;d<arguments.length;++d)p.exits.push(c.middleware(arguments[d]))},p.Context=s,s.prototype.pushState=function(){p.len++,N&&K.history.pushState(this.state,this.title,V&&"/"!==this.path?"#!"+this.path:this.canonicalPath)},s.prototype.save=function(){N&&"file:"!==K.location.protocol&&K.history.replaceState(this.state,this.title,V&&"/"!==this.path?"#!"+this.path:this.canonicalPath)},p.Route=t,t.prototype.middleware=function(a){var b=this;return function(c,d){return b.match(c.path,c.params)?a(c,d):void d()}},t.prototype.match=function(a,b){var d=this.keys,e=a.indexOf("?"),f=~e?a.slice(0,e):a,g=this.regexp.exec(decodeURIComponent(f));if(!g)return!1;for(var h=1,i=g.length;i>h;++h){var j=d[h-1],k=r(g[h]);k===c&&hasOwnProperty.call(b,j.name)||(b[j.name]=k)}return!0};var W=function(){var b=!1;if(M)return L&&"complete"===document.readyState?b=!0:a.addEventListener("load",function(){setTimeout(function(){b=!0},0)}),function(a){if(b)if(a.state){var d=a.state.path;p.replace(d,a.state)}else if(Q){var e=K.location;V?p.show(e.hash.substr(2),c,c,!1):p.show(e.pathname+e.hash,c,c,!1)}}}();return p.sameOrigin=x,H}function e(a,c,d,e){b.defineProperty(a,c,{value:d,enumerable:!!e,writable:!0,configurable:!0})}function f(a,b,c){a.__proto__=b}function g(a,b,c){for(var d=0,f=c.length;f>d;d++){var g=c[d];e(a,g,b[g])}}function h(a){this.data=a,this.walk(a)}function i(a){for(var b=0,c=a.length;c>b;b++)j(a[b])}function j(a,b){return a&&"object"==typeof a?new h(a):void 0}function k(){this.id=x++,this.subs=[]}function l(a,b,c){this.cb=c,this.vm=a,this.expOrFn=b,this.depIds={},this.newDepIds={},"function"==typeof b?this.getter=b:this.getter=this.parseGetter(b),this.value=this.get()}ht.ui.framework=ht.ui.fw={};var m={borderlayout:"ht.ui.BorderLayout",splitlayout:"ht.ui.SplitLayout",flowlayout:"ht.ui.FlowLayout",hboxlayout:"ht.ui.HBoxLayout",vboxlayout:"ht.ui.VBoxLayout",tablayout:"ht.ui.TabLayout",relativelayout:"ht.ui.RelativeLayout",panel:"ht.ui.Panel",dialog:"ht.ui.Dialog",tablelayout:"ht.ui.TableLayout",tablerow:"ht.ui.TableRow",hbuttongroup:"ht.ui.HButtonGroup",vbuttongroup:"ht.ui.VButtonGroup",router:"ht.ui.framework.RouterView",button:"ht.ui.Button",linkbutton:"ht.ui.LinkButton",togglebutton:"ht.ui.ToggleButton",radiobutton:"ht.ui.RadioButton",checkbox:"ht.ui.CheckBox",menubutton:"ht.ui.MenuButton",menu:"ht.ui.Menu",contextmenu:"ht.ui.ContextMenu",slider:"ht.ui.Slider",progressbar:"ht.ui.ProgressBar",breadcrumb:"ht.ui.Breadcrumb",htmlview:"ht.ui.HtmlView",htview:"ht.ui.HTView",label:"ht.ui.Label",textfield:"ht.ui.TextField",textarea:"ht.ui.TextArea",combobox:"ht.ui.ComboBox",cascadecombobox:"ht.ui.CascadeComboBox",numberinput:"ht.ui.NumberInput",colorpicker:"ht.ui.ColorPicker",colorpane:"ht.ui.ColorPane",datetimepicker:"ht.ui.DateTimePicker",datetimepane:"ht.ui.DateTimePane",tablepane:"ht.ui.TablePane",tableview:"ht.ui.TableView",tableheader:"ht.ui.TableHeader",listview:"ht.ui.ListView",treeview:"ht.ui.TreeView",treetablepane:"ht.ui.TreeTablePane",treetable:"ht.ui.TreeTableView",propertyview:"ht.ui.PropertyView",propertypane:"ht.ui.PropertyPane",gridpane:"ht.ui.GridPane",gridview:"ht.ui.GridView",palette:"ht.ui.Palette",sidebar:"ht.ui.Sidebar",monacoeditor:"ht.ui.MonacoEditor"},n=function(a){var b;if(a.indexOf(".")>=0)return r.getClass(a);var c=m[a];return c?r.getClass(c):b=r.getClass("ht.ui."+a.charAt(0).toUpperCase()+a.slice(1))},o=ht.ui,p=o.fw,q=null,r=ht.Default,s=function(a,b,c,d,e,f,g){var h=new a;c.add(h);for(var i in b){var j=b[i];if(0===i.indexOf("s:"))if(r.isString(j)&&"{"===j[0]&&"}"===j[j.length-1]){j=j.substring(1,j.length-1);var k=!1;if("{"===j[0]&&"}"===j[j.length-1]&&(j=j.substring(1,j.length-1),k=!0),f)var l=r.bind({data:f,property:j,getValue:function(a,b){var c=new Function("fragment","query","data"," with(fragment) { with(query){ with(data) {return "+b+"} } }");return c(e,{query:e._query,params:e._params},a)}},{data:h,property:i.substr(2),accessType:"style"},k);d._bindingIds[l]&&r.unbind(l),d._bindingIds[l]=g,g._$bindings||(g._$bindings=[]),g._$bindings.push({type:"column",column:h,dataProperty:j,columnProperty:i.substr(2),accessType:"style",twoWayBinding:k})}else h.s(i.substr(2),j);else if(0===i.indexOf("a:"))if(r.isString(j)&&"{"===j[0]&&"}"===j[j.length-1]){j=j.substring(1,j.length-1);var k=!1;if("{"===j[0]&&"}"===j[j.length-1]&&(j=j.substring(1,j.length-1),k=!0),f)var l=r.bind({data:f,property:j,getValue:function(a,b){var c=new Function("fragment","query","data"," with(fragment) { with(query){ with(data) {return "+b+"} } }");return c(e,{query:e._query,params:e._params},a)}},{data:h,property:i.substr(2),accessType:"attr"},k);d._bindingIds[l]&&r.unbind(l),d._bindingIds[l]=g,g._$bindings||(g._$bindings=[]),g._$bindings.push({type:"column",column:h,dataProperty:j,columnProperty:i.substr(2),accessType:"attr",twoWayBinding:k})}else h.a(i.substr(2),j);else if(r.isString(j)&&"{"===j[0]&&"}"===j[j.length-1]){j=j.substring(1,j.length-1);var k=!1;if("{"===j[0]&&"}"===j[j.length-1]&&(j=j.substring(1,j.length-1),k=!0),f)var l=r.bind({data:f,property:j,getValue:function(a,b){var c=new Function("fragment","query","data"," with(fragment) { with(query){ with(data) {return "+b+"} } }");return c(e,{query:e._query,params:e._params},a)}},{data:h,property:i},k);d._bindingIds[l]&&r.unbind(l),d._bindingIds[l]=g,g._$bindings||(g._$bindings=[]),g._$bindings.push({type:"column",column:h,dataProperty:j,columnProperty:i,twoWayBinding:k})}else{var m=r.setter(i);h[m]?h[m](j):h[i]=j}}return h};!function(a){function d(a,b){if(0==a[b].length)return a[b]={};var c={};for(var d in a[b])c[d]=a[b][d];return a[b]=c,c}function e(a,c,f,g){var h=a.shift();if(h){var i=c[f]=c[f]||[];"]"==h?Array.isArray(i)?""!=g&&i.push(g):"object"==typeof i?i[b.keys(i).length]=g:i=c[f]=[c[f],g]:~h.indexOf("]")?(h=h.substr(0,h.length-1),!o.test(h)&&Array.isArray(i)&&(i=d(c,f)),e(a,i,h,g)):(!o.test(h)&&Array.isArray(i)&&(i=d(c,f)),e(a,i,h,g))}else Array.isArray(c[f])?c[f].push(g):"object"==typeof c[f]?c[f]=g:"undefined"==typeof c[f]?c[f]=g:c[f]=[c[f],g]}function f(a,b,c){if(~b.indexOf("]")){var d=b.split("[");d.length;e(d,a,"base",c)}else{if(!o.test(b)&&Array.isArray(a.base)){var f={};for(var g in a.base)f[g]=a.base[g];a.base=f}l(a.base,b,c)}return a}function g(a){var c={base:{}};return b.keys(a).forEach(function(b){f(c,b,a[b])}),c.base}function h(a){return String(a).split("&").reduce(function(a,b){try{b=decodeURIComponent(b.replace(/\+/g," "))}catch(c){}var d=b.indexOf("="),e=m(b),g=b.substr(0,e||d),h=b.substr(e||d,b.length),h=h.substr(h.indexOf("=")+1,h.length);return""==g&&(g=b,h=""),f(a,g,h)},{base:{}}).base}function i(a,b){if(!b)throw new TypeError("stringify expects an object");return b+"="+encodeURIComponent(a)}function j(a,b){var c=[];if(!b)throw new TypeError("stringify expects an object");for(var d=0;d<a.length;d++)c.push(p(a[d],b+"["+d+"]"));return c.join("&")}function k(a,c){for(var d,e=[],f=b.keys(a),g=0,h=f.length;h>g;++g)d=f[g],e.push(p(a[d],c?c+"["+encodeURIComponent(d)+"]":encodeURIComponent(d)));return e.join("&")}function l(a,b,d){var e=a[b];c===e?a[b]=d:Array.isArray(e)?e.push(d):a[b]=[e,d]}function m(a){for(var b,c,d=a.length,e=0;d>e;++e)if(c=a[e],"]"==c&&(b=!1),"["==c&&(b=!0),"="==c&&!b)return e}a.version="0.4.2";var n=b.prototype.toString,o=/^[0-9]+$/;a.parse=function(a){return null==a||""==a?{}:"object"==typeof a?g(a):h(a)};var p=a.stringify=function(a,b){return Array.isArray(a)?j(a,b):"[object Object]"==n.call(a)?k(a,b):"string"==typeof a?i(a,b):b+"="+a}}(ht.ui.framework.qs={});var t="__proto__"in{},u=Array.prototype,v=b.create(u),w=["push","pop","shift","unshift","splice","sort","reverse"];w.forEach(function(a){var b=u[a];e(v,a,function(){var c,d=Array.prototype.slice.apply(arguments),e=b.apply(this,d),f=this.__dep__;switch(a){case"push":case"unshift":c=d;break;case"splice":c=d.slice(2)}return c&&i(c),f.notify(),e})}),h.prototype={walk:function(a){var c=this;b.keys(a).forEach(function(b){"_"===b[0]&&"_"===b[1]||c.convert(b,a[b])})},convert:function(a,b){this.defineReactive(this.data,a,b)},defineReactive:function(a,c,d){var h=new k;if(Array.isArray(d)){e(d,"__dep__",h);var l=t?f:g;l(d,v,w),i(d)}else j(d);b.getOwnPropertyDescriptor(a,c)&&null!=b.getOwnPropertyDescriptor(a,c).get||b.defineProperty(a,c,{enumerable:!0,configurable:!1,get:function(){return k.target&&h.depend(),d},set:function(a){if(a!==d){if(d=a,Array.isArray(a)){e(a,"__dep__",h);var b=t?f:g;b(a,v,w),i(a)}else j(a);h.notify()}}})}};var x=0;k.prototype={addSub:function(a){this.subs.push(a)},depend:function(){k.target.addDep(this)},removeSub:function(a){var b=this.subs.indexOf(a);-1!=b&&this.subs.splice(b,1)},notify:function(){this.subs.forEach(function(a){a.update()})}},k.target=null,l.prototype={update:function(){this.run()},run:function(){var a=this.get(),b=this.value;a!==b&&(this.value=a,this.cb.call(this.vm,a,b))},addDep:function(a){this.newDepIds.hasOwnProperty(a.id)||(this.newDepIds[a.id]=a),this.depIds.hasOwnProperty(a.id)||(a.addSub(this),this.depIds[a.id]=a)},get:function(){k.target=this;var a=this.getter.call(this.vm,this.vm);return k.target=null,this.cleanupDeps(),a},cleanupDeps:function(){for(var a=this.newDepIds,c=this.depIds,d=b.keys(c),e=0,f=d.length;f>e;e++){var g=d[e],h=a[g];h||(c[g].removeSub(this),delete c[g])}this.newDepIds={}},parseGetter:function(a){var b=a.split(/\[|\./);return function(a){for(var c=0,d=b.length;d>c;c++){if(!a)return;var e=b[c];isNaN(e[0])||"]"!==e[e.length-1]||(e=e.substr(0,e.length-1)),a=a[e]}return ht.Default.clone(a)}},tearDown:function(){for(var a=b.keys(this.depIds),c=a.length;c--;)this.depIds[a[c]].removeSub(this);this.depIds={}}};var y={_syncValue:function(a,b,c,d,e,f){if(f||(d=ht.Default.clone(d)),e instanceof Function)e(a,b,c,d);else if(a instanceof ht.Data){if("style"===c)return a.setStyle(b,d);if("attr"===c)return a.setAttr(b,d);a[ht.Default.setter(b)](d)}else a.setUpInteractors?a[ht.Default.setter(b)](d):a instanceof(ht.ui&&ht.ui.Form)?a.setItem(b,d):(b=b.split(/\[|\./),b.forEach(function(c,e){isNaN(c[0])||"]"!==c[c.length-1]||(c=c.substr(0,c.length-1)),e<b.length-1?a=a[c]:a[c]=d}))},hasSetting:function(a,b,c){var d=this._setting;if(d)for(var e=0,f=d.length;f>e;e++){var g=d[e];if(g[0]===a&&g[1]===b&&g[2]===c)return!0}},removeSetting:function(a,b,c){var d=this._setting;if(d)for(var e=0,f=d.length;f>e;e++){var g=d[e];if(g[0]===a&&g[1]===b&&g[2]===c){d.splice(e,1);break}}},addListener:function(a,c,d,e,f,g,h,i){var k=this;if(a instanceof ht.Data){var m;"attr"===d||"style"===d?(m=function(b){if(!k.hasSetting(a,c,d)){k._setting||(k._setting=[]),k._setting.push([a,c,d]);for(var j=c.split(","),l=0,m=j.length;m>l;l++)j[l]=j[l].trim();if(b.data===a&&j.indexOf(b.property.substr(2))>=0){var n;n=e?e(a,c,d):b.newValue,k._syncValue(f,g,h,n,i)}k.removeSetting(a,c,d)}},a.getDataModel().addDataPropertyChangeListener(m)):(m=function(b){if(!k.hasSetting(a,c,d)){k._setting||(k._setting=[]),k._setting.push([a,c,d]);for(var j=c.split(","),l=0,m=j.length;m>l;l++)j[l]=j[l].trim();if(b.data===a&&j.indexOf(b.property)>=0){var n;n=e?e(a,c,d):b.newValue,k._syncValue(f,g,h,n,i)}k.removeSetting(a,c,d)}},a.getDataModel().addDataPropertyChangeListener(m))}else if(a.setUpInteractors)m=function(b){if(!k.hasSetting(a,c,d)){k._setting||(k._setting=[]),k._setting.push([a,c,d]);for(var j=c.split(","),l=0,m=j.length;m>l;l++)j[l]=j[l].trim();if(j.indexOf(b.property)>=0){var n;n=e?e(a,c,d):b.newValue,k._syncValue(f,g,h,n,i)}k.removeSetting(a,c,d)}},a.addPropertyChangeListener(m);else if(a instanceof(ht.ui&&ht.ui.Form))m=function(b){if(!k.hasSetting(a,c,d)){k._setting||(k._setting=[]),k._setting.push([a,c,d]);for(var j=c.split(","),l=0,m=j.length;m>l;l++)j[l]=j[l].trim();if("formDataValueChange"===b.kind&&j.indexOf(b.name)>=0||"jsonChange"===b.kind){var n;n=e?e(a,c,d):"jsonChange"===b.kind?b.newValue[c]:b.newValue,k._syncValue(f,g,h,n,i)}k.removeSetting(a,c,d)}},a.addChangeListener(m);else{var n;a.__observer__?n=a.__observer__:(n=j(a),b.defineProperty(a,"__observer__",{value:n,enumerable:!1,writable:!0,configurable:!0}));var o=n.data;m=function(b,e){k.hasSetting(a,c,d)||(k._setting||(k._setting=[]),k._setting.push([a,c,d]),k._syncValue(f,g,h,b,i,!0),k.removeSetting(a,c,d))};var p;p=e?new l(o,function(){return e(a,c,d)},m):new l(o,c,m),m.watcher=p,k._syncValue(f,g,h,p.value,i,!0)}return m},removeListener:function(a,b,c){return a instanceof ht.Data?"style"===b?a.getDataModel().removeDataPropertyChangeListener(c):"attr"===b?a.getDataModel().removeDataPropertyChangeListener(c):a.getDataModel().removeDataPropertyChangeListener(c):a.setUpInteractors?a.removePropertyChangeListener(c):a instanceof(ht.ui&&ht.ui.Form)?a.removeChangeListener(c):a.tearDown(),c},bind:function(a,b,c){var d=this,e=a.data,f=a.property,g=a.accessType,h=b.data,i=b.property,j=b.accessType;null==c&&(c=!0);var k,l=d.addListener(e,f,g,a.getValue,h,i,j,b.setValue);return c&&(k=d.addListener(h,i,j,b.getValue,e,f,g,a.setValue)),d._listeners[++d._id]={source:l.watcher||e,target:k?k.watcher||h:k,sourceAccessType:g,targetAccessType:j,sourceListener:l,targetListener:k,twoWayBinding:c},d._id},bindForm:function(a,b,c,d,e){var f=this,g=[],h=a instanceof ht.ui.Form,i=h?a:b;if(d||(d=i.getFormDataNames()),d)if(Array.isArray(d))for(var j=0,k=d.length;k>j;j++){var l=d[j];if(ht.Default.isString(l)){var m=f.bind({data:a,property:!h&&e?e+"."+l:l},{data:b,property:h&&objectPrefix?e+"."+l:l},c);g.push(m)}else{var m=f.bind({data:l.source,accessType:l.sourceAccessType,property:!h&&e?e+"."+l.sourceProperty:l.sourceProperty,getValue:l.sourceGetValue,setValue:l.sourceSetValue},{data:l.target,accessType:l.targetAccessType,property:h&&e?e+"."+l.targetProperty:l.targetProperty,getValue:l.targetGetValue,setValue:l.targetSetValue},c);g.push(m)}}else{var n=d.a,o=d.s,p=d.p;if(n)for(var j=0,k=n.length;k>j;j++){var l=n[j],m=f.bind({data:a,accessType:"attr",property:!h&&e?e+"."+l:l},{data:b,accessType:"attr",property:h&&objectPrefix?e+"."+l:l},c);g.push(m)}if(o)for(var j=0,k=o.length;k>j;j++){var l=o[j],m=f.bind({data:a,accessType:"style",property:!h&&e?e+"."+l:l},{data:b,accessType:"style",property:h&&objectPrefix?e+"."+l:l},c);g.push(m)}if(p)for(var j=0,k=p.length;k>j;j++){var l=p[j],m=f.bind({data:a,property:!h&&e?e+"."+l:l},{data:b,property:h&&objectPrefix?e+"."+l:l},c);g.push(m)}}return g},unbindImpl:function(a){var b=this,c=b._listeners[a];if(c){var d=c.source,e=c.target,f=c.sourceAccessType,g=c.targetAccessType,h=c.sourceListener,i=c.targetListener;b.removeListener(d,f,h),c.twoWayBinding&&b.removeListener(e,g,i),delete b._listeners[a]}},unbind:function(a){var b=this;if(Array.isArray(a))for(var c=0,d=a.length;d>c;c++)b.unbindImpl(a[c]);else b.unbindImpl(a)}};y._id=0,y._listeners={},ht.Default.bind=function(a,b,c){return y.bind(a,b,c)},ht.Default.bindForm=function(a,b,c,d,e){return y.bindForm(a,b,c,d,e)},ht.Default.setBindingProperty=function(a,b,c){h.prototype.defineReactive(a,b,c)},ht.Default.watchObject=function(a,c,d){var e;a.__observer__?e=a.__observer__:(e=j(a),b.defineProperty(a,"__observer__",{value:e,enumerable:!1,writable:!0,configurable:!0}));var f=e.data;return new l(f,c,d)},ht.Default.unwatchObject=function(a){a&&a.tearDown()},ht.Default.unbind=function(a){y.unbind(a)},ht.ui.BindingUtils=y,p.RouterView=function(){p.RouterView.superClass.constructor.call(this)},r.def("ht.ui.framework.RouterView",ht.ui.Label,{__preferredSize:{width:0,height:0},__minSize:{width:0,height:0}}),ht.ui.Abc=function(){ht.ui.Abc.superClass.constructor.call(this)},r.def("ht.ui.Abc",ht.ui.Label,{}),p.App=function(a){var b=this;a||(a={}),b._cachedComps={},b._config=a,b._bindingIds={};var c=b._rootFragment=new p.Fragment(a.root,b);if(c.getComponent()._$fragment=c,a.routes){var e=b._router=d(),f=a.routes;e.base(a.base||"."),e("*",function(a,b){a.query=ht.ui.framework.qs.parse(a.querystring),b()});for(var g=!1,h=0,i=f.length;i>h;h++)if("/"===f[h].path){g=!0;break}g||(f=f.slice(0),f.splice(0,0,{path:"/",fragment:null})),b.registerRoutes(f)}},p.App.callHandler=function(a,b,c){for(;a&&!a._$fragment;)a=a.getParent();var d=a&&a._$fragment;if(d){var e=d._config.handlers;if(e){var f=e[b];f&&f.apply(d,c)}}},p.App.registerAlias=function(a){for(var b in a)m[b]=a[b]},r.def(p.App,b,{addToDOM:function(a){var b=this,c=b._rootFragment,d=c._config;d.handlers&&d.handlers.$beforeMount&&d.handlers.$beforeMount.call(c);var e=b.getRootView();a&&a instanceof Function?a.call(b,e):e.addToDOM(a),d.handlers&&d.handlers.$mounted&&d.handlers.$mounted.call(c),b._router&&b._router.start({click:!1,hashbang:!0})},removeFromDOM:function(a){var b=this,c=b._rootFragment,d=c._config;d.handlers&&d.handlers.$beforeDestroy&&d.handlers.$beforeDestroy.call(c);var e=b.getRootView();a&&a instanceof Function?a.call(b,e):e.removeFromDOM(),d.handlers&&d.handlers.$destroyed&&d.handlers.$destroyed.call(c),b._router&&b._router.stop()},getRouter:function(){return this._router},getRootView:function(){return this._rootFragment.getComponent()},getFragments:function(){return this._config.fragments},setRouterInterceptor:function(a){this.routerInterceptor=a},getRouterInterceptor:function(){return this.routerInterceptor},joinPathImpl:function(a,b){var c=0;return"/"===a[a.length-1]&&c++,"/"===b[0]&&c++,0===c?a+"/"+b:1===c?a+b:a+b.substr(1)},joinPath:function(a){for(var b="",c=0,d=a.length;d>c;c++)b=this.joinPathImpl(b,a[c]);return b},registerRoutes:function(a,b,c){for(var d=this,e=d.getRouter(),f=0;f<a.length;f++){var g=a[f],h=g.path,i=g.fragment;g.fragments&&(i=g.fragments,i._$isFragments=!0);var j=b?b.slice(0):[];j.push(h);var k=c?c.slice(0):[];k.push(i);var l=d.joinPath(j);!function(a,b,c){e(a,function(a){var e=d.routerInterceptor,f=!0;if(e){var g=d.routerInterceptor(a);g===!1&&(f=!1)}if(f&&!d._tabChanging){for(var h=[],i=0,j=b.length;j>i;i++){var k=b[i];k.indexOf(":")>0?h.push(k.substring(0,k.indexOf(":"))):h.push(k)}d.handleRouteChanged(h,c,a)}})}(l,j,k),g.children&&d.registerRoutes(g.children,j,k)}},checkBinding:function(){for(var a=this,c=a._bindingIds,d=b.keys(c),e=0,f=d.length;f>e;e++){var g=d[e],h=c[g];h.isInDOM()||(r.isString(g)?r.unbind(g.split("-")):r.unbind(g),delete c[g])}},handleRouteChanged:function(a,b,c){for(var d=this,e=d.getFragments(),f=function(a){for(var b=[],c=a.size(),d=0;c>d;d++){var e=a.get(d);(e._$isRouterView||e._$isRouterParent)&&b.push(e)}if(0!==b.length)return b;for(var d=0;c>d;d++){var e=a.get(d);if(e.getChildren&&(b=f(e.getChildren()),b&&b.length>0))return b}},g=[],h=0,i=d.getRootView();h<b.length;){var j=i._$isRouterParent?[i]:f(i.getChildren()),k=b[h],l=r.isString(k)?e[k]:k;if(!l){"/"!==a.join("")&&console.error("Fragment: "+k+" is not found");break}if(l._$isFragments){for(var m={},n=0,o=j.length;o>n;n++){var p=j[n];m[p._$routerName]=p}for(var q in l)"_$isFragments"!==q&&(i=d.replaceRouterView(i,l[q],m[q],b,c,g,a,h))}else i=d.replaceRouterView(i,l,j[0],b,c,g,a,h);h++}for(var n=0,o=g.length;o>n;n++)delete g[n]._$routerViewWorked;d.checkBinding()},replaceRouterView:function(a,b,d,e,f,g,h,i){var d,j=this;if(d._$isRouterView&&!d._$routerViewWorked){if(d._$fragmentConfig===b&&i!==e.length-1)return d instanceof ht.ui.TabLayout&&d._$isRouterParent&&(d._$routerViewWorked=!0,g.push(d)),d;var k=new p.Fragment(b,j,f.query,f.params),l=k.getComponent(),m=d._$template;l instanceof ht.ui.TabLayout&&l._$isRouterParent&&(l._$routerViewWorked=!0,g.push(l)),l._$isRouterView=!0,l._$template=m,l._$fragmentConfig=b,l._$routerName=d._$routerName,l._$fragment=k;var n=d._$fragment;n&&n._config.handlers&&n._config.handlers.$beforeDestroy&&n._config.handlers.$beforeDestroy.call(n),d!==l&&(delete d._$isRouterView,delete d._$template,delete d._$fragmentConfig,delete d._$routerName);var o=a._$fragment,q=d.getParent(),s=q.getChildren().indexOf(d);q.removeView(d),n&&n._config.handlers&&n._config.handlers.$destroyed&&n._config.handlers.$destroyed.call(n);var t=m.layoutParams||{},u=b.template&&b.template.layoutParams||{},v=r.addMethod(r.clone(u),t);for(var w in m)if("$"!==w[0]&&"layoutParams"!==w){var x,y=m[w],z=!1;if(r.isString(y)&&"{"===y[0]&&"}"===y[y.length-1]){z=!0,y=y.substring(1,y.length-1);var A=new Function("fragment","query","configData"," with(fragment) { with(query){ with(configData) {return "+y+"} } }");x=A(o,{query:o._query,params:o._params},o._configData)}0===w.indexOf(":")?k._configData[w.substr(1)]=z?x:y:l[r.setter(w)](z?x:y)}return b.handlers&&b.handlers.$beforeMount&&b.handlers.$beforeMount.call(k),q.addView(l,v,s),b.handlers&&b.handlers.$mounted&&b.handlers.$mounted.call(k),l}if(d instanceof ht.ui.TabLayout&&d._$isRouterParent){for(var B=d,C="/",D=0;i>D;D++)(0!==D||"/"!==h[D])&&(C=j.joinPathImpl(C,h[D]));B._$partRoutePath=C,B._$listener||(B._$listener=function(a){j._tabChanging=!0;var b=a.oldValue;if(b&&b.getParent()===B){var c=b._$fragment;c&&c._config.handlers&&c._config.handlers.$deactivated&&c._config.handlers.$deactivated.call(c)}var d=a.newValue;if(d?j.getRouter()(d._$tabFullPath):j.getRouter()(this._$partRoutePath),d){var e=d._$fragment;e&&e._config.handlers&&e._config.handlers.$activated&&e._config.handlers.$activated.call(e)}j._tabChanging=!1},B.on("p:activeView",B._$listener),B.on("beforeAddView",function(a){var b=a.child,c=b._$fragment;c&&c._config.handlers&&c._config.handlers.$beforeMount&&c._config.handlers.$beforeMount.call(c)}),B.on("addView",function(a){var b=a.child,c=b._$fragment;c&&c._config.handlers&&c._config.handlers.$mounted&&c._config.handlers.$mounted.call(c)}),B.on("beforeRemoveView",function(a){var b=a.child,c=b._$fragment;c&&c._config.handlers&&c._config.handlers.$deactivated&&c._config.handlers.$deactivated.call(c)}),B.on("removeView",function(a){var b=a.child,c=b._$fragment;c&&(c._config.handlers&&c._config.handlers.$beforeDestroy&&c._config.handlers.$beforeDestroy.call(c),c._config.handlers&&c._config.handlers.$destroyed&&c._config.handlers.$destroyed.call(c))}));for(var E,F=!1,G=B.getChildren(),D=0,H=G.size();H>D;D++){var I=G.get(D);if(I._$tabRoute===h[i]){E=I,F=!0;break}}if(F&&i!==e.length-1)return B.setActiveView(E),E._$tabFullPath=f.path,E;var J;F&&(J=G.indexOf(E),B.removeView(E));var k=new p.Fragment(b,j,f.query,f.params),l=k.getComponent();return l._$fragment=k,l._$tabRoute=h[i],l._$tabFullPath=f.path,B.addView(l,c,J),B.setActiveView(l),l}}}),p.Fragment=function(a,c,d,e){var f=this;f._config=a,f._app=c,f._query=d,f._params=e,f.views={};var g=a.data,h=a.computed;g instanceof Function?g=f._configData=g():f._configData=g||{},g&&h&&b.keys(h).forEach(function(a){b.defineProperty(g,a,{configurable:!1,enumerable:!0,get:"function"==typeof h[a]?h[a]:h[a].get,set:h[a].set||function(){}})}),f._radiosMap={},f._component=f.parseUI(a.template),delete f._radiosMap,a.handlers&&a.handlers.$created&&a.handlers.$created.call(f)},r.def(p.Fragment,b,{getApp:function(){return this._app},getRouter:function(){return this._app._router},getComponent:function(){return this._component},getQuery:function(){return this._query},getParams:function(){return this._params},getData:function(){return this._configData},rebind:function(a){var b=this,d=b.getApp(),e=b.getData(),f=d._bindingIds,g=!1;for(var h in f){var i=f[h];i===a&&(g=!0)}a.getId()!=q&&(b.views[a.getId()]=a);var j=a._$bindings;if(j&&e&&!g)for(var k=0;k<j.length;k++){var l=j[k];if("form"===l.type){var m=a._$form=new ht.ui.Form(a);a.getForm=function(){return this._$form};var n=l.formValue,o=r.bindForm(e,m,!0,c,r.isBoolean(n)?c:n);f[o]&&r.unbind(o),o&&o.length>0&&(f[o.join("-")]=a)}else if("column"===l.type){var o=r.bind({data:e,property:l.dataProperty,getValue:function(a,c){var d=new Function("fragment","query","data"," with(fragment) { with(query){ with(data) {return "+c+"} } }");return d(b,{query:b._query,params:b._params},a)}},{data:l.column,property:l.columnProperty,accessType:l.accessType},l.twoWayBinding);f[o]&&r.unbind(o),f[o]=a}else{var o=r.bind({data:e,property:l.dataProperty,getValue:function(a,c){var d=new Function("fragment","query","data"," with(fragment) { with(query){ with(data) {return "+c+"} } }");return d(b,{query:b._query,params:b._params},a)}},{data:a,property:l.viewProperty},l.twoWayBinding);f[o]&&r.unbind(o),f[o]=a}}var p=a.getChildren&&a.getChildren();if(p)for(var s=0;s<p.size();s++){var t=p.get(s);b.rebind(t)}},getAliveId:function(a,b){var c=this,d="";if(a)for(;a;){if(c._component===a){d=c.getParams()[0]+"/"+d;break}d=""===d?a.getClassName()+"/"+d+b:a.getClassName()+"/"+d,a=a.getParent()}else d=c.getParams()[0];return d},parseUI:function(a,d,e){var f,g,h,i=this,j=i._app,k=j._cachedComps,l=j.getFragments(),m=i._config,p=a.$keepAlive,q=i._configData,t=m.fragments,u=a.$isRouter,v=a.$isRouterParent,w=a.$type,x=a.$routerName;if(p&&(h=i.getAliveId(d,e),k[h])){var g=k[h];return i.rebind(g),
g}if(u&&!w&&(w="router"),r.isString(w))if(t&&t[w])f=new Fragment(t[w],j),g=f.getComponent();else if(l&&l[w]){var f=new Fragment(l[w],j);g=f.getComponent()}else{var y=n(w);y?g=new y:console.error(w+" Can't found")}else"object"==typeof w&&(f=new Fragment(w,j),g=f.getComponent());u&&(g._$template=a,g._$isRouterView=!0,g._$fragmentConfig=m,g._$fragment=f),x&&(g._$routerName=x),v&&(g._$isRouterParent=!0),g instanceof ht.ui.TabLayout&&g.on("p:children",function(){j.checkBinding()});for(var z,A=b.keys(a),B=!1,C=0,D=A.length;D>C;C++){var E=A[C],F=a[E];if(0!==E.indexOf("$")){if(!g[ht.Default.setter(E)])continue;if("children"===E)for(var G=0,H=F.length;H>G;G++){var I=F[G],J=i.parseUI(I,g,G);g.addView(J)}else if("contentView"===E){var J=i.parseUI(F,g,G);g.setContentView(J)}else if("leftView"===E&&g instanceof ht.ui.BorderLayout){var J=i.parseUI(F,g,G);g.setLeftView(J)}else if("rightView"===E&&g instanceof ht.ui.BorderLayout){var J=i.parseUI(F,g,G);g.setRightView(J)}else if("topView"===E&&g instanceof ht.ui.BorderLayout){var J=i.parseUI(F,g,G);g.setTopView(J)}else if("bottomView"===E&&g instanceof ht.ui.BorderLayout){var J=i.parseUI(F,g,G);g.setBottomView(J)}else if("centerView"===E&&g instanceof ht.ui.BorderLayout){var J=i.parseUI(F,g,G);g.setCenterView(J)}else if("columns"===E&&Array.isArray(F)&&g.addColumns){var K=g.getColumnModel();F.forEach(function(a){if(!(a instanceof o.Column)){var b=r.getClass(a.className);a=s(b?b:o.Column,a,K,j,i,q,g);var c=a.parentId;if(null!=c){var d=K.getDataById(c);a.setParent(d)}}})}else if(F&&r.isString(F)&&"{"===F[0]&&"}"===F[F.length-1]){F=F.substring(1,F.length-1);var L=!1;if("{"===F[0]&&"}"===F[F.length-1]&&(F=F.substring(1,F.length-1),L=!0),q)var M=r.bind({data:q,property:F,getValue:function(a,b){var c=new Function("fragment","query","data"," with(fragment) { with(query){ with(data) {return "+b+"} } }");return c(i,{query:i._query,params:i._params},a)}},{data:g,property:E},L);j._bindingIds[M]&&r.unbind(M),j._bindingIds[M]=g,g._$bindings||(g._$bindings=[]),g._$bindings.push({dataProperty:F,viewProperty:E,twoWayBinding:!0})}else{var N=a[E];if("columnWidthMap"===E&&g instanceof ht.ui.TableLayout)for(var O in N)N[O].prefWidthSet=!0,N[O].weightSet=!0;g[ht.Default.setter(E)](N),"id"===E&&(i.views[N]=g)}}else if("$listeners"===E)for(var P=b.keys(F),G=0,Q=P.length;Q>G;G++){var R=P[G],S=F[R];S instanceof Function?g.on(R,S,i):g.on(R,i._config.handlers[S],i)}else if("$created"===E)z=F instanceof Function?F:m.handlers[F];else if("$override"===E)for(var T in F)g[T]=F[T];else if("$form"===E)B=!0;else if("$groupId"===E&&g instanceof o.ToggleButton){var U=i._radiosMap;U[F]||(U[F]=new o.Radios),g.setRadios(U[F])}}if(B){var V=g._$form=new ht.ui.Form(g);g.getForm=function(){return this._$form};var W=a.$form,M=r.bindForm(i.getData(),V,!0,c,ht.Default.isBoolean(W)?c:W);M&&M.length>0&&(j._bindingIds[M.join("-")]=g),g._$bindings||(g._$bindings=[]),g._$bindings.push({type:"form",formValue:W})}return z&&z.call(i,g),p&&(k[h]=g),g}})}("undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:this,Object);