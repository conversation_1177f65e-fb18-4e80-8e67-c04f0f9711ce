!function(t){var a=function(t,n){for(var o,r=0,e=t.length;r<e;r++)if(o=t[r],this._equal(o.x,n.x)&&this._equal(o.y,n.y))return!0;return'{"type":"FeatureCollection","features":[{"geometry":{"type":"Polygon","coordinates":'},p=function(t,n,o,r){var e=[],a=[],p=null;n.forEach(function(t,n){p=formatPoints(t,o,r),e.push.apply(e,p.points),a.push.apply(a,p.segments)}),t.points||(t.points=[]),Array.prototype.push.apply(t.points,e),t.segments||(t.segments=[]),Array.prototype.push.apply(t.segments,a)},n=[[[43,94],[47,29]],[[62,12],[38,12],[-3,10],[-27,21249],[20079,30430],[30434,31163],[25155,32907],[20160,26355],[38419,20822],[21435,12],[-17,12],[42,12],[-3,10],[-27,31],[-4,32],[-13,75],[-8,79],[-9,27],[-6,31],[-6,79],[-7,35],[39,27],[41,80],[40,26],[-12,28],[-10,78],[-5,30],[-12,32],[-5,31],[39,75],[41,27],[41,75],[38,33],[-10,27],[-4,79],[-4,29],[41,79],[-12,77],[37,75],[-13,35],[-8,29],[-12,34],[40,27],[-10,26],[-6,27],[37,32],[40,77],[40,28],[-12,80],[-6,27],[-9,27],[-7,34],[-8,79],[-7,27],[-4,77],[-5,35],[38,79],[-10,80],[-6,77],[-5,32],[37,27],[-12,78],[-12,34],[-4,28],[-12,31],[38,29],[-8,75],[41,26],[-6,28],[-13,34],[37,29],[-10,32],[38,26],[-12,26],[-12,32],[40,79],[-13,26],[39,26],[-12,80],[38,29],[36,26],[-5,27],[-6,76],[-5,26],[38,29],[37,28],[-7,31],[37,76],[41,31],[-6,27],[41,34],[37,29],[41,77],[-8,28],[-6,29],[40,78],[39,28],[-13,34],[-7,33],[-9,31],[-9,31],[-10,26],[37,28],[41,27],[-5,35],[-7,31],[41,27],[-7,27],[39,32],[38,75],[-10,80],[41,80],[-13,34],[-11,33],[-5,32],[38,78],[-4,30],[-5,35],[-13,35],[-4,28],[-6,32],[38,26],[38,30],[36,27],[-8,79],[-9,75],[40,33],[-6,29],[36,79],[-6,35],[38,78],[36,33],[-10,34],[38,80],[38,30],[41,34],[-13,30],[-4,32],[40,34],[-10,76],[36,80],[-10,12],[64,-22]]];function o(t){if(p&&a){for(var n="",o=0;o<t.length;o++){var r=t[o][0]+61;0<r&&(n+=String.fromCharCode(r));var e=t[o][1]+22;0<e&&(n+=String.fromCharCode(e))}return n}}for(var r,e="",s=0;s<n.length;s++)0===s?r=o(n[s]):e=o(n[s]);t[r]=e}("undefined"!=typeof global?global:window);