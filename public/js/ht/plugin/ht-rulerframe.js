!function(k,V){"use strict";var M="position",s="absolute",F="relative",y="px",x="left",w="right",L="top",r="bottom",N="display",j="none",o="block",T=ht.Default,q=T.getInternal(),K=Math.floor,d=Math.ceil,c=Math.PI,t=null,l=k.parseInt,X=function(N){return N.getContext("2d")},Q=function(){return document},m=function(b){return Q().createElement(b)},b=function(){return m("canvas")},W=function(T,w,v){T.style.setProperty(w,v,t)},D=function(j,$,s){T.def(ht.widget[j],$,s)},p=function(l,q){l.appendChild(q)},Z=function(i,W){i.removeChild(W)},q=T.getInternal(),P=q.addEventListener,_=q.removeEventListener;ht.widget.RulerFrame=function(w){var t=this,K=t._view=q.createView(null,t),h=t.$1k=b(),l=t.$2k=b(),R=t.$3k=b(),k=t.$4k=b();t._defaultRulerConfig={size:20,borderWidth:1,borderStyle:"solid",borderColor:"#888",defaultMajorTickSpacing:50,minMajorTickSpacing:10,minPhysicalMajorTickSpacing:40,maxPhysicalMajorTickSpacing:100,tickSpacingAdaptable:!0,majorTickTextFont:"12px Arial",majorTickTextColor:"#666",majorTickColor:"#888",minorTickColor:"#ccc",background:"rgba(0,0,0,0)",guideColor:"rgb(0, 173, 239)",guideWidth:2,guideVisible:!1,guideTipVisible:!1,guideTipBorderColor:"#666",guideTipTextColor:"#666",guideTipTextFont:"12px Arial",guideTipBackground:"#fff"},t._topRulerConfig={visible:!0},t._rightRulerConfig={visible:!1},t._bottomRulerConfig={visible:!1},t._leftRulerConfig={visible:!0},p(K,h),p(K,R),p(K,l),p(K,k),W(K,M,F),W(K,"box-sizing","border-box"),W(K,"-moz-box-sizing","border-box"),W(h,M,s),W(l,M,s),W(R,M,s),W(k,M,s),t.$14k=function(){t.$13k=1,t.iv()},t.$15k=function($){if(t._topRulerConfig.guideVisible||t._rightRulerConfig.guideVisible||t._bottomRulerConfig.guideVisible||t._leftRulerConfig.guideVisible||t._defaultRulerConfig.guideVisible||(t._topRulerConfig.guideTipVisible||t._rightRulerConfig.guideTipVisible||t._bottomRulerConfig.guideTipVisible||t._leftRulerConfig.guideTipVisible||t._defaultRulerConfig.guideTipVisible)&&t._component){var v=K.getBoundingClientRect();t.$16k={x:$.clientX-v.left,y:$.clientY-v.top},t.$5k()}},t.setComponent(w)},D("RulerFrame",V,{ms_v:1,ms_fire:1,ms_ac:["defaultRulerConfig","topRulerConfig","rightRulerConfig","bottomRulerConfig","leftRulerConfig","component"],setComponent:function(n){var N=this,Q=N._component,S=N.getView();if(S){if(N._component=n,N.fp("component",Q,n),Q){var R=N.getComponentView(Q);Z(S,R),_(S,"mousemove",N.$15k),N.removeComponentPropertyChangeListener(Q,N.$14k)}if(n){var m=N.getComponentView(n);p(S,m),W(m,M,s),P(S,"mousemove",N.$15k),N.addComponentPropertyChangeListener(n,N.$14k)}}},addComponentPropertyChangeListener:function(j,n){j&&j.mp&&j.mp(n)},removeComponentPropertyChangeListener:function(p,a){p&&p.ump&&p.ump(a)},getComponentHZoom:function(L){return L&&L.getZoom?L.getZoom():1},getComponentVZoom:function(z){return z&&z.getZoom?z.getZoom():1},getComponentViewRect:function(B){return B&&B.getViewRect?B.getViewRect():void 0},getComponentView:function(g){return g&&g.getView?g.getView():g},invalidateComponent:function(l){l&&l.iv&&l.iv()},validateComponent:function(A){A&&A.validate&&A.validate()},$7k:function(g,f,p,K,J,k,R,A,r,L,d,O){if(f.visible){var D=this._defaultRulerConfig,B="borderStyle",I="borderColor",Z="borderWidth",F="background",b=f[B]||D[B],c=f[I]||D[I],e=f[Z]||D[Z],u=f.size!=t?f.size:D.size,a=f[F]||D[F],s=u+e,v=this.$6k(e,b,c,g,p,J,s,R,A,r,L,d,O);W(g,J,"0px"),k?q.setCanvas(g,K-v,u):q.setCanvas(g,u,K-v),W(g,"background",a),W(g,N,o)}else W(g,N,j),W(this.getComponentView(this._component),J,"0px")},$6k:function(M,G,X,D,N,v,S,w,q,h,L,b,p){var r=0;return W(D,N,M+"px "+G+" "+X),W(this.getComponentView(this._component),v,S+y),w?(W(D,q,h+y),r+=h):W(D,q,"0px"),L?(W(D,b,p+y),r+=p):W(D,b,"0px"),r},validateImpl:function(){var o=this,O=o._component,D=o.$1k,Z=o.$2k,k=o.$3k,A=o.$4k,P=o._view,q=o._defaultRulerConfig,T=o._topRulerConfig,C=o._rightRulerConfig,K=o._bottomRulerConfig,i=o._leftRulerConfig,l=q.size;if(P&&O){var p=T.size!=t?T.size:l,X=C.size!=t?C.size:l,e=K.size!=t?K.size:l,W=i.size!=t?i.size:l;o.$7k(D,T,"border-bottom",P.offsetWidth,L,!0,i.visible,x,W,C.visible,w,X),o.$7k(Z,C,"border-left",P.offsetHeight,w,!1,T.visible,L,p,K.visible,r,e),o.$7k(k,K,"border-top",P.offsetWidth,r,!0,i.visible,x,W,C.visible,w,X),o.$7k(A,i,"border-right",P.offsetHeight,x,!1,T.visible,L,p,K.visible,r,e),o.$13k?delete o.$13k:o.invalidateComponent(O),o.validateComponent(O),o.$5k()}},$5k:function(){function A(G,$,o,j,l,J){if($.visible){var Q=X(G),w=$[C]||F,Y=$[U]!=t?$[U]:N,u=$[v]||r,H=$[n]||b,A=$.size!=t?$.size:V,Tl=$[W]||L,qb=$[g]||p,pb=$[f]||m,ne=$[R]!=t?$[R]:_,Df=$[S]!=t?$[S]:x,xp=$[B]!=t?$[B]:E,sm=$[K]!=t?$[K]:I,Sk=$[s]!=t?$[s]:T,Po=$[D]!=t?$[D]:e,nq=$[q]||yo,Ce=$[i]||ln;Y&&(w=y[J]=y.$8k(y[J]||w,$[O]||P,$[d]||k,j?Z:z,qb)),o.call(y,Q,c,h,a,M,A,j?Z:z,w,u,H,l,Tl,pb);var Mg=y.$16k;(ne||Df)&&Mg&&(j?y.$9k(Q,Mg.x,A,nq,Ce,ne,Df,xp,sm,Sk,Po):y.$10k(Q,Mg.y,A,nq,Ce,ne,Df,xp,sm,Sk,Po,l))}}var y=this,j=y.$1k,G=y.$2k,J=y.$3k,$=y.$4k,Q=y._topRulerConfig,u=y._rightRulerConfig,w=y._bottomRulerConfig,l=y._leftRulerConfig,o=y._defaultRulerConfig,H=y._component,Y=y.getComponentViewRect(H),Z=y.getComponentHZoom(H),z=y.getComponentVZoom(H),c=Y.x*Z,a=c+Y.width*Z,h=Y.y*z,M=h+Y.height*z,V=y._defaultRulerConfig.size,C="defaultMajorTickSpacing",O="maxPhysicalMajorTickSpacing",d="minPhysicalMajorTickSpacing",U="tickSpacingAdaptable",v="majorTickTextFont",n="majorTickTextColor",W="majorTickColor",f="minorTickColor",R="guideVisible",S="guideTipVisible",B="guideTipBorderColor",K="guideTipTextColor",s="guideTipTextFont",D="guideTipBackground",q="guideColor",i="guideWidth",g="minMajorTickSpacing",F=o[C],P=o[O],k=o[d],N=o[U],r=o[v],b=o[n],L=o[W],p=o[g],m=o[f],_=o[R],x=o[S],E=o[B],I=o[K],T=o[s],e=o[D],yo=o[q],ln=o[i];y._view&&H&&(A(j,Q,y.$11k,!0,!1,"_currentTopMajorTickSpacing"),A(G,u,y.$12k,!1,!0,"_currentRightMajorTickSpacing"),A(J,w,y.$11k,!0,!0,"_currentBottomMajorTickSpacing"),A($,l,y.$12k,!1,!1,"_currenLeftMajorTickSpacing"))},$8k:function(E,_,y,p,g){return y>E*p?E=K(_/p/g)*g:E*p>_&&(E=d(y/p/g)*g),E},getHTipText:function(N){var F=this,W=F._component,k=0,o=F._view.getBoundingClientRect();return W.lp?k=l(W.lp({x:N.x+o.left,y:N.y}).x):k-=l(this.getComponentView(W).style.left)||0,k},$9k:function(W,y,G,t,N,U,K,b,d,T,g){var x=this,D=x._component;W.save(),q.translateAndScale(W,0,0,1);var v=y-(l(this.getComponentView(D).style.left)||0),s=0;if(y=x.getHTipText(x.$16k),U&&(W.beginPath(),W.fillStyle=t,W.rect(v,s,N,G),W.fill()),K){W.beginPath(),W.textAlign="center",W.textBaseline="middle",W.font=T;var X=W.measureText(y).width+6;W.fillStyle=g,W.rect(v-X/2,s,X,G),W.fill(),W.strokeStyle=b,W.stroke(),W.beginPath(),W.fillStyle=d,W.fillText(y,v,s+G/2)}W.restore()},getVTipText:function(F){var I=this,X=I._component,z=0,R=I._view.getBoundingClientRect();return X.lp?z=l(X.lp({x:F.x,y:F.y+R.top}).y):z-=l(this.getComponentView(X).style.top)||0,z},formatScaleText:function(K){return Math.round(K)},$10k:function(O,E,D,u,z,g,B,r,w,Z,n,J){var k=this,o=k._component;O.save(),q.translateAndScale(O,0,0,1);var t=D/2,x=E-(l(this.getComponentView(o).style.top)||0);if(E=k.getVTipText(k.$16k),g&&(O.beginPath(),O.fillStyle=u,O.rect(t-D/2,x,D,z),O.fill()),B){O.translate(t,x),O.rotate((J?90:-90)*c/180),O.translate(-t,-x),O.beginPath(),O.textAlign="center",O.textBaseline="middle",O.font=Z;var C=O.measureText(E).width+6;O.fillStyle=n,O.rect(t-C/2,x-D/2,C,D),O.fill(),O.strokeStyle=r,O.stroke(),O.fillStyle=w,O.fillText(E,t,x)}O.restore()},$11k:function(G,Y,V,x,y,D,e,B,H,T,S,d,o){G.save(),V=0;var g=Y,$=x,i=(g+$)/2;Y=0,x=$-g,q.translateAndScale(G,0,0,1);var z=0,Q=0,u=l(D/2),C=D-u,X=S?0:u,O=B*e,I=O/10;Y-=O,x+=O,G.clearRect(Y,0,x-Y,D),G.beginPath(),G.fillStyle=o;var r=K(i/I)*I-g;for(z=r;x>z;z+=I)G.rect(z,V+X,1,C);for(z=r;z>Y;z-=I)G.rect(z,V+X,1,C);for(G.fill(),X=S?0:1,G.beginPath(),G.fillStyle=d,r=K(i/O)*O-g,z=r;x>z;z+=O)G.rect(z,V+X,1,D-1);for(z=r;z>Y;z-=O)G.rect(z,V+X,1,D-1);G.fill();var P=l(/\d+px/.exec(H)[0]),n=(P||10)/2;G.textBaseline="middle",X=S?D-n-2:n+2,G.beginPath(),G.fillStyle=T,G.font=H;var U=K(i/O)*O/e;for(z=r,Q=U;x>z;z+=O,Q+=B){var w=this.getHScaleText?this.getHScaleText(z):Q;G.fillText(this.formatScaleText(w),z+2,V+X)}for(z=r,Q=U;z>Y;z-=O,Q-=B){var w=this.getHScaleText?this.getHScaleText(z):Q;G.fillText(this.formatScaleText(w),z+2,V+X)}G.restore()},$12k:function(f,S,Y,B,u,n,e,t,$,D,W,s,J){function x(B,v,G,M,g,T){g=F.getVScaleText?F.getVScaleText(G):g,g=F.formatScaleText(g),f.translate(B+v,G),f.rotate(-M),f.translate(-B-v,-G),f.fillText(g,B+v+(T?2:1),G),f.translate(B+v,G),f.rotate(M),f.translate(-B-v,-G)}f.save(),S=0;var o=Y,p=u,h=(o+p)/2;Y=0,u=p-o,q.translateAndScale(f,0,0,1);var L=0,F=this,r=0,U=l(n/2),X=n-U,g=W?0:U,i=t*e,k=i/10;Y-=i,u+=i,f.clearRect(S,0,n,u-Y),f.beginPath(),f.fillStyle=J;var Z=K(h/k)*k-o;for(L=Z;u>L;L+=k)f.rect(S+g,L,X,1);for(L=Z;L>Y;L-=k)f.rect(S+g,L,X,1);for(f.fill(),g=W?0:1,f.beginPath(),f.fillStyle=s,Z=K(h/i)*i-o,L=Z;u>L;L+=i)f.rect(S+g,L,n-1,1);for(L=Z;L>Y;L-=i)f.rect(S+g,L,n-1,1);f.fill();var d=l(/\d+px/.exec($)[0]),z=(d||10)/2,N=90*c/180;f.textBaseline="middle",g=W?n-z:z+2,N=W?-N:N,f.beginPath(),f.fillStyle=D,f.font=$;var b=K(h/i)*i/e;for(L=Z,r=b;u>L;L+=i,r+=t)x(S,g,L,N,r,W);for(L=Z,r=b;L>Y;L-=i,r-=t)x(S,g,L,N,r,W);f.restore()},onPropertyChanged:function(){this.iv()},dispose:function(){var Q=this,g=Q._component,H=Q._view;g&&Q.removeComponentPropertyChangeListener(g,Q.$14k),H&&(_(H,"mousemove",Q.$15k),Z(H.parentNode,H),Q._view=null)}})}("undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:(0,eval)("this"),Object);