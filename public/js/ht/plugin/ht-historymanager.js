!function(p,i,D){"use strict";var V="ht",Z=p[V],s=<PERSON><PERSON>,h=s.def,U=s.getInternal();Z.HistoryManager=function(_){this._histories=[],this.setDataModel(_)},h(<PERSON><PERSON>,i,{ms_ac:["dataModel","histories","historyIndex","maxHistoryCount","disabled"],ms_fire:1,_historyIndex:-1,_betweenTransaction:0,_maxHistoryCount:200,_disabled:!1,ignoredPropertyMap:{imageLoaded:!0,children:!0,attaches:!0,shape:!0,childChange:!0,agentChange:!0,sourceAgent:!0,targetAgent:!0,edgeGroup:!0,"*":!0},ignoreDataModelPropertyMap:{},beginInteraction:function(){this.beginTransaction()},endInteraction:function(){this.endTransaction()},beginTransaction:function(){if(!this._disabled){var r=this;r._betweenTransaction++,1===r._betweenTransaction&&(r._transactionHistories={})}},endTransaction:function(){if(!this._disabled){var S=this,w=S._histories;if(S._betweenTransaction>0&&S._betweenTransaction--,0===S._betweenTransaction){if(S._transactionHistories){var R=[];for(var j in S._transactionHistories)R.push(S._transactionHistories[j]);R.length&&(w=w.slice(0,S._historyIndex+1),w.push(R),w.length>S._maxHistoryCount&&(w=w.slice(w.length-S._maxHistoryCount)),S.setHistories(w),S.setHistoryIndex(w.length-1,!0))}delete S._transactionHistories}}},setDataModel:function(I){var Z=this,q=Z._dataModel;q!==I&&(q&&(delete q._historyManager,q.ump(Z.handleDataModelPropertyChange,Z),q.umm(Z.$5p,Z),q.umd(Z.$6p,Z),q.removeHierarchyChangeListener(Z.handleHierarchyChange,Z),q.removeIndexChangeListener(Z.handleIndexChange,Z)),Z._dataModel=I,I&&(I._historyManager=Z,I.mp(Z.handleDataModelPropertyChange,Z),I.mm(Z.$5p,Z),I.md(Z.$6p,Z),I.addHierarchyChangeListener(Z.handleHierarchyChange,Z),I.addIndexChangeListener(Z.handleIndexChange,Z)),Z.fp("dataModel",q,I),Z.clear())},setHistoryIndex:function(C,A){var l=this,S=l._historyIndex,B=l._histories.length;if(-1>C?C=-1:C>=B&&(C=B-1),S!==C){if(!A){var i=C-S;i>0?l.$2p(i):0>i&&l.$1p(-i)}l._historyIndex=C,l.fp("historyIndex",S,C),l.dataModel&&l.dataModel.onHistoryManagerChanged()}},setMaxHistoryCount:function(B){var h=this,a=h._histories,U=h._maxHistoryCount;(!B||0>=B)&&(B=10),U!==B&&(h._maxHistoryCount=B,h.fp("maxHistoryCount",U,B),a.length>B&&h.clear())},cloneValue:function(O){return Z.Default.clone(O)},isPropertyUndoable:function(c){return c&&!this.ignoredPropertyMap[c]},isIndexUndoable:function(){return!1},isDataModelPropertyUndoable:function(r){return r&&!this.ignoreDataModelPropertyMap[r]},$5p:function(M){this.handleChange(M,M.kind)},$6p:function(V){this.handleChange(V,"property")},handleHierarchyChange:function(H){this.handleChange(H,"hierarchy")},handleIndexChange:function(c){this.handleChange(c,"index")},handleDataModelPropertyChange:function(o){this.handleChange(o,"dataModelProperty")},toChildrenInfo:function(h){var K={};return K.data=h,K.children=[],h.eachChild(function(P){K.children.push(this.toChildrenInfo(P))},this),K},restoreChildren:function(e){var I=e.data;e.children.forEach(function(h){var W=h.data;W.getParent()!==I&&I.addChild(W),this._dataModel.contains(W)||this._dataModel.add(W),this.restoreChildren(h)},this)},handleChange:function(Q,A){var z=this;if(!(z._disabled||z._isUndoRedoing||s.loadingRefGraph)){var v,$=(z._histories,Q.data),b=Q.property;if(!$||!($._refGraph||$ instanceof Z.RefGraph)){if("property"===A)z.isPropertyUndoable(b,$)&&(v={kind:A,data:$,property:b,oldValue:z.cloneValue(Q.oldValue,$,b),newValue:z.cloneValue(Q.newValue,$,b),event:Q});else if("hierarchy"===A||"index"===A&&z.isIndexUndoable(Q))v={kind:A,data:$,oldIndex:Q.oldIndex,newIndex:Q.newIndex,event:Q};else if("clear"===A)v={kind:A,json:Q.json,event:Q};else if("add"===A){if(v={kind:A,data:$,event:Q,childrenInfo:this.toChildrenInfo($),parent:$.getParent()},v.parent){var R=z._dataModel.getSiblings($);v.siblingsIndex=R.indexOf($)}$ instanceof Z.Node&&(v.host=$.getHost(),v.attaches=$.getAttaches()?$.getAttaches().toArray():D),$ instanceof Z.Edge&&(v.source=$.getSource(),v.target=$.getTarget())}else"remove"===A?v={kind:A,data:$,event:Q}:"dataModelProperty"===A&&z.isDataModelPropertyUndoable(b,$)&&(v={kind:A,property:b,oldValue:z.cloneValue(Q.oldValue,$,b),newValue:z.cloneValue(Q.newValue,$,b),event:Q});z.addHistory(v)}}},addHistory:function(O){var V=this;if(O)if(V._betweenTransaction){var P=(O.data?O.data._id:0)+"_"+O.kind+"_"+O.property;if("property"===O.kind||"dataModelProperty"===O.kind){var L=V._transactionHistories[P];L&&(O.oldValue=L.oldValue)}V._transactionHistories[P]=O}else{var v=V._histories;v=v.slice(0,V._historyIndex+1),v.push([O]),v.length>V._maxHistoryCount&&(v=v.slice(v.length-V._maxHistoryCount)),V.setHistories(v),V.setHistoryIndex(v.length-1,!0)}},canUndo:function(){return!this._disabled&&this._historyIndex>=0&&this._historyIndex<this._histories.length},canRedo:function(){return!this._disabled&&this._historyIndex>=-1&&this._historyIndex<this._histories.length-1},undo:function(Z){(!Z||0>=Z)&&(Z=1),this.setHistoryIndex(this._historyIndex-Z)},$1p:function($){if(this.canUndo()){var e,c=this,Z=c._dataModel,O=c._histories,F=c._historyIndex,Q=0;for(c._isUndoRedoing=!0,s.setIsolating(!0);$>0;){if(F>=0&&F<O.length){Q++,e=O[F],F--;for(var R=e.length-1;R>=0;R--){var H=e[R],_=H.kind,V=H.data,v=H.property,C=H.event,P=this.cloneValue(H.oldValue,V,v);if(H.undo)H.undo();else if("add"===_)Z.remove(V,{keepChildren:!0});else if("remove"===_)Z.contains(V)||Z.add(V,C.rootsIndex,C.datasIndex);else if("clear"===_)Z.deserialize(s.clone(H.json));else if("property"===_)if("parent"===v)P?P.addChild(V,C.oldIndex):(V.setParent(P),C.oldIndex>=0&&Z.moveTo(V,C.oldIndex));else{var A=null;0===v.indexOf("a:")?(A="attr",v=v.replace("a:","")):0===v.indexOf("s:")?(A="style",v=v.replace("s:","")):0===v.indexOf("f:")&&(A="field",v=v.replace("f:","")),U.setPropertyValue(V,A,v,P)}else if("dataModelProperty"===_){var A=null;0===v.indexOf("a:")?(A="attr",v=v.replace("a:","")):0===v.indexOf("s:")?(A="style",v=v.replace("s:","")):0===v.indexOf("f:")&&(A="field",v=v.replace("f:","")),U.setPropertyValue(Z,A,v,P)}else"hierarchy"===_?Z.moveTo(V,H.oldIndex):"index"===_?Z.moveToIndex(V,H.oldIndex):"selection"===_&&Z.sm().ss(H.oldValue)}}$--}s.setIsolating(!1),delete c._isUndoRedoing,c.afterUndo(Q)}},afterUndo:function(){},redo:function(j){(!j||0>=j)&&(j=1),this.setHistoryIndex(this._historyIndex+j)},$2p:function(X){if(this.canRedo()){var O,D=this,k=D._dataModel,a=D._histories,e=D._historyIndex,P=0;for(D._isUndoRedoing=!0,s.setIsolating(!0);X>0;){if(e>=-1&&e<a.length-1){P++,e++,O=a[e];for(var x=0;x<O.length;x++){var $=O[x],C=$.kind,I=$.data,Q=$.property,n=$.event,o=this.cloneValue($.newValue,I,Q);if($.redo)$.redo();else if("add"===C)$.parent&&!I.getParent()&&$.parent.addChild(I,$.siblingsIndex),k.contains(I)||k.add(I,n.rootsIndex,n.datasIndex),this.restoreChildren($.childrenInfo),I instanceof Z.Node&&(I.setHost($.host),$.attaches&&$.attaches.forEach(function(v){v.setHost(I)})),I instanceof Z.Edge&&(I.setSource($.source),I.setTarget($.target));else if("remove"===C)k.remove(I);else if("clear"===C)k.clear();else if("property"===C)if("parent"===Q)o?o.addChild(I,n.newIndex):(I.setParent(o),n.newIndex>=0&&k.moveTo(I,n.newIndex));else{var F=null;0===Q.indexOf("a:")?(F="attr",Q=Q.replace("a:","")):0===Q.indexOf("s:")?(F="style",Q=Q.replace("s:","")):0===Q.indexOf("f:")&&(F="field",Q=Q.replace("f:","")),U.setPropertyValue(I,F,Q,o)}else if("dataModelProperty"===C){var F=null;0===Q.indexOf("a:")?(F="attr",Q=Q.replace("a:","")):0===Q.indexOf("s:")?(F="style",Q=Q.replace("s:","")):0===Q.indexOf("f:")&&(F="field",Q=Q.replace("f:","")),U.setPropertyValue(k,F,Q,o)}else"hierarchy"===C?k.moveTo(I,$.newIndex):"index"===C?k.moveToIndex(I,$.newIndex):"selection"===C&&k.sm().ss($.newValue)}}X--}s.setIsolating(!1),delete D._isUndoRedoing,this.afterRedo(P)}},afterRedo:function(){},clear:function(){this.setHistories([]),this.setHistoryIndex(-1,!0),this._betweenTransaction=0,delete this._transactionHistories}})}("undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:(0,eval)("this"),Object);