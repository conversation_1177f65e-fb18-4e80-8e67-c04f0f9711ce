!function(f,h,G){"use strict";var W="px",e="0",m="innerHTML",C="className",R=ht.Default,b=ht.Color,I=ht.Node,L="position",E="top",k="left",x=R.animate,r=R.getInternal(),T="width",e="0",K="none",S="max-height",w="font",N="background",c="border-box",M="user-select",a="box-sizing",g="overflow",l=R.isTouchable,B=R.isTouchEvent,r=R.getInternal(),X=b.titleIconBackground,U=R.scrollBarInteractiveSize,o=/msie 9/.test(f.navigator?f.navigator.userAgent.toLowerCase():""),d=null,q=function(){return document},j=function(s){return q().createElement(s)},A=function(){return j("div")},y=function(){var m=A(),b=m.style;return b.msTouchAction=K,b.cursor="default",l&&b.setProperty("-webkit-tap-highlight-color","rgba(0, 0, 0, 0)",d),b.position="absolute",b.left=e,b.top=e,m},J=function(){return j("canvas")},z=function(){return document.body},Q=function(H,u,k){H.style.setProperty(u,k,d)},p=function(S,H){S.style.removeProperty(H)},u=function(c,K,I){R.def(ht.widget[c],K,I)},V=function(T,P){T.appendChild(P)},s=function(f,p){f.removeChild(p)},Z=r.addEventListener;r.removeEventListener,r.addMethod(R,{paletteExpandIcon:{width:16,height:16,comps:[{type:"triangle",rect:[4,4,10,8],background:X,rotation:3.14}]},paletteCollapseIcon:{width:16,height:16,comps:[{type:"triangle",rect:[4,4,10,8],background:X}]},paletteTitleLabelColor:R.labelSelectColor,paletteTitleLabelFont:R.labelFont,paletteContentLabelFont:R.labelFont,paletteContentLabelColor:"#777",paletteContentBackground:"#fff",paletteTitleHeight:R.widgetTitleHeight,paletteTitleBackground:b.titleBackground,paletteTitleHoverBackground:b.titleBackground,paletteSeparatorWidth:1,paletteSeparatorColor:G,paletteItemHoverBorderColor:b.highlight,paletteItemSelectBackground:b.highlight},!0);var $=".palette-item:hover{border: 1px solid "+R.paletteItemHoverBorderColor+" !important}"+" .palette-header:hover{background: "+R.paletteTitleHoverBackground+" !important}",H=document.createElement("style");l||(H.styleSheet?H.styleSheet.cssText=$:H.appendChild(q().createTextNode($))),q().getElementsByTagName("head")[0].appendChild(H);var v=function(K){var D=this;D.$22h=K,D.addListeners()};R.def(v,h,{ms_listener:1,getView:function(){return this.$22h.getView()},$26h:function(){var V=this;V.$36h&&z().removeChild(V.$36h),V.$23h=V.$24h=V.$25h=V.$35h=V.$36h=d},handle_touchstart:function(m){for(var Q,y=this,U=y.$22h,l=m.target,S=U.sm(),J=U.dm(),g="palette-header",h="palette-header-tool",t="palette-item",K=!1,f=!1,M=!1;l&&(l[C]||"").indexOf(g)<0&&(l[C]||"").indexOf(t)<0;)l=l.parentNode;if(l&&l[C].indexOf(h)>=0?K=!0:l&&l[C].indexOf(g)>=0?M=!0:l&&l[C].indexOf(t)>=0&&(f=!0),R.isLeftButton(m))if(y.$27h(m))y.$24h=R.getClientPoint(m),y.$25h=U.ty();else if(K){R.preventDefault(m),Q=l.parentNode.$11h;var z=J.getDataById(Q),P=z.s("tools")[l.toolIndex];P.action&&P.action.call(U)}else if(M){R.preventDefault(m),Q=l.$11h;var z=J.getDataById(Q);z.isExpanded()?z.setExpanded(!1):z.setExpanded(!0)}else if(f){Q=l.$11h;var e=J.getDataById(Q);S.ss(e),U.handleDragAndDrop&&(R.preventDefault(m),e.s("draggable")&&(U.handleDragAndDrop(m,"prepare"),y.$35h=0)),e.s("draggable")||(R.preventDefault(m),y.$24h=R.getClientPoint(m),y.$25h=U.ty())}else R.preventDefault(m),y.$24h=R.getClientPoint(m),y.$25h=U.ty();else y.$26h(m)},handle_mousedown:function(Y){this.handle_touchstart(Y)},handle_mousewheel:function(A){this.handleScroll(A,A.wheelDelta/40,A.wheelDelta!==A.wheelDeltaX)},handle_DOMMouseScroll:function(o){this.handleScroll(o,-o.detail,1)},handleScroll:function(H,w,$){var r=this.$22h;R.preventDefault(H),$&&r._41o()&&r.ty(r.ty()+20*w)},handle_mouseup:function(s){this.handle_touchend(s)},handle_touchend:function(o){var i=this;i.$37h(o),i.$26h(o)},handleWindowMouseUp:function(l){this.handleWindowTouchEnd(l)},handleWindowTouchEnd:function(O){var u=this;u.$37h(O),u.$26h(O)},$37h:function(a){var z=this,G=z.$22h;2===z.$35h&&(z.$35h=3,G.handleDragAndDrop(a,"end"))},handleWindowMouseMove:function($){this.handleWindowTouchMove($)},handleWindowTouchMove:function(c){var m=this,a=m.$22h,p=m.$23h,z=m.$24h,H=m.$25h,T=R.getClientPoint(c),J=a._29I,G=m.$36h;if(1===m.$35h||2===m.$35h){if(m.$35h=2,a.handleDragAndDrop(c,"between"),B(c)){var r=c.touches[0];c=r?r:c.changedTouches[0]}G.style.left=c.pageX-parseInt(G.width)/2+W,G.style.top=c.pageY-parseInt(G.height)/2+W}else"p"===p?a.ty(H+T.y-z.y):"v"===p&&a.ty(H+(z.y-T.y)/J.height*a._59I)},handle_mousemove:function(O){this.handle_touchmove(O)},handle_touchmove:function(N){if(!R.isDragging()&&R.isLeftButton(N)){var X=this,T=X.$22h,D=X.$27h(N);if(X.$24h){if(!X.$23h){if(R.getDistance(R.getClientPoint(N),X.$24h)<2)return;X.$23h=D?"v":"p",R.startDragging(X,N)}}else if(D)T._43o();else if(0===X.$35h){if(X.$35h=1,T.handleDragAndDrop(N,"begin"),R.startDragging(X,N),B(N)){var w=N.touches[0];N=w?w:N.changedTouches[0]}var C=X.$36h=new Image,m=T.$10h[T.sm().ld().getId()].querySelector(".image-box"),I=parseInt(m.style.width),s=parseInt(m.style.height);C.draggable=!1,C.src=m.toDataURL(),C.width=I,C.height=s,C.style.position="absolute",C.style.left=N.pageX-I/2+W,C.style.top=N.pageY-s/2+W,z().appendChild(C)}}},$27h:function(N){var d=this.$22h,f=d.getView(),v=f.getBoundingClientRect(),h=d._29I,W=N.clientX-v.left+f.scrollLeft;return d._41o()&&h.x+h.width-W<U}}),ht.widget.Palette=function(m){var Y=this,q=Y._view=r.createView(null,Y);Y.$9h={},Y.$10h={},Y.$4h={},Y._29I={x:0,y:0,width:0,height:0},Y._59I=0,Y.dm(m?m:new ht.DataModel),q[C]="ht-widget-palette",Y.$29h=new v(Y),Q(q,N,R.paletteContentBackground),Q(q,g,"auto"),Q(q,a,c),Q(q,"-moz-"+a,c),Q(q,"-webkit-"+M,K),Q(q,"-moz-"+M,K),Q(q,"-ms-"+M,K),Q(q,M,K),Q(q,"position","absolute"),Q(q,"overflow","hidden"),V(q,Y._79O=y()),Z(q,"dragstart",function(t){t.dataTransfer&&(t.dataTransfer.setData("Text","nodeid:"+t.target.$11h),t.dataTransfer.effectAllowed="all",Y.$29h.$26h())})},u("Palette",h,{ms_v:1,ms_fire:1,ms_dm:1,ms_sm:1,ms_vs:1,ms_bnb:1,ms_ac:["itemImageWidth","itemImageHeight","itemImagePadding","itemMargin","layout","autoHideScrollBar","scrollBarSize","scrollBarColor"],$30h:0,_itemImagePadding:4,_itemImageWidth:70,_itemImageHeight:50,_itemMargin:10,_layout:"largeicons",_autoHideScrollBar:R.autoHideScrollBar,_scrollBarSize:R.scrollBarSize,_scrollBarColor:R.scrollBarColor,getViewRect:function(){return this._29I},ty:function(C){return C==d?this.getTranslateY():(this.setTranslateY(C),void 0)},setTranslateY:function(C){if(this.$32h==d){var w=this,v=w.$33h(C),p=w.$30h;w.$30h=v,w.fp("translateY",p,v)}},getTranslateY:function(){return this.$30h},setLayout:function(K){var x,h,$=this,D=$._layout;$._layout=K,"smallicons"===K?x=h=20:"iconsonly"===K?x=h=50:(x=70,h=50),$.setItemImageWidth(x),$.setItemImageHeight(h),$.setItemImagePadding(4),$.fp("layout",D,K)},getDataAt:function(j){for(var W=j.target;W&&W.$11h==d;)W=W.parentNode;return W&&W.$11h!=d?this.getDataModel().getDataById(W.$11h):void 0},$20h:function(){var b=16;return l&&(b*=1.2),b},$19h:function(){return R.paletteTitleHeight},$18h:function(){var F=R.paletteSeparatorWidth,Z=R.paletteTitleBackground,o=R.paletteSeparatorColor||R.brighter(Z);return F+W+" solid "+o},$17h:function(f){Q(f,"cursor","pointer"),Q(f,"display","inline-block"),Q(f,"margin-right",(l?8:4)+W),Q(f,"vertical-align",E)},$1h:function(p){var F=this,i=A(),P=A(),M=j("span");i[C]="palette-header",Q(i,L,"relative"),Q(i,N,R.paletteTitleBackground),Q(i,"color",R.paletteTitleLabelColor),Q(i,E,e),Q(i,a,c),Q(i,"-moz-"+a,c),Q(i,"padding","0 5px 0 0"),Q(i,"border-top",F.$18h()),Q(i,T,"100%"),Q(i,"cursor","pointer"),Q(i,"white-space","nowrap"),Q(i,g,"hidden"),Q(i,w,R.paletteTitleLabelFont),Q(i,"line-height",F.$19h()+W),i.$11h=p.getId();var Y=J(),U=F.$19h(),D=F.$20h();F.$17h(Y),r.setCanvas(Y,D,U),V(i,Y);var Z=p.s("tools");if(Z)for(var X=0;X<Z.length;X++){var $=J();F.$17h($),r.setCanvas($,D,U),$[C]="palette-header-tool palette-header-tool"+p.getId()+"-"+X,$.style.position="absolute",$.style.right=(D+10)*X+"px",$.toolIndex=X,V(i,$)}return Y[C]="palette-toggle-icon-"+p.getId(),P[C]="palette-content",Q(P,"max-height",0+W),Q(P,w,R.paletteContentLabelFont),Q(P,g,"hidden"),P.$11h=p.getId(),F.$9h[p.getId()]=P,M[m]=p.getName(),Q(M,w,R.paletteTitleLabelFont),V(i,Y),V(i,M),[i,P]},$2h:function(l){var b=this,Y=b._layout,x=o&&l.s("draggable")?j("a"):A(),X=J(),d=A(),z=l.getName()||"",c=l.s("title")||l.getToolTip()||z,q=b._itemMargin;X[C]="image-box";var h=b.getItemImageWidth(),M=b.getItemImageHeight();return r.setCanvas(X,h,M),V(x,X),d[m]=z,d[C]="label-box","iconsonly"!==Y&&V(x,d),x[C]="palette-item",Q(x,"vertical-align",E),Q(x,"cursor","pointer"),Q(x,"border-radius",5+W),Q(x,"border","1px solid transparent"),Q(x,"text-align","center"),Q(x,"display","inline-block"),Q(x,"margin-left",q+W),Q(x,"margin-top",q+W),Q(x,"color",R.paletteContentLabelColor),"smallicons"===Y?(Q(X,"vertical-align","middle"),Q(x,"margin-left",2+W),Q(x,"margin-top",2+W),Q(x,"padding",2+W),Q(x,"text-align",k),Q(d,"display","inline-block"),Q(d,"min-width",b.$21h+b._itemMargin+W)):"largeicons"===Y&&(Q(d,"max-width",h+W),Q(d,"overflow","hidden")),x.$11h=l.getId(),c&&(x.title=c),l.s("draggable")&&!b.handleDragAndDrop&&(o?(x.href="#",Q(x,"text-decoration",K)):x.draggable="true"),x},$16h:function(h,w,n,E){var M=r.initContext(h);r.translateAndScale(M,0,0,1),M.clearRect(0,0,n,n);var x=(n-E)/2;R.drawStretchImage(M,R.getImage(w),"fill",0,x,E,E),M.restore()},$15h:function(O){var X=this,o=O.getId(),y=X._view.querySelector(".palette-toggle-icon-"+o),F=O.isExpanded()?R.paletteCollapseIcon:R.paletteExpandIcon;if(y&&F){var p=X.$19h(),e=X.$20h();X.$16h(y,F,p,e)}},_drawToolsIcon:function($){var i=this,F=$.s("tools");if(F)for(var A=0;A<F.length;A++){var T=i._view.querySelector(".palette-header-tool"+$.getId()+"-"+A),V=F[A].icon,t=i.$19h(),M=i.$20h();i.$16h(T,V,t,M)}},$14h:function(V){var u=this,l=V.getId(),L=u.$10h[l].querySelector(".image-box"),c=V.getImage(),v=V.s("image.stretch");if(L&&c){var b=r.initContext(L),Q=u.getItemImagePadding();Q="smallicons"===u._layout?Q/2:Q;var F=u.getItemImageWidth()-2*Q,i=u.getItemImageHeight()-2*Q;r.translateAndScale(b,0,0,1),b.clearRect(0,0,F,i),R.drawStretchImage(b,R.getImage(c),v,Q,Q,F,i,V,u,u.getBodyColor(V)),b.restore()}},validateImpl:function(){var b,n,Q,u=this,N=u.$9h,T=u._layout,h=u.$10h,O=u.$4h,D=u._view,y=u.dm();if(u.$13h&&(delete u.$13h,O={},y.each(function(D){O[D.getId()]=D})),"smallicons"===T)for(var k in O){var M=O[k];if(M instanceof I){var i=M.getName()||"",P=R.getTextSize(R.paletteContentLabelFont,i).width;u.$21h!=d&&u.$21h>P||(u.$21h=P)}}for(var k in O){Q=O[k];var v,z;if(y.contains(Q)){if(Q instanceof ht.Group){var E,f=u.$1h(Q),t=h[Q.getId()];t&&(E=t.nextSibling,s(D,E),s(D,t)),n=y.getSiblings(Q).indexOf(Q);var G=D.children[2*n]||u._79O;G&&G.parentNode?(D.insertBefore(f[0],G),D.insertBefore(E||f[1],G)):(D.appendChild(f[0]),D.appendChild(E||f[1])),h[Q.getId()]=f[0],b=N[Q.getId()]=E||f[1],z=Q.$12h;var l=Q.s("promptText");z||(Q.$12h=j("div"),Q.$12h[m]=l||"",z=Q.$12h),0===Q.getChildren().size()?b.contains(z)||V(b,z):b.contains(z)&&s(b,z)}else if(v=Q.getParent()){var c=u.$2h(Q),C=h[Q.getId()];b=N[v.getId()],C&&s(C.parentNode,C),n=y.getSiblings(Q).indexOf(Q);var K=b.children[n];K?b.insertBefore(c,K):V(b,c),h[Q.getId()]=c,u.$14h(Q)}}else{var $=h[Q.getId()],A=$.parentNode;if(Q instanceof ht.Group){var U=$.nextSibling;s(D,$),s(D,U),delete N[Q.getId()]}else s(A,$),0===A.children.length&&(v=y.getDataById(A.$11h),v&&(z=v.$12h,z&&!A.contains(z)&&V(A,z)));delete h[Q.getId()]}}u.$4h={};var w=function(){var t=u._59I,R=0;u.$32h!=d&&(clearInterval(u.$32h),R=0,delete u.$32h),u.$32h=setInterval(function(){u.$31h(),t===u._59I?(R++,R>=2&&(clearInterval(u.$32h),delete u.$32h)):(R=0,t=u._59I)},30)};for(var J in N)if(b=N[J],Q=y.getDataById(N[J].$11h),u.$15h(Q),u._drawToolsIcon(Q),Q.isExpanded()){if(b.style.maxHeight===0+W){var X=b.scrollHeight+u._itemMargin+W;x(b).duration(200).set(S,X).set("padding-bottom",u._itemMargin+W).end(function(){return function(){w()}}(X))}else b.style.maxHeight=b.scrollHeight+W;b.style.paddingBottom=u._itemMargin+W}else b.style.maxHeight!==0+W&&x(b).duration(200).set(S,e).set("padding-bottom",e).end(function(){return function(){w()}}(b));u.$28h(),u.$31h()},$31h:function(){for(var V=this,z=V._view,T=0,x=z.children,w=0;w<x.length;w++){var e=x[w];e.className&&e.className.indexOf("palette-")>=0&&(T+=e.offsetHeight)}V._59I=T,V.$30h=V.$33h(V.ty());var G=V.ty();z.scrollTop=-G,V._29I={x:0,y:-G,width:z.clientWidth,height:z.clientHeight},Q(V._79O,E,-G+W),V._93I()},$33h:function(p){var e=this,R=e._29I.height-e._59I;return R>p&&(p=R),p>0?0:Math.round(p)},redraw:function(){this.$13h||(this.$13h=1,this.iv())},onPropertyChanged:function(s){["autoHideScrollBar","scrollBarSize","scrollBarColor","translateY"].indexOf(s.property)<0&&this.redraw(),"translateY"===s.property&&(this.iv(),this._43o())},findDataByName:function(r){for(var _=this.dm().getDatas(),f=0;f<_.size();f++){var C=_.get(f);if(C.getName()===r)return C}},setDataModel:function(g){var M=this,U=M._dataModel,n=M._selectionModel;U!==g&&(U&&(U.umm(M.$6h,M),U.umd(M.$8h,M),U.umh(M.$7h,M),n||U.sm().ums(M.$28h,M)),M._dataModel=g,g.mm(M.$6h,M),g.md(M.$8h,M),g.mh(M.$7h,M),n?n._21I(g):g.sm().ms(M.$28h,M),M.sm().setSelectionMode("single"),M.fp("dataModel",U,g))},$6h:function(o){var D=this,w=D._view,J=o.data,X=D.$4h;"add"===o.kind?X[J.getId()]=J:"remove"===o.kind?X[J.getId()]=J:"clear"===o.kind&&(D.$10h={},D.$9h={},D.$4h={},w[m]=""),D.iv()},$7h:function(p){var o=this,a=p.data;o.$4h[a.getId()]=a,o.iv()},$8h:function(l){var T=this,Z=l.data,g=l.property;"expanded"===g?T.iv():(T.$4h[Z.getId()]=Z,T.iv())},$28h:function(){var M,s=this,c=s.sm(),P="palette-item",u=c.ld();this.dm().each(function(U){M=s.$10h[U.getId()],M&&M[C].indexOf(P)>=0&&(U===u?Q(M,N,R.paletteItemSelectBackground):p(M,N))})}})}("undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:(0,eval)("this"),Object);