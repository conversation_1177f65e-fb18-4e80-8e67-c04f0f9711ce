!function(A){"use strict";var t="ht",p=A[t],u=function(){return document},v=function(){return u().body},$=function(o,v,e){o.style.setProperty(v,e,null)},Q=function(s){return u().createElement(s)},w=function(){return Q("div")},M=function(){var s=Q("canvas");return s},G=function(i,z){$(i,"-webkit-transform",z),$(i,"-ms-transform",z),$(i,"transform",z)},V=function(W,r){$(W,"-webkit-transform-origin",r),$(W,"-ms-transform-origin",r),$(W,"transform-origin",r)},h=function(g,p){g.appendChild(p)},n=function(t,z){t.removeChild(z)},S=A.parseInt,H=p.Default,Z=H.getInternal(),E=Z.addEventListener,x=(Z.removeEventListener,Math.PI),W="white-space",f="visibility",e="left",O="top",s="width",D="height",I="position",K="display",r="z-index",U="px",y="0 0",Y="absolute",g="visible",k="hidden",B="none",R="block",j="nowrap",C="rgba(0, 0, 0, 0.005)";H.setImage("node_dragger","data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAN9JREFUeNrsV9sNhDAMKyzQVdgARmGzrMJNUFZhAh6nfkVcG9PQgHSR8lEksJs6pnGuLCimSRzAa0yyBK9O4gy8GokU+O0kJOAwiQYg0LP1xNYDW3+0CfBYNb7VOuN4LAGpiOaYUhFDas9F2NPHDELNENJqaHgBgSQJ3ufakfQJqckERcOiK+Ae1FGWBNKGh9oX5WPpLpdNYfffijbsxTHh7VKP7388n1g1h7OKUoUuyGpJakQEuhwkZAKcDXVOdWcrOrL/feBVBHI/q8fcjE1nA9PpyHQ+NJ2Qi8A3AQYAOtS27fCoRY0AAAAASUVORK5CYII=");var z=p.graph.GraphView.prototype,d=z._42;z.adjustHtmlNodeIndex=!0,z._42=function(j,Z){if(d.call(this,j,Z),this.adjustHtmlNodeIndex)for(var t=this.getDataModel()._datas._as,k=t.length,g=1,q=0;k>q;q++){var I=t[q];if(I instanceof m){var G=this.getDataUI(I);$(G.$2f,r,g+""),$(G.$3f,r,g+1+""),g+=2}}};var i=p.HtmlNodeUI=function(R,c){var H=this;i.superClass.constructor.call(H,R,c);var z=H.$2f=w(),T=H.$3f=M();$(z,I,Y),$(z,f,k),$(z,W,j),T.draggable=!1,$(T,I,Y),$(T,K,B),V(T,y),E(z,"change",function(W){var i=W.target,J=i.bind||i.getAttribute("bind"),V=i.type&&"checkbox"===i.type?i.checked:i.value,v=c.getContext();J&&v&&(v[J]=V,H.$4f=JSON.stringify(v))}),["mousedown","touchstart","keydown","mousewheel","DOMMouseScroll"].forEach(function(U){E(z,U,this.$9f.bind(this))},H)};H.def(i,Z.ui().NodeUI,{_visible:!0,$11f:function(){var M=this,w=M.$3f,I=M._data,E=I.getDraggerImageWidth(),j=I.getDraggerImageHeight(),S=I.getDraggerImage(),O=Z.initContext(w);O.beginPath(),Z.setCanvas(w,E,j),Z.translateAndScale(O,0,0,1),O.clearRect(0,0,E,j),H.drawImage(O,H.getImage(S),0,0,E,j),O.restore()},_80o:function(d){i.superClass._80o.call(this,d);var E=this,M=E._data,Q=M._padding,_=2*Q,A=E.$2f,m=E.$3f,v=E.gv,F=v.getZoom(),b=v.getTranslateX(),L=v.getTranslateY(),j=v.getView(),t=E._83o,p=M._width,q=M._height,N=t.position,X=t.rotation,P=(p-_)/M.$5f*F,a=(q-_)/M.$6f*F,y=E._html,V=M._html,T=M.getHtmlType();if("html"===T){var l=M.getContext()||{},n=E.$4f,c=M.$10f,J=JSON.stringify(l);y&&n&&y===V&&n===J||(E.$4f=J,E._html=V,A.innerHTML=c?c(l):V)}else if(null!=T){var o=M.getHtml();"ht"===T&&(o=o.getView()),y&&y===o&&A.contains(y)||(E._html=o,A.innerHTML="",h(A,o))}if(!A.parentNode){var W=v.$1f;if(!W){var H=w();$(H,I,Y),$(H,r,"0"),W=v.$1f=H;var Z=v._canvas.nextSibling;Z?j.insertBefore(H,Z):h(j,H)}h(W,A),h(W,m),M.onContentInitialized&&M.onContentInitialized(A)}if(M._scalable){var u=M.$5f,z=M.$6f;G(A,"rotate("+180*(X/x)+"deg) scale("+P+","+a+")"),$(A,s,""),$(A,D,""),$(A,e,(N.x-u/F/2)*F+b+U),$(A,O,(N.y-z/F/2)*F+L+U)}else{var Ef=S(A.style.width),xi=S(A.style.height),Jo=S((p-_)*F),Si=S((q-_)*F),bg="100%",ym=A.children[0];$(ym,s,bg),$(ym,D,bg),(Ef!==Jo||xi!==Si)&&($(A,s,Jo+U),$(A,D,Si+U),"ht"===T&&V.invalidate()),G(A,"rotate("+180*(X/x)+"deg)"),$(A,e,(N.x-Jo/F/2)*F+b+U),$(A,O,(N.y-Si/F/2)*F+L+U)}var Fp=E.dragRect;v.isMovable(M)&&v.isSelected(M)&&Fp?(d.save(),d.fillStyle=C,d.fillRect(Fp.x,Fp.y,Fp.width,Fp.height),d.restore(),$(m,e,Fp.x*F+b+U),$(m,O,Fp.y*F+L+U),G(m,"scale("+F+","+F+")"),$(m,K,R),E.$11f()):$(m,K,B),$(A,f,this._visible?g:k)},dispose:function(){var W=this.gv.$1f;this.$2f.parentNode===W&&W.removeChild(this.$2f),this.$3f.parentNode===W&&W.removeChild(this.$3f)},_84o:function(c){this._visible=c,$(this.$2f,f,c?g:k),$(this.$3f,K,c?R:B)},_3O:function(){var j=this,R=j.gv,Q=j._data;i.superClass._3O.call(j);var y=Q.getRect();R.isEditable(Q)&&(j.dragRect={x:y.x+y.width+Q._padding,y:y.y+10,width:Q.getDraggerImageWidth(),height:Q.getDraggerImageHeight()},j._68o(j.dragRect))},rectIntersects:function(X){var w=this._79o();return p.Default.intersection(w,X)?!0:void 0},$9f:function(c){var e=this.gv,O=this._data;e.sm().contains(O)&&c.stopPropagation()}});var m=p.HtmlNode=function(){m.superClass.constructor.call(this)};p.Default.def(m,p.Node,{ms_ac:["html","context","scalable","padding","draggerImage","draggerImageWidth","draggerImageHeight"],_padding:p.Default.isTouchable?12:6,_image:null,_scalable:!0,_draggerImage:"node_dragger",_draggerImageWidth:20,_draggerImageHeight:20,setHtml:function(q){var o=this,g=o._html;o._html=q,"html"===o.getHtmlType()&&"Handlebars"in A&&(o.$10f=Handlebars.compile(q)),o.$13f(),o.fp("html",g,q)},setContext:function(Q){var X=this,s=X._context;X._context=Q,X.fp("context",s,Q),X.$13f()},setScalable:function(O){var Q=this,F=Q._scalable;Q._scalable=O,Q.fp("scalable",F,O),Q.$13f()},getHtmlType:function(){var k=this._html;return k?"string"==typeof k?"html":k.getView?"ht":"dom":null},$13f:function(){var i=this,G=i._html,N=i.$10f;if(G){var F=w(),O=!1,B=i.getHtmlType();if($(F,I,Y),$(F,W,j),$(F,f,k),"html"===B?(F.innerHTML=N?N(i.getContext()||{}):G,O=!0):"ht"===B?(h(F,G.getView()),O=!0):"dom"===B&&(h(F,G),O=!0),O){var m=2*i._padding;h(v(),F),i.$5f=F.scrollWidth,i.$6f=F.scrollHeight,i._width=i.$5f+m,i._height=i.$6f+m,i._originWidth=i._width,i._originHeight=i._height,n(v(),F)}}},getUIClass:function(){return p.HtmlNodeUI}})}("undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:(0,eval)("this"),Object);