!function(s,J){"use strict";var _="ht",K=s[_],y="innerHTML",k="className",T=null,q="px",Y=<PERSON><PERSON>,P=Y.getInternal(),d="0",D=function(){return document},F=function(Y){return D().createElement(Y)},z=function(){return F("canvas")},e=function(t,$,f){t.style.setProperty($,f,T)},H=function(p,t,l){Y.def(K.widget[p],t,l)},h=function(p,S){p.appendChild(S)},O=function(G,S){G.removeChild(S)},j=P.addEventListener,M=P.removeEventListener,p=Y.isTouchable,U=Y.isTouchEvent;P.addMethod(Y,{menuLabelFont:(p?"16":"13")+"px arial, sans-serif",menuLabelColor:"#000",menuBackground:"#F0EFEE",menuHoverBackground:"#648BFE",menuHoverLabelColor:"#fff",menuSeparatorWidth:1,menuSeparatorColor:"#999"},!0),K.widget.Menu=function(v){var g=this,y=g._view=P.createView(null,g),b=g.$1g=new K.widget.ContextMenu,N=F("ul");b._r=!0,b._view[k]+=" ht-widget-dropdownmenu",y[k]="ht-widget-menu",N[k]="header",e(y,"margin",d),e(y,"padding",d),e(y,"background",Y.menuBackground),e(y,"-webkit-user-select","none"),e(y,"-moz-user-select","none"),e(y,"user-select","none"),e(y,"text-align","left"),e(y,"border-bottom",Y.menuSeparatorWidth+"px solid "+Y.menuSeparatorColor),e(y,"cursor","default"),e(y,"overflow","auto"),e(y,"white-space","nowrap"),e(y,"font",Y.menuLabelFont),e(y,"color",Y.menuLabelColor),e(y,"box-sizing","border-box"),e(y,"-moz-box-sizing","border-box"),e(N,"list-style","none"),e(N,"margin",d),e(N,"padding",d),e(N,"display","inline-block"),h(y,N),g.setItems(v),g.$2g=function(b){g.$3g(b)},g.$4g=function(X){g.$5g(X)},g.$6g=function(A){g.$7g(A)},g.$8g=function(V){g.$9g(V)},g.$9b=function(i){g.$10g(i)},g._autoShow=!0,g.setAutoShow(!1),b.afterHide=function(){g.$11g()},b.afterShow=function(){g.$12g()},M(D(),"keydown",b.$3b),g.$3b=function(J){g.$13g(J)},g.invalidate()},H("Menu",J,{_items:T,$14g:Y.menuHoverBackground,$15g:Y.menuHoverLabelColor,$16g:{},_enableGlobalKey:!1,ms_v:1,$21g:"smallicons",$22g:0,$23g:0,$24g:"left",getDropDownMenu:function(){return this.$1g},setLayout:function(u){var o=this;if(o.$21g=u,o.setItems(o._items),"largeicons"===u){for(var l=o._view.querySelectorAll(".header-item"),z=0,I=0;I<l.length;I++){var y=l[I];z=Math.max(z,y.clientWidth)}for(var I=0;I<l.length;I++){var y=l[I];e(y,"min-width",z+q)}}this.invalidate()},getLayout:function(){return this.$21g},setHeaderItemHGap:function(o){this.$22g=o;for(var X=this._view.querySelectorAll(".header-item"),P=0;P<X.length;P++){var u=X[P];e(u,"margin-left",o+q),e(u,"margin-right",o+q)}},getHeaderItemHGap:function(){return this.$22g},setHeaderItemVGap:function(R){this.$23g=R;for(var S=this._view.querySelectorAll(".header-item"),j=0;j<S.length;j++){var C=S[j];e(C,"margin-top",R+q),e(C,"margin-bottom",R+q)}},getHeaderItemVGap:function(){return this.$24g},setHeaderItemAlign:function(M){this.$24g=M,e(this._view,"text-align",M)},getHeaderItemAlign:function(){return this.$23g},enableGlobalKey:function(){var z=this,b=z._enableGlobalKey;b===!1&&(j(D(),"keydown",z.$3b),z._enableGlobalKey=!0)},disableGlobalKey:function(){this._enableGlobalKey=!1,M(D(),"keydown",this.$3b)},setHoverBackground:function(c){this.$14g=c},setHoverColor:function(h){this.$15g=h},setItems:function(N){var x=this,d=x._view,b=x.$21g;if(x._items=N,d.children[0][y]="",x.$16g={},N&&N.length){for(var V=d.children[0],P=0,L=D().createDocumentFragment();P<N.length;P++){var K=N[P],a=F("li"),$=F("span");if(K.icon){var H=z();H[k]="menu-item-icon","smallicons"===b?(e(H,"height","1.2em"),e(H,"width","1.2em"),e(H,"vertical-align","middle")):(e(H,"height","32px"),e(H,"width","32px"),e(H,"display","block"),e(H,"margin","0 auto")),H.$20g=K.icon,h(a,H)}a[k]="header-item",e(a,"display","inline-block"),e(a,"vertical-align","top"),e(a,"padding","0 1.2em"),e(a,"line-height","1.8em"),"largeicons"===b&&e(a,"text-align","center"),e(a,"background-color","rgba(0,0,0,0)"),a.setAttribute("data-index",P),x.$16g[P]=K.items,$[y]=K.label,"iconsonly"!==b&&h(a,$),h(L,a)}h(V,L)}},showDropdownMenu:function(E){var W=this,C=W.$16g[E],Z=W.$1g,r=W._view.children[0].children[E],n=W.$17g;if(r&&r!==n){n&&W.hideDropdownMenu();var _=r.getBoundingClientRect(),S=Y.getWindowInfo();W.$17g=r,Z.setItems(C),Z.show(_.left+S.left,_.top+_.height+S.top,!1)}},hideDropdownMenu:function(){this.$1g.hide()},getItemByProperty:function($,K){var u=this,p=u._items;return p&&0!==p.length?u.$1g.getItemByProperty($,K,p):T},$12g:function(){var v=this,d=v.$17g;d.style.background=v.$14g,d.style.color=v.$15g,v._autoShow||j(D(),p?"touchstart":"mousedown",v.$9b)},$11g:function(){var F=this,w=F.$17g;w&&(w.style.background="",w.style.color="",F.$17g=T),M(D(),p?"touchstart":"mousedown",F.$9b)},$10g:function(Y){var z=this,r=z._view,d=z.$1g,C=r.children[0];!D().body.contains(r)||C.contains(Y.target)||d._view.contains(Y.target)||z.hideDropdownMenu()},$13g:function(F){var v=this,O=v.$1g;D().body.contains(v._view)&&O.$13b.$4b.call(O.$13b,F,v._items)},setAutoShow:function(P){var V=this,d=V.$1g,X=V._view;V._autoShow!==P&&(V._autoShow?(M(X,"mouseover",V.$2g),M(X,"mouseout",V.$4g),M(d._view,"mouseout",V.$4g),p||(j(X,"mouseover",V.$8g),j(X,"mouseout",V.$8g)),j(X,p?"touchstart":"mousedown",V.$6g)):(M(X,"mouseover",V.$8g),M(X,"mouseout",V.$8g),M(X,p?"touchstart":"mousedown",V.$6g),M(D(),p?"touchstart":"mousedown",V.$9b),p||(j(X,"mouseover",V.$2g),j(X,"mouseout",V.$4g),j(d._view,"mouseout",V.$4g))),V._autoShow=P)},$3g:function(H){var L=this,E=L._view.children[0],D=H.target;if(E!==D&&E.contains(D)){for(;"header-item"!==D[k];)D=D.parentNode;L.showDropdownMenu(D.getAttribute("data-index"))}},$5g:function(W){var y=this,r=y._view.children[0],N=y.$1g,m=W.target,G=W.relatedTarget;!r.contains(m)&&!N._view.contains(m)||r.contains(G)||N._view.contains(G)||y.hideDropdownMenu()},$7g:function(w){w.preventDefault();var F=this,C=F._view.children[0],G=F.$1g,L=w.target;if(Y.isLeftButton(w)&&C!==L&&C.contains(L))if(U(w)){for(;"header-item"!==L[k];)L=L.parentNode;var S=L.getAttribute("data-index"),G=F.$1g,p=F._view.children[0].children[S],B=F.$17g;G.isShowing()&&F.hideDropdownMenu(),p!==B&&F.showDropdownMenu(S)}else if(G.isShowing())F.hideDropdownMenu();else{for(;"header-item"!==L[k];)L=L.parentNode;F.showDropdownMenu(L.getAttribute("data-index"))}},$9g:function(P){var L=this,u=L._view,f=L.$1g,o=P.target;if(u.contains(o)){for(var h=u.querySelectorAll(".header-item"),i=T,H=0;H<h.length;H++){var F=h[H];F.style.background="",F.style.color="","mouseover"===P.type?F.contains(o)&&(i=F):"mouseout"===P.type&&f.isShowing()&&L.$17g===F&&(i=F)}f.isShowing()&&(i||(i=L.$17g),L.showDropdownMenu(i.getAttribute("data-index"))),i&&(i.style.background=L.$14g,i.style.color=L.$15g)}},getShowingMenuIndex:function(){var G=this.$17g;return G?G.getAttribute("data-index"):-1},addTo:function(s){var i=this,I=i._view;h(s,I),i.invalidate()},dispose:function(){var J=this,L=J._view,X=J.$1g;L&&(J._autoShow?(M(L,"mouseover",J.$2g),M(L,"mouseout",J.$4g),M(X._view,"mouseout",J.$4g)):(M(L,"mouseover",J.$8g),M(L,"mouseout",J.$8g),M(L,p?"touchstart":"mousedown",J.$6g),M(D(),p?"touchstart":"mousedown",J.$9b)),M(D(),"keydown",J.$3b),X.dispose(),L.parentNode&&O(L.parentNode,L),J._view=J.$1g=J.$16g=J._items=J.$17g=J.$2g=J.$4g=J.$6g=J.$8g=J.$9b=J.$3b=T)},$19g:function(W,y,s,H){var G=P.initContext(W);P.translateAndScale(G,0,0,1),G.clearRect(0,0,s,H),Y.drawStretchImage(G,Y.getImage(y),"fill",0,0,s,H),G.restore()},validateImpl:function(){var p,N,j,_=this,L=_._view,m=L.querySelectorAll(".menu-item-icon");for(j=0;j<m.length;j++){var q=m[j];p=q.clientWidth,N=q.clientHeight,p&&N&&(P.setCanvas(q,p,N),_.$19g(q,Y.getImage(q.$20g),p,N))}}})}("undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:(0,eval)("this"),Object);