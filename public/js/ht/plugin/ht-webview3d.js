!function(c){"use strict";function t(j){var N,Y,q,G,F,p,t,R=[],A=0;N=W.createDiv(!0);var f=j.getView();f.insertBefore(N,j.getCanvas()),Y=d.Default.createDiv(),Y.style.WebkitTransformStyle="preserve-3d",Y.style.MozTransformStyle="preserve-3d",Y.style.transformStyle="preserve-3d",N.appendChild(Y),[{event:"mousedown",style:"none"},{event:"mouseup",style:"auto"},{event:"touchstart",style:"none"},{event:"touchend",style:"auto"}].forEach(function(q){var F=q.style;l.addEventListener(f,q.event,function(){j.isDragProtectForWebView()&&(N.style.pointerEvents=F)})}),this.updateWebView=function(){if(j.getWidth()&&j.getHeight()){var z=j.getCanvas(),E=1*z.style.width.slice(0,-2),c=1*z.style.height.slice(0,-2);(R[0]!==E||R[1]!==c)&&(R[0]=E,R[1]=c,N.style.width=E+"px",N.style.height=c+"px",Y.style.width=E+"px",Y.style.height=c+"px");var X=j._projectMatrix[5]*R[1]/2;X!==q&&(q=X,N.style.WebkitPerspective=q+"px",N.style.MozPerspective=q+"px",N.style.perspective=q+"px"),p="translateZ("+q+"px)"+n(j._viewMatrix);var f=p+"translate("+R[0]/2+"px,"+R[1]/2+"px)";G===f||F||(Y.style.WebkitTransform=f,Y.style.MozTransform=f,Y.style.transform=f,G=f),A++,t=!1,j.dm().each(function(S){S.isWebView&&Z(S)});for(var S,w=Y.children,Q=[],K=0,M=w.length;M>K;K++)S=w[K],S._isHtWebView&&S._renderCookie!==A&&Q.push(S);Q.length&&Q.forEach(function(l){Y.removeChild(l)})}};var $=new Array(16),Z=function(g){var P=g.getAttach();if(P){var J=j.isVisible(g);if(J&&P.parentNode!==Y?Y.appendChild(P):!J&&P.parentNode&&W.removeHTML(P),J){P._renderCookie=A,t=!0;var a=g.getFinalScale3d(),D=g._prefrenceSize;D&&D[0]?D[1]||(D[1]=D[0]/a[0]*a[1]):D=[a[0],a[1]],P.style.width=D[0]+"px",P.style.height=D[1]+"px";var B=1/D[0],Z=1/D[1],w=1,v=g.mat;$[0]=v[0]*B,$[1]=v[1]*B,$[2]=v[2]*B,$[3]=v[3]*B,$[4]=v[4]*Z,$[5]=v[5]*Z,$[6]=v[6]*Z,$[7]=v[7]*Z,$[8]=v[8]*w,$[9]=v[9]*w,$[10]=v[10]*w,$[11]=v[11]*w,$[12]=v[12],$[13]=v[13],$[14]=v[14],$[15]=v[15];var c=m($,F?"translate("+R[0]/2+"px,"+R[1]/2+"px)"+p:""),L=P.$a2;L!==c&&(P.$a2=c,P.style.WebkitTransform=c,P.style.MozTransform=c,P.style.transform=c)}}}}function C(V){return Math.abs(V)<1e-10?0:V}function n(U){var v=U;return"matrix3d("+C(v[0])+","+C(-v[1])+","+C(v[2])+","+C(v[3])+","+C(v[4])+","+C(-v[5])+","+C(v[6])+","+C(v[7])+","+C(v[8])+","+C(-v[9])+","+C(v[10])+","+C(v[11])+","+C(v[12])+","+C(-v[13])+","+C(v[14])+","+C(v[15])+")"}function m(Z,P){var l=Z,_="matrix3d("+C(l[0])+","+C(l[1])+","+C(l[2])+","+C(l[3])+","+C(-l[4])+","+C(-l[5])+","+C(-l[6])+","+C(-l[7])+","+C(l[8])+","+C(l[9])+","+C(l[10])+","+C(l[11])+","+C(l[12])+","+C(l[13])+","+C(l[14])+","+C(l[15])+")";return"translate(-50%,-50%)"+(P||"")+_}var g="ht",d=c[g],W=d.Default,l=W.getInternal(),r=l.superCall,b=d.graph3d.Graph3dView,U=b.prototype.validateImpl;b.prototype.validateImpl=function(){U.call(this);var x=this._webViewRenderer;x||(x=this._webViewRenderer=new t(this)),x.updateWebView()},b.prototype.isDragProtectForWebView=function(){return this._dragProtectForWebView},b.prototype.setDragProtectForWebView=function(m){this._dragProtectForWebView=m};var o=d.WebView3d=function(){var y=this;r(o,y),y.s({shape3d:"billboard","shape3d.reverse.flip":!0})},i=[1,1,1,1],P=[0,0,0,0];W.def(g+"."+"WebView3d",d.Node,{ms_ac:["attach"],isWebView:!0,attachDOM:function(n,g,l){if(!n)return this.detachDOM();if("string"==typeof n){var J=document.createElement("iframe");J.src=n,n=J}var Q=n.style;Q.position="absolute",Q.border=0,Q.outline=0,Q.padding=0,Q.margin=0,n._isHtWebView=!0,this.setAttach(n),this._prefrenceSize=[g,l],this.s("shape3d.blend",P)},detachDOM:function(){this.setAttach(null),this.s("shape3d.blend",this.getBgColor())},setBgColor:function(Q){this.a("defaultBgColor",Q),this.getAttach()||this.s("shape3d.blend",Q)},getBgColor:function(){return this.a("defaultBgColor")||i}})}("undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:(0,eval)("this"),Object);