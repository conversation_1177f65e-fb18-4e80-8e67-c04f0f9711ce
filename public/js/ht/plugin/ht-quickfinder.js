!function(e,p){"use strict";var t="ht",M=e[t],G=function(Z){return(/ble$/.test(Z)||/ed$/.test(Z)||<PERSON><PERSON>etter[Z]?"is":"get")+<PERSON>.charAt(0).toUpperCase()+Z.slice(1)};<PERSON>.<PERSON>inder=function(j,f,C,X,q){var s=this;s.$9j={},s.$1j=j,s.$2j=f,s.$3j=C,s.$4j=X||s.getValue,s.$5j=q||s.$5j,j.each(s.$7j,s),j.mm(s.$11j,s,!0),j.md(s.$12j,s,!0)},M.<PERSON>fault.def(<PERSON>.<PERSON>inder,p,{$6j:"__ht__null__",getValueFunc:function(){return this.$4j},getFilterFunc:function(){return this.$5j},$11j:function(L){"add"===L.kind?this.$7j(L.data):"remove"===L.kind?this.$8j(L.data):"clear"===L.kind&&(this.$9j={})},$12j:function(O){var v=this,R=v.$3j,i=v.$2j;if(v.$5j(O.data)){if(null==R&&i===O.property);else if("style"===R&&"s:"+i===O.property);else if("attr"!==R||"a:"+i!==O.property)return;var Y=v.$10j(O.oldValue);Y&&Y.remove(O.data),v.$7j(O.data)}},$10j:function(H){return H=null==H?this.$6j:H,this.$9j[H]},find:function(X){var t=this.$10j(X);return t?t.toList():new M.List},findFirst:function(f){var g=this.$10j(f);return!g||g.isEmpty()?null:g.get(0)},$7j:function(Q){var V=this;if(V.$5j(Q)){var _=V.$4j(Q),t=V.$10j(_);t||(t=new M.List,_=null==_?V.$6j:_,V.$9j[_]=t),t.add(Q)}},$8j:function(Y){var m=this;if(m.$5j(Y)){var X=m.$4j(Y),M=m.$10j(X);M&&(M.remove(Y),M.isEmpty()&&(X=null==X?m.$6j:X,delete m.$9j[X]))}},dispose:function(){this.$1j.umm(this.$11j,this),this.$1j.umd(this.$12j,this),delete this.$1j},getDataModel:function(){return this.$1j},getAccessType:function(){return this.$3j},getPropertyName:function(){return this.$2j},$5j:function(b){return null!=this.$3j||this.$4j!==this.getValue||b[G(this.$2j)]?!0:!1},getValue:function(i){var Z=this,C=Z.$3j,f=Z.$2j;return null==C?i[G(f)]():"style"===C?i.s(f):"attr"===C?i.a(f):void 0}})}("undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:(0,eval)("this"),Object);