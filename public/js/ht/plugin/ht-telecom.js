!function(L,K){"use strict";var R=ht.AlarmSeverity=function(n,H,I,W,x){this.value=n,this.name=H,this.nickName=I,this.color=W,this.displayName=x};ht.Default.def("ht.AlarmSeverity",K,{toString:function(){return this.displayName||this.name}}),function(){R.severities=new ht.List,R._vm={},R._nm={},R._cp=function(P,h){if(P&&h){var z=P.value-h.value;return z>0?1:0>z?-1:0}return P&&!h?1:!P&&h?-1:0},R.each=function(Y,S){R.severities.each(Y,S)},R.getSortFunction=function(){return R._cp},R.setSortFunction=function(E){R._cp=E,R.severities.sort(E)},R.add=function(q,F,U,P,E){var n=new R(q,F,U,P,E);return R._vm[q]=n,R._nm[F]=n,R.severities.add(n),R.severities.sort(R._cp),n},R.remove=function(B){var G=R._nm[B];return G&&(delete R._nm[B],delete R._vm[G.value],R.severities.remove(G)),G},R.CRITICAL=R.add(500,"Critical","C","#FF0000"),R.MAJOR=R.add(400,"Major","M","#FFA000"),R.MINOR=R.add(300,"Minor","m","#FFFF00"),R.WARNING=R.add(200,"Warning","W","#00FFFF"),R.INDETERMINATE=R.add(100,"Indeterminate","N","#C800FF"),R.CLEARED=R.add(0,"Cleared","R","#00FF00"),R.isClearedAlarmSeverity=function(y){return y?0===y.value:!1},R.getByName=function(v){return R._nm[v]},R.getByValue=function(b){return R._vm[b]},R.clear=function(){R.severities.clear(),R._vm={},R._nm={}},R.compare=function(b,a){return R._cp(b,a)}}();var a=ht.AlarmState=function(f){this._d=f,this._nm={},this._am={},this._ps=null,this._haa=null,this._hna=null,this._hoa=null,this._hta=null,this._hls=!1,this._aac=0,this._nac=0};ht.Default.def("ht.AlarmState",K,{_ep:!0,_f:function(){this._c1(),this._c2(),this._c3(),this._c4(),this._c5(),this._c6(),this._c7(),this._d.fp("alarmState",null,this)},getHighestAcknowledgedAlarmSeverity:function(){return this._haa},getHighestNewAlarmSeverity:function(){return this._hna},getHighestOverallAlarmSeverity:function(){return this._hoa},getHighestNativeAlarmSeverity:function(){return this._hta},hasLessSevereNewAlarms:function(){return this._hls},_c1:function(){var e=null;for(var Q in this._am)Q=R.getByName(Q),R.isClearedAlarmSeverity(Q)||0!==this.getAcknowledgedAlarmCount(Q)&&(e=e?R.compare(e,Q)>0?e:Q:Q);this._haa=e},_c2:function(){var x=null;for(var u in this._nm)u=R.getByName(u),R.isClearedAlarmSeverity(u)||0!==this.getNewAlarmCount(u)&&(x=x?R.compare(x,u)>0?x:u:u);this._hna=x},_c3:function(){if(!this._hna)return this._hls=!1,void 0;for(var J in this._nm)if(J=R.getByName(J),!R.isClearedAlarmSeverity(J)&&0!==this.getNewAlarmCount(J)&&R.compare(this._hna,J)>0)return this._hls=!0,void 0;this._hls=!1},_c4:function(){var C=this._haa,r=this._hna,Z=this._ps;this._hoa=C,R.compare(r,this._hoa)>0&&(this._hoa=r),R.compare(Z,this._hoa)>0&&(this._hoa=Z)},_c5:function(){var z=this._haa,M=this._hna;this._hta=z,R.compare(M,this._hta)>0&&(this._hta=M)},increaseAcknowledgedAlarm:function(z,C){if(0!==C){C=C||1;var J=this._am[z.name]||0;J+=C,this._am[z.name]=J,this._f()}},increaseNewAlarm:function(N,D){if(0!==D){D=D||1;var r=this._nm[N.name]||0;r+=D,this._nm[N.name]=r,this._f()}},decreaseAcknowledgedAlarm:function(T,t){if(0!==t){t||(t=1);var Y=this._am[T.name]||0;if(Y-=t,0>Y)throw"Alarm count can not be negative";this._am[T.name]=Y,this._f()}},decreaseNewAlarm:function(q,i){if(0!==i){i||(i=1);var W=this._nm[q.name]||0;if(W-=i,0>W)throw"Alarm count can not be negative";this._nm[q.name]=W,this._f()}},acknowledgeAlarm:function(f){this.decreaseNewAlarm(f,1),this.increaseAcknowledgedAlarm(f,1)},acknowledgeAllAlarms:function(h){if(h){var r=this.getNewAlarmCount(h);this.decreaseNewAlarm(h,r),this.increaseAcknowledgedAlarm(h,r)}else for(var $ in this._nm)this.acknowledgeAllAlarms(R.getByName($))},_c6:function(){this._aac=0;for(var F in this._am)F=R.getByName(F),this._aac+=this.getAcknowledgedAlarmCount(F)},getAcknowledgedAlarmCount:function(d){return d?this._am[d.name]||0:this._aac},getAlarmCount:function(v){return this.getAcknowledgedAlarmCount(v)+this.getNewAlarmCount(v)},_c7:function(){this._nac=0;for(var q in this._nm)this._nac+=this.getNewAlarmCount(R.getByName(q))},getNewAlarmCount:function(a){return a?this._nm[a.name]||0:this._nac},setNewAlarmCount:function(V,a){this._nm[V.name]=a,this._f()},removeAllNewAlarms:function(k){k?delete this._nm[k]:this._nm={},this._f()},setAcknowledgedAlarmCount:function(I,M){this._am[I.name]=M,this._f()},removeAllAcknowledgedAlarms:function(W){W?delete this._am[W.name]:this._am={},this._f()},isEmpty:function(){return!this._hoa},clear:function(){this._am={},this._nm={},this._f()},getPropagateSeverity:function(){return this._ps},setPropagateSeverity:function(L){if(this._ep||(L=null),this._ps!==L){var H=this._ps;this._ps=L,this._f(),this._d.fp("propagateSeverity",H,L)}},isEnablePropagation:function(){return this._ep},setEnablePropagation:function(q){var W=this._ep;this._ep=q,this._d.fp("enablePropagation",W,q)&&(q||this.setPropagateSeverity(null))}});var e=ht.AlarmStatePropagator=function(G){this._dataModel=G,this._enable=!1,this._isPropagating=!1};ht.Default.def("ht.AlarmStatePropagator",K,{getDataModel:function(){return this._dataModel},isEnable:function(){return this._enable},setEnable:function(m){this._enable!==m&&(this._enable=m,this._enable?(this._dataModel.mm(this.handleDataModelChange,this),this._dataModel.md(this.handleDataPropertyChange,this),this._dataModel.each(function(y){this.propagate(y)},this)):(this._dataModel.umm(this.handleDataModelChange,this),this._dataModel.umd(this.handleDataPropertyChange,this)))},handleDataModelChange:function(M){M.data&&this.propagate(M.data)},handleDataPropertyChange:function(L){if("alarmState"===L.property||"enablePropagation"===L.property)this.propagate(L.data);else if("parent"===L.property){var N=L.oldValue;N&&this.propagate(N),this.propagate(L.data)}},propagate:function(p){p&&!this._isPropagating&&(this._isPropagating=!0,this.propagateToTop(p),this._isPropagating=!1)},propagateToTop:function(V){for(this.propagateToParent(null,V);V&&V.getParent();)this.propagateToParent(V,V.getParent()),V=V.getParent()},propagateToParent:function(X,O){var i=null;O.getChildren().each(function(K){var C=K.getAlarmState().getHighestOverallAlarmSeverity();R.compare(C,i)>0&&(i=C)}),O.getAlarmState().setPropagateSeverity(i)}}),ht.AlarmStateStatistics=function(C){this.sumNew=0,this.sumAcked=0,this.sumTotal=0,this.severtiyMap={},this.dataMap={},this.setDataModel(C)},ht.Default.def("ht.AlarmStateStatistics",K,{ms_fire:1,getDataModel:function(){return this._dataModel},setDataModel:function(N){var g=this._dataModel;g!==N&&(g&&(g.umd(this.handleDataPropertyChange,this),g.umm(this.handleDataModelChange,this),this.severtiyMap={},this.dataMap={}),this._dataModel=N,this.reset(),N.md(this.handleDataPropertyChange,this),N.mm(this.handleDataModelChange,this),this.fp("dataModel",g,N))},dispose:function(){this._dataModel.umd(this.handleDataPropertyChange,this),this._dataModel.umm(this.handleDataModelChange,this),delete this._dataModel},handleDataPropertyChange:function(Y){"alarmState"===Y.property&&(this.increase(Y.data),this.fireAlarmStateChange())},handleDataModelChange:function(w){"add"===w.kind?(this.increase(w.data),this.fireAlarmStateChange()):"remove"===w.kind?(this.decrease(w.data),this.fireAlarmStateChange()):"clear"===w.kind&&(this.severtiyMap={},this.dataMap={},this.fireAlarmStateChange())},fireAlarmStateChange:function(){this.sumAcked=0,this.sumNew=0,this.sumTotal=0,R.each(function(o){var x=this.getSumInfo(o);this.sumAcked+=x.ackedCount,this.sumNew+=x.newCount,this.sumTotal+=x.totalCount},this),this.fp("alarmState",!1,!0)},getNewAlarmCount:function(F){if(!F)return this.sumNew;var Q=this.getSumInfo(F);return Q.newCount},getAcknowledgedAlarmCount:function(m){if(!m)return this.sumAcked;var k=this.getSumInfo(m);return k.ackedCount},getTotalAlarmCount:function(n){if(!n)return this.sumTotal;var g=this.getSumInfo(n);return g.totalCount},getSumInfo:function(I){var r=this.severtiyMap[I.name];return r||(r={},r.newCount=0,r.ackedCount=0,r.totalCount=0,this.severtiyMap[I.name]=r),r},decrease:function(c){var k=this.dataMap[c.getId()];k&&(delete this.dataMap[c.getId()],R.each(function(h){var l=k[h.name],r=this.getSumInfo(h);r.newCount=r.newCount-l.newCount,r.ackedCount=r.ackedCount-l.ackedCount,r.totalCount=r.totalCount-l.totalCount},this))},increase:function(J){if(this.decrease(J),!this._filterFunc||this._filterFunc(J)){var u={},Z=J.getAlarmState();this.dataMap[J.getId()]=u,R.each(function(c){var Q={};Q.newCount=Z.getNewAlarmCount(c),Q.ackedCount=Z.getAcknowledgedAlarmCount(c),Q.totalCount=Z.getAlarmCount(c),u[c.name]=Q;var z=this.getSumInfo(c);z.newCount=z.newCount+Q.newCount,z.ackedCount=z.ackedCount+Q.ackedCount,z.totalCount=z.totalCount+Q.totalCount},this)}},reset:function(){this.severtiyMap={},this.dataMap={},this._dataModel.each(this.increase,this),this.fireAlarmStateChange()},setFilterFunc:function(M){var c=this._filterFunc;this._filterFunc=M,this.reset(),this.fp("filterFunc",c,M)},getFilterFunc:function(){return this._filterFunc}});var T=ht.Data.prototype;T.getAlarmState=function(){return this._alarmState||(this._alarmState=new a(this))},T=ht.DataModel.prototype,T.isEnableAlarmStatePropagator=function(){return!!this._alarmStatePropagator&&this._alarmStatePropagator.isEnable()},T.setEnableAlarmStatePropagator=function(D){D!=this.isEnableAlarmStatePropagator()&&(D?(this._alarmStatePropagator=new e(this)).setEnable(!0):this._alarmStatePropagator.setEnable(!1))},T=ht.graph.GraphView.prototype,T.getNote2=function(L){var I=L.getAlarmState().getHighestNewAlarmSeverity();if(I){var R=L.getAlarmState().getNewAlarmCount(I)+I.nickName;return L.getAlarmState().hasLessSevereNewAlarms()&&(R+="+"),R}return L.s("note2")},T.getNote2Background=function(t){var v=t.getAlarmState().getHighestNewAlarmSeverity();return v?v.color:t.s("note2.background")},T.getBodyColor=function(F){var a=F.getAlarmState().getHighestNativeAlarmSeverity();return a?a.color:F.s("body.color")},T.getBorderColor=function(J){var r=J.getAlarmState().getPropagateSeverity();return r?r.color:J.s("border.color")},T=ht.widget.TreeView.prototype,T.getBorderColor=function(D){var W=D.getAlarmState().getPropagateSeverity();return W?W.color:D.s("border.color")},T.getIcon=function(_){return _.getIcon()?"__alarmIcon__":null},T=ht.widget.TreeTableView.prototype,T.getBorderColor=function(w){var O=w.getAlarmState().getPropagateSeverity();return O?O.color:w.s("border.color")},T.getIcon=function(r){return r.getIcon()?"__alarmIcon__":null},ht.Default.setImage("__alarmIcon__",{width:16,height:16,comps:[{type:"image",name:{func:function(p){return p.getIcon()}},color:{func:function(i){var J=i.getAlarmState().getHighestNativeAlarmSeverity();return J?J.color:i.s("body.color")}},rect:[0,0,16,16]}]});var z=ht.Style;z["note2.expanded"]=!0,z["note2.color"]="#000"}("undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:(0,eval)("this"),Object);