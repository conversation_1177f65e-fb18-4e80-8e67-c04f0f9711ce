!function(v,r){"use strict";var n="ht",C=n+".layout.",S=v[n]||module.parent.exports.ht,N=S.List,h=S.DataModel,D=S.Node,z=S.Edge,i=S.Group,f=Math,j=f.sqrt,U=f.random,I=f.max,Q=f.min,H=function(C){return C*C};S.Default.getInternal().addMSMap({ms_force:function(c){c._interval=50,c._stepCount=10,c._motionLimit=.01,c._edgeRepulsion=.65,c._nodeRepulsion=.65,c._damper=1,c._maxMotion=0,c._motionRatio=0,c.init=function(d){var $=this;d instanceof h?$.dm=d:$.gv=d,$._nodeMap={},$._nodes=new N,$._edges=new N},c.start=function(){var p=this,l=p.gv;if(!p._timer){var T=p.cdm=l?l.dm():p.dm;T.mm(p.handleDataModelChange,p),T.md(p.handleDataPropertyChange,p),l&&l.mp(p.handleGV,p),T.each(function(i){if(p.isVisible(i)&&p.isLayoutable(i)&&i instanceof D)if(p instanceof K){var t=i.p3();i.p3([t[0]+U(),t[1]+U(),t[2]+U()])}else t=i.p(),i.p(t.x+U(),t.y+U())}),p._timer=setInterval(function(){p.relax()},p._interval),p._damper=1}},c.stop=function(){var x=this;x._timer&&(x.cdm.umm(x.handleDataModelChange,x),x.cdm.umd(x.handleDataPropertyChange,x),x.gv&&x.gv.ump(x.handleGV,x),clearInterval(x._timer),delete x._timer,delete x.cdm)},c.handleGV=function(e){var r=this;if("dataModel"===e.property){var M=e.oldValue,o=e.newValue;M&&(M.umm(r.handleDataModelChange,r),M.umd(r.handleDataPropertyChange,r)),this.cdm=o,o.mm(r.handleDataModelChange,r),o.md(r.handleDataPropertyChange,r)}},c.relax=function(){var W=this;if(!(W._damper<.1&&W._maxMotion<W._motionLimit)){this.cdm.each(function(j){W.isVisible(j)&&(j instanceof z?W.addEdge(j):j instanceof D&&W.addNode(j))});for(var c,X,k=0,i=W._edges,b=W._nodes,A=b.size();k<W._stepCount;k++){for(i.each(W.relaxEdge,W),c=0;A>c;c++)for(X=0;A>X;X++)W.relaxNode(b.get(c),b.get(X));W.moveNode()}W._isAdjusting=1,b.each(function(Z){Z.fix||(Z.p?Z.v.p3(Z.p):Z.v.p(Z.x,Z.y))}),delete W._isAdjusting,W._nodeMap={},b.clear(),i.clear(),W.onRelaxed()}},c.onRelaxed=function(){},c.isRunning=function(){return!!this._timer},c.isVisible=function(R){return R.s("layoutable")===!1?!1:this.gv?this.gv.isVisible(R):!0},c.isLayoutable=function(b){if(b.s("layoutable")===!1)return!1;if(b instanceof i)return!1;var O=this;return O.gv?O.gv.isMovable(b)&&!O.gv.isSelected(b):!(O.cdm||O.dm).sm().co(b)},c.getNodeRepulsion=function(){return this._nodeRepulsion},c.setNodeRepulsion=function(V){this._nodeRepulsion=V,this._damper=1},c.getEdgeRepulsion=function(){return this._edgeRepulsion},c.setEdgeRepulsion=function(g){this._edgeRepulsion=g,this._damper=1},c.getStepCount=function(){return this._stepCount},c.setStepCount=function(q){this._stepCount=q,this._damper=1},c.getInterval=function(){return this._interval},c.setInterval=function(w){var r=this;r._interval!==w&&(r._interval=w,r._timer&&(clearInterval(r._timer),r._timer=setInterval(function(){r.relax()},w)))},c.handleDataPropertyChange=function(k){!this._isAdjusting&&this.isVisible(k.data)&&(this._damper=1)},c.handleDataModelChange=function(){this._damper=1},c.damp=function(){var G=this._maxMotion,$=this._damper;this._motionRatio<=.001&&((.2>G||G>1&&.9>$)&&$>.01?this._damper-=.01:.4>G&&$>.003?this._damper-=.003:$>1e-4&&(this._damper-=1e-4)),G<this._motionLimit&&(this._damper=0)}}}),S.layout.ForceLayout=function(n){this.init(n)},S.Default.def(C+"ForceLayout",r,{ms_force:1,getLimitBounds:function(){return this._limitBounds},setLimitBounds:function(I){this._limitBounds=I,this._damper=1},getNodeSize:function(Z){var t=this.gv;return t&&t.getDataUIBounds?t.getDataUIBounds(Z):Z.getRect()},addNode:function(r){var x=this,R=x._nodeMap[r._id];if(R)return R;var L=r.p();R={v:r,x:L.x,y:L.y,dx:0,dy:0,fix:!x.isLayoutable(r),s:x.getNodeSize(r)};var B=R.s,T=j(H(B.width)+H(B.height))*x._nodeRepulsion;return R.r=1>T?100:T,x._nodeMap[r._id]=R,x._nodes.add(R),R},addEdge:function(F){if(F._40I&&F._41I){var S=this,C=S.addNode(F._40I),Q=S.addNode(F._41I),N={s:C,t:Q};Q=Q.s,C=C.s;var R=Q.width+C.width,Y=Q.height+C.height;N.length=j(R*R+Y*Y)*S._edgeRepulsion,N.length<=0&&(N.length=100),S._edges.add(N)}},relaxEdge:function(R){var F=R.t,B=R.s,V=F.x-B.x,r=F.y-B.y,Z=j(V*V+r*r),z=100*R.length,w=.25*V/z*Z,u=.25*r/z*Z;F.dx=F.dx-w,F.dy=F.dy-u,B.dx=B.dx+w,B.dy=B.dy+u},relaxNode:function(L,z){if(L!==z){var V=0,b=0,Z=L.x-z.x,Y=L.y-z.y,s=Z*Z+Y*Y;0===s?(V=U(),b=U()):36e4>s&&(V=Z/s,b=Y/s);var E=L.r*z.r/400;V*=E,b*=E,L.dx+=V,L.dy+=b,z.dx-=V,z.dy-=b}},moveNode:function(){var u=this,U=u._limitBounds,C=u._maxMotion,n=0,i=u._damper;u._nodes.each(function(G){if(!G.fix){var O=G.dx*i,T=G.dy*i;if(G.dx=O/2,G.dy=T/2,n=I(j(O*O+T*T),n),G.x+=I(-40,Q(40,O)),G.y+=I(-40,Q(40,T)),U){G.x<U.x&&(G.x=U.x,u.adjust(1,0)),G.y<U.y&&(G.y=U.y,u.adjust(0,1));var o=G.s;G.x+o.width>U.x+U.width&&(G.x=U.x+U.width-o.width,u.adjust(-1,0)),G.y+o.height>U.y+U.height&&(G.y=U.y+U.height-o.height,u.adjust(0,-1))}}}),u._maxMotion=n,u._motionRatio=n>0?C/n-1:0,u.damp()},adjust:function(I,X){var h=this._limitBounds;this._nodes.each(function(b){I>0?(!h||b.x+b.s.width+I<h.x+h.width)&&(b.x+=I):(!h||b.x+I>h.x)&&(b.x+=I),X>0?(!h||b.y+b.s.height+X<h.y+h.height)&&(b.y+=X):(!h||b.y+X>h.y)&&(b.y+=X)})}});var K=S.layout.Force3dLayout=function(M){this.init(M)};S.Default.def(C+"Force3dLayout",r,{ms_force:1,getNodeSize3d:function(R){return R.s3()},addNode:function(o){var h=this,E=h._nodeMap[o._id];if(E)return E;E={v:o,p:o.p3(),d:[0,0,0],fix:!h.isLayoutable(o),s:h.getNodeSize3d(o)};var J=E.s,w=S.Default.getDistance(J)*h._nodeRepulsion;return E.r=1>w?100:w,h._nodeMap[o._id]=E,h._nodes.add(E),E},addEdge:function(k){if(k._40I&&k._41I){var w=this,v=w.addNode(k._40I),b=w.addNode(k._41I),n={s:v,t:b};b=b.s,v=v.s,n.length=j(H(b[0]+v[0])+H(b[1]+v[1])+H(b[2]+v[2]))*w._edgeRepulsion,n.length<=0&&(n.length=100),w._edges.add(n)}},relaxEdge:function(E){var k=E.t.p,z=E.s.p,P=E.t.d,Q=E.s.d,y=k[0]-z[0],_=k[1]-z[1],l=k[2]-z[2],q=j(y*y+_*_+l*l),A=100*E.length,N=.25*y/A*q,H=.25*_/A*q,B=.25*l/A*q;P[0]-=N,P[1]-=H,P[2]-=B,Q[0]+=N,Q[1]+=H,Q[2]+=B},relaxNode:function(d,Z){if(d!==Z){var D=d.p,S=Z.p,i=0,N=0,W=0,n=D[0]-S[0],Y=D[1]-S[1],o=D[2]-S[2],e=n*n+Y*Y+o*o;0===e?(i=U(),N=U(),W=U()):216e6>e&&(i=n/e,N=Y/e,W=o/e);var p=d.r*Z.r/400,f=d.d,j=Z.d;i*=p,N*=p,W*=p,f[0]+=i,f[1]+=N,f[2]+=W,j[0]-=i,j[1]-=N,j[2]-=W}},moveNode:function(){var P=this,c=P._maxMotion,h=0,t=P._damper;P._nodes.each(function(e){if(!e.fix){var V=e.p,C=e.d,O=C[0]*t,r=C[1]*t,D=C[2]*t;C[0]=O/2,C[1]=r/2,C[2]=D/2,h=I(j(O*O+r*r+D*D),h),V[0]+=I(-40,Q(40,O)),V[1]+=I(-40,Q(40,r)),V[2]+=I(-40,Q(40,D))}}),P._maxMotion=h,P._motionRatio=h>0?c/h-1:0,P.damp()}})}("undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:(0,eval)("this"),Object);