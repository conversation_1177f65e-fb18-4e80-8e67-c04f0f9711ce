!function(X,e,x){"use strict";var l=X.ht;l.Astar={},function(e){var r=e();X.AStar=r.astar,X.AStar.Graph=r.Graph}(function(){function i(_){for(var T=_,d=[];T.parent;)d.unshift(T),T=T.parent;return d}function z(){return new Z(function(F){return F.f})}function L(L,_){_=_||{},this.nodes=[],this.diagonal=!!_.diagonal,this.grid=[];for(var A=0;A<L.length;A++){this.grid[A]=[];for(var W=0,e=L[A];W<e.length;W++){var C=new d(A,W,e[W]);this.grid[A][W]=C,this.nodes.push(C)}}this.$13x()}function d(T,S,j){this.x=T,this.y=S,this.weight=j}function Z(R){this.content=[],this.scoreFunction=R}var U={search:function(E,p,d,u){E.$6x(),u=u||{};var a=u.heuristic;a||(a=E.diagonal?U.$4x.diagonal:U.$4x.manhattan);var k=u.closest||!1,J=u.punish,o=z(),c=p;for(p.h=a(p,d),E.$7x(p),o.push(p);o.size()>0;){var Z=o.pop();if(Z===d)return i(Z);Z.closed=!0;for(var r=E.neighbors(Z),N=0,B=r.length;B>N;++N){var K=r[N];if(!K.closed&&!K.$9x()){var L=Z.g+K.getCost(Z,J),T=K.visited;(!T||L<K.g)&&(K.visited=!0,K.parent=Z,K.h=K.h||a(K,d),K.g=L,K.f=K.g+K.h,E.$7x(K),k&&(K.h<c.h||K.h===c.h&&K.g<c.g)&&(c=K),T?o.$10x(K):o.push(K))}}}return k?i(c):[]},$4x:{manhattan:function(J,d){var L=Math.abs(d.x-J.x),W=Math.abs(d.y-J.y);return L+W},diagonal:function(j,L){var w=1,R=Math.sqrt(2),o=Math.abs(L.x-j.x),B=Math.abs(L.y-j.y);return w*(o+B)+(R-2*w)*Math.min(o,B)}},$5x:function(M){M.f=0,M.g=0,M.h=0,M.visited=!1,M.closed=!1,M.parent=null}};return L.prototype.$13x=function(){this.dirtyNodes=[];for(var Y=0;Y<this.nodes.length;Y++)U.$5x(this.nodes[Y])},L.prototype.$6x=function(){for(var O=0;O<this.dirtyNodes.length;O++)U.$5x(this.dirtyNodes[O]);this.dirtyNodes=[]},L.prototype.$7x=function(o){this.dirtyNodes.push(o)},L.prototype.neighbors=function(F){var j=[],T=F.x,Z=F.y,W=this.grid;return W[T-1]&&W[T-1][Z]&&j.push(W[T-1][Z]),W[T+1]&&W[T+1][Z]&&j.push(W[T+1][Z]),W[T]&&W[T][Z-1]&&j.push(W[T][Z-1]),W[T]&&W[T][Z+1]&&j.push(W[T][Z+1]),this.diagonal&&(W[T-1]&&W[T-1][Z-1]&&j.push(W[T-1][Z-1]),W[T+1]&&W[T+1][Z-1]&&j.push(W[T+1][Z-1]),W[T-1]&&W[T-1][Z+1]&&j.push(W[T-1][Z+1]),W[T+1]&&W[T+1][Z+1]&&j.push(W[T+1][Z+1])),j},L.prototype.toString=function(){for(var V=[],o=this.grid,j=0;j<o.length;j++){for(var U=[],h=o[j],B=0;B<h.length;B++)U.push(h[B].weight);V.push(U.join(" "))}return V.join("\n")},d.prototype.toString=function(){return"["+this.x+" "+this.y+"]"},d.prototype.getCost=function(A,B){var o;if(o=A&&A.x!=this.x&&A.y!=this.y?1.41421*this.weight:this.weight,B){var b=A;if(!b)return o;var e=b.parent;if(!e)return o;if(Math.abs(e.x+this.x-2*b.x)>1e-5)return o+B;if(Math.abs(e.y+this.y-2*b.y)>1e-5)return o+B}return o},d.prototype.$9x=function(){return 0===this.weight},Z.prototype={push:function(J){this.content.push(J),this.$11x(this.content.length-1)},pop:function(){var p=this.content[0],n=this.content.pop();return this.content.length>0&&(this.content[0]=n,this.$12x(0)),p},remove:function(K){var I=this.content.indexOf(K),j=this.content.pop();I!==this.content.length-1&&(this.content[I]=j,this.scoreFunction(j)<this.scoreFunction(K)?this.$11x(I):this.$12x(I))},size:function(){return this.content.length},$10x:function(l){this.$11x(this.content.indexOf(l))},$11x:function(C){for(var k=this.content[C];C>0;){var s=(C+1>>1)-1,y=this.content[s];if(!(this.scoreFunction(k)<this.scoreFunction(y)))break;this.content[s]=k,this.content[C]=y,C=s}},$12x:function(d){for(var P=this.content.length,s=this.content[d],a=this.scoreFunction(s);;){var m,Z=d+1<<1,i=Z-1,p=null;if(P>i){var X=this.content[i];m=this.scoreFunction(X),a>m&&(p=i)}if(P>Z){var C=this.content[Z],W=this.scoreFunction(C);(null===p?a:m)>W&&(p=Z)}if(null===p)break;this.content[d]=this.content[p],this.content[p]=s,d=p}}},{astar:U,Graph:L}});var I=l.Astar.Finder=function(X,l){var F=this;F.gv=X,F.$13x(l),F.refreshMap()};I.prototype={},I.prototype.constructor=I,e.defineProperties(I.prototype,{_debugInfo:{get:function(){return this.gv._astarDebugInfo},set:function(A){this.gv._astarDebugInfo=A}}}),I.prototype.$13x=function(Z){var k=this,j=k.$14x={};Z=Z||{};var i=Z.gridSize||10;j.gridSizeX=Z.gridSizeX||i,j.gridSizeY=Z.gridSizeY||i,j.rect=Z.rect,j.filter=Z.filter,j.extendBlocks=Z.extendBlocks||3,j.fastOverlap=Z.fastOverlap===x?!0:Z.fastOverlap,j.nodeRectExtend=Z.nodeRectExtend||0,j.diagonal=Z.diagonal===x?!0:Z.diagonal,j.turnPunish=Z.turnPunish===x?.1:Z.turnPunish,j.simplify=Z.simplify===x?!0:Z.simplify,j.toGridCenter=Z.toGridCenter===x?!1:Z.toGridCenter,j.closest=Z.closest===x?!0:Z.closest},I.prototype.refreshMap=function(){var k,f=this,y=f.$14x,R=y.gridSizeX,t=y.gridSizeY,D=y.extendBlocks;y.rect?k=y.rect:(k=f.$15x(),l.Default.grow(k,R*D,t*D)),l.Default.grow(k,R,t);var o=Math.floor(k.x/R)*R,E=Math.floor(k.y/t)*t,P=Math.ceil(k.width/R),U=Math.ceil(k.height/t),M=P*R,Z=U*t;f.$16x={x:o,y:E,w:M,h:Z,gridX:R,gridY:t,xLen:P,yLen:U},f.$17x(),f.$21x()},I.prototype.$15x=function(){var f=this.gv;if(f instanceof l.graph.GraphView)return f.getContentRect();var E,$=l.Default.unionRect;return f.dm().each(function(h){f.isVisible(h)&&h.getRect&&(E=$(E,h.getRect()))}),E},I.prototype.$17x=function(){var a,z,W,A,F=this,Y=F.$16x,P=Y.xLen,Q=Y.yLen;for(a=new Array(P),W=0;P>W;W++)for(a[W]=z=new Array(Q),A=Q;A--;)z[A]=1;var q=F.$14x.filter;for(F.gv.dm().each(function($){q&&q($)===!1||(!F._debugInfo||$!==F._debugInfo.grid&&$!=F._debugInfo.path)&&F.$1cw(a,$)}),W=0;P>W;W++)a[W][0]=1,a[W][Q-1]=1;for(A=0;Q>A;A++)a[0][A]=1,a[P-1][A]=1;F.grid=a;var C=new AStar.Graph(a,{diagonal:F.$14x.diagonal});F.graph=C},I.prototype.$18x=function(d,T,Q,g,w,c,F,p){var u=this.$19x,D=u(Q-d,w-F,g-T,c-p);if(1e-6>=D&&D>=-1e-6)return!1;var G=u(w-d,w-F,c-T,c-p)/D;if(0>G||G>1)return!1;var U=u(Q-d,w-d,g-T,c-T)/D;return 0>U||U>1?!1:!0},I.prototype.$19x=function(A,N,y,E){return A*E-N*y},I.prototype.$1aw=function(n,s,m,h){for(var r=[-(s[1]-n[1]),s[0]-n[0]],f=r[0]*(m[0]-n[0])+r[1]*(m[1]-n[1])>=0,a=0,X=h.length;X>a;a+=2)if(r[0]*(h[a]-n[0])+r[1]*(h[a+1]-n[1])>=0===f)return!1;return!0},I.prototype.$1bw=function(u,c){for(var h,X,e=this,i=0,y=u.length;y>i;i+=2)if(h=i===y-2?0:i+2,X=h===y-2?0:h+2,e.$1aw([u[i],u[i+1]],[u[h],u[h+1]],[u[X],u[X+1]],c))return!1;for(var i=0,y=c.length;y>i;i+=2)if(h=i===y-2?0:i+2,X=h===y-2?0:h+2,e.$1aw([c[i],c[i+1]],[c[h],c[h+1]],[c[X],c[X+1]],u))return!1;return!0},I.prototype.$1cw=function(t,O){if(O.getRect){var z=this,I=O.getRect();l.Default.grow(I,z.$14x.nodeRectExtend);var J,G,u,F,P,c,x,n,m,j,Q,Y=z.$1dw({x:I.x,y:I.y}),s=z.$1dw({x:I.x+I.width,y:I.y+I.height}),e=z.$14x.fastOverlap;if(!e){var o=O.getCorners();x=[],o.forEach(function(O){x.push(O.x,O.y)}),j=z.$14x.gridSizeX/2,Q=z.$14x.gridSizeY/2}for(G=Math.max(0,Y.x),u=Math.min(s.x,t.length-1),P=Math.max(0,Y.y),c=Math.min(s.y,t.length?t[0].length-1:-1),J=G;u>=J;J++)for(F=P;c>=F;F++)if(e)t[J][F]=0;else{m=z.$1ew({x:J,y:F});var w=m.x-j,K=m.x+j,X=m.y-Q,U=m.y+Q;n=[w,X,w,U,K,U,K,X],z.$1bw(x,n)&&(t[J][F]=0)}}},I.prototype.$1dw=function(K,n){var H=this,w=H.$16x,x=(K.x-w.x)/w.gridX,g=(K.y-w.y)/w.gridY;return n!==!1&&(x=Math.round(x),g=Math.round(g)),{x:x,y:g}},I.prototype.$1ew=function(c){var n=this,Y=n.$16x;return{x:c.x*Y.gridX+Y.x,y:c.y*Y.gridY+Y.y}},I.prototype.$1fw=function(n){var W=this,D=W.$16x,H=D.xLen,Z=D.yLen;if(n.x>=0&&n.x<H&&n.y>=0&&n.y<Z)return n;var o={x:n.x,y:n.y};return o.x<0?o.x=0:o.x>=H&&(o.x=H-1),o.y<0?o.y=0:o.y>=Z&&(o.y=Z-1),o},I.prototype.findPath=function($,S){var z,B,D,t,c=this,g=c.$1dw($),w=c.$1dw(S);if(D=c.$1fw(g),t=c.$1fw(w),D!==g&&t!==w){var b=c.$16x,X=b.x-b.gridX/2,O=b.y-b.gridY/2,e=X+b.gridX*b.xLen,i=O+b.gridY*b.yLen;if(!(c.$18x($.x,$.y,S.x,S.y,X,O,e,O)||c.$18x($.x,$.y,S.x,S.y,e,O,e,i)||c.$18x($.x,$.y,S.x,S.y,e,i,X,i)||c.$18x($.x,$.y,S.x,S.y,X,i,X,O)))return[$,S]}D!==g&&(z=!0,g=D),t!==w&&(B=!0,w=t);var x=c.graph.grid[g.x][g.y],Z=c.graph.grid[w.x][w.y],d=AStar.search(c.graph,x,Z,{closest:c.$14x.closest,punish:c.$14x.turnPunish});if(!d||!d.length)return null;var A=[];A.push($),z&&A.push(c.$1ew(d[0]));for(var P=1,C=d.length;C-1>P;P++)A.push(c.$1ew(d[P]));if(B)A.push(c.$1ew(d[C-1])),A.push(S);else{var E=d[C-1];w.x!==E.x||w.y!==E.y||c.$14x.toGridCenter?A.push(c.$1ew(E)):A.push(S)}return c.$14x.simplify&&(A=c.simplifyPath(A)),c.$20x=A,c.$21x(),A},I.prototype.debugOn=function(b){this.debugFlag=!0,this.debugSettings=b||{},this.$21x()},I.prototype.simplifyPath=function(Z){var G=Z.length;if(2>=G)return Z;var f,s,M=[Z[0],Z[1]],j=M[0],U=M[1],W=1e-5;for(f=2;G>f;f++)s=Z[f],Math.abs((U.x-j.x)*(s.y-j.y)-(U.y-j.y)*(s.x-j.x))<W?U=M[M.length-1]=s:(j=U,U=s,M.push(s));return M},I.prototype.$21x=function(){var _=this;if(_.$22x(),this.debugFlag){var B=_.grid;if(B){var d,A=_.gv;if(_.debugSettings.$16x!==!1){d=[];for(var j,i,Q,t,P,J=[],s=_.$14x.gridSizeX/2,w=_.$14x.gridSizeY/2,X=B.length,n=X?B[0].length:0,g=1;X-1>g;g++)for(var k=1;n-1>k;k++)B[g][k]||(j=_.$1ew({x:g,y:k}),i=j.x-s,Q=j.x+s,t=j.y-w,P=j.y+w,d.push({x:i,y:t},{x:i,y:P},{x:Q,y:P},{x:Q,y:t}),J.push(1,2,2,2,5));var e=new l.Shape;e.s("shape.border.color","rgba(50, 50, 50, 0.2)"),e.s("shape.border.width",1),e.s("shape.background","rgba(200, 50, 100, 0.2)"),e.s("2d.editable",!1),e.s("2d.selectable",!1),e.setPoints(d),e.setSegments(J),_._debugInfo={grid:e},A.dm().add(e)}_.debugSettings.path!==!1&&(d=new l.Shape,_.$20x&&(d.setPoints(_.$20x),d.s("shape.border.color","red"),d.s("shape.border.width",1),d.s("2d.editable",!1),d.s("2d.selectable",!1),d.s("shape.background",null),_._debugInfo||(_._debugInfo={}),_._debugInfo.path=d,A.dm().add(d)))}}},I.prototype.debugOff=function(){this.debugFlag=!1,this.$22x()},I.prototype.$22x=function(){var a=this;if(a._debugInfo){var Z=a._debugInfo.grid,Q=a._debugInfo.path,Y=a.gv;Z&&Y.dm().remove(Z),Q&&Y.dm().remove(Q),a._debugInfo=null}}}("undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:(0,eval)("this"),Object);