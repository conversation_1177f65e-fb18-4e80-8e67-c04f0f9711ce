!function(n,A,y){"use strict";var w=ht.LiveNode=function(){w.superClass.constructor.apply(this)};ht.Default.def("ht.LiveNode",ht.Node,{_width:100,_height:35,_image:null,_enabled:!0,_editable:!0,_hover:!1,_pressed:!1,isEnabled:function(){return this._enabled},setEnabled:function(j){var J=this._enabled;this._enabled=j,this.fp("enabled",J,j)},isEditable:function(){return this._enabled},setEditable:function($){var M=this._editable;this._editable=$,this.fp("editable",M,$)},isHover:function(){return this._hover},setHover:function(K){var t=this._hover;this._hover=K,this.fp("hover",t,K)},isPressed:function(){return this._pressed},setPressed:function(Y){var K=this._pressed;this._pressed=Y,this.fp("pressed",K,Y)},getBackground:function(){var s,d,w=this;return s=w._enabled?w._pressed?"live.background.active":w._hover?"live.background.hover":"live.background":"live.background.disabled",d=w.s(s),d?d:w.s("live.background")},getForeground:function(){var y,a,k=this;return y=k._enabled?k._pressed?"live.label.active":k._hover?"live.label.hover":"live.label.color":"live.label.disabled",a=k.s(y),a?a:k.s("live.label.color")},setRotation:null,getUIClass:function(){return ht.graph.LiveNodeUI}});var e=ht.graph.LiveNodeUI=function(){e.superClass.constructor.apply(this,arguments)};ht.Default.def("ht.graph.LiveNodeUI",ht.Default.getInternal().ui().NodeUI,{freeDraw:function(h,I){var y=this,p=y._data,w=ht.Default.getImage(p._image);ht.Default.drawImage(h,w,I.x,I.y,I.width,I.height,p,y.gv)},onKeyDown:function(i){var E=this,r=E._data;return 32===i.keyCode||13===i.keyCode?(r.setPressed(!0),!0):void 0},onKeyUp:function(K){var r=this._data;(32===K.keyCode||13===K.keyCode)&&(r.setPressed(!1),r.onClicked&&r.onClicked(K))},onMouseOver:function(){this._data.setHover(!0)},onMouseOut:function(){this._data.setHover(!1)},onMouseDown:function(){this._data.setPressed(!0)},onMouseMove:function(){},onMouseUp:function(M){var E=this._data;E.setPressed(!1),E.onClicked&&E.onClicked(M)}});var U=ht.ButtonNode=function(){U.superClass.constructor.apply(this)};ht.Default.def("ht.ButtonNode",ht.LiveNode,{_image:"button_image"}),ht.Default.setImage("button_image",{width:{func:"getWidth"},height:{func:"getHeight"},comps:[{type:{func:"<EMAIL>"},borderWidth:{func:"<EMAIL>"},borderColor:{func:"<EMAIL>"},gradient:{func:"<EMAIL>"},gradientColor:{func:"<EMAIL>"},background:{func:"getBackground"},rect:[0,0,1,1],relative:!0},{type:"text",text:{func:"<EMAIL>"},align:{func:"<EMAIL>"},color:{func:"getForeground"},font:{func:"<EMAIL>"},rect:[0,0,1,1],relative:!0,offsetX:{func:"<EMAIL>.x"},offsetY:{func:"<EMAIL>.y"}}]});var L=ht.ToggleButtonNode=function(){L.superClass.constructor.apply(this)};ht.Default.def("ht.ToggleButtonNode",ht.ButtonNode,{_selected:!1,getUIClass:function(){return ht.graph.ToggleButtonNodeUI},getBackground:function(){var J,x,S=this;return S._enabled?(S._hover&&(J="live.background.hover"),S.s(J)||(J=S._selected?"live.background.active":"live.background")):J="live.background.disabled",x=S.s(J),x?x:S.s("live.background")},getForeground:function(){var q,p,N=this;return N._enabled?(N._hover&&(q="live.label.hover"),N.s(q)||(q=N._selected?"live.label.active":"live.label.color")):q="live.label.disabled",p=N.s(q),p?p:N.s("live.label.color")},isSelected:function(){return this._selected},setSelected:function(W){var Y=this,g=Y._selected,u=Y._buttonGroup;Y._selected=W,Y.fp("selected",g,W)&&(u&&W&&u._selected!==Y&&(u._selected&&u._selected.setSelected(!1),u._selected=Y,u.onChanged(Y)),Y.onChanged(W))},onChanged:function(){}});var V=ht.graph.ToggleButtonNodeUI=function(){V.superClass.constructor.apply(this,arguments)};ht.Default.def("ht.graph.ToggleButtonNodeUI",ht.graph.LiveNodeUI,{rectIntersects:function(){return!0},onKeyDown:function(o){var _=this;return V.superClass.onKeyDown.call(_,o)?(_.toggleSelect(),!0):void 0},onMouseDown:function(E){var Y=this;V.superClass.onMouseDown.call(Y,E),Y.toggleSelect()},toggleSelect:function(){var n=this,o=n._data;o._buttonGroup?o._selected||o.setSelected(!0):o.setSelected(!o._selected),o.setHover(!1)}});var f=ht.CheckboxNode=function(){var w=this;f.superClass.constructor.apply(w),w.s("live.label.align","left"),w.s("live.background",ht.Color.widgetIconBackground),w.s("live.background.active",ht.Color.widgetIconHighlight)};ht.Default.def("ht.CheckboxNode",ht.ToggleButtonNode,{_image:"checkbox_image",getBackground:function(){var m,c=this;return m=c._enabled?c._selected?"live.background.active":"live.background":"live.background.disabled",c.s(m)},getForeground:function(){var U,t=this;return U=t._enabled?t._selected?"live.background.active":"live.background":"live.background.disabled",t.s(U)}}),ht.Default.setImage("checkbox_image",{width:{func:"getWidth"},height:{func:"getHeight"},comps:[{type:{func:"<EMAIL>"},background:{func:"getBackground"},rect:{func:function(F){var E=(F._width,F._height);return{x:.1*E,y:.2*E,width:.6*E,height:.6*E}}}},{type:"shape",points:{func:function(y){var r=(y._width,y._height);return[.3*r,.5*r,.4*r,.6*r,.55*r,.35*r]}},borderWidth:{func:function(p){return.05*p._height}},borderColor:"#FFFFFF",visible:{func:function(E){return E._selected||E._hover}}},{type:"text",text:{func:"<EMAIL>"},align:{func:"<EMAIL>"},color:{func:"getForeground"},font:{func:"<EMAIL>"},rect:{func:function(u){var x=u._width,j=u._height;return{x:.8*j,y:0,width:x-.8*j,height:j}}},offsetX:{func:"<EMAIL>.x"},offsetY:{func:"<EMAIL>.y"}}]});var m=ht.RadioButtonNode=function(){var O=this;m.superClass.constructor.apply(O),O.s("live.label.align","left"),O.s("live.background",ht.Color.widgetIconBackground),O.s("live.background.active",ht.Color.widgetIconHighlight)};ht.Default.def("ht.RadioButtonNode",ht.ToggleButtonNode,{_image:"radioButton_image",getUIClass:function(){return ht.graph.RadioButtonNodeUI},getBackground:function(){var V,I=this;return V=I._enabled?I._selected?"live.background.active":"live.background":"live.background.disabled",I.s(V)},getForeground:function(){var m,S=this;return m=S._enabled?S._selected?"live.background.active":"live.background":"live.background.disabled",S.s(m)}}),ht.Default.setImage("radioButton_image",{width:{func:"getWidth"},height:{func:"getHeight"},comps:[{type:"circle",borderWidth:{func:function(K){return.1*K._height}},borderColor:{func:"getBackground"},rect:{func:function(f){var R=(f._width,f._height);return{x:.1*R,y:.2*R,width:.6*R,height:.6*R}}}},{type:"circle",background:{func:"getBackground"},rect:{func:function(B){var F=(B._width,B._height);return{x:.3*F,y:.4*F,width:.2*F,height:.2*F}}},visible:{func:function(p){return p._selected||p._hover}}},{type:"text",text:{func:"<EMAIL>"},align:{func:"<EMAIL>"},color:{func:"getBackground"},font:{func:"<EMAIL>"},rect:{func:function(C){var U=C._width,z=C._height;return{x:.8*z,y:0,width:U-.8*z,height:z}}},offsetX:{func:"<EMAIL>.x"},offsetY:{func:"<EMAIL>.y"}}]});var E=ht.graph.RadioButtonNodeUI=function(){E.superClass.constructor.apply(this,arguments)};ht.Default.def("ht.graph.RadioButtonNodeUI",ht.graph.ToggleButtonNodeUI,{toggleSelect:function(){this._data._selected||this._data.setSelected(!0)}});var s=ht.SwitchNode=function(){var u=this;s.superClass.constructor.apply(u),u.s("live.background",ht.Color.widgetIconBackground),u.s("live.background.active",ht.Color.widgetIconHighlight),u.s("live.label.color",ht.Color.widgetIconBackground),u.s("live.label.active",ht.Color.widgetIconHighlight)};ht.Default.def("ht.SwitchNode",ht.ToggleButtonNode,{_image:"switch_image",getBackground:function(){return this.s(this._selected?"live.background.active":"live.background")},getForeground:function(){return this.s(this._selected?"live.label.active":"live.label.color")}}),ht.Default.setImage("switch_image",{width:{func:"getWidth"},height:{func:"getHeight"},comps:[{type:{func:"<EMAIL>"},borderWidth:{func:"<EMAIL>"},borderColor:{func:"<EMAIL>"},background:{func:"<EMAIL>"},opacity:{func:function(z){return z._enabled?1:.5}},rect:[0,0,1,1],relative:!0},{type:"circle",background:{func:"getBackground"},opacity:{func:function(Q){return Q._enabled?1:.5}},rect:{func:function(S){var j=30,q=10,R=S._selected;return{x:R?S._width-q-j:q,y:(S._height-j)/2,width:j,height:j}}}},{type:"text",text:{func:function(j){return j.s(j._selected?"switch.text.on":"switch.text.off")}},rect:[17,1,1],relative:!0,offsetX:{func:function(U){return U._selected?-10:10}},color:{func:"getForeground"},font:{func:"<EMAIL>"},align:{func:"<EMAIL>"}}]});var u=ht.ComboboxNode=function(){var c=this;u.superClass.constructor.apply(c),c.s("live.label.align","left")};ht.Default.def("ht.ComboboxNode",ht.LiveNode,{_image:"combobox_image",_buttonWidth:20,_maxHeight:200,_selectedIndex:-1,getUIClass:function(){return ht.graph.ComboboxNodeUI},getItems:function(){return this._items},setItems:function(J){var G=this._items;this._items=J,this.fp("items",G,J)},getSelectedItem:function(){return this._selectedItem},setSelectedItem:function(p){var J,P=this,z=P._items,U=P._selectedItem;!z||(J=z.indexOf(p))<0||(P._selectedItem=p,P._selectedIndex=J,P.s("live.label",p?P.getItemName(p):""),P.fp("selectedItem",U,p)&&P.onChanged(p))},getSelectedIndex:function(){return this._selectedIndex},setSelectedIndex:function(N){var x=this,B=x._items;!B||0>N||N>=B.length||(x._selectedIndex=N,x.setSelectedItem(B[N]))},getItemName:function(s){return s.label||s},onChanged:function(){}}),ht.Default.setImage("combobox_image",{width:{func:"getWidth"},height:{func:"getHeight"},comps:[{type:{func:"<EMAIL>"},borderWidth:{func:"<EMAIL>"},borderColor:{func:"<EMAIL>"},gradient:{func:"<EMAIL>"},gradientColor:{func:"<EMAIL>"},background:{func:"getBackground"},rect:[0,0,1,1],relative:!0},{type:"shape",points:{func:function(h){var g=h._buttonWidth,u=h._width,y=h._height;return[u-g+.5*g,.6*y,u-g+.75*g,.4*y,u-g+.25*g,.4*y]}},background:{func:function(e){return e._pressed?"#000000":"#FFFFFF"}}},{type:"text",text:{func:"<EMAIL>"},align:{func:"<EMAIL>"},color:{func:"getForeground"},font:{func:"<EMAIL>"},rect:[0,0,1,1],relative:!0,offsetX:{func:"<EMAIL>.x"},offsetY:{func:"<EMAIL>.y"}}]});var F=ht.graph.ComboboxNodeUI=function(){F.superClass.constructor.apply(this,arguments)};ht.Default.def("ht.graph.ComboboxNodeUI",ht.graph.LiveNodeUI,{rectIntersects:function(){return!0},onKeyDown:function(i){var e,G=this._data,R=0;return(37===i.keyCode||38===i.keyCode)&&(R=-1),(39===i.keyCode||40===i.keyCode)&&(R=1),R?(G._selectedIndex>=0?e=G._selectedIndex+R:G._items&&G._items.length>0&&(e=1===R?0:G.items.length-1),G.setSelectedIndex(e),this._ignore=!0,!0):27===i.keyCode||13===i.keyCode?(this._hidePopup(),!0):void 0},onMouseMove:function(){this._data._pressed&&(this._moved=!0)},onMouseUp:function(N){var L=this;F.superClass.onMouseUp.call(L,N),L._moved||(L._list?L._hidePopup():L._showPopup()),delete L._moved},_42:function(S){if(F.superClass._42.call(this,S),this._list&&this._data._selectedIndex>=0){var w=this._list.getDataModel().getDatas().get(this._data._selectedIndex);this._list.sm().ld()!==w&&this._list.sm().setSelection(w)}},_showPopup:function(){var g,b,c,h,E,m,L,n,B,$,e=this,p=e._data,u=p._items,R=e.gv._view.getBoundingClientRect(),s=(p._position.x-p._width/2)*e.gv._zoom+e.gv.tx()+R.left-e.gv._view.scrollLeft,Z=(p._position.y+p._height/2)*e.gv._zoom+e.gv.ty()+R.top-e.gv._view.scrollTop;if(u&&0!==u.length){for(g=new ht.DataModel,b=this._list=new ht.widget.ListView(g),b.getIcon=function(){return null},b.drawRow=function(W,n,q,C,z,B,R){b._focusData===n&&(W.fillStyle=ht.Default.darker(n.s("live.background")),W.beginPath(),W.rect(C,z,B,R),W.fill()),ht.widget.ListView.prototype.drawRow.apply(b,arguments)},c=0;c<u.length;c++)h=new ht.Data,h.setName(p.getItemName(u[c])),h._index=c,g.add(h),p._selectedIndex===c&&b.sm().setSelection(h);b.onSelectionChanged=function(){var C=b.sm().ld();C&&!e._ignore&&(p.setSelectedIndex(C._index),e._hidePopup(),delete e._ignore)},b.getSelectBackground=function(N){var e=N.s("live.background");return N===b._focusData?ht.Default.darker(e):e},ht.Default.getInternal().addEventListener(b.getView(),"mousemove",function($){b.setFocusData(b.getDataAt($)),b.invalidateModel()}),n=ht.Default.getWindowInfo(),m=p._width,L=Math.min(b.getRowHeight()*u.length,p._maxHeight,n.height),B=n.width-m-10,$=n.height-L-10,s=s>B?B:s,Z=Z>$?$:Z,s=0>s?0:s,Z=0>Z?0:Z,E=b.getView().style,E.left=s+n.left+"px",E.top=Z+n.top+"px",E.width=m+"px",E.height=L+"px",E.zIndex=1e4,E.background="white",E.borderWidth="1px",E.borderColor="#DDDDE0",E.borderStyle="solid",E.borderRadius="5px",ht.Default.appendToScreen(b.getView()),D(function(){e._hidePopup()})}},_hidePopup:function(){this._list&&(ht.Default.removeHTML(this._list.getView()),delete this._list)}});var S=ht.ProgressBarNode=function(){var E=this;S.superClass.constructor.apply(E),E.s("label.position",17),E.s("live.background",ht.Color.widgetIconBackground),E.s("live.background.active",ht.Color.widgetIconHighlight)};ht.Default.def("ht.ProgressBarNode",ht.LiveNode,{_image:"progressBar_image",_value:0,getValue:function(){return this._value},setValue:function(w){var Y=this._value;this._value=w,this.fp("value",Y,w)},getName:function(){return this._value+"%"}}),ht.Default.setImage("progressBar_image",{width:{func:"getWidth"},height:{func:"getHeight"},comps:[{type:"rect",background:{func:"<EMAIL>"},rect:[0,0,1,1],relative:!0},{type:"rect",background:{func:"<EMAIL>"},rect:{func:function(_){return[0,0,_._value/100,.5]}},relative:!0},{type:"rect",background:{func:function(D){return ht.Default.darker(D.s("live.background.active"))}},rect:{func:function(H){return[0,.5,H._value/100,.5]}},relative:!0}]});var i=ht.SliderNode=function(){var p=this;i.superClass.constructor.apply(p),p.s("label.position",17),p.s("live.background",ht.Color.widgetIconBackground),p.s("live.background.active",ht.Color.widgetIconHighlight)};ht.Default.def("ht.SliderNode",ht.LiveNode,{_image:"slider_image",_orientation:"horizontal",_value:0,_min:0,_max:100,_step:1,getUIClass:function(){return ht.graph.SliderNodeUI},getOrientation:function(){return this._orientation},setOrientation:function(l){var N=this._orientation;this._orientation=l,this.fp("orientation",N,l)},isHorizontal:function(){var i=this._orientation;return"h"===i||"horizontal"===i},getValue:function(){return this._value},setValue:function(w){var s=this,x=s._min,X=s._max,y=s._step;x>w&&(w=x),w>X&&(w=X),w=Math.floor(w/y)*y;var M=s._value;s._value=w,s.fp("value",M,w)&&s.onChanged(w)},getMin:function(){return this._min},setMin:function(p){var V=this._min;this._min=p,this.fp("min",V,p),this.setValue(this._value)},getMax:function(){return this._max},setMax:function(M){var r=this._max;this._max=M,this.fp("max",r,M),this.setValue(this._value)},getStep:function(){return this._step},setStep:function(C){var d=this._step;this._step=C,this.fp("step",d,C),this.setValue(this._value)},getName:function(){return this._value+""},onChanged:function(){}}),ht.Default.setImage("slider_image",{width:{func:"getWidth"},height:{func:"getHeight"},comps:[{type:"rect",background:{func:"<EMAIL>"},rect:{func:function(g){var y=g.s("slider.bar.size"),m=g.s("slider.thumb.size")+g.s("live.border.width"),D=g._value/(g._max-g._min),p=g.isHorizontal(),x=g._width,N=g._height;return{x:p?m:(x-y)/2,y:p?(N-y)/2:N-m-(N-2*m)*D,width:p?(x-2*m)*D:y,height:p?y:(N-2*m)*D}}}},{type:"rect",background:{func:"<EMAIL>"},rect:{func:function(Y){var x=Y.s("slider.bar.size"),G=Y.s("slider.thumb.size")+Y.s("live.border.width"),g=Y._value/(Y._max-Y._min),I=Y.isHorizontal(),u=Y._width,W=Y._height;return{x:I?G+(u-2*G)*g:(u-x)/2,y:I?(W-x)/2:G,width:I?(u-2*G)*(1-g):x,height:I?x:(W-2*G)*(1-g)}}}},{type:"circle",borderWidth:{func:"<EMAIL>"},borderColor:{func:"<EMAIL>"},background:{func:"<EMAIL>"},rect:{func:function(T){var m=T.s("slider.thumb.size"),n=T._value/(T._max-T._min),i=T.isHorizontal(),k=T._width,U=T._height;return{x:i?n*(k-2*m):k/2-m,y:i?U/2-m:(1-n)*(U-2*m),width:2*m,height:2*m}}}}]});var Z=ht.graph.SliderNodeUI=function(){Z.superClass.constructor.apply(this,arguments)};ht.Default.def("ht.graph.SliderNodeUI",ht.graph.LiveNodeUI,{rectIntersects:function(){return!0},onKeyDown:function(W){var w=this._data,o=w.isHorizontal(),R=0;return(o&&37===W.keyCode||!o&&40===W.keyCode)&&(R=-1),(o&&39===W.keyCode||!o&&38===W.keyCode)&&(R=1),R?(w.setValue(w._value+w._step*R),!0):void 0},onMouseDown:function(a){var A=this,t=A._data,B=ht.Default.getClientPoint(a);A._start=t.isHorizontal()?B.x:B.y,A._startValue=t._value},onDrag:function(x){var B=this;if(B._start===y)return!1;var t=B._data,G=t.isHorizontal(),u=t._step,A=ht.Default.getClientPoint(x),r=G?A.x:A.y,f=t.s("slider.thumb.size")+t.s("live.border.width"),O=G?t._width:t._height-2*f,L=(r-B._start)*(G?1:-1),q=(t._max-t._min)*L/O/B.gv._zoom;return t.setValue(B._startValue+Math.floor(q/u)*u),!0},onMouseUp:function(){delete this._start,delete this._startValue}});var O=ht.SpinnerNode=function(){O.superClass.constructor.apply(this),this._styleMap={},this._styleMap["label.position"]=16};ht.Default.def("ht.SpinnerNode",ht.LiveNode,{_image:"spinner_image",_value:0,_min:0,_max:100,_step:1,getUIClass:function(){return ht.graph.SpinnerNodeUI},getValue:function(){return this._value},setValue:function(v){v<this._min&&(v=this._min),v>this._max&&(v=this._max),v=Math.floor(v/this._step)*this._step;var i=this._value;this._value=v,this.fp("value",i,v)&&this.onChanged(v)},getMin:function(){return this._min},setMin:function(s){var G=this._min;this._min=s,this.fp("min",G,s),this.setValue(this._value)},getMax:function(){return this._max},setMax:function(S){var C=this._max;this._max=S,this.fp("max",C,S),this.setValue(this._value)},getStep:function(){return this._step},setStep:function(p){var P=this._step;this._step=p,this.fp("step",P,p),this.setValue(this._value)},getName:function(){return this._value+""},onChanged:function(){},getForeground:function(){return this.s(this._enabled?"live.label.active":"live.label.color")}}),ht.Default.setImage("spinner_image",{width:{func:"getWidth"},height:{func:"getHeight"},comps:[{type:{func:"<EMAIL>"},borderWidth:{func:"<EMAIL>"},borderColor:{func:"<EMAIL>"},gradient:{func:"<EMAIL>"},gradientColor:{func:"<EMAIL>"},background:{func:function(h){return h.s(h._enabled?"spinner.background":"live.background.disabled")}},rect:[0,0,1,1],relative:!0},{type:"rect",background:{func:function(e){var l;return l=e._topPressed?"live.background.active":e._topHover?"live.background.hover":"live.background",e.s(l)}},rect:{func:function(q){var x=q.s("spinner.button.width"),_=q.s("live.border.width");return{x:q._width-x,y:_,width:x-_,height:q._height/2-_}}}},{type:"shape",points:{func:function(M){var J=M.s("spinner.button.width"),t=M._width,h=M._height;return[t-J+.5*J,.15*h,t-J+.75*J,.4*h,t-J+.25*J,.4*h]}},background:{func:function(j){return j._topHover?"#000000":"#FFFFFF"}}},{type:"rect",background:{func:function(l){var u;return u=l._bottomPressed?"live.background.active":l._bottomHover?"live.background.hover":"live.background",l.s(u)}},rect:{func:function(r){var v=r.s("spinner.button.width"),a=r.s("live.border.width"),Y=r._width,N=r._height;return{x:Y-v,y:N/2,width:v-a,height:N/2-a}}}},{type:"shape",points:{func:function(F){var k=F.s("spinner.button.width"),G=F._width,Z=F._height;return[G-k+.5*k,.9*Z,G-k+.75*k,.65*Z,G-k+.25*k,.65*Z]}},background:{func:function(Q){return Q._bottomHover?"#000000":"#FFFFFF"}}}]});var z=ht.graph.SpinnerNodeUI=function(){z.superClass.constructor.apply(this,arguments)};ht.Default.def("ht.graph.SpinnerNodeUI",ht.graph.LiveNodeUI,{_refresh:function(Q){var X=this._data,N=X.s("spinner.button.width"),j=this.gv.getLogicalPoint(Q),R={x:X._position.x+X._width/2-N,y:X._position.y-X._height/2,width:N,height:X._height/2},z={x:X._position.x+X._width/2-N,y:X._position.y,width:N,height:X._height/2};X._topHover=ht.Default.containsPoint(R,j),X._bottomHover=ht.Default.containsPoint(z,j)},onKeyDown:function(q){var M=this._data,w=0;return 38===q.keyCode&&(w=1),40===q.keyCode&&(w=-1),w?(M.setValue(M._value+M._step*w),!0):void 0},onMouseOut:function(){var B=this._data;B._topHover=!1,B._bottomHover=!1,this.gv.invalidateData(B)},onMouseDown:function(q){var m=this,V=m._data,H=0;m._refresh(q),V._topPressed=V._topHover,V._bottomPressed=V._bottomHover,V._topPressed&&(H=1),V._bottomPressed&&(H=-1),H&&(V.setValue(V._value+V._step*H),m._timer&&clearTimeout(m._timer),m._interval&&clearInterval(m._interval),m._timer=setTimeout(function(){m._interval=setInterval(function(){V.setValue(V._value+V._step*H)},100)},1e3))},onMouseMove:function(H){this._refresh(H),this.gv.invalidateData(this._data)},onMouseUp:function(){var Y=this,R=Y._data;R._topHover=!1,R._bottomHover=!1,R._topPressed=!1,R._bottomPressed=!1,Y.gv.invalidateData(R),Y._timer&&(clearTimeout(Y._timer),delete Y._timer),Y._interval&&(clearInterval(Y._interval),delete Y._interval)}}),ht.ButtonGroup=function(q){var g=this;g._items=new ht.List,g.addAll(q)},ht.Default.def("ht.ButtonGroup",A,{add:function(O){var l=this;l._items.contains(O)||(l._items.add(O),O._buttonGroup=l,l._selected&&O._selected&&l._selected.setSelected(!1),O._selected&&(l._selected=O))},addAll:function(J){J&&new ht.List(J).each(self.add,self)},remove:function(e){var p=this;p._items.contains(e)&&(p._items.remove(e),delete e._buttonGroup,p._selected===e&&delete p._selected)},getItems:function(){return this._items},clear:function(){var r=this;r._items.each(r.remove,r)},getSelected:function(){return this._selected},onChanged:function(){}});var P=ht.Style,$=ht.Color,h=$.widgetBackground,J=$.widgetIconHighlight,N=ht.Default.labelSelectColor;P["live.shape"]="rect",P["live.border.width"]=1,P["live.border.color"]=$.widgetBorder,P["live.gradient"]="",P["live.gradient.color"]="#FFF",P["live.background"]=J,P["live.background.disabled"]=h,P["live.background.hover"]=ht.Default.brighter(J),P["live.background.active"]=ht.Default.darker(J),P["live.label"]="",P["live.label.offset.x"]=0,P["live.label.offset.y"]=0,P["live.label.align"]="center",P["live.label.font"]=y,P["live.label.color"]=N,P["live.label.disabled"]=$.widgetIconBackground,P["live.label.hover"]=N,P["live.label.active"]=N,P["switch.background"]=h,P["switch.text.on"]="ON",P["switch.text.off"]="OFF",P["slider.bar.size"]=6,P["slider.thumb.size"]=8,P["slider.thumb.background"]=J,P["spinner.background"]="#FFFFFF",P["spinner.button.width"]=20;var Y=ht.graph.GraphView.prototype;Y.getFocusData=function(){return this._focusData},Y.handleKeyUp=function(t){var v=this._focusData,l=v&&this._25I[v._id];v&&v._enabled&&v._editable&&l&&l.onKeyUp&&l.onKeyUp(t)},Y.handleMouseDown=function(y,r){this._focusData=r;var w=r&&this._25I[r._id];r&&r._enabled&&r._editable&&w&&w.onMouseDown&&w.onMouseDown(y)},Y.handleMouseUp=function(T,w){var n=w&&this._25I[w._id];w&&w._enabled&&w._editable&&n&&n.onMouseUp&&n.onMouseUp(T)},Y.handleMouseMove=function(b){var Z=this._lastHoverData,j=Z&&this._25I[Z._id],f=this.getDataAt(b),K=f&&this._25I[f._id];Z&&j&&Z._enabled&&Z._editable&&(Z===f?j.onMouseMove&&j.onMouseMove(b):j.onMouseOut&&j.onMouseOut(b)),f&&Z!==f&&f._enabled&&f._editable&&K&&K.onMouseOver&&K.onMouseOver(b),this._lastHoverData=f},Y._93O=function(d,m){var x=m&&this._25I[m._id];return m&&m._enabled&&m._editable&&x&&x.onDrag&&x.onDrag(d)},Y=ht.graph.DefaultInteractor.prototype,Y.handle_keyup=function(g){this.gv.handleKeyUp&&this.gv.handleKeyUp(g)},Y.handle_mousemove=function(U){this.gv.handleMouseMove&&this.gv.handleMouseMove(U)};var l=new ht.List,D=function($){setTimeout(function(){l.add($)},0)};ht.Default.getInternal().addEventListener(n,ht.Default.isTouchable?"touchend":"mouseup",function(){l.size()>0&&setTimeout(function(){l.each(function(G){G()}),l.clear()},0)})}("undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:(0,eval)("this"),Object);