!function(P,s,L){"use strict";var n="ht",v=P[n],V=v.Default,J=v.graph,f=V.getInternal(),O=f.ui(),Z=O.NodeUI.prototype,j=O.ShapeUI.prototype,o=<PERSON><PERSON>,h=o.prototype,E=O.EdgeUI.prototype,W=v.Node,K=v.Shape,F=v.Edge,e=v.Group,G=V.isTouchable,k=V.isTouch<PERSON>vent,H=V.preventDefault,p=P.setTimeout,N=V.grow,D=V.isDragging,u=V.getDistance,A=null,a=v.List,m="undefined"!=typeof Float32Array?Float32Array:Array,q=Math,Q=q.PI,b="Crossing",T="No Crossing",R=Q/2,g=2*Q,y=q.atan2,z=q.sqrt,d=q.pow,Y=q.sin,r=q.cos,X=q.min,S=q.max,B=q.abs,l=q.ceil,t=q.round,U=V.unionPoint,c=V.unionRect,x=V.containsPoint,I=V.clone,$="rgb(52, 152, 219)",w="red",C={northwest:0,north:1,northeast:2,west:7,east:3,southwest:6,south:5,southeast:4},_={width:400,height:100,comps:[{type:"shape",points:{func:function(){for(var W=[10,8,162,36,314,37,269,2,398,49,268,98,314,58,161,60,9,91,103,49,10,8],d=0;d<W.length;d++){var n=W[d];n=0===d%2?n/400*_.width:n/100*_.height,W[d]=n}return _.width>120&&(W[4]=_.width-26,W[6]=_.width-40,W[10]=_.width-40,W[12]=_.width-26,W[18]=33),W}},segments:[1,3,2,2,2,2,3,2,2],background:"white",gradient:"linear.east",gradientColor:"red"}]},M={width:300,height:300,comps:[{type:"circle",rect:[20,0,120,120],background:$,rotation:.61},{type:"circle",rect:[200,120,70,70],background:$,rotation:.61},{type:"circle",rect:[200,220,70,70],background:$,rotation:.61},{type:"shape",points:[80,60,80,155,200,155],borderWidth:20,borderColor:$},{type:"shape",points:[80,60,80,255,200,255],borderWidth:20,borderColor:$}]},i={width:300,height:300,comps:[{type:"shape",points:[114,239,115,106,112,73,149,73,149,73,175,73,177,105,179,261,178,302,147,307,98,308,83,307,72,276,73,260,72,81,74,29,115,21,191,21,221,22,224,53,223,238],segments:[1,2,3,2,3,2,3,2,4,2,3,2,3,2],borderWidth:20,borderColor:w,rotation:.61}]},lg={width:300,height:300,comps:[{type:"circle",rect:[210,0,90,90],background:$},{type:"circle",rect:[0,210,90,90],background:$},{type:"shape",points:[255,45,45,255],borderWidth:20,borderColor:$}]},wp={width:300,height:300,comps:[{type:"shape",points:[20,20,280,280],borderWidth:30,borderColor:w},{type:"shape",points:[280,20,20,280],borderWidth:30,borderColor:w}]},cc={width:300,height:300,comps:[{type:"shape",points:[270,30,30,150,270,270],borderWidth:40,borderColor:$}]},yo={width:300,height:300,comps:[{type:"shape",points:[30,30,270,150,30,270],borderWidth:40,borderColor:w}]},db={width:300,height:300,comps:[{type:"rect",rect:[125.58,52.76,40,231.96],background:"rgb(0,0,0)",borderWidth:1,borderColor:"#000"},{type:"rect",rect:[27.87,61.06,150,30],background:"rgb(0,0,0)",borderWidth:1,borderColor:"#000",rotation:5.5},{type:"rect",rect:[113.36,61,150,30],background:"rgb(0,0,0)",borderWidth:1,borderColor:"#000",rotation:.79}]},ao={width:300,height:300,author:"",note:"",comps:[{type:"rect",rect:[135.15,38.76,28.63,82.84],background:"rgb(0,0,0)",borderWidth:1,borderColor:"#000"},{type:"triangle",rect:[94.69,2.35,110,44.77],background:"rgb(0,0,0)",borderWidth:1,borderColor:"#000"},{type:"rect",rect:[31.71,115.72,240,15],background:"rgb(0,0,0)",borderWidth:1,borderColor:"#000"},{type:"rect",rect:[31.71,167.62,240,15],background:"rgb(0,0,0)",borderWidth:1,borderColor:"#000"},{type:"rect",rect:[135.15,177.33,28.63,82.84],background:"rgb(0,0,0)",borderWidth:1,borderColor:"#000"},{type:"triangle",rect:[94.69,251.39,110,44.77],background:"rgb(0,0,0)",borderWidth:1,borderColor:"#000",rotation:3.14}]},Fm=function(I){var S=I.touches[0];return S?S:I.changedTouches[0]},od=function(K,O,u,Y){return y(Y-O,u-K)},sb=function(d,X){return{x:d.x*X,y:d.y*X}},mq=function(g,F){g.each(function(_){return g.isSelected(_)?F(_):void 0})},$e=function(q,N){q.reverseEach(function(u){return q.isSelected(u)?N(u):void 0})},jh=function(J,x,m){var e,X,I,i,Z,M=this,n=x,z=M.gv;if(J instanceof K){I=J.getSegments();var S=z.getDataUI(J);Z=S._55O.$15l,I&&I.size()||(I=pd(Z)),J.isClosePath()&&I.indexOf(5)<0&&(I=new a(I._as),I.add(5)),e=pq(J,S,n),X=Z[e]}else{var l=z.getDataUI(J);Z=zo(l,J.s("edge.points")),I=J.s("edge.segments"),I&&I.size()||(I=pd(Z)),Z=Z._as,e=pq(J,l,n),X=Z[e]}if(I&&(i=I.get(n)),2===i||1===i||5===i){var O=Z[e+1];5===i&&(O=Z[0]);var H=ir(X,m,O);return H}return m},fh=function(E,z,G,l){var O=z/2,$=G/2,v=Nq(E,-O,-$,l),B=Nq(E,O,-$,l),D=Nq(E,-O,$,l),Z=Nq(E,O,$,l),g=Nq(E,0,-$,l),X=Nq(E,O,0,l),A=Nq(E,0,$,l),o=Nq(E,-O,0,l);return[v,B,D,Z,g,X,A,o]},ir=function(W,u,b){var $={x:u.x-W.x,y:u.y-W.y},y={x:b.x-W.x,y:b.y-W.y},P=z(d(b.x-W.x,2)+d(b.y-W.y,2)),r={x:y.x/P,y:y.y/P},x=$.x*r.x+$.y*r.y,a={x:r.x*x,y:r.y*x};return{x:W.x+a.x,y:W.y+a.y}},Nq=function(W,U,F,I){var P=r(I)*U-Y(I)*F,V=Y(I)*U+r(I)*F;return W?{x:W.x+P,y:W.y+V}:{x:P,y:V}},Nd=function(T,G){return G>2*T?T:G/2},Sh=function(l,K,T,V){var N=u(l,K);return T=V?X(T,N):Nd(T,N),N?T/=N:T=0,{x:l.x+(K.x-l.x)*T,y:l.y+(K.y-l.y)*T}},bl=function(X,E){var w,n,z=X.gv,C=X._data,P=C._40I,Q=C._41I,W=C.s("edge.offset"),D=C.s("edge.source.position"),t=C.s("edge.target.position"),M=C.s("edge.source.offset.x"),H=C.s("edge.source.offset.y"),d=C.s("edge.target.offset.x"),O=C.s("edge.target.offset.y"),c=C.s("edge.source.anchor.x"),N=C.s("edge.source.anchor.y"),m=C.s("edge.target.anchor.x"),e=C.s("edge.target.anchor.y");if(E?(w=f.getEdgeAgentPosition(z,P,D,M,H,c,N),n=f.getEdgeAgentPosition(z,Q,t,d,O,m,e)):(w=f.getEdgeAgentPosition(z,P,D,0,0,c,N),n=f.getEdgeAgentPosition(z,Q,t,0,0,m,e)),"points"===C.s("edge.type")&&W&&!C.s("edge.center")&&w){var G=C.s("edge.points")||new a,U=G.size(),Z=Sh(w,U?G.get(0):n,W,U),g=Sh(n,U?G.get(U-1):w,W,U);w=Z,n=g}return[w,n]},zo=function(i,I,w){var f=i._78o,V=i._data,T=V.s("edge.type"),k=[f.sourcePoint,f.targetPoint];return"points"===T?w&&(k=bl(i)):k=f.center?T?w?bl(i):bl(i,!0):w?bl(i):[f.c1,f.c2]:w?bl(i):bl(i,!0),I=new a(I?I._as:A),I.add(k[0],0),I.add(k[1]),I},Ad=function(q){return q=new a(q._as),q.removeAt(0),q.removeAt(q.size()-1),q},pd=function(Z){Z._as||(Z=new a(Z));var X=new a;X.add(1);for(var M=1;M<Z.size();M++)X.add(2);return X},rm=function(V,e){return{x:(V.x+e.x)/2,y:(V.y+e.y)/2}},cd=function(e,C){return{x:e.x+1*(C.x-e.x)/4,y:e.y+1*(C.y-e.y)/4}},Ai=function(G,w){return{x:G.x+3*(w.x-G.x)/4,y:G.y+3*(w.y-G.y)/4}},hi=function(P,A,G,X){var b=A.x+A.width/2,c=A.y+A.height/2,O=z(b*b+c*c),F=y(c,b)+G;return{x:X.x+O*r(F),y:X.y+O*Y(F)}},Dm=h.validateImpl,ed=h.isRectEditable,Pb=h.isPointEditable,tk=h._42,Tg=j._79o,Ld=E._79o,kd=j._48O,ph=j._47O,Vd=Z._48O,Zr=Z._80o,Un=j._80o,sc=Z.getSelectWidth,$l=Z._47O,ai=E._47O,Ob=h.isEditable,dp=["northwest","north","northeast","east","southeast","south","southwest","west"],Af=function(Y,z,K,u,h,k,g,o,n,y){var t=Y.x,_=Y.y,x=Y.width,j=Y.height,W={x:t+x/2,y:_+j/2};u=u||0,z=Nq(W,z.x-W.x,z.y-W.y,-u),g&&!o&&(z.x+=n,z.y+=y);var Z=.01;if("northwest"===K?(z.x+h>t+x-Z&&(z.x=t+x-h-Z),z.y+h>_+j-Z&&(z.y=_+j-h-Z),Y=U(z,{x:t+x,y:_+j})):"north"===K?(z.y+h>_+j-Z&&(z.y=_+j-h-Z),Y=U({x:t,y:z.y},{x:t+x,y:_+j})):"northeast"===K?(z.x<t+h+Z&&(z.x=t+h+Z),z.y+h>_+j-Z&&(z.y=_+j-h-Z),Y=U({x:t,y:z.y},{x:z.x,y:_+j})):"west"===K?(z.x+h>t+x-Z&&(z.x=t+x-h-Z),Y=U({x:z.x,y:_},{x:t+x,y:_+j})):"east"===K?(z.x<t+h+Z&&(z.x=t+h+Z),Y=U({x:t,y:_},{x:z.x,y:_+j})):"southwest"===K?(z.x+h>t+x-Z&&(z.x=t+x-h-Z),z.y<_+h+Z&&(z.y=_+h+Z),Y=U({x:z.x,y:_},{x:t+x,y:z.y})):"south"===K?(z.y<_+h+Z&&(z.y=_+h+Z),Y=U({x:t,y:_},{x:t+x,y:z.y})):"southeast"===K&&(z.x<t+h+Z&&(z.x=t+h+Z),z.y<_+h+Z&&(z.y=_+h+Z),Y=U({x:t,y:_},z)),"keepRatio"===k&&(!g&&o||g&&!o)&&("northeast"===K||"southeast"===K||"northwest"===K||"southwest"===K)){var R=Y.width,b=Y.height,O=R/x,d=b/j,I=X(O,d);Y.width=x*I,Y.height=j*I,"northeast"===K?Y.y=_+(j-Y.height):"northwest"===K?(Y.y=_+(j-Y.height),Y.x=t+(x-Y.width)):"southwest"===K&&(Y.x=t+(x-Y.width))}Y.x-=W.x,Y.y-=W.y;var M=hi(A,Y,u,W);return Y.x=M.x-Y.width/2,Y.y=M.y-Y.height/2,Y},vb=function(J,F,y,o){var s=J.getRotation(),X=F,_=X.x,h=X.y,O=X.width,M=X.height,L={x:_+X.width/2,y:h+X.height/2};o=Nq(L,o.x-L.x,o.y-L.y,-s),"northwest"===y?X=U(o,{x:_+O,y:h+M}):"north"===y?X=U({x:_,y:o.y},{x:_+O,y:h+M}):"northeast"===y?X=U({x:_,y:o.y},{x:o.x,y:h+M}):"west"===y?X=U({x:o.x,y:h},{x:_+O,y:h+M}):"east"===y?X=U({x:_,y:h},{x:o.x,y:h+M}):"southwest"===y?X=U({x:o.x,y:h},{x:_+O,y:o.y}):"south"===y?X=U({x:_,y:h},{x:_+O,y:o.y}):"southeast"===y&&(X=U({x:_,y:h},o)),J.setRect(X),X.x-=L.x,X.y-=L.y;var b=hi(J,X,s,L);J.p(b.x,b.y)},pl=function(b,g,i,d,r,F,Q,T){var O=A;F?(O=Nq(A,r.x-g.x,r.y-g.y,-i),O.x-=Q.x,O.y-=Q.y):O=Nq(A,r.x-g.x,r.y-g.y,0);var c={x:g.x+O.x,y:g.y+O.y};T&&(c=T(c.x,c.y)),c.e=b.getPoints().get(d).e,b.setPoint(d,c);var j=b.getWidth(),K=b.getHeight(),a=b.p().x-j/2,L=b.p().y-K/2;a-=g.x,L-=g.y;var Y=hi(b,{x:a,y:L,width:j,height:K},i,g);b.p(Y.x,Y.y)},pq=function(A,T,Y){var l,Z;if(A instanceof K?(l=A.getPoints(),Z=A.getSegments()):A instanceof F&&"points"===A.s("edge.type")&&(l=A.s("edge.points"),Z=A.s("edge.segments"),l=zo(T,l)),Z&&Z.size()||(Z=pd(l)),A instanceof K&&A.isClosePath()&&Z.indexOf(5)<0&&(Z=new a(Z._as),Z.add(5)),Z)for(var X=-1,V=0;V<Z.size();V++){if(V===Y)return X;var k=Z.get(V);1===k?X+=1:2===k?X+=1:3===k?X+=2:4===k&&(X+=3)}},Ir=function(q,V,_){var B,N;if(q instanceof K?(B=q.getPoints(),N=q.getSegments()):q instanceof F&&"points"===q.s("edge.type")&&(B=q.s("edge.points"),N=q.s("edge.segments"),B=zo(V,B)),N&&N.size()||(N=pd(B)),N)for(var a=-1,g=0;g<N.size();g++){var t=N.get(g);if(1===t?a+=1:2===t?a+=1:3===t?a+=2:4===t&&(a+=3),a===_)return g}},kk=function(H,x){var d=[H[0]-x[0],H[1]-x[1],H[2]-x[2]];return d},oj=function(){var s=new m(16);return s[0]=1,s[1]=0,s[2]=0,s[3]=0,s[4]=0,s[5]=1,s[6]=0,s[7]=0,s[8]=0,s[9]=0,s[10]=1,s[11]=0,s[12]=0,s[13]=0,s[14]=0,s[15]=1,s},pn=function(v,S){var j=v[0],U=v[1],K=v[2];return v[0]=S[0]*j+S[4]*U+S[8]*K+S[12],v[1]=S[1]*j+S[5]*U+S[9]*K+S[13],v[2]=S[2]*j+S[6]*U+S[10]*K+S[14],v},Zc=function(u,A){if(A){var w=Y(A),E=r(A),s=u[0],O=u[1],V=u[2],g=u[3],B=u[8],e=u[9],h=u[10],L=u[11];u[0]=s*E-B*w,u[1]=O*E-e*w,u[2]=V*E-h*w,u[3]=g*E-L*w,u[8]=s*w+B*E,u[9]=O*w+e*E,u[10]=V*w+h*E,u[11]=g*w+L*E}},lc=function(U,o){for(var l=U.s("grid.row.count"),E=U.s("grid.column.count"),s=0;l>s;s++)for(var f=0;E>f;f++){var T=U.getCellRect(s,f);if(x(T,o))return{rowIndex:s,columnIndex:f,rect:T,grid:U}}return null},Fb=function(Y,j,V,I,d,g){var P={},l=V-Y,y=I-j,G=q.sqrt(l*l+y*y),z=l/G,b=y/G,X=(-Y+d)*z+(-j+g)*b;return 0>=X?(P.x=Y,P.y=j):X>=G?(P.x=V,P.y=I):(P.x=Y+X*z,P.y=j+X*b),l=d-P.x,y=g-P.y,P.z=q.sqrt(l*l+y*y),P},Ko=function(Y,T){var f,l,b,S,e,I,w,t,R=Y.getPoints(),L=Y.getSegments(),v=Y.p(),k=Y.getRotation();if(!L||!(L.indexOf(3)>0||L.indexOf(4)>0)){for(t=0;t<=R.size()-1;t++)if(t===R.size()-1?(e=R.get(0),S=R.get(t)):(S=R.get(t),e=R.get(t+1)),S=Nq(v,S.x-v.x,S.y-v.y,k),e=Nq(v,e.x-v.x,e.y-v.y,k),I=Fb(S.x,S.y,e.x,e.y,T.x,T.y),w=I.z,w<(Y.s("shape.border.width")||10)){l=S,b=e,f=t;break}if(f!=A){var i=Fb(l.x,l.y,b.x,b.y,T.x,T.y),x=u(l,i);return{attachIndex:f,attachOffset:x}}}},Fo=function(j){for(var K=j._data,R=K.p(),p=K.getRotation(),w=K.getPoints(),u=A,P=[],q=0;q<w.size();q++)u=w.get(q),P.push(Nq(R,u.x-R.x,u.y-R.y,p));j._55O.$15l=P};V.setImage("xeditinteractor_parentIcon",M),V.setImage("xeditinteractor_hostIcon",i),V.setImage("xeditinteractor_edgeIcon",lg),V.setImage("xeditinteractor_removeIcon",wp),V.setImage("xeditinteractor_sourceIcon",cc),V.setImage("xeditinteractor_targetIcon",yo),V.setImage("xeditinteractor_elevationIcon",db),V.setImage("xeditinteractor_tallIcon",ao),h._editPointSize=G?24:9,h._edgeHelpers=["parent","remove","source","target"],h._leftTopHelpers=["parent"],h._rightTopHelpers=["host"],h._leftBottomHelpers=["edge"],h._rightBottomHelpers=["remove"],dp=dp.concat(dp).concat(dp),E._79o=function(){var B=this,D=B.gv,a=D.getZoom(),K=D._editInteractor,Z=Ld.call(B);if(!Z)return null;var P=bl(B,!0),x=P[0],w=P[1],r={x:Z.x,y:Z.y,width:Z.width,height:Z.height},k=U(x,w);if(K instanceof J.XEditInteractor){r=c(r,k);var Y=B._55O,A=D.getEditPointSize()/a+2,O=0;Y&&Y._43O&&(O+=A/2,N(r,O))}return r},E._47O=function(O){var Q=this,y=Q.gv,V=y._editInteractor;V instanceof J.XEditInteractor||ai.call(Q,O)},E.rectIntersects=function(r){var Z=this,W=Z._data,C=Z.gv,N=C.getZoom(),K=Z._55O,I=C._editInteractor;if(I instanceof J.XEditInteractor&&C.isEditable(W)&&K&&K._43O&&"points"===W.s("edge.type")){var o=W.s("edge.points"),l=W.s("edge.segments");o=zo(Z,o),l&&l.size()||(l=pd(o)),o=o._as;var j=!1,X=G?5:1,E=r.x+X,k=r.y+X,Q={x:E,y:k};if(I._edge===W&&I._hoverSegmentIndex>=0){var Y=C._editInteractor._hoverSegmentIndex,T=l.get(Y),U=pq(W,Z,Y),B=A;if(U!=A&&U>=0){var a=o[U],V=C.getSegmentHoverWidth()/N;if(2===T||1===T){var x=o[U+1];B=xh(new lb(a.x,a.y),new lb(x.x,x.y),new lb(Q.x-V,Q.y-V),new lb(Q.x+2*V,Q.y+2*V)),B.$62l===b&&(j=!0)}else if(3===T){var _=o[U+1],x=o[U+2];B=Gl(new lb(a.x,a.y),new lb(_.x,_.y),new lb(x.x,x.y),new lb(Q.x-V,Q.y-V),new lb(Q.x+2*V,Q.y+2*V)),B.$62l===b&&(j=!0)}else if(4===T){var S=o[U+1],m=o[U+2];x=o[U+3],B=Kn(new lb(a.x,a.y),new lb(S.x,S.y),new lb(m.x,m.y),new lb(x.x,x.y),new lb(Q.x-V,Q.y-V),new lb(Q.x+2*V,Q.y+2*V)),B.$62l===b&&(j=!0)}}}if(j)return!0}return A},j._47O=function(a){var T=this,W=T.gv,M=W._editInteractor;M instanceof J.XEditInteractor||ph.call(T,a)},j.rectIntersects=function(p){var a=this,C=a._data,Q=a.gv,U=Q.getZoom(),N=a._55O,L=Q._editInteractor;if(L instanceof J.XEditInteractor&&Q.isEditable(C)&&N&&N._43O){var f=N.$15l,S=!1,F=G?5:1,k=p.x+F,t=p.y+F,j={x:k,y:t};if(f&&f.length===C.getPoints().size()||(Fo(a),f=N.$15l),L._shape===C&&L._hoverSegmentIndex>=0){var I=C.getSegments();I&&I.size()||(I=pd(f));var $=Q._editInteractor._hoverSegmentIndex,W=I.get($),u=pq(C,a,$),r=A;if(u!=A&&u>=0){var E=f[u],v=Q.getSegmentHoverWidth()/U;if(2===W||1===W){var P=f[u+1];r=xh(new lb(E.x,E.y),new lb(P.x,P.y),new lb(j.x-v,j.y-v),new lb(j.x+2*v,j.y+2*v)),r.$62l===b&&(S=!0)}else if(3===W){var w=f[u+1],P=f[u+2];r=Gl(new lb(E.x,E.y),new lb(w.x,w.y),new lb(P.x,P.y),new lb(j.x-v,j.y-v),new lb(j.x+2*v,j.y+2*v)),r.$62l===b&&(S=!0)}else if(4===W){var q=f[u+1],h=f[u+2];P=f[u+3],r=Kn(new lb(E.x,E.y),new lb(q.x,q.y),new lb(h.x,h.y),new lb(P.x,P.y),new lb(j.x-v,j.y-v),new lb(j.x+2*v,j.y+2*v)),r.$62l===b&&(S=!0)}}}if(S)return!0}return A},j._48O=function(){var T=this,o=T.gv,y=o._editInteractor;y instanceof J.XEditInteractor?(Z._48O.call(T),T._55O._43O&&Fo(T)):kd.call(T)},j._79o=function(){var R=this,t=R.gv,G=t.getZoom(),K=t._editInteractor,S=Tg.call(R);if(S){var u={x:S.x,y:S.y,width:S.width,height:S.height};if(K instanceof J.XEditInteractor){var r=R._55O,e=t.getEditPointSize()/G+2,Z=0;r&&r._43O&&(Z+=e/2,N(u,Z))}return u}},Z._47O=function(e){var g=this,c=g.gv,O=c._editInteractor;if(O instanceof J.XEditInteractor);else{var f=g._55O;f&&f._98o&&$l.call(g,e)}},Z.getSelectWidth=function(){var j=this,Z=j.gv,Y=Z._editInteractor,z=j._data;return Z.isEditable(z)&&Y instanceof J.XEditInteractor?0:sc.call(j)},Z._80o=function(D){var w=this,x=w.gv._editInteractor;if(Zr.call(w,D),x&&x._debug){D.save(),D.beginPath(),D.lineWidth=1,D.strokeStyle="green";var S=w._79o();S&&(D.rect(S.x,S.y,S.width,S.height),D.stroke()),D.restore()}},j._80o=function(u){var w=this,l=w.gv._editInteractor;if(Un.call(w,u),l&&l._debug){u.save(),u.beginPath(),u.lineWidth=1,u.strokeStyle="green";var J=w._79o();u.rect(J.x,J.y,J.width,J.height),u.stroke(),u.restore()}},Z._48O=function(){var Z=this,g=Z.gv,q=Z._data,S=g._editInteractor,G=Z._55O,b=q.p(),T=q.getRotation();if(S instanceof J.XEditInteractor){if(G._42O){var t=fh(b,q.getWidth(),q.getHeight(),T),x=t[0],L=t[1],M=t[2],j=t[3],v=t[4],f=t[5],A=t[6],V=t[7];G.$45l=[x,v,L,f,j,A,M,V]}}else Vd.call(Z)},h.getResizeMode=function(){return this.$25l||"normal"},h.setResizeMode=function(b){var w=this.$25l;this.$25l=b,this.fp("resizeMode",w,b)},h.getSnapAngle=function(){return this.$26l||Math.PI/36},h.setSnapAngle=function(E){var d=this.$26l;this.$26l=E,this.fp("snapAngle",d,E)},h.getSnapSpacing=function(){return this.$27l},h.setSnapSpacing=function(g){var q=this.$27l;this.$27l=g,this.fp("snapSpacing",q,g)},h.getPointSnapSpacing=function(){return this._pointSnapSpacing},h.setPointSnapSpacing=function(g){var U=this._pointSnapSpacing;this._pointSnapSpacing=g,this.fp("pointSnapSpacing",U,g)},h.setHelperEditableFunc=function(b){var Q=this._helperEditableFunc;this._helperEditableFunc=b,this.fp("helperEditableFunc",Q,b)},h.getHelperEditableFunc=function(){return this._helperEditableFunc},h.setLeftTopHelpers=function(T){var G=this._leftTopHelpers;this._leftTopHelpers=T,this.fp("leftTopHelpers",G,T)},h.getLeftTopHelpers=function(){return this._leftTopHelpers},h.setLeftTopHelpersAlign=function(I){var t=this._leftTopHelpersAlign;this._leftTopHelpersAlign=I,this.fp("leftTopHelpersAlign",t,I)},h.getLeftTopHelpersAlign=function(){return this._leftTopHelpersAlign},h.setRightTopHelpers=function(f){var r=this._rightTopHelpers;this._rightTopHelpers=f,this.fp("rightTopHelpers",r,f)},h.getRightTopHelpers=function(){return this._rightTopHelpers},h.setRightTopHelpersAlign=function(e){var t=this._rightTopHelpersAlign;this._rightTopHelpersAlign=e,this.fp("rightTopHelpersAlign",t,e)},h.getRightTopHelpersAlign=function(){return this._rightTopHelpersAlign},h.setLeftBottomHelpers=function(G){var d=this._leftBottomHelpers;this._leftBottomHelpers=G,this.fp("leftBottomHelpers",d,G)},h.getLeftBottomHelpers=function(){return this._leftBottomHelpers},h.setLeftBottomHelpersAlign=function(T){var d=this._leftBottomHelpersAlign;this._leftBottomHelpersAlign=T,this.fp("leftBottomHelpersAlign",d,T)},h.getLeftBottomHelpersAlign=function(){return this._leftBottomHelpersAlign},h.setRightBottomHelpers=function(I){var C=this._rightBottomHelpers;this._rightBottomHelpers=I,this.fp("rightBottomHelpers",C,I)},h.getRightBottomHelpers=function(){return this._rightBottomHelpers},h.setRightBottomHelpersAlign=function(D){var V=this._rightBottomHelpersAlign;this._rightBottomHelpersAlign=D,this.fp("rightBottomHelpersAlign",V,D)},h.getRightBottomHelpersAlign=function(){return this._rightBottomHelpersAlign},h.setEdgeHelpers=function(l){var p=this._edgeHelpers;this._edgeHelpers=l,this.fp("edgeHelpers",p,l)},h.getEdgeHelpers=function(){return this._edgeHelpers},h.isHelperEditable=function(q,d){return q instanceof F&&("host"===d||"edge"===d||"elevation"===d||"tall"===d)?!1:q instanceof W&&("source"===d||"target"===d)?!1:"pointElevation"===d?this._helperEditableFunc?this._helperEditableFunc(q,d):!1:this._helperEditableFunc?this._helperEditableFunc(q,d):!0},h.getEditHelperSize=function(){return this._editHelperSize||(G?28:16)},h.setEditHelperSize=function(b){var r=this._editHelperSize;this._editHelperSize=b,this.fp("editHelperSize",r,b)},h.getEditRectBorderColor=function(){return this.$28l||"rgb(0, 168, 255)"},h.setEditRectBorderColor=function(c){var w=this.$28l;this.$28l=c,this.fp("editRectBorderColor",w,c)},h.getNodeBorderColor=function(){return this.$29l||"#1ABC9C"},h.setNodeBorderColor=function(P){var X=this.$29l;this.$29l=P,this.fp("nodeBorderColor",X,P)},h.getEditRectBorderStyle=function(){return this.$30l||"dashed"},h.setEditRectBorderStyle=function(y){var j=this.$30l;this.$30l=y,this.fp("editRectBorderStyle",j,y)},h.getControlPointBackground=function(){return this.$31l||"#ff0"},h.setControlPointBackground=function(E){var X=this.$31l;this.$31l=E,this.fp("controlPointBackground",X,E)},h.getControlPointBorderColor=function(){return this.$32l||this._editPointBorderColor},h.setControlPointBorderColor=function(n){var x=this.$32l;this.$32l=n,this.fp("controlPointBorderColor",x,n)},h.getJoinPointBackground=function(){return this.$33l||this._editPointBackground},h.setJoinPointBackground=function(J){var z=this.$33l;this.$33l=J,this.fp("joinPointBackground",z,J)},h.getJoinPointBorderColor=function(){return this.$34l||this._editPointBorderColor},h.setJoinPointBorderColor=function(P){var r=this.$34l;this.$34l=P,this.fp("joinPointBorderColor",r,P)},h.getDirectionLineColor=function(){return this.$35l||"#888"},h.setDirectionLineColor=function(H){var g=this.$35l;this.$35l=H,this.fp("directionLineColor",g,H)},h.getSegmentHoverColor=function(){return this.$36l||"rgba(255, 0, 0, 0.3)"},h.setSegmentHoverColor=function(g){var U=this.$36l;this.$36l=g,this.fp("segmentHoverColor",U,g)},h.getSegmentHoverWidth=function(){return this.$22l||(G?16:8)},h.setSegmentHoverWidth=function(w){var P=this.$22l;this.$22l=w,this.fp("segmentHoverWidth",P,w)},h.getSnapPointBorderColor=function(){return this.$38l||"#f00"},h.setSnapPointBorderColor=function(y){var w=this.$38l;this.$38l=y,this.fp("snapPointBorderColor",w,y)},h.getEditRectPadding=function(){return this.$39l!=A?this.$39l:6},h.setEditRectPadding=function(Y){var m=this.$39l;this.$39l=Y,this.fp("editRectPading",m,Y)},h.isRectEditable=function(b){var I=this,t=I._editInteractor;return t instanceof J.XEditInteractor?I._rectEditableFunc?I._rectEditableFunc(b):!0:ed.call(I,b)},h.isPointEditable=function(G){var e=this,v=e._editInteractor;return v instanceof J.XEditInteractor?e._pointEditableFunc?e._pointEditableFunc(G):!0:Pb.call(e,G)},h.validateImpl=function(){var R=this,j=R.$14l,H=R._editInteractor;if(j){var T=R.getWidth(),X=R.getHeight();(T!==j.clientWidth||X!==j.clientHeight)&&(H&&(H._68I=1),f.setCanvas(j,T,X),R._32I=1)}Dm.call(this)},h.isEditable=function(V){var Y=Ob.call(this,V),m=this._editInteractor;if(m instanceof J.XEditInteractor&&V instanceof F){if(!V.s("2d.editable"))return!1;var O=this.getEditableFunc();Y=O?O(V):!0}return Y},h._42=function(V,x){var M=this,A=M._editInteractor;tk.call(M,V,x),A instanceof J.XEditInteractor&&A._68I&&A._42()},h.isSnapPoint=function(){return!0};var Em=function(B){arguments.length>0&&this.$58l(B)},Wk=Em.prototype;Wk.$58l=function(z){this.$62l=z,this.points=new Array},Wk.$56l=function(n){this.points.push(n)},Wk.$57l=function($){this.points=this.points.concat($)};var lb=function(y,H){arguments.length>0&&this.$58l(y,H)},Mi=lb.prototype;Mi.$58l=function(V,W){this.x=V,this.y=W},Mi.add=function(W){return new lb(this.x+W.x,this.y+W.y)},Mi.addEquals=function(l){return this.x+=l.x,this.y+=l.y,this},Mi.$55l=function(i){return new lb(this.x*i,this.y*i)},Mi.eq=function(k){return this.x==k.x&&this.y==k.y},Mi.lt=function(M){return this.x<M.x&&this.y<M.y},Mi.lte=function(f){return this.x<=f.x&&this.y<=f.y},Mi.gt=function(A){return this.x>A.x&&this.y>A.y},Mi.gte=function(G){return this.x>=G.x&&this.y>=G.y},Mi.lerp=function(K,u){return new lb(this.x+(K.x-this.x)*u,this.y+(K.y-this.y)*u)},Mi.distanceFrom=function(p){var S=this.x-p.x,R=this.y-p.y;return z(S*S+R*R)},Mi.min=function(B){return new lb(X(this.x,B.x),X(this.y,B.y))},Mi.max=function(M){return new lb(S(this.x,M.x),S(this.y,M.y))},Mi.toString=function(){return this.x+","+this.y};var We=function(){this.$58l(arguments)},xr=We.prototype;We.TO=1e-6,We.AC=6,xr.$58l=function(P){this.co=new Array;for(var f=P.length-1;f>=0;f--)this.co.push(P[f])},xr.$55l=function(r){for(var Z=new We,s=0;s<=this.$50l()+r.$50l();s++)Z.co.push(0);for(var s=0;s<=this.$50l();s++)for(var e=0;e<=r.$50l();e++)Z.co[s+e]+=this.co[s]*r.co[e];return Z},xr.$59l=function(){for(var O=this.$50l();O>=0&&B(this.co[O])<=We.TO;O--)this.co.pop()},xr.$50l=function(){return this.co.length-1},xr.$51l=function(){var I;switch(this.$59l(),this.$50l()){case 0:I=new Array;break;case 1:I=this.$52l();break;case 2:I=this.$53l();break;case 3:I=this.$54l();break;case 4:I=this.$63l();break;default:I=new Array}return I},xr.$52l=function(){var T=new Array,x=this.co[1];return 0!=x&&T.push(-this.co[0]/x),T},xr.$53l=function(){var X=new Array;if(2==this.$50l()){var A=this.co[2],W=this.co[1]/A,x=this.co[0]/A,w=W*W-4*x;if(w>0){var Z=z(w);X.push(.5*(-W+Z)),X.push(.5*(-W-Z))}else 0==w&&X.push(.5*-W)}return X},xr.$63l=function(){var P=new Array;if(4==this.$50l()){var o=this.co[4],I=this.co[3]/o,X=this.co[2]/o,$=this.co[1]/o,A=this.co[0]/o,e=new We(1,-X,I*$-4*A,-I*I*A+4*X*A-$*$).$54l(),R=e[0],M=I*I/4-X+R;if(B(M)<=We.TO&&(M=0),M>0){var G=z(M),K=3*I*I/4-G*G-2*X,k=(4*I*X-8*$-I*I*I)/(4*G),W=K+k,c=K-k;if(B(W)<=We.TO&&(W=0),B(c)<=We.TO&&(c=0),W>=0){var Q=z(W);P.push(-I/4+(G+Q)/2),P.push(-I/4+(G-Q)/2)}if(c>=0){var Q=z(c);P.push(-I/4+(Q-G)/2),P.push(-I/4-(Q+G)/2)}}else if(0>M);else{var k=R*R-4*A;if(k>=-We.TO){if(0>k&&(k=0),k=2*z(k),K=3*I*I/4-2*X,K+k>=We.TO){var w=z(K+k);P.push(-I/4+w/2),P.push(-I/4-w/2)}if(K-k>=We.TO){var w=z(K-k);P.push(-I/4+w/2),P.push(-I/4-w/2)}}}}return P},xr.$54l=function(){var E=new Array;if(3==this.$50l()){var v=this.co[3],H=this.co[2]/v,w=this.co[1]/v,I=this.co[0]/v,O=(3*w-H*H)/3,q=(2*H*H*H-9*w*H+27*I)/27,P=H/3,t=q*q/4+O*O*O/27,h=q/2;if(B(t)<=We.TO&&(t=0),t>0){var f,l,s=z(t);f=-h+s,l=f>=0?d(f,1/3):-d(-f,1/3),f=-h-s,f>=0?l+=d(f,1/3):l-=d(-f,1/3),E.push(l-P)}else if(0>t){var X=z(-O/3),i=y(z(-t),-h)/3,D=r(i),M=Y(i),x=z(3);E.push(2*X*D-P),E.push(-X*(D+x*M)-P),E.push(-X*(D-x*M)-P)}else{var f;f=h>=0?-d(h,1/3):d(-h,1/3),E.push(2*f-P),E.push(-f-P)}}return E};var qs=function(_,L){arguments.length>0&&this.$58l(_,L)},yd=qs.prototype;yd.$58l=function(u,B){this.x=u,this.y=B},yd.length=function(){return z(this.x*this.x+this.y*this.y)},yd.$60l=function(u){return this.x*u.x+this.y*u.y};var nm=function(w,z,W,h){var x,j=(h.x-W.x)*(w.y-W.y)-(h.y-W.y)*(w.x-W.x),o=(z.x-w.x)*(w.y-W.y)-(z.y-w.y)*(w.x-W.x),L=(h.y-W.y)*(z.x-w.x)-(h.x-W.x)*(z.y-w.y);if(0!=L){var N=j/L,e=o/L;N>=0&&1>=N&&e>=0&&1>=e?(x=new Em(b),x.points.push(new lb(w.x+N*(z.x-w.x),w.y+N*(z.y-w.y)))):x=new Em(T)}else x=0==j||0==o?new Em("C"):new Em("P");return x},Ud=function(n,Z,P,h,J){var A,H,E,D,L,C,c,u=h.min(J),$=h.max(J),y=new Em(T);A=Z.$55l(-2),E=n.add(A.add(P)),A=n.$55l(-2),H=Z.$55l(2),D=A.add(H),L=new lb(n.x,n.y),c=new qs(h.y-J.y,J.x-h.x),C=h.x*J.y-J.x*h.y;for(var I=new We(c.$60l(E),c.$60l(D),c.$60l(L)+C).$51l(),g=0;g<I.length;g++){var M=I[g];if(M>=0&&1>=M){var K=n.lerp(Z,M),X=Z.lerp(P,M),z=K.lerp(X,M);h.x==J.x?u.y<=z.y&&z.y<=$.y&&(y.$62l=b,y.$56l(z)):h.y==J.y?u.x<=z.x&&z.x<=$.x&&(y.$62l=b,y.$56l(z)):z.gte(u)&&z.lte($)&&(y.$62l=b,y.$56l(z))}}return y},Gl=function(R,V,d,h,v){var J=h.min(v),M=h.max(v),u=new lb(M.x,J.y),i=new lb(J.x,M.y),n=Ud(R,V,d,J,u),P=Ud(R,V,d,u,M),A=Ud(R,V,d,M,i),p=Ud(R,V,d,i,J),D=new Em(T);return D.$57l(n.points),D.$57l(P.points),D.$57l(A.points),D.$57l(p.points),D.points.length>0&&(D.$62l=b),D},$r=function(z,m,E,w,S,r){var L,I,h,n,O,f,Z,u,N,Y,q=S.min(r),P=S.max(r),U=new Em(T);L=z.$55l(-1),I=m.$55l(3),h=E.$55l(-3),n=L.add(I.add(h.add(w))),O=new qs(n.x,n.y),L=z.$55l(3),I=m.$55l(-6),h=E.$55l(3),n=L.add(I.add(h)),f=new qs(n.x,n.y),L=z.$55l(-3),I=m.$55l(3),h=L.add(I),Z=new qs(h.x,h.y),u=new qs(z.x,z.y),Y=new qs(S.y-r.y,r.x-S.x),N=S.x*r.y-r.x*S.y;for(var W=new We(Y.$60l(O),Y.$60l(f),Y.$60l(Z),Y.$60l(u)+N).$51l(),G=0;G<W.length;G++){var c=W[G];if(c>=0&&1>=c){var $=z.lerp(m,c),H=m.lerp(E,c),v=E.lerp(w,c),x=$.lerp(H,c),J=H.lerp(v,c),l=x.lerp(J,c);S.x==r.x?q.y<=l.y&&l.y<=P.y&&(U.$62l=b,U.$56l(l)):S.y==r.y?q.x<=l.x&&l.x<=P.x&&(U.$62l=b,U.$56l(l)):l.gte(q)&&l.lte(P)&&(U.$62l=b,U.$56l(l))}}return U},Kn=function(n,d,z,Q,r,q){var x=r.min(q),W=r.max(q),c=new lb(W.x,x.y),u=new lb(x.x,W.y),w=$r(n,d,z,Q,x,c),O=$r(n,d,z,Q,c,W),Y=$r(n,d,z,Q,W,u),F=$r(n,d,z,Q,u,x),Z=new Em(T);return Z.$57l(w.points),Z.$57l(O.points),Z.$57l(Y.points),Z.$57l(F.points),Z.points.length>0&&(Z.$62l=b),Z},xh=function(o,q,v,i){var $=v.min(i),w=v.max(i),j=new lb(w.x,$.y),S=new lb($.x,w.y),e=nm($,j,o,q),s=nm(j,w,o,q),a=nm(w,S,o,q),X=nm(S,$,o,q),G=new Em(T);return G.$57l(e.points),G.$57l(s.points),G.$57l(a.points),G.$57l(X.points),G.points.length>0&&(G.$62l=b),G},Db=J.XEditInteractor=function(c){var $=this,Z=$._view=document.createElement("canvas");Z.className="editCanvas",Z.style.position="absolute",Db.superClass.constructor.call($,c)};V.def(Db,J.Interactor,{ms_edit:1,setUp:function(){var l=this,O=l.gv,y=O.getView(),P=l._view;Db.superClass.setUp.call(l),O.setEditInteractor(l),O.$14l=P,O._79O?y.insertBefore(P,O._79O):y.appendChild(P);var v=l.$49l=l.$11l.bind(l);O.dm().md(v),O.sm().ms(v),O.mp(v)},tearDown:function(){var c=this,Q=c.gv,C=Q.getView(),e=c.$49l;Db.superClass.tearDown.call(c),Q.setEditInteractor(A),Q.$14l=A,C.removeChild(c._view),Q.dm().umd(e),Q.sm().ums(e),Q.ump(e)},clear:function(){var W=this,u=W.gv;delete W._hoverSegmentIndex,delete W._edge,delete W._shape,delete u._editing,delete W.$10l,delete W.$13l,delete W.$9l,delete W._removePointIndex,delete W._lp,W.$12l="",delete W.$40l,delete W.$67l,delete W.$72l,delete W.$73l,delete W.$75l,delete W.$1l,delete W.$3l,delete W.$2l,delete W.$92l,delete W.$93l,delete W.$94l,W.clearDragging()},handle_keydown:function(N){this.$48l!==N.shiftKey&&(this.$48l=N.shiftKey,this.handle_mousemove(N,L,!0))},handle_keyup:function(s){this.$48l!==s.shiftKey&&(this.$48l=s.shiftKey,this.handle_mousemove(s,L,!0))},handle_touchstart:function(H){this.handle_mousemove(H),this.handle_mousedown(H)},handle_mousedown:function(Y){H(Y);var s=this,K=s.$13l,e=s.gv,z=e.dm(),d=e.sm(),m=e.getZoom(),k=e.lp(Y),O=V.isDoubleClick(Y),M=V.isLeftButton(Y),g=s.$7l,N=s._shape,x=s._hoverSegmentIndex,_=s._edge,r=s.$87l,h=s.$88l,t=function(){s._68I||r===s.$87l&&h===s.$88l||s.$43l()};if(7!==s.$67l&&(delete s.$87l,delete s.$88l),M&&(N||_)&&x>=0&&O&&(N&&d.contains(N)||_&&d.contains(_))){var w=jh.call(s,N||_,x,k);return s.addShapePoint(N||_,x,w),p(function(){s.handle_mousemove(Y)},40),void 0}if(M&&(N||_)&&s._removePointIndex>=0&&O)return s.removeShapePoint(),s.$5l=1,p(function(){s.handle_mousemove(Y)},40),void 0;if(!M||!e._editing)return t(),void 0;if(N&&K>=0){var G=N.getPoints().get(K),v=N.p(),j=Nq(v,G.x-v.x,G.y-v.y,-g+N.getRotation());u=Nq(v,k.x-v.x,k.y-v.y,-g),s.$24l={x:u.x-j.x,y:u.y-j.y},s.startDragging(Y),z.beginTransaction(),s.fi({kind:"beginEditPoint",event:Y,data:N,index:K}),s.$88l=K,s.$87l=N}else if(s.$10l){var F=e.sm().getSelection(),P=s.$6l,T=s.$1l={},o=s.$3l={},a=s.$16l[C[s.$10l]],U={x:P.x,y:P.y},Q={x:P.x+P.width/2,y:P.y+P.height/2};s.$24l={x:k.x-a.x/m,y:k.y-a.y/m},s.$2l=I(s.$8l),s.$4l=I(P),F.each(function(y){if(y instanceof W){var M=y.p(),u=y.getWidth(),H=y.getHeight(),q=Nq(Q,M.x-Q.x,M.y-Q.y,-g);T[y.getId()]={x:M.x-u/2,y:M.y-H/2,width:u,height:H},o[y.getId()]={x:(q.x-U.x)/P.width,y:(q.y-U.y)/P.height}}}),s.startDragging(Y),z.beginTransaction(),s.fi({kind:"beginEditRect",event:Y,direction:s.$10l})}else if(_&&K>=0){var n=zo(e.getDataUI(_),_.s("edge.points")),G=n.get(K);s.$24l={x:k.x-G.x,y:k.y-G.y},s.startDragging(Y),z.beginTransaction(),s.fi({kind:"beginEditPoint",event:Y,data:_,index:K}),s.$88l=K,s.$87l=_}else if(s.$40l){var P=s.$6l,Q={x:P.x+P.width/2,y:P.y+P.height/2},c=I(s.$23l);c.x/=m,c.y/=m;var L=Nq(Q,c.x-Q.x,c.y-Q.y,-g),u=Nq(Q,k.x-Q.x,k.y-Q.y,-g);s.$24l={x:u.x-L.x,y:u.y-L.y},s.startDragging(Y),z.beginTransaction(),s.fi({kind:"beginEditRotation",event:Y,data:s._rotationNode})}else if([1,2,3,5,6,7,8,9].indexOf(s.$67l)>=0){var B=s.$67l;(7===B||8===B||9===B)&&z.beginTransaction(),s.startDragging(Y)}else if(s.$71l&&O&&s.$79l(s.$71l,k)){var Z=d.toSelection();z.beginTransaction(),Z.each(function(Z){z.remove(Z)}),z.endTransaction()}t()},$11l:function(){var I=this,x=I.gv;x._editing&&(I.$13l==A||I._shape==A&&I._edge==A)&&(I.$73l==A||1!==I.$67l&&5!==I.$67l&&6!==I.$67l)||(I.$5l=1),I._68I=1},$46l:function(v){var B=this,$=B.gv,O=$.lp(v),z=[];return $e($,function(Y){var B=$.getDataUI(Y);if(B){var s=B._79o();x(s,O)&&z.push(Y)}}),z},$47l:function(D){var i=this,F=i.$13l,c=i.gv.dm();i.$10l?(i.fi({kind:"endEditRect",event:D,direction:i.$10l}),c.endTransaction()):i._shape&&F>=0?(i.fi({kind:"endEditPoint",event:D,data:i._shape,index:F}),c.endTransaction()):i._edge&&F>=0?(i.fi({kind:"endEditPoint",event:D,data:i._edge,index:F}),c.endTransaction()):i.$23l&&i.$40l&&(i.fi({kind:"endEditRotation",event:D}),c.endTransaction())},handleWindowTouchEnd:function(W){this.handleWindowMouseUp(W)},handleWindowMouseUp:function($){var D=this,T=D.gv,y=T.dm(),x=D._shape,l=D._edge,r=D.$13l,R=D.$40l,E=D.$9l,U=D.$10l,M=D.$67l;if((x||l)&&r>=0&&E>=0){var s,V;if(x){var q=x.p(),S=x.getRotation();s=x.getPoints(),V=s.get(E),pl(x,q,S,r,V,!1)}else if(s=zo(T.getDataUI(l),l.s("edge.points"),!0),V=s.get(E),0===r){var X=s.get(0);l.s("edge.source.offset.x",V.x-X.x),l.s("edge.source.offset.y",V.y-X.y)}else if(r===s.size()-1){var n=s.get(s.size()-1);l.s("edge.target.offset.x",V.x-n.x),l.s("edge.target.offset.y",V.y-n.y)}else"points"===l.s("edge.type")&&(s.set(r,V),l.s("edge.points",Ad(s)))}D.$47l($);var m=D.$73l;1===M?(y.beginTransaction(),m?T.sm().each(function(x){if(x.setHost&&T.isHelperEditable(x,"host")){x.setHost(m);
var j=D.$75l,Y=D.$74l;j&&(x.s("attach.row.index",j.rowIndex),x.s("attach.column.index",j.columnIndex)),Y&&(x.s("attach.index",Y.attachIndex),x.s("attach.offset",Y.attachOffset),x.s("attach.offset.relative",!1),x.s("attach.offset.opposite",!1))}}):T.sm().each(function(t){t.setHost&&T.isHelperEditable(t,"host")&&(t.setHost(A),t.s("attach.row.index",L),t.s("attach.column.index",L),t.s("attach.index",L),t.s("attach.offset",L),t.s("attach.offset.relative",!1),t.s("attach.offset.opposite",!1))}),y.endTransaction()):2===M?(y.beginTransaction(),m?T.sm().each(function(H){H.setParent&&T.isHelperEditable(H,"parent")&&H.setParent(m)}):T.sm().each(function(v){v.setParent&&T.isHelperEditable(v,"parent")&&v.setParent(A)}),y.endTransaction()):3===M?m&&(y.beginTransaction(),T.sm().each(function(x){if(T.isHelperEditable(x,"edge")&&x instanceof W&&m instanceof W){var Y=new v.Edge;y.add(Y),Y.setSource(x),Y.setTarget(m),D.onEdgeCreated&&D.onEdgeCreated($,Y)}}),y.endTransaction()):5===M||6===M?m&&(y.beginTransaction(),T.sm().each(function(B){B instanceof F&&m instanceof W&&(5===M&&T.isHelperEditable(B,"source")?B.setSource(m):6===M&&T.isHelperEditable(B,"source")&&B.setTarget(m))}),y.endTransaction()):(7===M||8===M||9===M)&&y.endTransaction(),(U||R)&&D.$43l(!0),k($)?(D.clear(),delete D.$64l,D._68I||D.$43l()):(D.handle_mousemove($,!0),M&&!D._68I&&D.$43l())},handleWindowTouchMove:function(s){this.handleWindowMouseMove(s)},handleWindowMouseMove:function(Q){this._78I(Q)},handle_mousemove:function(r,m,c){if(!this._pause){if(!c){var S=k(r)?Fm(r):r;this.$61l={clientX:S.clientX,clientY:S.clientY}}if(m===!0||!D()){var V=this,B=V.gv,I=!1,q=A;V.clear(),B.sm().size()>0&&(q=B.lp(c?V.$61l:r),I=V._79I(r,q)),I&&(B._editing=1),V.$12l!==V.$65l&&(V.$65l=V.$12l,V.setCursor(V.$12l)),V.$40l!==V.$64l&&(V.$64l=V.$40l,V._68I||V.$43l()),V._hoverSegmentIndex!==V.$66l&&(V.$66l=V._hoverSegmentIndex,V._68I||V.$43l())}}},_78I:function(r){this.autoScroll(r);var Z=this,q=Z.gv,x=q.lp(r),w=q.getZoom(),$=Z.$13l;if((Z._shape||Z._edge)&&$>=0){var L,n,_,M=Z._shape,m=Z._edge,H=Z.$24l,c=q.getPointSnapSpacing(),s=function(p,q){return c==A||isNaN(c)?{x:p,y:q}:{x:t(p/c)*c,y:t(q/c)*c}};if(M){L=M.getPoints();var i=M.p(),l=M.getRotation(),P=0;n=Nq(i,x.x-i.x,x.y-i.y,-l),pl(M,i,l,$,x,!0,H,s),Z.fi({kind:"betweenEditPoint",event:r,data:M,index:$})}else if(m){L=zo(q.getDataUI(m),m.s("edge.points"),!0),x.x-=H.x,x.y-=H.y,n=x;var k,G,X=m.s("edge.offset"),h=L.size(),v=h>2;if(0===$){var J=L.get(0);X&&(J=Sh(J,L.get(1),-X,v),x=Sh(x,L.get(1),-X,v)),k=x.x-J.x,G=x.y-J.y,_=s(k,G),m.s("edge.source.offset.x",_.x),m.s("edge.source.offset.y",_.y)}else if($===h-1){var b=L.get(h-1);X&&(b=Sh(b,L.get(h-2),-X,v),x=Sh(x,L.get(h-2),-X,v)),k=x.x-b.x,G=x.y-b.y,_=s(k,G),m.s("edge.target.offset.x",_.x),m.s("edge.target.offset.y",_.y)}else"points"===m.s("edge.type")&&(_=s(x.x,x.y),_.e=L.get($).e,L.set($,_),m.s("edge.points",Ad(L)));Z.fi({kind:"betweenEditPoint",event:r,data:m,index:$})}if(delete Z.$9l,q.isSnapPoint(M)||q.isSnapPoint(m))for(P=0;P<L.size();P++)if(P!==$){var N=L.get(P);if(u(N,n)<q.getEditPointSize()/w){Z.$9l=P;break}}}else if(Z.$10l){var H=Z.$24l;x.x-=H.x,x.y-=H.y;var I=q.sm().getSelection(),D=Z.$1l,d=Z.$7l,S=Z.$10l,O=Z.$4l,f=Z.$2l,e=q.getEditRectPadding()/w,a=2*e,C=q.getSnapSpacing(),Y=C!=A&&!isNaN(C),o=Af(f,x,S,d,2*e,q.getResizeMode(),Y,!0),E=Z.$6l;if(Y){var T=o.width-a,U=o.height-a,j=t(T/C)*C,mo=t(U/C)*C,mi=j-T,he=mo-U;("west"===S||"northwest"===S||"southwest"===S)&&(mi=-mi),("north"===S||"northwest"===S||"northeast"===S)&&(he=-he),o=Af(f,x,S,d,2*e,q.getResizeMode(),Y,!1,mi,he)}Z.$8l=o,E.x=o.x+e,E.y=o.y+e,E.width=o.width-a,E.height=o.height-a;var tl=o.x+o.width/2,Ek=o.y+o.height/2,rs=E.x,fp=E.y,Mo=E.width/O.width,cn=E.height/O.height;I.each(function(v){if(v instanceof W&&q.isEditable(v)&&q.isRectEditable(v)){var C=D[v.getId()],_=Z.$3l[v.getId()],k={x:C.x+C.width/2,y:C.y+C.height/2},X=0,b=0,L=S,$=180*B(d-v.getRotation())/Q;"northeast"===S?$>=315||45>$?(X=C.width*Mo+C.x,b=C.height*(1-cn)+C.y,L="northeast"):$>=45&&135>$?(X=C.width*(1-cn)+C.x,b=C.height*(1-Mo)+C.y,L="northwest"):$>=135&&225>$?(X=C.width*(1-Mo)+C.x,b=C.height*cn+C.y,L="southwest"):(X=C.width*cn+C.x,b=C.height*Mo+C.y,L="southeast"):"north"===S?$>=315||45>$?(b=C.height*(1-cn)+C.y,L="north"):$>=45&&135>$?(X=C.width*(1-cn)+C.x,L="west"):$>=135&&225>$?(b=C.height*cn+C.y,L="south"):(X=C.width*cn+C.x,L="east"):"northwest"===S?$>=315||45>$?(X=C.width*(1-Mo)+C.x,b=C.height*(1-cn)+C.y,L="northwest"):$>=45&&135>$?(X=C.width*(1-cn)+C.x,b=C.height*Mo+C.y,L="southwest"):$>=135&&225>$?(X=C.width*Mo+C.x,b=C.height*cn+C.y,L="southeast"):(X=C.width*cn+C.x,b=C.height*(1-Mo)+C.y,L="northeast"):"west"===S?$>=315||45>$?(X=C.width*(1-Mo)+C.x,L="west"):$>=45&&135>$?(b=C.height*Mo+C.y,L="south"):$>=135&&225>$?(X=C.width*Mo+C.x,L="east"):(b=C.height*(1-Mo)+C.y,L="north"):"southwest"===S?$>=315||45>$?(X=C.width*(1-Mo)+C.x,b=C.height*cn+C.y,L="southwest"):$>=45&&135>$?(X=C.width*cn+C.x,b=C.height*Mo+C.y,L="southeast"):$>=135&&225>$?(X=C.width*Mo+C.x,b=C.height*(1-cn)+C.y,L="northeast"):(X=C.width*(1-cn)+C.x,b=C.height*(1-Mo)+C.y,L="northwest"):"east"===S?$>=315||45>$?(X=C.width*Mo+C.x,L="east"):$>=45&&135>$?(b=C.height*(1-Mo)+C.y,L="north"):$>=135&&225>$?(X=C.width*(1-Mo)+C.x,L="west"):(b=C.height*Mo+C.y,L="south"):"south"===S?$>=315||45>$?(b=C.height*cn+C.y,L="south"):$>=45&&135>$?(X=C.width*cn+C.x,L="east"):$>=135&&225>$?(b=C.height*(1-cn)+C.y,L="north"):(X=C.width*(1-cn)+C.x,L="west"):"southeast"===S&&($>=315||45>$?(X=C.width*Mo+C.x,b=C.height*cn+C.y,L="southeast"):$>=45&&135>$?(X=C.width*cn+C.x,b=C.height*(1-Mo)+C.y,L="northeast"):$>=135&&225>$?(X=C.width*(1-Mo)+C.x,b=C.height*(1-cn)+C.y,L="northwest"):(X=C.width*(1-cn)+C.x,b=C.height*Mo+C.y,L="southwest"));var T=Nq(k,X-k.x,b-k.y,v.getRotation());vb(v,C,L,{x:T.x,y:T.y});var m=rs+E.width*_.x,H=fp+E.height*_.y,R={x:m,y:H};R=Nq({x:tl,y:Ek},R.x-tl,R.y-Ek,d),v.p(R.x,R.y)}}),Z.fi({kind:"betweenEditRect",event:r,direction:S})}else if(Z.$23l&&Z.$40l){var oo=Z.$8l,rj=oo.width,ws=oo.height,i={x:oo.x+rj/2,y:oo.y+ws/2},H=Z.$24l,ij=Z.$23l,Gj=ij.x/w-i.x,Tn=ij.y/w-i.y,Kf=z(Gj*Gj+Tn*Tn),Eq=Math.asin(H.x/Kf),Em=q.sm().getSelection(),Wf=R+y(x.y-i.y,x.x-i.x)-Eq,nr=q.getSnapAngle(),Wf=t(Wf/nr)*nr;0>Wf&&(Wf+=g);var wr=Wf-Z.$7l,Pp=[i.x,0,i.y];Em.each(function(u){if(q.isEditable(u)&&q.isRotationEditable(u)&&q.isVisible(u)){var _=kk(u.p3(),Pp),i=oj();Zc(i,-wr),u.setRotation(u.getRotation()+wr),pn(_,i),u.p3(Pp[0]+_[0],Pp[1]+_[1],Pp[2]+_[2])}}),Z.$7l=Wf,Z.fi({kind:"betweenEditRotation",event:r})}else if([1,2,3,5,6].indexOf(Z.$67l)>=0)Z.$72l=r,Z.$78l||(Z.$78l=1,p(function(){V.isDragging()&&Z.$43l(),delete Z.$78l},12));else if(7===Z.$67l){if(Z.$87l instanceof K){var M=Z.$87l,Ui=Z.$88l,cs=V.clone(M.getPoints().get(Ui)),Mf=x.y-cs.y;cs.e||(cs.e=0),Z.$92l||(Z.$92l=cs.e),cs.e=Z.$92l-Mf,M.setPoint(Ui,cs)}else if(Z.$87l instanceof F){var m=Z.$87l,Ui=Z.$88l,L=zo(q.getDataUI(m),m.s("edge.points")),cs=L.get(Ui),Mf=x.y-cs.y;cs.e||(cs.e=0),Z.$92l||(Z.$92l=cs.e),cs.e=Z.$92l-Mf,L.set(Ui,cs),m.s("edge.points",Ad(L))}}else if(8===Z.$67l){var Em=q.sm().getSelection(),gb=Z.$93l;gb||(gb=Z.$93l={}),Em.each(function(H){if(H.p3){var V=H.p3(),g=gb[H.getId()];g==A&&(g=gb[H.getId()]=V[1]),H.p3([V[0],g-(x.y-Z.$90l.y/w),V[2]])}})}else if(9===Z.$67l){var Em=q.sm().getSelection(),Hk=Z.$94l;Hk||(Hk=Z.$94l={}),Em.each(function(h){if(h.s3){var m=h.s3(),M=Hk[h.getId()];M==A&&(M=Hk[h.getId()]=m[1]);var O=M-(x.y-Z.$91l.y/w);0>O&&(O=.01),h.s3([m[0],O,m[2]])}})}},$79l:function(G,Y){var $=this,W=$.gv,C=W.getZoom(),J=W.getEditHelperSize(),b=G.x/C,r=G.y/C,y=J/C,N=y/2;return x({x:b-N,y:r-N,width:y,height:y},Y)?!0:void 0},getHelperCursor:function(){return"crosshair"},_79I:function(q,h){for(var Z=this,H=Z.gv,_=H.getZoom(),T=H.getEditPointSize(),f=T/_,N=f/2,d=Z.$46l(q),u=0;u<d.length;u++){var M,w,V=d[u],W=H.getDataUI(V);if(W&&W._55O){var I=W._55O;if(I._43O&&V instanceof F){if(M=V.s("edge.points"),M=zo(W,M),w=V.s("edge.segments"),w&&w.size()||(w=pd(M)),Z.$42l(V,h,M._as,w,"edge"))return!0;if("points"===V.s("edge.type")&&Z.$41l(V,h,M._as,w,"edge"))return}if(I._43O&&V instanceof K&&I.$15l&&I.$15l.length===V.getPoints().size()){if(M=I.$15l,w=V.getSegments(),w&&w.size()||(w=pd(M)),Z.$42l(V,h,M,w,"shape"))return!0;if(Z.$41l(V,h,M,w,"shape"))return}}}if(Z.$16l){var M,t=Z.$16l,a=t[3].x-t[7].x,X=t[3].y-t[7].y,s=z(a*a+X*X),$=t[1].x-t[5].x,O=t[1].y-t[5].y,C=z($*$+O*O),J={x:h.x*_,y:h.y*_};if(Z._80I(J,0,t[0],"northwest",s,C)||Z._80I(J,1,t[1],"north",s,C)||Z._80I(J,2,t[2],"northeast",s,C)||Z._80I(J,7,t[7],"west",s,C)||Z._80I(J,3,t[3],"east",s,C)||Z._80I(J,6,t[6],"southwest",s,C)||Z._80I(J,5,t[5],"south",s,C)||Z._80I(J,4,t[4],"southeast",s,C))return!0}if(Z.$23l){var U=Z.$23l,y=U.x/_,c=U.y/_;if(x({x:y-N,y:c-N,width:f,height:f},h))return Z.$40l=1,Z.$12l=Z.getHelperCursor("rotation")||"crosshair",!0}return Z.$68l&&Z.$79l(Z.$68l,h)?(Z.$67l=1,Z.$12l=Z.getHelperCursor("host")||"crosshair",!0):Z.$69l&&Z.$79l(Z.$69l,h)?(Z.$67l=2,Z.$12l=Z.getHelperCursor("parent")||"crosshair",!0):Z.$70l&&Z.$79l(Z.$70l,h)?(Z.$67l=3,Z.$12l=Z.getHelperCursor("edge")||"crosshair",!0):Z.$71l&&Z.$79l(Z.$71l,h)?(Z.$67l=4,Z.$12l=Z.getHelperCursor("remove")||"crosshair",!0):Z.$82l&&Z.$79l(Z.$82l,h)?(Z.$67l=5,Z.$12l=Z.getHelperCursor("source")||"crosshair",!0):Z.$83l&&Z.$79l(Z.$83l,h)?(Z.$67l=6,Z.$12l=Z.getHelperCursor("target")||"crosshair",!0):Z._pointElevationHelperPoint&&Z.$79l(Z._pointElevationHelperPoint,h)?(Z.$67l=7,Z.$12l=Z.getHelperCursor("pointElevation")||"crosshair",!0):Z.$90l&&Z.$79l(Z.$90l,h)?(Z.$67l=8,Z.$12l=Z.getHelperCursor("elevation")||"crosshair",!0):Z.$91l&&Z.$79l(Z.$91l,h)?(Z.$67l=9,Z.$12l=Z.getHelperCursor("tall")||"crosshair",!0):!1},_80I:function(S,I,O,Z,Y,A){var i=this,z=i.gv,r=z.getEditPointSize(),k=r/2,P=z.sm(),R=z.isDirectionEditable;if(R&&1===P.size()){var l=P.ld();if(R=R.bind(z),l instanceof W){if(!R(l,Z))return!1;if("east"===Z){if(R(l,"northeast")&&R(l,"southeast")&&2*r>=A)return!1}else if("west"===Z){if(R(l,"northwest")&&R(l,"southwest")&&2*r>=A)return!1}else if("north"===Z){if(R(l,"northwest")&&R(l,"northeast")&&2*r>=Y)return!1}else if("south"===Z&&R(l,"southwest")&&R(l,"southeast")&&2*r>=Y)return!1}}else{if(2*r>=Y&&("north"===Z||"south"===Z))return!1;if(2*r>=A&&("east"===Z||"west"===Z))return!1}if(x({x:O.x-k,y:O.y-k,width:r,height:r},S)){if(i.$10l!==Z){var L=180*i.$7l/Q,C="";L>=337.5||22.5>L?C="north":L>=22.5&&67.5>L?C="northeast":L>=67.5&&112.5>L?C="east":L>=112.5&&157.5>L?C="southeast":L>=157.5&&202.5>L?C="south":L>=202.5&&247.5>L?C="southwest":L>=247.5&&292.5>L?C="west":L>=292.5&&337.5>L&&(C="northwest");var a=dp.indexOf(C),C=dp[I-(1-a)+8];switch(C){case"northwest":i.$12l="nwse-resize";break;case"northeast":i.$12l="nesw-resize";break;case"southwest":i.$12l="nesw-resize";break;case"southeast":i.$12l="nwse-resize";break;case"north":i.$12l="ns-resize";break;case"east":i.$12l="ew-resize";break;case"south":i.$12l="ns-resize";break;case"west":i.$12l="ew-resize"}i.$10l=Z}return!0}return!1},$42l:function(T,c,F,m,e){var P,l=this,C=l.gv,q=C.getEditPointSize(),K=q/C.getZoom(),r=-1,Y={x:c.x,y:c.y,width:0,height:0},u=function(S){"shape"===e?l._shape=T:l._edge=T,l._removePointIndex=S},U=function(N){"shape"===e?l._shape=T:l._edge=T,l.$13l=N,l.$12l="crosshair"};for(N(Y,K/2),r=F.length-1,P=m.size()-1;P>=0;P--){var E=m.get(P);if(1===E||2===E)r-=1;else if(3===E){var H=F[r-1];if(x(Y,H))return U(r-1),!0;r-=2}else if(4===E){var B=F[r-1],R=F[r-2];if(x(Y,B))return U(r-1),!0;if(x(Y,R))return U(r-2),!0;r-=3}}for(r=F.length-1,P=m.size()-1;P>=0;P--){var E=m.get(P),W=F[r];if(x(Y,W))return u(r),U(r),!0;1===E||2===E?r-=1:3===E?r-=2:4===E&&(r-=3)}return!1},$41l:function(f,I,C,W,u){var g=this,l=g.gv,Z=-1,S=0,y={x:I.x,y:I.y,width:0,height:0},v=function(h,K){g._hoverSegmentIndex=K,g._lp=I,"shape"===u?g._shape=f:g._edge=f};for(N(y,l.getSegmentHoverWidth()/2/l.getZoom()),f instanceof K&&f.isClosePath()&&W.indexOf(5)<0&&(W=new a(W._as),W.add(5));S<W.size();S++){var O=W.get(S);if(1===O){var P=C[Z],Q=C[Z+1];if(P){var k=xh(new lb(P.x,P.y),new lb(Q.x,Q.y),new lb(y.x,y.y),new lb(y.x+y.width,y.y+y.height));if(k.$62l===b&&k.points.length>0)return v(k,S),!0}Z+=1}else if(2===O){var P=C[Z],Q=C[Z+1],k=xh(new lb(P.x,P.y),new lb(Q.x,Q.y),new lb(y.x,y.y),new lb(y.x+y.width,y.y+y.height));if(k.$62l===b&&k.points.length>0)return v(k,S),!0;Z+=1}else if(3===O){var P=C[Z],U=C[Z+1],o=C[Z+2],k=Gl(new lb(P.x,P.y),new lb(U.x,U.y),new lb(o.x,o.y),new lb(y.x,y.y),new lb(y.x+y.width,y.y+y.height));if(k.$62l===b)return v(k,S),!0;Z+=2}else if(4===O){var P=C[Z],h=C[Z+1],p=C[Z+2],o=C[Z+3],k=Kn(new lb(P.x,P.y),new lb(h.x,h.y),new lb(p.x,p.y),new lb(o.x,o.y),new lb(y.x,y.y),new lb(y.x+y.width,y.y+y.height));if(k.$62l===b)return v(k,S),!0;Z+=3}else if(5===O){var P=C[Z],Q=C[0];if(P){var k=xh(new lb(P.x,P.y),new lb(Q.x,Q.y),new lb(y.x,y.y),new lb(y.x+y.width,y.y+y.height));if(k.$62l===b&&k.points.length>0)return v(k,S),!0}}}return!1},addShapePoint:function(U,C,i){var I=this,V=I.gv,_=V.dm();if(_.beginTransaction(),0===arguments.length&&(U=I._shape||I._edge,C=I._hoverSegmentIndex,i=I._lp),U instanceof K||U instanceof F){var c=pq(U,V.getDataUI(U),C);if(!isNaN(C)&&i&&!isNaN(c)){var E=i;if(U instanceof K){var e=U.p(),k=U.getRotation(),H=U.toSegments();H&&H.size()||(H=pd(U.getPoints())),U.isClosePath()&&H.indexOf(5)<0&&(H=new a(H._as),H.add(5));var p=H.get(C),y="addPoint",r="removePointAt";if(2===p||1===p){var j={x:E.x,y:E.y};H.add(p,C),j=Nq(e,j.x-e.x,j.y-e.y,-k),U[y](j,c+1)}else if(3===p){var z=U.getPoints(),N=z.get(c),x=z.get(c+2),j={x:E.x,y:E.y};j=Nq(e,j.x-e.x,j.y-e.y,-k);var q=rm(N,j),T=rm(j,x);H.removeAt(C),H.add(3,C),H.add(3,C),U.setPoint(c+1,j),U[y](T,c+2),U[y](q,c+1)}else if(4===p){var z=U.getPoints(),N=z.get(c),x=z.get(c+3),j={x:E.x,y:E.y};j=Nq(e,j.x-e.x,j.y-e.y,-k);var q=cd(N,j),T=Ai(N,j),u=cd(j,x),o=Ai(j,x);H.removeAt(C),H.add(4,C),H.add(4,C),U[r](c+1),U[r](c+1),U[y](j,c+1),U[y](o,c+2),U[y](u,c+2),U[y](T,c+1),U[y](q,c+1)}else if(5===p){var j={x:E.x,y:E.y};H.add(2,C),j=Nq(e,j.x-e.x,j.y-e.y,-k),U[y](j,c+1)}U.isClosePath()&&H.remove(5),U.setSegments(H);var $=U.getWidth(),b=U.getHeight(),R=U.p().x-$/2,S=U.p().y-b/2;R-=e.x,S-=e.y;var t=hi(U,{x:R,y:S,width:$,height:b},k,e);U.p(t.x,t.y)}else if(U instanceof F){if("points"!==U.s("edge.type"))return _.endTransaction(),void 0;var z=U.s("edge.points");H=U.s("edge.segments"),z=zo(V.getDataUI(U),z),H&&H.size()||(H=pd(z)),H=new a(H._as.slice(0)),z=new a(z._as.slice(0));var p=H.get(C);if(2===p||1===p){var j={x:E.x,y:E.y};H.add(p,C),z.add(j,c+1)}else if(3===p){var N=z.get(c),x=z.get(c+2),j={x:E.x,y:E.y},q=rm(N,j),T=rm(j,x);H.removeAt(C),H.add(3,C),H.add(3,C),z.set(c+1,j),z.add(T,c+2),z.add(q,c+1)}else if(4===p){var N=z.get(c),x=z.get(c+3),j={x:E.x,y:E.y},q=cd(N,j),T=Ai(N,j),u=cd(j,x),o=Ai(j,x);H.removeAt(C),H.add(4,C),H.add(4,C),z.removeAt(c+1),z.removeAt(c+1),z.add(j,c+1),z.add(o,c+2),z.add(u,c+2),z.add(T,c+1),z.add(q,c+1)}U.s("edge.points",Ad(z)),U.s("edge.segments",H)}}}_.endTransaction()},removeShapePoint:function(D,d){var O=this,y="removeAt",k=O.gv,n=k.dm();if(n.beginTransaction(),0===arguments.length&&(D=O._shape||O._edge,d=O._removePointIndex),d>=0&&(D instanceof K||D instanceof F)){var g=Ir(D,O.gv.getDataUI(D),d),G=d;if(D instanceof K){var v=D.p(),Y=D.toSegments();Y&&Y.size()||(Y=pd(D.getPoints()));var W=Y.get(g),t="removePointAt";if(1===W&&0===G)if(D.getPoints().size()>1){var p=Y.get(g+1);Y[y](g+1),3===p?D[t](G+1):4===p&&(D[t](G+2),D[t](G+1)),D[t](G)}else D.dm().remove(D);else 2===W||1===W?(Y[y](g),D[t](G)):3===W?(Y[y](g),D[t](G),D[t](G-1)):4===W&&(Y[y](g),D[t](G),D[t](G-1),D[t](G-2));D.setSegments(Y);var Z=D.getWidth(),B=D.getHeight(),l=D.p().x-Z/2,A=D.p().y-B/2;l-=v.x,A-=v.y;var H=hi(D,{x:l,y:A,width:Z,height:B},D.getRotation(),v);D.p(H.x,H.y)}else if(D instanceof F){if("points"!==D.s("edge.type"))return n.endTransaction(),void 0;var b=D.s("edge.points");if(Y=D.s("edge.segments"),b&&(b=zo(O.gv.getDataUI(D),b),Y&&Y.size()||(Y=pd(b))),0===d||d===b.size()-1)return n.endTransaction(),void 0;Y=new a(Y._as.slice(0)),b=new a(b._as.slice(0));var W=Y.get(g);2===W||1===W?(Y[y](g),b[y](G)):3===W?(Y[y](g),b[y](G),b[y](G-1)):4===W&&(Y[y](g),b[y](G),b[y](G-1),b[y](G-2)),D.s("edge.segments",Y),D.s("edge.points",Ad(b))}}n.endTransaction()},changeShapeSegment:function(V,r,O){var Z=this,c=Z.gv,i=c.dm();if(i.beginTransaction(),1!==arguments.length||isNaN(V)||(O=V,V=Z._shape||Z._edge,r=Z._hoverSegmentIndex),V instanceof K||V instanceof F){var B=pq(V,Z.gv.getDataUI(V),r);if(!isNaN(r)&&!isNaN(B))if(V instanceof K){var w=V.p(),d=V.toSegments();d&&d.size()||(d=pd(V.getPoints()));var k=d.get(r),n=V.toPoints();if(1===O||2===O)1===k||2===k?d.set(r,O):3===k?(d.set(r,O),n.removeAt(B+1)):4===k&&(d.set(r,O),n.removeAt(B+1),n.removeAt(B+1));else if(3===O){var S=function(){var f=n.get(B),p=n.get(B+1),k=rm(f,p);d.set(r,3),n.add(k,B+1)};2===k||1===k?S():4===k&&(n.removeAt(B+1),n.removeAt(B+1),S())}else if(4===O){var H=function(){var t=n.get(B),w=n.get(B+1),Z=cd(t,w),E=Ai(t,w);d.set(r,4),n.add(Z,B+1),n.add(E,B+2)};2===k||1===k?H():3===k&&(n.removeAt(B+1),H())}V.setSegments(d),V.setPoints(n);var g=V.getWidth(),f=V.getHeight(),A=V.p().x-g/2,l=V.p().y-f/2;A-=w.x,l-=w.y;var X=new hi(V,{x:A,y:l,width:g,height:f},V.getRotation(),w);V.p(X.x,X.y)}else if(V instanceof F){if("points"!==V.s("edge.type"))return i.endTransaction(),void 0;var n=V.s("edge.points");d=V.s("edge.segments"),n&&(n=zo(Z.gv.getDataUI(V),n),d&&d.size()||(d=pd(n))),d=new a(d._as.slice(0)),n=new a(n._as.slice(0));var k=d.get(r);if(1===O||2===O)1===k||2===k?d.set(r,O):3===k?(d.set(r,O),n.removeAt(B+1)):4===k&&(d.set(r,O),n.removeAt(B+1),n.removeAt(B+1));else if(3===O){var S=function(){var c=n.get(B),b=n.get(B+1),I=rm(c,b);d.set(r,3),n.add(I,B+1)};2===k||1===k?S():4===k&&(n.removeAt(B+1),n.removeAt(B+1),S())}else if(4===O){var H=function(){var C=n.get(B),H=n.get(B+1),q=cd(C,H),f=Ai(C,H);d.set(r,4),n.add(q,B+1),n.add(f,B+2)};2===k||1===k?H():3===k&&(n.removeAt(B+1),H())}V.s("edge.points",Ad(n)),V.s("edge.segments",d)}}i.endTransaction()},getDefaultSegments:function(Y){var l=this.gv,E=l.getDataUI(Y);if(Y instanceof F){var H=Y.s("edge.points");return H=zo(E,H),pd(H)}if(Y instanceof K){var H=Y.getPoints(),j=Y.getSegments();return j&&j.size()>0?j:pd(H)}},$43l:function(P){var o=this;P&&(o.$5l=1),o._68I=1,o._42()},_42:function(){var r=this;if(r._68I){delete r._68I;var Q=r.gv,k=r.$5l,S=Q.getZoom(),X=Q.getEditPointSize(),d=X/2,Z=new a,u=r._view,C=r._edge,n=r._shape,q=u.getContext("2d"),p=Q.tx(),m=Q.ty(),P=A,L=Q.getEditPointBackground(),s=Q.getControlPointBackground(),T=Q.getControlPointBorderColor(),i=Q.getEditPointBorderColor(),o=Q.getEditRectBorderColor(),h=Q.getDirectionLineColor(),t=Q.getSegmentHoverColor(),x=Q.getSnapPointBorderColor(),y=Q.getJoinPointBackground(),O=Q.getJoinPointBorderColor(),R=Q.getSegmentHoverWidth(),b=[5,5],E=Q.getEditRectBorderStyle();mq(Q,function(J){Q.isSelected(J)&&Q.isVisible(J)&&Z.add(J)}),q.clearRect(0,0,l(u.width),l(u.height)),q.save(),f.translateAndScale(q,p,m,1);var D=A,j={x:0,y:0,width:0,height:0},B=0,H=0,G=0,M=0,J=0,Y=0,Qh=0,fg=0,eg=0,gs=0,_e=[];if(k?(delete r.$6l,delete r.$8l,delete r.$7l,delete r.$16l,delete r.$23l):(D=r.$6l,P=r.$7l),delete r.$68l,delete r.$69l,delete r.$71l,delete r.$70l,delete r.$82l,delete r.$83l,delete r._pointElevationHelperPoint,delete r.$90l,delete r.$91l,delete r.$75l,delete r.$74l,Z.each(function(L){var $=Q.getDataUI(L);if($){var V=$._55O;if(Q.isEditable(L))V._56O&&L instanceof W&&H++,_e.push(L),V._42O&&L instanceof W&&B++,Q.isHelperEditable(L,"host")&&M++,Q.isHelperEditable(L,"parent")&&G++,Q.isHelperEditable(L,"remove")&&J++,Q.isHelperEditable(L,"edge")&&Y++,Q.isHelperEditable(L,"source")&&Qh++,Q.isHelperEditable(L,"target")&&fg++,Q.isHelperEditable(L,"elevation")&&eg++,Q.isHelperEditable(L,"tall")&&gs++;else if(L instanceof e&&L.isExpanded()){var P=!0,o=Q.getEditableFunc();o&&(P=o(L)),P&&(Q.isHelperEditable(L,"host")&&M++,Q.isHelperEditable(L,"parent")&&G++,Q.isHelperEditable(L,"remove")&&J++,Q.isHelperEditable(L,"edge")&&Y++,Q.isHelperEditable(L,"source")&&Qh++,Q.isHelperEditable(L,"target")&&fg++,Q.isHelperEditable(L,"elevation")&&eg++,Q.isHelperEditable(L,"tall")&&gs++,_e.push(L))}}}),q.beginPath(),q.lineWidth=1,q.strokeStyle=Q.getNodeBorderColor(),_e.forEach(function(G){var s=Q.getDataUI(G),i=s._55O;if(k)if(P==A&&(P=G instanceof F?0:G instanceof e&&G.isExpanded()?0:G.getRotation(),r.$7l=P),D==A){var I,Y,b;if(G instanceof e&&G.isExpanded()||G instanceof F){var X=Q.getDataUIBounds(G);Y=X.width,b=X.height,I={x:X.x+Y/2,y:X.y+b/2}}else I=G.p(),Y=G.getWidth(),b=G.getHeight();D={x:I.x,y:I.y,width:0,height:0},j.x=I.x-Y/2,j.y=I.y-b/2,j.width=Y,j.height=b,D=c(D,j)}else{var I,Y,b,m,x=D.x+D.width/2,t=D.y+D.height/2,w={x:x,y:t};if(G instanceof e&&G.isExpanded()||G instanceof F){m=0;var X=Q.getDataUIBounds(G);Y=X.width,b=X.height,I={x:X.x+Y/2,y:X.y+b/2}}else I=G.p(),Y=G.getWidth(),b=G.getHeight(),m=G.getRotation();var y={x:I.x-Y/2,y:I.y-b/2,width:Y,height:b},J={x:y.x,y:y.y},z={x:y.x+y.width,y:y.y},$={x:y.x,y:y.y+y.height},H={x:y.x+y.width,y:y.y+y.height},J=Nq(I,J.x-I.x,J.y-I.y,m),$=Nq(I,$.x-I.x,$.y-I.y,m),H=Nq(I,H.x-I.x,H.y-I.y,m),z=Nq(I,z.x-I.x,z.y-I.y,m),l=-P,C=Nq(w,J.x-w.x,J.y-w.y,l),Z=Nq(w,z.x-w.x,z.y-w.y,l),_=Nq(w,$.x-w.x,$.y-w.y,l),R=Nq(w,H.x-w.x,H.y-w.y,l),v=U([C,Z,_,R]);D=c(D,v),D.x-=x,D.y-=t;var K=hi(A,D,P,w);D.x=K.x-D.width/2,D.y=K.y-D.height/2}if(B>1&&Q.isEditable(G)&&Q.isRectEditable(G)&&G instanceof W){var L=G.getRect(),n=i.$45l,g=sb(n[0],S),f=sb(n[2],S),E=sb(n[4],S),O=sb(n[6],S);G.getRotation()?(q.moveTo(g.x,g.y),q.lineTo(f.x,f.y),q.lineTo(E.x,E.y),q.lineTo(O.x,O.y),q.lineTo(g.x,g.y)):q.rect(L.x*S,L.y*S,L.width*S,L.height*S)}}),q.stroke(),D){r.$6l=I(D),N(D,Q.getEditRectPadding()/S);var Wh={x:D.x+D.width/2,y:D.y+D.height/2},Xp=fh(Wh,D.width,D.height,P),hk=Xp[0],Sg=Xp[1],Ni=Xp[2],vd=Xp[3],el=Xp[4],bp=Xp[5],ck=Xp[6],ie=Xp[7];if(hk=sb(Xp[0],S),Sg=sb(Xp[1],S),Ni=sb(Xp[2],S),vd=sb(Xp[3],S),el=sb(Xp[4],S),bp=sb(Xp[5],S),ck=sb(Xp[6],S),ie=sb(Xp[7],S),r.$8l=D,B>0&&(r.$16l=[hk,el,Sg,bp,vd,ck,Ni,ie],q.beginPath(),q.lineWidth=1,q.strokeStyle=o,"dashed"===E&&q.setLineDash&&q.setLineDash(b),q.moveTo(hk.x,hk.y),q.lineTo(Sg.x,Sg.y),q.lineTo(vd.x,vd.y),q.lineTo(Ni.x,Ni.y),q.lineTo(hk.x,hk.y),q.stroke(),"dashed"===E&&q.setLineDash&&q.setLineDash([])),q.beginPath(),q.lineWidth=1,q.fillStyle=L,q.strokeStyle=i,B>0){var Oo=bp.x-ie.x,ki=bp.y-ie.y,Oi=z(Oo*Oo+ki*ki),Ji=el.x-ck.x,vc=el.y-ck.y,$p=z(Ji*Ji+vc*vc),Jj=2*X;if(Q.isDirectionEditable&&1===_e.length){var oi=_e[0],Pn=Q.getDataUI(oi),Lc=Pn._55O,hn=Q.isDirectionEditable.bind(Q);if(Lc._42O){var Hc,es,Me,Zc;hn(oi,"northwest")&&(Hc=!0,q.rect(hk.x-d,hk.y-d,X,X)),hn(oi,"northeast")&&(es=!0,q.rect(Sg.x-d,Sg.y-d,X,X)),hn(oi,"southwest")&&(Me=!0,q.rect(Ni.x-d,Ni.y-d,X,X)),hn(oi,"southeast")&&(Zc=!0,q.rect(vd.x-d,vd.y-d,X,X)),hn(oi,"north")&&(Hc&&es?Oi>Jj&&q.rect(el.x-d,el.y-d,X,X):q.rect(el.x-d,el.y-d,X,X)),hn(oi,"south")&&(Zc&&Me?Oi>Jj&&q.rect(ck.x-d,ck.y-d,X,X):q.rect(ck.x-d,ck.y-d,X,X)),hn(oi,"west")&&(Me&&Hc?$p>Jj&&q.rect(ie.x-d,ie.y-d,X,X):q.rect(ie.x-d,ie.y-d,X,X)),hn(oi,"east")&&(Zc&&es?$p>Jj&&q.rect(bp.x-d,bp.y-d,X,X):q.rect(bp.x-d,bp.y-d,X,X))}}else Oi>Jj&&(q.rect(el.x-d,el.y-d,X,X),q.rect(ck.x-d,ck.y-d,X,X)),$p>Jj&&(q.rect(bp.x-d,bp.y-d,X,X),q.rect(ie.x-d,ie.y-d,X,X)),q.rect(hk.x-d,hk.y-d,X,X),q.rect(Sg.x-d,Sg.y-d,X,X),q.rect(Ni.x-d,Ni.y-d,X,X),q.rect(vd.x-d,vd.y-d,X,X)}if(H>0){var xe=r.$23l=Nq(el,0,2*-X,P);q.moveTo(xe.x+d,xe.y),q.arc(xe.x,xe.y,d,0,g,!1)}q.fill(),q.stroke(),r.$40l&&r.$44l(q,D,P);var Mr,Ee,Je=Q.getLeftTopHelpers(),Cq=Q.getRightTopHelpers(),gm=Q.getLeftBottomHelpers(),Tb=Q.getRightBottomHelpers(),Og=Q.getEdgeHelpers(),Xr=Q.getEditHelperSize(),ts=Xr/2,nk=function(N,p,g,m){"leftTop"===p?(Mr=D.x*S-Xr-d-("h"===m?g*(Xr+d):0)-1,Ee=D.y*S-Xr-d-("v"===m?g*(Xr+d):0)-1):"rightTop"===p?(Mr=(D.x+D.width)*S+d+("h"===m?g*(Xr+d):0)+1,Ee=D.y*S-Xr-d-("v"===m?g*(Xr+d):0)-1):"leftBottom"===p?(Mr=D.x*S-Xr-d-("h"===m?g*(Xr+d):0)-1,Ee=(D.y+D.height)*S+d+("v"===m?g*(Xr+d):0)+1):"rightBottom"===p&&(Mr=(D.x+D.width)*S+d+("h"===m?g*(Xr+d):0)+1,Ee=(D.y+D.height)*S+d+("v"===m?g*(Xr+d):0)+1);var j=Mr+ts,W=Ee+ts,o=(D.x+D.width/2)*S,e=(D.y+D.height/2)*S,Z=Nq({x:o,y:e},j-o,W-e,P);if("host"===N){if(M>0)return r.$77l(q,P,Z),!0}else if("edge"===N){if(Y>0)return r.$80l(q,P,Z),!0}else if("remove"===N){if(J>0)return _e&&1===_e.length&&_e[0]instanceof F?r.$81l(q,P,{x:o+Xr/2+5,y:e}):r.$81l(q,P,Z),!0}else if("parent"===N){if(G>0)return _e&&1===_e.length&&_e[0]instanceof F?r.$76l(q,P,{x:o-Xr/2-5,y:e}):r.$76l(q,P,Z),!0}else if("elevation"===N){if(eg>0)return r.$85l(q,P,Z),!0}else if("tall"===N){if(gs>0)return r.$86l(q,P,Z),!0}else if("source"===N||"target"===N){var B=_e[0],h=Q.getDataUI(B),C=bl(h,!0),y=C[0],u=C[1];if(Qh&&"source"===N)return r.$84l(q,D,y,!0,B.isLooped(),p,g,m),!0;if(fg&&"target"===N)return r.$84l(q,D,u,!1,B.isLooped(),p,g,m),!0}};if(Je&&Je.length>0){var Wn=0;Je.forEach(function(K){nk(K,"leftTop",Wn,Q.getLeftTopHelpersAlign()||"h")&&Wn++})}if(Cq&&Cq.length>0){var Wn=0;Cq.forEach(function(T){nk(T,"rightTop",Wn,Q.getRightTopHelpersAlign()||"h")&&Wn++})}if(gm&&gm.length>0){var Wn=0;gm.forEach(function(G){nk(G,"leftBottom",Wn,Q.getLeftBottomHelpersAlign()||"h")&&Wn++})}if(Tb&&Tb.length>0){var Wn=0;Tb.forEach(function(w){nk(w,"rightBottom",Wn,Q.getRightBottomHelpersAlign()||"h")&&Wn++})}if(Og&&Og.length>0&&((Qh||fg)&&_e&&1===_e.length&&_e[0]instanceof F?!0:!1)){var Wn=0;Og.forEach(function(K){nk(K,"edge",Wn,"h")&&Wn++})}if(r.$72l){var Di,Io=r.$67l,oq=r.$68l,Vq=r.$69l,ep=(r.$71l,r.$70l),jl=r.$82l,Wf=r.$83l;if(1===Io?Di=oq:2===Io?Di=Vq:3===Io?Di=ep:5===Io?Di=jl:6===Io&&(Di=Wf),Di){var Rq=Q.lp(r.$72l),of=Q.getDataAt(r.$72l);if(of&&of instanceof W){var lm,yj,wl;if(r.$73l=of,1===Io)if(of instanceof v.Grid)lm=r.$75l=lc(of,Rq);else if(of instanceof v.Shape){wl=r.$74l=Ko(of,Rq);var Sh=of.getPoints();if(wl){var th=wl.attachIndex,_h=pq(of,Q.getDataUI(of),th)+1,Ci=_h+1>=Sh.size()?0:_h+1,zm=Sh.get(_h),ul=Sh.get(Ci),Tg=of.p(),Md=of.getRotation();zm=Nq(Tg,zm.x-Tg.x,zm.y-Tg.y,Md),ul=Nq(Tg,ul.x-Tg.x,ul.y-Tg.y,Md),q.beginPath(),q.strokeStyle=t,q.lineWidth=R,q.moveTo(zm.x*S,zm.y*S),q.lineTo(ul.x*S,ul.y*S),q.stroke()}}lm?yj=lm.rect:(yj=V.clone(Q.getDataUIBounds(of)),V.grow(yj,2)),q.beginPath(),q.rect(yj.x*S,yj.y*S,yj.width*S,yj.height*S),q.strokeStyle=1===Io||4===Io||6===Io?w:$,q.lineWidth=2,q.setLineDash&&q.setLineDash([8,8]),q.stroke()}else delete r.$73l;Rq.x=Rq.x*S,Rq.y=Rq.y*S;var Jl=od(Di.x,Di.y,Rq.x,Rq.y),vh=Nq(Di,Rq.x-Di.x,Rq.y-Di.y,-Jl),jn=vh.x-Di.x;q.save(),q.translate(Di.x,Di.y),q.rotate(Jl),_.width=jn,_.height=40;var yl=$;(1===Io||4===Io||6===Io)&&(yl=w),_.comps[0].gradientColor=yl,V.drawImage(q,_,0,-20,jn,40),q.restore()}}}if(Z.each(function(l){var m=Q.getDataUI(l);if(m){var U,u,p=m._55O,E=m._78o;if(p&&p._43O&&l instanceof K?(U=p.$15l,u=l.getSegments(),u&&u.size()||(u=pd(U)),l.isClosePath()&&u.indexOf(5)<0&&(u=new a(u._as),u.add(5))):p&&E&&p._43O&&(U=l.s("edge.points"),u=l.s("edge.segments"),U=zo(m,U),u&&u.size()||(u=pd(U)),U&&(U=U._as)),U&&u){var W=0,z=u.size(),M=r._hoverSegmentIndex;U=U.slice(0);for(var b=0;b<U.length;b++)U[b]=sb(U[b],S);q.beginPath(),q.strokeStyle=h,q.lineWidth=1,W=0;for(var b=0;z>b;b++){var v=u.get(b);if(1===v||2===v)W++;else if(3===v){var H=U[W-1],G=U[W],J=U[W+1];q.moveTo(H.x,H.y),q.lineTo(G.x,G.y),q.lineTo(J.x,J.y),W+=2}else if(4===v){var H=U[W-1],k=U[W],f=U[W+1],J=U[W+2];q.moveTo(H.x,H.y),q.lineTo(k.x,k.y),q.moveTo(f.x,f.y),q.lineTo(J.x,J.y),W+=3}}if(q.stroke(),n===l||C===l&&"points"===C.s("edge.type")){q.beginPath(),W=0,q.strokeStyle=t,q.lineWidth=R;for(var b=0;z>b;b++){var v=u.get(b);if(1===v||2===v){if(M===b){var H=U[W-1],J=U[W];q.moveTo(H.x,H.y),q.lineTo(J.x,J.y);break}W++}else if(3===v){if(M===b){var H=U[W-1],G=U[W],J=U[W+1];q.moveTo(H.x,H.y),q.quadraticCurveTo(G.x,G.y,J.x,J.y);break}W+=2}else if(4===v){if(M===b){var H=U[W-1],k=U[W],f=U[W+1],J=U[W+2];q.moveTo(H.x,H.y),q.bezierCurveTo(k.x,k.y,f.x,f.y,J.x,J.y);break}W+=3}else if(5===v&&M===b){var H=U[W-1],J=U[0];q.moveTo(H.x,H.y),q.lineTo(J.x,J.y);break}}q.stroke()}q.beginPath(),W=0,q.strokeStyle=O,q.lineWidth=1,q.fillStyle=y;for(var b=0;z>b;b++){var v=u.get(b);if(1===v||2===v){var J=U[W];q.moveTo(J.x+d,J.y),q.arc(J.x,J.y,d,0,g,!0),W++}else if(3===v){var J=U[W+1];q.moveTo(J.x+d,J.y),q.arc(J.x,J.y,d,0,g,!0),W+=2}else if(4===v){var J=U[W+2];q.moveTo(J.x+d,J.y),q.arc(J.x,J.y,d,0,g,!0),W+=3}}q.fill(),q.stroke(),q.beginPath(),W=0,q.strokeStyle=T,q.lineWidth=1,q.fillStyle=s;for(var b=0;z>b;b++){var v=u.get(b);if(1===v||2===v)W++;else if(3===v){var G=U[W];q.moveTo(G.x+d,G.y),q.arc(G.x,G.y,d,0,g,!0),W+=2}else if(4===v){var k=U[W],f=U[W+1];q.moveTo(k.x+d,k.y),q.arc(k.x,k.y,d,0,g,!0),q.moveTo(f.x+d,f.y),q.arc(f.x,f.y,d,0,g,!0),W+=3}}if(q.fill(),q.stroke(),q.beginPath(),W=0,q.strokeStyle=x,q.lineWidth=2,(n===l||C===l)&&r.$9l!=A){var J=U[r.$9l];q.moveTo(J.x+d,J.y),q.arc(J.x,J.y,d,0,g,!0),q.stroke()}}}}),r.$87l&&Q.sm().contains(r.$87l)){var ud,qc=r.$87l,Mi=r.$88l,Xr=Q.getEditHelperSize(),ts=Xr/2;if(Q.isHelperEditable(qc,"pointElevation"))if(qc instanceof K){var Mm=qc.getPosition();ud=V.clone(qc.getPoints().get(Mi)),ud=Nq({x:Mm.x,y:Mm.y},ud.x-Mm.x,ud.y-Mm.y,qc.getRotation()),ud.x*=S,ud.y*=S,q.translate(ud.x,ud.y),V.drawImage(q,V.getImage("xeditinteractor_elevationIcon"),ts,-ts,Xr,Xr),q.translate(-ud.x,-ud.y),r._pointElevationHelperPoint={x:ud.x+Xr,y:ud.y}}else if(qc instanceof F){var Qk=zo(Q.getDataUI(qc),qc.s("edge.points")),ud=V.clone(Qk.get(Mi));ud.x*=S,ud.y*=S,q.translate(ud.x,ud.y),V.drawImage(q,V.getImage("xeditinteractor_elevationIcon"),ts,-ts,Xr,Xr),q.translate(-ud.x,-ud.y),r._pointElevationHelperPoint={x:ud.x+Xr,y:ud.y}}}q.restore(),r.$5l=0}},$89l:function(y,F,e,O){y.save();var Z=this,P=Z.gv,J=P.getEditHelperSize(),U=J/2;y.beginPath(),y.translate(e.x,e.y),y.rotate(F),V.drawImage(y,O,-U,-U,J,J),y.restore()},$85l:function(u,J,b){var y=this;y.$90l=b,y.$89l(u,J,b,V.getImage("xeditinteractor_elevationIcon"))},$86l:function(i,$,Y){var p=this;p.$91l=Y,p.$89l(i,$,Y,V.getImage("xeditinteractor_tallIcon"))},$77l:function(H,J,Q){var x=this;x.$68l=Q,x.$89l(H,J,Q,V.getImage("xeditinteractor_hostIcon"))},$80l:function(G,I,_){var R=this;R.$70l=_,R.$89l(G,I,_,V.getImage("xeditinteractor_edgeIcon"))},$81l:function(Q,u,j){var b=this;b.$71l=j,b.$89l(Q,u,j,V.getImage("xeditinteractor_removeIcon"))},$76l:function($,_,z){var f=this;f.$69l=z,f.$89l($,_,z,V.getImage("xeditinteractor_parentIcon"))},$84l:function(U,l,S,w,h){var P,M=this,z=M.gv,x=z.getZoom(),r=z.getEditHelperSize(),K=r/2;S.x*=x,S.y*=x,h&&(w?S.x-=K+5:S.x+=K+5),S.y-=r-2,w?(M.$82l=S,P=V.getImage("xeditinteractor_sourceIcon")):(M.$83l=S,P=V.getImage("xeditinteractor_targetIcon")),U.beginPath(),U.translate(S.x,S.y),V.drawImage(U,P,-K,-K,r,r),U.translate(-S.x,-S.y)},$44l:function(I,N,p){I.save();var B=this.gv.getZoom(),u=z(d(N.width,2)+d(N.height,2))/2*B+10,l=u,e=l+8,J=l+18,i=p,q=N.x+N.width/2,T=N.y+N.height/2;I.translate(q*B,T*B),I.beginPath(),I.strokeStyle="rgb(150, 150, 150)";for(var W=0,v=0,C=0,g=0,y=0,Z=0;360>Z;Z++)W=Z*Q/180,0===Z%5&&(v=r(W)*l,C=Y(W)*l,g=r(W)*e,y=Y(W)*e,I.moveTo(v,C),I.lineTo(g,y)),0===Z%45&&(v=r(W)*l,C=Y(W)*l,g=r(W)*J,y=Y(W)*J,I.moveTo(v,C),I.lineTo(g,y));I.stroke(),I.beginPath(),I.rotate(-R),I.beginPath(),I.fillStyle="rgba(255, 0, 0, 0.1)",I.strokeStyle="rgb(157, 157, 157)",I.moveTo(0,0),I.arc(0,0,J,0,p,!1),I.fill(),I.beginPath(),I.moveTo(0+J,0),I.arc(0,0,J,0,p,!1),I.stroke(),I.rotate(R),I.font="bold 12px Verdana, Arial";var G=20*Y(i),X=20*r(i),b=t(180*i/Q)+"°";I.textAlign="center",I.textBaseline="middle",I.beginPath();var $=I.measureText("365.°").width;I.fillStyle="white",I.rect(G-$/2,-X-10,$,20),I.fill(),I.beginPath(),I.fillStyle="rgb(79,79,79)",I.fillText(b,G,-X),I.restore()}})}("undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:(0,eval)("this"),Object);