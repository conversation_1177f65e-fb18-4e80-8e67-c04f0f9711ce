!function(U){"use strict";var p="ht",z=U[p],x=Math,k=x.max,O=x.min,I=x.abs,S=x.atan2,C=(x.cos,x.sin,x.ceil),r=z.Default,N=r.getInternal(),J=z.List,X=N.Mat,j=N.getNodeRect,Z=N.intersectionLineRect,Q=r.getDistance,L=r.setEdgeType,m="left",t="right",o="top",w="bottom",i="edge.type",u="edge.gap",R="edge.center",T="edge.extend",V=function(D,_){return j(D,_).width},q=function(Y,B){return j(Y,B).height},K=function(q,y){return N.getEdgeHostPosition(q,y,"source")},D=function(Z,n){return N.getEdgeHostPosition(Z,n,"target")},s=function(p,v){var X=p.s(i),z=p.getEdgeGroup();if(z){var r=0;if(z.eachSiblingEdge(function(q){v.isVisible(q)&&q.s(i)==X&&r++}),r>1)return p.s(u)*(r-1)/2}return 0},E=function(f,Q){var l=f.s(i),d=f.isLooped();if(!f.getEdgeGroup())return d?f.s(u):0;var N,h=0,G=0,m=0;return f.getEdgeGroup().getSiblings().each(function(x){x.each(function(L){if(L._40I===f._40I&&L.s(i)==l&&Q.isVisible(L)){var b=L.s(u);N?(G+=m/2+b/2,m=b):(N=L,m=b),L===f&&(h=G)}})}),d?G-h+m:h-G/2},A=function(f,L){var e=L.s("edge.corner.radius");return r.toRoundedCorner(f,e)};N.addMethod(z.Style,{"edge.ripple.elevation":-20,"edge.ripple.size":1,"edge.ripple.both":!1,"edge.ripple.straight":!1,"edge.ripple.length":-1,"edge.corner.radius":-1,"edge.ortho":.5,"edge.flex":20,"edge.extend":20},!0),L("boundary",function(t,v,O,z){z||(v=-v);var B,Y=K(O,t),q=D(O,t),y=j(O,t._40I),A=j(O,t._41I),M=new X(S(q.y-Y.y,q.x-Y.x)),b=Q(Y,q),d=Y.x,l=Y.y;return B=M.tf(0,v),Y={x:B.x+d,y:B.y+l},B=M.tf(b,v),q={x:B.x+d,y:B.y+l},B=Z(Y,q,y),B&&(Y={x:B[0],y:B[1]}),B=Z(Y,q,A),B&&(q={x:B[0],y:B[1]}),{points:new J([Y,q])}}),L("ripple",function(I,H,c,k){k||(H=-H);var G=K(c,I),f=D(c,I),n=Q(G,f),z=O(I.s("edge.offset"),n/2),v=I.s("edge.ripple.size"),i=I.s("edge.ripple.length"),s=I.s("edge.ripple.both"),o=I.s(R),d=I.s("edge.ripple.elevation"),a=new J,N=I.s("edge.ripple.straight")?null:new J,M=new X(S(f.y-G.y,f.x-G.x));k||(d=-d),n-=2*z,i>0&&(v=C(n/i));var V=n/v;N&&N.add(1);for(var u=0;v>u;u++)N&&N.add(3),0===u?a.add({x:z+V*u,y:o?0:H}):a.add({x:z+V*u,y:H}),a.add({x:z+V*u+V/2,y:d+H}),s&&(d=-d);for(a.add({x:z+n,y:o?0:H}),u=0;u<a.size();u++){var q=M.tf(a.get(u));q.x+=G.x,q.y+=G.y,a.set(u,q)}return{points:a,segments:N}}),L("h.v",function(M,r,W){r=E(M,W);var U=new J,Z=M.s(R),L=K(W,M),Y=L.x,o=L.y,f=D(W,M),u=f.x,i=f.y,g=M._40I instanceof z.Edge,S=M._41I instanceof z.Edge,m=0,p=0,F=u-Y,G=i-o;return Z||(m=g?0:V(W,M._40I)/2,p=S?0:q(W,M._41I)/2),F>=0&&0>=G?(U.add({x:Y+m,y:o+r}),U.add({x:u+r,y:o+r}),U.add({x:u+r,y:i+p})):0>=F&&G>=0?(U.add({x:Y-m,y:o+r}),U.add({x:u+r,y:o+r}),U.add({x:u+r,y:i-p})):F>=0&&G>=0?(U.add({x:Y+m,y:o+r}),U.add({x:u-r,y:o+r}),U.add({x:u-r,y:i-p})):(U.add({x:Y-m,y:o+r}),U.add({x:u-r,y:o+r}),U.add({x:u-r,y:i+p})),A(U,M)}),L("v.h",function(G,f,N){f=E(G,N);var U=new J,p=G.s(R),m=K(N,G),Y=m.x,u=m.y,C=D(N,G),s=C.x,L=C.y,j=G._40I instanceof z.Edge,Q=G._41I instanceof z.Edge,B=0,n=0,F=s-Y,S=L-u;return p||(B=Q?0:V(N,G._41I)/2,n=j?0:q(N,G._40I)/2),F>=0&&0>=S?(U.add({x:Y+f,y:u-n}),U.add({x:Y+f,y:L+f}),U.add({x:s-B,y:L+f})):0>=F&&S>=0?(U.add({x:Y+f,y:u+n}),U.add({x:Y+f,y:L+f}),U.add({x:s+B,y:L+f})):F>=0&&S>=0?(U.add({x:Y-f,y:u+n}),U.add({x:Y-f,y:L+f}),U.add({x:s-B,y:L+f})):(U.add({x:Y-f,y:u-n}),U.add({x:Y-f,y:L+f}),U.add({x:s+B,y:L+f})),A(U,G)}),L("ortho",function(U,G,Z){var m=new J,N=U.s(R),i=U.s("edge.ortho"),s=U._40I,e=U._41I,g=K(Z,U),Q=g.x,$=g.y,u=D(Z,U),_=u.x,O=u.y,B=_-Q,y=O-$,H=s instanceof z.Edge,v=e instanceof z.Edge,E=N||H?0:V(Z,s)/2,j=N||H?0:q(Z,s)/2,r=N||v?0:V(Z,e)/2,S=N||v?0:q(Z,e)/2,f=(B-(E+r)*(B>0?1:-1))*i,L=(y-(j+S)*(y>0?1:-1))*i;return I(B)<I(y)?B>=0&&0>=y?(m.add({x:Q+G,y:$-j}),m.add({x:Q+G,y:$+L+G-j}),m.add({x:_+G,y:$+L+G-j}),m.add({x:_+G,y:O+S})):0>=B&&y>=0?(m.add({x:Q+G,y:$+j}),m.add({x:Q+G,y:$+L+G+j}),m.add({x:_+G,y:$+L+G+j}),m.add({x:_+G,y:O-S})):B>=0&&y>=0?(m.add({x:Q+G,y:$+j}),m.add({x:Q+G,y:$+L-G+j}),m.add({x:_+G,y:$+L-G+j}),m.add({x:_+G,y:O-S})):(m.add({x:Q+G,y:$-j}),m.add({x:Q+G,y:$+L-G-j}),m.add({x:_+G,y:$+L-G-j}),m.add({x:_+G,y:O+S})):B>=0&&0>=y?(m.add({x:Q+E,y:$+G}),m.add({x:Q+f+G+E,y:$+G}),m.add({x:Q+f+G+E,y:O+G}),m.add({x:_-r,y:O+G})):0>=B&&y>=0?(m.add({x:Q-E,y:$+G}),m.add({x:Q+f+G-E,y:$+G}),m.add({x:Q+f+G-E,y:O+G}),m.add({x:_+r,y:O+G})):B>=0&&y>=0?(m.add({x:Q+E,y:$+G}),m.add({x:Q+f-G+E,y:$+G}),m.add({x:Q+f-G+E,y:O+G}),m.add({x:_-r,y:O+G})):(m.add({x:Q-E,y:$+G}),m.add({x:Q+f-G-E,y:$+G}),m.add({x:Q+f-G-E,y:O+G}),m.add({x:_+r,y:O+G})),A(m,U)}),L("flex",function(O,u,a){var P=new J,r=O.s("edge.flex")+s(O,a),g=O.s(R),C=O._40I,f=O._41I,N=K(a,O),p=N.x,B=N.y,w=D(a,O),k=w.x,L=w.y,X=C instanceof z.Edge,_=f instanceof z.Edge,Q=k-p,h=L-B,j=g||X?0:V(a,C)/2,l=g||X?0:q(a,C)/2,y=g||_?0:V(a,f)/2,m=g||_?0:q(a,f)/2,Z=Q>0?r:-r,E=h>0?r:-r;return I(Q)<I(h)?Q>=0&&0>=h?(P.add({x:p+u,y:B-l}),P.add({x:p+u,y:B+E+u-l}),P.add({x:k+u,y:L-E+u+m}),P.add({x:k+u,y:L+m})):0>=Q&&h>=0?(P.add({x:p+u,y:B+l}),P.add({x:p+u,y:B+E+u+l}),P.add({x:k+u,y:L-E+u-m}),P.add({x:k+u,y:L-m})):Q>=0&&h>=0?(P.add({x:p+u,y:B+l}),P.add({x:p+u,y:B+E-u+l}),P.add({x:k+u,y:L-E-u-m}),P.add({x:k+u,y:L-m})):(P.add({x:p+u,y:B-l}),P.add({x:p+u,y:B+E-u-l}),P.add({x:k+u,y:L-E-u+m}),P.add({x:k+u,y:L+m})):Q>=0&&0>=h?(P.add({x:p+j,y:B+u}),P.add({x:p+Z+u+j,y:B+u}),P.add({x:k-Z+u-y,y:L+u}),P.add({x:k-y,y:L+u})):0>=Q&&h>=0?(P.add({x:p-j,y:B+u}),P.add({x:p+Z+u-j,y:B+u}),P.add({x:k-Z+u+y,y:L+u}),P.add({x:k+y,y:L+u})):Q>=0&&h>=0?(P.add({x:p+j,y:B+u}),P.add({x:p+Z-u+j,y:B+u}),P.add({x:k-Z-u-y,y:L+u}),P.add({x:k-y,y:L+u})):(P.add({x:p-j,y:B+u}),P.add({x:p+Z-u-j,y:B+u}),P.add({x:k-Z-u+y,y:L+u}),P.add({x:k+y,y:L+u})),A(P,O)}),L("extend.east",function(j,w,r){var U=new J,u=j.s(T)+s(j,r),X=j.s(R),n=K(r,j),x=j._40I instanceof z.Edge,N=j._41I instanceof z.Edge,W=n.x+(X||x?0:V(r,j._40I)/2),O=n.y,Z=D(r,j),b=Z.x+(X||N?0:V(r,j._41I)/2),i=Z.y,Y=k(W,b)+u;return O>i?(U.add({x:W,y:O+w}),U.add({x:Y+w,y:O+w}),U.add({x:Y+w,y:i-w}),U.add({x:b,y:i-w})):(U.add({x:W,y:O-w}),U.add({x:Y+w,y:O-w}),U.add({x:Y+w,y:i+w}),U.add({x:b,y:i+w})),A(U,j)}),L("extend.west",function(k,C,I){var m=new J,j=k.s(T)+s(k,I),f=k.s(R),X=k._40I instanceof z.Edge,_=k._41I instanceof z.Edge,q=K(I,k),M=q.x-(f||X?0:V(I,k._40I)/2),x=q.y,h=D(I,k),P=h.x-(f||_?0:V(I,k._41I)/2),G=h.y,n=O(M,P)-j;return x>G?(m.add({x:M,y:x+C}),m.add({x:n-C,y:x+C}),m.add({x:n-C,y:G-C}),m.add({x:P,y:G-C})):(m.add({x:M,y:x-C}),m.add({x:n-C,y:x-C}),m.add({x:n-C,y:G+C}),m.add({x:P,y:G+C})),A(m,k)}),L("extend.north",function(W,Q,f){var Y=new J,i=W.s(T)+s(W,f),g=W.s(R),d=W._40I instanceof z.Edge,c=W._41I instanceof z.Edge,u=K(f,W),o=u.x,w=u.y-(g||d?0:q(f,W._40I)/2),X=D(f,W),t=X.x,C=X.y-(g||c?0:q(f,W._41I)/2),H=O(w,C)-i;return o>t?(Y.add({y:w,x:o+Q}),Y.add({y:H-Q,x:o+Q}),Y.add({y:H-Q,x:t-Q}),Y.add({y:C,x:t-Q})):(Y.add({y:w,x:o-Q}),Y.add({y:H-Q,x:o-Q}),Y.add({y:H-Q,x:t+Q}),Y.add({y:C,x:t+Q})),A(Y,W)}),L("extend.south",function(o,H,b){var G=new J,$=o.s(T)+s(o,b),v=o.s(R),W=o._40I instanceof z.Edge,n=o._41I instanceof z.Edge,l=K(b,o),x=l.x,Z=l.y+(v||W?0:q(b,o._40I)/2),p=D(b,o),X=p.x,w=p.y+(v||n?0:q(b,o._41I)/2),C=k(Z,w)+$;return x>X?(G.add({y:Z,x:x+H}),G.add({y:C+H,x:x+H}),G.add({y:C+H,x:X-H}),G.add({y:w,x:X-H})):(G.add({y:Z,x:x-H}),G.add({y:C+H,x:x-H}),G.add({y:C+H,x:X+H}),G.add({y:w,x:X+H})),A(G,o)});var e=function(Y,_,d,z,i){if(z.sort(function(n,Q){var J=n.getSourceAgent()===_?n.getTargetAgent():n.getSourceAgent(),v=Q.getSourceAgent()===_?Q.getTargetAgent():Q.getSourceAgent(),H=J.p(),W=v.p();if(d===m||d===t){if(H.y>W.y)return 1;if(H.y<W.y)return-1}else{if(H.x>W.x)return 1;if(H.x<W.x)return-1}return r.sortFunc(n.getId(),Q.getId())}),i){for(var j,k,c,U=Y.getSourceAgent(),C=Y.getTargetAgent(),v=0;v<z.size();v++){var F=z.get(v);F.getSourceAgent()===U&&F.getTargetAgent()===C||F.getTargetAgent()===U&&F.getSourceAgent()===C?(k||(k=new J),k.add(F,0)):k?(c||(c=new J),c.add(F)):(j||(j=new J),j.add(F))}z.clear(),j&&z.addAll(j),k&&z.addAll(k),c&&z.addAll(c)}var E=z.indexOf(Y),G=z.size(),A=Y.s(u);return{side:d,index:E,size:G,offset:-A*(G-1)/2+A*E}},h=function(v,h,P,o,p){var F=h.s(i);return e(h,P,o,P.getAgentEdges().toList(function(e){return v.isVisible(e)&&e.s(i)===F}),p)},v=function(g,J){var E=g.getSourceAgent()===J?g.getTargetAgent():g.getSourceAgent(),c=J.p(),H=E.p(),r=H.x-c.x,A=H.y-c.y;return r>0&&I(A)<=r?t:0>r&&I(A)<=-r?m:A>0&&I(r)<=A?w:o},a=function(M,h,b){var z=h.s(i),$=v(h,b);return e(h,b,$,b.getAgentEdges().toList(function(m){return M.isVisible(m)&&m.s(i)===z&&v(m,b)===$}))},n=function(c,e){var k=c.getSourceAgent()===e,E=k?c.getTargetAgent():c.getSourceAgent(),$=e.p(),I=E.p();return k?$.y>I.y?o:w:$.x<I.x?t:m},W=function(h,M,_){var k=M.s(i),N=n(M,_);return e(M,_,N,_.getAgentEdges().toList(function(l){return h.isVisible(l)&&l.s(i)===k&&n(l,_)===N}),N===t||N===w)},G=function(l,v){var R=l.getSourceAgent()===v,C=R?l.getTargetAgent():l.getSourceAgent(),d=v.p(),S=C.p();return R?d.x<S.x?t:m:d.y>S.y?o:w},$=function(P,S,H){var n=S.s(i),h=G(S,H);return e(S,H,h,H.getAgentEdges().toList(function(Y){return P.isVisible(Y)&&Y.s(i)===n&&G(Y,H)===h}),h===t||h===w)},F=function(h,x,d){var U=h.getSourceAgent(),W=h.getTargetAgent(),z=U.getId()>W.getId(),Y=z?W:U,S=z?U:W,L=Y.p(),X=S.p(),Q=d(x,h,Y),B=d(x,h,S),f=h.s(R),I=f?0:V(x,Y)/2,N=f?0:V(x,S)/2,k=f?0:q(x,Y)/2,G=f?0:q(x,S)/2,s=Q.offset,Z=B.offset,O=Q.side,K=B.side,u=new J;return O===o?(u.add({x:L.x+s,y:L.y-k}),u.add({x:L.x+s,y:X.y+Z}),K===m?u.add({x:X.x-N,y:X.y+Z}):u.add({x:X.x+N,y:X.y+Z})):O===w?(u.add({x:L.x+s,y:L.y+k}),u.add({x:L.x+s,y:X.y+Z}),K===m?u.add({x:X.x-N,y:X.y+Z}):u.add({x:X.x+N,y:X.y+Z})):O===m?(u.add({x:L.x-I,y:L.y+s}),u.add({x:X.x+Z,y:L.y+s}),K===w?u.add({x:X.x+Z,y:X.y+G}):u.add({x:X.x+Z,y:X.y-G})):O===t&&(u.add({x:L.x+I,y:L.y+s}),u.add({x:X.x+Z,y:L.y+s}),K===w?u.add({x:X.x+Z,y:X.y+G}):u.add({x:X.x+Z,y:X.y-G})),z&&u.reverse(),A(u,h)};L("ortho2",function(G,g,i){var y,n,$=G.s(R),H=G.s("edge.ortho"),r=G.getSourceAgent(),x=G.getTargetAgent(),z=r.getId()>x.getId(),v=z?x:r,b=z?r:x,I=v.p(),O=b.p(),h=a(i,G,v),P=a(i,G,b),W=$?0:V(i,v)/2,k=$?0:q(i,v)/2,u=$?0:V(i,b)/2,T=$?0:q(i,b)/2,p=new J,M=h.offset,E=P.offset,d=h.side;return d===t?(y=O.y>I.y?-M:M,n=I.x+W+(O.x-u-I.x-W)*H,p.add({x:I.x+W,y:I.y+M}),p.add({x:n+y,y:I.y+M}),p.add({x:n+y,y:O.y+E}),p.add({x:O.x-u,y:O.y+E})):d===m?(y=O.y>I.y?-M:M,n=I.x-W-(I.x-W-O.x-u)*H,p.add({x:I.x-W,y:I.y+M}),p.add({x:n-y,y:I.y+M}),p.add({x:n-y,y:O.y+E}),p.add({x:O.x+u,y:O.y+E})):d===w?(y=O.x>I.x?-M:M,n=I.y+k+(O.y-T-I.y-k)*H,p.add({x:I.x+M,y:I.y+k}),p.add({x:I.x+M,y:n+y}),p.add({x:O.x+E,y:n+y}),p.add({x:O.x+E,y:O.y-T})):d===o&&(y=O.x>I.x?-M:M,n=I.y-k-(I.y-k-O.y-T)*H,p.add({x:I.x+M,y:I.y-k}),p.add({x:I.x+M,y:n-y}),p.add({x:O.x+E,y:n-y}),p.add({x:O.x+E,y:O.y+T})),z&&p.reverse(),A(p,G)},!0),L("flex2",function(y,z,N){var r,d=y.getSourceAgent(),S=y.getTargetAgent(),j=d.getId()>S.getId(),k=j?S:d,E=j?d:S,Y=k.p(),L=E.p(),f=a(N,y,k),H=a(N,y,E),B=y.s(R),U=y.s("edge.flex")+(f.size-1)/2*y.s(u),Z=B?0:V(N,k)/2,l=B?0:q(N,k)/2,K=B?0:V(N,E)/2,D=B?0:q(N,E)/2,v=new J,W=f.offset,e=H.offset,g=f.side;return g===t?(r=L.y>Y.y?-W:W,v.add({x:Y.x+Z,y:Y.y+W}),v.add({x:Y.x+Z+U+r,y:Y.y+W}),v.add({x:L.x-K-U+r,y:L.y+e}),v.add({x:L.x-K,y:L.y+e})):g===m?(r=L.y>Y.y?-W:W,v.add({x:Y.x-Z,y:Y.y+W}),v.add({x:Y.x-Z-U-r,y:Y.y+W}),v.add({x:L.x+K+U-r,y:L.y+e}),v.add({x:L.x+K,y:L.y+e})):g===w?(r=L.x>Y.x?-W:W,v.add({x:Y.x+W,y:Y.y+l}),v.add({x:Y.x+W,y:Y.y+l+U+r}),v.add({x:L.x+e,y:L.y-D-U+r}),v.add({x:L.x+e,y:L.y-D})):g===o&&(r=L.x>Y.x?-W:W,v.add({x:Y.x+W,y:Y.y-l}),v.add({x:Y.x+W,y:Y.y-l-U-r}),v.add({x:L.x+e,y:L.y+D+U-r}),v.add({x:L.x+e,y:L.y+D})),j&&v.reverse(),A(v,y)},!0),L("extend.north2",function(L,g,m){var V=L.getSourceAgent(),K=L.getTargetAgent(),r=V.getId()>K.getId(),M=r?K:V,d=r?V:K,v=M.p(),B=d.p(),H=h(m,L,M,o),l=h(m,L,d,o,!0),i=L.s(R),$=i?0:q(m,M)/2,f=i?0:q(m,d)/2,G=H.offset,w=l.offset,n=L.s(T)+(H.size-1)/2*L.s(u),Q=O(v.y-$,B.y-f)-n+(v.x<B.x?G:-G),N=new J;return N.add({x:v.x+G,y:v.y-$}),N.add({x:v.x+G,y:Q}),N.add({x:B.x+w,y:Q}),N.add({x:B.x+w,y:B.y-f}),r&&N.reverse(),A(N,L)},!0),L("extend.south2",function(t,E,x){var y=t.getSourceAgent(),f=t.getTargetAgent(),b=y.getId()>f.getId(),B=b?f:y,G=b?y:f,M=B.p(),H=G.p(),N=h(x,t,B,w),$=h(x,t,G,w,!0),e=t.s(R),L=e?0:q(x,B)/2,l=e?0:q(x,G)/2,a=N.offset,i=$.offset,d=t.s(T)+(N.size-1)/2*t.s(u),P=k(M.y+L,H.y+l)+d+(M.x>H.x?a:-a),C=new J;return C.add({x:M.x+a,y:M.y+L}),C.add({x:M.x+a,y:P}),C.add({x:H.x+i,y:P}),C.add({x:H.x+i,y:H.y+l}),b&&C.reverse(),A(C,t)},!0),L("extend.west2",function(L,m,i){var P=L.getSourceAgent(),S=L.getTargetAgent(),H=P.getId()>S.getId(),Q=H?S:P,g=H?P:S,E=Q.p(),t=g.p(),w=h(i,L,Q,o),n=h(i,L,g,o,!0),p=L.s(R),D=p?0:V(i,Q)/2,U=p?0:V(i,g)/2,d=w.offset,W=n.offset,z=L.s(T)+(w.size-1)/2*L.s(u),$=O(E.x-D,t.x-U)-z+(E.y<t.y?d:-d),C=new J;return C.add({x:E.x-D,y:E.y+d}),C.add({x:$,y:E.y+d}),C.add({x:$,y:t.y+W}),C.add({x:t.x-U,y:t.y+W}),H&&C.reverse(),A(C,L)},!0),L("extend.east2",function(v,O,d){var S=v.getSourceAgent(),$=v.getTargetAgent(),D=S.getId()>$.getId(),z=D?$:S,g=D?S:$,B=z.p(),Z=g.p(),_=h(d,v,z,o),n=h(d,v,g,o,!0),w=v.s(R),f=w?0:V(d,z)/2,Q=w?0:V(d,g)/2,b=_.offset,K=n.offset,E=v.s(T)+(_.size-1)/2*v.s(u),G=k(B.x+f,Z.x+Q)+E+(B.y>Z.y?b:-b),i=new J;return i.add({x:B.x+f,y:B.y+b}),i.add({x:G,y:B.y+b}),i.add({x:G,y:Z.y+K}),i.add({x:Z.x+Q,y:Z.y+K}),D&&i.reverse(),A(i,v)},!0),L("v.h2",function(S,Z,K){return F(S,K,W)},!0),L("h.v2",function(o,H,Z){return F(o,Z,$)},!0)}("undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:(0,eval)("this"),Object);