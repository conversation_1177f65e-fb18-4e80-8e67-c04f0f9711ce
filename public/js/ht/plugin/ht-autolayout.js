!function(H,G,u){"use strict";var $="ht",v=$+".layout.",c=H[$]||module.parent.exports.ht,x=null,n=c.Default,I=n.def,J=c.List,Q=c.Node,L=c.Edge,X=c.Group,p=Math,j=p.floor,E=p.ceil,P=p.sqrt,r=p.max,B=p.min,s=p.abs,l=p.cos,T=p.sin,M=p.PI,W=2*M,C=M/2,z=p.atan,m=p.atan2,Z=p.random,O=p.pow,d=Number.MAX_VALUE,R=Number.MIN_VALUE,F="circular",Y="symmetric",o="hierarchical",i="towardnorth",t="towardsouth",D="towardeast",U="towardwest",a=function(){throw"Oops!"},q=function(C){return F===C?new rj:Y===C?new xj:o===C?new Yl:C===i||C===t||C===D||C===U?new $f:x},k=function(p){return p===U?new f(C):p===D?new f(-C):p===i?new f(M):x},f=function(E){this.s=T(E),this.c=l(E)};f.prototype.tf=function(n,h){return 1===arguments.length&&(h=n.y,n=n.x),{x:this.c*n-this.s*h,y:this.s*n+this.c*h}};var S=function(u,y){this.x=u,this.y=y};I(S,G,{equals:function(y){return this===y?!0:y instanceof S?y.x===this.x&&y.y===this.y:!1}});var V=function(z,V){this.width=z,this.height=V};I(V,G,{});var K=function(G,u){this.x=G,this.y=u};I(K,G,{});var g=function(){var m=arguments;2===m.length?(g.superClass.constructor.call(this,m[1].width,m[1].height),this.x=m[0].x,this.y=m[0].y):(g.superClass.constructor.call(this,m[2],m[3]),this.x=m[0],this.y=m[1])};I(g,V,{});var N=function(U,O){if(N.a2(U.x,O.x))this._a=1,this._b=0,this._c=-U.x;else{this._b=-1;var n=(O.y-U.y)/(O.x-U.x),o=U.y-U.x*n;this._a=n,this._c=o}};I(N,G,{a3:function(){return this._a},a4:function(){return this._b},a5:function(){return this._c}}),N.a6=function(I,_){if(N.a1(I.a3())&&N.a1(_.a3()))return x;if(N.a1(I.a4())&&N.a1(_.a4()))return x;if(N.a1(_.a4())){var K=I;I=_,_=K}var b,X,f=I.a3(),Y=I.a4(),a=-I.a5();N.a1(I.a3())?(b=_.a4(),X=-_.a5()):(b=_.a4()-_.a3()/I.a3()*I.a4(),X=-_.a5()-_.a3()/I.a3()*-I.a5());var p=X/b,Z=(a-p*Y)/f;return new S(Z,p)},N.a1=function(i){return N.a2(i,0)},N.a2=function(y,_){return s(y-_)<1e-5};var w=function(L){if(this._a=new em,L)for(var v=0;v<L.size();v++)this._a.aa(L.get(v))};I(w,G,{c:function(){return this._a.ah()},d:function(){return this._a.ah()},a:function(){for(var N=new J,B=this.c();B.i1();B.i2())N.add(B.i6(),0);return new w(N)},b:function(){return this._a.ay()}});var e=function(I,P){this.x=I,this.y=P};I(e,G,{a:function($,U){this.x=$,this.y=U}});var A=function(E,x){this.x=E||0,this.y=x||0};I(A,G,{b:function(){return new A(this.x,this.y)},a:function(t){this.z=t},c:function(){return this.x},d:function(){return this.y},f:function(z,s){this.x=z,this.y=s}});var y=function(U){this._c=new em,U?(this.ac(U.a8().b()),this.ad(U.a9().b())):(this.ac(new A),this.ad(new A))};I(y,G,{a6:function(){return this.a5(this)},ac:function(T){T.a(this),this._a=T},ad:function(B){B.a(this),this._b=B},a8:function(){return this._a},a9:function(){return this._b},a1:function(E,p){return this.a4(E,p,this.aa())},a2:function(){return this._c.ay()},a7:function(S){return this._c.ak(S)},aa:function(){return 0===this._c.ay()?x:this._c.as()},a3:function(){this._c.af()},i2:function(Y){var u=this.a7(Y);return u?new S(u.x,u.y):x},i1:function(){return this.a2()},i6:function(){var f=this.a8();return new S(f.c(),f.d())},i7:function(){var u=this.a9();return new S(u.c(),u.d())},i8:function(o){this.a8().f(o.x,o.y)},i9:function(S){this.a9().f(S.x,S.y)},i3:function(h,P,s){var L=this.a7(h);L&&L.a(P,s)},i4:function(W,T){this.a1(W,T)},i5:function(){this.a3()}});var h=function(w){h.superClass.constructor.call(this,w)};I(h,y,{a5:function(s){return new h(s)},a4:function(H,I,W){var m=new e(H,I);return this.ab(m,W),m},ab:function(V,Y){this._c.an(V,this._c.al(Y))}});var b=function(){if(2===arguments.length){var M=arguments[0],A=arguments[1];this._s=!1,this._w=30,this._h=30,this._x=M-this._w/2,this._y=A-this._h/2}else{var w=arguments[0];this._s=w._s,this._w=w._w,this._h=w._h,this._x=w._x,this._y=w._y}};I(b,G,{m3:function(){return this.m2(this)},m4:function(){return this._x+this._w/2},m5:function(){return this._y+this._h/2},m6:function(x,J){this._x=x-this._w/2,this._y=J-this._h/2},i1:function(){return this._x},i2:function(){return this._y},i5:function(K,T){this._x=K,this._y=T},i3:function(){return this._w},i4:function(){return this._h},i6:function(F,h){var D=(this._w-F)/2,O=(this._h-h)/2;this._x+=D,this._y+=O,this._w=F,this._h=h},m1:function(i){var K,x,A,q;i.width<=0?(K=this._x,x=this._x+this._w,A=this._y,q=this._y+this._h):(K=B(this._x,i.x),x=r(this._x+this._w,i.x+i.width),A=B(this._y,i.y),q=r(this._y+this._h,i.y+i.height)),i.x=K,i.y=A,i.width=x-K,i.height=q-A}});var _=function(t){t?_.superClass.constructor.call(this,t):_.superClass.constructor.call(this,0,0)};I(_,b,{m2:function(R){return new _(R)}});var nh={a2:function($){var X=xg.a2(hn.a($.xa()));return nh.a4($,X,nh.a3($,X))},a3:function(w,l){for(var q=w.x9();q.i1();q.i2())l.i7(q.i9(),-1);for(var F=0,d=new Cc(w.xa()),h=w.x9();h.i1();h.i2()){var G=h.i9();-1===l.i2(G)&&nh.a(G,d,l,F++)}return F},a6:function(C){for(var h=new Xq,Q=nh.a2(C),N=0;N<Q.length-1;N++){var y=C.xo(Q[N].x2(),Q[N+1].x3());h.aa(y)}return h},a4:function(L,E,n){for(var R=[],d=0,D=L.x9();n>d;d++)R[d]=new Ri;for(;D.i1();D.i2())R[E.i2(D.i9())].ae(D.i9());return R},a:function(A,M,L,D){for(M.c(A),L.i7(A,D);!M.a();){A=M.b();for(var a=A.ag();a;a=a.a8()){var U=a.a3();-1===L.i2(U)&&(L.i7(U,D),M.c(U))}for(var S=A.ae();S;S=S.a7()){var w=S.a2();-1===L.i2(w)&&(L.i7(w,D),M.c(w))}}},a1:function(Q,v,K){var u=new Dn(v,K);return u.a8(Q),u._i},a5:function(x,Z,H){for(var L=[],D=0;H>D;D++)L[D]=new Xq;for(var o=x.xf();o.i1();o.i2())L[Z.i2(o.i8())].aa(o.i8());return L},a7:function(i){var t=new Xq,g=xg.a3(hn.b(i.xa())),S=xg.a4(hn.a(i.xh())),y=nh.a1(i,S,g),q=nh.a5(i,S,y);if(q.length>1){for(var N=new Ri,C=0;C<q.length;C++){var z=q[C],V=x;if(1===z.ay()){var M=z.c2();1===M.a2().ad()?V=M.a2():1===M.a3().ad()&&(V=M.a3())}else{for(var R=z.c1();R.i1();R.i2()){var F=R.i8();if(g.i4(F.a2()))if(V){if(V!==F.a2()){V=x;break}}else V=F.a2();if(g.i4(F.a3()))if(V){if(V!==F.a3()){V=x;break}}else V=F.a3()}if(V){var $=z.c2();V=$.a2()!==V?$.a2():$.a3()}}V&&N.aa(V)}for(var b,w=N.x4();!N.ar();w=b)b=N.x4(),t.ac(i.xo(w,b))}return t}},Sm=function(){this._c=0,this._d=0,this._e=0,this._b=!0,this._f=!1};I(Sm,G,{a6:function(U){this._f=U},a7:function(G){this._b=G},a8:function(O){0!==O.x0()&&this.a9(O,O.x9().i9())},a9:function(j,K){if(this._xx=j.xk(),this._c=j.xl(),this._d=0,this._e=0,this.a0(K),this._b)for(var R=j.x9();R.i1();R.i2()){var l=R.i9();this._xx.i1(l)||(this.a1(l),this.a0(l))}j.xi(this._xx),j.xj(this._c)},a0:function(q){var X=++this._d;this._xx.z1(q,Sm._B),this.a5(q,X);for(var g=this._f?q.ap():q.af();g.i1();g.i2()){var R=g.i8();if(!this._c.i4(R)){this._c.i7(R,!0);var V=R.a1(q);this._xx.i1(V)?this.a3(R,V,!1):(this.a3(R,V,!0),this.a0(V),this.a2(R,V))}}this.a4(q,X,++this._e),this._xx.z1(q,Sm._C)},a5:function(){},a4:function(){},a3:function(){},a2:function(){},a1:function(){}}),Sm._B={},Sm._C={};var Cb=function(l){this._a=l};I(Cb,Sm,{a5:function(V){var w=this._a._ah.i2(V);this._a._ad[w].ae(V)}});var Ml=function(A){this._a=A};I(Ml,Sm,{a2:function(d,F){var $=d.a1(F),e=this._a[$.al()],Z=this._a[F.al()];Z._a+1>e._a?(e._c=e._a,e._b=e._d,e._a=Z._a+1,e._d=d):Z._a+1>e._c&&(e._c=Z._a+1,e._b=d)}});var Cm=function(F){this._a=F};I(Cm,Sm,{a3:function(v,f,s){s&&v.a2()===f&&this._a.ac(v)}});var is=function(){this._a=0,this._c=0};I(is,G,{});var ph=function(){this._a=0};I(ph,G,{a1:function(T,c){this._a=0;for(var e=c.length-1;e>=0;e--)c[e]=-1;for(var x=T.x9();x.i1();x.i2()){var Y=x.i9();if(0===Y.ak()){this.a2(Y,Y.al(),c);break}}for(var J=T.x9();J.i1();J.i2()){var f=J.i9(),r=f.al();-1===c[r]&&this.a2(f,r,c)}},a2:function($,k,h){h[k]=-2;for(var R=$.ag();R;){var x=R.a3(),n=x.al();switch(h[n]){case-1:this.a2(x,n,h);case-2:default:R=R.a8()}}h[k]=this._a++}});var oe={a1:function(u){var I=new ac;return I.a8(u),I._n},a2:function(A){var d,O=A.x9(),E=0;for(O.i4();O.i1();O.i2())0===O.i9().ak()&&(d=O.i9(),E++);if(1===E)return d;for(E=0,O.i4();O.i1();O.i2())0===O.i9().ao()&&(d=O.i9(),E++);return 1===E?d:oe.a8(A)},a8:function(L){var V=hn.a(L.x0()),f=xg.a2(V);return oe.a6(L,f)},a6:function(P,N){var L=P.xd(),o=hn.d(1),q=hn.a(P.x0(),-1),l=oe.a4(P,L);oe.a7(L,N,o,q,-1);for(var V=l.c1();V.i1();V.i2())P.x3(V.i8());return o[0]},a7:function(F,_,e,v,M){for(var k=0,$=F.ag();$;$=$.a8()){var K=$.a3(),p=oe.a7(K,_,e,v,M);p>M&&(M=p),k+=v[K.al()]}for(var I=k*(F._g.xa()-1-k),n=F.ag();n;n=n.a8())for(var O=n.a3(),i=n.a8();i;i=i.a8()){var z=i.a3();I+=v[O.al()]*v[z.al()]}return _.i7(F,I),v[F.al()]=k+1,I>M&&(M=I,e[0]=F),M},a4:function(K,r){var w=new Xq,X=new Cm(w);X.a6(!1),X.a9(K,r);for(var o=w.c1();o.i1();o.i2())K.x3(o.i8());return w},a3:function(R){return oe.a4(R,oe.a2(R))}},ac=function(){this._n=!0,this.a6(!1)};I(ac,Sm,{a3:function(g,M,Q){Q||(this._n=!1)},a1:function(){this._n=!1}});var Dn=function(w,W){this._i=0,this._m=W,this._j=w,this._l=!1};I(Dn,Sm,{a8:function(x){this._h=hn.a(x.x0()),this._k=hn.a(x.x0()),this._g=new Cc(x.xh()),Dn.superClass.a8.call(this,x)},a5:function(H,u){this._k[H.al()]=this._h[H.al()]=u},a3:function(L,X,K){if(this._g.c(L),!K){var x=L.a1(X);this._h[x.al()]=B(this._h[x.al()],this._k[X.al()])}},a1:function(){this._l=!1},a2:function(R,r){var E=R.a1(r);if(this._h[r.al()]>=this._k[E.al()]){for(;this._g.d()!==R;this._j.i5(this._g.b(),this._i));this._j.i5(this._g.b(),this._i),this._i++,this._g.a()?this._l?this._m.i5(E,!0):this._l=!0:this._m.i5(E,!0)}this._h[E.al()]=B(this._h[E.al()],this._h[r.al()])}});var Og=function(S,E){this._h=!1,this._i=S,this._g=E};I(Og,G,{z1:function(o,r){o._c[this._i]=r},i1:function(w){return w._c[this._i]},i5:function(r,G){r._c[this._i]=G},i4:function(s){return s._c[this._i]},i7:function(e,l){e._c[this._i]=l},i2:function(B){var W=B._c[this._i];return W?W:0},i6:function(m,o){m._c[this._i]=o},i3:function(P){var X=P._c[this._i];return X?X:0},c:function(){return this._h},d:function(){this._h=!0}});var ye=function(S,D){this._c=!1,this._d=S,this._b=D};I(ye,G,{i8:function(f,a){f._c[this._d]=a},i1:function(T){return T._c[this._d]},i7:function(e,j){e._c[this._d]=j},i4:function(b){var z=b._c[this._d];return z?z:!1},i5:function(G,n){G._c[this._d]=n},i2:function(C){var v=C._c[this._d];return v?v:0},i6:function(b,U){b._c[this._d]=U},i3:function(u){var e=u._c[this._d];return e?e:0},a:function(){return this._c},b:function(){this._c=!0}});var uh=function(X){this._bb=X,this.i4()};I(uh,G,{i1:function(){return this._aa!=x},i2:function(){this._aa=this._aa._a},i3:function(){this._aa=this._aa._b},i4:function(){this._aa=this._bb._b},i5:function(){this._aa=this._bb._c},i7:function(){return this._bb.ay()},i6:function(){return this._aa._c}});var vq=function(C){vq.superClass.constructor.call(this,C)};I(vq,uh,{i8:function(){return this.i6()}});var em=function(F){if(this._id=em.id(),this._a=0,F)for(F.i4();F.i1();F.i2())this.ae(F.i6())};I(em,G,{ac:function(I){var q=this.ag(I);return this._b?(this._b._b=q,q._a=this._b,this._b=q):this._b=this._c=q,this._a++,q},ae:function(R){var M=this.ag(R);return this._c?(this._c._a=M,M._b=this._c,this._c=M):this._b=this._c=M,this._a++,M},z1:function(h){h._b=x,h._a=x,this._c?(this._c._a=h,h._b=this._c,this._c=h):this._b=this._c=h,this._a++},ad:function(Q){Q._b=x,Q._a=x,this._b?(this._b._b=Q,Q._a=this._b,this._b=Q):this._b=this._c=Q,this._a++},aa:function(j){return this.ae(j),!0},ab:function(Y){for(;Y.i1();Y.i2())this.ae(Y.i6())},ao:function(I,C){if(C===this._b)return this.ac(I);if(C){var _=this.ag(I);return this.aq(_,C),_}return this.ae(I)},aq:function(V,p){if(p)if(p===this._b)this.ad(V);else{if(this._c){var Z=p._b;p._b=V,V._a=p,Z._a=V,V._b=Z}else V._b=x,V._a=x,this._b=this._c=V;this._a++}else this.ad(V)},ap:function(z,L){if(L)if(L===this._c)this.z1(z);else{if(this._b){var Z=L._a;L._a=z,z._a=Z,Z._b=z,z._b=L}else z._b=x,z._a=x,this._b=this._c=z;this._a++}else this.z1(z)},an:function(D,A){if(A===this._c)return this.ae(D);if(A){var Q=this.ag(D);return this.ap(Q,A),Q}return this.ac(D)},ay:function(){return this._a},ar:function(){return 0===this._a},af:function(){this._b=this._c=x,this._a=0},am:function(){return this._b._c},at:function(){var m=this.am();return this.aw(this._b),m},as:function(){return this._c._c},au:function(){return this.aw(this._c)},ak:function(D){for(var J=0,i=this._b;i;){if(D===J)return i._c;i=i._a,J++}return x},aj:function(L){return L._a?L._a:this._b},ai:function(y){return y._b?y._b:this._c},aw:function(A){return A!==this._b?A._b._a=A._a:this._b=A._a,A!==this._c?A._a._b=A._b:this._c=A._b,this._a--,A._c},av:function(Q){return this.aw(Q._aa)},ah:function(){return new uh(this)},al:function(q){for(var y=this._b;y;){if(!y._c&&!q)return y;if(y._c===q)return y;y=y._a}return x},a0:function(){for(var v=hn.d(this._a),j=0,G=this._b;G;)v[j]=G._c,G=G._a,j++;return v},ax:function(){for(var K=this._b;K;K=K._b){var J=K._a;K._a=K._b,K._b=J}var V=this._b;this._b=this._c,this._c=V},a1:function(s){var h=this.a0(),v=0;h.sort(s);for(var A=this._b;A;)A._c=h[v],A=A._a,v++},a2:function(){var $=this.a0();$.sort(hn.c);for(var H=0,M=this._b;M;)M._c=$[H],M=M._a,H++},az:function(S){this._b?S._b&&(this._c._a=S._b,S._b._b=this._c,this._c=S._c):(this._b=S._b,this._c=S._c),this._a+=S._a,S._b=S._c=x,S._a=0},ag:function(r){return new Ni(r)}}),em.id=function(){var q=0;return function(){return++q}}();var Xq=function(I){Xq.superClass.constructor.call(this,I)};I(Xq,em,{c1:function(){return new vq(this)},c2:function(){return this.am()},c3:function(){return this.at()}});var pl=function(){this._c=0};I(pl,G,{a:function($){this._c++,$._b=this._b,$._a=x,this._b?(this._b._a=$,this._b=$):this._b=this._a=$},b:function(C,l){if(!l)return this.a(C),void 0;var S=l._b;S?S._a=C:this._a=C,C._b=S,C._a=l,l._b=C,this._c++},c:function(S){var Y=S._a,b=S._b;this._c--,Y?Y._b=b:this._b=b,b?b._a=Y:this._a=Y}});var Cl=function(W,Q){this._p=W,this._j=Q,this._o=W._o[Q]};I(Cl,G,{i1:function(){return this._o!=x},i2:function(){this._o=this._o._k[this._j]},i3:function(){this._o=this._o._f[this._j]},i4:function(){this._o=this._p._o[this._j]},i5:function(){this._o=this._p._q[this._j]},i7:function(){return this._p._n[this._j]},i6:function(){return this._o},i8:function(){return this._o}});var Ni=function(G){this._c=G};I(Ni,G,{a:function(){return this._a},b:function(){return this._b},c:function(v){this._c=v},d:function(){return this._c}});var Ln=function(m,q,T,Z){this._r=m,this._s=q,this._q=T,this._p=Z};I(Ln,G,{i1:function(m){return this._p[m.a5()]},i3:function(x){return this._r[x.a5()]},i2:function(P){return this._s[P.a5()]},i4:function(E){return this._q[E.a5()]},i8:function(k,I){this._p[k.a5()]=I},i6:function(m,K){this._r[m.a5()]=K},i5:function(m,V){this._s[m.a5()]=V},i7:function(g,Q){this._q[g.a5()]=Q}});var Cc=function(I){this._a=hn.d(I),this._b=-1};I(Cc,G,{d:function(){return this._a[this._b]},b:function(){return this._a[this._b--]},c:function(j){this._a[++this._b]=j},a:function(){return this._b<0}});var os=function(){};I(os,G,{a0:function(F){this._c=hn.d(F)}});var Zr=function(D,i,K,J,v,G,E){this._g=0,D.xt(this,i,K,J,v,G,E)};I(Zr,os,{a5:function(){return this._h._u&&this._h.b1(),this._g},a2:function(){return this._d},a3:function(){return this._e},a1:function(q){return this._d!==q?this._d:this._e},a4:function(){for(var i=0;1>=i;i++)this._k[i]=x,this._f[i]=x},a8:function(){return this._k[0]},a7:function(){return this._k[1]},a6:function(m,l,n,y){this.a0(y),this._h=m,this._k=hn.d(2),this._f=hn.d(2),this._d=l,this._e=n}});var pk=function(v){this._j=0,this._h=v,this.i4()};I(pk,G,{i2:function(){this._k=this._k._k[this._j],this._k||0!==this._j||(this._k=this._h._o[1],this._j=1)},i3:function(){this._k=this._k._f[this._j],this._k||1!==this._j||(this._k=this._h._q[0],this._j=0)},i4:function(){this._k=this._h._o[0],this._k?this._j=0:(this._k=this._h._o[1],this._j=1)},i5:function(){this._k=this._h._q[1],this._k?this._j=1:(this._k=this._h._q[0],this._j=0)},i1:function(){return!!this._k},i6:function(){return this._k},i8:function(){return this._k},i7:function(){return this._h.ad()}});var $m=function(){this._a=Hg._A,this._b=Hg._A,this._c=new J};I($m,G,{i1:function(){return this._c.size()},i2:function(s){return this._c.get(s)},i3:function(K,w,o){this._c.set(K,new S(w,o))},i4:function(X,H){this._c.add(new S(X,H))},i5:function(){this._c.clear()},i6:function(){return this._a},i7:function(){return this._b},i8:function(N){this._a=N},i9:function(E){this._b=E}});var wn=function(){this._x=0,this._y=0,this._w=0,this._h=0};I(wn,G,{i5:function(q,Y){this._x=q,this._y=Y},i6:function(x,w){this._w=x,this._h=w},i4:function(){return this._h},i3:function(){return this._w},i1:function(){return this._x},i2:function(){return this._y}});var Fi=function(r,h,q,V){this._m=r,this._n=h,this._l=q,this._k=V};I(Fi,G,{i1:function(V){return this._k[V.al()]},i3:function(G){return this._m[G.al()]},i2:function(C){return this._n[C.al()]},i4:function(i){return this._l[i.al()]},z1:function(z,I){this._k[z.al()]=I},i6:function(o,X){this._m[o.al()]=X},i7:function($,U){this._n[$.al()]=U},i5:function(M,L){this._l[M.al()]=L}});var Ff=function(L,P){this._b=L,this._r=P,this._a=[];for(var u=this._b-1;u>=0;u--)this._a.push(u);this._c=new J};I(Ff,G,{a1:function(K){var _;if(0===this._a.length){this.a2(K,this._b,this._b+this._r);for(var T=this._b+this._r-1;T>this._b;T--)this._a.push(T);_=this._b,this._b+=this._r}else _=this._a.pop();return _},b:function(w){var n=this.a1(w),E=new Og(n,this);return this._c.add(E),this.a4(w,n),E},c:function(j){var M=this.a1(j),d=new ye(M,this);return this._c.add(d),this.a4(j,M),d},a2:function(m,j,u){for(var M=m._a;M;M=M._a){var F=hn.d(u);hn.f(M._c,F,j),M._c=F}},a3:function(s,r,i){var R=hn.d(i);hn.f(s._c,R,r),s._c=R},a4:function(r,U){for(var T=r._a;T;T=T._a)T._c[U]=x},a5:function(h,K){if(h instanceof Og){var Y=h;if(Y.c())throw"";Y.d();var T=h._i;this._a.indexOf(T)<0&&(this.a4(K,T),this._a.push(T),this._c.remove(h))}},a6:function(g,d){if(g instanceof ye){var n=g;if(n.a())throw"";n.b();var C=n._d;this._a.indexOf(C)<0&&(this.a4(d,C),this._a.push(C),this._c.remove(g))}}});var rf=function(K){this._id=em.id(),this._p=0,K.xs(this)};I(rf,os,{ad:function(){return this._n[0]+this._n[1]},ak:function(){return this._n[1]},ao:function(){return this._n[0]},al:function(){return this._g._y&&this._g.c(),this._p},ag:function(){return this._o[0]},ae:function(){return this._o[1]},af:function(){return new pk(this)},am:function(){return new Cl(this,1)},ap:function(){return new Cl(this,0)},an:function(){return new Hk(this)},aq:function(){return new ig(this,1)},aw:function(){return new ig(this,0)},ah:function(y){for(var C=this._o[0];C;C=C._k[0])if(C.a3()===y)return C;return x},ai:function(M){for(var Z=this._o[1];Z;Z=Z._k[1])if(Z.a2()===M)return Z;return x},aj:function(k){var U=this.ah(k);return U||(U=this.ai(k)),U},au:function(i){this.at(i,1,hn.d(this.ak()))},av:function(N){this.at(N,0,hn.d(this.ao()))},as:function(f,Q){this.a0(Q),this._g=f,this._o=hn.d(2),this._q=hn.d(2),this._n=hn.a(2)},ab:function(s,d,K,e,N){if(!d)return this.aa(s,K,e),void 0;var X;if(X=d._d===d._e?e:this!==d._d?1:0,0===N){var A=d._k[X];s._f[e]=d,s._k[e]=A,d._k[X]=s,A?A._d===A._e?A._f[e]=s:A._f[this!==A._d?1:0]=s:this._q[K]=s}else{var Q=d._f[X];s._k[e]=d,s._f[e]=Q,d._f[X]=s,Q?Q._d===Q._e?Q._k[e]=s:Q._k[this!==Q._d?1:0]=s:this._o[K]=s}this._n[K]++},aa:function(e,f,k){var F=this._q[f];e._k[k]=x,F?(e._f[k]=F,F._d===F._e?F._k[k]=e:F._k[this!==F._d?1:0]=e):(this._o[f]=e,e._f[k]=x),this._q[f]=e,this._n[f]++},ar:function(C,I,K){var f,b;f=C._k[K],b=C._f[K],f?f._f[f._d!==this?1:0]=b:this._q[I]=b,b?b._k[b._d!==this?1:0]=f:this._o[I]=f,this._n[I]--},ac:function(){for(var q=0;1>=q;q++)this._o[q]=x,this._q[q]=x,this._n[q]=0},at:function(k,V,K){if(!(this._n[V]<2)){for(var P=this._n[V],s=0,h=this._o[V];h;h=h._k[V])K[s]=h,s++;hn.s(K,P,k);var W=1,A=this._o[V]=K[0];for(A._f[V]=x;P>W;)h=K[W],h._f[V]=A,A._k[V]=h,W++,A=h;this._q[V]=h,h._k[V]=x}}});var ig=function(c,v){ig.superClass.constructor.call(this,c,v),this._h=1!==v?1:0};I(ig,Cl,{i6:function(){return this.i9()},i9:function(){return 0!==this._h?this._o._e:this._o._d}});var Hk=function(V){Hk.superClass.constructor.call(this,V)};I(Hk,pk,{i6:function(){return this._k.a1(this._h)},i9:function(){return this._k.a1(this._h)}});var Oe=function(l){Oe.superClass.constructor.call(this,l)};I(Oe,uh,{i9:function(){return this.i6()}});var Ip=function(n){this._o=n,this._c=n._a};I(Ip,G,{i1:function(){return this._c!=x},i2:function(){this._c=this._c._a},i3:function(){this._c=this._c._b},i5:function(){this._c=this._o._b},i4:function(){this._c=this._o._a},i7:function(){return this._o._c},i6:function(){return this._c},i9:function(){return this._c},i8:function(){return this._c}});var Ri=function(_){if(_&&_.length){Ri.superClass.constructor.call(this);for(var I=0;I<_.length;I++)this.ae(_[I])}else Ri.superClass.constructor.call(this,_)};I(Ri,em,{x1:function(){return new Oe(this)},x2:function(){return this.am()},x3:function(){return this.as()},x4:function(){return this.at()}});var Cp=function(r){this._d=r,Cp.superClass.constructor.call(this)};I(Cp,Ri,{});var _h=function(N){this._a=N,this._b=new Xq,this._c=new Ri};I(_h,G,{a:function(){for(var Q=this._a.x9();Q.i1();Q.i2())this.e(Q.i9())},b:function(){this.c(),this.d()},c:function(){for(;!this._c.ar();){var J=this._c.x4();this._a.xq(J)||this.g(J)}},d:function(){for(;!this._b.ar();){var L=this._b.c3();this._a.xp(L)||this.f(L)}},e:function($){for(var k=$.af();k.i1();k.i2())this._b.ac(k.i8()),this._a.h1(k.i8());this._c.ac($),this._a.h2($)},f:function(d){this._a.u1(d)},g:function(Q){this._a.h3(Q)}}),_h.h=function(k,n){for(n.i4();n.i1();n.i2()){var W=n.i8();k.xq(W.a2())||k.h3(W.a2()),k.xq(W.a3())||k.h3(W.a3()),k.xp(W)||k.u1(W)}},_h.i=function(p,d){for(d.i4();d.i1();d.i2()){var z=d.i8();p.xp(z)&&p.h1(z),0===z.a2().ad()&&p.h2(z.a2()),0===z.a3().ad()&&p.h2(z.a3())}};var Yn=function(){var J=arguments;this._g=J[0],this._f=this._g.xk(),this._h=this._g.xk(),this._d=new em,this._e=0,1!==J.length&&this.a(J[1],J[2],J[3],J[4])};I(Yn,G,{a:function(y,L,d,U){for(var Z=hn.d(d-L+1),r=L,n=0;d>=r;r++)Z[r]=new Cp(r);for(var G=this._g.x9();G.i1();G.i2()){var O=G.i9();(!U||U.i4(O))&&(this._f.z1(O,Z[y.i2(O)-L].ac(O)),this._e++)}for(;n<Z.length;n++)for(var p=Z[n],K=this._d.ae(p),k=p.x1();k.i1();k.i2())this._h.z1(k.i9(),K)},c:function(){this._g.xi(this._h),this._g.xi(this._f)},e:function(){return 0===this._e},g:function(){for(;this._d.am().ar();this._d.at());this._e--;var I=this._d.am().x4();return this._h.z1(I,x),this._f.z1(I,x),I},f:function(){for(;this._d.as().ar();this._d.au());this._e--;var k=this._d.as().x4();return this._h.z1(k,x),this._f.z1(k,x),k},d:function(q){var e=this._f.i1(q),G=this._h.i1(q),t=G.d(),U=x,l=G.a();l?(U=l.d(),this._h.z1(q,l)):(U=new Cp(t._d+1),this._h.z1(q,this._d.ae(U))),t.aw(e),this._f.z1(q,U.ac(q))},b:function(H){var h=this._f.i1(H),o=this._h.i1(H),M=o.d(),Q=x,N=o.b();N?(Q=N.d(),this._h.z1(H,N)):(Q=new Cp(M._d-1),this._h.z1(H,this._d.ac(Q))),M.aw(h),this._f.z1(H,Q.ac(H))}});var xg={a1:function(Q){return new Fi(Q,x,x,x)},a2:function(m){return new Fi(x,m,x,x)},a3:function(h){return new Fi(x,x,h,x)},a4:function(S){return new Ln(x,S,x,x)},a5:function(m){return new Ln(x,x,m,x)},a6:function(J){return new Ln(x,x,x,J)}},Bm=function(){var S=arguments;if(2===S.length){this._a=new em,this._b=new em,this._c=0;var H=S[0],x=S[1],T=new Lm(H._j2.gj(x)/2,0);this._a.ac(T),T=new Lm(H._j2.gj(x)/2,0),this._b.ac(T)}else this._a=S[1],this._b=S[2],this._c=S[3]};I(Bm,G,{});var Lm=function(j,m){this._b=j,this._a=m};I(Lm,G,{});var mo=function(){this._cx=!0,this._cs=new Bd,this._ct=new Ed,this._cw=new Gd};I(mo,G,{i5:function(l){this._cx=l},k:function(){var l=new Ib(this);return this._cx&&(this._cs.w1(l),l=this._cs),this._cw.w1(l),l=this._cw,this._ct.w1(l),l=this._ct},i2:function(v){this.k().i2(v)},i1:function(H){return this.k().i1(H)}});var $f=function(){$f.superClass.constructor.call(this),this._jv=20,this._jw=40,this._jx=function(E,w){var C=E.a3(),v=w.a3(),Y=C._g;return j(100*(Y.g5(C)-Y.g5(v)))}};I($f,mo,{i4:function(b){return oe.a1(b)},i3:function(O){if(!this.i4(O))throw"";var L=oe.a3(O);if(this._j2=O,this._j3=new dd(O),Cq.c(O),this._jy=O.xk(),!O.xb()){this.bu();var q=this._j3.c1();this.f(q),this.b(this._j3),this.c(this._j3)}for(var b;!L.ar();O.x3(b))b=L.c3(),Cq.b(O.g2(b))},bu:function(){if(this._jx)for(var b=this._j2.x9();b.i1();b.i2())b.i9().av(this._jx)},c:function($){for(var m=this.a2($),C=hn.a(m.length),U=0;U<m.length;U++){for(var s=m[U],T=0,o=s.ah();o.i1();o.i2()){var y=o.i6();T=r(T,this._j2.g9(y))}C[U]=T}for(var u=-this._jw,J=0;J<m.length;J++){u+=this._jw+C[J];for(var D=m[J],b=D.ah();b.i1();b.i2()){var j=b.i6();this._j2.s2(j,this._j2.g5(j),u-C[J]/2)}}},a2:function(T){for(var G=hn.d(T.b()),K=0,W=T.b();W>K;K++)G[K]=new em;return T.c1(),this.a1(T.c1(),0,G),G},a1:function(o,v,V){V[v].ae(o);for(var T=o.aw();T.i1();T.i2())this.a1(T.i9(),v+1,V)},b:function(G){var s=G.c1();this._j2.s2(s,0,this._j2.g6(s)),this.g(s)},g:function(T){for(var w=T.aw();w.i1();w.i2()){var y=w.i9(),I=this._jy.i1(y);this._j2.s2(y,this._j2.g5(T)+I._c,this._j2.g6(y)),this.g(y)}},f:function(U){if(this._j3.c2(U))return this._jy.z1(U,new Bm(this,U)),void 0;var x=U.aw(),i=x.i9();x.i2(),this.f(i);var m=this._jy.i1(i),b=new Bm(this,m._a,m._b,0);if(!x.i1())return b._a.ac(new Lm(this._j2.gj(U)/2,0)),b._b.ac(new Lm(this._j2.gj(U)/2,0)),this._jy.z1(U,b),void 0;for(;x.i1();){i=x.i9(),x.i2(),this.f(i),m=this._jy.i1(i);for(var n=b._b.ah(),k=m._a.ah(),u=2147483647,e=0,h=0;n.i1()&&k.i1();){var q=n.i6();n.i2();var N=k.i6();k.i2(),h+=q._a,e+=N._a,u=B(u,e-h-q._b-N._b)}m._c=this._jv-u,e+=m._c;var C=m._b.am();if(C._a=m._c,n.i1()&&!k.i1())for(var y=h-this.a3(m._b);n.i1();y=0){var p=n.i6();n.i2(),m._b.ae(new Lm(p._b,p._a+y))}else if(!n.i1()&&k.i1()){var E=this.a3(b._a);for(E=e-E;k.i1();E=0){var r=k.i6();k.i2(),b._a.ae(new Lm(r._b,r._a+E))}}b._b=m._b}this._jy.z1(U,b);for(var l=-m._c/2,$=U.aw();$.i1();){var c=$.i9();$.i2();var L=this._jy.i1(c);L._c+=l;var o=L._b.am();o._a+=l,o=L._a.am(),o._a+=l}b._a.ac(new Lm(this._j2.gj(U)/2,0)),b._b.ac(new Lm(this._j2.gj(U)/2,0))},a3:function(V){for(var Q=0,x=V.ah();x.i1();x.i2()){var Z=x.i6();Q+=Z._a}return Q}});var dd=function($){this._b=$,this.a()};I(dd,G,{c1:function(){return this._a||this.a(),this._a},b:function(){return this._a?this.d(this._a):-1},d:function(V){for(var u=0,i=V.aw();i.i1();i.i2())u=r(u,this.d(i.i9()));return u+1},c2:function(w){return 0===w.ao()},a:function(){for(var C=this._b.x9();C.i1();C.i2())if(0===C.i9().ak())return this._a=C.i9(),void 0}});var Vh=function(W){this._d=0,this._e=0,this._f=0,this._a=0,this._b=0,this._g=W,this._c=new em};I(Vh,G,{a:function(){return this._d+this._e+this._f}});var pc=function(){pc.superClass.constructor.call(this),this._kl=340,this._km=360,this._kk=40,this._ko=.5};I(pc,mo,{ic:function(){return this._km},ia:function(){return this._kl},i9:function(){return this._ko},i3:function(f){if(!oe.a1(f))throw"";this._a=f;var U=this.i8(),k=oe.a4(f,U);Cq.c(f),this._kn=hn.d(f.x0());for(var o=f.x9();o.i1();o.i2()){var t=o.i9();t!==U?this.aa(t,new Vh(this._kk+this.q(t.aq().i9()))):this.aa(t,new Vh(this._kk))}this.s(U),f.s2(U,0,0),this.t(U);for(var g;!k.ar();f.x3(g))g=k.c3()},i4:function(X){return oe.a1(X)},i0:function($){return this._kn[$.al()]},i8:function(){return oe.a2(this._a)},i7:function(E){for(var w,P=this.ib(E);;){if(w=this.i6(E),P>=w)break;for(var U=E.aw();U.i1();U.i2()){var s=U.i9();this.i0(s)._g*=1+this._ko}}var i=(P-w)/(2*E.ao());w=0;for(var X=E.aw();X.i1();X.i2()){var x=this.i0(X.i9());x._d+=i,x._e+=i,w+=x._d+x._e}this.id(E)},id:function(J){for(var t=hn.d(J.ao()),m=0,h=J.ap();h.i1();)t[m]=h.i8(),h.i2(),m++;var s=this;t.sort(function(U,g){var d=U.a3(),q=g.a3(),Z=s.i0(d).a()-s.i0(q).a();return Z>0?1:Z>=0?0:-1});for(var S=0;S<t.length;S++)this._a.h1(t[S]);for(var d=0;d<t.length;d+=2)this._a.u1(t[d]);for(m=t.length-1,0===m%2&&m--;m>0;m-=2)this._a.u1(t[m])},ib:function(V){return 0===V.ak()?this._km:2===V.ao()?B(180,this._kl):this._kl},i6:function(x){for(var B=0,y=x.ap();y.i1();y.i2()){for(var L,X=y.i8(),Z=X.a3(),C=this.i0(Z),u=-C._g,k=C._b,t=C._c,D=0,H=D+1,w=t._b,J=w.d();H>D;H=(L.y-k)/(L.x-u))L=J,w=t.ai(w),J=w.d(),D=(J.y-L.y)/(J.x-L.x);for(C._d=180*-z(H)/M,D=0,H=D-1,w=t._b,J=w.d();w.a().d().x===J.x;J=w.d())w=w.a();for(var f;D>H;H=(f.y-k)/(f.x-u))f=J,w=t.aj(w),J=w.d(),D=(J.y-f.y)/(J.x-f.x);C._e=180*z(H)/M,B+=C._d+C._e}return B},aa:function(i,w){this._kn[i.al()]=w},p:function(Y){var g=this.i0(Y),P=new em,y=2*this.q(Y);P.aa(new S(0,0)),P.aa(new S(0,y)),P.aa(new S(y,y)),P.aa(new S(y,0)),g._c=P,g._a=y/2,g._b=y/2},r:function(t){if(0===t.ao())this.p(t);else{var H=this.i0(t),c=this.q(t),Z=new em;Z.aa(new S(-c,-c)),Z.aa(new S(-c,c)),Z.aa(new S(c,-c)),Z.aa(new S(c,c));for(var C=t.aw();C.i1();C.i2()){var O=this.i0(C.i9());Z.az(O._c)}for(var X=Hg.h(Z),n=d,N=d,B=R,E=R,I=X.ah();I.i1();I.i2()){var g=I.i6();g.x<n&&(n=g.x),g.x>B&&(B=g.x),g.y<N&&(N=g.y),g.y>E&&(E=g.y)}for(var u=new em,Q=X.ah();Q.i1();Q.i2()){var b=Q.i6();u.aa(new S(b.x-n,b.y-N))}H._c=u,H._a=-n,H._b=-N}},s:function(h){if(0===h.ao())this.r(h);else{for(var D=h.aw();D.i1();D.i2())this.s(D.i9());this.i7(h);for(var x=0,u=h.aw();u.i1();u.i2()){var R=u.i9(),P=this.i0(R),o=180-(360-this.ib(h))/2-x-(P._e+P._f);x+=P.a(),o=o/180*M;for(var z=T(o),K=l(o),g=P._c._b;g;g=g.a()){var Z=g.d(),L=Z.x+P._g,F=Z.y-P._b,e=new S(L*K-z*F,L*z+K*F);g.c(e)}var i=P._a+P._g;P._a=i*K,P._b=i*z}this.r(h)}},t:function(P){var y=this._a.g4(P),G=0;if(P.ak()>0){var k=P.aq().i9(),b=this._a.g4(k);G=M+m(b.y-y.y,b.x-y.x)}for(var u=P.aw();u.i1();u.i2()){var f=u.i9(),v=this.i0(f);if(0!==G){var L=l(G),j=T(G),X=v._a*L-j*v._b,r=v._a*j+L*v._b;v._a=X,v._b=r}this._a.s2(f,y.x+v._a,y.y+v._b),this.t(f)}},q:function(g){return 1.41*(r(this._a.gj(g),this._a.g9(g))/2)}});var On=function(){};I(On,G,{i2:function(U){return U.ad()},i1:a,i3:a,i4:a});var Al=function(Q){this._a=Q};I(Al,G,{i2:function(w){for(var f=0,b=w.an();b.i1();b.i2())this._a.i1(b.i9())&&f++;return f},i4:function(g){return this._a.i1(g)==x},i1:a,i3:a});var bf=function(){bf.superClass.constructor.call(this),this._kq=!1,this._kp=90};I(bf,pc,{a:function(p,e){this._kr=e,this._ks=p,this._kq=!0},i7:function(Q){if(!this.u(Q))return bf.superClass.i7.call(this,Q),void 0;for(var I=this.i9(),A=this.ib(Q),H=(360-A)/2+A,u=new Xq(Q.ap());;){var $,j,T=this.i6(Q),F=u._b;for(T=(360-A)/2;F;F=F.a()){var R=F.d(),U=R.a3(),p=this.i0(U),h=this._ks.i3(R),W=h-(T+p._e);if(W>=0&&h+p._d>=H&&(W=T+p.a()<=H?H-T-p.a():2*(H-(h+p._d))),p._f=0,W>=0)p._f=W,$=F,j=p;else{for(-W>p._d+p._e?W=(p._d+p._e)/2:W/=-2,T-=W,H>=T&&T+p.a()>H&&(T+=W,W=T+p.a()-H,T-=W);$&&W>j._f;j=this.i0($.d().a3()))if(W-=j._f,j._f=0,$=$.b(),!$){j=x;break}$?j._f-=W:T+=W}T+=p.a()}if(H>=T){for(var k=0,q=(360-A)/2,g=Q.ap();g.i1();g.i2()){var _=g.i8(),t=_.a3(),E=this._ks.i3(_),d=this.i0(t),e=q+d._f+d._e;k<s(e-E)&&(k=s(e-E)),q+=d.a()}if(k<=this._kp)break}for(var c=Q.aw();c.i1();c.i2()){var P=c.i9();this.i0(P)._g*=1+I}}},ib:function(S){return this.u(S)?0===S.ak()?this.ic():this.ia():bf.superClass.ib.call(this,S)},u:function(K){return this._kq&&0!==K.ao()?this._ks.i1(K.ag())!=x:!1}});var gn=function(L){this._a=L};I(gn,G,{i1:function(z){return this._a.i1(z)},i2:a,i3:a,i4:a});var xl=function(){};I(xl,G,{w1:function(P){this._bb=P},w2:function(){return this._bb},w4:function(l){this._bb&&this._bb.i2(l)},w3:function(j){return this._bb?this._bb.i1(j):!0}});var Ed=function(){this._cg=45,this._ce=400,this._ch=400,this._cf=0};I(Ed,xl,{i1:function(u){if(this.w2()){for(var v=!0,T=u.xk(),h=nh.a3(u,T),j=hn.d(h),D=hn.d(h),R=0;h>R;R++)j[R]=new Ri,D[R]=new Xq;for(var r=u.xf();r.i1();r.i2()){var M=r.i8();D[T.i2(M.a2())].aa(M),u.h1(M)}for(var z=u.x9();z.i1();z.i2()){var q=z.i9();j[T.i2(q)].aa(q),u.h2(z.i9())}for(var o=0;h>o;o++){for(var n=j[o].x1();n.i1();n.i2())u.h3(n.i9());for(var F=D[o].c1();F.i1();F.i2())u.u1(F.i8());v=this.w3(u);for(var U=D[o].c1();U.i1();U.i2())u.h1(U.i8());for(var Y=j[o].x1();Y.i1();Y.i2())u.h2(Y.i9());if(!v)break}for(var x=0;h>x;x++)for(var k=j[x].x1();k.i1();k.i2())u.h3(k.i9());for(var B=0;h>B;B++)for(var s=D[B].c1();s.i1();s.i2())u.u1(s.i8());return u.xi(T),v}return!0},i2:function(J){if(!J.xb()){for(var P=J.xk(),W=nh.a3(J,P),r=hn.d(W),i=hn.d(W),U=hn.d(W),u=hn.d(W),k=0;W>k;k++)r[k]=new Ri,i[k]=new Xq;for(var L=J.xf();L.i1();L.i2()){var d=L.i8();i[P.i2(d.a2())].aa(d),J.h1(d)}for(var X=J.x9();X.i1();X.i2()){var _=X.i9();r[P.i2(_)].aa(_),J.h2(X.i9())
}for(var l=0;W>l;l++){for(var p=r[l].x1();p.i1();p.i2())J.h3(p.i9());for(var o=i[l].c1();o.i1();o.i2())J.u1(o.i8());this.w4(J);var D=J.g3();U[l]=new g(D.x,D.y,D.width,D.height);var t={};if(u[l]=t,this._cf>0){var y=this._cg+E((D.width+1)/this._cf)*this._cf,c=this._cg+E((D.height+1)/this._cf)*this._cf;t.x=D.x,t.y=D.y,t.width=y,t.height=c}else t.x=D.x,t.y=D.y,t.width=D.width+this._cg,t.height=D.height+this._cg;for(var z=i[l].c1();z.i1();z.i2())J.h1(z.i8());for(var H=r[l].x1();H.i1();H.i2())J.h2(H.i9())}for(var Z=0;W>Z;Z++)for(var n=r[Z].x1();n.i1();n.i2())J.h3(n.i9());for(var B=0;W>B;B++)for(var w=i[B].c1();w.i1();w.i2())J.u1(w.i8());if(Cq.a(u,x,this._ce/this._ch),this._cf<=0)for(var h=0;h<u.length;h++)this.w5(J,r[h],i[h],new S(u[h].x,u[h].y),U[h]);else for(var O=0;O<u.length;O++){var C=j((u[O].x-U[O].x)/this._cf)*this._cf,V=j((u[O].y-U[O].y)/this._cf)*this._cf,f=U[O].x+C,T=U[O].y+V;this.w5(J,r[O],i[O],new S(f,T),U[O])}J.xi(P)}},w5:function(R,t,z,e,h){for(var m=-h.x+e.x,u=-h.y+e.y,A=t.x1();A.i1();A.i2()){var Q=R.ga(A.i9());R.s4(A.i9(),new S(Q.x+m,Q.y+u))}for(var C=z.c1();C.i1();C.i2()){for(var N=C.i8(),Z=new J,D=R.gp(N).c();D.i1();D.i2()){var O=D.i6();Z.add(new S(O.x+m,O.y+u))}R.s5(N,new w(Z))}}});var Gd=function(){};I(Gd,xl,{i1:function(i){return this.w3(i)},i2:function(v){this.w7(v),this.w2()&&this.w4(v),this.w6(v)},w7:function(J){this.e(J),this.k(J),this.i(J)},e:function(K){for(var b=K.x9();b.i1();b.i2()){var G=K.g4(b.i9());K.s1(b.i9(),G)}},w6:function(g){this.l(g),this.j(g),this.f(g)},l:function(l){for(var N=l.x9();N.i1();N.i2()){var Y=l.g4(N.i9());l.s1(N.i9(),Y)}},j:function(E){for(var g=E.xf();g.i1();g.i2()){var A=E.g7(g.i8()),Z=A.i6();A.i8(Z),Z=A.i7(),A.i9(Z);for(var o=0;o<A.i1();o++){var w=A.i2(o);A.i3(o,w.x,w.y)}}},k:function(I){for(var G=I.xf();G.i1();G.i2()){var p=I.g7(G.i8()),i=p.i6();p.i8(i),i=p.i7(),p.i9(i);for(var F=0;F<p.i1();F++){var k=p.i2(F);p.i3(F,k.x,k.y)}}},f:function(w){this._ca&&(w.x1("A",this._ca),this._ca=x,this._b6=x),this._b8&&(w.x1("B",this._b8),this._b8=x,this._b9=x)},i:function(Q){this._ca=Q.xc("A"),this._ca&&(this._b6=new gn(this._ca),Q.x1("A",this._b6)),this._b8=Q.xc("B"),this._b8&&(this._b9=new gn(this._b8),Q.x1("B",this._b9))}});var Bd=function(){this._a=new Xq,this._c=10};I(Bd,xl,{i2:function(C){this._b=C.xl(),this.w9(C),this.w4(C),this.c(C),this.w8(C,this._b),C.xj(this._b)},i1:function(Y){if(this.w2()){this._b=Y.xl(),this.w9(Y);var X=this.w3(Y);return this.c(Y),Y.xj(this._b),X}return!0},w8:function(B,T){for(var b=B.xf();b.i1();b.i2()){var K=b.i8();T.i1(K)&&Cq.g(B,K,T.i1(K),this._c)}},w9:function(B){for(var G=B.xk(),g=B.x9();g.i1();g.i2()){for(var j=g.i9(),z=j.af();z.i1();z.i2()){var Z=z.i8(),p=Z.a1(j),F=G.i1(p);if(F!==Z)if(F){this._b.i1(F)||this._b.i8(F,new Xq);var l=this._b.i1(F);l.aa(Z),this._a.ac(Z),B.h1(Z)}else G.z1(p,Z)}for(var Y=j.af();Y.i1();Y.i2()){var Q=Y.i8(),L=Q.a1(j);G.z1(L,x)}}B.xi(G)},c:function(p){for(;!this._a.ar();p.u1(this._a.c3()));}});var Ib=function(g){this._a=g};I(Ib,G,{i2:function(K){this._a.i3(K)},i1:function(j){return this._a.i4(j)}});var xm=function(){xm.superClass.constructor.call(this),this._jo=30,this._jp=new Jl,this._jt=5};I(xm,mo,{i4:function(){return!0},i3:function(m){this._ju=m,Cq.c(m);for(var Z=this._jp.i1(m),C=0,v=m.x9();v.i1();v.i2())C=r(C,this.e(v.i9()));C<this._jt&&(C=this._jt),this.a(Z,C)},a:function(F,e){var C=F.i7(),g=2*M/C,R=0,p=hn.a(C),j=0;for(F.i4();C>j;)p[j]=this.e(F.i9())+this._jo,R+=p[j],j++,F.i2();var z=R/C,n=R/W;e>n&&(n=e),F.i4();for(var A=0,q=0;C>q;){var x=g/z*p[q];A+=x/2;var P=l(A)*n,Q=T(A)*n;A+=x/2,this._ju.s2(F.i9(),P,Q),q++,F.i2()}return n},e:function(w){var Y=this._ju.gj(w),b=this._ju.g9(w);return b>=Y?b:Y}});var rj=function(){rj.superClass.constructor.call(this),this._jm=new xm,this._jk=new bf};I(rj,mo,{i4:function(){return!0},i3:function(M){if(!(M.x0()<2)){this._jn=M,Cq.c(this._jn),Cq.e(this._jn);var F=new Sc(this._jn);F.a1(),F.h();var o=new _h(this._jn);o.a();for(var f=F.x9();f.i1();f.i2()){var l=f.i9(),B=F.c2(l);if(B.ay()>1){var K=F.d1(l);_h.h(this._jn,K.c1()),this._jm.i3(this._jn);var n=this._jn.g3();F.s7(l,n.width,n.height)}else if(1===B.ay()){var x=B.x2();F.s8(l,this._jn.gm(x)),this._jn.s2(x,0,0)}else F.s7(l,1,1);_h.i(this._jn,this._jn.xf())}o.b();var J=this.a7(F);oe.a4(F,J);var r=F.xk(),q=F.xl();this.a2(F,q,r),this.a1(F,q),this.a3(F,J,q),this._jk.a(q,r),this._jk.i3(F),this.a5(F,J,r);for(var p=F.x9();p.i1();p.i2())for(var L=p.i9(),Z=F.g4(L),y=F.c2(L).x1();y.i1();y.i2()){var i=y.i9();this._jn.s2(i,Z.x+this._jn.g5(i),Z.y+this._jn.g6(i))}}},a7:function(Y){for(var C,P=-1,x=Y.x9();x.i1();x.i2()){var X=x.i9();Y.c2(X).ay()>P&&(C=X,P=Y.c2(X).ay())}return C},a1:function(A,Y){for(var y=function(i,s){var m=Y.i3(i)-Y.i3(s);return m>0?1:m>=0?0:-1},W=A.x9();W.i1();W.i2())W.i9().av(y)},a2:function(t,b,q){for(var J=hn.a(this._jn.x0()),z=t.x9();z.i1();z.i2())for(var Z=z.i9(),r=t.c2(Z),X=r.x1();X.i1();X.i2())J[X.i9().al()]=Z.al();this.a4(t,oe.a2(t),J,b,q)},a3:function(F,L,k){if(F.c2(L).ay()>1){for(var J=0,q=0,o=0,E=L.ap();E.i1();E.i2()){var X=E.i8(),g=k.i3(X);g-J>q&&(q=g-J,o=(J+g)/2),J=g}360-J>q&&(o=(360+J)/2),this.a6(F,L,o);for(var Y=L.ap();Y.i1();Y.i2()){var N=Y.i8(),Z=k.i3(N);for(Z-=o;0>Z;Z+=360);k.i6(N,Z)}L.av(function(l,w){var M=k.i3(l)-k.i3(w);return M>0?1:M>=0?0:-1})}},a4:function(t,Z,T,V,l){for(var c=Z.al(),I=l.i3(Z),F=Z.ap();F.i1();F.i2()){for(var R=F.i8(),i=t.b(R),a=0,H=0,p=0,o=0,U=i.c1();U.i1();U.i2()){var K,L,Q=U.i8();T[Q.a2().al()]===c?(K=Q.a2(),L=Q.a3()):(K=Q.a3(),L=Q.a2()),p-=this._jn.g5(K),o+=this._jn.g6(K),a-=this._jn.g5(L),H+=this._jn.g6(L)}if(0!==p||0!==o){var z;for(z=180*m(o,p)/M-I;0>z;z+=360);V.i6(R,z)}if(0!==a&&0!==H){var k=180*m(H,a)/M;0>k&&(k+=360),l.i6(R.a3(),k)}this.a4(t,R.a3(),T,V,l)}},a5:function(F,w,Y){for(var e=F.g4(w),d=w.ap();d.i1();d.i2()){var T=d.i8(),c=T.a3(),N=F.g4(c),j=180*m(N.y-e.y,N.x-e.x)/M;Y.i1(c)&&(j+=Y.i3(c)),this.a6(F,c,j),this.a5(F,c,Y)}},a6:function(m,$,f){f=f/180*M;var J=m.c2($);if(!(J.ay()<=1))for(var d=J.x1();d.i1();d.i2()){var h=d.i9(),s=this._jn.g5(h),y=this._jn.g6(h),B=l(f),S=T(f),K=s*B-S*y,O=s*S+B*y;this._jn.s2(h,K,O)}}});var _m=function(){this._a=(new Date).getTime()};I(_m,G,{b:function(){return(new Date).getTime()-this._a}});var Hg={_A:new S(0,0),b:function(J,G,r){return Hg.c(J.x,J.y,G.x,G.y,r.x,r.y)},c:function(y,t,_,S,c,m){_-=y,S-=t,c-=y,m-=t;var V=c*S-m*_;return V>=0?0>=V?0:-1:1},d:function(O,M,m){return Hg.b(O,M,m)>0},f:function(V,L,v){return Hg.b(V,L,v)<0},g:function(f,T,D){return 0===Hg.b(f,T,D)},h:function(h){return Hg.i(h)},i:function(Z){var U=new em(Z.ah()),s=new em;if(U.a2(),U.ar())return s;var S=U.at();for(s.ae(S);!U.ar()&&S.equals(U.am());U.at());if(U.ar())return s;S=U.at();for(var P=s.ae(S),O=U.ah();O.i1();O.i2()){var W=O.i6();if(!W.equals(S))if(S=W,2===s.ay()&&Hg.g(s.am(),s.as(),W))P.c(W);else{var x;for(x=P;!Hg.f(s.ai(x).d(),x.d(),W);x=s.ai(x));var m;for(m=P;!Hg.d(s.aj(m).d(),m.d(),W);m=s.aj(m));for(;m!==s.aj(x);s.aw(s.aj(x)));P=s.an(W,x)}}return s},j:function(){return Hg.k(d)},k:function(s){return j(Z()*s)},l:function($,p){return Z()*(p-$)+$}},Jl=function(){};I(Jl,G,{i1:function(E){this._b=E;var e=new Xq;e=nh.a6(E),e.az(nh.a7(E));for(var R=this.a1();!e.ar();E.x5(e.c3()));return R.x1()},a1:function(){if(this._b.x0()<3)return new Ri(this._b.x9());for(var y=this._b.xk(),W=this._b.xk(),I=this._b.xl(),h=new Yn(this._b,new On,0,this.a3(this._b)),s=this._b.x0(),i=new Xq,Z=new Xq,F=new _h(this._b);s>3;s--){for(var D=h.g(),g=D.an();g.i1();g.i2())y.z1(g.i9(),s),W.i5(g.i9(),!1);for(var a=D.an();a.i1();a.i2())for(var e=a.i9(),Q=e.ap();Q.i1();Q.i2()){var q=Q.i8();y.i2(q.a3())===s&&(Z.aa(q),W.i5(q.a2(),!0),W.i5(q.a3(),!0))}if(Z.ay()<D.ad()-1){for(var Y=x,u=D.an();u.i1();u.i2()){var l=u.i9();if(y.i2(l)===s&&!W.i4(l))if(Y){var f=this._b.xo(Y,l);I.i7(f,!0),Z.aa(f),Y=x}else Y=l}if(Y)for(var N=D.an();N.i1();N.i2()){var M=N.i9();if(M!==Y&&!M.aj(Y)){var j=this._b.xo(Y,M);I.i7(j,!0),Z.aa(j);break}}if(Z.ay()<D.ad()-1){for(var P,r=2147483647,G=D.an();G.i1();G.i2()){var w=G.i9();w.ad()<r&&(P=w,r=w.ad())}for(var E=D.an();E.i1();E.i2()){var p=E.i9();if(!P.aj(p)&&P!==p){var m=this._b.xo(P,p);if(I.i7(m,!0),Z.aa(m),Z.ay()>=D.ad()-1)break}}}}for(var U=D.an();U.i1();U.i2())h.b(U.i9());for(var T=Z.c1();T.i1();T.i2()){var n=T.i8();I.i4(n)&&(h.d(n.a2()),h.d(n.a3()))}i.az(Z),F.e(D)}F.b(),h.c();for(var t=i.c1();t.i1();t.i2()){var O=t.i8();O._h&&(I.i4(O)?this._b.x5(O):this._b.h1(O))}var k,X=this.a4(this._b),V=new Ri,C=X.ak(0),B=X.ak(1);k=C.a2()===B.a2()||C.a2()===B.a3()?C.a3():C.a2(),V.aa(k);for(var v=X.c1();v.i1();v.i2()){var K=v.i8();k=K.a1(k),V.aa(k)}for(var S=i.c1();S.i1();S.i2()){var A=S.i8();I.i4(A)||A._h||this._b.u1(A)}return this._b.xi(W),this._b.xj(I),this._b.xi(y),this.a2(V),V},a2:function(e){if(e.ay()<this._b.x0()){for(var g=this._b.xk(),t=e._b;t;t=t.a()){var j=t.d();g.z1(j,t)}for(var w=new Yn(this._b,new Al(g),0,e.ay(),new Al(g));!w.e();){for(var p=w.f(),i=p.an();i.i1();i.i2()){var H=i.i9();if(g.i1(H)){var M,h=g.i1(H),X=e.ai(h).d();M=p.aj(X)?e.ao(p,h):e.an(p,h),g.z1(p,M);break}}for(var r=p.an();r.i1();r.i2()){var B=r.i9();g.i1(B)||w.d(B)}}this._b.xi(g),w.c()}},a3:function(U){for(var K=0,o=U.x9();o.i1();o.i2())K=r(K,o.i9().ad());return K},a4:function($){for(var t=[],W=0,N=$.x0();N>W;W++)t[W]=new is;var Q=new Ml(t);Q.a6(!1),Q.a8($);for(var p,T=-1,v=$.x9();v.i1();v.i2()){var n=v.i9(),z=t[n.al()];z._a+z._c>T&&(T=z._a+z._c,p=n)}for(var E=new Xq,B=p,U=t[B.al()]._d;U;U=t[B.al()]._d)E.ac(U),B=U.a1(B);B=p;for(var K=t[B.al()]._b;K;K=t[B.al()]._d)E.ae(K),B=K.a1(B);return E}});var Yr=function(){this._v=new pl,this._x=new pl,this._z=new Ff(3,5),this._w=new Ff(3,5),this._y=!1,this._u=!1,this._t={}};I(Yr,G,{xm:function(){var w=new rf(this);return w},xo:function(c,X){return this.xn(c,x,X,x,0,0)},xn:function(n,X,f,h,b,U){return new Zr(this,n,X,f,h,b,U)},x4:function(M){this.b3(M)},b3:function(f){for(var U;U=f._o[0];)this.x5(U);for(;U=f._o[1];)this.x5(U);this._v.c(f),f._g=x,this._y=!0},x5:function(G){this.a11(G)},a11:function(B){if(B._h!==this)throw"";this.a12(B,B.a2(),B.a3()),this._x.c(B),B._h=x,this._u=!0},x7:function(B){B._p=this._v._c,B._g=this,B.ac(),B._c.length<this._z._b&&this._z.a3(B,B._c.length,this._z._b),this._v.a(B),this._y=!0},x8:function(H){if(H._h)throw"";H._c.length<this._w._b&&this._w.a3(H,H._c.length,this._w._b),H._a&&H._a._h===this?this._x.b(H,H._a):this._x.a(H),H._h=this,H.a4(),this.b2(H,H.a2(),x,H.a3(),x,0,0),this._u=!0},xr:function(J,d,Q){var I=J.a2(),L=J.a3();J._h?(I!==d&&(I.ar(J,0,0),J._d=d,d.ab(J,x,0,0,0)),L!==Q&&(L.ar(J,1,1),J._e=Q,Q.ab(J,x,1,1,0))):(J._d=d,J._e=Q)},x3:function(R){this.xr(R,R.a3(),R.a2())},h1:function(q){this.a11(q)},u1:function(g){this.x8(g)},h2:function(M){this.x4(M)},h3:function(v){this.x7(v)},xa:function(){return this._v._c},x0:function(){return this._v._c},xh:function(){return this._x._c},xg:function(){return this._x._c},xb:function(){return 0===this._v._c},xq:function(Q){return Q._g===this},xp:function(h){return h._h===this},xd:function(){return this._v._a},x9:function(){return new Ip(this._v)},xf:function(){return new Ip(this._x)},x2:function(E,p){var q=hn.d(this.xh());if(E&&p)for(var w=this.x9();w.i1();w.i2())w.i9().at(E,1,q),w.i9().at(p,0,q);else if(!p&&E)for(var a=this.x9();a.i1();a.i2())a.i9().at(E,1,q);else if(p&&!E)for(var V=this.x9();V.i1();V.i2())V.i9().at(p,0,q)},xk:function(){return this._z.b(this._v)},xl:function(){return this._w.c(this._x)},xi:function(n){this._z.a5(n,this._v)},xj:function(J){this._w.a6(J,this._x)},xc:function(z){return this._t[z]},x1:function(g,t){this._t[g]=t},x6:function(i){delete this._t[i]},b2:function(h,m,t,H,e,g,o){m.ab(h,t,0,0,g),H.ab(h,e,1,1,o)},a12:function(w,f,r){f.ar(w,0,0),r.ar(w,1,1)},c:function(){for(var A=0,i=this.x9();i.i1();i.i2())i.i9()._p=A++;this._y=!1},b1:function(){for(var v=0,w=this.xf();w.i1();w.i2())w.i8()._g=v++;this._u=!1},xs:function(B){B.as(this,this._z._b),B._p=this._v._c,this._v.a(B)},xt:function(K,U,_,C,w,b,v){K.a6(this,U,C,this._w._b),K._g=this._x._c,this._x.a(K),this.b2(K,K.a2(),_,K.a3(),w,b,v)}});var me=function(){me.superClass.constructor.call(this)};I(me,Yr,{gb:function(j){return this.g1(j)},g7:function(r){return this.g2(r)},g5:function(U){var k=this.g1(U);return k.i1()+k.i3()/2},g6:function(L){var u=this.g1(L);return u.i2()+u.i4()/2},g4:function(L){return new S(this.g5(L),this.g6(L))},gi:function(f){return this.g1(f).i1()},gh:function(O){return this.g1(O).i2()},ga:function(t){var X=this.g1(t);return new S(X.i1(),X.i2())},gj:function(B){return this.g1(B).i3()},g9:function(o){return this.g1(o).i4()},gm:function(E){return new V(this.gj(E),this.g9(E))},s1:function(l,X){this.s2(l,X.x,X.y)},s2:function(n,A,$){var N=this.g1(n);N.i5(A-N.i3()/2,$-N.i4()/2)},s7:function(r,c,I){this.g1(r).i6(c,I)},s8:function(A,X){this.s7(A,X.width,X.height)},s3:function(a,p,U){this.g1(a).i5(p,U)},s4:function(J,R){this.s3(J,R.x,R.y)},gp:function(W){for(var V=this.g2(W),P=new J,E=0;E<V.i1();E++)P.add(V.i2(E));return new w(P)},gf:function(y){for(var L=this.g2(y),v=new em,N=0;N<L.i1();N++)v.aa(L.i2(N));return v},gc:function(g){var N=new J;N.add(this.gs(g));for(var L=this.gp(g).d();L.i1();L.i2())N.add(L.i6());return N.add(this.gl(g)),new w(N)},gd:function(z){var n=new em;n.aa(this.gs(z));for(var u=this.gp(z).d();u.i1();u.i2())n.aa(u.i6());return n.aa(this.gl(z)),n},m1:function(r,K){var x=this.g2(r);x.i5();var L=K.ah(),k=L.i6();this.gx(r,k);var i=K.as();for(L.i2();L.i6()!==i;L.i2()){var m=L.i6();x.i4(m.x,m.y)}this.gy(r,i)},s5:function(T,x){var l=this.g2(T);l.i5();for(var M=x.d();M.i1();M.i2()){var V=M.i6();l.i4(V.x,V.y)}},s6:function(o,F){var O=this.g2(o);O.i5();for(var H=F.ah();H.i1();H.i2()){var m=H.i6();O.i4(m.x,m.y)}},m2:function(y,X,e){this.gx(y,X),this.gy(y,e)},gn:function(B){return this.g2(B).i6()},gk:function(s){return this.g2(s).i7()},gt:function(e,S){this.g2(e).i8(S)},gz:function(M,h){this.g2(M).i9(h)},gs:function(t){var r=this.g2(t).i6();return r?new S(this.g5(t.a2())+r.x,this.g6(t.a2())+r.y):this.g4(t.a2())},gl:function($){var o=this.g2($).i7();return o?new S(this.g5($.a3())+o.x,this.g6($.a3())+o.y):this.g4($.a3())},gx:function(_,R){this.g2(_).i8(new S(R.x-this.g5(_.a2()),R.y-this.g6(_.a2())))},gy:function(K,Y){this.g2(K).i9(new S(Y.x-this.g5(K.a3()),Y.y-this.g6(K.a3())))},g8:function(){for(var K=new Xq,m=this.xf();m.i1();m.i2())K.aa(m.i8());return K},g3:function(){for(var _,T,N=_=d,q=T=R,J=this.x9();J.i1();J.i2()){var Y=this.ga(J.i9()),n=this.gm(J.i9());N=B(Y.x,N),_=B(Y.y,_),q=r(Y.x+n.width,q),T=r(Y.y+n.height,T)}for(var c=this.xf();c.i1();c.i2())for(var Z=this.gp(c.i8()).c();Z.i1();Z.i2()){var F=Z.i6();N=B(F.x,N),_=B(F.y,_),q=r(F.x,q),T=r(F.y,T)}return{x:j(N),y:j(_),width:j(q-N),height:j(T-_)}}});var fi=function(){fi.superClass.constructor.call(this),this.a(new _,new h)};I(fi,me,{a:function(d,j){this._a3=d,this._a4=j},xo:function(F,X){return this.l2(F,X,this._a4.a6())},l2:function(q,j,E){return this.l1(q,x,j,x,0,0,E)},xn:function(R,L,r,B,m,Z){return this.l1(R,L,r,B,m,Z,this._a4.a6())},l1:function(x,Z,X,$,e,w,N){var W=new Zr(this,x,Z,X,$,e,w);return W._l=N,W},xm:function(){var T=new rf(this);return T._r=this._a3.m3(),T},g3:function(){for(var B={x:0,y:0,width:-1,height:-1},e=this.x9();e.i1();e.i2())e.i9()._r.m1(B);return B},g1:function(a){return a._r},g2:function(s){return s._l},g5:function(k){return k._r.m4()},g6:function(R){return R._r.m5()},gi:function(W){return W._r.i1()},gh:function(n){return n._r.i2()},gj:function(f){return f._r.i3()},g9:function(T){return T._r.i4()},s2:function(Y,G,i){Y._r.m6(G,i)},s7:function(C,D,z){C._r.i6(D,z)},s3:function(k,n,F){k._r.i5(n,F)}});var vd=function(){vd.superClass.constructor.call(this),this._ap=this.xk(),this._as=this.xl()};I(vd,me,{g1:function(Y){var l=this._ap.i1(Y);return l||(l=new wn,this._ap.z1(Y,l)),l},g2:function(g){var S=this._as.i1(g);return S||(S=new $m,this._as.i8(g,S)),S}});var Sc=function(k){Sc.superClass.constructor.call(this),this._ay=k,this._a0=this.xk(),this._au=this.xl()};I(Sc,vd,{c2:function(d){var b=this._a0.i1(d);return b},a2:function(W,r){this._a0.z1(W,r)},h:function(){this._az||(this._az=this.xk());for(var A=hn.a(this._ay.x0()+1),d=1,Y=this.x9();Y.i1();){for(var n=this.c2(Y.i9()),w=n.x1();w.i1();w.i2()){var i=w.i9();A[i.al()]=d}for(var o=new Xq,F=n.x1();F.i1();F.i2())for(var S=F.i9(),v=A[S.al()],g=S.ap();g.i1();g.i2()){var U=g.i8(),N=U.a3(),j=A[N.al()];j===v&&o.ac(U)}this._az.z1(Y.i9(),o),Y.i2(),d++}},d1:function(o){return this._az.i1(o)},b:function(E){return this._au.i1(E)},a3:function(o,v){this._au.i8(o,v)},a1:function(){var Y=this._ay.xk(),A=xg.a4(hn.a(this._ay.xh())),f=nh.a5(this._ay,A,nh.a1(this._ay,A,Y));this.d2(Y,f),this._ay.xi(Y)},c1:function(r){for(var _,c=-1,q=0,o=r.length;o>q;q++){var p=r[q];p.ay()>c&&(_=p,c=p.ay())}return _},d2:function($,C){for(var e=this._ay.xl(),Y=this._ay.xk(),z=C.length,m=0;z>m;m++)for(var p=C[m],L=p.c1();L.i1();L.i2())e.i8(L.i8(),p);var H=this.c1(C);this.a4(H,$,e,new J,Y);var j={};z=C.length;for(var y=0;z>y;y++){var Z=C[y];if(Z.ay()>1){var P=this.xm();j[Z._id]=P}}for(var k=this._ay.x9();k.i1();k.i2()){var G=k.i9();if($.i4(G)&&!Y.i1(G)){var v=this.xm();j[G._id]=v;var b=new Ri;b.aa(G),this.a2(v,b)}}var T=hn.d(2),F=0;for(z=C.length;z>F;F++){var N=C[F];if(1===N.ay()){var s=N.c2();T[0]=s.a2(),T[1]=s.a3();for(var n=0;2>n;n++){var U=T[n];if(1===U.ad()){var V=this.xm();j[U._id]=V;var B=new Ri;B.aa(U),this.a2(V,B)}}}}for(var I=this._ay.x9();I.i1();I.i2()){var M=I.i9();if(Y.i1(M))for(var X=Y.i1(M),D=j[X._id],x=M.af();x.i1();x.i2()){var S=x.i8();if(e.i1(S)!==X){var i=j[e.i1(S)._id];if(!i){var _=S.a1(M),c=Y.i1(_);i=c?j[c._id]:j[_._id]}var f,u=D.aj(i);u?f=this.b(u):(u=this.xo(D,i),f=new Xq),f.aa(S),this.a3(u,f)}}else if($.i4(M))for(var l=j[M._id],q=M.af();q.i1();q.i2()){var d=q.i8(),W=d.a1(M),A=j[W._id];if(A){var t=l.aj(A);if(!t){var R=this.xo(l,A),Q=new Xq;Q.aa(d),this.a3(R,Q)}}}}if(2===this._ay.x0()&&1===this._ay.xg()){var h=this._ay.xf().i8(),r=j[h.a2()._id],w=j[h.a3()._id];if(w&&r&&!w.aj(r)){var g=this.xo(r,w),O=new Xq;O.aa(h),this.a3(g,O)}}var o=hn.a(this._ay.x0()),E=1;z=C.length;for(var a=0;z>a;a++){var K=C[a],jq=j[K._id];if(jq){var sr=this.c2(jq);sr||(sr=new Ri,this.a2(jq,sr));for(var Mp=K.c1();Mp.i1();Mp.i2()){var Ak=Mp.i8(),kg=Ak.a2();o[kg.al()]===E||$.i4(kg)&&Y.i1(kg)!==K||(o[kg.al()]=E,sr.aa(kg)),kg=Ak.a3(),o[kg.al()]===E||$.i4(kg)&&Y.i1(kg)!==K||(o[kg.al()]=E,sr.aa(kg))}}}this._ay.xj(e),this._ay.xi(Y)},a4:function(f,z,b,J,U){if(!J.contains(f)){J.add(f);for(var O=[],m=f.c1();m.i1();m.i2()){var Q=m.i8();O[0]=Q.a2(),O[1]=Q.a3();for(var g=0;2>g;g++){var G=O[g];if(z.i4(G)&&!U.i1(G)){f.ay()>1&&U.z1(G,f);for(var o=G.af();o.i1();o.i2())this.a4(b.i1(o.i8()),z,b,J,U)}}}}}});var sg=function(l,k,w){this._a={},sg.superClass.constructor.call(this);for(var j=new J,R=0,c=k.size();c>R;R++){var P=k.get(R);if(P instanceof L)j.add(P);else{P instanceof X&&P.setExpanded(!0);var M=l.getNodeSize(P);if(P instanceof X&&P.setExpanded(!1),M){var C=this.xm(),Q=l._repulsion;w!==F||P instanceof X?w===F?Q*=1.1:w===Y&&(Q*=.9):Q*=.6,w===D||w===U?this.s7(C,M.height*Q,M.width*Q):this.s7(C,M.width*Q,M.height*Q),C.node=P,this._a[P.getId()]=C}}}for(R=0,c=j.size();c>R;R++){var b=j.get(R),p=b.getSourceAgent(),u=b.getTargetAgent(),Z=this._a[p.getId()],y=this._a[u.getId()];Z&&y&&Z!==y&&this.xo(Z,y)}};I(sg,fi,{});var Cq={_D:new w,_E:new S(0,0),b:function(y){if(y.i1()>0){for(var q=new J,r=y.i1()-1;r>=0;r--)q.add(y.i2(r));y.i5();for(var T=0,s=q.size();s>T;T++){var b=q.get(T);y.i4(b.x,b.y)}}var K=y.i6();y.i8(y.i7()),y.i9(K)},c:function(j){Cq.d(j,!0)},d:function(l,A){if(A)for(var _=l.xf();_.i1();_.i2()){var Y=_.i8();l.gt(Y,Cq._E),l.gz(Y,Cq._E),l.s5(Y,Cq._D)}else for(var D=l.xf();D.i1();D.i2())l.s5(D.i8(),Cq._D)},e:function($){for(var A=new S(0,0),l=$.xf();l.i1();l.i2()){var U=l.i8();$.gt(U,A),$.gz(U,A)}},f:function(u,M,y,b){for(var A=u.gc(M).b(),W=hn.d(A),L=0,g=u.gc(M).c();g.i1();g.i2()){var Z=g.i6();(0>=L||!Z.equals(W[L-1]))&&(W[L]=new S(Z.x,Z.y),L++)}if(A=L,!(2>A)){var v=new J,P=Cq.i(new K(W[1].x-W[0].x,W[1].y-W[0].y));P.x*=b,P.y*=b;for(var h=Cq.h(W[0],P),O=Cq.h(W[1],P),x=new N(h,O),G=1;A-1>G;G++){var m=x,C=Cq.i(new K(W[G+1].x-W[G].x,W[G+1].y-W[G].y));C.x*=b,C.y*=b;var V=Cq.h(W[G],C),H=Cq.h(W[G+1],C);x=new N(V,H);var c=N.a6(m,x);c&&v.add(new S(c.x,c.y))}var X=new K(W[A-1].x-W[A-2].x,W[A-1].y-W[A-2].y);X=Cq.i(X),X.x*=b,X.y*=b;var e=Cq.h(W[A-1],X),D=new w(v);M.a2()===y.a2()?(u.s5(y,D),u.m2(y,h,e)):(u.s5(y,D.a()),u.m2(y,e,h))}},g:function(o,c,B,y){for(var K=y,j=B.c1();j.i1();j.i2()){var W=j.i8();Cq.f(o,c,W,K),0>K&&(K-=y),K=-K}},a:function(P,O,$){return Cq.j(P,O,$,1)},l:function(i,L,q){if(!i||i.length<1)return L&&(L.x=0,L.y=0,L.width=0,L.height=0),{width:0,height:0};for(var x=0,u=0,F=0;F<i.length;F++){var p=i[F];x=r(x,p.width),u=r(u,p.height)}var Z,n,X=x*u*i.length,e=P(X/q),b=X/e,a=j(b/x),V=E(b/x),c=E(i.length/a),d=E(i.length/V);V*d>a*c?(Z=a,n=c):(Z=V,n=d);var C,K=0,$=0,v=0,t=0;if(x>u)for(var A=0;A<i.length;A++)C=i[A],C.x=$*x,C.y=K*u,v=r(v,C.x+C.width),t=r(t,C.y+C.height),++$>=Z&&(K++,$=0);else for(var z=0;z<i.length;z++)C=i[z],C.x=$*x,C.y=K*u,v=r(v,C.x+C.width),t=r(t,C.y+C.height),++K>=n&&($++,K=0);return L&&(L.x=0,L.y=0,L.width=v,L.height=t),{width:n,height:Z}},j:function(U,c,d){if(!U||U.length<1)return c&&(c.x=0,c.y=0,c.width=0,c.height=0),0;for(var G,_,a=G=U[0].width,e=_=U[0].height,g=U.length,w=1;g>w;w++){var p=U[w].width;a=B(a,p),G=r(G,p);var N=U[w].height;e=B(e,N),_=r(_,N)}if(e/_>.95&&a/G>.95)return Cq.l(U,c,d).width;for(var f=new em,Q=0,x=0;g>x;x++){var m=U[x];f.aa(U[x]),Q=j(Q+m.width*m.height)}f.a1(function(k,z){var E=j(z.height)-j(k.height);return 0===E?j(z.width)-j(k.width):E});var R=0,h=0,z=j(d*P(Q/d)),F=z,k=0,l=new em;do{var i,T,v=new em,C=i=T=0;l.aa(v);for(var H=f.ah();H.i1();H.i2()){var q=H.i6();C+q.width>F&&v.ay()>0?(T=r(T,C),v=new em,v.aa(q),l.aa(v),C=j(q.width)):(v.aa(q),C=j(C+q.width)),1===v.ay()&&(i=j(i+v.am().height))}T=r(T,C),d*i>T&&k!==T&&(l.af(),F=j(1.1*F),k=T)}while(l.ar());for(var W=0,I=l.ah();I.i1();I.i2()){for(var M=0,D=I.i6(),V=D.ah();V.i1();V.i2()){var S=V.i6();S.x=M,S.y=W,M+=S.width}R=r(R,M),W+=Cq.k(D),h=r(h,W)}return c&&(c.x=0,c.y=0,c.width=R,c.height=h),l.ay()},k:function(f){for(var E=0,y=f.ah();y.i1();y.i2())E=r(y.i6().height,E);return E},h:function(L,E){return new S(L.x+E.x,L.y+E.y)},i:function(X){var H=P(X.x*X.x+X.y*X.y);return new K(-X.y/H,X.x/H)}},zm=function(r){this._a=r,this._b=!1};I(zm,G,{a:function(){return this._b},b:function(){return this._a},c:function(){return 1===this._a},d:function(){return 2===this._a},e:function(){return 4===this._a},f:function(){return 8===this._a},g:function(){return 0===this._a}}),zm.h=function(K,X){var U=K.xc("A");return U?U.i1(X):x},zm.i=function(k,j){var f=k.xc("B");return f?f.i1(j):x},zm.j=function(j){switch(j){case 1:return zm.k;case 2:return zm.l}return x},zm.k=new zm(1),zm.l=new zm(2);var cn=function(V,l,U,B,x){this._o=0,this._l=0,this._i=0,this._d=0,this._f=0,this._b=V,this._a=1e-4,this._r=l,this._p=1,this._e=(x.gj(V)+x.g9(V))/4;var g=.45*U*P(B);this._k=Hg.l(-g,g),this._h=Hg.l(-g,g),this._g=Hg.l(-g,g)};I(cn,G,{});var sd=function(){this._a=0,this._c=0,this._b=0};I(sd,G,{});var xj=function(){xj.superClass.constructor.call(this),this._dj=0,this._dh=0,this._dq=0,this._dp=0,this._dt=0,this._de=0,this._d3=0,this._dr=0,this._ed=0,this._dw=.65,this._ea=1,this._dl=80,this._dx=3,this._d8=!0,this._eb=3e5,this._ee=2,this._di=2,this._df=1e3};I(xj,mo,{i4:function(){return!0},i3:function(S){if(S&&(this._d5=S,this.s(S))){var J=new sd,w=0,v=j(this._dx*this._dz.length*this._dz.length+20*this._dz.length);v=r(v,1e4);var C=this._ea*this._ea*this._dz.length,g=this._df;try{for(;this._dj>C&&v>w;w++){var I=this.b(2147483647&w);0===g--&&(this._dy.b()>this._eb&&(w=v),g=this._df),this.h(I,J),this.d(I,J),this.i(I,J),this._d8?(this.g(I,J),this.j(I,J)):(this.f(I,J),this.c(I,J));var b=P(J._a*J._a+J._c*J._c+J._b*J._b);this.ac(I,J,b),this.aa(I,J,b)}}finally{this.r()}}},s:function(u){if(!u||u.xa()<1)return!1;this._d5=u,this._dp=1,this._dy=new _m,this._dt=u.x0(),this._d2=hn.d(this._dt),this._df=1+1e5/this._dt,this._ed=1/(2*this._di),this._de=this._ed*this._ee/(.05*this._dl),this._d3=O(this._dl,-1)*this._ed,this._dr=O(this._dl,3)*this._ed,this._dj=0,this._du=new sd,this._dq=r(20*this._dl,10);var I=r(.1,B(this._dw*this._dl,this._dq)),F=this._dt;Cq.c(u),this._dz=hn.d(F);for(var t=u.x9();t.i1();t.i2()){var L=t.i9(),p=new cn(L,I,this._dl,this._dt,u);this._dz[--F]=p,this._dj+=p._r,this._dh+=p._r*p._r,this._du._a+=p._k,this._du._c+=p._h,this._du._b+=p._g,this._d2[L.al()]=p}return this._d8=!1,this._dz.length>0},b:function(p){var G=this._dz.length,y=G-p%G-1,I=Hg.k(y+1),f=this._dz[I];return this._dz[I]=this._dz[y],this._dz[y]=f,f},f:function(C,f){for(var B,x,X=B=x=0,w=C._b.ae();w;w=w.a7()){var K,c=this._d2[w.a2().al()],h=c._k-C._k,W=c._h-C._h,b=c._g-C._g,u=h*h+W*W+b*b,A=P(u),V=A-(c._e+C._e);0>=V||(K=V*V*this._d3/A,X+=h*K,B+=W*K,x+=b*K)}for(var j=C._b.ag();j;j=j.a8()){var L,$=this._d2[j.a3().al()],G=$._k-C._k,H=$._h-C._h,M=$._g-C._g,v=G*G+H*H+M*M,d=P(v),T=d-($._e+C._e);0>=T||(L=T*T*this._d3/d,X+=G*L,B+=H*L,x+=M*L)}f._a+=X,f._c+=B,f._b+=x},g:function(S,K){var X,k,v=X=k=0;this._dp++,S._f=this._dp;for(var V=S._b.ae();V;V=V.a7()){var D=this._d2[V.a2().al()];D._f=this._dp;var n=D._k-S._k,U=D._h-S._h,x=D._g-S._g,Q=n*n+U*U+x*x,E=P(Q);if(0!==E){var e=r(1e-6,E-(S._e+D._e)),d=-this._ef[V.a5()]/(e*e);d+=e*e*this._d1[V.a5()],d/=E,v+=n*d,X+=U*d,k+=x*d}}for(var G=S._b.ag();G;G=G.a8()){var M=this._d2[G.a3().al()];M._f=this._dp;var L=M._k-S._k,l=M._h-S._h,$=M._g-S._g,f=L*L+l*l+$*$,i=P(f);if(0!==i){var o=r(1e-6,i-(S._e+M._e)),c=-this._ef[G.a5()]/(o*o);c+=o*o*this._d1[G.a5()],c/=i,v+=L*c,X+=l*c,k+=$*c}}K._a+=v,K._c+=X,K._b+=k},j:function(Y,D){for(var J,R,C=J=R=0,$=this._dt-1;$>=0;$--){var T=this._d2[$];if(T._f!==Y._f){var X=Y._k-T._k,b=Y._h-T._h,I=Y._g-T._g,L=X*X+b*b+I*I;if(0!==L){var v=P(L),u=r(1e-6,v-(Y._e+T._e)),E=this._dr/(u*u*v);C+=X*E,J+=b*E,R+=I*E}}}D._a+=C,D._c+=J,D._b+=R},c:function(M,u){for(var e,A,F=e=A=0,G=this._dt-1;G>=0;G--){var B=this._d2[G],y=M._k-B._k,O=M._h-B._h,Y=M._g-B._g,D=y*y+O*O+Y*Y;if(0!==D){var H,$=P(D),t=$-(M._e+B._e);H=0>=t?this._dr/(1e-8*$):this._dr/(t*t*$),F+=y*H,e+=O*H,A+=Y*H}}u._a+=F,u._c+=e,u._b+=A},i:function(X,w){var x=this._du._b/this._dt-X._g;w._b+=x*this._dl*this._dt/this._dh},d:function(F,g){if(0!==this._de){var H=this._du._a/this._dt-F._k,G=this._du._c/this._dt-F._h,Q=this._du._b/this._dt-F._g;g._a+=H*this._de,g._c+=G*this._de,g._b+=Q*this._de}},h:function(B,g){var Y=.05*(B._r+2);Y>0&&(g._a=Hg.l(-Y,Y),g._c=Hg.l(-Y,Y),g._b=Hg.l(-Y,Y))},ac:function(X,C,K){if(0!==K&&0!==X._a){var q=C._a*X._o+C._c*X._l+C._b*X._i,h=q/(K*X._a);this._dh-=X._r*X._r,this._dj-=X._r,X._r+=X._p*h>0?.45*h:.15*h,X._r>this._dq?X._r=this._dq:X._r<.1&&(X._r=.1),this._dj+=X._r,this._dh+=X._r*X._r,X._p=h}},aa:function(m,E,B){if(B>0){var o=m._r/B,s=E._a*o,K=E._c*o,G=E._b*o;m._k+=s,m._h+=K,m._g+=G,this._du._a+=s,this._du._c+=K,this._du._b+=G,m._a=B,m._o=E._a,m._l=E._c,m._i=E._b}},r:function(){for(var g=this._d2.length-1;g>=0;g--){var X=this._d2[g];this._d5.s2(X._b,X._k,X._h)}}});var qi=function(I,Y){this._e=I,this._f=Y,this._c={}};I(qi,G,{r:function(){for(var C in this._c){var G=this._c[C];G.g.setExpanded(G.b)}},p:function(){for(var I=new J,k=new J,N=new J,g=0,m=this._f.size();m>g;g++){var G=this._f.get(g);if(G instanceof L)G.isLooped()||I.add(G);else if(G instanceof Q){if(G.getParent()instanceof X){G instanceof X||k.add(G);continue}I.add(G),G instanceof X&&(N.add(G),this.l(G))}}return k.each(function(U){for(var G=!0,c=0;c<N.size();c++)if(U.isDescendantOf(N.get(c))){G=!1;break}G&&I.add(U)}),I},l:function(V){if(!this._c[V.getId()]){var T=this._e.getType(V),z=q(T);if(z){this._c[V.getId()]={g:V,b:V.isExpanded()},V.setExpanded(!0);for(var w,E=new J,n=V.getChildren(),t=0,o=n.size();o>t;t++)if(w=n.get(t),w instanceof X&&(this.l(w),w.setExpanded(!1)),w instanceof L||E.contains(w)||E.add(w),w instanceof Q){var v=w.getEdges();if(v)for(var b=0,W=v.size();W>b;b++){var p=v.get(b);E.contains(p)||E.add(p)}}var H=new sg(this._e,E,T);try{z.i2(H);var K=k(T);for(var x in H._a){var B=H._a[x],r=H.g4(B);B.node.p(K?K.tf(r):r)}}catch(O){}V.eachChild(function(k){k instanceof X&&k.setExpanded(!0)})}}}});var _r=function(){};I(_r,G,{i1:function(A,Y,i){var u=this.a1(A,Y);return this.a2(A,Y,i),u},a1:function(B,P){var E=_r.i4(B);E.ax();for(var j=0,w=E.x1();w.i1();w.i2())P.i7(w.i9(),-1);for(var H=E.x1();H.i1();H.i2()){for(var G=H.i9(),L=-1,g=G.aq();g.i1();g.i2())L=r(L,P.i2(g.i9()));P.i7(G,L+1),j=r(j,L+1)}return j+1},a2:function(e,r,K){K.az(_r.i3(e,r))}}),_r.i3=function(x,d){for(var q=new Xq,m=x.xf();m.i1();m.i2()){var j=m.i8();d.i2(j.a2())>d.i2(j.a3())&&(x.x3(j),q.ac(j))}return q},_r.i4=function(u){var p=hn.a(u.xa());return(new ph).a1(u,p),_r.i2(u,p)},_r.i2=function(x,h){for(var l=hn.d(x.x0()),U=x.x9();U.i1();U.i2()){var Y=U.i9(),i=Y.al();l[h[i]]=Y}return new Ri(l)};var fl=function(){fl.superClass.constructor.call(this),this.c0()};I(fl,Xq,{c0:function(){this._bc=1,this._bd=0}});var Yg=function(){this._m1=20,this._m2=60,this._m3=5,this._m4=0};I(Yg,G,{i4:function(m){this._m3=m},i5:function(h){this._m4=h},i3:function(t){this._m1=t},i6:function(Z){this._m2=Z},i2:function(I){this._m5=I},t1:function(){return this._m2},a1:function(l,g){for(var f=hn.d(g.length),d=0;d<g.length;d++)f[d]=g[d].x1();this.a2(l,f)},a2:function(D,R){for(var O=hn.a(R.length),H=0,X=0;X<R.length;X++){var E=0,N=R[X];for(N.i4();N.i1();N.i2())E=r(E,D.g9(N.i9()));for(O[X]=E,N.i4();N.i1();N.i2()){var s=(O[X]-D.g9(N.i9()))/2;D.s4(N.i9(),new S(D.gi(N.i9()),H+s))}H+=O[X]+this.t1(),N.i4()}},i1:function(V,q,F){this._m6=V,this.t2(q,F)}});var cp=function(){cp.superClass.constructor.call(this)};I(cp,Yg,{t2:function(B){var I=this._m6;this._a=I.xc("D"),this._h=I.xc("C"),this.a1(I,B),this.tg(I,B),this.tf(B,xg.a5(this._e),this._m5,this._l),this.tb(I,this._f[0]),this.ta(B),this.th(I,this._f[0],B),this.b(B),this.tb(I,this._f[1]),this.ta(B),this.th(I,this._f[1],B),this.b(B),this.a11(this._f[1]),this.a12(B),this.tb(I,this._f[2]),this.ta(B),this.th(I,this._f[2],B),this.b(B),this.tb(I,this._f[3]),this.ta(B),this.th(I,this._f[3],B),this.b(B),this.a11(this._f[3]),this.a12(B),this.tc(I),this.tj()},a11:function(S){for(var b=0;b<S.length;b++)S[b]=-S[b]},b:function(E){for(var Z=0;Z<E.length;Z++){var i=E[Z];i.ax()}for(var $=0;$<E.length;$++)for(var F,p=0,o=E[$].x1();o.i1();o.i2()){var s=o.i9(),X=s.al();this._l[X]=p++,this._b[X]=F,this._k[X]=x,F&&(this._k[F.al()]=s),F=s}var u=this._a;this._a=this._h,this._h=u;for(var h=this._m6.xf();h.i1();h.i2()){var I=h.i8(),N=this._m6.gn(I);this._m6.gt(I,new S(-N.x,N.y));var r=this._m6.gk(I);this._m6.gz(I,new S(-r.x,r.y))}var z=this._l,v=function(l,r){return!l&&r?1:l&&!r?-1:l||r?z[l.a2().al()]-z[r.a2().al()]:0},w=function(P,p){return!P&&p?1:P&&!p?-1:P||p?z[P.a3().al()]:0};this._m6.x2(v,w)},a12:function(J){for(var t=this._m6.xf();t.i1();t.i2()){var A=t.i8();this._m6.x3(A);var M=this._m6.gn(A),N=this._m6.gk(A);this._m6.gz(A,M),this._m6.gt(A,N)}for(var p=new em,i=0,_=0;i<J.length;i++)p.ae(J[i]);for(;_<J.length;_++)J[_]=p.au();var C=this._l,G=function(_,W){return!_&&W?1:_&&!W?-1:_||W?C[_.a2().al()]-C[W.a2().al()]:0},U=function(D,l){return!D&&l?1:D&&!l?-1:D||l?C[D.a3().al()]:0};this._m6.x2(G,U)},tg:function(Y,e){var M=Y.x0(),C=Y.xg(),d=0;for(this._l=hn.a(M),this._b=hn.d(M),this._k=hn.d(M),this._m=hn.d(M),this._i=hn.d(M),this._o=hn.d(M),this._f=hn.e(4,M),this._c=hn.a(M),this._g=hn.a(M),this._j=hn.a(M),this._d=hn.b(M),this._e=hn.b(C);d<e.length;d++)for(var l,t=0,Z=e[d].x1();Z.i1();Z.i2()){var c=Z.i9(),s=c.al();this._l[s]=t++,this._b[s]=l,this._k[s]=x,l&&(this._k[l.al()]=c),l=c}var r=this._l,B=function(v,B){return!v&&B?1:v&&!B?-1:v||B?r[v.a2().al()]-r[B.a2().al()]:0},Q=function(X,M){return!X&&M?1:X&&!M?-1:X||M?r[X.a3().al()]:0};Y.x2(B,Q)},tb:function(k,M){for(var b=k.x9();b.i1();b.i2()){var V=b.i9(),c=V.al();this._m[c]=V,this._i[c]=V,M[c]=d,this._o[c]=V,this._c[c]=d,this._d[c]=!1,this._j[c]=this._g[c]=0}},ta:function(s){for(var M=1;M<s.length;M++)for(var K=-1,g=s[M]._b;g;g=g.a()){var W=g.d(),U=W.al(),Y=W.ak();if(0!==Y){for(var l=j((Y+1)/2),u=E((Y+1)/2),_=1,y=W.ae(),d=!1;l>_;y=y.a7())_++;for(;u>=_&&!d;_++){var c=this._m6.g2(y),r=y.a2(),T=r.al();this._i[U]===W&&!this._e[y.a5()]&&K<this._l[T]&&(K=this._l[T],this._i[T]=W,this._m[U]=this._m[T],this._i[U]=this._m[U],d=!0,this._j[T]=c.i6().x,this._g[U]=c.i7().x),y=y.a7()
}}}},th:function(I,c,B){for(var M=I.x9();M.i1();M.i2()){var Q=M.i9(),u=Q.al();this._m[u]===Q&&this.td(I,Q,c)}for(var K=0;K<B.length;K++){var y=B[K].x1();if(y.i1()){var q=B[K].x1().i9(),s=q.al();this._o[this._m[s].al()]===q&&this.tk(I,q,c)}}for(var E=I.x9();E.i1();E.i2()){var G=E.i9(),i=G.al(),U=this._c[this._o[this._m[i].al()].al()];d>U&&(c[i]+=U)}},td:function(u,G,y){var k=G.al();if(y[k]===d){y[k]=0;var T=G,U=0;do{var B=T.al();if(B!==k&&(U-=this._g[B]),this._l[B]>0){var m=this._b[B],p=this._m[this._b[B].al()],Q=p.al();this.td(u,p,y),this._o[k]===G&&(this._o[k]=this._o[Q]),this._o[k]===this._o[Q]&&(y[k]=r(y[k],y[m.al()]+this.ti(u,m,T)-U))}U+=this._j[B],T=this._i[B]}while(T!==G);U=0,T=G;do{var z=T.al();z!==k&&(U-=this._g[z]),y[z]=y[k]+U,U+=this._j[z],T=this._i[z]}while(T!==G)}},tk:function(z,D,H){var S=D.al();if(!this._d[S]){this._d[S]=!0;var $=D;do{var m=$.al(),K=this._k[m];if(K){var F=K.al(),s=this._o[this._m[F].al()];if(s!==this._o[S]){var O=H[F]-H[S]-this.ti(z,$,K);this._c[s.al()]!==d&&(O+=this._c[s.al()]),this._c[this._o[S].al()]=B(this._c[this._o[S].al()],O)}else this.tk(z,this._m[F],H)}$=this._i[m]}while($!==D)}},tc:function(P){for(var t=hn.a(4),z=hn.a(4),V=P.x9();V.i1();V.i2()){var p=V.i9().al();z[0]+=this._f[0][p],z[1]+=this._f[1][p],z[2]+=this._f[2][p],z[3]+=this._f[3][p]}z[0]/=P.xa(),z[1]/=P.xa(),z[2]/=P.xa(),z[3]/=P.xa();for(var i=P.x9();i.i1();i.i2()){var l=i.i9(),$=l.al(),r=P.g4(l);t[0]=this._f[0][$]-z[0],t[1]=this._f[1][$]-z[1],t[2]=this._f[2][$]-z[2],t[3]=this._f[3][$]-z[3],t.sort(hn.n),P.s1(l,new S((t[1]+t[2])/2,r.y))}},ti:function(X,U,h){var z,L=X.gj(U),q=X.gj(h);return z=L>1&&q>1?this._m1+(L+q)/2:this._m3+(L+q)/2,this._l[U.al()]<this._l[h.al()]?(this._a&&(z+=this._a.i3(h)),this._h&&(z+=this._h.i3(U))):(this._a&&(z+=this._a.i3(U)),this._h&&(z+=this._h.i3(h))),z},tj:function(){this._l=this._b=this._k=this._e=this._m=this._i=this._f=this._c=this._o=this._d=this._j=this._g=x},tf:function(T,L,u,G){for(var E=T.length,j=2;E-1>j;j++)for(var D=-1,O=0,$=0,e=T[j].x1(),M=T[j].x1();M.i1();M.i2()){var Y,i=M.i9(),P=!1;if(1===i.ak()&&(Y=i.ae().a2(),u.i1(Y)&&u.i1(i)&&(P=!0)),$===T[j].ay()-1||P){for(var v=P?G[Y.al()]:T[j-1].ay();$>=O;O++){for(var d=e.i9(),J=d.am();J.i1();J.i2()){var A=J.i8(),b=G[A.a2().al()];(D>b||b>v)&&L.i7(J.i8(),!0)}e.i2()}D=v}$++}}});var ad=function(l,j){this._b=20,this._a=j,this._d=l,this._f={}};I(ad,G,{a3:function(c){this._b=c},a4:function(u,y,F,S,I){if(this.a2(u)){var $=this.b2(u);$._o=y,$._m=I,$._n=S,$._f=F}},b2:function(b){var p=this._f[b._id];return p||(p=new Hp,this._f[b._id]=p),p},a2:function(c){return!!this._f[c._id]},c:function(){for(var E=xg.a1(hn.a(this._d.xa())),y=xg.a1(hn.a(this._d.xa())),m=this._d.x9();m.i1();m.i2()){var W=m.i9();if(this.a2(W)){var r=this.b2(W);E.i6(W,this._b*(r.c()-1)),y.i6(W,this._b*(r.b()-1))}}this._d.x1("D",E),this._d.x1("C",y)},g:function(){this._d.x6("D"),this._d.x6("C")},f:function(){for(var d=this._d.x9();d.i1();d.i2()){var W=d.i9();if(this.a2(W)){var N=this._d.gi(W),$=this._d.gh(W),X=this._d.gj(W),y=this._d.g9(W),E=this.b2(W),_=E._q.ay()+E._b.ay()+E._f,s=E._d.ay()+E._g.ay()+E._o,x=E._i.ay()+E._l.ay()+E._n,t=E._h.ay()+E._k.ay()+E._m,C=this._a.a7(X,_),H=this._a.a7(X,s),O=this._a.a7(y,t),K=this._a.a7(y,x);E.a2(this._a.a8(X,_,C),this._a.a8(X,s,H),this._a.a8(y,t,O),this._a.a8(y,x,K));for(var P=E._j.c1();P.i1();P.i2()){var L=P.i8(),j=this.a1(L),I=this.b1(L),n=new em;j.b()===I.b()?(j.c()?(n.aa(new S(N+E._g._bd*H+E._c,$)),n.aa(new S(N+E._g._bd*H+E._c,$-this._b)),E._g._bd++,n.aa(new S(N+E._g._bd*H+E._c,$-this._b)),n.aa(new S(N+E._g._bd*H+E._c,$)),E._g._bd++,E._g._bc=r(E._g._bc,2)):j.d()?(n.aa(new S(N+E._b._bd*C+E._p,$+y)),n.aa(new S(N+E._b._bd*C+E._p,$+y+this._b)),E._b._bd++,n.aa(new S(N+E._b._bd*C+E._p,$+y+this._b)),n.aa(new S(N+E._b._bd*C+E._p,$+y)),E._b._bd++,E._b._bc=r(E._b._bc,2)):j.f()?(n.aa(new S(N,$+E._i._bd*K+E._a)),n.aa(new S(N-this._b,$+E._i._bd*K+E._a)),E._i._bd++,n.aa(new S(N-this._b,$+E._i._bd*K+E._a)),n.aa(new S(N,$+E._i._bd*K+E._a)),E._i._bd++,E._i._bc=r(E._i._bc,2)):j.e()&&(n.aa(new S(N+X,$+E._h._bd*O+E._e)),n.aa(new S(N+X+this._b,$+E._h._bd*O+E._e)),E._h._bd++,n.aa(new S(N+X+this._b,$+E._h._bd*O+E._e)),n.aa(new S(N+X,$+E._h._bd*O+E._e)),E._h._bd++,E._h._bc=r(E._h._bc,2)),this._d.m1(L,n)):j.c()||I.c()?j.e()||I.e()?(n.aa(new S(N+X-E._d._bd*H-E._c,$)),n.aa(new S(N+X-E._d._bd*H-E._c,$-this._b*E._d._bc)),n.aa(new S(N+X+this._b*E._h._bc,$-this._b*E._d._bc)),n.aa(new S(N+X+this._b*E._h._bc,$+E._h._bd*O+E._e)),n.aa(new S(N+X,$+E._h._bd*O+E._e)),E._d._bd++,E._d._bc++,E._h._bd++,E._h._bc++,I.c()&&n.ax(),this._d.m1(L,n)):j.f()||I.f()?(n.aa(new S(N+E._g._bd*H+E._c,$)),n.aa(new S(N+E._g._bd*H+E._c,$-this._b*E._g._bc)),n.aa(new S(N-this._b*E._i._bc,$-this._b*E._g._bc)),n.aa(new S(N-this._b*E._i._bc,$+E._i._bd*K+E._a)),n.aa(new S(N,$+E._i._bd*K+E._a)),E._g._bd++,E._g._bc++,E._i._bd++,E._i._bc++,I.c()&&n.ax(),this._d.m1(L,n)):(j.d()||I.d())&&(n.aa(new S(N+X-E._d._bd*H-E._c,$)),n.aa(new S(N+X-E._d._bd*H-E._c,$-this._b*E._d._bc)),n.aa(new S(N+X+this._b*E.b(),$-this._b*E._d._bc)),n.aa(new S(N+X+this._b*E.b(),$+y+this._b*E._q._bc)),n.aa(new S(N+X-E._q._bd*C-E._p,$+y+this._b*E._q._bc)),n.aa(new S(N+X-E._q._bd*C-E._p,$+y)),E._d._bd++,E._d._bc++,E._k._bc++,E._h._bc++,E._q._bc++,E._q._bd++,I.c()&&n.ax(),this._d.m1(L,n)):j.d()||I.d()?j.e()||I.e()?(n.aa(new S(N+X-E._q._bd*C-E._p,$+y)),n.aa(new S(N+X-E._q._bd*C-E._p,$+y+this._b*E._q._bc)),n.aa(new S(N+X+this._b*E._k._bc,$+y+this._b*E._q._bc)),n.aa(new S(N+X+this._b*E._k._bc,$+y-E._k._bd*O-E._e)),n.aa(new S(N+X,$+y-E._k._bd*O-E._e)),E._q._bd++,E._q._bc++,E._k._bd++,E._k._bc++,I.d()&&n.ax(),this._d.m1(L,n)):(j.f()||I.f())&&(n.aa(new S(N+E._b._bd*C+E._p,$+y)),n.aa(new S(N+E._b._bd*C+E._p,$+y+this._b*E._b._bc)),n.aa(new S(N-this._b*E._l._bc,$+y+this._b*E._b._bc)),n.aa(new S(N-this._b*E._l._bc,$+y-E._l._bd*K-E._a)),n.aa(new S(N,$+y-E._l._bd*K-E._a)),E._b._bd++,E._b._bc++,E._l._bd++,E._l._bc++,I.d()&&n.ax(),this._d.m1(L,n)):(n.aa(new S(N,$+y-E._l._bd*K-E._a)),n.aa(new S(N-this._b*E._l._bc,$+y-E._l._bd*K-E._a)),n.aa(new S(N-this._b*E._l._bc,$+y+this._b*E.a1())),n.aa(new S(N+X+this._b*E._k._bc,$+y+this._b*E.a1())),n.aa(new S(N+X+this._b*E._k._bc,$+y-E._k._bd*O-E._e)),n.aa(new S(N+X,$+y-E._k._bd*O-E._e)),E._l._bd++,E._l._bc++,E._b._bc++,E._q._bc++,E._k._bc++,E._k._bd++,I.f()&&n.ax(),this._d.m1(L,n))}}}},a5:function(f,E){for(var T=0;T<f.length;T++)for(var H=f[T],Z=E[T],L=H.x1();L.i1();L.i2()){var j=L.i9();if(this.a2(j)){var B=this.b2(j);Z._g=r(Z._g,this._b*(B.d()-1)),Z._j=r(Z._j,this._b*(B.a1()-1))}}},a1:function(V){var N,H=this._d.xc("A");if(H&&(N=H.i1(V)),!N||N.g()){var m=this._d.xc("B");if(!m)return zm.j(1);var u=m.i1(V);if(!u||u.g())return zm.j(1);if(u.c())return zm.j(8);if(u.f())return zm.j(1);if(u.d())return zm.j(4);if(u.e())return zm.j(2)}return N},b1:function(x){var N,g=this._d.xc("B");if(g&&(N=g.i1(x)),!N||N.g()){var s=this._d.xc("A");if(!s)return zm.j(8);var k=s.i1(x);if(!k||k.g())return zm.j(8);if(k.c())return zm.j(8);if(k.f())return zm.j(1);if(k.d())return zm.j(4);if(k.e())return zm.j(2)}return N}});var Hp=function(){this._o=0,this._f=0,this._n=0,this._m=0,this._c=0,this._p=0,this._e=0,this._a=0,this._j=new Xq,this._g=new fl,this._d=new fl,this._b=new fl,this._q=new fl,this._h=new fl,this._k=new fl,this._i=new fl,this._l=new fl};I(Hp,G,{a1:function(){return r(this._q._bc,this._b._bc)},d:function(){return r(this._d._bc,this._g._bc)},b:function(){return r(this._k._bc,this._h._bc)},c:function(){return r(this._l._bc,this._i._bc)},a2:function(H,h,P,y){this._c=h,this._a=y,this._p=H,this._e=P,this._g.c0(),this._d.c0(),this._b.c0(),this._q.c0(),this._k.c0(),this._h.c0(),this._l.c0(),this._i.c0()}});var xs=function(V,k,e,I){this._k=20,this._r=.5,this._d=V,this._c=k,this._j=e,this._m=I,this._i=V.xc("A")!=x||V.xc("B")!=x,this._t=new se(V,k,e,I),this._b=new ad(V,this)};I(xs,G,{a6:function(L){this._k=L,this._t.a1(L),this._b.a3(L)},g1:function(){return this._k},a9:function(q){return this.c1(),q},a5:function(h){return this.a1(),h},b3:function(t){return this.c1(),t=this.c4(t),this._b.c(),t},g2:function(a){return this._b.g(),a},e2:function(Y){Y=this.f(Y),this._b.f()},e1:function(){this._t.d(),this._n&&this._d.xi(this._n),this.a1(),this._d=x},a1:function(){this._i&&(this._q&&(this._d.x1("A",this._q),this._q=x),this._p&&(this._d.x1("B",this._p),this._p=x),this._h&&(this._d.xj(this._h),this._h=x),this._l&&(this._d.xj(this._l),this._l=x))},c1:function(){if(this._i){this._h||(this._h=this._d.xl()),this._l||(this._l=this._d.xl());for(var m=this._d.xf();m.i1();m.i2()){var V=m.i8(),H=this._j.i1(V.a2()),s=this._j.i1(V.a3());if(H&&!s){var j=this._j.i1(V.a2());this._m.i4(j)?this._l.i8(V,zm.h(this._d,j)):this._l.i8(V,zm.i(this._d,j))}else if(!H&&s){var i=this._j.i1(V.a3());this._m.i4(i)?this._h.i8(V,zm.i(this._d,i)):this._h.i8(V,zm.h(this._d,i))}else H||s||(this._m.i4(V)?(this._h.i8(V,zm.i(this._d,V)),this._l.i8(V,zm.h(this._d,V))):(this._h.i8(V,zm.h(this._d,V)),this._l.i8(V,zm.i(this._d,V))))}this._q=this._d.xc("A"),this._p=this._d.xc("B"),this._d.x1("A",this._h),this._d.x1("B",this._l)}},c4:function(t){this._n=this._d.xk(),this._a=this._d.xl(),this._g=this._d.xl();for(var K=new Xq,k=new Xq,w=new Xq,e=new Xq,i=new Xq,y=new Xq,h=new Xq,A=new Xq,D=new Xq,o=this._d.xk(),P=0;P<t.length;P++)for(var T=0,r=t[P].x1();r.i1();)o.i6(r.i9(),T),r.i2(),T++;for(var Y=function($,L){var s=o.i3($.a3())-o.i3(L.a3());return 0>=s?s>=0?0:-1:1},q=function(r,Q){var U=o.i3(r.a2())-o.i3(Q.a2());return 0>=U?U>=0?0:-1:1},E=0;E<t.length;E++)for(var B=t[E],g=B._b;g;g=g.a()){var p=g.d(),u=0;p.av(Y),p.au(q),K.af(),k.af(),w.af(),e.af(),i.af(),y.af(),h.af(),A.af(),D.af();for(var Q=p.ap();Q.i1();){var n=Q.i8(),b=this.b1(n);!b||b.d()||b.g()?w.aa(n):b.e()?K.aa(n):b.f()?(k.aa(n),D.aa(n)):b.c()&&(A.aa(n),D.aa(n)),Q.i2(),u++}u=0;for(var R=p.am();R.i1();){var G=R.i8(),x=this.a2(G);!x||x.c()||x.g()?e.aa(G):x.e()?K.aa(G):x.f()?(k.aa(G),D.aa(G)):x.d()&&(y.aa(G),D.aa(G)),R.i2(),u++}var Z=o.i3(p);if(!D.ar())for(var J=.1/D.ay(),I=Z-.4;!D.ar();I+=J){var $=D.c3();if($.a2()===p){var d=this._d.xm();this._n.z1(d,$.a2()),this._d.s7(d,1,1),this._c.z1(d,this._c.i1(p)),o.i6(d,I),this._a.i8($,this._d.gn($)),this._d.gt($,Hg._A),this._d.xr($,d,$.a3()),B.ao(d,g)}else{var a=this._d.xm();this._n.z1(a,$.a3()),this._d.s7(a,1,1),this._c.z1(a,this._c.i1(p)),o.i6(a,I),this._g.i8($,this._d.gk($)),this._d.gz($,Hg._A),this._d.xr($,$.a2(),a),B.ao(a,g)}}if(!K.ar())for(var m=.1/K.ay(),s=Z+.1;!K.ar();s+=m){var L=K.c3();if(L.a2()===p){var j=this._d.xm();this._n.z1(j,L.a2()),this._d.s7(j,1,1),this._c.z1(j,this._c.i1(p)),o.i6(j,s),this._a.i8(L,this._d.gn(L)),this._d.gt(L,Hg._A),this._d.xr(L,j,L.a3()),g=B.an(j,g)}else{var z=this._d.xm();this._n.z1(z,L.a3()),this._d.s7(z,1,1),this._c.z1(z,this._c.i1(p)),o.i6(z,s),this._g.i8(L,this._d.gk(L)),this._d.gz(L,Hg._A),this._d.xr(L,L.a2(),z),g=B.an(z,g)}}var f=xs._z;this._b.a2(p)&&(f=this._b.b2(p));var C=f._b.ay()+y.ay()+p.ao()+i.ay()+f._q.ay();if(C>0)for(var H=this._d.g9(p)/2,c=this._d.gj(p),l=this.a7(c,C),N=-.5*c+this.a8(this._d.gj(p),C,l)+l*(f._b.ay()+y.ay()),F=p.ap();F.i1();F.i2()){var v=F.i8();this.c2(v)||this._j.i1(v.a2())||(this._d.g2(v).i8(new S(N,H)),N+=l)}var U=this._t.a3(p),X=0,V=0,W=0,_=0;if(U&&(X=U._e.ay(),V=U._c.ay(),W=U._b.ay(),_=U._d.ay()),C=f._g.ay()+X+A.ay()+p.ak()+h.ay()+V+f._d.ay(),C>0){for(var M=this._d.gj(p),O=this.a7(M,C),Hr=this.a8(M,C,O),Ad=-.5*M+Hr+O*(f._g.ay()+X+A.ay()),tc=-this._d.g9(p)/2,$f=p.am();$f.i1();$f.i2()){var rl=$f.i8();this.d1(rl)||this._j.i1(rl.a3())||(this._d.g2(rl).i9(new S(Ad,tc)),Ad+=O)}if(U){for(var vk=-.5*M+Hr+O*(f._g.ay()+A.ay()+U._e.ay()-1),Sf=U._e.c1();Sf.i1();Sf.i2()){var Oe=Sf.i8();this._d.u1(Oe),Oe.a2()!==p||this.c2(Oe)?this.d1(Oe)||(this._d.g2(Sf.i8()).i9(new S(vk,tc)),vk-=O):(this._d.g2(Sf.i8()).i8(new S(vk,tc)),vk-=O),this._d.h1(Oe)}vk=.5*M-Hr-O*(f._d.ay()+h.ay());for(var un=U._c.c1();un.i1();un.i2()){var bs=un.i8();this._d.u1(bs),bs.a2()!==p||this.c2(bs)?this.d1(bs)||(this._d.g2(un.i8()).i9(new S(vk,tc)),vk-=O):(this._d.g2(un.i8()).i8(new S(vk,tc)),vk-=O),this._d.h1(bs)}}}this._b.a2(p)&&this._b.a4(p,X+A.ay()+p.ak()+h.ay()+V,y.ay()+p.ao()+i.ay(),W+k.ay(),_+K.ay())}return this._d.xi(o),t},a7:function(C,V){return 1>=V?0:C/(V-1+2*this._r)},a8:function(n,x,f){return 1>=x?.5*n:.5*(n-f*(x-1))},f:function(Y){var i=this.g1(),N=0;for(this._f=this._d.xk();N<Y.length;N++)for(var U=Y[N],f=U._b;f;){var l=f.d(),o=this._n.i1(l);if(o||this._t.b2(l))f=f.a();else{var G=new Ri,q=new Ri,d=new Ri,M=new Ri,h=new Ri,$=new Ri,w=new Xq,k=new Xq,Z=new jf(G,q,d,M,h,$,w,k);this._f.z1(l,Z),w.ab(l.am()),k.ab(l.ap());for(var O=f.b();O&&this._n.i1(O.d())===l;O=O.b()){var A=O.d(),D=this.c3(A);D.f()?q.ac(A):D.c()?M.ac(A):D.d()&&$.ac(A)}for(var n=f.a();n&&this._n.i1(n.d())===l;n=n.a()){var C=n.d(),y=this.c3(C);y.e()?G.aa(C):y.c()?d.aa(C):y.d()&&h.aa(C)}f=n}}for(var m=this.d2(Y),z=0,v=0;v<Y.length;v++){var R=m[v];v>0&&(z+=m[v-1]._j+m[v-1]._h+m[v-1]._b),z+=R._g+R._f+R._a+R._d;for(var I=Y[v].x1();I.i1();I.i2()){var L=I.i9();this._d.s3(L,this._d.gi(L),this._d.gh(L)+z)}R._c+=z,R._i+=z}for(var s=0;s<Y.length;s++)for(var e=Y[s],X=e.x1();X.i1();X.i2()){var r=X.i9();this._n.i1(r)&&e.av(X)}for(var H=this,p=function(W,h){return H.a3(W)?H.a3(h)?H._d.gi(W)>=H._d.gi(h)?-1:1:1:H.a3(h)?-1:H._d.gi(W)>=H._d.gi(h)?1:-1},a=function(K,m){return H.a3(K)?H.a3(m)?H._d.gi(K)>=H._d.gi(m)?1:-1:1:H.a3(m)?-1:H._d.gi(K)>=H._d.gi(m)?-1:1},E=0;E<Y.length;E++)for(var V=m[E],B=Y[E].x1();B.i1();B.i2()){var K=B.i9();if(!this._t.b2(K)){var _=this._f.i1(K),u=_._d,g=_._a,F=_._b,P=_._h,Q=_._f,W=_._c,T=_._g,c=_._e,J=0,j=0,t=0,b=0,df=K.ao(),Tc=K.ak(),Rg=this._d.gi(K),wq=this._d.gh(K),Qe=this._d.gj(K),fc=this._d.g9(K),ke=this._t.a3(K),Cm=xs._z;if(this._b.a2(K)&&(Cm=this._b.b2(K)),ke){if(J=ke._d.ay(),j=ke._b.ay(),t=ke._e.ay(),b=ke._c.ay(),J>0)for(var Zi=Cm._h.ay()+u.ay()+J+Cm._k.ay(),Sp=this.a7(fc,Zi),ee=this.a8(fc,Zi,Sp),og=wq+ee+Sp*(Cm._h.ay()+this.a4(u)),bd=ke._d.c1();bd.i1();bd.i2()){var Rq=bd.i8();this._d.u1(Rq),Rq.a2()===K?this.c2(Rq)||this._d.gx(Rq,new S(Rg+Qe,og)):(this.d1(Rq),this._d.gy(Rq,new S(Rg+Qe,og))),og+=Sp,this._d.h1(Rq)}if(j>0)for(var Vi=Cm._i.ay()+g.ay()+j+Cm._l.ay(),Ih=this.a7(fc,Vi),hb=this.a8(fc,Vi,Ih),kn=wq+hb+Ih*(Cm._i.ay()+this.a4(g)),he=ke._b.c1();he.i1();he.i2()){var Ek=he.i8();this._d.u1(Ek),Ek.a2()===K?this.c2(Ek)||this._d.gx(Ek,new S(Rg,kn)):this.d1(Ek)||this._d.gy(Ek,new S(Rg,kn)),kn+=Ih,this._d.h1(Ek)}}if(u.ay()>0){u.a1(p);for(var Oo=Cm._h.ay()+u.ay()+J+Cm._k.ay(),Wo=this.a7(fc,Oo),Rj=this.a8(fc,Oo,Wo),Fc=wq+Rj+Wo*Cm._h.ay(),_k=!0;!u.ar();){var $d=u.x4();if(this.a3($d)){_k&&(_k=!1,Fc+=Wo*J);var Xn=$d.ag(),rd=this._d.gd(Xn),vd=rd.at();if(rd.ac(new S(vd.x,V.b())),this.c2(Xn)){var dg=this._a.i1(Xn);rd.ac(new S(vd.x,dg.y+this._d.g6(K))),rd.ac(new S(dg.x+this._d.g5(K),dg.y+this._d.g6(K)))}else rd.ac(new S(vd.x,Fc)),rd.ac(new S(Rg+Qe,Fc));this._d.xr(Xn,K,Xn.a3()),this._d.m1(Xn,rd)}else{var Hr=$d.ae(),cp=this._d.gd(Hr),hc=cp.au();if(cp.ae(new S(hc.x,V.a())),this.d1(Hr)){var ih=this._g.i1(Hr);cp.ae(new S(hc.x,ih.y+this._d.g6(K))),cp.ae(new S(ih.x+this._d.g5(K),ih.y+this._d.g6(K)))}else cp.ae(new S(hc.x,Fc)),cp.ae(new S(Rg+Qe,Fc));this._d.xr(Hr,Hr.a2(),K),this._d.m1(Hr,cp)}this._d.x4($d),Fc+=Wo}}if(g.ay()>0){g.a1(a);for(var fo=Cm._i.ay()+g.ay()+j+Cm._l.ay(),fe=this.a7(fc,fo),Kq=this.a8(fc,fo,fe),ap=wq+Kq+fe*Cm._i.ay(),Fd=!0;!g.ar();){var oe=g.x4();if(this.a3(oe)){Fd&&(Fd=!1,ap+=fe*j);var no=oe.ag(),pq=this._d.gd(no),Yo=pq.at();if(pq.ac(new S(Yo.x,V.b())),this.c2(no)){var Xp=this._a.i1(no);pq.ac(new S(Yo.x,Xp.y+this._d.g6(K))),pq.ac(new S(Xp.x+this._d.g5(K),Xp.y+this._d.g6(K)))}else pq.ac(new S(Yo.x,ap)),pq.ac(new S(Rg,ap));this._d.xr(no,K,no.a3()),this._d.m1(no,pq)}else{var wf=oe.ae(),pg=this._d.gd(wf),fh=pg.au();if(pg.ae(new S(fh.x,V.a())),this.d1(wf)){var pf=this._g.i1(wf);pg.ae(new S(fh.x,pf.y+this._d.g6(K))),pg.ae(new S(pf.x+this._d.g5(K),pf.y+this._d.g6(K)))}else pg.ae(new S(fh.x,ap)),pg.ae(new S(Rg,ap));this._d.xr(wf,wf.a2(),K),this._d.m1(wf,pg)}this._d.x4(oe),ap+=fe}}var gs=Cm._g.ay()+Cm._d.ay()+Tc+P.ay()+F.ay()+t+b;Qe=this._d.gj(K);var Un=this.a7(Qe,gs),Gq=this.a8(Qe,gs,Un);gs=Cm._b.ay()+Cm._q.ay()+df+W.ay()+Q.ay();var Hh=this.a7(Qe,gs),pc=this.a8(Qe,gs,Hh);if(P.ay()>0)for(var xn,_q=Un,nb=i,Fi=this._d.gi(K)+Gq+_q*(Cm._g.ay()+P.ay()-1),bl=this._d.gh(K),ii=V._c-V._g-P.ay()*nb;!P.ar();this._d.x4(xn)){xn=P.x4();var vh=xn.ag(),fq=this._d.gd(vh),ao=fq.at();if(fq.ac(new S(ao.x,V.b())),fq.ac(new S(ao.x,ii)),this.c2(vh)){var sl=this._a.i1(vh);fq.ac(new S(sl.x+this._d.g5(K),ii)),fq.ac(new S(sl.x+this._d.g5(K),sl.y+this._d.g6(K)))}else fq.ac(new S(Fi,ii)),fq.ac(new S(Fi,bl)),Fi-=_q;ii+=nb,this._d.xr(vh,K,vh.a3()),this._d.m1(vh,fq)}if(F.ay()>0)for(var Lh,ir=Un,Gm=i,lk=this._d.gi(K)+this._d.gj(K)-Gq-ir*Cm._d.ay(),Bm=this._d.gh(K),hp=V._c-V._g-Gm;!F.ar();this._d.x4(Lh)){Lh=F.x4();var ok=Lh.ag(),id=this._d.gd(ok),Me=id.at();if(id.ac(new S(Me.x,V.b())),id.ac(new S(Me.x,hp)),this.c2(ok)){var jq=this._a.i1(ok);id.ac(new S(jq.x+this._d.g5(K),hp)),id.ac(new S(jq.x+this._d.g5(K),jq.y+this._d.g6(K)))}else id.ac(new S(lk,hp)),id.ac(new S(lk,Bm)),lk-=ir;hp-=Gm,this._d.xr(ok,K,ok.a3()),this._d.m1(ok,id)}if(W.ay()>0)for(var Qk,In=Hh,Zd=i,re=this._d.gi(K)+pc+In*(Cm._b.ay()+W.ay()-1),fr=this._d.gh(K)+this._d.g9(K),Kp=fr+W.ay()*Zd;!W.ar();this._d.x4(Qk)){Qk=W.x4();var Mc=Qk.ae(),Vg=this._d.gd(Mc),Ki=Vg.au();if(Vg.ae(new S(Ki.x,V.a())),Vg.ae(new S(Ki.x,Kp)),this.d1(Mc)){var ml=this._g.i1(Mc);Vg.ae(new S(ml.x+this._d.g5(K),Kp)),Vg.ae(new S(ml.x+this._d.g5(K),ml.y+this._d.g6(K)))}else Vg.ae(new S(re,Kp)),Vg.ae(new S(re,fr)),re-=In;Kp-=Zd,this._d.xr(Mc,Mc.a2(),K),this._d.m1(Mc,Vg)}if(Q.ay()>0)for(var dq,lj=Hh,je=i,eh=this._d.gi(K)+this._d.gj(K)-pc-Hh*Cm._q.ay(),lf=this._d.gh(K)+this._d.g9(K),sj=lf+je;!Q.ar();this._d.x4(dq)){dq=Q.x4();var Xg=dq.ae(),Fk=this._d.gd(Xg),cc=Fk.au();if(Fk.ae(new S(cc.x,V.a())),Fk.ae(new S(cc.x,sj)),this.d1(Xg)){var Dk=this._g.i1(Xg);Fk.ae(new S(Dk.x+this._d.g5(K),sj)),Fk.ae(new S(Dk.x+this._d.g5(K),Dk.y+this._d.g6(K)))}else Fk.ae(new S(eh,sj)),Fk.ae(new S(eh,lf)),eh-=lj;sj+=je,this._d.xr(Xg,Xg.a2(),K),this._d.m1(Xg,Fk)}for(;!c.ar();){var Rc=c.c3(),yd=this._d.gl(Rc);V.a()+12<yd.y&&this._d.g7(Rc).i4(yd.x,V.a())}for(;!T.ar();){var Xk=T.c3(),Bg=this._d.gs(Xk);if(V.b()-12>Bg.y){var vg=this._d.gf(Xk);vg.ac(new S(Bg.x,V.b())),this._d.s6(Xk,vg)}}}}for(var Kc=0;Kc<Y.length;Kc++)for(var Zh=Y[Kc],bn=Zh._b;bn;bn=bn.a()){var Ql=bn.d(),Mn=this._t.a3(Ql);Mn&&Mn._a!=x&&(this._d.x4(Mn._a),Zh.aw(bn.b()))}return this._d.xi(this._f),this._d.xj(this._a),this._d.xj(this._g),Y},c3:function($){return this.a3($)?this.b1($.ag()):this.a2($.ae())},b1:function($){return this._h?this._h.i1($):xs.s},a2:function(l){return this._l?this._l.i1(l):xs.u},c2:function(f){if(f){var V=this.b1(f);return V!=x&&V.a()}return!1},d1:function(d){if(d){var n=this.a2(d);return n!=x&&n.a()}return!1},a3:function(w){return 1===w.ao()},b2:function(W){return 1===W.ak()},a4:function(O){for(var X=0,s=O._b;s;s=s.a())this.b2(s.d())&&X++;return X},d2:function(m){for(var K=this._k,X=hn.d(m.length+1),G=0;G<m.length;G++){var J=m[G],q=new Wh;X[G]=q,q._c=d,q._i=R;for(var F=J.x1();F.i1();F.i2()){var $=F.i9(),e=this._d.gb($);q._c=B(q._c,e.i2()),q._i=r(q._i,e.i2()+e.i4())}}this._b.a5(m,X);for(var w=0;w<m.length;w++)for(var Y=X[w],E=m[w].x1();E.i1();E.i2()){var x=E.i9(),C=this._f.i1(x);C&&(Y._h=r(Y._h,r(C._f.ay()*K,C._c.ay()*K)),Y._f=r(Y._f,r(C._b.ay()*K,C._h.ay()*K)))}return X}}),xs.s=zm.j(2),xs.u=zm.j(1),xs._z=new Hp;var rk=function(){this._af=0,this._b=0};I(rk,G,{ib:function(y){this._af=y},ia:function(V,k,n){this.a6(V,k,n),this.b2(!1);var q=this.g();if(this.o()&&q>0){for(var B=this.r(),x=0;20>x&&q>0&&this.o();x++){this.b2(!0);var A=this.g();q>A&&(this.a7(B),q=A)}this.b3(B),this.b1()}return this.c()},a6:function(d,R,F){this._b=(new Date).getTime(),this._ac=d,this._ah=R;var u=this;this._p=function(i,X){var Q=u._n[i.al()]-u._n[X.al()];return Q>0?1:Q>=0?0:-1},this._ad=hn.d(F);for(var U=0;U<this._ad.length;U++)this._ad[U]=new Ri;this._ab=hn.a(this._ac.x0()),this._f=hn.d(this._ac.x0()),this._n=hn.a(this._ac.x0()+1);var o=this._ab;this._o=function(h,T){if(!h&&T)return 1;if(h&&!T)return-1;if(!h&&!T)return 0;var x=h,U=T,A=x._h,m=x.a2(),C=U.a2(),u=o[m.al()]-o[C.al()];if(0===u){var B=rk.b(zm.h(A,x),A.gn(x)),r=rk.b(zm.h(A,U),A.gn(U)),j=B-r;if(0===j){var K=o[x.a3().al()]-o[U.a3().al()];return 0===K?rk.a(zm.i(A,x),A.gk(x))-rk.a(zm.i(A,U),A.gk(U)):K}return j}return u},this._l=function(m,r){if(!m&&r)return 1;if(m&&!r)return-1;if(!m&&!r)return 0;var X=m,z=r,B=X._h,P=X.a3(),Y=z.a3(),Q=o[P.al()]-o[Y.al()];if(0===Q){var p=rk.a(zm.i(B,X),B.gk(X))-rk.a(zm.i(B,z),B.gk(z));if(0===p){var k=o[X.a2().al()]-o[z.a2().al()];return 0===k?rk.b(zm.h(B,X),B.gn(X))-rk.b(zm.h(B,z),B.gn(z)):k}return p}return Q},this._z=function(i,R){if(!i&&R)return 1;if(i&&!R)return-1;if(!i&&!R)return 0;var p=i,J=R,n=p._h;return rk.b(zm.h(n,p),n.gn(p))-rk.b(zm.h(n,J),n.gn(J))},this._e=function(G,R){if(!G&&R)return 1;if(G&&!R)return-1;if(!G&&!R)return 0;var e=G,N=R,A=e._h;return rk.a(zm.i(A,e),A.gk(e))-rk.a(zm.i(A,N),A.gk(N))},this._ac.x2(this._e,this._z)},c:function(){this._ah=x,this._aa=x,this._f=x,this._n=x,this._p=x,this._o=x,this._l=x,this._ac=x;var H=this._ad;return this._ad=x,H},o:function(){var k=(new Date).getTime()-this._b;return k<=this._af},m:function(){for(var s=this,t=function(F,u){return E(s._n[F.a3().al()])-E(s._n[u.a3().al()])},M=this._ac.x9();M.i1();M.i2()){for(var u=M.i9().aw();u.i1();u.i2())this._n[u.i9().al()]=Hg.j();M.i9().av(t)}},b2:function(z){for(var v=0;v<this._ad.length;v++)this._ad[v].af();if(z){this.m();for(var $=0,l=this._ab.length;l>$;$++)this._ab[$]=0;this._ac.x2(x,this._z)}var O=this._ac.xm();this._ah.i7(O,0);for(var P=this._ac.x9();P.i1();P.i2())0===P.i9().ak()&&P.i9()!==O&&this._ac.xo(O,P.i9());var U=new Cb(this);U.a6(!0),U.a9(this._ac,O),this._ad[0].at(),this._ac.x4(O),this.d()},a1:function(){this._ac.x2(this._o,this._l);for(var R=0,X=1;X<this._ad.length;X++){var M=this.a2(this._ad[X-1],this._ad[X]);R+=M}return R},a2:function(F,D){var c=F.ah(),N=D.ah(),X=new em,K=new em,G=0;for(this._aa=hn.d(this._ac.x0());c.i1()&&N.i1();N.i2())G+=this.a8(c.i6(),X,K,!0),G+=this.a8(N.i6(),K,X,!1),c.i2();for(;c.i1();c.i2())G+=this.a8(c.i6(),X,K,!0);for(;N.i1();N.i2())G+=this.a8(N.i6(),K,X,!1);return G},a8:function(Q,V,J,L){var k=0,K=0,W=0;if(this._aa[Q.al()])for(var D=this._aa[Q.al()].a(),j=V._b;j!==D;j=j.a()){var y=j._c;y===Q?(k++,W+=K,V.aw(j)):K++}var d=k*J.ay()+W;if(L)for(var X=Q.ag();X;X=X.a8()){var c=X.a3();this._ab[c.al()]>=this._ab[Q.al()]&&(this._aa[c.al()]=J.ae(c))}else for(var M=Q.ae();M;M=M.a7()){var N=M.a2();this._ab[N.al()]>this._ab[Q.al()]&&(this._aa[N.al()]=J.ae(N))}return d},g:function(){for(var Z=this.r(),B=this.a1(),j=!0,O=0;3>O&&this.o()&&B>0;){var v=this.k();B>v?(this.a7(Z),B=v):O++,j=!j}if(this.b3(Z),this.b1(),B>0){for(var C=1,f=0;1===C&&B>0;f++){this.e(),this.i();var D=this.a1();B>D?(C=1,this.a7(Z)):C=-1,B=D}this.b3(Z),this.b1()}return B},e:function(){for(var G=this.l(),h=this.r(),o=hn.d(this._ac.x0()),D=this._ad.length-1;D>=0;D--)for(var y=this._ad[D].ah();y.i1();y.i2()){var w=y.i6();if(1===w.ak()&&1===w.ao()){var P=G.i1(w.ag());if(P&&!o[P.al()])for(var Q=this.a4(w,P),c=P.al(),N=o[c]=hn.d(Q+1),Z=N.length-1;Z>=0;Z--)N[Z]=new em}}for(var g=0;g<this._ad.length;g++)for(var Y=this._ad[g].ah();Y.i1();Y.i2()){var U=Y.i6();if(1===U.ak()&&1===U.ao()){var O=G.i1(U.ag());if(O){var A=O.al(),M=this.a4(U,O)-1;o[A][M].ae(U.ae())}}else for(var r=U.ae();r;r=r.a7()){var V=G.i1(r);V&&o[V.al()][this.a4(U,V)-1].ae(r)}}for(var d=this._ac.x9();d.i1();d.i2()){var $=d.i9();if(o[$.al()])for(var k=$.ag();k;k=k.a8()){var l=G.i1(k);if(l)for(var L=o[l.al()];L[0].ay()>0;){for(var e,z=0;;){e=L[z].am();var m=e.a3();if(1!==m.ak()||1!==m.ao())break;z++}var X=L[z].at().a3();z--,X=e.a2(),e=L[z].at();for(var i=e.a3();z>=0;)if(h[X.al()]!==h[i.al()]&&(this._ab[X.al()]=h[i.al()]),X=X.ae().a2(),--z>=0){var T=L[z].at();i=T.a3()}}}}this.b1(),this._ac.xj(G)},i:function(){for(var w=this.f(),X=this.r(),N=hn.d(this._ac.x0()),r=0;r<this._ad.length;r++)for(var G=this._ad[r].ah();G.i1();G.i2()){var L=G.i6();if(1===L.ak()&&1===L.ao()){var R=w.i1(L.ae());if(R&&!N[R.al()])for(var U=this.a4(R,L),m=N[R.al()]=hn.d(U+1),o=m.length-1;o>=0;o--)m[o]=new em}}for(var H=this._ad.length-1;H>=0;H--)for(var O=this._ad[H].ah();O.i1();O.i2()){var J=O.i6();if(1===J.ak()&&1===J.ao()){var u=w.i1(J.ae());u&&N[u.al()][this.a4(u,J)-1].ae(J.ag())}else for(var Q=J.ag();Q;Q=Q.a8()){var t=w.i1(Q);t&&N[t.al()][this.a4(t,J)-1].ae(Q)}}for(var I=this._ac.x9();I.i1();I.i2()){var V=I.i9();if(N[V.al()])for(var h=V.ae();h;h=h.a7()){var v=w.i1(h);if(v)for(var S=N[v.al()];S[0].ay()>0;){for(var b,M=0;;){b=S[M].am();var k=b.a2();if(1!==k.ak()||1!==k.ao())break;M++}var e=S[M].at().a2();M--,e=b.a3(),b=S[M].at();for(var z=b.a2();M>=0;)if(X[e.al()]!==X[z.al()]&&(this._ab[e.al()]=X[z.al()]),e=e.ag().a3(),--M>=0){var B=S[M].at();z=B.a2()}}}}this.b1(),this._ac.xj(w)},a4:function(s,k){return this._ah.i2(s)-this._ah.i2(k)},l:function(){for(var u=xg.a6(hn.d(this._ac.xg())),V=this._ac.x9();V.i1();V.i2()){var c=V.i9();if(c.ao()>1){for(var d=0,x=c.ag();x;x=x.a8()){var r=x.a3();1===r.ak()&&1===r.ao()&&d++}if(d>1)for(var y=c.ag();y;y=y.a8()){var O=y,K=O.a3();if(1===K.ak()&&1===K.ao()){for(;1===K.ak()&&1===K.ao();K=O.a3())u.i8(O,c),O=K.ag();u.i8(O,c)}}}}return u},f:function(){for(var z=xg.a6(hn.d(this._ac.xg())),M=this._ac.x9();M.i1();M.i2()){var U=M.i9();if(U.ak()>1){for(var h=0,$=U.ae();$;$=$.a7()){var s=$.a2();1===s.ak()&&1===s.ao()&&h++}if(h>1)for(var w=U.ae();w;w=w.a7()){var V=w,t=V.a2();if(1===t.ak()&&1===t.ao()){for(;1===t.ak()&&1===t.ao();t=V.a2())z.i8(V,U),V=t.ae();z.i8(V,U)}}}}return z},k:function(){for(var S=1;S<this._ad.length;S++){for(var A=this._ad[S],E=A.ah();E.i1();E.i2()){var e=E.i6();this._n[e.al()]=this.a5(e,A.ay(),e.am(),this._ad[S-1].ay()),this._n[e.al()]+=this._ab[e.al()]/(3*this._ad[S-1].ay())}this.a3(A,this._p)}return this.a1()},a5:function(U,j,y,D){var d=0;if(0===y.i7())d=D*this._ab[U.al()]/j;else{for(;y.i1();y.i2()){var O=y.i8();d+=O.a2()===U?this._ab[O.a3().al()]:this._ab[O.a2().al()]}d/=y.i7()}return d},a7:function(i){hn.f(this._ab,i,i.length)},b3:function(Q){hn.f(Q,this._ab,Q.length)},r:function(){var X=hn.a(this._ab.length);return this.a7(X),X},d:function(){for(var o=0;o<this._ad.length;o++)for(var Z=0,x=this._ad[o].ah();x.i1();)this._ab[x.i6().al()]=Z,x.i2(),Z++},b1:function(){for(var n=0;n<this._ad.length;n++){for(var e=this._ad[n],v=e._b;v;v=v.a()){var d=v.d();this._f[this._ab[d.al()]]=d}for(var z=0,B=e._b;B;)B.c(this._f[z]),B=B.a(),z++}},a3:function(K,w){for(var q=K.ah(),c=0;c<K.ay();q.i2())this._f[c]=q.i6(),c++;hn.s(this._f,K.ay(),w);for(var D=0,z=K._b;z;)z.c(this._f[D]),this._ab[this._f[D].al()]=D,z=z.a(),D++}}),rk.b=function(O,G){if(!O)return 0;var B=O.a()?j(G.x):0,M=O.a()?j(G.y):0;return O.e()?1e4-M:O.f()?-1e4+M:O.c()?-2e4-B:B},rk.a=function(A,m){if(!A)return 0;var C=A.a()?j(m.x):0,x=A.a()?j(m.y):0;return A.e()?1e4+x:A.f()?-1e4-x:A.d()?-2e4-C:C};var Yl=function(){Yl.superClass.constructor.call(this),this._i6=0,this._i3=2147483647,this._i0=60,this._iz=20,this._i2=20,this._i4=20,this.i5(!1),this._i7=new _r,this._i1=new rk,this._i8=new cp};I(Yl,mo,{j2:function(){return this._i2},i4:function(){return!0},i3:function(P){this._i6=(new Date).getTime(),Cq.d(P,!1);var f=P.xk(),o=P.xk(),V=P.xl(),O=new Xq,i=new xs(P,f,o,V);i.a6(this.j2()),this._i8.i3(this._iz),this._i8.i6(this._i0),this._i8.i4(this._i2),this._i8.i5(this._i4),this._i8.i2(o);for(var K=this._i7.i1(P,f,O),e=O.c1();e.i1();e.i2()){var Y=e.i8();V.i7(Y,!0);var q=P.gn(Y);P.gt(Y,P.gk(Y)),P.gz(Y,q)}this.a2(P,f,o),K=i.a9(K);var N=this.j1(P,f,K);N=i.a5(N),N=i.b3(N),this._i8.i1(P,N,f),N=i.g2(N),i.e2(N),this.b(P,o),this.w(P),this.a1(P,O),i.e1(),P.xj(V),P.xi(o),P.xi(f)},j1:function(i,t,x){if(this._i1 instanceof rk){var D=this._i1,T=(new Date).getTime()-this._i6;D.ib(this._i3-T)}var e=this._i1.ia(i,t,x);return e},a1:function(t,S){for(var B=S.c1();B.i1();B.i2()){var C=B.i8(),F=t.gs(C),s=t.gl(C);t.x3(C);var y=t.gp(C);t.s5(C,y.a()),t.gy(C,F),t.gx(C,s)}},b:function(h,b){for(var o=h.x9();o.i1();o.i2()){var G=o.i9(),t=b.i1(G);if(t&&!h.xp(t)){for(var J=G.am().i8().a2();b.i1(J);J=G.am().i8().a2())G=J;h.u1(t);for(var P=G.ae(),_=new em;b.i1(P.a3());P=P.a3().ag()){var w=h.gs(P);_.aa(w),_.az(h.gf(P));var y=h.gl(P);y.equals(w)||_.aa(y)}var m=h.gs(P);_.aa(m),_.az(h.gf(P));var U=h.gl(P);U.equals(m)||_.aa(U),h.m1(t,_)}}for(var Z=h.x9();Z.i1();Z.i2())b.i1(Z.i9())&&h.x4(Z.i9())},w:function(I){for(var O=I.xf();O.i1();O.i2()){var p=O.i8(),E=I.g2(p);if(E.i1()>0){var Z=new J,c=I.gc(p),W=c.c(),Y=W.i6();W.i2();var P=Y.x,a=Y.y;if(W.i1()){var U=W.i6(),N=U.x,z=U.y;for(W.i2();W.i1();W.i2()){var g=W.i6(),e=g.x,V=g.y,T=(P-e)*(z-V)/(a-V)+e;s(T-N)>=1&&(Z.add(U),P=N,a=z),U=g,N=e,z=V}}Z.size()<E.i1()&&I.s5(p,new w(Z))}}},a2:function(l,w,j){var B=l.g8().c1();for(B.i5();B.i1();B.i3()){var s=B.i8().a2(),m=B.i8().a3(),z=w.i2(m)-w.i2(s);if(z>1){for(var M,O,Y=s;z>1;z--)M=l.xm(),l.s7(M,1,1),l.s4(M,Hg._A),O=l.xo(Y,M),Y===s&&l.gt(O,l.gn(B.i8())),w.i7(M,w.i2(Y)+1),j.z1(M,B.i8()),Y=M;O=l.xo(M,m),l.gz(O,l.gk(B.i8())),l.h1(B.i8())}}}});var se=function(d,t,c,J){this._i=20,this._j=d,this._g=t,this._a=c,this._h=J};I(se,G,{a1:function(v){this._i=v},b2:function(F){return this._e?this._e.i4(F):!1},a3:function(V){return this._f?this._f.i1(V):x},d:function(){this._j.xi(this._f),this._j.xi(this._e)}});var Wh=function(){this._c=0,this._i=0,this._g=0,this._j=0,this._f=0,this._h=0,this._d=0,this._e=0,this._a=0,this._b=0};I(Wh,G,{a:function(){return this._c-this._g-this._f-this._a},b:function(){return this._i+this._j+this._h+this._b}});var Vg=function(){this._d=new Xq,this._b=new Xq,this._c=new Xq,this._e=new Xq};I(Vg,G,{});var jf=function(Y,B,X,l,D,K,p,q){this._d=Y,this._a=B,this._b=X,this._h=l,this._f=D,this._c=K,this._e=p,this._g=q};I(jf,G,{});var hn={a:function(S,x){for(var c=[],b=0;S>b;b++)c[b]=x||0;return c},b:function(k){for(var N=[],U=0;k>U;U++)N[U]=!1;return N},c:function(z,M){if(z instanceof S)return z.x<M.x?-1:z.x>M.x?1:z.y<M.y?-1:z.y<=M.y?0:1;if(z instanceof V)return M.width>z.width?-1:M.width<z.width?1:M.height>z.height?-1:M.height>=z.height?0:1;if(z instanceof g)return z.x<M.x?-1:z.x>M.x?1:z.y<M.y?-1:z.y>M.y?1:M.width>z.width?-1:M.width<z.width?1:M.height>z.height?-1:M.height>=z.height?0:1;throw""},d:function(C){for(var d=[],h=0;C>h;h++)d[h]=x;return d},e:function(p,B){for(var D=[],K=0;p>K;K++)D[K]=hn.a(B);return D},f:function(x,N,Y){for(var d=0;Y>d;d++)N[d]=x[d]},s:function(T,C,q){var j=[];hn.f(T,j,C),j.sort(q),hn.f(j,T,C)},n:function(X,E){return X-E}};c.layout.AutoLayout=function(z,h){z instanceof c.DataModel?this.dm=z:this.gv=z,this.options=h||{}},I(v+"AutoLayout",G,{_repulsion:1,_type:x,_offsetX:x,_offsetY:x,_xf:0,_yf:0,_animate:!0,_frames:x,_interval:x,_duration:x,_easing:x,isAnimate:function(){return this._animate},setAnimate:function(z){this._animate=z},getFrames:function(){return this._frames},setFrames:function(S){this._frames=S},getInterval:function(){return this._interval},setInterval:function(x){this._interval=x},getDuration:function(){return this._duration},setDuration:function(z){this._duration=z},getEasing:function(){return this._easing},setEasing:function(o){this._easing=o},getRepulsion:function(){return this._repulsion},setRepulsion:function(U){this._repulsion=U},getOffsetX:function(){return this._offsetX},setOffsetX:function(O){this._offsetX=O},getOffsetY:function(){return this._offsetY},setOffsetY:function(j){this._offsetY=j},getNodeSize:function(L){var j,y=this.gv;L.getChildrenRect&&(j=L.getChildrenRect(),j&&n.grow(j,15)),!j&&y&&y.getDataUIBounds&&(j=y.getDataUIBounds(L)),j||(j=L.getRect(),n.grow(j,15));var l,s,T;return l=L.s("autolayout.gap"),s=L.s("autolayout.hgap"),T=L.s("autolayout.vgap"),l!==u||s!==u||T!==u?(s=(s||0)+(l||0),T=(T||0)+(l||0)):(l=this.options.gap||0,s=l+(this.options.hgap||0),T=l+(this.options.vgap||0)),s&&(j.x-=s,j.width=j.width+2*s),T&&(j.y-=T,j.height=j.height+2*T),j},isLayoutable:function(Y){var M=this.gv;return M&&!M.isVisible(Y)?!1:Y.s("layoutable")===!1?!1:Y instanceof L?Y.getSourceAgent()&&Y.getTargetAgent()&&!Y.isLooped()?!0:!1:Y instanceof Q?Y.getHost()?!1:M?M.isMovable(Y):!0:!1
},getType:function(){return this._type},getLayoutDatas:function(){var l,t=this,z=t.gv,f=t.dm,y=!1,P=new J;return z?z.sm().size()>1?(y=!0,l=z.sm().getSelection()):l=z.dm().getDatas():f.sm().size()>1?(y=!0,l=f.sm().getSelection()):l=f.getDatas(),t._xf=t._yf=d,l.each(function(r){if(t.isLayoutable(r)&&(P.add(r),y&&r instanceof Q)){var c=r.p();c.x<t._xf&&(t._xf=c.x),c.y<t._yf&&(t._yf=c.y)}}),y||(t._xf=t._offsetX==x?50:t._offsetX,t._yf=t._offsetY==x?50:t._offsetY),P},getLayoutResult:function(M){var A={};return this.layoutImpl(M,x,A),A},layout:function(c,S){return this.layoutImpl(c,S)},layoutImpl:function(j,B,R){this._type=j;var p=this,X={},Z=q(j),c=p.getLayoutDatas(),K=new qi(p,c);if(!Z)return!1;c=K.p();var f=new sg(p,c,j);try{Z.i2(f)}catch(Q){return K.r(),B&&B(),!1}var v,z,g,x,a=f._a;for(v in a)z=a[v],x=f.g4(z),X[v]={x:x.x+p._xf,y:x.y+p._yf};if(j===D||j===U||j===i){var E,y=k(j),t=d,I=d,P=2*p._repulsion;for(v in X)z=a[v],g=X[v],x=y.tf(g),g.x=x.x,g.y=x.y,j===D||j===U?(E=x.x-f.g9(z)/P,t>E&&(t=E),E=x.y-f.gj(z)/P,I>E&&(I=E)):(E=x.x-f.gj(z)/P,t>E&&(t=E),E=x.y-f.g9(z)/P,I>E&&(I=E));for(v in X)z=a[v],g=X[v],g.x=g.x-t+p._xf,g.y=g.y-I+p._yf}if(!R&&p._animate){var N=p.gv,Y={};for(v in X)Y[v]=a[v].node.p();N&&(N._autoLayouting=1),n.startAnim({duration:p._duration,frames:p._frames,interval:p._interval,easing:p._easing,finishFunc:function(){K.r(),B&&B(),N&&(delete N._autoLayouting,N.onAutoLayoutEnded())},action:function(r){for(v in X){var L=Y[v],o=X[v];a[v].node.p(L.x+(o.x-L.x)*r,L.y+(o.y-L.y)*r)}}})}else{for(v in X)z=a[v],g=X[v],R?R[z.node.getId()]=g:z.node.p(g);K.r(),B&&B()}return!0}})}("undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:(0,eval)("this"),Object);