!function(s,j,B){"use strict";function I(b){var q,T=b.key.toLowerCase(),O=b.keyCode;return O>=65&&90>=O?(q=String.fromCharCode(O).toLowerCase(),q!==T?q:T):O>=48&&57>=O?(q=String.fromCharCode(O),q!==T&&(T=q),T):O>=96&&105>=O?(q=String.fromCharCode(O-48),q!==T&&(T=q),"numpad"+T):(q=C[O],q!==B?q:T)}function p(b){var k=0;if("keydown"===b.type)b.ctrlKey&&(k+=8),b.metaKey&&(k+=4),b.altKey&&(k+=2),b.shiftKey&&(k+=1);else if("keyup"===b.type){k=K;var s=E(I(b));"control"===s&&8&k?k-=8:"meta"===s&&4&k?k-=4:"alt"===s&&2&k?k-=2:"shift"===s&&1&k&&(k-=1)}K=k}function E(X){return"spacebar"===X?X=" ":["left","right","up","down"].indexOf(X)>=0?X="arrow"+X:"os"===X?X="meta":"help"===X&&(X="insert"),X}function O(Z,_){t(Z)&&(Z=Z.split(" + "));var D=Z.length,z=0;if(_)z=16;else for(var Y=0;D-1>Y;Y++){var F=Z[Y].toLowerCase(),b=g[F];null!=b&&(z+=1<<b)}var u=E(Z[D-1].toLowerCase());return z+"_"+u}function n(l,o){var a=0;l.ctrlKey&&(a+=8),l.metaKey&&(a+=4),l.altKey&&(a+=2),l.shiftKey&&(a+=1),o&&(a=16);var g=E(I(l));return a+"_"+g}var M="ht",N=s[M],W=N.Default,t=W.isString,a=N.Default.isFunction,u=(N.Default.clone,"ctrl"),P="meta",V="alt",q="shift",C={192:"`",189:"-",187:"=",219:"[",221:"]",220:"\\",186:";",222:"'",188:",",190:".",191:"/",111:"numpad/",106:"numpad*",109:"numpad-",107:"numpad+",110:"numpad.",32:" "},K=0;s.addEventListener("keydown",p,!0),s.addEventListener("keyup",p,!0);var g={};g[u]=3,g[P]=2,g[V]=1,g[q]=0,N.ShortcutManager=function(){this._actionMap={},this._lastEvent=B,this._keyEventNotifier=new N.Notifier,this._target=B,this._global=!1,this._stopPropagation=!1},W.def("ht.ShortcutManager",j,{registerShortcut:function(T,b,d){T&&(T=O(T,d),a(b)&&(b={action:b}),this._actionMap[T]=b)},unregisterShortcut:function(t,n){t&&(t=O(t,n),this._actionMap[t]=B)},clearShortcuts:function(){this._actionMap={},this._keyEventNotifier=new N.Notifier},enableShortcut:function(K){this.disableShortcut();var V=this._target=K||this._target||s;this._global=V===s;var l=this._keydownListenerFunc=this.keydownListener.bind(this),o=this._keyupListenerFunc=this.keyupListener.bind(this);V.addEventListener("keydown",l,!0),V.addEventListener("keyup",o,!0)},disableShortcut:function(){var o=this._target;o&&(o.removeEventListener("keydown",this._keydownListenerFunc,!0),o.removeEventListener("keyup",this._keyupListenerFunc,!0))},keydownListener:function(F){if(!N.Default.ignoreKeyEvent&&(this.fireKeyEvent({kind:"down",event:F}),!N.Default.ignoreShortcuts)){this._lastEvent=F;var S=n(F),k=this._actionMap[S];if(k||(S=n(F,!0),k=this._actionMap[S]),k){var y={kind:"down",shortcut:S,event:F};for(var p in k)y[p]=k[p];var g=this.beforeShortcutAction(F,y);g!==!1&&(k.action(y),this.stopPropagation&&(F.preventDefault(),F.stopImmediatePropagation()))}}},keyupListener:function(W){if(!N.Default.ignoreKeyEvent&&(this.fireKeyEvent({kind:"up",event:W}),!N.Default.ignoreShortcuts)){for(var h="_"+E(I(W)),t=j.keys(this._actionMap),R=0,Q=t.length;Q>R;R++){var H=t[R];if(!(H.indexOf(h)<0)){var l=this._actionMap[H];if(l){var X={kind:"up",shortcut:H,event:W};for(var F in l)X[F]=l[F];var O=this.beforeShortcutAction(W,X);O!==!1&&l.action(X)}}}this._lastEvent=B}},beforeShortcutAction:function(){},addKeyListener:function(k,o,S){this._keyEventNotifier.add(k,o,S)},removeKeyListener:function(Q,I){this._keyEventNotifier.remove(Q,I)},fireKeyEvent:function(l){this._keyEventNotifier.fire(l)},isShiftDown:function(){return N.ShortcutManager.isShiftDown()},isCtrlDown:function(){return N.ShortcutManager.isCtrlDown()},isMetaDown:function(){return N.ShortcutManager.isMetaDown()},isAltDown:function(){return N.ShortcutManager.isAltDown()}}),j.defineProperties(N.ShortcutManager.prototype,{global:{get:function(){return this._global}},stopPropagation:{get:function(){return this._stopPropagation},set:function(N){this._stopPropagation=N}}}),N.ShortcutManager.getKey=function(d){return E(I(d))},N.ShortcutManager.isShiftDown=function(){return K&!0},N.ShortcutManager.isCtrlDown=function(){return K&!0},N.ShortcutManager.isMetaDown=function(){return K&!0},N.ShortcutManager.isAltDown=function(){return K&!0}}("undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:(0,eval)("this"),Object);