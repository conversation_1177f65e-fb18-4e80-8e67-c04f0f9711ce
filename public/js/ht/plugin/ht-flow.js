!function(v){"use strict";var J=v.ht,B=<PERSON><PERSON>,h=B.getInternal(),e=h.ui(),E=null,Q="__segmentLengths",C="__lineTotalLength",m="__linePoints",b="__distance0",l="flow.count",V="flow.step",d="flow.element.max",G="flow.element.count",Z="flow.element.min",a="flow.element.space",c="flow.element.autorotate",g="flow.element.background",Y="flow.element.shadow.max",P="flow.element.shadow.min",y="flow.element.shadow.begincolor",i="flow.element.shadow.endcolor",U="flow.element.shadow.visible",W="flow.element.image",n="flow",D=J.Math.Vector2,A=new D;new D,new D,J.List;var S=J.Default._edgeProtectMethod,K=S.getStraightLinePoints,w=function(u,C){for(var c=[],$=u.length,Q=0;$>Q;Q++){var X=u[Q];X._as&&(X=X._as);for(var F=X[0],p=1;p<X.length;p++)c.push([F,X[p]]),F=X[p]}for(var K=[],Q=0;Q<c.length;Q++){var Z=N(c[Q][0],c[Q][1],C);K.push(Z)}return{distance:K,segments:c}},I=function(l,A,d){if(l){for(var $,e=w(l,A),G=e.distance,U=e.segments,T=1/0,W=null,H=0,y=G.length;y>H;H++){var D=G[H];D.z<T&&($=H,T=D.z,W=D)}if(null==d&&(d=.1),W.z<d){for(var u=0,k=0;$>=k;k++)u+=B.getDistance(U[k][0],$>k?U[k][1]:W);return u}}},N=function(f,M,d){var v=f.x,S=f.y,B=M.x,l=M.y,o=d.x,D=d.y,U=B-v,P=l-S,p=Math.sqrt(U*U+P*P),Q=U/p,b=P/p,X=(-v+o)*Q+(-S+D)*b,s={x:v+X*Q,y:S+X*b};return s.x>=Math.min(f.x,M.x)&&s.x<=Math.max(f.x,M.x)&&s.y>=Math.min(f.y,M.y)&&s.y<=Math.max(f.y,M.y)||(s.x=Math.abs(s.x-f.x)<Math.abs(s.x-M.x)?f.x:M.x,s.y=Math.abs(s.y-f.y)<Math.abs(s.y-M.y)?f.y:M.y),U=o-s.x,P=D-s.y,s.z=Math.sqrt(U*U+P*P),s},j=function(h,F){if(h){var u=w(h,F).distance,P=1/0,M=null;return u.forEach(function(H){H.z<P&&(P=H.z,M=H)}),M}},H=S.calculateLineLength,x=S.calcSegmentIndexByDistance,z=S.calculatePointAlongLine,R=function(U,o,F){if(!U)return F;if(0===o){var l=U[0][0],u=U[0][1];return F+Math.atan2(u.y-l.y,u.x-l.x)}if(100===o){U=U[U.length-1];var l=U[U.length-2],u=U[U.length-1];return F+Math.atan2(u.y-l.y,u.x-l.x)}for(var G=0,Q=[],y=U.length,R=0;y>R;R++){var k=U[R],C=H(k);G+=C,Q.push(C)}for(var N=G*o/100,v=x(N,Q),V=0,_=0;v>_;_++)V+=Q[_];N-=V;for(var T=z(U[v],N),K=U[v],w=0,Y=0,A=0;A<K.length-1;A++){var e=K[A],L=K[A+1],g=L.x-e.x,P=L.y-e.y,O=Math.sqrt(g*g+P*P);if(w+=O,w>N){Y=A;break}}var a=K[Y];return F+Math.atan2(T.y-a.y,T.x-a.x)},u=function(l){var M=0;if(l)if(Array.isArray(l[0]))for(var X=l.length,q=0;X>q;q++){var I=l[q],U=H(I);M+=U}else M=H(l);return M},f=function(g,R,v,F){return A.set(R,v).rotateAround(null,F),g?{x:g.x+A.x,y:g.y+A.y}:{x:A.x,y:A.y}},r=function(p){var I=p._data,g=K(p);if(g){I.s("flow.reverse")&&(g.reverse(),g.forEach(function(F){F.reverse()}));for(var k=0,w=[],x=g.length,f=0;x>f;f++){var r=g[f],D=H(r);k+=D,w.push(D)}if(I[Q]=w,I[C]=k,I[m]=g,I instanceof J.Edge){var v=B.unionPoint(g),O=v.x+v.width/2,l=v.y+v.height/2;I.$10e={x:O,y:l}}o(p,!0)}},X=S.getPercentPosition,o=function(q,o){var F=q._data,J=F[C],_=F.s(l),z=F.s(V),M=0,K=F[Q],m=F.s(d),g=F.s(Z),j=F.s(G),N=(m-g)/(j-1),Y=[];if(K){if(1===j)Y.push(m);else if(2===j)Y.push(m),Y.push(g);else{if(!(j>2))return;Y.push(m);for(var y=j-2;y>0;y--)Y.push(g+N*y);Y.push(g)}var n=0,S=0;Y.forEach(function(G){j-1>n&&(S+=F.getFlowElementSpace(G)),n++}),S+=(m+g)/2,M=(J-_*S+S)/_;var f=q[b];for(null==f&&(f=0),o||(f+=z);f>J+S;){var L=q._overCount;L?L++:L=1,L>=_&&(L=null),q._overCount=L,F.s("flow.autoreverse")?L?f-=M+S:(f=0,F.s("flow.reverse",!F.s("flow.reverse"))):f-=M+S}q[b]=f}},$="prototype",F=J.graph.GraphView[$],O=J.Data[$],p=e.DataUI[$],T=e.ShapeUI[$],q=e.EdgeUI[$],L=J.DataModel[$],M=J.Style;M[d]==E&&(M[d]=7),M[Z]==E&&(M[Z]=0),M[l]==E&&(M[l]=1),M[V]==E&&(M[V]=3),M[G]==E&&(M[G]=10),M[a]==E&&(M[a]=3.5),M[c]==E&&(M[c]=!1),M[g]==E&&(M[g]="rgba(255, 255, 114, 0.4)"),M[y]==E&&(M[y]="rgba(255, 255, 0, 0.3)"),M[i]==E&&(M[i]="rgba(255, 255, 0, 0)"),M[U]==E&&(M[U]=1),M[Y]==E&&(M[Y]=22),M[P]==E&&(M[P]=4),F.calculatePointLength=function(c,o,P){var D=this.getDataUI(c),i=K(D);return I(i,o,P)},B.calculatePointLength=function(e,y,c,M){var o=K(E,e,y);return I(o,c,M)},B.calculateClosestPointOnLine=N,F.calculateClosestPoint=function(b,R){var Y=this.getDataUI(b),L=K(Y);return j(L,R)},B.calculateClosestPoint=function(J,u,p){var z=K(E,J,u);return j(z,p)},F.getPercentAngle=function(i,l){var q=this.getDataUI(i),E=K(q);return i.getRotation?i.getRotation():0,R(E,l,i.getRotation?i.getRotation():0)},B.getPercentAngle=function(i,L,$){var B=K(E,i,L);return R(B,$,0)},F.calculateLength=function(H){var o=this.getDataUI(H),G=K(o);return u(G)},B.calculateLength=function(D,k){var Q=K(E,D,k);return u(Q)},F.getPercentPosition=function(m,S){var Z=this.getDataUI(m),e=K(Z);return X(e,S)},B.getPercentPositionOnPoints=function(R,W,_){var j=K(E,R,W);return X(j,_)};var s=function(o){var m=o.data,J=this.dm();if(m&&"add"===o.kind){var a=J.$3e;a&&m.s(n)&&a.indexOf(m)<0&&a.push(m)}"clear"===o.kind&&(J.$3e=[])},t=function(Z){var u=Z.property,l=Z.data,I=Z.newValue,k=this.dm().$3e;if(k&&"s:flow"===u)if(I)k.indexOf(l)<0&&k.push(l);else for(var M=k.length,d=0;M>d;d++)if(k[d]===l){k.splice(d,1);break}},_=F.setDataModel;F.setDataModel=function(c){var w=this,H=w._dataModel;if(H!==c){H&&(H.umm(s,w),H.umd(t,w),H.$3e=[]),c.mm(s,w),c.md(t,w);var O=c.$3e=[];c.each(function(f){f.s(n)&&O.indexOf(f)<0&&O.push(f)}),_.call(w,c)}},O.getFlowElementSpace=function(){return this.s(a)};var k=function(L){var e=this,W=e._data,F=W[C],t=W[Q],q=W[m],K=W.s(l),n=0,I=e[b],_=W.s(d),A=W.s(Z),$=W.s(G),D=W.s(P),p=W.s(Y),u=W.s(c),r=(p-D)/($-1),U=(_-A)/($-1),y=W.getRotation?W.getRotation():0,v=W.getPosition?W.p():W.$10e,R=[],a=[];if(I!=E){if(1===$)R.push(_);else if(2===$)R.push(_),R.push(A);else{if(!($>2))return;R.push(_);for(var B=$-2;B>0;B--)R.push(A+U*B);R.push(A)}if(1===$)a.push(p);else if(2===$)a.push(p),a.push(D);else{if(!($>2))return;a.push(p);for(var B=$-2;B>0;B--)a.push(D+r*B);a.push(D)}var S=0,X=0;R.forEach(function(s){$-1>S&&(X+=W.getFlowElementSpace(s)),S++}),X+=(_+A)/2,n=(F-K*X+X)/K,L.save();for(var B=0;K>B;B++){var J=I,H=0,w=e._overCount,s=0;W.s("flow.autoreverse")&&w&&w>K-(B+1)||(J-=B*(n+X),S=0,R.forEach(function(o){var U=J-H;if(U>=0&&F>U){var I=!0,N=x(U,t);s=0;for(var g=0;N>g;g++)s+=t[g];if(U-=s,I){var d=z(q[N],U),C=y;if(u){for(var D=q[N],r=0,Y=0,P=0;P<D.length-1;P++){var O=D[P],V=D[P+1],l=V.x-O.x,h=V.y-O.y,B=Math.sqrt(l*l+h*h);if(r+=B,r>U){Y=P;break}}var p=D[Y];C+=Math.atan2(d.y-p.y,d.x-p.x)}y&&(d=f(v,d.x-v.x,d.y-v.y,y)),e.$5e(L,d,o,a[S],C)}}H+=W.getFlowElementSpace(R[S]),S++}))}L.restore()}},Re=q._80o;q._80o=function(t){Re.call(this,t);var M=this,D=M._data,K=M.gv;D.s(n)&&K.$7e!=E&&k.call(M,t)};var Xj=T._80o;T._80o=function(x){Xj.call(this,x);var P=this,S=P._data,J=P.gv;S.s(n)&&J.$7e!=E&&k.call(P,x)};var Mi=q._79o,Cf=T._79o,jf=function(){var _=this,s=_._data,A=s.s(d),o=!1,u=E;if(_._6I||(o=!0),u=s instanceof J.Edge?Mi.call(_):Cf.call(_),s.s(n)&&o){var y=s.s(Y),K=s.s(U);K&&y>A&&(A=y),A>0&&B.grow(u,Math.ceil(A/2)),r(_)}return!s.s(n)&&o&&(s[Q]=s[C]=s[m]=_[b]=E),u};T._79o=jf,q._79o=jf,p.$5e=function(Z,l,I,D,Y){var m=this,N=m._data,L=m.gv,X=N.s(g),v=N.s(y),j=N.s(i),M=N.s(U),o=L.$8e,p=N.s(W);if(o==E&&(o=L.$8e={}),Z.beginPath(),p!=E){var k=B.getImage(p),q=I/2;Z.translate(l.x,l.y),Z.rotate(Y),Z.translate(-l.x,-l.y),B.drawImage(Z,k,l.x-q,l.y-q,I,I,N),Z.translate(l.x,l.y),Z.rotate(-Y),Z.translate(-l.x,-l.y)}else if(L.__flowBatch){var x=L.__flowBatchGroup;x||(x=L.__flowBatchGroup={});var r=[l.x,l.y,I/2];x[X]?x[X].push(r):x[X]=[r]}else Z.fillStyle=X,Z.arc(l.x,l.y,I/2,0,2*Math.PI,!0),Z.fill();if(M){var f=22,H=f+"_"+v+"_"+j,$=o[H];if($==E){var b=document.createElement("canvas");h.setCanvas(b,f,f);var J=b.getContext("2d"),F=f/2,C=F,z=F;h.translateAndScale(J,0,0,1),J.beginPath();var u=J.createRadialGradient(C,z,0,C,C,F);u.addColorStop(0,v),u.addColorStop(1,j),J.fillStyle=u,J.arc(C,z,F,0,2*Math.PI,!0),J.fill(),$=o[H]=b}var q=D/2;B.drawImage(Z,$,l.x-q,l.y-q,D,D,N)}},F.$9e=function(){var y=this,N=y.dm().$3e;y._24I,N.forEach(function(l){y._24I[l._id]=l}),y.iv()};var eb=L.prepareRemove;L.prepareRemove=function(M){eb.call(this,M);var g=M._dataModel,m=g.$3e;if(m)for(var y=m.length,E=0;y>E;E++)if(m[E]===M){m.splice(E,1);break}},F.setFlowInterval=function(q){var _=this,S=_.$11e;_.$11e=q,_.fp("flowInterval",S,q),_.$7e!=E&&(clearInterval(_.$7e),delete _.$7e,_.enableFlow(q))},F.getFlowInterval=function(){return this.$11e},F.enableFlow=function(C){var G=this,_=G.dm(),Q=_.$3e;G.$7e==E&&(Q.forEach(function(T){var J=G.getDataUI(T);r(J)}),G.$7e=setInterval(function(){_.$3e.forEach(function(D){o(G.getDataUI(D))}),G.$9e()},C||G.$11e||50))},F.disableFlow=function(){var i=this;clearInterval(i.$7e),delete i.$7e;var n=i.dm().$3e;n&&i.$9e()};var sf=function(){this.__flowBatchGroup={}},xq=function(w){var f=this.__flowBatchGroup;if(f){w.save();for(var o in f){w.fillStyle=o,w.beginPath();var D=f[o];D.forEach(function(M){w.moveTo(M[0],M[1]),w.arc(M[0],M[1],M[2],0,2*Math.PI,!0)}),w.fill()}w.restore()}};F.setFlowBatch=function(O){var j=this;!!j.__flowBatch!=!!O&&(j.__flowBatch=O,O?(j.addBottomPainter(sf),j.addTopPainter(xq)):(j.removeBottomPainter(sf),j.removeTopPainter(xq)))}}("undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:(0,eval)("this"),Object);