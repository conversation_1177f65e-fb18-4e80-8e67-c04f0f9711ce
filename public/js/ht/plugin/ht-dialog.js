!function(r,i){"use strict";var J="ht",j=r[J],u="px",$="left",x="top",Y="bottom",t="right",w="innerHTML",p="className",Q="",v="width",k="height",y="string",H="position",G="absolute",B="0",M="opacity",h="background",K=j.<PERSON>,z=K.getInternal(),q=j.Color,s=K.animate,T=K.isTouchable,n=K.isTouchEvent,l=null,C=(Math.sqrt,r.parseInt),d=r.setTimeout,O=(Math.round,q.titleIconBackground),R=function(H){return typeof H===y||H instanceof String},f=function(){return document},S=function(t){return f().createElement(t)},A=function(){var q=S("div");return q.tabIndex=-1,q.style.outline="none",q},e=function(){return S("canvas")},I=function(x,G){F(x,"-webkit-transform",G),F(x,"-ms-transform",G),F(x,"transform",G)},F=function(X,R,D){X.style.setProperty(R,D,l)},V=function($,I,R){K.def(j.widget[$],I,R)},N=function(K,i){K.appendChild(i)},U=function(){return f().documentElement},D=function(){return U().clientWidth},b=function(){return U().clientHeight},L=z.addEventListener,_=z.removeEventListener,c=function(w){var T=w.touches[0];return T?T:w.changedTouches[0]};z.addMethod(K,{dialogCloseIcon:{width:100,height:100,comps:[{type:"shape",points:[10,10,90,90],segments:[1,2],borderWidth:8,borderColor:O},{type:"shape",points:[90,10,10,90],segments:[1,2],borderWidth:8,borderColor:O}]},dialogMaximizeIcon:{width:100,height:100,comps:[{type:"rect",rect:[10,15,80,75],borderWidth:6,borderColor:O},{type:"rect",rect:[10,10,80,20],background:O}]},dialogRestoreIcon:{width:100,height:100,comps:[{type:"rect",rect:[10,34,56,56],borderWidth:8,borderColor:O},{type:"rect",rect:[10,34,56,14],background:O},{type:"rect",rect:[34,10,56,14],background:O},{type:"rect",rect:[82,10,8,56],background:O},{type:"rect",rect:[66,62,24,8],background:O}]},dialogTitleLabelColor:K.labelSelectColor,dialogTitleLabelFont:(T?"18":"14")+"px arial, sans-serif",dialogContentLabelFont:K.labelFont,dialogTitleBackground:q.titleBackground,dialogHeaderBackground:q.headerBackground,dialogButtonBackground:"#1ABC9C",dialogButtonSelectBackground:"#16A085",dialogButtonLabelColor:"#fff"},!0);var Z=function(C){var U=this;U.$1d=C,U.addListeners()};K.def(Z,i,{ms_listener:1,getView:function(){return this.$1d.getView()},clear:function(){delete this.$2d,delete this.$3d,delete this.$4d,delete this.$5d},handle_touchstart:function(k){var e=this,J=e.$1d,T=J.$6d,F=J._config,m=J.$7d,Z=k,_=k.target;if(m.contains(_)||K.preventDefault(k),K.isLeftButton(k)){n(k)&&(Z=c(k));var L=e.$8d={x:Z.pageX,y:Z.pageY};e.$9d={x:L.x,y:L.y},!F.maximized&&F.draggable&&T.contains(_)&&(e.$4d=!0,K.startDragging(e,k)),e.handle_mousemove(k)&&(e.$2d=!0,K.startDragging(e,k)),m.contains(k.target)||J.$41d.focus()}},handle_mousedown:function(X){this.handle_touchstart(X)},handle_touchend:function(U){if(K.isLeftButton(U)&&!this.$5d&&!this.$3d){var m=this,L=m.$1d,A=U.target,B=L._config,q=L.$18d,h=L.$16d;for(B.closable&&q.contains(A)&&L.hide(),B.maximizable&&h.contains(A)&&(B.maximized?L.restore():L.maximize());A&&(A.className||"").indexOf("dialog-button")<0;)A=A.parentNode;A&&A.buttonItem&&L.action&&L.action(A.buttonItem,U),delete m.$8d,delete m.$9d}},handle_mouseup:function(Q){this.handle_touchend(Q)},handleWindowTouchEnd:function(X){var x=this,V=x.$1d,R=V.$10d;x.$2d&&x.$3d?R.fire({kind:"endResize",target:V,originEvent:X}):x.$4d&&x.$5d&&R.fire({kind:"endMove",target:V,originEvent:X}),this.clear()},handleWindowMouseUp:function(x){this.handleWindowTouchEnd(x)},handle_mousemove:function(A){var W=this.$1d._config;if(W.maximized)return!1;if("w"!==W.resizeMode&&"h"!==W.resizeMode&&"wh"!==W.resizeMode)return!1;var E=this,g=E.getView(),q=g.querySelector(".resize-area"),f=q.getBoundingClientRect(),$={x:f.left,y:f.top,width:f.width,height:f.height};A=n(A)?c(A):A;var B=A.clientX,O=A.clientY,y={x:B,y:O};return K.containsPoint($,y)?(F(g,"cursor","nwse-resize"),!0):(F(g,"cursor",Q),void 0)},handleWindowTouchMove:function(r){r.preventDefault();var E=r;n(r)&&(E=c(r));var B=this,A=B.$8d,g=B.$9d;if(!(g.x==A.x&&g.y==A.y&&K.getDistance(g,{x:E.pageX,y:E.pageY})<=1)){var M=B.$1d,U=M._config,p=M.$21d,P=U.resizeMode||"wh",z=E.pageX-A.x,S=E.pageY-A.y;if(B.$2d){var W=p.offsetWidth,X=p.offsetHeight,i=W+z,I=X+S;if(i=Math.max(i,50),I=Math.max(I,50),"center"===U.position||U.position==l){var k={},L=p.getBoundingClientRect();k.x=L.left,k.y=L.top,U.position=k}"w"===P?(M.setSize(i,X),A.x+=i-W):"h"===P?(M.setSize(W,I),A.y+=I-X):"wh"===P&&(M.setSize(i,I),A.x+=i-W,A.y+=I-X),B.$3d?M.$10d.fire({kind:"betweenResize",target:M,originEvent:r}):(B.$3d=!0,M.$10d.fire({kind:"beginResize",target:M,originEvent:r}))}else if(B.$4d){var v=p.getBoundingClientRect(),f=v.width,T=v.height,m=D(),Y=b(),o=C(p.style.left)||0,O=C(p.style.top)||0,R=o+z,Q=O+S,q=M.adjustPosition({x:R,y:Q},{width:f,height:T},{width:m,height:Y});q&&(R=q.x,Q=q.y);var s=R-o,h=Q-O;F(p,$,R+u),F(p,x,Q+u),A.x+=s,A.y+=h,U.position={x:R,y:Q},B.$5d?M.$10d.fire({kind:"betweenMove",target:M,originEvent:r}):(B.$5d=!0,M.$10d.fire({kind:"beginMove",target:M,originEvent:r}))}}},handleWindowMouseMove:function($){this.handleWindowTouchMove($)},handleWindowResize:function(){var S=this,I=S,J=I._config,t=I.$21d,K=D(),M=b(),O=J.width,f=J.height,V=J.position||"center";J.maximized?(F(t,v,K+u),F(t,k,M+u),F(t,x,B),F(t,$,B)):(F(t,v,O+u),F(t,k,f+u),"center"===V?(F(t,$,(K-O)/2+u),F(t,x,(M-f)/2+u)):(F(t,$,V.x+u),F(t,x,V.y+u))),I.iv()},handle_mousewheel:function(E){E.stopPropagation()},handle_DOMMouseScroll:function(N){N.stopPropagation()}}),j.widget.Dialog=function(o){var T=this,m=T._view=z.createView(null,T);m[p]="ht-widget-dialog",F(m,H,G),F(m,x,B),F(m,Y,B),F(m,$,B),F(m,t,B),F(m,v,"auto"),F(m,k,"auto"),K.baseZIndex!=l&&F(m,"z-index",K.baseZIndex+"");var d=T.$11d=new Z(T);T.bindingHandleWindowResize=d.handleWindowResize.bind(T),T.$10d=new j.Notifier,o&&T.setConfig(o)},V("Dialog",i,{ms_v:1,ms_fire:1,_modal:!0,setTitle:function(S){this._config.title=S,this.getView().querySelector(".dialog-container-title span").innerHTML=S},clearNodes:function(i){if(i)for(;i.firstChild;)i.removeChild(i.firstChild)},$31d:function(){var Y=this,g=Y.$6d=A(),P=Y._config,I=Y.$12d=Y.$13d();g[p]="dialog-container-title",F(g,"cursor","default"),F(g,"white-space","nowrap"),F(g,"overflow","hidden"),F(g,"font",K.dialogTitleLabelFont),F(g,h,P.titleBackground||K.dialogTitleBackground),F(g,"color",P.titleColor||K.dialogTitleLabelColor);var B=S("span"),z=Y.$25d();if(P.titleAlign&&F(g,"text-align",P.titleAlign),P.titleIcon){var x=Y.$14d=Y.$15d();N(g,x)}if(P.title&&P.title.trim&&(P.title=P.title.trim()),B[w]=P.title||"&nbsp;",N(g,B),P.maximizable){var U=Y.$16d=Y.$17d();N(I,U)}if(P.closable){var d=Y.$18d=Y.$19d();N(I,d)}return N(g,I),F(g,"display","block"),F(g,"line-height",z+u),g},$13d:function(){var v=A();return F(v,H,G),F(v,$,B),F(v,t,5+u),F(v,x,B),F(v,Y,B),F(v,"text-align",t),F(v,"white-space","nowrap"),v[p]="dialog-title-controls",v},$20d:function(){var T=this.$21d=A(),R=this._config,c=R.borderWidth;return F(T,H,"fixed"),F(T,"box-shadow","rgba(0, 0, 0, 0.2) 0px 5px 10px 0px"),F(T,"padding",B+" "+c+u+" "+c+u+" "+c+u),F(T,"box-sizing","border-box"),F(T,"-moz-box-sizing","border-box"),T[p]="dialog-container",F(T,h,R.titleBackground||K.dialogTitleBackground),T},$22d:function(){var r,C=this,g=C._config,U=C.$7d=A(),B=g.content,q=0,b=g.contentPadding||0;g.buttons!=l&&g.buttons.length>0&&(q=32),R(B)?U[w]=B:B.getView?(r=B.getView(),N(U,r)):(r=B,N(U,r)),F(U,H,G),F(U,"font",K.dialogContentLabelFont),g.width&&F(U,v,g.width-10-2*b+u),j.Default.appendToScreen(U);var I=U.offsetWidth+1,J=U.offsetHeight,L=C.$25d();return g.width==l?g.width=I+10+2*b:F(U,v,"auto"),g.height==l&&(g.height=J+L+q+5+2*b),j.Default.removeHTML(U),r&&(F(r,"box-sizing","border-box"),F(r,"-moz-box-sizing","border-box"),F(r,v,"100%"),F(r,k,"100%")),U[p]="dialog-content",F(U,x,L+b+u),F(U,Y,q+b+u),F(U,$,b+u),F(U,t,b+u),F(U,"overflow","hidden"),U},getOverlayDiv:function(){return this.$41d},$23d:function(){if(this.$41d)return this.$41d;var v=this.$41d=A();return v[p]="dialog-overlay",F(v,H,G),F(v,x,B),F(v,Y,B),F(v,$,B),F(v,t,B),F(v,h,"rgba(235, 235, 235, 0.7)"),v},_config:l,setSize:function(d,$){var J=this,b=J._config;b&&(b.width=d,b.height=$,J.isShowing()&&(J.bindingHandleWindowResize(),J.iv()))},getConfig:function(){return this._config},$24d:function(){var f=this._config,$=f.titleIconSize||16;return T&&($*=1.2),$},$25d:function(){var h=this._config,s=h.titleHeight||K.widgetTitleHeight;return s},$26d:function(S){F(S,"cursor","pointer"),F(S,"display","inline-block"),F(S,"margin-right",(T?8:4)+u),F(S,"vertical-align",x)},$27d:function(){var d=this.$30d=A(),P=10;return T&&(P=20),F(d,v,P+u),F(d,k,P+u),F(d,H,G),F(d,Y,B),F(d,t,B),d[p]="resize-area",d},$15d:function(){var s=this,M=e();M[p]="dialog-title-control dialog-title-control-icon";var h=s.$25d(),R=s.$24d();return s.$26d(M),z.setCanvas(M,R,h),M},$17d:function(){var K=e();K[p]="dialog-title-control dialog-title-control-maximize";var x=this.$25d(),s=this.$24d();return this.$26d(K),z.setCanvas(K,s,x),K},$19d:function(){var F=e();F[p]="dialog-title-control dialog-title-control-close";var g=this.$25d(),c=this.$24d();return this.$26d(F),z.setCanvas(F,c,g),F},$28d:function(){var y=A();return F(y,h,K.dialogContentBackground||"white"),F(y,v,"100%"),F(y,k,"100%"),F(y,H,"relative"),y},$29d:function(){var X=this,c=A();F(c,"line-height",32+u),F(c,H,G),F(c,Y,B),F(c,$,B),F(c,"white-space","nowrap"),F(c,"overflow","hidden"),F(c,t,B),F(c,h,K.dialogHeaderBackground),c[p]="dialog-container-buttons";var f=this._config,E=f.buttonsAlign||t,g=0;return F(c,"text-align",E),X.$42d=[],f.buttons.forEach(function(l){var w={};for(var B in l)w[B]=l[B];l.background||(w.background=K.dialogButtonBackground),l.selectBackground||(w.selectBackground=K.dialogButtonSelectBackground),l.labelColor||(w.labelColor=K.dialogButtonLabelColor);var _=K.createObject(j.widget.Button,w),V=_.getView();V[p]="dialog-button "+l.className,V.buttonItem=l,X.$42d.push(_),F(V,H,"relative"),F(V,"display","inline-block"),F(V,"text-align",$),F(V,"height",24+u);var r=T?10:5;E===$||E===t?F(V,"margin-"+E,r+u):0===g||F(V,"margin-"+$,r+u),F(V,"vertical-align","middle");var A=K.getTextSize(_.getLabelFont(),l.label).width+10;_.onClicked=function(b){l.action&&l.action.call(X,l,b)},F(V,v,A+u),N(c,_.getView()),g++}),c},setConfig:function(P){if(P){var E=this,h=E._view;E._config=P,E.action=P.action,E.clearNodes(h),P.borderWidth==l&&(P.borderWidth=5);var G=E.$23d(),w=E.$28d(),z=E.$31d(),i=E.$22d(),X=E.$20d(),u=E.$30d=E.$27d();if(N(h,G),N(h,X),N(X,w),N(w,z),N(w,i),P.buttons!=l&&P.buttons.length>0){var S=E.$29d();N(w,S)}N(X,u),P.maximized?(P.maximized=!1,E.bindingHandleWindowResize(),E.maximize(!0)):E.bindingHandleWindowResize(),E.isShowing()&&E.iv()}},hide:function(){var t=this,Q=t.$21d,l=t.$41d,W=t._view;W[p]="ht-widget-dialog",s(Q).duration(200).scale(.7).set(M,B).end(function(){t.onHidden&&t.onHidden(),j.Default.removeHTML(W),t.$10d.fire({kind:"hide",target:t})}),s(l).duration(200).set(M,B).end(),_(r,"resize",t.bindingHandleWindowResize)},isShowing:function(){return!!this._view.parentNode},setModal:function(Z){this._modal=Z;var H=this.$23d();H.style.display=Z?"block":"none";var M=this._view;F(M,v,Z?"auto":B),F(M,k,Z?"auto":B)},isModal:function(){return this._modal},$32d:function(){var Z=this,A=Z._config,G=Z.$21d;I(G,Q),Z.iv(),A.maximized?Z.$10d.fire({kind:"maximize",target:Z}):Z.$10d.fire({kind:"restore",target:Z})},maximize:function(H){var d=this,f=d.$21d,m=d._config;if(!m.maximized){m.maximized=!0,d.$33d(),d.$16d[p]="dialog-title-control dialog-title-control-minimize";var b=m.width,P=m.height,G=C(f.style.left)||0,V=C(f.style.top)||0;d.$36d=b,d.$37d=P,d.$34d=G,d.$35d=V;var I,E=H?0:200,L=this._view.parentNode;I=L&&L!==document.body?this._view.parentNode.getBoundingClientRect():K.getWindowInfo(),s(f).duration(E).set($,I.left+u).set(x,I.top+u).set(v,I.width+u).set(k,I.height+u).end(function(){d.$32d()})}},restore:function(){var e=this,V=e.$34d,X=e.$35d,P=e.$36d,W=e.$37d,i=e._config;if(i.maximized&&(i.maximized=!1,e.$33d(),e.$16d[p]="dialog-title-control dialog-title-control-maximize",e.isShowing()&&V!=l&&X!=l&&P!=l&&W!=l)){var h=e.$21d;s(h).duration(200).set($,V+u).set(x,X+u).set(v,P+u).set(k,W+u).end(function(){e.$32d()})}delete e.$34d,delete e.$35d,delete e.$36d,delete e.$37d},show:function(y){var e=this,o=e._view,X=e._config,Z=e.$21d,x=e.$41d;y=y||X.parentDOM,X&&X.zIndex!=l&&F(o,"z-index",X.zIndex+Q),y?j.Default.appendChild(y,o):j.Default.appendToScreen(o),I(Z,"scale(0.7)"),F(Z,M,B),e.iv(),e.validate(),e._view[p]+=" dialog-show",d(function(){s(Z).duration(200).scale(1).set(M,"1").end(function(){F(Z,v,Z.clientWidth+u),F(Z,k,Z.clientHeight+u),e.onShown&&e.onShown(),e.$10d.fire({kind:"show",target:e});var y=X&&X.content;y&&y.iv&&y.iv()}),s(x).duration(200).set(M,"1").end(),L(r,"resize",e.bindingHandleWindowResize)},30)},setPosition:function(G){var c=this.$21d;c.style.left=G.x+"px",c.style.top=G.y+"px"},addEventListener:function(j,q,u){this.$10d.add(j,q,u)},removeEventListener:function(B,k){this.$10d.remove(B,k)},$38d:function(x,e,p,o){var Z=z.initContext(x);z.translateAndScale(Z,0,0,1),Z.clearRect(0,0,p,p);var j=(p-o)/2;K.drawStretchImage(Z,K.getImage(e),"fill",0,j,o,o),Z.restore()},$33d:function(){var n=this,i=n._config,u=n.$16d,$=i.maximized?K.dialogRestoreIcon:K.dialogMaximizeIcon;if(u&&$){var p=n.$25d(),d=n.$24d();n.$38d(u,K.getImage($),p,d)}},$39d:function(){var _=this,Q=_._config,s=_.$14d,D=Q.titleIcon;if(s&&D){var l=_.$25d(),y=_.$24d();_.$38d(s,K.getImage(D),l,y)}},$40d:function(){var Y=this,x=Y.$18d,t=K.dialogCloseIcon;if(x&&t){var N=Y.$25d(),F=Y.$24d();Y.$38d(x,K.getImage(t),N,F)}},invalidate:function(H){var C=this,D=C.$42d;C._68I||(C._68I=1,K.callLater(C.validate,C,l,H),C.onInvalidated&&C.onInvalidated(),C.fireViewEvent("invalidate"));var s=C._config.content;s.invalidate&&s.invalidate(),D&&D.forEach(function(E){E.iv()})},validateImpl:function(){var e=this;e.$40d(),e.$33d(),e.$39d();var H=e._config.content;H.initView&&(H.setX(0),H.setY(0),H.setWidth(e.$7d.clientWidth),H.setHeight(e.$7d.clientHeight))},adjustPosition:function(S,L,q){var b=L.width,e=L.height,h=q.width,y=q.height,v=S.x,E=S.y,V=this._config,Z=V.minDragSize||20;return"inside"===V.dragMode?(v+b>h&&(v=h-b),E+e>y&&(E=y-e),0>v&&(v=0),0>E&&(E=0)):(null==V.dragMode||"none"===V.dragMode)&&(-b+Z>v&&(v=-b+Z),v>h-Z&&(v=h-Z),E>y-Z&&(E=y-Z),0>E&&(E=0)),{x:v,y:E}}})}("undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:(0,eval)("this"),Object);