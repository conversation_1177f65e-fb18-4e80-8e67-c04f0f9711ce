var isTianyanCollectData = true;
var staffId = ""; // 业务系统获取工号信息逻辑
var provinceCode = ""; //  业务系统获取省分信息逻辑
if (isTianyanCollectData && isTianyanCollectData) {
  var uam_xy_paq = uam_xy_paq || [];
  var uam_xy_obj = new Object();
  var uam_xy_session = new Object();
  (function () {
    uam_xy_paq.push(["server_url", BASE_URL + "/tianyan/sendlog/sendqueue"]);
    // 对接系统编码进行修改
    // uam_xy_paq.push(["systemCode", "YYGLPTSCGL"]);
    uam_xy_paq.push(["systemCode", "GZZX"]);
    // 对接系统licence进行修改
    // uam_xy_paq.push(["licence", "YYGLPTSCGL"]);
    uam_xy_paq.push(["licence", "GZZX"]);
    try {
      uam_xy_paq.push(["staffId", staffId || ""]);
      uam_xy_paq.push(["provinceCode", provinceCode || ""]);
    } catch (e) {}
    var d = document,
      g = d.createElement("script"),
      s = d.getElementsByTagName("script")[0];
    g.type = "text/javascript";
    g.async = true;
    g.defer = true;
    g.src = BASE_URL + "/tianyan/uam_public_commonajax.js";
    s.parentNode.insertBefore(g, s);
  })();
}
