apiVersion: v1
kind: Service
metadata:
  name: netfm3-web-svc
spec:
  type: NodePort
  ports:
    - port: 80
      targetPort: 80
  selector:
    app: netfm3-web
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: netfm3-web-deploy
  labels:
    app: netfm3-web
spec:
  replicas: 1
  selector:
    matchLabels:
      app: netfm3-web
  template:
    metadata:
      labels:
        app: netfm3-web
    spec:
      containers:
        - name: netfm3-web
          image: harbor.dcos.ncmp.unicom.local/eoms-netfm/netfm3-web-prod:v1.6.12
          resources:
            limits:
              cpu: 1
              memory: 3Gi
          env:
            - name: ENV_PROFILE
              value: "prod"
          ports:
            - containerPort: 80
