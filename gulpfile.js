const path = require("path");
const util = require("util");
const fs = require("fs");
const {
  readdirSync,
  lstatSync,
  readFileSync,
  writeFileSync,
  existsSync,
  mkdirSync,
} = fs;
// const { readFile, writeFile } = fs.promises;
const readFile = util.promisify(fs.readFile);
const writeFile = util.promisify(fs.writeFile);
const { execSync } = require("child_process");

const { src, dest, series } = require("gulp");
const cleanCSS = require("gulp-clean-css");
const cssWrap = require("gulp-css-wrap");
const rename = require("gulp-rename");
const mergeStream = require("merge-stream");

const sass = require("gulp-sass");
sass.compiler = require("node-sass");

const prettier = require("prettier");

const resolve = dir => path.join(__dirname, dir);

const paths = {
  frameThemeDir: "src/themes/", //框架主题文件夹
  variablesDir: "src/themes/theme-variables/", //框架主题变量文件夹
  outBathDir: "dist_theme/", //主题处理中转文件夹
};
const outBathDir = resolve(paths.outBathDir);
if (!existsSync(outBathDir)) {
  mkdirSync(outBathDir);
}

// 生成变量键值对
const createThemeMaps = cb => {
  const whiteArr = [
    "$--all-transition",
    "$--fade-transition",
    "$--fade-linear-transition",
    "$--md-fade-transition",
    "$--border-transition-base",
    "$--color-transition-base",
    "$--box-shadow-base",
    "$--box-shadow-dark",
    "$--message-shadow",
  ];
  // "./node_modules/element-theme-chalk/src/common/var.scss";
  readFile(resolve(paths.variablesDir + "frame-variables-default.scss"))
    .then(data => {
      const prefixMap = Buffer.from("$default: (");
      const suffixMap = Buffer.from(");");
      const res = new Set(
        data
          .toString()
          .replace(/(\/{2,}.*?(\r|\n))|(\/\*(\n|.)*?\*\/)/g, "")
          .match(/\$[a-zA-Z0-9-_]*/g)
      );
      let resString = "";
      res.forEach(element => {
        if (!whiteArr.includes(element)) {
          resString += `"${element}": ${element},`;
        } else {
          resString += `"${element}": "#{${element}}",`;
        }
      });
      writeFile(
        resolve(paths.outBathDir + "theme-maps.scss"),
        Buffer.concat([prefixMap, Buffer.from(resString), suffixMap])
      )
        .then(() => {
          cb();
        })
        .catch(err => {
          throw new Error(err);
        });
    })
    .catch(err => {
      throw new Error(err);
    });
};

// 获取主题变量文件名
const getVariablesFiles = () => {
  const files = readdirSync(resolve(paths.variablesDir));
  let res = [];
  files.forEach(item => {
    const stats = lstatSync(resolve(paths.variablesDir + item));
    if (stats.isFile()) {
      if (/^frame-variables/.test(item)) {
        res.push(item);
      }
    }
  });
  return res;
};

const getThemeName = fileName => {
  const name = fileName.slice(0, fileName.indexOf("."));
  return name.length > 15 ? name.slice(16) : "default";
};

const getThemeNameLists = files => {
  let res = [];
  files.forEach(item => {
    res.push(getThemeName(item));
  });
  return res;
};

const files = getVariablesFiles();
const themeNameLists = getThemeNameLists(files);

// 生成主题列表js
const creatThemeList = cb => {
  const out = "export default " + JSON.stringify(themeNameLists);
  writeFileSync(
    resolve(paths.outBathDir + "frame-theme-list.js"),
    Buffer.from(prettier.format(out, { parser: "babel" }))
  );
  cb();
};

// 生成不同主题编译后的变量,可供js调用
const createExportCss = () => {
  const themeMapsBuf = readFileSync(
    resolve(paths.outBathDir + "theme-maps.scss")
  );
  const exportBuf = readFileSync(
    resolve(paths.frameThemeDir + "frame-export-js.txt")
  );
  const hasMapVarDir = resolve(paths.outBathDir + "theme-variables");
  if (!existsSync(hasMapVarDir)) {
    mkdirSync(hasMapVarDir);
  }
  let merge = mergeStream();
  files.forEach(item => {
    const themeName = getThemeName(item);
    const itemBuf = readFileSync(resolve(paths.variablesDir + item));
    const hasMapVarPath = paths.outBathDir + "theme-variables/" + item;
    writeFileSync(
      resolve(hasMapVarPath),
      Buffer.concat([itemBuf, themeMapsBuf, exportBuf])
    );
    const stream = src(resolve(hasMapVarPath))
      .pipe(sass.sync().on("error", sass.logError))
      .pipe(cleanCSS())
      .pipe(rename({ dirname: "", basename: themeName, extname: ".css" }))
      .pipe(dest(paths.outBathDir + "hasmap-css/"));
    merge.add(stream);
  });
  return merge;
};

const createVarsAndExport = cb => {
  const prefixMap = Buffer.from("$themes: (");
  const suffixMap = Buffer.from(");");
  const exportDir = resolve(paths.outBathDir + "theme-export");
  if (!existsSync(exportDir)) {
    mkdirSync(exportDir);
  }
  let varsString = "";
  themeNameLists.forEach(item => {
    const str = readFileSync(
      resolve(paths.outBathDir + "hasmap-css/" + item + ".css"),
      "utf8"
    );
    const exportPath = paths.outBathDir + "theme-export/" + item + ".scss";
    varsString += `"${item}"${str.slice(str.indexOf("default") + 7, -1)},`;
    writeFileSync(
      resolve(exportPath),
      str.slice(0, str.indexOf("default")) + "};"
    );
  });
  writeFileSync(
    resolve(paths.outBathDir + "theme-vars.scss"),
    Buffer.concat([prefixMap, Buffer.from(varsString), suffixMap])
  );
  cb();
};

// 根据不同主题变量编译element
const buildThemeTask = cb => {
  files.forEach(item => {
    const themeName = getThemeName(item);
    const shell = `${resolve("node_modules/.bin/et")} -c ${resolve(
      paths.variablesDir + item
    )} -o ${resolve(paths.outBathDir + "theme-list/" + themeName)}`;
    execSync(shell);
    console.log(themeName + "主题编译完成");
  });
  cb();
};

const customThemeName = ".custom-theme-";

// 给不同主题css添加命名空间
const cssWrapTask = () => {
  let merge = mergeStream();
  themeNameLists.forEach(item => {
    const stream = src(
      resolve(paths.outBathDir + "theme-list/" + item + "/index.css")
    )
      .pipe(cssWrap({ selector: customThemeName + item }))
      .pipe(cleanCSS())
      .pipe(rename({ dirname: "", basename: "theme-" + item }))
      .pipe(dest(resolve(paths.outBathDir + "theme-css/")));
    merge.add(stream);
  });
  return merge;
};

const moveFileTask = () => {
  let merge = mergeStream();
  const themeListFiles = src(
    resolve(paths.outBathDir + "frame-theme-list.js")
  ).pipe(dest(resolve(paths.frameThemeDir)));
  const varFiles = src(resolve(paths.outBathDir + "theme-vars.scss")).pipe(
    dest(resolve(paths.frameThemeDir))
  );
  const exportFiles = src(resolve(paths.outBathDir + "theme-export/**")).pipe(
    dest(resolve(paths.frameThemeDir + "theme-export/"))
  );
  const cssFiles = src(resolve(paths.outBathDir + "theme-css/**")).pipe(
    dest(resolve(paths.frameThemeDir + "theme-css/"))
  );
  const fontFiles = src(resolve(paths.outBathDir + "default/fonts/**")).pipe(
    dest(resolve(paths.frameThemeDir + "theme-css/fonts/"))
  );
  return merge.add(themeListFiles, cssFiles, varFiles, exportFiles, fontFiles);
};

exports.createThemeMaps = createThemeMaps;
exports.creatThemeList = creatThemeList;
exports.createExportCss = createExportCss;
exports.createVarsAndExport = createVarsAndExport;
exports.buildThemeTask = buildThemeTask;
exports.cssWrapTask = cssWrapTask;
exports.moveFileTask = moveFileTask;
exports.default = series(
  createThemeMaps,
  creatThemeList,
  createExportCss,
  createVarsAndExport,
  buildThemeTask,
  cssWrapTask,
  moveFileTask
);
