{"name": "dap", "version": "2.5.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "test:unit": "vue-cli-service test:unit", "test:e2e": "vue-cli-service test:e2e", "lint": "vue-cli-service lint --fix", "cross": "cross-env LIMIT=8192 increase-memory-limit", "svgo": "svgo -f src/assets/icons/svg --config=src/assets/icons/svgo.yml"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@antv/g6": "3.5.0", "@types/leaflet": "1.5.7", "accounting-js": "1.1.1", "animate.css": "4.1.1", "axios": "0.18.1", "canvg": "3.0.7", "clipboard": "2.0.4", "codemirror": "5.48.4", "core-js": "^3.22.0", "countup": "1.8.2", "crc-32": "1.2.0", "echarts": "4.8.0", "echarts-liquidfill": "2.0.6", "echarts-stat": "1.2.0", "element-resize-detector": "1.2.1", "element-ui": "2.15.7", "esri-leaflet": "2.3.0", "file-save": "0.2.0", "file-saver": "2.0.2", "font-awesome": "4.7.0", "heatmap.js": "^2.0.5", "highlight": "0.2.4", "html-webpack-plugin": "4.0.0", "html2canvas": "1.0.0-rc.7", "itsm-common": "1.0.13-beta2", "jquery": "3.5.1", "jquery-ui": "1.12.1", "js-cookie": "2.2.1", "jsonp": "0.2.1", "jsplumb": "2.15.2", "lautec-leaflet-measure": "3.1.1", "leaflet": "^1.7.1", "leaflet-minimap": "3.6.1", "leaflet.icon.glyph": "0.2.1", "leaflet.markercluster": "1.4.1", "lodash": "4.17.20", "md5": "2.2.1", "moment": "2.24.0", "normalize.css": "8.0.1", "nprogress": "0.2.0", "osmbuildings": "0.2.0-b", "path-to-regexp": "3.0.0", "proj4": "2.6.2", "proj4leaflet": "1.0.2", "rangy": "1.3.0", "resize-observer-polyfill": "1.5.1", "sm-crypto": "0.2.5", "sortable": "2.0.0", "spark-md5": "3.0.0", "sql-formatter": "2.3.2", "swiper": "5.4.5", "uuid": "7.0.2", "v-charts": "1.19.0", "v-charts-h": "1.0.4", "validator": "11.1.0", "vue": "2.6.11", "vue-animate-number": "0.4.2", "vue-awesome-swiper": "4.1.1", "vue-baidu-map": "0.21.22", "vue-codemirror": "4.0.6", "vue-grid-layout": "2.3.7", "vue-infinite-loading": "2.4.4", "vue-json-viewer": "2.2.16", "vue-router": "3.0.3", "vue-smart-widget": "0.5.5", "vue-splitpane": "1.0.6", "vue2-leaflet": "^2.7.1", "vue2-leaflet-draw-toolbar": "0.1.1", "vuedraggable": "2.20.0", "vuex": "3.0.1", "vuex-persistedstate": "2.6.0", "ztree": "3.5.24"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "7.8.3", "@babel/plugin-proposal-optional-chaining": "7.8.3", "@vue/cli-plugin-babel": "4.5.13", "@vue/cli-plugin-eslint": "4.5.13", "@vue/cli-plugin-unit-mocha": "4.5.13", "@vue/cli-service": "4.5.13", "@vue/eslint-config-prettier": "6.0.0", "@vue/test-utils": "1.0.0-beta.29", "add-asset-html-webpack-plugin": "3.1.3", "babel-eslint": "10.1.0", "babel-plugin-dynamic-import-node": "2.3.0", "babel-plugin-import": "1.13.3", "chai": "4.1.2", "clean-webpack-plugin": "1.0.1", "compression-webpack-plugin": "6.0.2", "cross-env": "6.0.3", "element-theme": "2.0.1", "element-theme-chalk": "2.15.6", "eslint": "6.7.2", "eslint-plugin-prettier": "3.3.1", "eslint-plugin-vue": "6.2.2", "filemanager-webpack-plugin": "2.0.5", "gulp": "4.0.2", "gulp-clean-css": "4.2.0", "gulp-css-wrap": "0.1.2", "increase-memory-limit": "1.0.7", "less": "3.11.1", "less-loader": "5.0.0", "merge-stream": "2.0.0", "prettier": "2.2.1", "sass": "1.32.11", "sass-loader": "8.0.2", "sass-resources-loader": "2.2.1", "script-ext-html-webpack-plugin": "2.1.4", "svg-sprite-loader": "4.1.6", "svgo": "1.2.2", "vcrontab": "0.3.3", "vue-template-compiler": "2.6.11"}}