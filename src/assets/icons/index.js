import Vue from "vue";
import SvgIcon from "@/components/SvgIcon"; // svg组件

// register globally
Vue.component("svg-icon", SvgIcon);

const { svgFiles } = require("@/settings");

const req = require.context("./svg", false, /\.svg$/);
const requireAll = (requireContext, isPluginDir) => {
  if (!isPluginDir) {
    return requireContext.keys().map(requireContext);
  } else {
    return requireContext
      .keys()
      .filter(item => {
        const prefixs = svgFiles
          .filter(item => /^src\/plugin\//.test(item))
          .map(tmp => "." + tmp.slice(10));
        return prefixs.some(ele => item.includes(ele));
      })
      .map(requireContext);
  }
};
requireAll(req);

const req2 = require.context("../../plugin/", true, /\.svg$/);
requireAll(req2, true);
