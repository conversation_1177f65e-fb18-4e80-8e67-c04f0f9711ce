<template>
  <section style="position: relative">
    <div
      ref="echart"
      :style="{
        height: $route.query.type == 'app' ? 'calc(100vh - 20px)' : '500px',
      }"
    ></div>
    <div class="pobtn">
      <span @click="roamMap(1)" class="handle-icon"></span>
      <span @click="roamMap(0)" class="handle-icon icon-sub"></span>
      <span @click="roamMap()" class="handle-icon icon-reset"></span>
    </div>
  </section>
</template>

<script>
import { apiGuangshuai } from "@/plugin/backbone/modules/commonProvince/workOrderWaitDetail/api/CommonApi";
import { mapGetters, mapActions } from "vuex";

import echarts from "echarts";

export default {
  props: {
    guangshuaiData: {
      type: Array,
    },

    xAxis: {
      type: Array,
      default() {
        return [
          {
            type: "category",
            boundaryGap: false,
            axisLabel: {
              formatter: function (value, index) {
                return value.substring(0, 10);
              },
              textStyle: {
                color: "#AAAAAA",
                // fontStyle: "italic",
              },
              // interval: 2, // 每隔2个数据点显示一个label
              // rotate: 45, // 水平字旋转的角度
            },

            axisLine: {
              lineStyle: {
                color: "#D9D9D9",
              },
            },
          },
        ];
      },
    },
  },

  data() {
    return {
      myChart: null,
    };
  },

  mounted() {
    if (this.$route.query.type == "app") {
      // 全屏
      this["settings/toggleFullScreen"](true);
    }
    // APP端
    if (!this.guangshuaiData) {
      if (this.$route.query.woId) {
        this.getGuangshuai();
      } else {
        this.$message.error("缺少woId参数或者光衰趋势图不存在");
      }
    } else {
      // PC端
      this.initEchart(this.guangshuaiData);
    }
  },
  methods: {
    ...mapActions(["settings/toggleShowSettings", "settings/toggleFullScreen"]),

    // 获取光衰数据
    getGuangshuai() {
      apiGuangshuai({
        woId: this.$route.query.woId,
      })
        .then(res => {
          if (res?.data?.trendStatus == "YES") {
            this.initEchart(res.data.trendData || []);
          } else {
            this.$message.error("无光衰图");
          }
        })
        .catch(err => {
          console.log(err);
        });
    },
    // 渲染 光衰趋势图
    initEchart(gsData) {
      this.myChart = echarts.init(this.$refs.echart);
      let bgColor = "#FFFFFF";
      let color = [
        "#8B5CFF", // 紫色线条
        "#FFB6C1", // 浅粉色线条
        "#FFC005",
        "#FF515A",
        "#8B5CFF",
        "#00CA69",
      ];
      let echartData = gsData;
      echartData.push({
        ai_value: undefined,
        kpi_value: undefined,
        name: "2025-04-19 00:00:00",
      });
      debugger;
      let xAxisData = echartData.map(v => v.name);
      //  ["2025-02-01 00:00:00", "", "", "", "", "", "", "","", "", "", "", "", "", "", "", "","", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "", "","", "", "", "", "", "", "","", "", "", "", "", "", "", "", "", "","", "", "", "", "", "", "", "","", "", "", "", "", "", "", "", "2025-02-28 00:00:00"]
      let yAxisData1 = echartData.map(v => v.ai_value);
      // [20.1, 19.975, 19.8454675, 20.975, 19.6335375, 19.5, 20.45464657, 19.5765875,20.1, 19.975, 19.8454675, 20.975, 19.6335375, 19.5, 20.45464657, 19.5765875,20.1, 19.975, 19.8454675, 21.975, 19.6335375, 19.5, 20.45464657, 19.5765875,20.1, 19.975, 19.8454675, 20.975, 19.6335375, 19.5, 20.45464657, 18.5765875,20.1, 19.975, 19.8454675, 21.975, 19.6335375, 19.5, 20.45464657, 18.5765875,20.1, 19.975, 19.8454675, 21.975, 19.6335375, 19.5, 20.45464657, 19.5765875,20.1, 19.975, 19.8454675, 20.975, 19.6335375, 19.5, 20.45464657, 19.5765875,20.1, 19.975, 19.8454675, 20.975, 19.6335375, 19.5, 20.45464657, 19.5765875]
      let yAxisData2 = echartData.map(v => v.kpi_value);
      // [19.975, 20.88755766, 20.3543643, 19.113333, 20.344547, 20.56876979, 20.0, 19.0,19.975, 20.88755766, 20.3543643, 19.113333, 20.344547, 20.56876979, 20.0, 19.0,19.975, 20.88755766, 20.3543643, 19.113333, 20.344547, 20.56876979, 20.0, 19.0,19.975, 20.88755766, 20.3543643, 18.113333, 21.344547, 20.56876979, 21.0, 19.0,19.975, 20.88755766, 20.3543643, 19.113333, 20.344547, 20.56876979, 21.0, 19.0,19.975, 20.88755766, 20.3543643, 19.113333, 20.344547, 20.56876979, 20.0, 19.0,19.975, 20.88755766, 20.3543643, 19.113333, 20.344547, 20.56876979, 20.0, 10.0,19.975, 20.88755766, 20.3543643, 19.113333, 20.344547, 20.56876979, 21.0, 19.0]
      const hexToRgba = (hex, opacity) => {
        let rgbaColor = "";
        let reg = /^#[\da-f]{6}$/i;
        if (reg.test(hex)) {
          rgbaColor = `rgba(${parseInt("0x" + hex.slice(1, 3))},${parseInt(
            "0x" + hex.slice(3, 5)
          )},${parseInt("0x" + hex.slice(5, 7))},${opacity})`;
        }
        return rgbaColor;
      };

      let xAxisOption = this.xAxis;
      xAxisOption[0].data = xAxisData;
      let options = {
        backgroundColor: bgColor,
        color: color,
        legend: {
          right: 120,
          top: 10,
        },
        tooltip: {
          trigger: "axis",
          formatter: function (params) {
            let html = `<div style="color: #666;font-size: 14px;line-height: 24px">
                <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;opacity:0;"></span>
                ${params[0].name}
            </div>`;
            params.forEach(v => {
              html += `<div style="color: #666;font-size: 14px;line-height: 24px">
                    <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${
                      color[v.componentIndex]
                    };"></span>
                    ${v.seriesName}
                    <span style="color:${
                      color[v.componentIndex]
                    };font-weight:700;font-size: 14px">${
                v.value ? v.value : "--"
              }</span>
                </div>`;
            });

            return html;
          },
          extraCssText:
            "background: #fff; border-radius: 0;box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);color: #D9D9D9;",
          axisPointer: {
            type: "line", // 改为线条类型
            lineStyle: {
              // 设置线条样式
              type: "dashed",
              color: "rgba(220,220,220,0.8)", // 浅灰色
              width: 1,
              dashOffset: 4,
              cap: "round",
            },
          },
        },
        grid: {
          width: this.$route.query.type == "app" ? "95%" : "97%",
          top: this.$route.query.type == "app" ? "15%" : "12%",
          left: this.$route.query.type == "app" ? "3%" : "2%",
          height: this.$route.query.type == "app" ? "80%" : "87%",
          containLabel: true,
        },
        xAxis: xAxisOption,

        yAxis: [
          {
            type: "value",
            name: "单位:dB",
            axisLabel: {
              textStyle: {
                color: "#bbb",
              },
            },
            nameTextStyle: {
              color: "#bbb",
              fontSize: 12,
              lineHeight: 40,
            },
            splitLine: {
              lineStyle: {
                type: "dashed",
                color: "#ddd",
              },
            },
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
          },
        ],
        dataZoom: [
          //滑动条
          {
            xAxisIndex: 0, //这里是从X轴的0刻度开始
            show: false, //是否显示滑动条，不影响使用
            type: "inside", // 这个 dataZoom 组件是 slider 型 dataZoom 组件
            startValue: 0, // 从头开始。
            start: 0,
            // endValue: 15, // 一次性展示6个。
            zoomLock: true,
          },
        ],
        series: [
          {
            name: "AI拟合预测指标",
            type: "line",
            smooth: true,
            showSymbol: false,
            zlevel: 3,
            lineStyle: {
              normal: {
                color: color[0], // 使用紫色
                opacity: 0.8,
                type: "dashed", // 设置为虚线
                dashOffset: 1,
                dash: [4, 4], // 虚线的样式
              },
            },
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: hexToRgba(color[0], 0.1), // 调整红色区域透明度
                    },
                    {
                      offset: 1,
                      color: hexToRgba(color[0], 0.05), // 调整渐变结束透明度
                    },
                  ],
                  false
                ),
                shadowColor: hexToRgba(color[0], 0.1),
                shadowBlur: 10,
                position: "top",
              },
            },
            data: yAxisData1,
          },
          {
            name: "链路KPI指标",
            type: "line",
            smooth: true,
            showSymbol: false, // 不显示标记点
            zlevel: 3,
            lineStyle: {
              normal: {
                color: color[1],
                opacity: 0.8,
              },
            },
            data: yAxisData2,
          },
        ],
      };
      this.myChart.setOption(options);
      let ro = new ResizeObserver(entries => {
        this.myChart.resize();
      });

      ro.observe(this.$refs.echart);
    },
    // 放大缩小
    roamMap(flag) {
      const that = this;
      let currentZoom = this.myChart.getOption().dataZoom[0].start; // 当前的缩放比例

      if (flag === 0) {
        // 上滚，缩小图表
        currentZoom -= 5; // 减小范围值，实现缩小效果
      } else if (flag == 1) {
        // 下滚，放大图表
        currentZoom += 5; // 增加范围值，实现放大效果
      } else {
        currentZoom = 0;
      }

      this.myChart.setOption({
        dataZoom: [
          {
            start: currentZoom,
          },
          {
            start: currentZoom,
          },
        ],
      });
    },
  },
};
</script>
<style lang="scss">
.pobtn {
  position: absolute;
  top: -2px;
  right: 0;
  .handle-icon {
    display: inline-block;
    width: 34px;
    height: 34px;
    margin-left: 5px;
    margin: 2px 0px;
    cursor: pointer;
    background: url(./zoom_in.png) no-repeat center;
    background-size: contain;

    box-shadow: rgba(0, 0, 0, 0.3) 0px 1px 4px;
  }
  .icon-sub {
    background: url(./zoom_out.png) no-repeat center;
    background-size: contain;
  }
  .icon-reset {
    background: url(./overview.png) no-repeat center;
    background-size: contain;
  }
}
</style>
