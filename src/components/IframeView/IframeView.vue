<template>
  <iframe
    :id="id"
    :src="url"
    frameborder="0"
    width="100%"
    scrolling="auto"
    :style="{ height: rightBodyHeight }"
  ></iframe>
</template>

<script>
import router, { resetRouter } from "@/router.js";
import Layout from "@/framework/views/layouts";
import moment from "moment";
import { mapGetters } from "vuex";

export default {
  name: "IframeView",
  data() {
    return {
      url: "",
      id: "",
    };
  },
  watch: {
    /* $route(to) {
      if (to.params.tab) {
        this.url = to.params.url;
        this.id = this.$route.path;
      } else {
        const url = to.meta["url"];
        if (url && url.indexOf("@/components/IframeView?url=") !== -1)
          this.goUrl();
      }
    }, */
  },
  computed: {
    ...mapGetters({
      frameStyle: "frameStyle",
      token: "token",
    }),
    router() {
      return router;
    },
    rightBodyHeight() {
      return `calc(100vh - ${this.frameStyle.headerHeight}px)`;
    },
  },
  created() {
    if (this.$route.params.tab) {
      const url = this.$route.params.url;
      if (url.indexOf("?") !== -1) {
        this.url = `${url}&token=${
          this.token
        }&globalUniqueID=${sessionStorage.getItem("globalUniqueID")}`;
      } else {
        this.url = `${url}?token=${
          this.token
        }&globalUniqueID=${sessionStorage.getItem("globalUniqueID")}`;
      }
      this.urlAppendParam();
      this.id = this.$route.path;
    } else {
      this.goUrl();
    }
  },
  mounted() {
    window.addEventListener("message", this.receiveMessage, false);
  },
  activated() {
    window.addEventListener("message", this.receiveMessage, false);
  },
  deactivated() {
    window.removeEventListener("message", this.receiveMessage, false);
  },
  methods: {
    goUrl() {
      let url = this.$route.meta.url.replace(
        "@/components/IframeView?url=",
        ""
      );
      let id = this.$route.path;
      this.id = id;
      if (url !== null && url !== undefined) {
        if (url.lastIndexOf("?") !== -1) {
          this.url = `${url}&token=${
            this.token
          }&globalUniqueID=${sessionStorage.getItem("globalUniqueID")}`;
        } else {
          this.url = `${url}?token=${
            this.token
          }&globalUniqueID=${sessionStorage.getItem("globalUniqueID")}`;
        }
        this.urlAppendParam();
      }
    },
    urlAppendParam() {
      let _this = this;
      let query = _this.$route.query;
      if (query) {
        for (let key in query) {
          _this.url += "&" + key + "=" + query[key];
        }
      }
    },
    receiveMessage(e) {
      let d = e.data;
      if (typeof d === "string" && d.indexOf('{"type":"opentab"') !== -1) {
        d = JSON.parse(d);
      }
      if (d.type == "opentab") {
        let url = d.url;
        let title = d.title;
        const self = this;
        const date = moment().format("x");
        let permission_routes = this.$store.getters.permission_routes.slice(
          3,
          -1
        );
        let newRoutes = [
          ...permission_routes,
          {
            path: "/layout" + self.$route.name + date,
            component: Layout,
            name: "layout" + self.$route.name + date,
            hidden: true,
            meta: {
              title: self.$route.meta.title,
            },
            children: [
              {
                path: self.$route.name + date,
                name: self.$route.name + date,
                component: () => import("@/components/IframeView/index"),
                meta: {
                  title: title,
                },
              },
            ],
          },
          {
            path: "*",
            redirect: "/404",
            hidden: true,
          },
        ];
        resetRouter();
        this.$store.commit("permission/SET_ROUTES", newRoutes);
        this.router.addRoutes(newRoutes);
        this.$router.push({
          name: self.$route.name + date,
          params: { url: url, title: title, tab: true },
        });
        // this.$store
        //   .dispatch("permission/generateRoutes", newRoutes)
        //   .then(val => {
        //     this.router.addRoutes(val);
        //     this.$router.push({
        //       name: self.$route.name + date,
        //       params: { url: url, title: title, tab: true },
        //     });
        //   });
      }
    },
  },
};
</script>

<style></style>
