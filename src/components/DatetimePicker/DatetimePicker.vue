<template>
  <div class="datetime-picker">
    <el-date-picker
      v-if="isBase"
      key="base"
      class="base-date-picker width100"
      v-model="baseValue"
      :type="type"
      :format="baseFormat"
      v-bind="baseProps"
    >
    </el-date-picker>
    <el-date-picker
      v-if="isBaseRange"
      key="baseRange"
      class="base-date-picker width100"
      v-model="baseValue"
      :type="type"
      :format="baseFormat"
      v-bind="baseProps"
    >
    </el-date-picker>

    <!-- 自定义 -->
    <!-- 年份范围 -->
    <div v-if="isYearRange" class="yearrange-wrap width100">
      <el-date-picker
        v-model="rangeValues.start"
        type="year"
        placeholder="选择开始年份"
        v-bind="baseProps"
        class="start"
      >
      </el-date-picker>
      <span class="el-range-separator">{{ $attrs.rangeSeparator || "-" }}</span>
      <el-date-picker
        v-model="rangeValues.end"
        type="year"
        placeholder="选择结束年份"
        v-bind="baseProps"
        class="end"
      >
      </el-date-picker>
    </div>
    <!-- 周范围 -->
    <div v-if="isWeekRange" class="weekrange-wrap width100">
      <el-date-picker
        v-model="rangeValues.start"
        type="week"
        placeholder="选择开始周"
        :format="baseFormat"
        v-bind="baseProps"
        class="start"
      >
      </el-date-picker>
      <span class="el-range-separator">{{ $attrs.rangeSeparator || "-" }}</span>
      <el-date-picker
        v-model="rangeValues.end"
        type="week"
        placeholder="选择结束周"
        :format="baseFormat"
        v-bind="baseProps"
        class="end"
      >
      </el-date-picker>
    </div>
    <!-- 小时范围 -->
    <div v-if="isTimeRange" class="time-wrap width100">
      <el-date-picker
        v-model="rangeValuesTime.start"
        type="date"
        placeholder="选择开始日期"
        v-bind="baseProps"
        class="date-picker"
      >
      </el-date-picker>
      <el-time-select
        :key="`${timePickerKey}1`"
        v-model="rangeValuesTime.startTime"
        :picker-options="timePicker"
        placeholder="选择开始时间"
        :disabled="!rangeValuesTime.start"
        class="time-select"
      >
      </el-time-select>
      <span class="el-range-separator">{{ $attrs.rangeSeparator || "-" }}</span>
      <el-date-picker
        v-model="rangeValuesTime.end"
        type="date"
        placeholder="选择结束日期"
        v-bind="baseProps"
        class="date-picker"
      >
      </el-date-picker>
      <el-time-select
        :key="`${timePickerKey}2`"
        v-model="rangeValuesTime.endTime"
        :picker-options="timePicker"
        placeholder="选择结束时间"
        :disabled="!rangeValuesTime.end"
        class="time-select"
      >
      </el-time-select>
    </div>
  </div>
</template>

<script>
import moment from "moment";
moment.locale("zh-cn");
import _ from "lodash";

export default {
  name: "DatetimePicker",
  inheritAttrs: false,
  props: {
    type: {
      type: String,
      default: "date",
      validater: function (value) {
        return [
          "year",
          "month",
          "date",
          "week",
          "datetime",
          "datetimerange",
          "daterange",
          "yearrange",
          "monthrange",
          "weekrange",
          "hourrange",
          "minute15range",
          "minute5range",
          "minuterange",
        ].includes(value);
      },
    },
    value: {},
  },
  data() {
    return {
      baseValue: null,
      rangeValues: {
        start: null,
        end: null,
      },
      rangeValuesTime: {
        start: null,
        startTime: null,
        end: null,
        endTime: null,
      },
      timePickerKey: 0,
    };
  },
  computed: {
    $$attrs() {
      let attrs = {};
      Object.entries(this.$attrs).forEach(([key, value]) => {
        attrs[_.camelCase(key)] = value;
      });
      return attrs;
    },
    isBase() {
      return ["year", "month", "date", "week", "datetime"].includes(this.type);
    },
    isBaseRange() {
      return ["monthrange", "datetimerange", "daterange"].includes(this.type);
    },
    baseProps() {
      const attrs = this.$$attrs;
      return {
        readonly: attrs.readonly,
        disabled: attrs.disabled,
        editable: attrs.editable,
        clearable: attrs.clearable,
        size: attrs.size,
        placeholder: attrs.placeholder,
        startPlaceholder: attrs.startPlaceholder,
        endPlaceholder: attrs.endPlaceholder,
        align: attrs.align,
        timeArrowControl: attrs.timeArrowControl,
        popperClass: attrs.popperClass,
        pickerOptions: { ...(attrs?.pickerOptions ?? {}), firstDayOfWeek: 1 },
        rangeSeparator: attrs.rangeSeparator,
        defaultTime:
          attrs.defaultTime ??
          (this.type == "datetime" || this.type == "date" || this.isTimeRange)
            ? null
            : ["00:00:00", "23:59:59"],
        name: attrs.name,
        unlinkPanels: attrs.unlinkPanels,
        validateEvent: attrs.validateEvent,
        prefixIcon: attrs.prefixIcon,
        clearIcon: attrs.clearIcon,
      };
    },
    baseFormat() {
      if (this.type == "week" || this.type == "weekrange") {
        return "yyyy年第WW周";
      } else {
        return null;
      }
    },
    isYearRange() {
      return this.type == "yearrange";
    },
    isWeekRange() {
      return this.type == "weekrange";
    },
    isTimeRange() {
      return [
        "hourrange",
        "minute15range",
        "minute5range",
        "minuterange",
      ].includes(this.type);
    },
    isHourRange() {
      return this.type == "hourrange";
    },
    timePicker() {
      if (this.type == "hourrange") {
        return {
          start: "00:00",
          end: "23:00",
          step: "01:00",
        };
      } else if (this.type == "minute15range") {
        return {
          start: "00:00",
          end: "23:45",
          step: "00:15",
        };
      } else if (this.type == "minute5range") {
        return {
          start: "00:00",
          end: "23:55",
          step: "00:05",
        };
      } else {
        return {
          start: "00:00",
          end: "23:59",
          step: "00:01",
        };
      }
    },
    timeProps() {
      const attrs = this.$$attrs;
      return {
        readonly: attrs.readonly,
        disabled: attrs.disabled,
        editable: attrs.editable,
        clearable: attrs.clearable,
        size: attrs.size,
        placeholder: attrs.placeholder,
        align: attrs.align,
        popperClass: attrs.popperClass,
        pickerOptions: { ...(attrs?.pickerOptions ?? {}) },
        name: attrs.name,
        prefixIcon: attrs.prefixIcon,
        clearIcon: attrs.clearIcon,
      };
    },
  },
  watch: {
    type: {
      handler: function () {
        this.onValueChange(this.value);
      },
    },
    value: {
      // immediate: true,
      handler: "onValueChange",
    },
    baseValue: {
      handler: "onBaseChange",
    },
    rangeValues: {
      deep: true,
      handler: function () {
        if (this.isYearRange) {
          this.onYearChange();
        }
        if (this.isWeekRange) {
          this.onWeekChange();
        }
      },
    },
    rangeValuesTime: {
      deep: true,
      handler: "onTimeRangeChange",
    },
    timePicker() {
      this.timePickerKey = new Date().getTime();
    },
  },
  created() {
    this.onValueChange(this.value);
    // this.firstChange();
  },
  methods: {
    onBaseChange() {
      const val = this.baseValue;
      let resVal = null;
      if (Array.isArray(val) && val.length > 0) {
        const [val1, val2] = val;
        const valRange1 = val1 ? moment(val[0]) : null;
        const valRange2 = val2 ? moment(val[1]) : null;
        if (this.type == "monthrange") {
          resVal = [
            valRange1 ? valRange1.format("YYYY-MM-DD 00:00:00") : null,
            valRange2
              ? valRange2.endOf("month").format("YYYY-MM-DD 23:59:59")
              : null,
          ];
        } else if (this.type == "daterange") {
          resVal = [
            valRange1 ? valRange1.format("YYYY-MM-DD 00:00:00") : null,
            valRange2 ? valRange2.format("YYYY-MM-DD 23:59:59") : null,
          ];
        } else {
          resVal = [
            valRange1 ? valRange1.format("YYYY-MM-DD HH:mm:ss") : null,
            valRange2 ? valRange2.format("YYYY-MM-DD HH:mm:ss") : null,
          ];
        }
      } else {
        if (val) {
          if (this.type != "week") {
            resVal = val ? moment(val).format("YYYY-MM-DD HH:mm:ss") : null;
          } else {
            if (val) {
              resVal = [
                moment(val).weekday(0).format("YYYY-MM-DD 00:00:00"),
                moment(val).weekday(6).format("YYYY-MM-DD 23:59:59"),
              ];
            }
          }
        }
      }
      if (this.isBase || this.isBaseRange) {
        this.emitValue(resVal);
      }
    },
    onYearChange() {
      const { start, end } = this.rangeValues;
      let resVal = [];
      if (start || end) {
        if (start && !end) {
          resVal = [
            // moment(start).year() + "-01-01 00:00:00",
            moment(start).format("YYYY-01-01 00:00:00"),
            // moment(start).format("YYYY-12-31 23:59:59"),
            null,
          ];
        } else if (!start && end) {
          resVal = [
            // moment(end).format("YYYY-01-01 00:00:00"),
            null,
            moment(end).format("YYYY-12-31 23:59:59"),
          ];
        } else {
          resVal = [
            moment(start).format("YYYY-01-01 00:00:00"),
            moment(end).format("YYYY-12-31 23:59:59"),
          ];
        }
      }
      if (this.isYearRange) {
        this.emitValue(resVal);
      }
    },
    onWeekChange() {
      const { start, end } = this.rangeValues;
      let resVal = [];
      if (start || end) {
        if (start && !end) {
          resVal = [
            moment(start).weekday(0).format("YYYY-MM-DD 00:00:00"),
            moment(start).weekday(6).format("YYYY-MM-DD 23:59:59"),
          ];
        } else if (!start && end) {
          resVal = [
            moment(end).weekday(0).format("YYYY-MM-DD 00:00:00"),
            moment(end).weekday(6).format("YYYY-MM-DD 23:59:59"),
          ];
        } else {
          resVal = [
            moment(start).weekday(0).format("YYYY-MM-DD 00:00:00"),
            moment(end).weekday(6).format("YYYY-MM-DD 23:59:59"),
          ];
        }
      }
      if (this.isWeekRange) {
        this.emitValue(resVal);
      }
    },
    onTimeRangeChange() {
      const { start, startTime, end, endTime } = this.rangeValuesTime;
      let resVal = [];
      if (!(!start && !end)) {
        if (start) {
          const time = startTime ? startTime : "00:00";
          resVal[0] = moment(start).format("YYYY-MM-DD ") + time + ":00";
        } else {
          resVal[0] = null;
        }
        if (end) {
          const time = endTime ? endTime : "00:00";
          resVal[1] = moment(end).format("YYYY-MM-DD ") + time + ":00";
        } else {
          resVal[1] = null;
        }
      }
      if (this.isTimeRange) {
        this.emitValue(resVal);
      }
    },
    onValueChange(val) {
      this.resetValue();
      if ((!val || _.isEmpty(val)) && !_.isDate(val)) {
        this.resetValue();
      } else {
        const regExp = /[\u4e00-\u9fa5]/g;
        if (this.isBase) {
          const oneVal = Array.isArray(val) ? val[1] ?? val[0] : val;
          const baseValue =
            oneVal instanceof Date
              ? oneVal
              : moment(oneVal?.replace?.(regExp, ""))?.toDate?.();
          this.$set(this, "baseValue", baseValue);
        }
        if (this.isBaseRange || this.isYearRange || this.isWeekRange) {
          const [val1, val2] = Array.isArray(val)
            ? val.slice(0, 2)
            : [null, val];
          const valRange1 =
            val1 instanceof Date
              ? val1
              : val1
              ? moment(val1?.replace?.(regExp, ""))?.toDate?.()
              : null;
          const valRange2 =
            val2 instanceof Date
              ? val2
              : val2
              ? moment(val2?.replace?.(regExp, ""))?.toDate?.()
              : null;
          if (this.isBaseRange) {
            this.$set(this, "baseValue", [valRange1 ?? "", valRange2]);
          } else {
            this.$set(this, "rangeValues", {
              start: valRange1,
              end: valRange2,
            });
          }
        }
        if (this.isTimeRange) {
          const [val1, val2] = Array.isArray(val)
            ? val.slice(0, 2)
            : [null, val];
          let valRange1 =
            val1 instanceof Date
              ? val1
              : val1
              ? moment(val1?.replace?.(regExp, ""))?.toDate?.()
              : null;
          let valRange2 =
            val2 instanceof Date
              ? val2
              : val2
              ? moment(val2?.replace?.(regExp, ""))?.toDate?.()
              : null;
          if (this.type == "hourrange") {
            if (valRange1 && valRange1.getMinutes() != 0) {
              valRange1.setMinutes(0);
            }
            if (valRange2 && valRange2.getMinutes() != 0) {
              valRange2 = moment(valRange2)
                .add(1, "h")
                .set({ m: 0, s: 0 })
                .toDate();
            }
          }
          if (["minute15range", "minute5range"].includes(this.type)) {
            const Divisor = Number(this.type.match(/[0-9]/g).join(""));
            if (valRange1 && valRange1.getMinutes() % Divisor != 0) {
              valRange1 = moment(valRange1)
                .subtract(valRange1.getMinutes() % Divisor, "m")
                .toDate();
            }
            if (valRange2 && valRange2.getMinutes() % Divisor != 0) {
              valRange2 = moment(valRange2)
                .set("m", parseInt(valRange2.getMinutes() / Divisor) * Divisor)
                .add(Divisor, "m")
                .toDate();
            }
          }
          this.$set(this, "rangeValuesTime", {
            start: valRange1,
            startTime: valRange1 ? moment(valRange1).format("HH:mm") : null,
            end: valRange2,
            endTime: valRange2 ? moment(valRange2).format("HH:mm") : null,
          });
        }
      }
    },
    resetValue() {
      this.baseValue = "";
      this.rangeValues = {
        start: "",
        end: "",
      };
      this.rangeValuesTime = {
        start: "",
        startTime: "",
        end: "",
        endTime: "",
      };
    },
    firstChange() {
      if (this.isBase || this.isBaseRange) {
        this.onBaseChange();
      }
      if (this.isYearRange) {
        this.onYearChange();
      }
      if (this.isWeekRange) {
        this.onWeekChange();
      }
      if (this.isTimeRange) {
        this.onTimeRangeChange();
      }
    },
    emitValue(val) {
      // this.$emit("update:value", val);
      this.$emit("change", val);
      console.log(val);
    },
  },
};
</script>

<style lang="scss" scoped>
.datetime-picker {
  display: inline-block;
  .width100 {
    width: 100%;
  }
  .el-range-separator {
    display: inline-block;
    width: 22px;
    height: 100%;
    margin: 0;
    padding: 0 5px;
    text-align: center;
  }
  .yearrange-wrap {
    .start,
    .end {
      width: 100px;
    }
  }
  .weekrange-wrap {
    .start,
    .end {
      width: 140px;
    }
  }
  .time-wrap {
    .date-picker {
      width: 130px;
    }
    .time-select {
      width: 100px;
    }
    .el-range-separator {
      width: 20px;
    }
  }
}
</style>
