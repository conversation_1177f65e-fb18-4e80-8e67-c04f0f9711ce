<template>
  <svg-icon
    v-if="isSvgIcon"
    :icon-class="iconClass"
    :class-name="className"
    v-on="$listeners"
    v-bind="$attrs"
  ></svg-icon>
  <i v-else :class="[iconClass, className]" v-on="$listeners"></i>
</template>

<script>
export default {
  name: "BaseIcon",
  props: {
    iconClass: {
      type: String,
      required: true,
      default: "el-icon-picture-outline",
    },
    className: {
      type: String,
      default: "",
    },
  },
  computed: {
    isSvgIcon() {
      let reg = /^el-icon/i;
      let regAwe = /^fa fa-/i;
      return !(reg.test(this.iconClass) || regAwe.test(this.iconClass));
    },
  },
};
</script>
