import axios from "@/utils/request";

export const getTopoData = (code, resId, resType, params) => {
  if (!params) {
    params = {};
  }
  params.code = code;
  params.resId = resId || "NULL";
  params.resType = resType || "NULL";
  return axios.request({
    url: "/topoapi/index",
    params,
    method: "get",
  });
};
export const saveTopoData = (code, resId, resType, json) => {
  var data = {
    code: code,
    resId: resId || "NULL",
    resType: resType || "NULL",
    location: json,
  };
  return axios.request({
    url: "/topoapi/save",
    data,
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
  });
};
export const deleteTopoInstance = (code, resId, resType) => {
  var data = {
    code: code,
    resId: resId || "NULL",
    resType: resType || "NULL",
  };
  return axios.request({
    url: "/topoapi/delete",
    data,
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
  });
};
export const alarmRegister = data => {
  return axios.request({
    url: "/alarmapi/login/registerStat",
    data,
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
  });
};
export const alarmUnRegister = sessionId => {
  var data = {
    sessionId,
  };
  return axios.request({
    url: "/alarmapi/login/unregisterStat",
    data,
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
  });
};
export const alarmStat = (
  sessionId,
  topoCode,
  resId,
  resType,
  topoSubCondition
) => {
  var data = {
    sessionId,
    topoCode,
    resId,
    resType,
  };
  if (topoSubCondition) {
    data.topoSubCondition = topoSubCondition;
  }
  return axios.request({
    url: "/alarmapi/stat/topo/cubeDetail",
    data,
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
  });
};
export const alarmReal = sessionId => {
  var data = {
    sessionId,
  };
  return axios.request({
    url: "/alarmapi/stat/topo/alarm/query",
    data,
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
  });
};
