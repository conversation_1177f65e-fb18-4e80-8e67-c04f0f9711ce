<template>
  <div class="layout">
    <topo-toolbar></topo-toolbar>
    <topo-network></topo-network>
    <el-drawer title="属性" :visible.sync="propertyOpen">
      <topo-property :datas="properties"></topo-property>
    </el-drawer>
  </div>
</template>
<script>
import TopoNetwork from "./network";
import TopoToolbar from "./TopoToolbar";
import TopoProperty from "./TopoProperty";
import Bus from "../../framework/bus.js";
export default {
  name: "TopoLayout",
  components: {
    TopoNetwork,
    TopoToolbar,
    TopoProperty,
  },
  data() {
    return {
      propertyOpen: false,
      properties: [],
    };
  },
  mounted() {
    //this.$refs.layout.parentNode.style.padding = 0;
  },
  created() {
    var self = this;
    Bus.$on("network-right-clickData", function(data) {
      var hasProperty = data.a("hasProperty");
      if (hasProperty) {
        self.propertyOpen = true;
        self.getPropertyConfig(data);
      }
    });
  },
  methods: {
    getPropertyConfig(data) {
      var self = this;
      var properties = require("./properties/" + data.a("className") + ".json");
      if (properties) self.createDatas(data, properties);
    },
    createDatas(node, properties) {
      var self = this;
      this.properties = [];
      properties.forEach(pro => {
        self.properties.push({
          name: pro.displayName,
          value: node.a(pro.name),
        });
      });
    },
  },
};
</script>
<style scoped>
.layout {
  border: 1px solid #d7dde4;
  background: #f5f7f9;
  position: relative;
  border-radius: 4px;
  overflow: hidden;
  height: 100%;
}
.header {
  color: #d7dde4;
}
</style>
