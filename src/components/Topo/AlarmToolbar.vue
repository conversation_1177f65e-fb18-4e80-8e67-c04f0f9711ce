<template>
  <div class="alarm-toolbar">
    <div @click="clickHandler('network-select')" title="选择">
      <img src="./icons/alarm/select.png" />
    </div>
    <div @click="clickHandler('network-drag')" title="拖拽">
      <img src="./icons/alarm/drag.png" />
    </div>
    <div @click="clickHandler('network-eye')">
      <img src="./icons/alarm/eye.png" />
    </div>
    <div @click="clickHandler('network-zoom-in')" title="放大">
      <img src="./icons/alarm/zoom_in.png" />
    </div>
    <div @click="clickHandler('network-zoom-out')" title="缩小">
      <img src="./icons/alarm/zoom_out.png" />
    </div>
    <div
      @click="clickHandler('network-double-clickBackground')"
      title="前一视图"
    >
      <img src="./icons/alarm/back.png" />
    </div>
    <div @click="clickHandler('network-zoom-full')" title="全图">
      <img src="./icons/alarm/fit.png" />
    </div>
    <div @click="clickHandler('network-save')" title="保存">
      <img src="./icons/alarm/full.png" />
    </div>
    <div @click="clickHandler('network-reset')" title="重置">
      <img src="./icons/alarm/edit.png" />
    </div>
    <div @click="clickHandler('network-export')" title="导出">
      <img src="./icons/alarm/picture.png" />
    </div>
    <div @click="clickHandler('network-print')" title="打印">
      <img src="./icons/alarm/print.png" />
    </div>
    <div>
      <select v-model="layout" @change="layoutChangeHandler">
        <option
          v-for="item in layouts"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></option>
      </select>
    </div>
    <div title="回车后搜索">
      <input type="text" v-model="searchValue" @keyup.enter="searchHandler" />
    </div>
  </div>
</template>
<script>
export default {
  name: "AlarmToolbar",
  data() {
    return {
      layout: "",
      searchValue: "",
      layouts: [
        {
          label: "初始布局",
          value: "",
        },
        {
          label: "圆形布局",
          value: "circular",
        },
        {
          label: "分层布局",
          value: "hierarchical",
        },
        {
          label: "环形布局",
          value: "symmetric",
        },
        {
          label: "树形布局",
          value: "towardnorth",
        },
        {
          label: "倒树形布局",
          value: "towardsouth",
        },
        {
          label: "东向布局",
          value: "towardeast",
        },
        {
          label: "西向布局",
          value: "towardwest",
        },
        {
          label: "横向对齐",
          action: function () {
            self.doAlign("horizontal");
          },
        },
        {
          label: "纵向对齐",
          action: function () {
            self.doAlign("vertical");
          },
        },
      ],
    };
  },
  methods: {
    clickHandler(value) {
      this.$parent.$refs.TopoGraph.fire(value);
    },
    layoutChangeHandler() {
      this.$parent.$refs.TopoGraph.fire("network-layout", {
        data: this.layout,
      });
    },
    doAlign(mode) {
      this.TopoGraph.fire("network-align", {
        data: mode,
      });
    },
    searchHandler() {
      if (this.searchValue) {
        this.$parent.$refs.TopoGraph.fire("network-search", {
          data: this.searchValue,
        });
      }
    },
  },
};
</script>
<style lang="less" scoped>
.alarm-toolbar {
  position: absolute;
  top: 5px;
  right: 20px;
  width: 505px;
  height: 28px;
  border: 1px solid #9db6cd;
  background-color: #c9d9e7;
  border-radius: 4px;
  display: flex;
  flex-flow: row wrap;
  justify-content: flex-start;
  align-items: center;
  div {
    padding-left: 10px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    img {
      width: 17px;
      height: 17px;
      line-height: 17px;
      &:hover {
        cursor: pointer;
      }
    }
    select {
      width: 70px;
      height: 20px;
      line-height: 20px;
    }
    input {
      width: 78px;
      height: 20px;
      line-height: 20px;
    }
  }
}
</style>
