<template>
  <div class="layout" ref="layout">
    <topo-graph ref="TopoGraph">
      <topo-multi-overview ref="MultiOverview"></topo-multi-overview>
    </topo-graph>
    <alarm-toolbar></alarm-toolbar>
  </div>
</template>
<script>
import ht from "@/assets/js/ht-import.js";
import TopoGraph from "./TopoGraph";
import TopoMultiOverview from "./TopoMultiOverview";
import AlarmToolbar from "./AlarmToolbar";
import {
  alarmRegister,
  alarmUnRegister,
  alarmStat,
  alarmReal,
} from "./api/topo";
export default {
  components: {
    TopoGraph,
    TopoMultiOverview,
    AlarmToolbar,
  },
  mounted() {
    this.$refs.layout.parentNode.style.padding = 0;
    this.initTopoGraph();
  },
  destroyed() {
    this.clearTopoGraphView();
  },
  methods: {
    initTopoGraph() {
      this.clearTopoGraphView();
      this.initTopoGraphProps();
      this.initTopoGraphView();
      this.$refs.TopoGraph.on("network-data-loaded", this.handleDataLoaded);
      this.$refs.TopoGraph.resize();
    },
    initTopoGraphProps() {
      if (this.$route.query.layoutMode) {
        this.$refs.TopoGraph.setLayoutMode(this.$route.query.layoutMode);
      }
      this.$refs.TopoGraph.initProps({
        code: this.$route.query.code,
        resId: this.$route.query.resId,
        resType: this.$route.query.resType,
        background: this.$route.query.background,
        fitcontent: this.$route.query.autoFitContent,
        contextmenuConf: this.$route.query.contextmenuConf,
      });
    },
    initTopoGraphView() {
      this.$refs.TopoGraph.initTopo();
    },
    clearTopoGraphView() {
      if (this.iRealAlarm != null) {
        clearInterval(this.iRealAlarm);
      }
      if (this.$refs.TopoGraph) this.$refs.TopoGraph.clear();
      alarmUnRegister(this.sessionId)
        .then(res => {
          console.info(res);
        })
        .catch(err => {
          console.info(err);
          this.$message.error("拓扑告警取消注册失败!");
        });
    },
    initAlarm() {
      const userInfo = JSON.parse(sessionStorage.getItem("userInfo"));
      this.userId = userInfo ? userInfo.userId : new Date().getTime() + "";
      var severities = ht.AlarmSeverity.severities;
      severities.get(5).color = "#FF0000";
      severities.get(4).color = "#FFA500";
      severities.get(3).color = "#FFFF00";
      severities.get(2).color = "#CCFFFF";

      alarmRegister(this.userId)
        .then(res => {
          this.sessionId = res.data;
          //this.$message.success("拓扑告警注册完成!");
        })
        .catch(err => {
          console.error(err);
          this.$message.error("拓扑告警注册失败!");
        });
    },
    handleDataLoaded(e) {
      this._item = e.data;
      this.startAlarmFetch();
    },
    startAlarmFetch() {
      this.initAlarm();
      this.clearAllAlarm();
      this.getAlarmStat();
    },
    getAlarmStat() {
      let item = this._item;
      if (item == null) return;
      console.info("启动获取统计告警:" + new Date().getTime());
      console.info("sessionId:" + this.sessionId);
      alarmStat(this.sessionId, item.code, item.resId, item.resType)
        .then(res => {
          console.info("获取统计告警:");
          console.info(res.data);
          var statics = res.data;
          for (var i = 0; i < statics.nodes.length; i++) {
            var node = statics.nodes[i];
            var nodeId = node.nodeId;
            for (var j = 0; j < node.alarms.length; j++) {
              var alarm = node.alarms[j];
              this.processAlarm(nodeId, alarm.severity, alarm.newCount, 1);
              this.processAlarm(nodeId, alarm.severity, alarm.ackCount, 2);
            }
          }
          //发出告警变化事件
          if (statics.nodes.length > 0)
            this.$refs.TopoGraph.fire("network-alarm-changed", {
              data: item,
            });

          if (this.iRealAlarm != null) {
            clearInterval(this.iRealAlarm);
          }
          var self = this;
          this.iRealAlarm = setInterval(function() {
            self.getRealAlarm();
          }, 5000);
        })
        .catch(err => {
          console.error(err);
          this.$message.error("获取告警统计失败!");
        });
    },
    getRealAlarm() {
      console.info("启动获取实时告警:" + new Date().getTime());
      alarmReal(this.sessionId)
        .then(res => {
          console.info("获取实时告警:");
          console.info(res.data);
          var array = res.data;
          for (var i = 0; i < array.length; i++) {
            var alarm = array[i];
            this.applyAlarmToElement(alarm); //告警叠加
          }
          //发出告警变化事件
          if (array.length > 0) {
            this.$refs.TopoGraph.fire("network-alarm-changed", {
              data: this._item,
            });
          }
        })
        .catch(err => {
          console.info(err);
          this.$message.error("获取实时告警失败!");
        });
    },
    clearAllAlarm() {
      let item = this._item;
      if (item) {
        var dm = item.dataModel;
        dm.each(function(data) {
          data.getAlarmState().clear();
        });
        console.info("清除所有告警！");
        //发出告警变化事件
        this.$refs.TopoGraph.fire("network-alarm-changed", {
          data: item,
        });
      }
    },
    processAlarm(nodeId, alarmSeverity, alarmCount, flag) {
      if (this._item && this._item.dataModel) {
        var dm = this._item.dataModel;
        var element = dm.getDataById(nodeId);
        var severities = ht.AlarmSeverity.severities;
        var severity = severities.get(6 - alarmSeverity);
        if (element) {
          if (flag == 1) {
            //新产生,PON的最高告警是1>2>3>4>5 而html5的告警时1<2<3<4<5
            if (severity) {
              severity.nickName = "";
              element.getAlarmState().increaseNewAlarm(severity, alarmCount);
            }
          } else if (flag == 2) {
            //已确认
            if (severity) {
              severity.nickName = "";
              element
                .getAlarmState()
                .increaseAcknowledgedAlarm(severity, alarmCount);
            }
          }
        }
      }
    },
    applyAlarmToElement(alarmObject) {
      if (this._item && this._item.dataModel) {
        var msgType = alarmObject["msgType"];
        var element = this._item.dataModel.getDataById(alarmObject["nodeId"]);

        var alarmSeverity = new Number(alarmObject["severity"]);
        var severities = ht.AlarmSeverity.severities;

        if (element != null) {
          if (msgType == "CURRENT_ALARM") {
            //告警产生
            element
              .getAlarmState()
              .increaseNewAlarm(severities.get(6 - alarmSeverity), 1);
          } else if (msgType == "ShortNmsAlarm") {
            var changeStatus = alarmObject["AlarmChangeStatus"];
            var oldAlarmStatus = alarmObject["OldAlarmStatus"];
            if (changeStatus == 1 || changeStatus == 3) {
              //消失
              if (oldAlarmStatus == 2) {
                //确认
                if (
                  element
                    .getAlarmState()
                    .getAcknowledgedAlarmCount(
                      severities.get(6 - alarmSeverity)
                    ) > 1
                )
                  element
                    .getAlarmState()
                    .decreaseAcknowledgedAlarm(
                      severities.get(6 - alarmSeverity)
                    );
              } else {
                if (
                  element
                    .getAlarmState()
                    .getNewAlarmCount(severities.get(6 - alarmSeverity)) > 1
                )
                  element
                    .getAlarmState()
                    .decreaseNewAlarm(severities.get(6 - alarmSeverity));
              }
            } else if (changeStatus == 2) {
              //确认
              if (
                element
                  .getAlarmState()
                  .getNewAlarmCount(severities.get(6 - alarmSeverity)) > 1
              )
                element
                  .getAlarmState()
                  .decreaseNewAlarm(severities.get(6 - alarmSeverity));
              if (
                element
                  .getAlarmState()
                  .getAcknowledgedAlarmCount(
                    severities.get(6 - alarmSeverity)
                  ) > 1
              )
                element
                  .getAlarmState()
                  .increaseAcknowledgedAlarm(severities.get(6 - alarmSeverity));
            }
          }
        }
      }
    },
  },
  //  watch: {
  //    $route: "initTopoGraph",
  //  },
};
</script>
<style scoped>
.layout {
  border: 1px solid #d7dde4;
  background: #f5f7f9;
  position: relative;
  border-radius: 4px;
  overflow: hidden;
  height: calc(100vh - 100px);
}
</style>
