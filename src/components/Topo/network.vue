<template>
  <div class="topo" ref="network"></div>
</template>

<script>
import ht from "@/assets/js/ht-import.js";
import {
  getTopoData,
  saveTopoData,
  deleteTopoInstance,
} from "@/framework/api/topo";
import Bus from "../../framework/bus.js";

export default {
  name: "TopoNetwork",
  props: {
    code: {
      type: String,
      default() {
        return "TEST_TOPO";
      },
    },
    resId: {
      type: String,
      default() {
        return "TEST_TOPO";
      },
    },
    resType: {
      type: String,
      default() {
        return "TEST_TOPO";
      },
    },
  },
  mounted() {
    this.$refs.network.parentNode.style.padding = 0;
    var topoHeight = this.$refs.network.clientHeight;
    var topoWidth = this.$refs.network.clientWidth;

    let dm = (this.dataModel = new ht.DataModel());
    let graphView = (this.graphView = new ht.graph.GraphView(this.dataModel));
    graphView.setWidth(topoWidth);
    graphView.setHeight(topoHeight);
    let view = graphView.getView();

    view.className = "topo";
    graphView.setEditable(false);
    this.$refs.network.appendChild(view);

    //this.initNetwork();
    this.initListener();

    var createNode = function(dm, position, source, group, name) {
      var node = new ht.Node();
      node.s({
        label: name,
        "label.font": "16px arial",
      });
      node.setImage(require("./images/" + name + ".png"));
      node.setPosition(position);
      node.setHost(source);
      node.setParent(group);

      node.a("className", "ROOM");
      //是否有属性窗口
      node.a("hasProperty", true);
      //属性窗口显示的内容，与properties/ROOM.json能对应
      node.a("SN", "12321");
      node.a("LABEL_CN", "xx机房");
      node.a("ROOM_TYPE", "核心机房");
      node.a("SEFVICE_LEVEL", "一级");
      dm.add(node);

      var edge = new ht.Edge(source, node);
      edge.s("edge.type", "bus");
      edge.s("label.rotation", (3 * Math.PI) / 2);
      if (name == "AMF") edge.setName("Namf");
      if (name == "SMF") edge.setName("Nsmf");
      dm.add(edge);

      return node;
    };
    var createGroup = function(name) {
      var group = new ht.Group();
      group.setName(name);
      group.setExpanded(true);
      dm.add(group);
      return group;
    };
    var group = createGroup("");
    group.s({
      "select.width": 0,
      "group.type": "roundRect",
      "group.background": null,
      "group.border.width": 3,
      "group.border.pattern": [8, 3, 3, 3],
    });

    var bus = new ht.Shape();
    bus.setPoints([{ x: 0, y: 25 }, { x: 450, y: 25 }]);
    bus.s({
      "shape.background": null,
      "shape.border.width": 2,
      "shape.border.color": "lightblue",
      "shape.border.cap": "butt",
      "shape.border.join": "miter",
    });
    bus.setParent(group);
    dm.add(bus);

    createNode(dm, { x: 90, y: -50 }, bus, group, "NSSF");
    createNode(dm, { x: 190, y: -50 }, bus, group, "NRF");
    createNode(dm, { x: 290, y: -50 }, bus, group, "PCF");
    createNode(dm, { x: 390, y: -50 }, bus, group, "UDM");

    createNode(dm, { x: 230, y: 100 }, bus, group, "AMF");
    var smf = createNode(dm, { x: 330, y: 100 }, bus, group, "SMF");

    var an = new ht.Node();
    an.s({
      label: "(R)AN",
      "label.font": "16px arial",
    });
    an.setImage(require("./images/AMF.png"));
    an.setPosition({ x: -120, y: 250 });
    an.setHost(bus);
    dm.add(an);

    var upf = new ht.Node();
    upf.s({
      label: "UPF",
      "label.font": "16px arial",
    });
    upf.setImage(require("./images/UPF.png"));
    upf.setPosition({ x: 390, y: 250 });
    upf.setHost(bus);
    upf.setParent(group);
    dm.add(upf);

    var dn = new ht.Node();
    dn.s({
      label: "DN",
      "label.font": "16px arial",
    });
    dn.setImage(require("./images/HOST.png"));
    dn.setPosition({ x: 590, y: 250 });
    dn.setHost(bus);
    dm.add(dn);

    var an2upf = new ht.Edge();
    an2upf.setSource(an);
    an2upf.setTarget(upf);
    an2upf.setName("N3");
    dm.add(an2upf);

    var dn2upf = new ht.Edge();
    dn2upf.setSource(upf);
    dn2upf.setTarget(dn);
    dn2upf.setName("N6");
    dm.add(dn2upf);

    var smf2upf = new ht.Edge();
    smf2upf.setSource(upf);
    smf2upf.setTarget(smf);
    smf2upf.setName("N4");
    dm.add(smf2upf);

    var upf2upf = new ht.Edge();
    upf2upf.setSource(upf);
    upf2upf.setTarget(upf);
    upf2upf.setName("N9");
    dm.add(upf2upf);

    bus.setPosition({ x: 400, y: 150 });
  },
  created() {
    Bus.$on("network-zoom-in", this.zoomIn);
    Bus.$on("network-zoom-out", this.zoomOut);
    Bus.$on("network-zoom-full", this.zoomFull);
    Bus.$on("network-save", this.saveNetwork);
    Bus.$on("network-reset", this.deleteTopoInstance);
    Bus.$on("network-export", this.export);
    Bus.$on("network-print", this.print);
  },
  methods: {
    initNetwork() {
      getTopoData(this.code, this.resId, this.resType)
        .then(res => {
          let json = res.data;

          this.dataModel.clear();
          this.dataModel.deserialize(json, null, true);
          //this.$Message.success("拓扑加载完成!");
        })
        .catch(err => {
          console.info(err);
          // errorHandle(err,this);
        });
    },
    initListener() {
      //禁止系统右键
      document.oncontextmenu = function() {
        return false;
      };
      //处理鼠标左键、右键、双击事件
      this.graphView.addInteractorListener(function(b) {
        if ("clickData" === b.kind) {
          if (b.event.button == 0)
            Bus.$emit("network-left-clickData", {
              data: b.data,
            });
          if (b.event.button == 2)
            Bus.$emit("network-right-clickData", {
              data: b.data,
            });
        } else if ("doubleClickData" === b.kind) {
          Bus.$emit("network-double-clickData", {
            data: b.data,
          });
        }
      });
    },
    saveNetwork() {
      let json = this.dataModel.serialize();
      saveTopoData(this.code, this.resId, this.resType, json)
        .then(res => {
          console.info(res);
          //this.$Message.success("拓扑保存完成!");
        })
        .catch(err => {
          console.info(err);
          //errorHandle(err,this);
        });
    },
    deleteTopoInstance() {
      deleteTopoInstance(this.code, this.resId, this.resType)
        .then(res => {
          console.info(res);
          this.initNetwork();
          //this.$Message.success("拓扑重置完成!");
        })
        .catch(err => {
          console.info(err);
          //errorHandle(err,this);
        });
    },
    getGv() {
      return this.graphView;
    },
    zoomIn() {
      this.graphView.zoomIn(true);
    },
    zoomOut() {
      this.graphView.zoomOut(true);
    },
    zoomFull() {
      this.graphView.fitContent(true);
    },
    export() {
      var w = window.open();
      var self = this;
      setTimeout(function() {
        if (w) {
          var doc = w.document;
          doc.open();
          doc.write("<img src=" + self.graphView.toDataURL() + "/>");
          doc.close();
          doc.body.style.margin = "0";
          doc.title = "导出图片 " + new Date();
        }
      }, 1000);
    },
    print() {
      var w = window.open();
      if (w) {
        var doc = w.document;
        doc.open();
        doc.write("<img src=' + this.graphView.toDataURL('lime') + '/>");
        doc.close();
        doc.body.style.margin = "0";
        w.print();
      }
    },
  },
};
</script>

<style lang="less" scoped>
.topo {
  width: 100%;
  background-color: #ffffff;
  border: 1px solid #d7dde4;
  background: #f5f7f9;
  position: relative;
  border-radius: 4px;
  overflow: hidden;
  height: calc(100vh - 100px);
}
</style>
