<template>
  <div class="topo-multi-overview">
    <div class="expand" @click="expandHandler">
      <i v-if="!expand" class="el-icon-caret-bottom"></i>
      <i v-if="expand" class="el-icon-caret-top"></i>
    </div>
    <div class="snapshot" v-show="expand">
      <div
        v-for="(stack, index) in stacks"
        :key="stack.id"
        @click="changeStack(stack, index)"
        :class="{ selected: index == selectIndex }"
      >
        <img :src="stack.snapshot" />
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: "TopoMultiOverview",
  data() {
    return {
      stacks: [],
      expand: true,
      selectIndex: 0,
    };
  },
  props: {},
  mounted() {
    this.$parent.on("network-stack-changed", this.handleStackChanged);
  },
  methods: {
    handleStackChanged(e) {
      this.stacks = e.data._as;
      console.log(this.stacks);
      this.selectIndex = this.stacks.length > 0 ? this.stacks.length - 1 : 0;
    },
    changeStack(stack, index) {
      this.selectIndex = index;
      this.$parent.fire("network-datamodel-change", {
        data: stack,
      });
    },
    expandHandler() {
      this.expand = !this.expand;
      window.getSelection().empty();
    },
  },
};
</script>

<style lang="less" scoped>
.topo-multi-overview {
  position: absolute;
  right: 20px;
  top: 90px;
  width: 120px;
  height: 324px;
  border-radius: 4px;
  .expand {
    border: 1px solid #9db6cd;
    background-color: #c9d9e7;
    border-radius: 3px;
    width: 100%;
    height: 20px;
    line-height: 20px;
    text-align: center;
    font-size: 20px;
    &:hover {
      cursor: pointer;
    }
  }
  .snapshot {
    overflow: auto;
    div {
      width: 120px;
      border: 1px solid #cccccc;
      border-radius: 2px;
      background-color: #ffffff;
      padding: 2px 3px 2px 3px;
      &:hover {
        cursor: pointer;
        transform: scale(1.1);
        -webkit-transform: scale(1.1);
        -moz-transform: scale(1.1);
        -o-transform: scale(1.1);
        -ms-transform: scale(1.1);
      }
      img {
        width: 100%;
        height: 60px;
        border: 1px solid #cccccc;
        background-color: #ffffff;
      }
    }
    .selected {
      border: 2px solid #ff8c00;
    }
  }
}
</style>
