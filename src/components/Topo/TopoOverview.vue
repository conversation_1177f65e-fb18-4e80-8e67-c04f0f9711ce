<template>
  <div ref="overview"></div>
</template>
<script>
import ht from "@/assets/js/ht-import.js";
export default {
  name: "TopoOverview",
  data() {
    return {};
  },
  mounted() {
    var self = this;
    self._expand = true;
    setTimeout(function() {
      var graphView = self.$parent.getGv();
      var overview = new window.ht.graph.Overview(graphView);
      overview.getView().className = "overview animation";
      self.$refs.overview.appendChild(overview.getView());

      var div = document.createElement("div");
      div.style.setProperty("width", "24px", null);
      div.style.setProperty("height", "24px", null);
      div.style.setProperty("position", "absolute", null);
      div.style.setProperty("left", "0", null);
      div.style.setProperty("top", "0", null);
      div.style.setProperty(
        "background",
        " url(" + require("./icons/expand.png") + ") no-repeat",
        null
      );
      div.style.setProperty("background-position", "center center", null);
      overview._view.appendChild(div);

      function handleTransitionEnd(e) {
        if (e.propertyName === "width") {
          overview.invalidate();
        }
      }
      overview._view.addEventListener(
        "webkitTransitionEnd",
        handleTransitionEnd,
        false
      );
      overview._view.addEventListener(
        "transitionend",
        handleTransitionEnd,
        false
      );
      var eventName = ht.Default.isTouchable ? "touchstart" : "mousedown";
      div.addEventListener(eventName, function(e) {
        if (self._expand) {
          overview._view.style.setProperty("width", "24px", null);
          overview._view.style.setProperty("height", "24px", null);
          overview._canvas.style.setProperty("opacity", "0", null);
          overview._mask.style.setProperty("opacity", "0", null);
          div.style.setProperty(
            "background-image",
            "url(" + require("./icons/shrink.png") + ")",
            null
          );
          div.style.setProperty("width", "24px", null);
          div.style.setProperty("height", "24px", null);
          self._expand = false;
        } else {
          overview._view.style.setProperty("width", "", null);
          overview._view.style.setProperty("height", "", null);
          overview._canvas.style.setProperty("opacity", "1", null);
          overview._mask.style.setProperty("opacity", "1", null);
          div.style.setProperty(
            "background-image",
            "url(" + require("./icons/expand.png") + ")",
            null
          );
          div.style.setProperty("width", "24px", null);
          div.style.setProperty("height", "24px", null);
          self._expand = true;
        }
        overview.invalidate();
        e.stopPropagation();
      });
      overview.setContentBackground("white");
      window.addEventListener(
        "resize",
        function() {
          overview.invalidate();
        },
        false
      );
    }, 1000);
  },
};
</script>

<style lang="less" scoped>
.overview {
  position: absolute;
  right: 0px;
  bottom: 0px;
  width: 200px;
  height: 150px;
  background: rgb(240, 239, 248);
  border-radius: 4px;
}
.animation {
  -webkit-transition: width 0.3s, height 0.3s;
  -moz-transition: width 0.3s, height 0.3s;
  -o-transition: width 0.3s, height 0.3s;
  transition: width 0.3s, height 0.3s;
}
</style>
