<template>
  <div class="layout" ref="layout">
    <topo-toolbar ref="TopoToolbar"></topo-toolbar>
    <topo-graph ref="TopoGraph">
      <topo-overview ref="TopoOverview"></topo-overview>
    </topo-graph>
    <el-drawer title="属性" :visible.sync="propertyOpen">
      <topo-property :datas="properties"></topo-property>
    </el-drawer>
  </div>
</template>
<script>
import TopoToolbar from "./TopoToolbar";
import TopoGraph from "./TopoGraph";
import TopoOverview from "./TopoOverview";
import TopoProperty from "./TopoProperty";

export default {
  components: {
    TopoToolbar,
    TopoGraph,
    TopoProperty,
    TopoOverview,
  },
  data() {
    return {
      propertyOpen: false,
      properties: [],
    };
  },
  mounted() {
    this.$refs.layout.parentNode.style.padding = 0;
    this.initTopoGraph();
  },
  methods: {
    initTopoGraph() {
      this.clearTopoGraphView();
      this.initTopoGraphProps();
      this.initTopoGraphView();

      var self = this;
      this.$refs.TopoGraph.on("network-right-clickData", function(e) {
        var hasProperty = e.data.a("hasProperty");
        if (hasProperty) {
          self.propertyOpen = true;
          self.getPropertyConfig(e.data);
        }
      });

      this.$refs.TopoGraph.resize();
    },
    initTopoGraphProps() {
      if (this.$route.query.layoutMode) {
        this.$refs.TopoGraph.setLayoutMode(this.$route.query.layoutMode);
      }
      this.$refs.TopoGraph.initProps({
        code: this.$route.query.code,
        resId: this.$route.query.resId,
        resType: this.$route.query.resType,
        background: this.$route.query.background,
        fitcontent: this.$route.query.autoFitContent,
        contextmenuConf: this.$route.query.contextmenuConf,
        layoutMode: this.$route.query.layoutMode,
      });
    },
    initTopoGraphView() {
      this.$refs.TopoGraph.initTopo();
    },
    clearTopoGraphView() {
      this.$refs.TopoGraph.clear();
    },
    getPropertyConfig(data) {
      var self = this;
      var properties = require("./properties/" + data.a("className") + ".json");
      if (properties) self.createDatas(data, properties);
    },
    createDatas(node, properties) {
      var self = this;
      this.properties = [];
      properties.forEach(pro => {
        self.properties.push({
          name: pro.displayName,
          value: node.a(pro.name),
        });
      });
    },
  },
  //  watch: {
  //    $route: "initTopoGraph",
  //  },
};
</script>
<style scoped>
.layout {
  border: 1px solid #d7dde4;
  background: #f5f7f9;
  position: relative;
  border-radius: 4px;
  overflow: hidden;
  height: calc(100vh - 100px);
}
</style>
