<template>
  <div class="topo" ref="view">
    <el-table size="small" :data="datas" border>
      <el-table-column prop="name" label="名称" align="center">
      </el-table-column>
      <el-table-column prop="value" label="值" align="center">
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  name: "TopoProperty",
  data() {
    return {};
  },
  props: {
    datas: Array,
  },
};
</script>
<style lang="less" scoped>
.topo {
  width: 100%;
  height: 100%;
  background-color: #ffffff;
}
</style>
