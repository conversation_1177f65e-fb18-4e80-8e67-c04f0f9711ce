<template>
  <div class="toolbar" ref="toolbar"></div>
</template>

<script>
import ht from "@/assets/js/ht-import.js";
export default {
  name: "TopoToolbar",
  mounted() {
    this.$refs.toolbar.parentNode.style.padding = 0;
    this.TopoGraph = this.$parent.$refs.TopoGraph;

    var height = this.$refs.toolbar.clientHeight;
    var width = this.$refs.toolbar.clientWidth;
    var self = this;
    var items = [
      {
        id: "prefixView",
        icon: require("./icons/pre_view.png"),
        label: "前一视图",
        action: function () {
          self.TopoGraph.fire("network-double-clickBackground");
        },
      },
      {
        id: "zoomIn",
        icon: require("./icons/zoom_in.png"),
        label: "放大",
        action: function () {
          self.TopoGraph.fire("network-zoom-in");
        },
      },
      {
        id: "zoomOut",
        icon: require("./icons/zoom_out.png"),
        label: "缩小",
        action: function () {
          self.TopoGraph.fire("network-zoom-out");
        },
      },
      {
        id: "zoomFull",
        icon: require("./icons/zoomOverview.png"),
        label: "全图",
        action: function () {
          self.TopoGraph.fire("network-zoom-full");
        },
      },
      {
        id: "save",
        icon: require("./icons/eye_off.png"),
        label: "保存",
        action: function () {
          self.TopoGraph.fire("network-save");
        },
      },
      {
        id: "reset",
        icon: require("./icons/eye_off2.png"),
        label: "重置",
        action: function () {
          self.TopoGraph.fire("network-reset");
        },
      },
      {
        id: "export",
        icon: require("./icons/dc.png"),
        label: "导出",
        action: function () {
          self.TopoGraph.fire("network-export");
        },
      },
      {
        id: "print",
        icon: require("./icons/dy.png"),
        label: "打印",
        action: function () {
          self.TopoGraph.fire("network-print");
        },
      },
      {
        element: this.getLayoutMenu(),
      },
      {
        element: this.getSearchInput(),
      },
    ];
    var toolbar = (this.toolbar = new ht.widget.Toolbar(items));
    toolbar.setWidth(width);
    toolbar.setHeight(height);
    this.$refs.toolbar.appendChild(toolbar.getView());
  },
  methods: {
    getSearchInput() {
      var self = this;
      var searchInput = ht.Default.createElement("input");
      searchInput.value = "";
      searchInput.title = "回车搜索！";
      searchInput.style.width = "150px";
      searchInput.style.height = "25px";
      searchInput.onkeydown = function (e) {
        if (e.keyCode === 13) {
          if (searchInput.value != null) {
            var value = searchInput.value;
            if (value && value.length > 0) {
              self.TopoGraph.fire("network-search", {
                data: value,
              });
            }
          }
        }
      };
      return searchInput;
    },
    getLayoutMenu() {
      var self = this;
      var layoutMenu = new window.ht.widget.Menu([
        {
          icon: require("./icons/bj.png"),
          label: "布局",
          items: [
            {
              label: "初始布局",
              action: function () {
                self.doLayout();
              },
            },
            {
              label: "圆形布局",
              action: function () {
                self.doLayout("circular");
              },
            },
            {
              label: "环形布局",
              action: function () {
                self.doLayout("symmetric");
              },
            },
            {
              label: "分层布局",
              action: function () {
                self.doLayout("hierarchical");
              },
            },
            {
              label: "树形布局",
              action: function () {
                self.doLayout("towardsouth");
              },
            },
            {
              label: "倒树形布局",
              action: function () {
                self.doLayout("towardnorth");
              },
            },
            {
              label: "东向布局",
              action: function () {
                self.doLayout("towardeast");
              },
            },
            {
              label: "西向布局",
              action: function () {
                self.doLayout("towardwest");
              },
            },
            {
              label: "横向对齐",
              action: function () {
                self.doAlign("horizontal");
              },
            },
            {
              label: "纵向对齐",
              action: function () {
                self.doAlign("vertical");
              },
            },
          ],
        },
      ]);
      layoutMenu.getView().style.display = "inline-block";
      return layoutMenu;
    },
    doLayout(mode) {
      this.TopoGraph.fire("network-layout", {
        data: mode,
      });
    },
    doAlign(mode) {
      this.TopoGraph.fire("network-align", {
        data: mode,
      });
    },
  },
};
</script>

<style lang="less" scoped>
.toolbar {
  width: 100%;
  height: 35px;
  background-color: #ffffff;
}
</style>
