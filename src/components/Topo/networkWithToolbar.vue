<template>
  <div class="layout" ref="layout">
    <topo-toolbar></topo-toolbar>
    <topo-network></topo-network>
  </div>
</template>
<script>
import TopoNetwork from "./network";
import TopoToolbar from "./TopoToolbar";
export default {
  components: {
    TopoNetwork,
    TopoToolbar,
  },
  mounted() {
    this.$refs.layout.parentNode.style.padding = 0;
  },
};
</script>
<style scoped>
.layout {
  border: 1px solid #d7dde4;
  background: #f5f7f9;
  position: relative;
  border-radius: 4px;
  overflow: hidden;
  height: 100%;
}
.header {
  color: #d7dde4;
}
</style>
