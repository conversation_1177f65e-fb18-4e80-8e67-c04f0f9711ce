<template>
  <div class="topo" ref="container">
    <div class="topo" ref="graph"></div>
    <slot></slot>
  </div>
</template>

<script>
import ResizeObserver from "resize-observer-polyfill";
import { getTopoData, saveTopoData, deleteTopoInstance } from "./api/topo";
import ht from "@/assets/js/ht-import.js";
import Vue from "vue";

export default {
  name: "TopoGraph",
  props: {
    _code: {
      type: String,
    },
    _resId: {
      type: String,
    },
    _resType: {
      type: String,
    },
    _layoutMode: {
      type: String,
    },
    _background: {
      type: String,
    },
    _autoFitContent: {
      type: <PERSON>olean,
    },
    _contextmenuConf: {
      type: Object,
    },
  },
  data() {
    return {
      code: this._code,
      resId: this._resId,
      resType: this._resType,
      layoutMode: this._layoutMode,
      background: this._background,
      fitcontent: this._autoFitContent,
      contextmenuConf:
        this._contextmenuConf || require("./contextmenus/default.js"),
      events: {},
    };
  },
  mounted() {
    var self = this;

    //this.stack = new ht.List();
    let graphView = (window.gv = this.graphView = new ht.graph.GraphView());
    graphView.enableToolTip();
    graphView.dm().enableAnimation();

    window.gv = graphView;
    let view = graphView.getView();

    view.className = "topo";
    graphView.setEditable(false);
    this.$refs.graph.appendChild(view);

    //禁止系统右键
    document.oncontextmenu = function() {
      return false;
    };
    this.contextmenu = new ht.widget.ContextMenu();

    this.graphView.getBodyColor = function(data) {
      return data.s("body.color") ? data.s("body.color") : "";
    };

    //处理鼠标左键、右键、图元双击、空白处双击事件
    this.graphView.addInteractorListener(function(b) {
      let data = b.data;
      if ("clickData" === b.kind) {
        setTimeout(() => {
          if (data.isDouble) {
            return;
          }
          if (b.event.button == 0) {
            self.fire("network-left-clickData", {
              data,
            });
            self.$emit("network-left-clickData", {
              data,
            });
          }
          if (b.event.button == 2)
            self.fire("network-right-clickData", {
              data,
            });
        }, 500);
      } else if ("doubleClickData" === b.kind) {
        data.isDouble = true;
        setTimeout(() => {
          data.isDouble = false;
        }, 600);
        self.fire("network-double-clickData", b);
      } else if ("doubleClickBackground" === b.kind) {
        self.fire("network-double-clickBackground");
      } else if ("clickBackground" == b.kind) {
        console.info(b);
        if (b.event.button == 0) self.fire("network-left-clickBackground");
        if (b.event.button == 2) self.fire("network-right-clickBackground");
      }
    });
    window.addEventListener(
      "resize",
      function() {
        setTimeout(function() {
          self.resize();
        }, 100);
      },
      false
    );
    let ro = new ResizeObserver(() => {
      setTimeout(function() {
        self.resize();
      }, 100);
    });

    ro.observe(this.$refs.container);
    this.on("network-zoom-in", this.zoomIn);
    this.on("network-zoom-out", this.zoomOut);
    this.on("network-zoom-full", this.zoomFull);
    this.on("network-save", this.saveTopo);
    this.on("network-reset", this.deleteTopoInstance);
    this.on("network-export", this.export);
    this.on("network-print", this.print);
    this.on("network-layout", this.doLayout);
    this.on("network-align", this.doAlign);
    this.on("network-search", this.search);

    this.on("network-double-clickData", this.handleDoubleClick);
    this.on("network-double-clickBackground", this.handleDoubleClickBackground);
    this.on("network-right-clickBackground", this.handleRightClickBackground);
    this.on("network-right-clickData", this.handleRightClickData);

    this.on("network-datamodel-change", this.handleDataModelChange);
    this.on("nodes-page-change", this.handleNodesPageChange);

    this.initPagination();
  },
  methods: {
    initProps(props) {
      this.code = props.code || "TEST_TOPO";
      this.resId = props.resId;
      this.resType = props.resType;
      this.layoutMode = props.layoutMode;
      this.background = props.background || "background.jpg";
      this.fitcontent = props.fitcontent;
      this.contextmenuConf = props.contextmenuConf || this.contextmenuConf;
    },
    initTopo() {
      this.stack = new ht.List();
      this.graphView.setZoom(1);
      this.graphView.tx(0);
      this.graphView.ty(0);
      this.loadData(this.code, this.resId, this.resType);
      this.setTopoBackground();
      //if (this.background) this.setBackground();
    },
    initPagination() {
      let self = this;
      ht.Default.setImage("pagination", {
        width: 250,
        height: 35,
        pixelPerfect: false,
        scrollable: true,
        interactive: true,
        renderHTML: function(data, gv, cache) {
          if (!cache.htmlView) {
            var div = (cache.htmlView = document.createElement("div")),
              vueDiv = (cache.vueDiv = document.createElement("div"));
            vueDiv.innerHTML =
              `
                <el-pagination ref="pagination"
                layout="prev, pager, next"
                :page-size=` +
              (data.a("pageSize") || 10) +
              ` :page-count=` +
              (data.a("pageCount") || 50) +
              `>
                </el-pagination>`;
            div.appendChild(vueDiv);
            //div.style.position = "absolute";
            div.layoutHTML = function() {
              gv.layoutHTML(data, div, true);
            };
            var obj = new Vue().$mount(vueDiv);
            var pagination = obj.$refs.pagination;
            pagination.$on("current-change", function(pageNum) {
              //派发事件
              self.fire("nodes-page-change", pageNum);
            });
          }
          var content = data.a("content");
          if (content && content !== cache.content) {
            cache.vueDiv.innerHTML = cache.content = content;
          }
          return cache.htmlView;
        },
        comps: [],
      });
    },
    handleNodesPageChange(pageNum) {
      console.log(pageNum);
      this.graphView.isVisible = function(data) {
        let pn = data.a("pageNum");
        if (pn) {
          if (pn == pageNum) {
            return true;
          } else {
            return false;
          }
        } else {
          return true;
        }
      };
      //this.graphView.ivm();
      this.graphView.iv();
      this.graphView.dm().each(function(data) {
        let pn = data.a("pageNum");
        if (pn && pn == pageNum) {
          data.setAnimation({
            show: {
              property: "opacity",
              accessType: "style",
              from: 0,
              to: 1,
              repeat: false,
            },
            start: ["show"],
          });
        }
      });
    },
    setBackground() {
      //背景图片
      if (this.background) {
        this.graphView.getView().style.background =
          "url(" + require("./images/" + this.background) + ") no-repeat";
        this.graphView.getView().style.backgroundSize = "100% 100%";
        this.graphView.getView().style.backgroundAttachment = "fixed";
      }
    },
    loadData(code, resId, resType, params) {
      this.graphView.setDisabled(true, require("./images/loading.gif"));
      getTopoData(code, resId, resType, params)
        .then(res => {
          let json = res.data;
          let dm = new ht.DataModel();
          dm.enableAnimation();
          dm.deserialize(json, null, true);
          this.graphView.setDataModel(dm);
          this.handleNodesPageChange(1);
          for (var i = 0; i < dm.getDatas().size(); i++) {
            var data = dm.getDatas().get(i);
            if (
              "ht.Node" ==
              dm
                .getDatas()
                .get(i)
                .getClassName()
            ) {
              let width = data.getWidth() ? data.getWidth() : 32;
              let height = data.getHeight() ? data.getHeight() : 32;
              data.setSize(width, height);
              data.s("select.color", "red");
              data.s("select.width", "3");
            }
            //使用相对路径
            if (
              data.getImage &&
              data.getImage() &&
              data.getImage() != "node_image" &&
              data.getImage() != "group_image" &&
              data.getImage() != "pagination" &&
              data.getImage() != "subGraph_image"
            )
              data.setImage(require("@/" + data.getImage()));
            if (data.s("group.image")) {
              data.setStyle(
                "group.image",
                require("@/" + data.s("group.image"))
              );
            }
          }
          var layoutmode = this.layoutMode || dm.a("layoutmode");
          if (layoutmode) this.setLayout(layoutmode);
          if (this.fitcontent === true || dm.a("fitcontent") === true)
            this.graphView.fitContent(true);
          this.graphView.setDisabled(false);
          //this.$message.success("拓扑加载完成!");
          this.graphView.invalidate();

          var selectData = this.graphView.dm().getDataById(this.resId);
          if (selectData) {
            this.graphView
              .dm()
              .sm()
              .as(selectData);
          }

          var item = {
            id: new Date().getTime(),
            code: code,
            resId: resId,
            resType: resType,
            snapshot: this.graphView.toDataURL(),
            dataModel: dm,
            zoom: this.graphView.getZoom(),
            tx: this.graphView.tx(),
            ty: this.graphView.ty(),
            layoutmode: layoutmode,
            fitcontent: this.fitcontent === true || dm.a("fitcontent") === true,
          };
          this.stack.add(item);

          this.fire("network-data-loaded", {
            data: item,
          });
          this.fire("network-stack-changed", {
            data: this.stack,
          });
          this.$emit("network-data-loaded", {});
        })
        .catch(err => {
          console.error(err);
          this.$message.error("拓扑加载失败!");
          this.graphView.setDisabled(false);
        });
    },
    selectDataFromDm(dm, resId) {
      if (!resId) {
        return null;
      }
      let selectData = null;
      dm.getDatas().forEach(node => {
        if (node && node.a && node.a("id") == resId) {
          selectData = node;
          return;
        }
      });
      return selectData;
    },
    addStack(item) {
      if (this.stack) {
        this.stack.each(data => {
          if (data && data.code == item.code) {
            this.stack.remove(data);
          }
        });
        this.stack.add(item);
      }
    },
    saveTopo() {
      let json = this.graphView.dm().serialize();
      let item = this.stack.get(this.stack.size() - 1);
      saveTopoData(item.code, item.resId, item.resType, json)
        .then(res => {
          console.info(res);
          this.$message.success("拓扑保存完成!");
        })
        .catch(err => {
          console.error(err);
          this.$message.error("拓扑保存失败!");
        });
    },
    deleteTopoInstance() {
      let item = this.stack.get(this.stack.size() - 1);
      deleteTopoInstance(item.code, item.resId, item.resType)
        .then(res => {
          console.info(res);
          this.clear();
          this.initTopo();
          this.$message.success("拓扑重置完成!");
        })
        .catch(err => {
          console.error(err);
          this.$message.error("拓扑重置失败!");
        });
    },
    getGv() {
      return this.graphView;
    },
    zoomIn() {
      this.graphView.zoomIn(true);
    },
    zoomOut() {
      this.graphView.zoomOut(true);
    },
    zoomFull() {
      this.graphView.fitContent(true);
    },
    export() {
      var w = window.open();
      var self = this;
      setTimeout(function() {
        if (w) {
          var doc = w.document;
          doc.open();
          doc.write("<img src='" + self.graphView.toDataURL() + "'/>");
          doc.close();
          doc.body.style.margin = "0";
          doc.title = "导出图片 " + new Date();
        }
      }, 1000);
    },
    print() {
      var w = window.open();

      if (w) {
        var doc = w.document;
        doc.open();
        doc.write("<img src='" + this.graphView.toDataURL("lime") + "'/>");
        doc.close();
        doc.body.style.margin = "0";
        setTimeout(function() {
          w.print();
        }, 1000);
      }
    },
    search(e) {
      var value = e.data;
      var gv = this.graphView;
      gv.dm().each(function(data) {
        if (
          (data.getName() && data.getName().indexOf(value) > -1) ||
          (data.getToolTip() && data.getToolTip().indexOf(value) > -1)
        ) {
          {
            gv.dm()
              .sm()
              .setSelection(data);
          }
        }
      });
    },
    setLayoutMode(layoutMode) {
      this.layoutMode = layoutMode;
    },
    setLayout(layoutMode) {
      if (this.layout == null) {
        this.layout = new ht.layout.AutoLayout(this.graphView);
        this.layout.isLayoutable = function(data) {
          if (data.a("IN_AUTO_LAYOUT") === false) {
            return false;
          }
          return true;
        };
        this.layout.setEasing(this.bounceOut);
      }
      this.layout.layout(layoutMode);
    },
    doLayout(e) {
      var layoutType = e.data;
      if (layoutType == null || layoutType.length == 0) {
        this.initTopo();
      } else {
        var autoLayout = new ht.layout.AutoLayout(this.graphView);
        autoLayout.isLayoutable = function(data) {
          if (data.a("IN_AUTO_LAYOUT") === false) {
            return false;
          }
          return true;
        };
        autoLayout.setEasing(this.bounceOut);
        autoLayout.layout(layoutType);
      }
    },
    doAlign(e) {
      var alignMode = e.data;
      let centerXSum = 0;
      let centerYSum = 0;
      const smNodes = new ht.List();
      this.graphView
        .dm()
        .sm()
        .each(function(data) {
          //判断是否Node
          if (data instanceof ht.Node) {
            const { x, y } = data.p();
            centerXSum += x;
            centerYSum += y;
            smNodes.add(data);
          }
        });
      const size = smNodes.size();
      if (size <= 1) {
        this.$message.info("请选择多个图元!");
      } else {
        const centerX = centerXSum / size;
        const centerY = centerYSum / size;
        smNodes.each(function(item) {
          const { x, y } = item.p();
          if (alignMode == "horizontal") {
            item.p({ x: x, y: centerY });
          } else {
            item.p({ x: centerX, y: y });
          }
        });
      }
    },
    bounceOut: function(t) {
      var s = 7.5625,
        r;
      if (t < 1 / 2.75) {
        r = s * t * t;
      } else if (t < 2 / 2.75) {
        r = s * (t -= 1.5 / 2.75) * t + 0.75;
      } else if (t < 2.5 / 2.75) {
        r = s * (t -= 2.25 / 2.75) * t + 0.9375;
      } else {
        r = s * (t -= 2.625 / 2.75) * t + 0.984375;
      }
      return r;
    },
    clear() {
      this.graphView.dm().clear();
      this.layoutMode = "";
      this.stack = new ht.List();
    },
    handleDoubleClick(e) {
      var data = e.data;

      var code = data.a("zoom.code");
      var resId = data.a("zoom.res.id");
      var resType = data.a("zoom.res.type");
      var disable = data.a("zoom.disable");

      if (code && !disable) {
        if (this.graphView.getCurrentSubGraph() != null) {
          this.graphView.upSubGraph();
        }
        this.code = code;
        this.resId = resId;
        this.resType = resType;
        this.loadData(code, resId, resType, data._attrObject);
      }
    },
    handleDoubleClickBackground() {
      console.info("handleDoubleClickBackground");
      console.info(this.graphView.getCurrentSubGraph());
      console.info("this.stack.size():::" + this.stack.size());
      if (
        this.stack.size() > 1 &&
        this.graphView.getCurrentSubGraph() === null
      ) {
        var item = this.stack.get(this.stack.size() - 2);

        this.graphView.setDataModel(item.dataModel);
        this.graphView.setZoom(item.zoom);
        this.graphView.tx(item.tx);
        this.graphView.ty(item.ty);
        if (item.layoutmode) this.setLayout(item.layoutmode);
        if (item.fitcontent) this.graphView.fitContent(true);

        this.stack.removeAt(this.stack.size() - 1);

        this.fire("network-data-loaded", {
          data: item,
        });
        this.fire("network-stack-changed", {
          data: this.stack,
        });
        this.$emit("network-data-loaded", {});
      } else {
        this.$message.info("无前一视图!");
      }
    },
    handleRightClickData(e) {
      var data = e.data;
      var className = data.a("className");
      console.info(data);
      var cm = this.contextmenuConf;
      if (cm) {
        var items = cm.default[className];

        if (items) {
          for (var i = 0; i < items.length; i++) {
            items[i].data = data;
            items[i].scope = this;
          }
          this.contextmenu.setItems(items);
        } else this.contextmenu.setItems([]);

        this.contextmenu.addTo(this.graphView.getView());
      }
    },
    handleRightClickBackground() {
      var cm = require("./contextmenus/topo.js");
      if (cm) {
        var items = cm.default[this.code];
        if (items) {
          for (var i = 0; i < items.length; i++) {
            items[i].code = this.code;
            items[i].resId = this.resId;
            items[i].resType = this.resType;
          }
          this.contextmenu.setItems(items);
        } else {
          this.contextmenu.setItems([]);
        }
        this.contextmenu.addTo(this.graphView.getView());
      }
      this.contextmenu.addTo(this.graphView.getView());
    },
    handleDataModelChange(e) {
      var item = e.data;
      this.graphView.setDataModel(item.dataModel);
      this.graphView.setZoom(item.zoom);
      this.graphView.tx(item.tx);
      this.graphView.ty(item.ty);
      if (item.layoutmode) this.setLayout(item.layoutmode);
      if (item.fitcontent) this.graphView.fitContent(true);
      this.$emit("network-data-loaded", {});
      this.fire("network-data-loaded", {
        data: item,
      });
    },
    resize() {
      let graphView = this.graphView;
      graphView.invalidate();

      if (this.$refs.graph) {
        if (this.$refs.graph.parentNode)
          this.$refs.graph.parentNode.style.padding = 0;
        var topoHeight = this.$refs.graph.clientHeight;
        var topoWidth = this.$refs.graph.clientWidth;
        graphView.setWidth(topoWidth);
        graphView.setHeight(topoHeight);
      }
    },
    invalidate() {
      this.graphView.invalidate();
    },
    on(eventName, listener, scope) {
      var notifier = this.events[eventName];
      if (notifier == null) {
        notifier = new ht.Notifier();
      }
      notifier.add(listener, scope);
      this.events[eventName] = notifier;
    },
    off(eventName, listener, scope) {
      var notifier = this.events[eventName];
      if (notifier) notifier.remove(listener, scope);
    },
    fire(eventName, data) {
      var notifier = this.events[eventName];
      if (notifier) notifier.fire(data);
    },
    getBackgroundColor() {
      var t = this.$store.state.settings.themeName;
      return t == "default" ? "white" : "#32667b";
    },
    setTopoBackground() {
      this.graphView.getView().style.background = this.getBackgroundColor();
    },
  },
  watch: {
    "$store.state.settings.themeName": "setTopoBackground",
  },
};
</script>

<style scoped>
/deep/ .el-pagination .btn-prev .el-icon {
  font-size: 20px;
}
/deep/ .el-pagination .btn-next .el-icon {
  font-size: 20px;
}
/deep/ .el-pagination .el-pager li {
  font-size: 15px;
}
.topo {
  width: 100%;
  height: 100%;
  background-color: #ffffff;
}
</style>
