import Bus from "../../../framework/bus.js";
//空白处右键
export default {
  LOCATE_DATA_SELECT: [
    {
      label: "圈选完成",
      action: function(e) {
        console.info(Bus);
        console.info(e.code);
        console.info(e.graphView);
        console.info(e.map);
        console.info(e.scope);

        var datas = [];
        e.graphView
          .dm()
          .sm()
          .each(function(data) {
            datas.push({
              id: data.a("id"),
              name: data.a("name"),
              className: data.a("className"),
            });
          });
        this.$confirm("圈选的对象数量为:" + datas.length, "请确认", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            e.scope.$emit("LOCATE_DATA_SELECT", datas);
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: "请继续圈选对象！",
            });
          });
      },
    },
  ],
};
