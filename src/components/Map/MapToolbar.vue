<template>
  <div class="map-toolbar">
    <div @click="clickHandler('map-select')" title="点选">
      <img src="./icons/select.png" />
    </div>
    <div @click="clickHandler('map-drag')" title="平移">
      <img src="./icons/drag.png" />
    </div>
    <div @click="clickHandler('map-zoom-full')" title="全图">
      <img src="./icons/full.png" />
    </div>
    <div @click="clickHandler('map-zoom-in')" title="放大">
      <img src="./icons/zoom_in.png" />
    </div>
    <div @click="clickHandler('map-zoom-out')" title="缩小">
      <img src="./icons/zoom_out.png" />
    </div>
    <div @click="clickHandler('map-measure-distance')" title="测距">
      <img src="./icons/ruler.png" />
    </div>
    <div @click="clickHandler('map-measure-area')" title="测面">
      <img src="./icons/area.png" />
    </div>
    <div @click="clickHandler('map-clear')" title="清除">
      <img src="./icons/drag.png" />
    </div>
    <!--
    <div @click="clickMeasure()" title="测量">
      <img :src="measureSrc" />
    </div>-->
  </div>
</template>
<script>
import L from "leaflet";
require("lautec-leaflet-measure");

export default {
  name: "MapToolbar",
  data() {
    return {
      layout: "",
      measureStatus: true,
      measureSrc: require("./icons/ruler.png"),
    };
  },
  mounted() {
    var map = (this.map = this.$parent.$parent.mapObject);
    this.measureControl = L.control.measure({});
    this.measureControl.addTo(this.map);
    map.on("measurestart", function() {
      console.info("measurestart>>>>>>>>>>>");
    });
  },
  methods: {
    clickHandler(value) {
      this.map.fire(value);
    },
    clickMeasure() {
      if (this.measureStatus) {
        this.measureSrc = require("./icons/drag.png");
        this.measureStatus = false;
        this.measureControl._startMeasure();
      } else {
        this.measureStatus = true;
        this.measureSrc = require("./icons/ruler.png");
        this.measureControl._layer.clearLayers();
        this.map.fire("measurestop");
      }
    },
  },
};
</script>
<style lang="less" scoped>
@import "./css/leaflet-measure.css";
.map-toolbar {
  width: 250px;
  height: 30px;
  border: 1px solid #9db6cd;
  background-color: #ffffff;
  border-radius: 4px;
  display: flex;
  flex-flow: row wrap;
  justify-content: flex-start;
  align-items: center;
  div {
    padding-left: 10px;
    height: 20px;
    line-height: 20px;
    img {
      width: 20px;
      height: 20px;
      line-height: 20px;
      &:hover {
        cursor: pointer;
      }
    }
    select {
      width: 70px;
      height: 23px;
      line-height: 23px;
    }
  }
}
</style>
