<template>
  <div class="measure-popup">
    <strong>长度：</strong
    ><span>&nbsp;&nbsp;&nbsp;{{ length | filterLength }}公里</span>
    <br />
    <br />
    <strong>面积：</strong
    ><span>&nbsp;&nbsp;&nbsp;{{ area | filterArea }}平方公里</span>
  </div>
</template>

<script>
export default {
  name: "MapMeasurePopup",
  props: ["length", "area"],
  filters: {
    filterLength: function(value) {
      return (value / 1000).toFixed(2);
    },
    filterArea: function(value) {
      return (value / (1000 * 1000)).toFixed(2);
    },
  },
};
</script>

<style scoped>
.measure-popup {
  width: 200px;
  padding: 10px 10px;
  border-radius: 5px;
}
</style>
