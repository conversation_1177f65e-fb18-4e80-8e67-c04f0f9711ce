<template>
  <div class="vue-leaflet" ref="container">
    <l-map id="map" ref="map" style="width: 100%; height:100%;">
      <l-feature-group ref="features">
        <l-popup :options="options">
          <map-measure-popup
            :length="measureLength"
            :area="measureArea"
          ></map-measure-popup>
        </l-popup>
      </l-feature-group>
      <l-control position="topright">
        <map-toolbar v-show="showToolbar"></map-toolbar>
      </l-control>
    </l-map>
  </div>
</template>

<script>
import {
  addResizeListener,
  removeResizeListener,
} from "element-ui/src/utils/resize-event";
import L from "leaflet";
import { LMap, LControl, LPopup, LFeatureGroup } from "vue2-leaflet";
import MapToolbar from "./MapToolbar";
import MapMeasurePopup from "./MapMeasurePopup";
import ht from "@/assets/js/ht-import.js";
import { Icon } from "leaflet";
import "leaflet.icon.glyph";
import "leaflet/dist/leaflet.css";
import "leaflet.markercluster/dist/MarkerCluster.css";
import "leaflet.markercluster/dist/MarkerCluster.Default.css";
import HeatmapOverlay from 'heatmap.js/plugins/leaflet-heatmap'
delete Icon.Default.prototype._getIconUrl;

Icon.Default.mergeOptions({
  iconRetinaUrl: require("leaflet/dist/images/marker-icon-2x.png"),
  iconUrl: require("leaflet/dist/images/marker-icon.png"),
  shadowUrl: require("leaflet/dist/images/marker-shadow.png"),
});
import jsonp from "jsonp";

require("./api/DrawInteractor.js");
var esri = require("esri-leaflet");
require("proj4");
require("proj4leaflet");
require("leaflet.markercluster");
require("./api/baidu.js");

export default {
  name: "MapView",
  components: {
    LMap,
    MapToolbar,
    LControl,
    LPopup,
    LFeatureGroup,
    MapMeasurePopup,
  },
  props: {
    _code: {
      type: String,
    },
    _config: {
      type: Object,
    },
    _locateDatas: {
      type: Array,
    },
    _contextmenuConf: {
      type: Object,
    },
    _mapContextmenuConf: {
      type: Object,
    },
    _state: {
      type: String,
    },
    _clusterDatas: {
      type: Array,
    },
    _clusterConfig: {
      type: Object,
    },
    _heatmapConfig: {
      type: Object,
    },
    _showMiniMap: {
      type: Boolean,
    },
    _showToolbar: {
      type: Boolean,
    },
  },
  data() {
    return {
      options: {
        autoClose: false,
      },
      measureLength: 0,
      measureArea: 0,
      showToolbar: !!this._showToolbar || !!this.$route.query.showToolbar,
      showMiniMap: !!this._showMiniMap || !!this.$route.query.showMiniMap,
    };
  },
  computed: {
    code: function() {
      return this._code || this.$route.query.code || "";
    },
    config: function() {
      return (
        this._config ||
        this.$route.query.config ||
        require("./config/default.json")
      );
    },
    state: function() {
      //地图状态: 0 : 平移   1：测距、测面  2： 选取经纬度  3： 选取数据 ..继续添加
      return this._state || this.$route.query.state || 0;
    },
    contextmenuConf: function() {
      return (
        this._contextmenuConf ||
        this.$route.query.contextmenuConf ||
        require("./contextmenus/default.js")
      );
    },
    mapContextmenuConf: function() {
      return (
        this._mapContextmenuConf ||
        this.$route.query.mapContextmenuConf ||
        require("./contextmenus/common.js")
      );
    },
    clusterConfig: function() {
      return this._clusterConfig || this.$route.query.clusterConfig;
    },
    heatmapConfig: function() {
      return this._heatmapConfig || this.$route.query.heatmapConfig;
    },
    clusterDatas: function() {
      return (
        this._clusterDatas ||
        this.$route.query.clusterDatas ||
        [
          //    {
          //      longitude: 116.395645,
          //      latitude: 39.929945,
          //      title:"1",
          //      icon:L.icon({
          //        iconUrl : require("leaflet/dist/images/marker-icon-2x.png")
          //      })
          //    }
        ]
      );
    },
    locateDatas: function() {
      return (
        this._locateDatas ||
        this.$route.query.locateDatas ||
        [
          //   {
          //      id: "adsfads",
          //      name: "adsfads",
          //      className: "MGW",
          //	    flick: false,
          //      locateType: "point", // 点:point,线：line,面: polygon
          //      //point 有效
          //      image: require("./icons/eye.png"),
          //      longitude: 116.395645,
          //      latitude: 39.929945,
          //      width:32,
          //      height:32,
          //      style:{
          //          'shape':'circle' //覆盖image
          //      },
          //   },
          //   {
          //     id: "adsfads12341",
          //     name: "adsfads",
          //     className: "MGW",
          //	 flick: true,
          //     locateType: "polygon", // 点:point,线：line,面: polygon
          //     //line,polygon 有效
          //     path: [
          //       { longitude: 116.393245, latitude: 39.924532 },
          //       { longitude: 116.396623, latitude: 39.934343 },
          //       { longitude: 116.397665, latitude: 39.945445 },
          //       { longitude: 116.393245, latitude: 39.924532 },
          //     ],
          //     lineColor: "green",
          //     //仅 polygon 有效
          //     fillColor: "lightgreen",
          //   },
        ]
      );
    },
  },
  mounted() {
    var self = this;
    var map = (window.map = this.map = this.$refs.map.mapObject);

    map.removeControl(map.zoomControl);
    map.removeControl(map.attributionControl);
    map.on("map-select", this.handleMapSelect);
    map.on("map-drag", this.handleMapDrag);
    map.on("map-zoom-full", this.handleMapFull);
    map.on("map-zoom-in", function() {
      map.zoomIn();
    });
    map.on("map-zoom-out", function() {
      map.zoomOut();
    });
    map.on("map-measure-distance", this.handleMapMeasureDistance);
    map.on("map-measure-area", this.handelMapMeasureArea);
    map.on("map-clear", this.mapClear);

    map.on("measurefinish", function(result) {
      self.measureLength = result.length;
      self.measureArea = result.area;
      self.$refs.features.mapObject.openPopup(
        result.points[result.pointCount - 1]
      );
    });
    map.on("measurestop", function() {
      self.$refs.features.mapObject.closePopup();
    });

    map.on("map-created", function() {
      self.locateDatasOnMap();
      self.showClusterDatasOnMap();
      //self.setZoomDuration(8,12);
      //self.setCenter(33,133);
      //console.info(self.graphView);
    });

    map.on("network-right-clickBackground", this.handleRightClickBackground);
    map.on("network-right-clickData", this.handleRightClickData);
    map.on("network-double-clickData", function() {
      console.info("network-double-clickData");
    });
    map.on("network-double-clickBackground", function() {
      console.info("network-double-clickBackground");
    });

    this.handleStateChange();
    this.initMap();
  },
  beforeDestroy() {
    removeResizeListener(this.$refs.container, this.onContainerResize);
  },
  methods: {
    initMap() {
      var self = this;
      var config = this.config;
      if (config) {
        var type = config.basemaps[0].type;
        if ("esri" === type) {
          var url = config.basemaps[0].url;
          jsonp(url + "?f=json", {}, function(err, res) {
            if (err) {
              console.error(err);
              self.$message.error("地图加载失败！");
            } else {
              self.createEsriMap(res);
            }
          });
        }
        if ("baidu" === type) {
          this.createBaiduMap();
        }
      } else {
        this.$message.error("地图配置加载错误！");
      }
    },
    createEsriMap(meta) {
      this.metadata = meta;

      this.createGraphView();
      this.createEsriMapOrigin();
      this.createEsriMapCRS();
      this.createEsriTiledMapLayer();

      this.createDynamicMapLayers();
      this.createClusterGroup();
      this.createHeatmap();

      this.handleMapFull();
      this.map.fire("map-created");
    },
    createBaiduMap() {
      this.createGraphView();
      this.createBaiduTiledMapLayer();

      this.createDynamicMapLayers();
      this.createClusterGroup();
      this.createHeatmap();

      this.handleMapFull();
      this.map.fire("map-created");
    },
    createGraphView() {
      var self = this;
      var map = this.map;
      var graphView = (window.graphView = this.graphView = new ht.graph.GraphView());

      var div = graphView.getView();
      div.style.position = "absolute";
      div.style.top = 0;
      div.style.left = 0;
      div.style.bottom = 0;
      div.style.right = 0;
      div.style.zIndex = 401;
      this.$refs.map.$el.appendChild(div);

      var func = graphView.validateImpl;
      graphView.validateImpl = function() {
        this._canvas.width = 0;
        this._canvas.height = 0;
        this._canvas.style.width = 0;
        this._canvas.style.height = 0;
        func.call(graphView);
      };
      graphView.setToolTipEnabled();
      graphView.setAutoScrollZone(-1);
      graphView.handleScrollZoom = function() {};
      graphView.handlePinchZoom = function() {};
      graphView.handleScroll = function() {};
      graphView.handleZoom = function() {};
      graphView.handlePinch = function() {};
      graphView.isMovable = function() {
        return false;
      };
      graphView
        .dm()
        .sm()
        .ms(function(e) {
          console.info(e);
          self.$emit("SELECT_DATA_CHANGE", self.getSelectDatas());
        });
      //graphView.isSelectable = function() {
      //  return false;
      //};
      var handleClick = function(e) {
        var data = graphView.getDataAt(e);
        if (data) {
          e.stopPropagation();
        } else if (e.metaKey || e.ctrlKey) {
          e.stopPropagation();
        }
      };
      graphView.getView().addEventListener("click", handleClick, false);
      graphView.getView().addEventListener("dblclick", handleClick, false);
      graphView
        .getView()
        .addEventListener(
          ht.Default.isTouchable ? "touchstart" : "mousedown",
          handleClick,
          false
        );
      this.onContainerResize();
      addResizeListener(this.$refs.container, this.onContainerResize);

      document.onkeydown = function(e) {
        if (e.keyCode === 27) {
          self.mapClear();
        }
      };
      //禁止系统右键
      document.oncontextmenu = function() {
        // return false;
      };
      this.contextmenu = new ht.widget.ContextMenu();
      this.contextmenu.getView().style.zIndex = 404;
this.graphView.enableToolTip();
this.graphView.getToolTip = function(e){
                    var data = this.getDataAt(e);
                    if(data){
                      self.graphView.hoverData=data;
                        return data;
                    }
                    delete self.graphView.hoverData;
                    return null;
                }; 
      //处理鼠标左键、右键、图元双击、空白处双击事件
      this.graphView.addInteractorListener(function(b) {
        if ("clickData" === b.kind) {
          if (b.event.button == 0)
            map.fire("network-left-clickData", {
              data: b.data,
            });
          if (b.event.button == 2)
            map.fire("network-right-clickData", {
              data: b.data,
            });
        } else if ("doubleClickData" === b.kind) {
          map.fire("network-double-clickData", {
            data: b.data,
          });
        } else if ("doubleClickBackground" === b.kind) {
          map.fire("network-double-clickBackground");
        } else if ("clickBackground" == b.kind) {
          console.info(b);
          if (b.event.button == 0) map.fire("network-left-clickBackground");
          if (b.event.button == 2) map.fire("network-right-clickBackground");
        } else if (b.kind === "onEnter") {
          console.log("鼠标进入图元");
          map.fire("network-data-mouse-enter", {
            data: b.data,
          });
        } else if (b.kind === "onHover" || b.kind === "hover") {
          console.log("鼠标在图元上悬停");
          if("undefined" != typeof self.graphView.hoverData){

          map.fire("network-data-mouse-hover", {
            data: self.graphView.hoverData,
          });
          }else if("undefined" !=typeof b.data){
          map.fire("network-data-mouse-hover", {
            data: b.data,
          });
          }
        } else if (b.kind === "onLeave") {
          console.log("鼠标离开图元");
          map.fire("network-data-mouse-leave", {
            data: b.data,
          });
        }
      });
      map.on("movestart", function() {
        div.style.display = "none";
      });
      map.on("moveend", function() {
        self.synchronize();
        div.style.display = "";
      });

      map.on("click", function(e) {
        self.handleMapClick(e);
      });
    },
    createEsriTiledMapLayer() {
      this.map.crs = this.crs;
      this.map.options.crs = this.crs;
      for(let i = 0 ; i < this.config.basemaps.length ; i++)
      {
        var url = this.config.basemaps[i].url;
        var token = this.config.basemaps[i].token;
        var baseMapLayer = new esri.TiledMapLayer({
          url: url,
          maxZoom: this.maxZoom,
          minZoom: this.minZoom,
          crs: L.CRS.EPSG4326,
          continuousWorld: true,
          useCors: false,
          attribution: "",
          token: token,
        });
        if (url)
          baseMapLayer.setUrl(url);

        this.map.addLayer(baseMapLayer);
      }

      var miniMapLayer = new esri.TiledMapLayer({
        url: url,
        maxZoom: this.maxZoom,
        minZoom: this.minZoom,
        crs: L.CRS.EPSG4326,
        continuousWorld: true,
        useCors: false,
      });
      this.createMiniMapLayer(miniMapLayer);
    },
    createBaiduTiledMapLayer() {
      this.map.crs = L.CRS.Baidu;
      this.map.options.crs = L.CRS.Baidu;
      var baiduLayer = this.config.basemaps[0].baiduLayer;

      var baseMapLayer = L.tileLayer.baidu({
        layer: baiduLayer,
      });
      if (this.config.basemaps[0].url)
        baseMapLayer.setUrl(this.config.basemaps[0].url);
      this.map.addLayer(baseMapLayer);

      var miniMapLayer = L.tileLayer.baidu({
        layer: baiduLayer,
      });
      this.createMiniMapLayer(miniMapLayer);
    },
    createMiniMapLayer(miniMapLayer) {
      if (this.showMiniMap == null || this.showMiniMap) {
        var MiniMap = require("leaflet-minimap");
        new MiniMap(miniMapLayer, {
          toggleDisplay: true,
        }).addTo(this.map);
      }
    },
    createDynamicMapLayers() {
      var config = this.config;
      var map = this.map;
      var layers = (this.layers = []);
      var metadataCount = 0;

      if (!config.layers) return;

      for (var i = 0; i < config.layers.length; i++) {
        var bm = config.layers[i];
        // TODO 扩展动态图
        if (bm.type === "esri") {
          var dynamic = esri.dynamicMapLayer({
            url: bm.url,
            opacity: bm.alpha,
            zIndex: 2,
            format: "png8",
            crs: L.CRS.EPSG4326,
            id: bm.id,
            visible: bm.visible,
            name: bm.name,
            useCors: false,
            f: "image",
            token: bm.token
          });
          map.addLayer(dynamic);
          layers.push(dynamic);

          dynamic.service.metadata(function(err, metadata) {
            if (!err) {
              this.metadata = metadata;
              metadataCount++;
              if (metadataCount == config.layers.length) {
                map.fire("map-metadata-loaded");
              }
            }
          }, dynamic);
        }
      }
    },
    createEsriMapOrigin() {
      var origin = {
        x: 0,
        y: 0,
      };
      if (this.metadata.tileInfo && this.metadata.tileInfo.origin) {
        origin = this.metadata.tileInfo.origin;
      }
      this.origin = origin;
    },
    getMapResolutions() {
      var lods = this.metadata.tileInfo.lods,
        resolutions = [];
      if (lods && lods.length > 0) {
        for (var i = 0; i < lods.length; i++) {
          if (lods[i]) {
            resolutions[i] = lods[i].resolution;
          }
        }
        this.minZoom = lods[0].level;
        this.maxZoom = lods[lods.length - 1].level;
      }
      return resolutions;
    },
    createEsriMapCRS() {
      var sr = this.metadata.spatialReference;
      var origin = this.origin;
      var resolutions = this.getMapResolutions();

      if (sr && sr.wkid === 4326) {
        this.crs = new L.Proj.CRS(
          "EPSG:4326",
          "+title=WGS 84 (long/lat) +proj=longlat +ellps=WGS84 +datum=WGS84 +units=degrees",
          {
            resolutions: resolutions,
            origin: [origin.x, origin.y],
          }
        );
      } else {
        if (sr.wkid === 3857) {
          this.crs = new L.Proj.CRS(
            "EPSG:3857",
            "+title=WGS 84 / Pseudo-Mercator +proj=merc +a=6378137 +b=6378137 +lat_ts=0.0 +lon_0=0.0 +x_0=0.0 +y_0=0 +k=1.0 +units=m +nadgrids=@null +no_defs",
            {
              resolutions: resolutions,
              origin: [origin.x, origin.y],
            }
          );
        } else if (sr.wkid === 102100) {
          this.crs = new L.Proj.CRS(
            "EPSG:3857",
            "+title=WGS 84 / Pseudo-Mercator +proj=merc +a=6378137 +b=6356752.3142451793 +lat_ts=0.0 +lon_0=0.0 +x_0=0.0 +y_0=0 +k=1.0 +units=m +nadgrids=@null +no_defs",
            {
              resolutions: resolutions,
              origin: [origin.x, origin.y],
            }
          );
          console.info();
        } else {
          this.crs = new L.Proj.CRS(
            "EPSG:3857",
            "+title=WGS 84 / Pseudo-Mercator +proj=merc +a=6378137 +b=6378137 +lat_ts=0.0 +lon_0=0.0 +x_0=0.0 +y_0=0 +k=1.0 +units=m +nadgrids=@null +no_defs",
            {
              resolutions: resolutions,
              origin: [origin.x, origin.y],
            }
          );
        }
      }
    },
    createHeatmap() {
      let map = this.map;
      let config = Object.assign(
        {
          'radius': 20,
          'maxOpacity': 0.8,
          'scaleRadius': false,
          'useLocalExtrema': true,
          latField: 'lat',
          lngField: 'lng',
          valueField: 'count'
        },
        this.heatmapConfig
      );
      var heatmapLayer = this.heatmapLayer = new HeatmapOverlay(config)
      heatmapLayer.addTo(map)
      /**
      var data = [{ lng: 116.395605, lat: 39.929985, count: 3 },
          { lng: 116.394595, lat: 39.929875, count: 1 },
          { lng: 116.393485, lat: 39.929765, count: 9 },
          { lng: 116.392375, lat: 39.929655, count: 8 },
          { lng: 116.391265, lat: 39.929545, count: 7 },
          { lng: 116.390155, lat: 39.929435, count: 6 },
          { lng: 116.395045, lat: 39.929325, count: 5 }
        ];
      this.addHeatmapLayer(data) **/
    },
    addHeatmapLayer(data)
    {
      this.heatmapLayer.setData( {
          max: 8,
          data: data
      });
    },
    setHeatmapLayerData(data)
    {
      this.heatmapLayer.setData(data);
    },
    createClusterGroup() {
      let map = this.map;
      let config = Object.assign(
        {
          spiderfyOnMaxZoom: false,
          showCoverageOnHover: false,
          zoomToBoundsOnClick: false,
        },
        this.clusterConfig
      );

      let markers = (this.mapClusterGroup = L.markerClusterGroup(config));
      console.info(markers);
      markers.on("click", function(a) {
        map.fire("cluster-node-click", a);
      });
      markers.on("mouseover", function(a) {
        map.fire("cluster-node-mouseover", a);
      });
      markers.on("mouseout", function(a) {
        map.fire("cluster-node-mouseout", a);
      });
      markers.on("clusterclick", function(a) {
        map.fire("cluster-click", a);
      });

      map.addLayer(markers);
    },
    handleMapSelect() {
      //选取地图数据
      this.state = 3;
    },
    handleMapDrag() {
      this.state = 0;
    },
    handleMapFull() {
      var lng = this.config.initCenter.lng,
        lat = this.config.initCenter.lat,
        zoom = this.config.initZoom;
      this.map.flyTo([lat, lng], zoom);
    },
    synchronize() {
      var graphView = this.graphView;
      var map = this.map;
      graphView.tx(0);
      graphView.ty(0);

      graphView.dm().each(function(data) {
        if (data.getClassName() === "ht.Shape") {
          var points = new ht.List();
          var latLngs = data.getAttr("latLng");
          if (latLngs == null) return;
          else {
            if (latLngs instanceof ht.List) {
              latLngs.each(function(latLng) {
                var point = map.latLngToContainerPoint(latLng);
                points.add(point);
              });
              data.setPoints(points);
            } else {
              var latlngs = new ht.List();
              var pxpoints = data.getPoints();
              for (var j = 0; j < pxpoints.size(); j++) {
                var pp = pxpoints.get(j);
                var x = graphView.tx();
                var y = graphView.ty();
                var point = map.containerPointToLatLng(
                  new L.Point(x + pp.x, y + pp.y)
                );
                latlngs.add(point);
              }
              data.a("latLng", latlngs);
            }
          }
        } else if (data.getClassName() === "ht.Node") {
          var latLng = data.a("latLng");
          if (latLng == null) return;
          var position = map.latLngToContainerPoint(latLng);
          data.setPosition(position);
        }
      });
    },
    handleMapClick(e) {
      if (this.state == 2) {
        this.handleLatlngSelect(e);
      }
    },
    handleLatlngSelect(e) {
      var latlng = e.latlng;
      this.$confirm(
        "选取的经纬度为:" + latlng.lng.toFixed(6) + " " + latlng.lat.toFixed(6),
        "请确认",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          this.$emit("LATLNG_SELECT", latlng);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "请继续选取经纬度！",
          });
        });
    },
    handleMapMeasureDistance() {
      var self = this;
      self.state = 1;
      this.mapClear();
      var graphView = this.graphView;
      var map = this.map;

      var drawInterator = new ht.DrawInteractor(
        graphView,
        this.$refs.map.$el,
        this.map
      );
      graphView.setInteractors(
        new ht.List([
          new ht.graph.SelectInteractor(graphView),
          new ht.graph.EditInteractor(graphView),
          new ht.graph.MoveInteractor(graphView),
          new ht.graph.DefaultInteractor(graphView),
          drawInterator,
        ])
      );
      drawInterator._vectorType = "shape";
      drawInterator.onCreateStarted = function() {
        console.info("onCreateStarted");
      };
      drawInterator.onCreateCompleted = function(node) {
        if (node.getClassName() === "ht.Shape") {
          var points = new ht.List();
          var latLngpoints = node.getPoints();
          for (var i = 0; i < latLngpoints.size(); i++) {
            var latLng = latLngpoints.get(i);
            var x = graphView.tx();
            var y = graphView.ty();
            var point = map.containerPointToLatLng(
              new L.Point(x + latLng.x, y + latLng.y)
            );
            points.add(point);
          }
          node.a("latLng", points);
          node.setStyle("shape.background", null);
          node.setStyle("shape.border.color", "rgba(255,0,0,0.7)");

          var distance = 0;
          for (var j = 0; j < points.size() - 1; j++) {
            var prevPoint = points.get(j);
            var nextPoint = points.get(j + 1);

            distance += prevPoint.distanceTo(nextPoint);
          }
          node.setName("长度:" + (distance / 1000).toFixed(3) + "千米");
        }
        self.$refs.map.$el.style.cursor = "default";
        self.graphView.setEditable(false);
      };
    },
    handelMapMeasureArea() {
      var self = this;
      this.mapClear();
      var graphView = this.graphView;
      var map = this.map;

      self.state = 1;
      var drawInterator = new ht.DrawInteractor(
        graphView,
        this.$refs.map.$el,
        this.map
      );
      graphView.setInteractors(
        new ht.List([
          new ht.graph.SelectInteractor(graphView),
          new ht.graph.EditInteractor(graphView),
          new ht.graph.MoveInteractor(graphView),
          new ht.graph.DefaultInteractor(graphView),
          drawInterator,
        ])
      );
      drawInterator._vectorType = "shape";
      drawInterator._fillStyle = "rgba(0,255,0,0.3)";
      drawInterator.onCreateStarted = function() {
        console.info("onCreateStarted");
      };
      drawInterator.onCreateCompleted = function(node) {
        if (node.getClassName() === "ht.Shape") {
          //封闭
          node.setClosePath(true);

          var points = new ht.List();
          var latLngpoints = node.getPoints();
          for (var i = 0; i < latLngpoints.size(); i++) {
            var latLng = latLngpoints.get(i);
            var x = graphView.tx();
            var y = graphView.ty();
            var point = map.containerPointToLatLng(
              new L.Point(x + latLng.x, y + latLng.y)
            );
            points.add(point);
          }
          node.a("latLng", points);
          node.setStyle("shape.background", "rgba(0,255,0,0.3)");
          node.setStyle("shape.border.color", "rgba(255,0,0,0.7)");

          var distance = 0;
          for (var j = 0; j < points.size() - 1; j++) {
            var prevPoint = points.get(j);
            var nextPoint = points.get(j + 1);

            distance += prevPoint.distanceTo(nextPoint);
          }
          distance += points.get(0).distanceTo(points.get(points.size() - 1));
          var area = self._calculateArea(latLngpoints);
          node.setName(
            "面积:" +
              area +
              "平方千米 周长:" +
              (distance / 1000).toFixed(3) +
              "千米"
          );
        }
        self.$refs.map.$el.style.cursor = "default";
        self.graphView.setEditable(false);
      };
    },
    getClusterDatas() {
      return this.clusterDatas;
    },
    showClusterDatasOnMap() {
      var clusterDatas = this.clusterDatas;
      if (clusterDatas && clusterDatas.length > 0) {
        let cluster = this.mapClusterGroup;
        cluster.clearLayers();
        for (var i = 0; i < clusterDatas.length; i++) {
          let latitude = clusterDatas[i].latitude;
          let longitude = clusterDatas[i].longitude;
          let title = clusterDatas[i].title;
          let icon = clusterDatas[i].icon;
          if (latitude && longitude) {
            var marker = L.marker(new L.LatLng(latitude, longitude), {
              title: title,
              data: clusterDatas[i],
            });
            if (title) marker.bindPopup(title);
            if (icon) marker.setIcon(icon);
            cluster.addLayer(marker);
          }
        }
      }
    },
    locateDatasOnMap() {
      var locateDatas = this.locateDatas;
      if (locateDatas && locateDatas.length > 0) {
        var showDatas = [];
        for (var i = 0; i < locateDatas.length; i++) {
          var locateData = locateDatas[i];
          if ("point" === locateData.locateType) {
            var node = new ht.Node();
            node.setId(locateData.id);
            node.setImage(locateData.image);
            node.setName(locateData.name);
            node.setToolTip(locateData.name);
            if (locateData.width && locateData.height)
              node.setSize(locateData.width, locateData.height);
            else node.setSize(32, 32);
            node.a("id", locateData.id);
            node.a("name", locateData.name);
            node.a("className", locateData.className);
            node.a("flick", locateData.flick);
            node.a("data", locateData);
            node.a(
              "latLng",
              new L.latLng(locateData.latitude, locateData.longitude)
            );
            node.s("select.color", "red");
            node.s("select.width", "3");
            node.s("interactive", true);
            if (locateData.style) {
              for (var p in locateData.style) {
                node.s(p, locateData.style[p]);
              }
            }
            showDatas.push(node);
          }
          if (
            "line" === locateData.locateType ||
            "polygon" === locateData.locateType
          ) {
            var shape = new ht.Shape();
            shape.setId(locateData.id);
            shape.setName(locateData.name);
            shape.a("id", locateData.id);
            shape.a("name", locateData.name);
            shape.a("className", locateData.className);
            shape.a("flick", locateData.flick);
            shape.a("data", locateData);
            shape.setStyle("shape.border.color", locateData.lineColor);
            shape.setStyle("shape.border.width", locateData.lineWidth ?? 1);
            shape.s("select.color", "red");

            if ("polygon" === locateData.locateType)
              shape.setStyle("shape.background", locateData.fillColor);
            else shape.setStyle("shape.background", null);

            var path = locateData.path;
            var latlngs = new ht.List();
            for (var j = 0; j < path.length; j++) {
              latlngs.add(new L.LatLng(path[j].latitude, path[j].longitude));
            }
            shape.a("latLng", latlngs);
            if (locateData.style) {
              for (var s in locateData.style) {
                node.s(s, locateData.style[s]);
              }
            }
            showDatas.push(shape);
          }
        }
        this.showDatasOnMap(showDatas, true);
      }
    },
    showDatasOnMap(datas, isZoom) {
      if (!datas || datas.length == 0) return;

      var graphView = this.graphView;
      var map = this.map;
      var points = new ht.List();

      graphView.dm().clear();
      graphView.dm().disableAnimation();

      for (var i = 0; i < datas.length; i++) {
        var node = datas[i];
        if (node.a("flick")) {
          node.setAnimation({
            hide: {
              property: "opacity",
              accessType: "style",
              from: 1,
              to: 0,
              frames: 1,
              next: "show",
            },
            show: {
              property: "opacity",
              accessType: "style",
              from: 0,
              to: 1,
              frames: 1,
              next: "hide",
            },
            start: ["hide"],
          });
        }
        //点
        if (node.getClassName() === "ht.Node") {
          var latLng = node.a("latLng");
          node.setPosition(map.latLngToContainerPoint(latLng));
          points.add(latLng);
          graphView.dm().add(node);
        }
        //线和面
        if (node.getClassName() === "ht.Shape") {
          var latLngs = node.a("latLng");
          var sp = new ht.List();
          var segs = new ht.List();
          segs.add(1);
          for (var j = 0; j < latLngs.size(); j++) {
            var ll = latLngs.get(j);
            sp.add(map.latLngToContainerPoint(ll));
            if (j > 0) segs.add(2);
          }
          node.setPoints(sp);
          node.setSegments(segs);
          points.addAll(latLngs);
          graphView.dm().add(node);
        }
      }
      graphView.dm().enableAnimation(300);

      if (isZoom && points.size() > 0) {
        if (points.size() == 1) {
          var zoom = map.getMaxZoom() - 3;
          map.flyTo(points.get(0), zoom < 8 ? 8 : zoom, { duration: 0.8 });
        } else {
          var latLngBounds = this._getMaxBound(points);
          map.flyToBounds(latLngBounds, { duration: 0.8 });
          //  map.fitBounds(latLngBounds);
        }
      }
    },
    _getMaxBound(points) {
      var y1 = -90,
        x1 = -180,
        y2 = 90,
        x2 = 180;
      for (var i = 1; i < points.size(); i++) {
        var latLng = points.get(i);
        if (y1 < latLng.lat) {
          y1 = latLng.lat;
        }
        if (x1 < latLng.lng) {
          x1 = latLng.lng;
        }
        if (y2 > latLng.lat) {
          y2 = latLng.lat;
        }
        if (x2 > latLng.lng) {
          x2 = latLng.lng;
        }
      }
      var sw = new L.LatLng(y1, x1),
        ne = new L.LatLng(y2, x2);
      var latLngBounds = new L.LatLngBounds(sw, ne);
      return latLngBounds;
    },
    _calculateArea: function(latLngs) {
      var pointsCount = latLngs.size(),
        DEG_TO_RAD = Math.PI / 180,
        area = 0.0,
        d2r = DEG_TO_RAD,
        p1,
        p2;

      if (pointsCount > 2) {
        for (var i = 0; i < pointsCount; i++) {
          p1 = latLngs.get(i);
          p2 = latLngs.get((i + 1) % pointsCount);
          area +=
            (p2.lng - p1.lng) *
            d2r *
            (2 + Math.sin(p1.lat * d2r) + Math.sin(p2.lat * d2r));
        }
        area = (area * 6378137.0 * 6378137.0) / 2.0;
      }

      return Math.abs(L.Util.formatNum(area / 1000 / 1000, 3));
    },
    mapClear() {
      this.state = 0;

      var graphView = this.graphView;
      graphView.dm().clear();
      graphView.setEditable(false);

      this.mapClusterGroup.clearLayers();
      this.$refs.features.mapObject.closePopup();
    },
    handleRightClickBackground() {
      var cm = this.mapContextmenuConf;
      if (cm) {
        var items = cm.default[this.code];
        if (items) {
          for (var i = 0; i < items.length; i++) {
            items[i].code = this.code;
            items[i].graphView = this.graphView;
            items[i].map = this.map;
            items[i].scope = this;
          }
          this.contextmenu.setItems(items);
        } else {
          this.contextmenu.setItems([]);
        }
        this.contextmenu.addTo(this.graphView.getView());
      }
      this.contextmenu.addTo(this.graphView.getView());
    },
    handleRightClickData(e) {
      var data = e.data;
      var className = data.a("className");
      console.info(data);
      var cm = this.contextmenuConf;
      if (cm) {
        var items = cm.default[className];

        if (items) {
          for (var i = 0; i < items.length; i++) {
            items[i].data = data;
          }
          this.contextmenu.setItems(items);
        } else this.contextmenu.setItems([]);

        this.contextmenu.addTo(this.graphView.getView());
      }
    },
    handleStateChange() {
      if (this.state == 0) {
        this.$refs.map.$el.style.cursor = "default";
      }
      if (this.state == 1) {
        this.$refs.map.$el.style.cursor = "crosshair";
      }
      if (this.state == 2) {
        this.$refs.map.$el.style.cursor = "crosshair";
      }
      if (this.state == 3) {
        this.$refs.map.$el.style.cursor = "crosshair";
      }
    },
    setZoomDuration(minZoom, maxZoom) {
      if (minZoom >= maxZoom) return;

      this.map.options.minZoom = minZoom;
      this.map.options.maxZoom = maxZoom;
    },
    setCenter(lat, lng, zoom) {
      this.map.setView([lat, lng], zoom || this.map.getZoom());
    },
    getSelectDatas() {
      var datas = [];
      this.graphView
        .dm()
        .sm()
        .each(function(data) {
          datas.push({
            id: data.a("id"),
            name: data.a("name"),
            className: data.a("className"),
            data: data.a("data"),
          });
        });
      return datas;
    },
    onContainerResize() {
      let self = this;
      // setTimeout(function() {
      self?.graphView?.invalidate?.();
      self?.map?.invalidateSize?.(true);
      // }, 100);
    },
  },
  watch: {
    state: "handleStateChange",
    _locateDatas: "locateDatasOnMap",
    _clusterDatas: "showClusterDatasOnMap",
  },
};
</script>
<style scoped>
.vue-leaflet {
  width: 100%;
  height: 100%;
}
</style>
