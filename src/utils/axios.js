import axios from "./request";

// ------get 方式------
// get方式 application/x-www-form-urlencoded
const get = (url, params = {}, configs = {}) => {
  return axios({
    url: url,
    method: "get",
    params: params,
    ...configs,
  });
};

// get方式 application/json
const getJson = (url, params = {}, configs = {}) => {
  return axios({
    url: url,
    method: "get",
    headers: {
      "Content-Type": "application/json",
    },
    params: params,
    ...configs,
  });
};

// get方式 application/x-www-form-urlencoded 下载文件
const getBlob = (url, params = {}, configs = {}) => {
  return axios({
    url: url,
    method: "get",
    params: params,
    responseType: "blob",
    ...configs,
  });
};

// get方式 application/json 下载文件
const getJsonBlob = (url, params = {}, configs = {}) => {
  return axios({
    url: url,
    method: "get",
    headers: {
      "Content-Type": "application/json",
    },
    params: params,
    responseType: "blob",
    ...configs,
  });
};
// ------get 方式------

// ------post 方式------
// post方式 application/x-www-form-urlencoded
const post = (url, params = {}, configs = {}) => {
  return axios({
    url: url,
    method: "post",
    data: params,
    ...configs,
  });
};

// post方式 application/json
const postJson = (url, params = {}, configs = {}) => {
  return axios({
    url: url,
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    data: params,
    ...configs,
  });
};

// post方式 application/x-www-form-urlencoded 下载文件
const postBlob = (url, params = {}, configs = {}) => {
  return axios({
    url: url,
    method: "post",
    data: params,
    responseType: "blob",
    ...configs,
  });
};

// post方式 application/json 下载文件
const postJsonBlob = (url, params = {}, configs = {}) => {
  return axios({
    url: url,
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    data: params,
    responseType: "blob",
    ...configs,
  });
};

// post方式 multipart/form-data 上传文件
const postFormData = (url, params = {}, configs = {}) => {
  return axios({
    url: url,
    method: "post",
    headers: {
      "Content-Type": "multipart/form-data",
    },
    data: params,
    ...configs,
  });
};
// ------post 方式------

// ------put 方式------
// put方式 application/x-www-form-urlencoded
const put = (url, params = {}, configs = {}) => {
  return axios({
    url: url,
    method: "put",
    data: params,
    ...configs,
  });
};

// put方式 application/json
const putJson = (url, params = {}, configs = {}) => {
  return axios({
    url: url,
    method: "put",
    headers: {
      "Content-Type": "application/json",
    },
    data: params,
    ...configs,
  });
};
// ------put 方式------

// ------patch 方式------
// patch方式 application/x-www-form-urlencoded
const patch = (url, params = {}, configs = {}) => {
  return axios({
    url: url,
    method: "patch",
    data: params,
    ...configs,
  });
};

// patch方式 application/json
const patchJson = (url, params = {}, configs = {}) => {
  return axios({
    url: url,
    method: "patch",
    headers: {
      "Content-Type": "application/json",
    },
    data: params,
    ...configs,
  });
};
// ------put 方式------

// ------delete 方式------
// del方式 application/x-www-form-urlencoded
const del = (url, params = {}, configs = {}) => {
  return axios({
    url: url,
    method: "delete",
    params: params,
    data: params,
    ...configs,
  });
};

// del方式 application/json
const delJson = (url, params = {}, configs = {}) => {
  return axios({
    url: url,
    method: "delete",
    headers: {
      "Content-Type": "application/json",
    },
    params: params,
    data: params,
    ...configs,
  });
};
// ------delete 方式------

export {
  get,
  getJson,
  getBlob,
  getJsonBlob,
  post,
  postJson,
  postBlob,
  postJsonBlob,
  postFormData,
  put,
  putJson,
  patch,
  patchJson,
  del,
  delJson,
};
