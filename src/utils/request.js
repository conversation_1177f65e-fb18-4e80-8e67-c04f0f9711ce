import axios from "axios";
import qs from "qs";
import { getToken } from "@/utils/auth";
import { MessageBox, Notification } from "element-ui";
import router from "@/router.js";
import store from "@/store/";
const { axiosBasePath = "" } = require("@/settings");
let http203Nofi = {};

// create an axios instance
const service = axios.create({
  withCredentials: true, // send cookies when cross-domain requests
  timeout: 20000, // request timeout
});

// request interceptor
service.interceptors.request.use(
  config => {
    // do something before request is sent
    // 网关记录日志时，可以区分请求来源
    config.headers["Request-Origin-App"] = "NETFM3_PC";

    let token = getToken();
    if (token) {
      // let each request carry token
      config.headers["Authorization"] = getToken();
    }
    if (config.method === "post") {
      if (
        config.headers["Content-Type"] !== "application/json" &&
        config.headers["Content-Type"] !== "multipart/form-data"
      ) {
        config.data = qs.stringify(config.data);
      }
    }
    config.url =
      axiosBasePath + (/^\//.test(config.url) ? config.url : "/" + config.url);
    return config;
  },
  error => {
    // do something with request error
    console.log(error); // for debug
    return Promise.reject(error);
  }
);

// response interceptor
service.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
   */

  /**
   * Determine the request status by custom code
   */
  response => {
    const httpStatus = response.status;
    if (httpStatus != 203) {
      const { responseType } = response.config;
      if (responseType == "blob") {
        return new Promise(resolve => {
          const blob = response.data;
          const reader = new FileReader();
          reader.readAsText(blob);
          reader.onload = e => {
            let isFile = false;
            let resData;
            try {
              resData = JSON.parse(e.target.result);
              if (!Object.prototype.hasOwnProperty.call(resData, "status")) {
                isFile = true;
              }
            } catch (err) {
              isFile = true;
            }
            if (!isFile) {
              return resolve(resData);
            } else {
              let filename = "下载";
              let resConfig = response.config;
              if (resConfig.method == "get") {
                if (typeof resConfig?.params == "string") {
                  try {
                    filename = JSON.parse(resConfig.params)?.filename ?? "下载";
                  } catch (error) {
                    filename = qs.parse(resConfig.params)?.filename ?? "下载";
                  }
                  filename = JSON.parse(resConfig.params)?.filename ?? "下载";
                } else {
                  filename = resConfig?.params?.filename ?? "下载";
                }
              }
              if (resConfig.method == "post") {
                if (typeof resConfig?.data == "string") {
                  try {
                    filename = JSON.parse(resConfig.data)?.filename ?? "下载";
                  } catch (error) {
                    filename = qs.parse(resConfig.data)?.filename ?? "下载";
                  }
                } else {
                  filename = resConfig?.data?.filename ?? "下载";
                }
              }
              if (
                Object.prototype.hasOwnProperty.call(
                  response.headers,
                  "content-disposition"
                )
              ) {
                filename = decodeURIComponent(
                  response.headers["content-disposition"]?.split("=")[1]
                );
              }
              if (window.navigator.msSaveOrOpenBlob) {
                window.navigator.msSaveBlob(blob, filename);
              } else {
                const url = window.URL.createObjectURL(new Blob([blob]));
                const a = document.createElement("a");
                a.style.display = "none";
                a.download = filename;
                a.href = url;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
              }
              return resolve({ status: "0", data: "success" });
            }
          };
        });
      } else {
        const res = response.data;
        const status = ("" + (res.status ?? res.code)).toLocaleLowerCase();
        const msg = res.message ?? res.msg ?? res.data;

        if (
          status !== "0" &&
          status !== "succeed" &&
          status !== "success" &&
          status !== "ok" &&
          status !== "200"
        ) {
          if (status < 100 && status != 1) {
            Notification({
              message: msg ?? "error",
              type: "error",
              duration: 3 * 1000,
              showClose: true,
            });
          }

          if (status === "1") {
            // to re-login
            // MessageBox.confirm(
            //   "您已注销，可以取消以留在此页，或重新登录。",
            //   "确认注销",
            //   {
            //     confirmButtonText: "重新登录",
            //     cancelButtonText: "取消",
            //     type: "warning",
            //   }
            // ).then(() => {
            //   location.reload();
            // });
          }
          return Promise.reject(res);
        } else {
          return res;
        }
      }
    } else {
      store.dispatch("user/logout");
      router.replace({ name: "Login" });
      if (!http203Nofi.close)
        http203Nofi = Notification({
          message: "登录状态已更改,请重新登录!",
          type: "error",
          showClose: true,
          onClose: function () {
            http203Nofi = {};
          },
        });
      return Promise.reject(response.data);
    }
  },
  error => {
    console.log("err" + error); // for debug
    let message = "";
    if (error.response) {
      const status = error.response.status;
      if (status === 404) {
        message = "404:请求资源未找到";
      } else if (status === 500) {
        message = "500:服务器出错";
      } else if (status === 403) {
        message = "403:拒绝访问";
      }
    } else {
      message = error.message;
      if (message.indexOf("timeout") !== -1) {
        message = "请求超时请重试";
      } else {
        message = "";
      }
    }
    if (message !== "") {
      Notification({
        message: message,
        type: "error",
        // duration: 3 * 1000,
        showClose: true,
      });
    }
    return Promise.reject(error);
  }
);

export default service;
