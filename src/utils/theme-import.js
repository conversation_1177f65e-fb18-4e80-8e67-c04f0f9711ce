const {
  // themeList,
  setThemeDefault,
  echartsThemeList,
  requireAll,
} = require("@/utils/theme");
const whiteEcharts = ["default", "light", "dark"];

setThemeDefault();
const req = require.context("@/themes/echarts/", false, /\.js$/);

echartsThemeList.forEach(element => {
  if (!whiteEcharts.includes(element)) {
    // import(`@/themes/echarts/${element}.js`);
    requireAll(req, element, "js");
  }
});
