import { post } from "@/utils/axios";
import store from "@/store/index.js";

const TokenKey = "Visual-Token";

let refreshTimer;

export function getToken() {
  return sessionStorage.getItem(TokenKey);
}

export function setToken(token) {
  return sessionStorage.setItem(TokenKey, token);
}

export function removeToken() {
  return sessionStorage.removeItem(TokenKey);
}

//刷新已登录用户Token
// export function refreshToken() {
//   if (typeof refreshTimer != "number") {
//     refreshTimer = setInterval(() => {
//       post("/login/token/refresh")
//         .then(res => {
//           sessionStorage.setItem(TokenKey, res.data);
//           store.commit("user/SET_TOKEN", res.data);
//         })
//         .catch(() => {
//           stopRefreshToken();
//         });
//     }, 12 * 60 * 60 * 1000);
//   }
// }

export function refreshToken() {
  if (typeof refreshTimer != "number") {
    refreshTimer = setInterval(() => {
      post("/login/nfm3/token/refresh")
        .then(res => {
          sessionStorage.setItem(TokenKey, res.data);
          store.commit("user/SET_TOKEN", res.data);
        })
        .catch(() => {
          stopRefreshToken();
        });
    }, 11 * 60 * 60 * 1000);
  }
}

//停止刷新Token
export function stopRefreshToken() {
  if (typeof refreshTimer == "number") {
    clearInterval(refreshTimer);
    refreshTimer = null;
  }
}
