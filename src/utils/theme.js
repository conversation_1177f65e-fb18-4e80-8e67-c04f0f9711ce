const path = require("path");
const {
  themeArr = [],
  themeDefault = "default",
  echartsThemeArr = [],
  echartsThemeDefault = "default",
} = require("@/settings");
export const themeCanUses = require("@/themes/frame-theme-list");

export const themeList = getThemeList();
export const defaultTheme = getDefaultTheme();
export const echartsThemeList = getEchartsThemeList();
export const defaultEchartsTheme = getEchartsThemeDefault();

export function getDefaultTheme() {
  const themeDefaultStorage = localStorage.getItem("themeDefault");
  return themeDefaultStorage
    ? themeList.includes(themeDefaultStorage)
      ? themeDefaultStorage
      : themeList[0]
    : themeList.includes(themeDefault)
    ? themeDefault
    : themeList[0];
}

export function requireAll(requireContext, element, suffix) {
  const r = new RegExp(`\\/${element}\\.${suffix}$`);
  requireContext
    .keys()
    .filter(item => r.test(item))
    .map(requireContext);
}

export function setThemeDefault() {
  const themeDefault = defaultTheme;
  const req = require.context("@/themes/theme-css/", false, /\.css$/);
  requireAll(req, `theme-${themeDefault}`, "css");
  document.body.className = "custom-theme-" + themeDefault;
  const html = document.getElementsByTagName("html")[0];
  html.className = html.className + " custom-theme-" + themeDefault;
}

export function getThemeList() {
  return Array.isArray(themeArr)
    ? themeArr.length === 0
      ? themeCanUses
      : themeArr
    : themeCanUses;
}

export function getEchartsThemeList() {
  return Array.isArray(echartsThemeArr)
    ? echartsThemeArr.length === 0
      ? ["default", "light", "dark"]
      : echartsThemeArr
    : ["default", "light", "dark"];
}

export function getEchartsThemeDefault() {
  const echartsThemeDefaultStorage = localStorage.getItem(
    "echartsThemeDefault"
  );
  return echartsThemeDefaultStorage
    ? echartsThemeList.includes(echartsThemeDefaultStorage)
      ? echartsThemeDefaultStorage
      : echartsThemeList[0]
    : echartsThemeList.includes(echartsThemeDefault)
    ? echartsThemeDefault
    : echartsThemeList[0];
}

export function resolve(dir) {
  return path.join(__dirname, dir);
}
