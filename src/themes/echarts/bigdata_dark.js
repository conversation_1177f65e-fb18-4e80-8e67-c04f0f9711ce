import echarts from "echarts/lib/echarts";

const theme = {
  color: [
    "#26FFE8",
    "#FFC343",
    "#5ab1ef",
    "#fa6e86",
    "#ffb980",
    "#0067a6",
    "#c4b4e4",
    "#d87a80",
    "#9cbbff",
    "#d9d0c7",
    "#87a997",
    "#d49ea2",
    "#5b4947",
    "#7ba3a8",
  ],
  textStyle: {},
  title: {
    textStyle: {
      color: "#eeeeee",
    },
    subtextStyle: {
      color: "#aaaaaa",
    },
  },
  grid: {
    containLabel: true,
    left: 10,
    right: 10,
    top: 40,
    bottom: 10,
  },
  line: {
    itemStyle: {
      normal: {
        borderWidth: 1,
      },
    },
    lineStyle: {
      normal: {
        width: 2,
      },
    },
    symbolSize: 4,
    symbol: "circle",
    smooth: true,
  },
  radar: {
    itemStyle: {
      normal: {
        borderWidth: 1,
      },
    },
    lineStyle: {
      normal: {
        width: 2,
      },
    },
    symbolSize: 4,
    symbol: "circle",
    smooth: false,
  },
  bar: {
    itemStyle: {
      barBorderRadius: 10,
    },
  },
  pie: {
    itemStyle: {
      normal: {
        borderWidth: 0,
        borderColor: "#ccc",
      },
      emphasis: {
        borderWidth: 0,
        borderColor: "#ccc",
      },
    },
  },
  scatter: {
    itemStyle: {
      normal: {
        borderWidth: 0,
        borderColor: "#ccc",
      },
      emphasis: {
        borderWidth: 0,
        borderColor: "#ccc",
      },
    },
  },
  boxplot: {
    itemStyle: {
      normal: {
        borderWidth: 0,
        borderColor: "#ccc",
      },
      emphasis: {
        borderWidth: 0,
        borderColor: "#ccc",
      },
    },
  },
  parallel: {
    itemStyle: {
      normal: {
        borderWidth: 0,
        borderColor: "#ccc",
      },
      emphasis: {
        borderWidth: 0,
        borderColor: "#ccc",
      },
    },
  },
  sankey: {
    itemStyle: {
      normal: {
        borderWidth: 0,
        borderColor: "#ccc",
      },
      emphasis: {
        borderWidth: 0,
        borderColor: "#ccc",
      },
    },
  },
  funnel: {
    itemStyle: {
      normal: {
        borderWidth: 0,
        borderColor: "#ccc",
      },
      emphasis: {
        borderWidth: 0,
        borderColor: "#ccc",
      },
    },
  },
  gauge: {
    itemStyle: {
      normal: {
        borderWidth: 0,
        borderColor: "#ccc",
      },
      emphasis: {
        borderWidth: 0,
        borderColor: "#ccc",
      },
    },
    detail: {
      color: "auto",
    },
    title: {
      color: "#eeeeee",
    },
  },
  candlestick: {
    itemStyle: {
      normal: {
        color: "#fd1050",
        color0: "#0cf49b",
        borderColor: "#fd1050",
        borderColor0: "#0cf49b",
        borderWidth: 1,
      },
    },
  },
  graph: {
    itemStyle: {
      normal: {
        borderWidth: 0,
        borderColor: "#ccc",
      },
    },
    lineStyle: {
      normal: {
        width: 1,
        color: "#aaaaaa",
      },
    },
    symbolSize: 4,
    symbol: "circle",
    smooth: false,
    color: [
      "#19d4ae",
      "#5ab1ef",
      "#fa6e86",
      "#ffb980",
      "#0067a6",
      "#c4b4e4",
      "#d87a80",
      "#9cbbff",
      "#d9d0c7",
      "#87a997",
      "#d49ea2",
      "#5b4947",
      "#7ba3a8",
    ],
    label: {
      normal: {
        textStyle: {
          color: "#eeeeee",
        },
      },
    },
  },
  map: {
    roam: false,
    label: {
      show: true,
      color: "#ccc",
    },
    itemStyle: {
      areaColor: "transparent",
      borderColor: "rgba(94, 174, 254,1)",
      borderWidth: 1.5,
    },
    emphasis: {
      label: {
        show: true,
        color: "#ccc",
      },
      itemStyle: {
        areaColor: "transparent",
        borderColor: "rgba(94, 174, 254,1)",
        borderWidth: 4,
      },
    },
  },

  geo: {
    roam: false,
    label: {
      show: true,
      color: "#ccc",
    },
    itemStyle: {
      areaColor: "transparent",
      borderColor: "rgba(94, 174, 254,1)",
      borderWidth: 1,
    },
    emphasis: {
      // 也是选中样式
      label: {
        show: true,
        color: "#ccc",
      },
      itemStyle: {
        areaColor: "",
        borderColor: "rgba(94, 174, 254,1)",
        borderWidth: 2,
      },
    },
  },
  categoryAxis: {
    axisLine: {
      show: true,
      lineStyle: {
        color: "#eeeeee",
      },
    },
    axisTick: {
      show: true,
      lineStyle: {
        color: "#eeeeee",
      },
    },
    axisLabel: {
      show: true,
      textStyle: {
        color: "#eeeeee",
      },
    },
    splitLine: {
      show: false,
      lineStyle: {
        color: ["#aaaaaa"],
      },
    },
    splitArea: {
      show: false,
      areaStyle: {
        color: ["#eeeeee"],
      },
    },
  },
  valueAxis: {
    axisLine: {
      show: false,
      lineStyle: {
        color: "#eeeeee",
      },
    },
    axisTick: {
      show: false,
      lineStyle: {
        color: "#eeeeee",
      },
    },
    axisLabel: {
      show: true,
      textStyle: {
        color: "#eeeeee",
      },
    },
    splitLine: {
      show: false,
      lineStyle: {
        color: ["#aaaaaa"],
      },
    },
    splitArea: {
      show: false,
      areaStyle: {
        color: ["#eeeeee"],
      },
    },
  },
  logAxis: {
    axisLine: {
      show: true,
      lineStyle: {
        color: "#eeeeee",
      },
    },
    axisTick: {
      show: true,
      lineStyle: {
        color: "#eeeeee",
      },
    },
    axisLabel: {
      show: true,
      textStyle: {
        color: "#eeeeee",
      },
    },
    splitLine: {
      show: true,
      lineStyle: {
        color: ["#aaaaaa"],
      },
    },
    splitArea: {
      show: false,
      areaStyle: {
        color: ["#eeeeee"],
      },
    },
  },
  timeAxis: {
    axisLine: {
      show: true,
      lineStyle: {
        color: "#eeeeee",
      },
    },
    axisTick: {
      show: true,
      lineStyle: {
        color: "#eeeeee",
      },
    },
    axisLabel: {
      show: true,
      textStyle: {
        color: "#eeeeee",
      },
    },
    splitLine: {
      show: true,
      lineStyle: {
        color: ["#aaaaaa"],
      },
    },
    splitArea: {
      show: false,
      areaStyle: {
        color: ["#eeeeee"],
      },
    },
  },
  toolbox: {
    iconStyle: {
      normal: {
        borderColor: "#999999",
      },
      emphasis: {
        borderColor: "#666666",
      },
    },
  },
  legend: {
    textStyle: {
      color: "#eeeeee",
    },
  },
  tooltip: {
    axisPointer: {
      lineStyle: {
        color: "#eeeeee",
        width: "1",
      },
      crossStyle: {
        color: "#eeeeee",
        width: "1",
      },
    },
  },
  timeline: {
    lineStyle: {
      color: "#eeeeee",
      width: 1,
    },
    itemStyle: {
      normal: {
        color: "#dd6b66",
        borderWidth: 1,
      },
      emphasis: {
        color: "#a9334c",
      },
    },
    controlStyle: {
      normal: {
        color: "#eeeeee",
        borderColor: "#eeeeee",
        borderWidth: 0.5,
      },
      emphasis: {
        color: "#eeeeee",
        borderColor: "#eeeeee",
        borderWidth: 0.5,
      },
    },
    checkpointStyle: {
      color: "#e43c59",
      borderColor: "rgba(194,53,49,0.5)",
    },
    label: {
      normal: {
        textStyle: {
          color: "#eeeeee",
        },
      },
      emphasis: {
        textStyle: {
          color: "#eeeeee",
        },
      },
    },
  },
  visualMap: {
    textStyle: {
      color: "#eeeeee",
    },
    color: ["#bf444c", "#d88273", "#f6efa6"],
  },
  dataZoom: {
    backgroundColor: "rgba(47,69,84,0)",
    dataBackgroundColor: "rgba(255,255,255,0.3)",
    fillerColor: "rgba(167,183,204,0.4)",
    handleColor: "#a7b7cc",
    handleSize: "100%",
    textStyle: {
      color: "#eeeeee",
    },
  },
  markPoint: {
    label: {
      normal: {
        textStyle: {
          color: "#eeeeee",
        },
      },
      emphasis: {
        textStyle: {
          color: "#eeeeee",
        },
      },
    },
  },
};
echarts.registerTheme("bigdata_dark", theme);
