@use "~element-ui/packages/theme-chalk/src/mixins/utils" as *;
@use "./transition.scss";
$fa-font-path: "~font-awesome/fonts/";

html,
body {
  font-family: "Microsoft YaHei", "Avenir", Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow: hidden;
  @include themify("self") {
    background: themed("$frameContainerBg");
    color: themed("$--color-text-primary");
    font-size: themed("$--font-size-base");
    &::-webkit-scrollbar-track-piece {
      background-color: themed("$--background-color-base");
    }
    &::-webkit-scrollbar-thumb:vertical {
      background-color: themed("$--color-primary-light-1");
    }
    &::-webkit-scrollbar-thumb:horizontal {
      background-color: themed("$--color-primary-light-1");
    }
  }
}

body .el-table th.gutter {
  display: table-cell !important;
}

*,
*::before,
*::after {
  box-sizing: border-box;
  scrollbar-width: thin;
  @include themify() {
    scrollbar-color: themed("$--color-primary-light-1")
      themed("$--background-color-base");
  }
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track-piece {
  border-radius: 6px;
}

::-webkit-scrollbar-thumb:vertical {
  height: 5px;
  border-radius: 6px;
}

::-webkit-scrollbar-thumb:horizontal {
  width: 5px;
  border-radius: 6px;
}

.el-table {
  ::-webkit-scrollbar {
    width: 8px;
    height: 9px;
    &-track-piece,
    &-thumb {
      border-radius: 3px;
    }
  }
  .el-table__body-wrapper {
    scrollbar-width: auto;
  }
}
.el-table--small,
.el-descriptions--small {
  font-size: 13px !important;
}

@include themify("!&") {
  ::-webkit-scrollbar-track-piece {
    background-color: themed("$--background-color-base");
  }

  ::-webkit-scrollbar-thumb:vertical {
    background-color: themed("$--color-primary-light-1");
    &:hover {
      background-color: mix(
        themed("$--color-black"),
        themed("$--color-primary-light-4"),
        40%
      );
    }
  }

  ::-webkit-scrollbar-thumb:horizontal {
    background-color: themed("$--color-primary-light-1");
    &:hover {
      background-color: mix(
        themed("$--color-black"),
        themed("$--color-primary-light-4"),
        40%
      );
    }
  }
}

.el-card {
  .el-card__header {
    font-weight: 700;
  }
}

.el-container,
.el-main {
  width: 100%;
}

.clearfix {
  @include utils-clearfix;
}

.text-truncate {
  @include utils-ellipsis;
}

.vertical-center {
  @include utils-vertical-center;
}

// cursor
.cursor-pointer {
  cursor: pointer;
}
::v-deep .el-button {
  user-select: unset;
}
// .el-tabs--card > .el-tabs__header .el-tabs__item .el-icon-close {
//   line-height: 14px !important;
// }

@include themify("!&") {
  .el-loading-mask {
    background-color: rgba(themed("$--color-white"), 0.9) !important;
  }

  .el-table--striped {
    .el-table__body tr.el-table__row--striped td {
      background: rgba(
        themed("$--table-row-hover-background-color"),
        0.65
      ) !important;
    }
    &.el-table--enable-row-hover
      .el-table__body
      tr.el-table__row--striped:hover
      td {
      background-color: themed("$--table-row-hover-background-color");
    }
  }

  .el-table__expand-icon {
    color: themed("$--icon-color-base") !important;
  }

  .frame-tabs .el-tabs__item.is-active {
    background: rgba(themed("$frameTab-background-color-active"), 0.9);
    color: themed("$frameTab-text-color-active");
    &:hover {
      color: themed("$frameTab-text-color-active");
    }
  }

  .text-primary {
    color: themed("$--color-primary");
  }

  .text-success {
    color: themed("$--color-success");
  }

  .text-warning {
    color: themed("$--color-warning");
  }

  .text-danger {
    color: themed("$--color-danger");
  }

  .text-info {
    color: themed("$--color-info");
  }

  // nprogress 样式
  #nprogress .bar {
    background: themed("$--color-primary") !important;
    height: 3px !important;
  }

  // v-chart v-chart-h样式
  .v-charts-data-empty {
    background-color: rgba(themed("$--color-white"), 0.9) !important;
  }
  .v-charts-component-loading {
    background-color: rgba(themed("$--color-white"), 0.9) !important;
  }

  // tooltip 样式
  .el-tooltip__popper.is-light {
    color: themed("$--tooltip-fill") !important;
  }

  // input
  input:not([type]),
  input[type=""],
  input[type="text"],
  input[type="password"],
  input[type="textarea"] {
    -webkit-appearance: none;
    background-color: themed("$--input-background-color");
    background-image: none;
    border-radius: themed("$--input-border-radius");
    border: themed("$--input-border");
    box-sizing: border-box;
    color: themed("$--input-font-color");
    display: inline-block;
    font-size: inherit;
    outline: none;
    padding: 0 15px;
    transition: themed("$--border-transition-base");

    &::placeholder {
      color: themed("$--input-placeholder-color");
    }

    &:hover {
      border-color: themed("$--input-hover-border");
    }

    &:focus {
      outline: none;
      border-color: themed("$--input-focus-border");
    }

    &.el-select__input {
      border: inherit;
    }
  }

  // drawer
  .el-drawer__header {
    color: themed("$--color-text-secondary") !important;
  }

  // upload
  .el-upload-dragger {
    @include themify() {
      background: themed("$--color-white");
      border: 1px dashed themed("$--border-color-lighter");
    }
  }
}
