// 字体设置
@include themify("!&") {
  // 点击选中
  .ztree li a {
    color: themed("$--color-text-primary");
  }
  .ztree li a.curSelectedNode {
    background-color: themed("$--color-primary");
  }
  // 展开图标
  .ztree li span.button.ico_open {
    background-image: url("~#{themed('$--ztree-background-img')}");
    // background-image: #{$--ztree-background-img};
  }
  // 闭合图标
  .ztree li span.button.ico_close {
    background-image: url("~#{themed('$--ztree-background-img')}");
  }
  // 最底层文件
  .ztree li span.button.ico_docu {
    background-image: url("~#{themed('$--ztree-background-img')}");
  }
  // 复选框未勾选状态
  .ztree li span.button.chk.checkbox_false_full {
    background-image: url("~#{themed('$--ztree-background-img')}");
  }
  .ztree li span.button.chk.checkbox_false_full_focus {
    background-image: url("~#{themed('$--ztree-background-img')}");
  }
  // 复选框 勾选
  .ztree li span.button.chk.checkbox_true_full {
    background-image: url("~#{themed('$--ztree-background-img')}");
  }
  .ztree li span.button.chk.checkbox_true_full_focus {
    background-image: url("~#{themed('$--ztree-background-img')}");
  }
  .ztree li span.button.chk.checkbox_true_part {
    background-image: url("~#{themed('$--ztree-background-img')}");
  }
  .ztree li span.button.chk.checkbox_false_part {
    background-image: url("~#{themed('$--ztree-background-img')}");
  }
}

/* // 展开图标 */
.ztree li span.button.ico_open {
  width: 17px;
  height: 17px;
  background-position: -109px -15px;
}

// 闭合图标
.ztree li span.button.ico_close {
  width: 17px;
  height: 17px;
  background-position: -109px 2px;
}

// 最底层文件
.ztree li span.button.ico_docu {
  width: 17px;
  height: 17px;
  background-position: -109px -32px;
  margin-top: 3px;
}

// 根节点 -
// .ztree li span.button.root_open {
//   width: 19px;
//   height: 19px;
//   background-image: url("~#{$--ztree-background-img}");
//   background-position: -91px -54px;
// }
// // 中间打开+
// .ztree li span.button.center_open {
//   width: 19px;
//   height: 19px;
//   background-image: url("~#{$--ztree-background-img}");
//   background-position: -91px -17px;
// }
// //中间连线
// .ztree li ul.line {
// }

// 复选框未勾选状态
.ztree li span.button.chk.checkbox_false_full {
  width: 14px;
  height: 14px;
  background-position: 0px 0px;
}

.ztree li span.button.chk.checkbox_false_full_focus {
  width: 14px;
  height: 14px;
  background-position: 0px -14px;
}

// 复选框 勾选
.ztree li span.button.chk.checkbox_true_full {
  width: 14px;
  height: 14px;
  background-position: -14px 0px;
}

.ztree li span.button.chk.checkbox_true_full_focus {
  width: 14px;
  height: 14px;
  background-position: -14px -14px;
}

.ztree li span.button.chk.checkbox_true_part {
  width: 14px;
  height: 14px;
  background-position: -14px -56px;
}

.ztree li span.button.chk.checkbox_false_part {
  width: 14px;
  height: 14px;
  background-position: -14px -56px;
}

// 在<style lang="scss" scoped>中引入时把下面注释取消
// 字体设置
@include themify("com") {
  ::v-deep .ztree li a {
    color: themed("$--color-text-primary");
  }
  ::v-deep .ztree li a.curSelectedNode {
    background-color: themed("$--color-primary");
  }
  // 展开图标
  ::v-deep .ztree li span.button.ico_open {
    background-image: url("~#{themed('$--ztree-background-img')}");
    // background-image: #{$--ztree-background-img};
  }
  // 闭合图标
  ::v-deep.ztree li span.button.ico_close {
    background-image: url("~#{themed('$--ztree-background-img')}");
  }
  // 最底层文件
  ::v-deep.ztree li span.button.ico_docu {
    background-image: url("~#{themed('$--ztree-background-img')}");
  }
  // 复选框未勾选状态
  ::v-deep.ztree li span.button.chk.checkbox_false_full {
    background-image: url("~#{themed('$--ztree-background-img')}");
  }
  ::v-deep.ztree li span.button.chk.checkbox_false_full_focus {
    background-image: url("~#{themed('$--ztree-background-img')}");
  }
  // 复选框 勾选
  ::v-deep.ztree li span.button.chk.checkbox_true_full {
    background-image: url("~#{themed('$--ztree-background-img')}");
  }
  ::v-deep.ztree li span.button.chk.checkbox_true_full_focus {
    background-image: url("~#{themed('$--ztree-background-img')}");
  }
  ::v-deep.ztree li span.button.chk.checkbox_true_part {
    background-image: url("~#{themed('$--ztree-background-img')}");
  }
  ::v-deep.ztree li span.button.chk.checkbox_false_part {
    background-image: url("~#{themed('$--ztree-background-img')}");
  }
}

// 展开图标
::v-deep.ztree li span.button.ico_open {
  width: 17px;
  height: 17px;
  background-position: -109px -15px;
}

// 闭合图标
::v-deep.ztree li span.button.ico_close {
  width: 17px;
  height: 17px;
  background-position: -109px 2px;
}

// 最底层文件
::v-deep.ztree li span.button.ico_docu {
  width: 17px;
  height: 17px;
  background-position: -109px -32px;
  margin-top: 3px;
}

// 根节点 -
// ::v-deep.ztree li span.button.root_open {
//   width: 19px;
//   height: 19px;
//   background-image: url("~#{$--ztree-background-img}");
//   background-position: -91px -54px;
// }
// // 中间打开+
// ::v-deep.ztree li span.button.center_open {
//   width: 19px;
//   height: 19px;
//   background-image: url("~#{$--ztree-background-img}");
//   background-position: -91px -17px;
// }
// //中间连线
// ::v-deep.ztree li ul.line {
// }

// 复选框未勾选状态
::v-deep.ztree li span.button.chk.checkbox_false_full {
  width: 14px;
  height: 14px;
  background-position: 0px 0px;
}

::v-deep.ztree li span.button.chk.checkbox_false_full_focus {
  width: 14px;
  height: 14px;
  background-position: 0px -14px;
}

// 复选框 勾选
::v-deep.ztree li span.button.chk.checkbox_true_full {
  width: 14px;
  height: 14px;
  background-position: -14px 0px;
}

::v-deep.ztree li span.button.chk.checkbox_true_full_focus {
  width: 14px;
  height: 14px;
  background-position: -14px -14px;
}

::v-deep.ztree li span.button.chk.checkbox_true_part {
  width: 14px;
  height: 14px;
  background-position: -14px -56px;
}

::v-deep.ztree li span.button.chk.checkbox_false_part {
  width: 14px;
  height: 14px;
  background-position: -14px -56px;
}
