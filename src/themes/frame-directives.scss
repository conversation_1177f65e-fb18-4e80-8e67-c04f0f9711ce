$browser-default-font-size: 12px !default; //$--font-size-base
@function px2rem($px) {
  //$px为需要转换的字号
  @return $px / $browser-default-font-size * 1rem;
}
@function px2rem2($px, $base: 12px) {
  $browser-default-font-size: $base;
  //$px为需要转换的字号
  @return $px / $browser-default-font-size * 1rem;
}
@mixin remCalc($property, $base: 12px, $values...) {
  $max: length($values); //返回$values列表的长度值
  $pxValues: "";
  $remValues: "";
  $browser-default-font-size: $base;

  @for $i from 1 through $max {
    $value: strip-units(
      nth($values, $i)
    ); //返回$values列表中的第$i个值，并将单位值去掉
    $browser-default-font-size: strip-units($browser-default-font-size);
    $pxValues: #{$pxValues + $value * $browser-default-font-size}px;

    @if $i < $max {
      $pxValues: #{$pxValues + " "};
    }
  }

  @for $i from 1 through $max {
    $value: strip-units(nth($values, $i));
    $remValues: #{$remValues + $value}rem;

    @if $i < $max {
      $remValues: #{$remValues + " "};
    }
  }

  #{$property}: $pxValues;
  #{$property}: $remValues;
}

// 主题映射方法
$theme-map: ();
@mixin themify($type: "&", $themes: $themes) {
  @each $theme-name, $map in $themes {
    @if $type == "&" {
      .custom-theme-#{$theme-name} & {
        @each $key, $value in $map {
          $theme-map: map-merge(
            $theme-map,
            (
              "#{$key}": $value,
            )
          ) !global;
        }

        @content;
      }
    } @else if $type == "!&" {
      .custom-theme-#{$theme-name} {
        @each $key, $value in $map {
          $theme-map: map-merge(
            $theme-map,
            (
              "#{$key}": $value,
            )
          ) !global;
        }

        @content;
      }
    } @else if $type == "self" {
      &.custom-theme-#{$theme-name} {
        @each $key, $value in $map {
          $theme-map: map-merge(
            $theme-map,
            (
              "#{$key}": $value,
            )
          ) !global;
        }

        @content;
      }
    } @else if $type == "com" {
      .custom-theme-#{$theme-name} * #{&} {
        @each $key, $value in $map {
          $theme-map: map-merge(
            $theme-map,
            (
              "#{$key}": $value,
            )
          ) !global;
        }

        @content;
      }
    }

    $theme-map: () !global;
  }
}

@function themed($key) {
  @return map-get($theme-map, $key);
}
