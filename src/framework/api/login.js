import axios from "@/utils/request";

export function login(data) {
  return axios({
    url: "/login",
    method: "post",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    data: data,
  });
}

export function getInfo() {
  return axios({
    url: "framework/sysmanage/resources/findbytoken",
    method: "get",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
  });
}

export function logout() {
  return axios({
    url: "/logout",
    method: "post",
    headers: {
      "Content-Type": "application/json;charset=UTF-8",
    },
  });
}

export function checkToken(token) {
  return axios({
    url: "/login/token/verify?token=" + token,
    method: "get",
  });
}
