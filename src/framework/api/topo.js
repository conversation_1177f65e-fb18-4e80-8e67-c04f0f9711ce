import axios from "@/utils/request";

export const getTopoData = (code, resId, resType) => {
  return axios.request({
    url:
      "/topoapi/index?code=" +
      code +
      "&resId=" +
      (resId || "NULL") +
      "&resType=" +
      (resType || "NULL"),
    data: {},
    method: "get",
  });
};
export const saveTopoData = (code, resId, resType, json) => {
  var data = {
    code: code,
    resId: resId || "NULL",
    resType: resType || "NULL",
    location: json,
  };
  return axios.request({
    url: "/topoapi/save",
    data,
    method: "post",
  });
};
export const deleteTopoInstance = (code, resId, resType) => {
  var data = {
    code: code,
    resId: resId || "NULL",
    resType: resType || "NULL",
  };
  return axios.request({
    url: "/topoapi/delete",
    data,
    method: "post",
  });
};
export const alarmRegister = userId => {
  var data = {
    userId: userId,
  };
  return axios.request({
    url: "/topoapi/alarm/register",
    data,
    method: "post",
  });
};
export const alarmUnRegister = sessionId => {
  var data = {
    sessionId: sessionId,
  };
  return axios.request({
    url: "/topoapi/alarm/unregister",
    data,
    method: "post",
  });
};
export const alarmStat = (sessionId, code, resId, resType) => {
  return axios.request({
    url:
      "/topoapi/alarm/stat?code=" +
      code +
      "&resId=" +
      resId +
      "&resType=" +
      resType +
      "&sessionId=" +
      sessionId,
    data: {},
    method: "get",
  });
};
export const alarmReal = sessionId => {
  return axios.request({
    url: "/topoapi/alarm/real?sessionId=" + sessionId,
    data: {},
    method: "get",
  });
};
