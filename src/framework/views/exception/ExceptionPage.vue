<template>
  <div :style="exceptionStyle">
    <div class="exception">
      <div class="img">
        <img :src="config[type].img" />
      </div>
      <div class="content">
        <h1>{{ config[type].title }}</h1>
        <div class="desc">{{ config[type].desc }}</div>
        <div class="action">
          <el-button type="primary" @click="handleToHome">返回首页</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import types from "./type";
import { mapGetters } from "vuex";

export default {
  name: "Exception",
  props: {
    type: {
      type: String,
      default: "404",
    },
  },
  data() {
    return {
      config: types,
      exceptionStyle: { overflowY: "auto", height: "90vh" },
    };
  },
  computed: {
    ...mapGetters(["frameStyle"]),
  },
  watch: {
    "frameStyle.headerHeight"() {
      this.getExceptionStyle();
    },
  },
  mounted() {
    this.getExceptionStyle();
  },
  methods: {
    handleToHome() {
      this.$router.replace({ name: "home" });
    },
    getExceptionStyle() {
      this.$nextTick(() => {
        let classList = this?.$parent?.$parent?.$el?.classList;
        if (
          classList &&
          Array.from(classList).includes?.("frame-container-main")
        ) {
          this.exceptionStyle.height = `calc(100vh - ${this.frameStyle.headerHeight}px)`;
        } else {
          this.exceptionStyle.height = `100vh`;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.exception {
  position: relative;
  top: 50%;
  transform: translateY(-50%);
  // min-height: 500px;
  // height: 100%;
  align-items: center;
  text-align: center;
  // margin-top: 150px;
  .img {
    display: inline-block;
    padding-right: 52px;
    zoom: 1;
    img {
      height: 360px;
      max-width: 430px;
    }
  }
  .content {
    display: inline-block;
    flex: auto;
    h1 {
      // color: #434e59;
      @include themify() {
        color: themed("$--color-text-primary");
      }
      font-size: 72px;
      font-weight: 600;
      line-height: 72px;
      margin-bottom: 24px;
    }
    .desc {
      // color: rgba(0, 0, 0, 0.45);
      @include themify() {
        color: themed("$--color-text-secondary");
      }
      font-size: 20px;
      line-height: 28px;
      margin-bottom: 16px;
    }
  }
}
</style>
