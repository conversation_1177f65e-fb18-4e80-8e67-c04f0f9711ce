<template>
  <el-container
    class="layout"
    :class="{ 'layout-fullscreen': fullScreen }"
    direction="horizontal"
  >
    <frame-aside
      :width="asideWidth"
      :style="asideStyle"
      v-show="!fullScreen"
    ></frame-aside>
    <el-container
      class="layout-container"
      direction="vertical"
      :style="layoutContainerStyle"
    >
      <div
        ref="header"
        class="frame-header-wrap"
        :class="headerClass"
        :style="headerNavbarStyle"
        v-show="!fullScreen"
      >
        <frame-head-navbar
          v-if="showHeadNavBar"
          :height="headNavbar + 'px'"
        ></frame-head-navbar>
        <tab-view v-if="!hideTabView"></tab-view>
      </div>
      <el-main
        class="frame-container-main"
        :class="{ 'fullscreen-page': routeFullScreen }"
        :style="containerStyle"
      >
        <slot></slot>
        <el-button
          type="primary"
          class="btn-exit-full-screen"
          @click.stop="toggleFullScreen(false)"
          v-if="fullScreen"
        >
          <el-tooltip content="退出全屏"
            ><base-icon
              :icon-class="
                fullScreen ? 'exit-full-screen' : 'full-screen'
              " /></el-tooltip
        ></el-button>
      </el-main>
    </el-container>
  </el-container>
</template>

<script>
import FrameAside from "./FrameAside";
import FrameHeadNavbar from "./FrameHeadNavbar";
import TabView from "../TabView/TabView";
import { mapGetters } from "vuex";

export default {
  name: "FrameLayout",
  components: {
    FrameAside,
    FrameHeadNavbar,
    TabView,
  },
  data() {
    return {
      headerHeight: 50,
      headNavbar: 50,
    };
  },
  computed: {
    ...mapGetters([
      "layoutMode",
      "sidebar",
      "fixedHeader",
      "fullScreen",
      "visitedViews",
      "topShowLogo",
      "topShowBreadcrumb",
      "hideTabView",
      "openOnceFullscreen",
      "keepAliveInstance",
      "frameStyle",
    ]),
    asideWidth() {
      return this.sidebar.collapse ? "65px" : "208px";
    },
    asideStyle() {
      return {
        // height: `cal(100vh-${this.height})`,
        position: this.sidebar.fixed ? "fixed" : "",
        overflow: "hidden",
        minHeight: "100vh",
      };
    },
    layoutContainerStyle() {
      return {
        marginLeft: this.fullScreen ? "" : this.asideWidth,
      };
    },
    containerStyle() {
      const height = this.frameStyle.headerHeight - 16;
      return {
        marginTop: this.fullScreen
          ? "0"
          : this.fixedHeader
          ? height + "px"
          : "",
        minHeight: this.fullScreen
          ? ""
          : this.fixedHeader
          ? `calc(100vh - ${height}px)`
          : "",
        padding: this.routeFullScreen ? "0px" : "",
      };
    },
    headerClass() {
      return { "frame-header-fix": this.fixedHeader };
    },
    headerNavbarStyle() {
      return {
        width: this.fixedHeader ? `calc(100% - ${this.asideWidth})` : "",
      };
    },
    routeFullScreen() {
      return this.$route.meta.openTarget === "FullScreen";
    },
    showHeadNavBar() {
      return process.env.NODE_ENV !== "production";
    },
  },
  watch: {
    $route(to) {
      if (
        this.openOnceFullscreen &&
        this.keepAliveInstance["keys"].findIndex(tmp => tmp === to.fullPath) !==
          -1
      ) {
        this.toggleFullScreen(
          to.meta.openTarget === "FullScreen" && this.fullScreen
        );
      } else {
        this.toggleFullScreen(to.meta.openTarget === "FullScreen");
      }
    },
    layoutMode() {
      this.updateStyle();
    },
    fullScreen() {
      this.updateStyle();
    },
  },
  created() {
    this.toggleFullScreen(this.$route.meta.openTarget === "FullScreen");
  },
  mounted() {
    this.$nextTick(() => {
      this.$store.dispatch(
        "keepAlive/updateActionInstance",
        this.$slots.default[0].componentInstance
      );
      this.updateStyle();
    });
  },
  methods: {
    toggleFullScreen(fullScreen) {
      this.$store.dispatch("settings/toggleFullScreen", fullScreen);
    },
    updateStyle() {
      this.$nextTick(() => {
        this.$store.commit(
          "settings/UPDATE_FRAMESTYLE",
          window.getComputedStyle(this.$refs.header).height
        );
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.layout {
  position: relative;
  .layout-container {
    .frame-header-wrap {
      width: 100%;
      @include themify() {
        background-color: rgba(themed("$componentBg"), 1);
        // border-bottom: $--border-base;
      }
      // box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 0 3px 0 rgba(0, 0, 0, 0.04);
    }
    .head-navbar {
      width: 100%;
      margin-bottom: 2px;
    }
    .frame-header-fix {
      position: fixed;
      z-index: 2000;
    }
    ::v-deep .frame-tabs .el-tabs__header {
      margin: 0 0;
    }
    .frame-container-main {
      // background-color: $frameContainerBg;
      padding: 0px;
      overflow-x: hidden;
      ::v-deep {
        .el-loading-mask {
          z-index: auto;
        }
        > *:first-child:not([class~="full-main"]) {
          padding: 8px;
        }
        > .full-main {
          padding: 0;
        }
      }
    }
  }
  .btn-exit-full-screen {
    position: fixed;
    top: 240px;
    right: 0;
    width: 48px;
    height: 48px;
    @include themify() {
      background-color: rgba(themed("$--color-primary"), 0.4);
    }
    line-height: 48px;
    font-size: 24px;
    text-align: center;
    padding: 0;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    z-index: 999;
    &:hover,
    &:focus {
      @include themify() {
        background-color: rgba(themed("$--color-primary"), 0.5);
      }
    }
  }
}

.horizontal-menu-breadcrumb {
  width: 100%;
  @include themify() {
    background-color: themed("$componentBg");
  }
  margin-left: 0px !important;
  margin-top: 0px;
  padding: 0 12px;
}

::v-deep .el-backtop {
  background: rgba(0, 0, 0, 0.4) !important;
  .el-button--info {
    background-color: rgba(0, 0, 0, 0.4);
    border-color: rgba(0, 0, 0, 0.4);
  }
}
</style>
