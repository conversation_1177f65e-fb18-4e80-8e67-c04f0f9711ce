<template>
  <frame-layout>
    <template v-if="!hideTabView">
      <!-- <transition name="fade-transform" mode="out-in"> -->
      <keep-alive>
        <router-view :key="key" v-if="!$route.meta.noCache" />
      </keep-alive>
      <!-- </transition> -->
      <!-- <transition name="fade-transform" mode="out-in"> -->
      <router-view :key="key" v-if="$route.meta.noCache"></router-view>
      <!-- </transition> -->
    </template>
    <router-view v-else :key="key"></router-view>
    <settings :buttonTop="300"></settings>
  </frame-layout>
</template>

<script>
import FrameLayout from "./FrameLayout.vue";
import Settings from "../settings/Settings";
import { mapGetters } from "vuex";

export default {
  name: "Layout",
  components: {
    FrameLayout,
    Settings,
  },
  computed: {
    ...mapGetters(["hideTabView"]),
    key() {
      return this.$route.fullPath;
    },
    cachedViews() {
      return this.$store.state.tagsView.cachedViews;
    },
  },
};
</script>
