<template>
  <el-aside :width="width">
    <div ref="topWrap" class="top-wrap">
      <div class="aside-title" :class="{ collapse: sidebar.collapse }">
        {{ asideTitle }}
      </div>
      <div class="aside-icon cursor-pointer" @click="toggleAside">
        <!-- <em :class="`fa fa-${sidebar.collapse ? 'bars' : 'arrow-left'}`"></em> -->
        <hamburger :is-active="!sidebar.collapse" />
      </div>
    </div>
    <el-scrollbar
      v-if="sidebar.fixed"
      ref="scrollContainer"
      :style="scrollbarStyle"
      class="scroll-container"
    >
      <frame-menu ref="frameMenu" :is-collapse="sidebar.collapse"></frame-menu>
    </el-scrollbar>
    <frame-menu
      v-else
      ref="frameMenu"
      :is-collapse="sidebar.collapse"
    ></frame-menu>
  </el-aside>
</template>

<script>
import Hamburger from "@/components/Hamburger";
import FrameMenu from "../menu/FrameMenu.vue";
import { mapGetters } from "vuex";
import settings from "@/settings";
const { asideTitle = "故障工单" } = settings;

export default {
  name: "FrameAside",
  components: {
    Hamburger,
    FrameMenu,
  },
  props: {
    width: {
      type: String,
      default: "200px",
      required: false,
    },
  },
  data() {
    return {
      topWrapHeight: 90,
    };
  },
  computed: {
    ...mapGetters(["sidebar"]),
    scrollbarStyle() {
      return {
        height: this.sidebar.fixed
          ? `calc(100vh - ${this.topWrapHeight}px)`
          : "",
      };
    },
    scrollWrapper() {
      return this.$refs.scrollContainer.$refs.wrap;
    },
    asideTitle() {
      return asideTitle;
    },
  },
  watch: {
    $route() {
      // this.moveToTarget(this.$route.path);
    },
  },
  methods: {
    moveToTarget(currentTag) {
      const $container = this.$refs.scrollContainer.$el;
      const $containerHeight = $container.offsetHeight;
      const $scrollWrapper = this.scrollWrapper;
      const tagList = this.$refs.frameMenu.$refs.list.filter(
        item => !item.item["hidden"]
      );
      // .slice(1);

      let firstTag = null;
      let lastTag = null;

      // find first tag and last tag
      if (tagList.length > 0) {
        firstTag = tagList[0].basePath;
        lastTag = tagList[tagList.length - 1].basePath;
      }

      if (currentTag.indexOf(firstTag) !== -1) {
        $scrollWrapper.scrollTop = 0;
      } else if (currentTag.indexOf(lastTag) !== -1) {
        $scrollWrapper.scrollTop =
          $scrollWrapper.scrollHeight - $containerHeight;
      } else {
        // find preTag and nextTag
        const currentIndex = tagList.findIndex(
          item => currentTag.indexOf(item.basePath) !== -1
        );
        if (currentIndex === -1) {
          return false;
        }
        const prevTag = tagList[currentIndex - 1];
        const nextTag = tagList[currentIndex + 1];

        // the tag's offsetTop after of nextTag
        const afterNextTagOffsetTop =
          nextTag.$el.offsetTop + nextTag.$el.offsetHeight;

        // the tag's offsetTop before of prevTag
        const beforePrevTagOffsetTop = prevTag.$el.offsetTop;

        if (
          afterNextTagOffsetTop >
          $scrollWrapper.scrollTop + $containerHeight
        ) {
          $scrollWrapper.scrollTop = afterNextTagOffsetTop - $containerHeight;
        } else if (beforePrevTagOffsetTop < $scrollWrapper.scrollTop) {
          $scrollWrapper.scrollTop = beforePrevTagOffsetTop;
        }
      }
    },
    toggleAside() {
      this.$store.dispatch("settings/toggleSidebarCollapse");
    },
    getHeadHeight() {
      this.headHeight = Math.ceil(
        window
          .getComputedStyle(this.$refs.topWrap)
          ?.height?.replace?.(/auto|px/, "") ?? 90
      );
    },
  },
};
</script>

<style lang="scss" scoped>
.el-aside {
  .top-wrap {
    @include themify() {
      border-bottom: 1px solid themed("$--border-color-light");
      border-right: 1px solid themed("$--border-color-light");
    }
  }
  .aside-title {
    text-align: center;
    line-height: 21px;
    font-size: 15px;
    font-weight: 700;
    padding: 18px 0 21px;
    @include themify() {
      border-bottom: 1px solid themed("$--border-color-light");
    }
    &.collapse {
      letter-spacing: 6px;
    }
  }
  .aside-icon {
    text-align: center;
    height: 40px;
    line-height: 40px;
    font-size: 15px;
  }
  @include themify() {
    background-color: themed("$frameMenu-background-color");
    color: themed("$frameMenu-text-color");
  }
  .el-menu {
    border: 0;
    @include themify() {
      border-right: 1px solid themed("$--border-color-light");
    }
    a {
      overflow: hidden;
    }
  }
  .scroll-container ::v-deep {
    .el-menu--collapse {
      .submenu-title-noDropdown {
        text-align: center;
        .el-tooltip {
          padding: 0 !important;
        }
      }
      .el-submenu {
        text-align: center;
        & > .el-submenu__title {
          padding: 0 !important;
        }
      }
    }
  }
}
</style>

<style lang="scss">
.scroll-container {
  .el-scrollbar__wrap {
    overflow-x: hidden !important;
  }
  .is-horizontal {
    display: none !important;
  }
}
</style>
