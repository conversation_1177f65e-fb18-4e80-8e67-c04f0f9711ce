<template>
  <div class="head-navbar" :style="{ height: height }">
    <hamburger
      id="hamburger-container"
      class="hamburger-container"
      :is-active="!sidebar.collapse"
      @toggleClick="toggleAside"
      :style="{ lineHeight: height }"
    />
    <breadcrumb
      v-if="sidebar.sideShowBreadcrumb"
      class="breadcrumb-container"
      :style="{ height: height, lineHeight: height }"
    />
    <personal-panel :height="height"></personal-panel>
  </div>
</template>

<script>
import Hamburger from "@/components/Hamburger";
import Breadcrumb from "@/components/Breadcrumb";
import PersonalPanel from "../tools/PersonalPanel";
import { mapGetters } from "vuex";

export default {
  name: "FrameHeadNavbar",
  components: {
    Hamburger,
    Breadcrumb,
    PersonalPanel,
  },
  props: {
    height: {
      type: String,
      default: "50px",
      required: false,
    },
  },
  computed: {
    ...mapGetters(["sidebar"]),
  },
  methods: {
    toggleAside() {
      this.$store.dispatch("settings/toggleSidebarCollapse");
    },
  },
};
</script>

<style lang="scss" scoped>
.head-navbar {
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  @include themify() {
    background-color: themed("$componentBg");
  }
  .hamburger-container {
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }
  .breadcrumb-container {
    float: left;
  }
}
</style>

<style lang="scss">
@include themify("!&") {
  .head-navbar {
    > .personal-panel span.usermenu-item {
      color: themed("$--color-black");
    }
  }
}
</style>
