<template>
  <el-header :height="height">
    <logo v-if="showLogo" :height="height" style="float: left"></logo>
    <el-scrollbar
      v-show="showMenu"
      ref="scrollContainer"
      style="float: left"
      :style="elScrollbarStyle"
      @wheel.native.prevent="handleScroll"
    >
      <frame-menu ref="frameMenu" mode="horizontal"></frame-menu>
    </el-scrollbar>
    <personal-panel ref="userMenu" :height="height"></personal-panel>
  </el-header>
</template>

<script>
import {
  addResizeListener,
  removeResizeListener,
} from "element-ui/src/utils/resize-event";
import Logo from "../tools/Logo.vue";
import PersonalPanel from "../tools/PersonalPanel";
import FrameMenu from "../menu/FrameMenu.vue";
const tagAndTagSpacing = 0; // tagAndTagSpacing

export default {
  name: "FrameHeader",
  components: {
    Logo,
    PersonalPanel,
    FrameMenu,
  },
  props: {
    height: {
      type: String,
      default: "60px",
      required: false,
    },
    showLogo: {
      type: Boolean,
      default: true,
      required: false,
    },
  },
  data() {
    return {
      userMenuWidth: "122px",
      showMenu: false,
    };
  },
  computed: {
    scrollWrapper() {
      return this.$refs.scrollContainer.$refs.wrap;
    },
    elScrollbarStyle() {
      let userMenuWidth = this.userMenuWidth;
      return {
        width: `calc(100% - 150px - (${userMenuWidth}))`,
      };
    },
  },
  watch: {
    $route() {
      this.moveToTarget(this.$route.path);
    },
  },
  created() {
    this.getUserMenuWidthFunc = this.getUserMenuWidth;
  },
  mounted() {
    addResizeListener(this.$refs.userMenu.$el, this.getUserMenuWidthFunc);
  },
  beforeDestroy() {
    removeResizeListener(this.$refs.userMenu.$el, this.getUserMenuWidthFunc);
  },
  methods: {
    handleScroll(e) {
      const eventDelta = e.wheelDelta || -e.deltaY * 40;
      const $scrollWrapper = this.scrollWrapper;
      $scrollWrapper.scrollLeft = $scrollWrapper.scrollLeft - eventDelta / 4;
    },
    moveToTarget(currentTag) {
      const $container = this.$refs.scrollContainer.$el;
      const $containerWidth = $container.offsetWidth;
      const $scrollWrapper = this.scrollWrapper;
      const tagList = this.$refs.frameMenu.$refs.list.filter(
        item => !item.item["hidden"]
      );
      // .slice(1);

      let firstTag = null;
      let lastTag = null;

      // find first tag and last tag
      if (tagList.length > 0) {
        firstTag = tagList[0].basePath;
        lastTag = tagList[tagList.length - 1].basePath;
      }

      if (currentTag.indexOf(firstTag) !== -1) {
        $scrollWrapper.scrollLeft = 0;
      } else if (currentTag.indexOf(lastTag) !== -1) {
        $scrollWrapper.scrollLeft =
          $scrollWrapper.scrollWidth - $containerWidth;
      } else {
        // find preTag and nextTag
        const currentIndex = tagList.findIndex(
          item => currentTag.indexOf(item.basePath) !== -1
        );
        if (currentIndex === -1) {
          return false;
        }
        const prevTag = tagList[currentIndex - 1];
        const nextTag = tagList[currentIndex + 1];

        // the tag's offsetLeft after of nextTag
        const afterNextTagOffsetLeft =
          nextTag.$el.offsetLeft + nextTag.$el.offsetWidth + tagAndTagSpacing;

        // the tag's offsetLeft before of prevTag
        const beforePrevTagOffsetLeft =
          prevTag.$el.offsetLeft - tagAndTagSpacing;

        if (
          afterNextTagOffsetLeft >
          $scrollWrapper.scrollLeft + $containerWidth
        ) {
          $scrollWrapper.scrollLeft = afterNextTagOffsetLeft - $containerWidth;
        } else if (beforePrevTagOffsetLeft < $scrollWrapper.scrollLeft) {
          $scrollWrapper.scrollLeft = beforePrevTagOffsetLeft;
        }
      }
    },
    getUserMenuWidth() {
      let self = this;
      const elComputedStyle = window.getComputedStyle(self.$refs.userMenu.$el);
      self.userMenuWidth =
        elComputedStyle.width + " + " + elComputedStyle.paddingLeft;
      self.showMenu = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.el-header ::v-deep {
  width: 100%;
  padding: 0 !important;
  @include themify() {
    background-color: themed("$frameMenu-background-color");
    color: themed("$frameMenu-text-color");
  }
  .logo {
    width: 150px;
  }
  // .user-wrapper {
  //   min-width: 160px;
  // }
  > .personal-panel span.usermenu-item {
    @include themify() {
      color: themed("$frameMenu-text-color");
    }
  }
  .el-scrollbar__bar.is-vertical {
    display: none;
  }
}
</style>
