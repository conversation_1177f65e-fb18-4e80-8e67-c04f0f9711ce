<template>
  <div class="frame-tabs">
    <scroll-pane ref="scrollPane" class="tags-view-wrapper" @scroll="closeMenu">
      <span
        v-for="tag in visitedViews"
        ref="tag"
        :key="tag.fullPath"
        :class="activePage == tag.fullPath ? 'active' : ''"
        class="tags-view-item cursor-pointer"
        @contextmenu.prevent="handleContextmenu($event, tag)"
        @click.prevent="activePage = tag.fullPath"
        >{{ tag.title }}
        <!-- v-if="!isAffix(tag)" -->
        <span class="el-icon-close" @click.prevent.stop="removeTab(tag)" />
      </span>
    </scroll-pane>
    <ul
      v-show="visible"
      class="el-dropdown-menu el-popper el-dropdown-menu--medium tagsview-menu"
      :style="{ left: left + 'px', top: top + 'px' }"
    >
      <template v-for="(item, name) in menuList">
        <template v-if="name === 'close'">
          <li
            class="el-dropdown-menu__item"
            :key="name"
            v-if="item.show && !(selectedTag.meta && selectedTag.meta.affix)"
            @click="closeSelectedTag(selectedTag)"
          >
            关闭
          </li>
        </template>
        <template v-else>
          <li
            class="el-dropdown-menu__item"
            :key="name"
            v-if="item.show"
            @click="item.action"
          >
            {{ item.label }}
          </li>
        </template>
      </template>
    </ul>
  </div>
</template>

<script>
import path from "path";
import { mapGetters } from "vuex";

import ScrollPane from "./ScrollPane.vue";

export default {
  name: "TabView",
  components: {
    ScrollPane,
  },
  data() {
    let self = this;
    return {
      activePage: "",
      multipage: true,
      visible: false,
      top: 0,
      left: 0,
      selectedTag: {},
      affixTags: [],
      menuList: {
        refresh: {
          label: "刷新",
          action: self["refreshSelectedTag"],
          show: true,
        },
        close: { label: "关闭", action: self["closeSelectedTag"], show: true },
        closeLeft: {
          label: "关闭左侧",
          action: self["closeLeftTags"],
          show: true,
        },
        closeRight: {
          label: "关闭右侧",
          action: self["closeRightTags"],
          show: true,
        },
        closeOthers: {
          label: "关闭其它",
          action: self["closeOthersTags"],
          show: true,
        },
      },
    };
  },
  computed: {
    ...mapGetters([
      "visitedViews",
      "permission_routes",
      "layoutMode",
      "sidebar",
      "fixedHeader",
    ]),
    layoutModeLeft() {
      return this.layoutMode === "sidemenu"
        ? this.fixedHeader
          ? 0
          : !this.sidebar.collapse
          ? 200
          : 65
        : 0;
    },
  },
  watch: {
    $route: function () {
      this.addTags();
      this.activePage = this.$route.fullPath;
      this.moveToCurrentTag();
    },
    activePage: function (key) {
      const route = this.visitedViewsFilter(key)[0];
      if (route) {
        this.$router.push(route);
      }
    },
    visible(value) {
      if (value) {
        document.body.addEventListener("click", this.closeMenu);
      } else {
        document.body.removeEventListener("click", this.closeMenu);
      }
    },
    layoutMode() {
      this.visible = false;
    },
  },
  created() {
    // this.initTags();
    this.addTags();
    this.activePage = this.$route.fullPath;
  },
  methods: {
    visitedViewsFilter(key) {
      return this.visitedViews.filter(item => item.fullPath === key);
    },
    removeAlert() {
      this.$message({
        message: "这是最后一页，不能再操作了",
        type: "warning",
        showClose: true,
        offset: 50,
      });
    },
    removeTab(view) {
      if (this.visitedViews.length === 1) {
        this.removeAlert();
        return false;
      }
      this.$store
        .dispatch("tagsView/delView", view)
        .then(({ visitedViews }) => {
          if (this.isActive(view)) {
            this.toLastView(visitedViews, view);
          }
        });
    },
    isActive(route) {
      return route.path === this.$route.path;
    },
    filterAffixTags(routes, basePath = "/") {
      let tags = [];
      routes.forEach(route => {
        if (route.meta && route.meta.affix) {
          const tagPath = path.resolve(basePath, route.path);
          tags.push({
            fullPath: tagPath,
            path: tagPath,
            name: route.name,
            meta: { ...route.meta },
          });
        }
        if (route.children) {
          const tempTags = this.filterAffixTags(route.children, route.path);
          if (tempTags.length >= 1) {
            tags = [...tags, ...tempTags];
          }
        }
      });
      return tags;
    },
    isAffix(tag) {
      return tag.name == "backbone_toDoRepairOrder";
    },
    initTags() {
      const affixTags = (this.affixTags = this.filterAffixTags(this.routes));
      for (const tag of affixTags) {
        // Must have tag name
        if (tag.name) {
          this.$store.dispatch("tagsView/addVisitedView", tag);
        }
      }
    },
    addTags() {
      const { name } = this.$route;
      if (name && name !== "redirect") {
        this.$store.dispatch("tagsView/addView", this.$route);
      }
      return false;
    },
    closeSelectedTag(view = this.selectedTag) {
      this.$store
        .dispatch("tagsView/delView", view)
        .then(({ visitedViews }) => {
          if (this.isActive(view)) {
            this.toLastView(visitedViews, view);
          }
        });
    },
    async closeLeftTags() {
      const { visitedViews } = await this.$store.dispatch(
        "tagsView/delLeftViews",
        this.selectedTag
      );
      if (this.isActive(this.selectedTag)) {
        this.toLastView(visitedViews, this.selectedTag);
      } else {
        this.$router.replace(this.selectedTag);
      }
    },
    async closeRightTags() {
      const { visitedViews } = await this.$store.dispatch(
        "tagsView/delRightViews",
        this.selectedTag
      );
      if (this.isActive(this.selectedTag)) {
        this.toLastView(visitedViews, this.selectedTag);
      } else {
        this.$router.replace(this.selectedTag);
      }
    },
    closeOthersTags() {
      this.$router.replace(this.selectedTag);
      this.$store.dispatch("tagsView/delOthersViews", this.selectedTag);
    },
    // eslint-disable-next-line no-unused-vars
    toLastView(visitedViews, view) {
      const latestView = visitedViews.slice(-1)[0];
      if (latestView) {
        this.$router.replace(latestView);
      } else {
        // now the default is to redirect to the home page if there is no tags-view,
        // you can adjust it according to your needs.
        /* if (view.name === "home") {
          // to reload home page
          this.$router.replace({ path: "/redirect" + view.fullPath });
        } else {
          this.$router.push("/");
        } */
        return;
      }
    },
    refreshSelectedTag() {
      let self = this;
      self.$nextTick(() => {
        const selectedTag = self.selectedTag;
        const { query, params, fullPath } = selectedTag;
        let skipType = "";
        if (JSON.stringify(query) !== "{}") {
          skipType = "query";
        } else if (JSON.stringify(params) !== "{}") {
          skipType = "params";
        }
        self.$router.replace({
          name: "redirect",
          params: {
            skipType: skipType,
            view: selectedTag,
            isCurrentRoute: fullPath === this.$route.fullPath,
          },
        });
      });
    },
    findTagIndex(key) {
      return this.visitedViews.findIndex(item => item.fullPath === key);
    },
    handleContextmenu(e, tag) {
      if (this.visitedViews.length === 1) {
        // return false;
        this.menuList["close"].show = false;
        this.menuList["closeLeft"].show = false;
        this.menuList["closeRight"].show = false;
        this.menuList["closeOthers"].show = false;
      } else {
        this.menuList["close"].show = true;
        this.menuList["closeOthers"].show = true;
        const tagIndex = this.findTagIndex(tag.fullPath);
        if (tagIndex === 0) {
          this.menuList["closeLeft"].show = false;
        } else {
          this.menuList["closeLeft"].show = true;
        }
        if (tagIndex === this.visitedViews.length - 1) {
          this.menuList["closeRight"].show = false;
        } else {
          this.menuList["closeRight"].show = true;
        }
      }
      e.preventDefault();
      e.stopPropagation();
      this.selectedTag = tag;
      this.openMenu(e);
    },
    openMenu(e) {
      const menuMinWidth = 105;
      const offsetLeft = this.$el.getBoundingClientRect().left; // container margin left
      const offsetWidth = this.$el.offsetWidth; // container width
      const maxLeft = offsetWidth - menuMinWidth; // left boundary
      const left = e.clientX - offsetLeft + 15; // 15: margin right

      if (left > maxLeft) {
        this.left = maxLeft;
      } else {
        this.left = left;
      }
      this.left = this.left + this.layoutModeLeft;

      this.top = e.clientY;
      this.visible = true;
    },
    closeMenu() {
      this.visible = false;
    },
    moveToCurrentTag() {
      const tags = this.$refs.tag;
      this.$nextTick(() => {
        const index = this.findTagIndex(this.activePage);
        if (index != -1) {
          this.$refs.scrollPane.moveToTarget(tags[index]);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.frame-tabs {
  padding: 16px 24px 0;
  @include themify() {
    background-color: themed("$frameContainerBg");
  }
  /* ::v-deep .el-tabs__item {
    height: 29px;
    line-height: 29px;
  }
  ::v-deep {
    .el-tabs__nav-wrap {
      width: 100%;
    }
    .el-tabs__nav-next,
    .el-tabs__nav-prev {
      height: 29px;
      line-height: 29px;
    }
  } */
  .tags-view-item {
    display: inline-block;
    margin-right: 8px;
    padding: 7px 24px;
    border: solid 1px #fcfcfc;
    border-radius: 4px 4px 0 0;
    transform: translateY(-1px);
    transition: transform 0.3s ease;
    @include themify() {
      background-color: themed("$--color-white");
      color: themed("$--color-text-secondary");
      // border-color: themed("");
      &.active {
        color: themed("$--color-primary");
      }
    }
    &.active {
      transform: translateY(0px);
      font-weight: 700;
    }
    .el-icon-close {
      font-size: 12px;
      &:hover {
        font-weight: 700;
      }
    }
  }
  .tagsview-menu {
    transform-origin: center top;
    z-index: 2020;
    position: absolute;
  }
}
</style>
