<template>
  <el-drawer
    class="setting-drawer--wrap"
    :visible="showSettings"
    :before-close="toggleShowSettings"
    :append-to-body="true"
    :lock-scroll="true"
    size="320px"
  >
    <template slot="title"
      ><span class="setting-drawer-title">系统设置</span>
    </template>
    <div v-if="!singleLayout">
      <p class="setting-drawer-block">导航模式</p>
      <div class="setting-drawer-block">
        <el-tooltip content="侧边栏导航" placement="top">
          <div
            class="setting-drawer-index-item"
            @click="handleLayout('sidemenu')"
          >
            <img
              src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTIiIGhlaWdodD0iNDUiIHZpZXdCb3g9IjAgMCA1MiA0NSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+PGRlZnM+PGZpbHRlciB4PSItOS40JSIgeT0iLTYuMiUiIHdpZHRoPSIxMTguOCUiIGhlaWdodD0iMTIyLjUlIiBmaWx0ZXJVbml0cz0ib2JqZWN0Qm91bmRpbmdCb3giIGlkPSJhIj48ZmVPZmZzZXQgZHk9IjEiIGluPSJTb3VyY2VBbHBoYSIgcmVzdWx0PSJzaGFkb3dPZmZzZXRPdXRlcjEiLz48ZmVHYXVzc2lhbkJsdXIgc3RkRGV2aWF0aW9uPSIxIiBpbj0ic2hhZG93T2Zmc2V0T3V0ZXIxIiByZXN1bHQ9InNoYWRvd0JsdXJPdXRlcjEiLz48ZmVDb2xvck1hdHJpeCB2YWx1ZXM9IjAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAuMTUgMCIgaW49InNoYWRvd0JsdXJPdXRlcjEiIHJlc3VsdD0ic2hhZG93TWF0cml4T3V0ZXIxIi8+PGZlTWVyZ2U+PGZlTWVyZ2VOb2RlIGluPSJzaGFkb3dNYXRyaXhPdXRlcjEiLz48ZmVNZXJnZU5vZGUgaW49IlNvdXJjZUdyYXBoaWMiLz48L2ZlTWVyZ2U+PC9maWx0ZXI+PHJlY3QgaWQ9ImIiIHdpZHRoPSI0OCIgaGVpZ2h0PSI0MCIgcng9IjQiLz48ZmlsdGVyIHg9Ii00LjIlIiB5PSItMi41JSIgd2lkdGg9IjEwOC4zJSIgaGVpZ2h0PSIxMTAlIiBmaWx0ZXJVbml0cz0ib2JqZWN0Qm91bmRpbmdCb3giIGlkPSJjIj48ZmVPZmZzZXQgZHk9IjEiIGluPSJTb3VyY2VBbHBoYSIgcmVzdWx0PSJzaGFkb3dPZmZzZXRPdXRlcjEiLz48ZmVHYXVzc2lhbkJsdXIgc3RkRGV2aWF0aW9uPSIuNSIgaW49InNoYWRvd09mZnNldE91dGVyMSIgcmVzdWx0PSJzaGFkb3dCbHVyT3V0ZXIxIi8+PGZlQ29sb3JNYXRyaXggdmFsdWVzPSIwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwLjEgMCIgaW49InNoYWRvd0JsdXJPdXRlcjEiLz48L2ZpbHRlcj48L2RlZnM+PGcgZmlsdGVyPSJ1cmwoI2EpIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgyIDEpIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxtYXNrIGlkPSJkIiBmaWxsPSIjZmZmIj48dXNlIHhsaW5rOmhyZWY9IiNiIi8+PC9tYXNrPjx1c2UgZmlsbD0iIzAwMCIgZmlsdGVyPSJ1cmwoI2MpIiB4bGluazpocmVmPSIjYiIvPjx1c2UgZmlsbD0iI0YwRjJGNSIgeGxpbms6aHJlZj0iI2IiLz48cGF0aCBmaWxsPSIjRkZGIiBtYXNrPSJ1cmwoI2QpIiBkPSJNMCAwaDQ4djEwSDB6Ii8+PHBhdGggZmlsbD0iIzMwMzY0OCIgbWFzaz0idXJsKCNkKSIgZD0iTTAgMGgxNnY0MEgweiIvPjwvZz48L3N2Zz4="
              alt="侧边栏导航"
            />
            <div
              class="setting-drawer-index-selectIcon"
              v-if="layoutMode === 'sidemenu'"
            >
              <base-icon icon-class="el-icon-check" />
            </div>
          </div>
        </el-tooltip>
        <el-tooltip content="顶部栏导航" placement="top">
          <div
            class="setting-drawer-index-item"
            @click="handleLayout('topmenu')"
          >
            <img
              src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTIiIGhlaWdodD0iNDUiIHZpZXdCb3g9IjAgMCA1MiA0NSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+PGRlZnM+PGZpbHRlciB4PSItOS40JSIgeT0iLTYuMiUiIHdpZHRoPSIxMTguOCUiIGhlaWdodD0iMTIyLjUlIiBmaWx0ZXJVbml0cz0ib2JqZWN0Qm91bmRpbmdCb3giIGlkPSJhIj48ZmVPZmZzZXQgZHk9IjEiIGluPSJTb3VyY2VBbHBoYSIgcmVzdWx0PSJzaGFkb3dPZmZzZXRPdXRlcjEiLz48ZmVHYXVzc2lhbkJsdXIgc3RkRGV2aWF0aW9uPSIxIiBpbj0ic2hhZG93T2Zmc2V0T3V0ZXIxIiByZXN1bHQ9InNoYWRvd0JsdXJPdXRlcjEiLz48ZmVDb2xvck1hdHJpeCB2YWx1ZXM9IjAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAuMTUgMCIgaW49InNoYWRvd0JsdXJPdXRlcjEiIHJlc3VsdD0ic2hhZG93TWF0cml4T3V0ZXIxIi8+PGZlTWVyZ2U+PGZlTWVyZ2VOb2RlIGluPSJzaGFkb3dNYXRyaXhPdXRlcjEiLz48ZmVNZXJnZU5vZGUgaW49IlNvdXJjZUdyYXBoaWMiLz48L2ZlTWVyZ2U+PC9maWx0ZXI+PHJlY3QgaWQ9ImIiIHdpZHRoPSI0OCIgaGVpZ2h0PSI0MCIgcng9IjQiLz48ZmlsdGVyIHg9Ii00LjIlIiB5PSItMi41JSIgd2lkdGg9IjEwOC4zJSIgaGVpZ2h0PSIxMTAlIiBmaWx0ZXJVbml0cz0ib2JqZWN0Qm91bmRpbmdCb3giIGlkPSJjIj48ZmVPZmZzZXQgZHk9IjEiIGluPSJTb3VyY2VBbHBoYSIgcmVzdWx0PSJzaGFkb3dPZmZzZXRPdXRlcjEiLz48ZmVHYXVzc2lhbkJsdXIgc3RkRGV2aWF0aW9uPSIuNSIgaW49InNoYWRvd09mZnNldE91dGVyMSIgcmVzdWx0PSJzaGFkb3dCbHVyT3V0ZXIxIi8+PGZlQ29sb3JNYXRyaXggdmFsdWVzPSIwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwLjEgMCIgaW49InNoYWRvd0JsdXJPdXRlcjEiLz48L2ZpbHRlcj48L2RlZnM+PGcgZmlsdGVyPSJ1cmwoI2EpIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSgyIDEpIiBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxtYXNrIGlkPSJkIiBmaWxsPSIjZmZmIj48dXNlIHhsaW5rOmhyZWY9IiNiIi8+PC9tYXNrPjx1c2UgZmlsbD0iIzAwMCIgZmlsdGVyPSJ1cmwoI2MpIiB4bGluazpocmVmPSIjYiIvPjx1c2UgZmlsbD0iI0YwRjJGNSIgeGxpbms6aHJlZj0iI2IiLz48cGF0aCBmaWxsPSIjMzAzNjQ4IiBtYXNrPSJ1cmwoI2QpIiBkPSJNMCAwaDQ4djEwSDB6Ii8+PC9nPjwvc3ZnPg=="
              alt="顶部栏导航"
            />
            <div
              class="setting-drawer-index-selectIcon"
              v-if="layoutMode !== 'sidemenu'"
            >
              <base-icon icon-class="el-icon-check" />
            </div>
          </div>
        </el-tooltip>
      </div>
      <el-divider class="setting-drawer-divider"></el-divider>
    </div>
    <div class="setting-drawer-item clearfix" v-if="false">
      <span>固定 Header</span>
      <el-switch
        v-model="isFixedHeader"
        class="setting-drawer-switch"
      ></el-switch>
    </div>
    <el-tooltip
      content="侧边菜单布局时可配置"
      placement="left"
      :disabled="layoutMode === 'sidemenu'"
      v-if="!singleLayout || (singleLayout && layoutMode === 'sidemenu')"
    >
      <div class="setting-drawer-item clearfix" v-if="false">
        <span :style="{ opacity: layoutMode !== 'sidemenu' ? 0.5 : null }"
          >固定侧边菜单</span
        >
        <el-switch
          v-model="fixedAside"
          :disabled="layoutMode !== 'sidemenu'"
          class="setting-drawer-switch"
        ></el-switch>
      </div>
    </el-tooltip>
    <el-tooltip
      content="侧边菜单布局时可配置"
      placement="left"
      :disabled="layoutMode === 'sidemenu'"
      v-if="!singleLayout || (singleLayout && layoutMode === 'sidemenu')"
    >
      <div class="setting-drawer-item clearfix">
        <span :style="{ opacity: layoutMode !== 'sidemenu' ? 0.5 : null }"
          >折叠侧边菜单</span
        >
        <el-switch
          v-model="collapseAside"
          :disabled="layoutMode !== 'sidemenu'"
          class="setting-drawer-switch"
        ></el-switch>
      </div>
    </el-tooltip>
    <template v-if="themeList.length > 1">
      <el-divider class="setting-drawer-divider"></el-divider>
      <p class="setting-drawer-block">主题设置</p>
      <el-form label-position="left" label-width="80px" class="theme-form">
        <el-form-item label="主题名称">
          <el-select v-model="currentThemeName" placeholder="请选择">
            <el-option
              v-for="(item, index) in themeList"
              :key="index"
              :label="item"
              :value="item"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="echarts主题">
          <el-select v-model="currentEchartsThemeName" placeholder="请选择">
            <el-option
              v-for="(item, index) in echartsThemeList"
              :key="index"
              :label="item"
              :value="item"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </template>

    <el-button
      v-if="false"
      type="primary"
      class="handle-button"
      :style="{ top: buttonTop + 'px' }"
      @click.stop.prevent="toggleShowSettings"
    >
      <i :class="showSettings ? 'el-icon-close' : 'el-icon-setting'" />
    </el-button>
  </el-drawer>
</template>

<script>
import debounce from "lodash/debounce";
import { mapGetters, mapActions } from "vuex";
import { themeList, echartsThemeList } from "@/utils/theme";
import { toggleClass } from "@/utils/index";
export default {
  name: "Settings",
  props: {
    buttonTop: {
      default: 250,
      type: Number,
    },
  },
  data() {
    return {
      loadedThemes: [],
    };
  },
  computed: {
    ...mapGetters([
      "layoutMode",
      "singleLayout",
      "sidebar",
      "fixedHeader",
      "showSettings",
      "themeName",
      "echartsThemeName",
    ]),
    isFixedHeader: {
      get() {
        return this.fixedHeader;
      },
      set() {
        this["settings/toggleFixedHeader"]();
      },
    },
    fixedAside: {
      get() {
        return this.sidebar.fixed;
      },
      set() {
        this["settings/toggleSidebarFixed"]();
      },
    },
    collapseAside: {
      get() {
        return this.sidebar.collapse;
      },
      set() {
        this["settings/toggleSidebarCollapse"]();
      },
    },
    themeList: function () {
      return themeList;
    },
    currentThemeName: {
      get() {
        return this.themeName;
      },
      set(val) {
        this["settings/toggleThemeName"](val);
      },
    },
    echartsThemeList: function () {
      return echartsThemeList;
    },
    currentEchartsThemeName: {
      get() {
        return this.echartsThemeName;
      },
      set(val) {
        this["settings/toggleEchartsThemeName"](val);
      },
    },
  },
  watch: {
    currentThemeName(newValue, oldValue) {
      this.toggleBodyClass(newValue, oldValue);
    },
  },
  mounted() {
    this.loadedThemes.push(this.currentThemeName);
  },
  methods: {
    ...mapActions([
      "settings/toggleShowSettings",
      "settings/toggleLayoutMode",
      "settings/toggleSidebarCollapse",
      "settings/toggleSidebarFixed",
      "settings/toggleFixedHeader",
      "settings/toggleThemeName",
      "settings/toggleEchartsThemeName",
    ]),
    toggleShowSettings: debounce(function () {
      this["settings/toggleShowSettings"]();
    }, 250),
    handleLayout(layout) {
      this["settings/toggleLayoutMode"](layout);
    },
    async toggleBodyClass(newValue, oldValue) {
      if (!this.loadedThemes.includes(newValue)) {
        this.loadedThemes.push(newValue);
        await import(`../../../themes/theme-css/theme-${newValue}.css`);
      }
      toggleClass(document.body, "custom-theme-" + oldValue);
      toggleClass(document.body, "custom-theme-" + newValue);
      const html = document.getElementsByTagName("html")[0];
      toggleClass(html, "custom-theme-" + oldValue);
      toggleClass(html, "custom-theme-" + newValue);
    },
  },
};
</script>

<style lang="scss" scoped>
.setting-drawer--wrap {
  .setting-drawer-title {
    @include themify() {
      color: themed("$--color-text-primary");
      font-weight: 700;
    }
  }

  & ::v-deep {
    .el-drawer__header {
      margin-bottom: 16px;
      padding: 20px 12px 0 12px;
    }
    @include themify() {
      .el-drawer__close-btn {
        color: themed("$--color-text-primary");
      }
      .el-drawer__body {
        border-top: themed("$--border-base");
      }
    }
    .el-drawer__body {
      overflow: auto;
    }
  }

  .setting-drawer-block {
    padding: 0 12px;
    font-weight: 500;
  }

  .setting-drawer-divider {
    margin: 12px 0;
  }

  .setting-drawer-index-item {
    display: inline-block;
    position: relative;
    margin-right: 16px;
    border-radius: 4px;
    cursor: pointer;

    img,
    .img {
      width: 48px;
      height: 42px;
    }

    .setting-drawer-index-selectIcon {
      position: absolute;
      top: 0;
      right: 0;
      width: 100%;
      padding-top: 15px;
      padding-left: 24px;
      height: 100%;
      @include themify() {
        color: themed("$--color-primary");
      }
      font-size: 13px;
      font-weight: 700;
      > i {
        font-weight: bold;
      }
    }
  }

  .setting-drawer-item {
    padding: 12px;
    @include themify() {
      > span {
        line-height: themed("$--switch-height");
        height: themed("$--switch-height");
      }
    }
  }

  .setting-drawer-switch {
    float: right;
  }

  .theme-form ::v-deep {
    .el-form-item {
      padding: 0 12px;
      font-size: inherit;
      .el-form-item__label,
      .el-form-item__content {
        font-size: inherit;
      }
    }
  }

  .handle-button {
    width: 48px;
    height: 48px;
    position: absolute;
    left: -48px;
    text-align: center;
    font-size: 24px;
    border-radius: 6px 0 0 6px !important;
    z-index: 0;
    padding: 0;
    line-height: 48px;
    i {
      font-size: 24px;
      line-height: 48px;
    }
  }
}
</style>
