<template>
  <div class="frame-without">
    <router-view :key="key"></router-view>
  </div>
</template>

<script>
export default {
  name: "without",
  computed: {
    key() {
      return this.$route.fullPath;
    },
  },
  mounted() {
    this.updateStyle();
  },
  methods: {
    updateStyle() {
      this.$nextTick(() => {
        this.$store.commit("settings/UPDATE_FRAMESTYLE", "0px");
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.frame-without {
  position: relative;
  width: 100%;
  min-height: 100vh;
  padding: 0px;
  overflow-x: hidden;
  ::v-deep {
    .el-loading-mask {
      z-index: auto;
    }
    > *:first-child:not([class~="full-main"]) {
      padding: 8px;
    }
    > .full-main {
      padding: 0;
    }
  }
}
</style>
