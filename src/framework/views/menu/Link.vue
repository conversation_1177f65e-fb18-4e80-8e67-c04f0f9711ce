<template>
  <router-link v-bind="linkProps(to)">
    <slot />
  </router-link>
</template>

<script>
import { isExternal } from "@/utils/validate";
import { mapGetters } from "vuex";
import qs from "qs";

export default {
  name: "AppLink",
  props: {
    to: {
      type: String,
      required: true,
    },
  },
  computed: {
    ...mapGetters(["token"]),
    qs() {
      return qs;
    },
  },
  methods: {
    linkProps(url) {
      if (isExternal(url)) {
        return {
          is: "a",
          href:
            url.indexOf("?") !== -1
              ? `${url}&token=${
                  this.token
                }&globalUniqueID=${sessionStorage.getItem("globalUniqueID")}`
              : `${url}?token=${
                  this.token
                }&globalUniqueID=${sessionStorage.getItem("globalUniqueID")}`,
          target: "_blank",
          rel: "noopener",
        };
      }
      const [path, querys = ""] = url.split(/\/\?|\?/);
      const query =
        querys !== "" && querys !== "undefined" ? this.qs.parse(querys) : {};
      const { needToken = 0 } = query;
      if (needToken == 0) {
        return {
          is: "router-link",
          to: { path: path, query },
        };
      } else {
        this.$delete(query, "needToken");
        return {
          is: "router-link",
          to: { path: path, query: { ...query, token: this.token } },
        };
      }
    },
  },
};
</script>
