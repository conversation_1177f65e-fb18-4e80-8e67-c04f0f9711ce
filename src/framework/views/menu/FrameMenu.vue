<template>
  <el-menu
    :default-active="activeMenu"
    :collapse="isCollapse"
    :unique-opened="false"
    :collapse-transition="false"
    :mode="mode"
    v-if="themeVars"
    :background-color="themeVars.menuBg"
    :text-color="themeVars.menuText"
    :active-text-color="themeVars.menuActiveText"
    class="frame-menu"
  >
    <!-- v-if="variables"
    :background-color="variables.menuBg"
    :text-color="variables.menuText"
    :active-text-color="variables.menuActiveText" -->
    <menu-item
      v-for="route in permission_routes"
      :key="route.path"
      :item="route"
      :base-path="route.path"
      ref="list"
    />
  </el-menu>
</template>

<script>
import MenuItem from "./MenuItem";
import { mapGetters } from "vuex";

export default {
  name: "FrameMenu",
  components: {
    MenuItem,
  },
  props: {
    mode: {
      type: String,
      default: "vertical",
      required: false,
    },
    isCollapse: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  computed: {
    ...mapGetters(["permission_routes", "themeName", "themeVars"]),
    activeMenu() {
      const route = this.$route;
      const { meta, path } = route;
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu;
      }
      return path;
    },
  },
};
</script>

<style lang="scss" scoped>
@include themify("!&") {
  .frame-menu.el-menu--collapse {
    ::v-deep .el-submenu.is-active {
      .frame-menu-icon-item {
        color: themed("$frameMenu-text-color-active");
      }
    }
  }

  .frame-menu ::v-deep > .menu-wrapper {
    > a > .el-menu-item,
    .el-submenu > .el-submenu__title {
      font-weight: bold;
    }
    > {
      .el-submenu .el-menu-item {
        &.is-active > span {
          padding-left: 26px;
        }
        > span {
          padding-left: 30px;
        }
        &.is-active .frame-menu-icon-item + span {
          padding-left: 2px;
        }
        .frame-menu-icon-item + span {
          padding-left: 6px;
        }
      }
    }
  }
}
</style>

<style lang="scss">
.frame-menu {
  border: none;
  height: 100%;
  width: 100% !important;
  white-space: nowrap;
  &.el-menu--horizontal {
    border-bottom: none !important;
    > .menu-wrapper {
      display: inline-block;
      > a {
        // display: inline-block;
        > .el-menu-item {
          display: inline-block;
          margin: 0;
          border-bottom: 2px solid transparent;
          padding: 0 5px;
          @include themify() {
            color: themed("$frameMenu-text-color");
          }
        }
      }
      > .el-submenu {
        display: inline-block;
        .el-submenu__title {
          border-bottom: 2px solid transparent;
          padding: 0 5px;
          @include themify() {
            color: themed("$frameMenu-text-color");
          }

          // &:hover {
          //   background-color: #fff;
          // }
        }
        .el-submenu__icon-arrow {
          position: static;
          vertical-align: middle;
          margin-left: 8px;
          margin-top: -3px;
        }
      }
    }
  }
  &.el-menu--collapse > .menu-wrapper {
    > a > .el-menu-item span,
    > .el-submenu > .el-submenu__title span {
      height: 0;
      width: 0;
      overflow: hidden;
      visibility: hidden;
      display: inline-block;
    }
    > a > .el-menu-item .el-submenu__icon-arrow,
    > .el-submenu > .el-submenu__title .el-submenu__icon-arrow {
      display: none;
    }
  }
  .el-submenu.is-active > .el-submenu__title {
    @include themify() {
      color: themed("$frameMenu-text-color-active") !important;
    }
  }
  .el-submenu .menu-wrapper:last-child {
    .el-menu-item {
      border-bottom: none;
    }
  }
  .el-submenu .menu-wrapper:first-child {
    .el-menu-item {
      @include themify() {
        border-top: 1px solid themed("$--border-color-light");
      }
    }
  }
  .el-submenu .el-menu {
    overflow: hidden;
  }
}
.el-menu--horizontal {
  .el-submenu__title,
  .el-menu-item {
    @include themify() {
      height: 50px;
      line-height: 50px;
    }
  }
  @include themify() {
    & .el-menu {
      & .el-menu-item.is-active,
      & .el-submenu.is-active > .el-submenu__title {
        color: themed("$frameMenu-text-color-active") !important;
      }
    }
    & .el-menu-item:not(.is-disabled):hover,
    & .el-menu-item:not(.is-disabled):focus {
      color: themed("$frameMenu-text-color-active") !important;
    }
  }
}
.el-submenu,
.el-menu-item {
  font-size: 15px !important;
  .frame-menu-icon-item {
    vertical-align: middle;
    margin-right: 7px;
    margin-left: 3px;
    width: 24px;
    text-align: center;
    font-size: 32px;
    color: inherit !important;
    &.svg-icon {
      width: 32px;
      margin-right: 14px;
      vertical-align: middle;
    }
    &:not(.svg-icon) {
      font-size: 32px;
      margin-left: 2px;
      margin-right: 20px;
    }
  }
  .el-menu--collapse & .frame-menu-icon-item {
    margin-right: 2px;
  }
}
.el-submenu__title {
  font-size: 15px !important;
}
@include themify("!&") {
  .el-menu--horizontal .el-submenu.is-active {
    .el-submenu__title .frame-menu-icon-item {
      color: themed("$frameMenu-text-color-active");
    }
  }
  .el-scrollbar__thumb {
    background-color: themed("$--color-primary") !important;
  }
  .el-menu--vertical .el-submenu.is-active {
    .el-submenu__title {
      color: themed("$frameMenu-text-color-active") !important;
      .frame-menu-icon-item {
        color: inherit;
      }
    }
  }
  .el-menu-item,
  .el-submenu {
    border-bottom: 1px solid themed("$--border-color-light");
  }
  .el-menu-item.is-active {
    border-left: 4px solid themed("$--color-primary");
    // padding-left: 34px !important;
    .frame-menu-icon-item {
      margin-left: -1px;
    }
  }
}
.el-menu--vertical {
  max-height: 100vh;
  overflow-y: auto;
  > .el-menu--popup-right-start {
    margin-right: 0px;
    margin-left: 7px;
    @include themify() {
      box-shadow: themed("$--box-shadow-base");
    }
  }
}
</style>
