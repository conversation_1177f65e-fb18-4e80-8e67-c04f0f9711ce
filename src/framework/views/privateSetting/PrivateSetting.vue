<template>
  <div>
    <el-card class="passWord">
      <el-form
        :model="ruleForm"
        status-icon
        :rules="rules"
        ref="ruleForm"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item label="原密码" prop="oldpass">
          <el-input
            type="password"
            v-model.trim="ruleForm.oldpass"
            autocomplete="off"
            class="pass"
          ></el-input>
        </el-form-item>
        <el-form-item label="新密码" prop="pass">
          <el-input
            type="password"
            v-model.trim="ruleForm.pass"
            autocomplete="off"
            class="pass"
          ></el-input>
        </el-form-item>
        <el-form-item label="确认密码" prop="checkPass">
          <el-input
            type="password"
            v-model.trim="ruleForm.checkPass"
            autocomplete="off"
            class="pass"
          ></el-input>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitForm('ruleForm')"
            >提交
          </el-button>
          <el-button @click="resetForm('ruleForm')">重填</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>
<script>
import { apiPass } from "./api/api";
import { apiLogout } from "./api/logout";
import md5 from "md5";
import { sm3 } from "sm-crypto";

export default {
  data() {
    let validatePass = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入新密码"));
      } else {
        let reg = /^(?=.*[A-Za-z])(?=.*\d)(?=.*[$@$!%*#?&])[A-Za-z\d$@$!%*#?&]{8,}$/;
        let str = value.trim();
        if (str == this.ruleForm.oldpass) {
          callback(new Error("新密码不能与原密码相同"));
        }
        if (str != "" && !reg.test(str)) {
          // this.$message({
          //   message: "密码必须由至少8位的字母、数字、特殊字符组成",
          //   type: "warning",
          // });
          callback(new Error("密码必须由至少8位的字母、数字、特殊字符组成"));
        } else {
          if (this.ruleForm.checkPass !== "") {
            this.$refs.ruleForm.validateField("checkPass");
          }
          callback();
        }
      }
    };
    let validatePass2 = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入确认密码"));
      } else if (value !== this.ruleForm.pass) {
        callback(new Error("两次输入密码不一致!"));
      } else {
        callback();
      }
    };
    return {
      ruleForm: {
        oldpass: "",
        pass: "",
        checkPass: "",
      },
      rules: {
        oldpass: [{ required: true, message: "请输入原密码", trigger: "blur" }],
        pass: [{ required: true, validator: validatePass, trigger: "blur" }],
        checkPass: [
          { required: true, validator: validatePass2, trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    onSubmit() {},
    submitForm(formName) {
      let that = this;
      that.$refs[formName].validate(valid => {
        if (valid) {
          let params = {
            oldPwd: sm3(that.ruleForm.oldpass),
            oldPwd2: md5(that.ruleForm.oldpass),
            newPwd: sm3(that.ruleForm.pass),
          };
          apiPass(params)
            .then(result => {
              console.log(result);
              if (result.status == 0) {
                that.$message({
                  message: "恭喜！修改密码成功，请重新登录！",
                  type: "success",
                });
                that.$refs[formName].resetFields();
                this.logout();
              }
            })
            .catch(result => {
              if (result.status == 101) {
                that.$message({
                  message: "原密码输入有误!",
                  type: "error",
                });
              }
            });
        } else {
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },

    //退出登录
    logout() {
      console.log(1111111111);
      apiLogout()
        .then(res => {
          console.log(res);
          this.$router.replace({ name: "Login" });
          this.$store.dispatch("user/logout");
        })
        .catch(e => {
          console.log(e);
          this.$router.replace({ name: "Login" });
          this.$store.dispatch("user/logout");
        });
    },
  },
};
</script>
<style lang="scss" scoped>
.passWord {
  // width: 50vw;
  height: 50vh;
}

.pass {
  width: 200px;
}
</style>
