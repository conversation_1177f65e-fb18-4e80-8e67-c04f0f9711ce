import axios from "@/utils/request";

export function upnodeFun(url, parameter) {
  return axios({
    url: url,
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    data: parameter,
  });
}
export function handleNode(url, parameter) {
  return axios({
    url: url,
    method: "post",
    data: parameter,
  });
}
let urllist = {
  // passUrl: "/framework/personal/modifypassword", //修改密码地址
  passUrl: "/framework/sysmanage/users/modifypwd", //修改密码地址
};
const apiPass = params => upnodeFun(urllist.passUrl, params);

export { apiPass };
