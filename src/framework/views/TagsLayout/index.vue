<template>
  <div class="frame-tags-layout">
    <tab-view></tab-view>
    <div class="frame-tags-layout-wrap">
      <keep-alive>
        <router-view :key="key" v-if="!$route.meta.noCache" />
      </keep-alive>
      <router-view :key="key" v-if="$route.meta.noCache"></router-view>
    </div>
  </div>
</template>

<script>
import TabView from "../TabView/TabView";

export default {
  name: "TagsLayout",
  components: {
    TabView,
  },
  computed: {
    key() {
      return this.$route.fullPath;
    },
  },
  mounted() {
    this.updateStyle();
  },
  methods: {
    updateStyle() {
      this.$nextTick(() => {
        this.$store.commit("settings/UPDATE_FRAMESTYLE", "32px");
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.frame-tags-layout {
  position: relative;
  ::v-deep {
    .frame-tabs {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      z-index: 2000;
      .el-tabs__header {
        margin: 0 0;
      }
    }
  }
  &-wrap {
    margin-top: 32px;
    ::v-deep {
      .el-loading-mask {
        z-index: auto;
      }
      > *:first-child:not([class~="full-main"]) {
        padding: 8px;
      }
      > .full-main {
        padding: 0;
      }
    }
  }
}
</style>
