<template>
  <div class="login-page" :style="loginPageStyle">
    <el-form
      :model="loginForm"
      :rules="loginRules"
      ref="loginForm"
      label-position="left"
      label-width="0px"
      size="medium"
      class="login-container"
    >
      <h3 class="title">{{ settings.title }}</h3>
      <el-form-item prop="account">
        <el-input
          type="text"
          v-model.trim="loginForm.account"
          auto-complete="off"
          placeholder="用户名"
        ></el-input>
      </el-form-item>
      <el-form-item prop="checkPass">
        <el-input
          type="password"
          v-model.trim="loginForm.checkPass"
          auto-complete="off"
          placeholder="密码"
          :show-password="false"
        ></el-input>
      </el-form-item>
      <el-form-item
        prop="verifyCode"
        v-show="requireVerifyCode"
        class="form-item-code clearfix"
      >
        <el-input
          v-model.trim="loginForm.verifyCode"
          placeholder="验证码"
          class="code-input"
        ></el-input>
        <el-tooltip
          class="item"
          effect="dark"
          content="点击更换图片"
          placement="right-start"
        >
          <img
            :src="'data:image/png;base64,' + codeIco"
            class="image-code"
            @click="getCode()"
          />
        </el-tooltip>
      </el-form-item>
      <el-form-item prop="isRemember">
        <el-checkbox v-model="loginForm.isRemember">记住密码</el-checkbox>
      </el-form-item>
      <el-form-item class="form-item-btn">
        <el-button
          type="primary"
          style="width: 100%"
          class="btn-login"
          @click.native.prevent="handleSubmit"
          :loading="logining"
          round
          >登录</el-button
        >
      </el-form-item>
    </el-form>
    <div class="login-logo">
      <svg-icon :icon-class="settings.logoMini"></svg-icon>
      <span>{{ settings.titleEn }}</span>
    </div>
    <div class="login-tempo"></div>
    <div class="login-dot"></div>
    <div class="login-org">{{ settings.copyright }}</div>
  </div>
</template>
<script>
import { Message } from "element-ui";
import settings from "@/settings";
import axios from "axios";
const { loginBgUrl = "" } = settings;
let loginBg = typeof loginBgUrl == "function" ? loginBgUrl() : null;

export default {
  data() {
    return {
      codeIco: "",
      logining: false,
      loginForm: {
        account: "",
        checkPass: "",
        verifyCode: "",
        rnd: "",
        isRemember: false,
      },
      loginRules: {
        account: [{ required: true, message: "请输入账号", trigger: "blur" }],
        checkPass: [{ required: true, message: "请输入密码", trigger: "blur" }],
        // verifyCode: [{ validator: this.checkCode, trigger: "blur" }],
      },
      isShowCountdown: false,
      second: 60,
      timer: null,
      notifyCase: null,
      requireVerifyCode: false,
    };
  },
  computed: {
    settings() {
      return settings;
    },
    loginBg() {
      return loginBg;
    },
    loginPageStyle() {
      if (this.loginBg) {
        return { backgroundImage: "url(" + this.loginBg + ")" };
      } else {
        return {};
      }
    },
  },
  mounted() {
    this.getCode();
  },
  watch: {
    "notifyCase.closed"(newValue) {
      if (newValue) {
        sessionStorage.removeItem("vuex");
      }
    },
  },
  beforeRouteEnter(to, from, next) {
    next(vm => {
      if (to.params.noActive) {
        vm.notifyCase = vm.$notify({
          title: "长期未操作而自动注销",
          message:
            "由于您长时间未做任何操作，为保障安全，系统自动注销了您的帐户，请重新登录。",
          type: "warning",
          duration: 0,
          showClose: true,
        });
      }
    });
  },
  async created() {
    let info =
      localStorage.getItem("info") &&
      JSON.parse(decodeURIComponent(atob(localStorage.getItem("info"))));
    if (info && info["isRemember"]) {
      this.loginForm.account = info.account;
      this.loginForm.checkPass = info.checkPass;
      this.loginForm.isRemember = info.isRemember;
    }
    document.addEventListener("keydown", this.keyEnter, false);
    window.addEventListener("beforeunload", this.updateHandler, true);
  },
  beforeDestroy() {
    document.removeEventListener("keydown", this.keyEnter, false);
    window.removeEventListener("beforeunload", this.updateHandler, true);
  },
  methods: {
    updateHandler() {
      this.notifyCase && this.notifyCase.close();
    },
    checkCode(rule, value, callback) {
      if (!value) {
        return callback(new Error("请输入验证码"));
      } else if (this.loginForm.verifyCode === "") {
        return callback(new Error("请先获取验证码"));
      }
      if (value != this.loginForm.verifyCode) {
        return callback(new Error("验证码输入错误"));
      } else {
        callback();
      }
    },
    getCode() {
      let url = "/login/verifycode";
      if (settings.axiosBasePath) {
        url = settings.axiosBasePath + url;
      }
      axios
        .get(url, {
          params: {
            id: 5,
          },
        })
        .then(res => {
          if (res.data.data.requireVerifyCode === "false") {
            this.requireVerifyCode = false;
          } else {
            this.requireVerifyCode = true;
            this.codeIco = res.data.data.image;
            this.loginForm.rnd = res.data.data.rnd;
          }
        })
        .catch(() => {
          console.log("获取验证码失败");
        });
    },
    handleSubmit() {
      let self = this;
      let isLoginSuccess = false;
      self.$refs.loginForm.validate(valid => {
        if (valid) {
          self.logining = true;
          self.$store
            .dispatch("user/login", self.loginForm)
            .then(res => {
              if (
                res.status !== "Succeed" &&
                res.status !== "PasswordExpired"
              ) {
                Message({
                  message: res.msg || "error",
                  type: "error",
                  duration: 5 * 1000,
                });
              } else {
                self.$notify({
                  title: "登录成功",
                  message: "欢迎回来",
                  type: "success",
                  offset: 50,
                });
                self.$router.replace({ path: "/home" });
              }
              isLoginSuccess = true;
            })
            .catch(err => {
              if (err.status === "VerifyCodeError") {
                Message({
                  message: err.msg || "error",
                  type: "error",
                  duration: 5 * 1000,
                });
                this.getCode();
                this.loginForm.verifyCode = "";
              } else if (err.status === "Mismatching") {
                self.$message({
                  message: err.msg,
                  type: "error",
                  showClose: true,
                });
              } else if (err.status === "PasswordExpired") {
                self.$notify({
                  title: "密码过期",
                  message: "密码已过期,请修改密码!",
                  type: "warning",
                  offset: 50,
                });
                this.$router.replace({ name: "PersonalSet_ChangePwd" });
              }
            })
            .finally(() => {
              self.logining = false;
              if (self.loginForm.isRemember && isLoginSuccess) {
                localStorage.setItem(
                  "info",
                  btoa(encodeURIComponent(JSON.stringify(self.loginForm)))
                );
              } else {
                localStorage.setItem("info", "");
              }
              if (self.notifyCase) {
                self.notifyCase.close();
              }
            });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    keyEnter(e) {
      e = window.event || e;
      if (this.$route.name == "Login" && e.keyCode === 13) {
        this.handleSubmit();
      }
    },
  },
};
</script>

<style lang="scss">
.login-page {
  $--color-white: #fff;
  $--input-border-radius: 20px;
  position: relative;
  height: 100%;
  background-color: #333744;
  background-image: url("~@/assets/login/loginbg.jpg");
  background-repeat: no-repeat;
  background-position: center bottom;
  background-size: cover;
  .login-container {
    position: absolute;
    top: 50%;
    left: 50%;
    margin-left: -250px;
    margin-top: -200px;
    width: 500px;
    height: 420px;
    z-index: 2;
    .title {
      margin: 0px auto 80px;
      text-align: center;
      letter-spacing: 2px;
      height: 45px;
      line-height: 45px;
      font-size: 32px;
      font-weight: bold;
      color: #ebebf0;
    }
    .el-input {
      input {
        background: rgba(0, 0, 0, 0.15);
        border: 1px solid rgba(0, 0, 0, 0);
        border-radius: $--input-border-radius;
        color: $--color-white;
        &::placeholder {
          color: #cbd0dc;
          text-align: center;
        }
        &:focus {
          background: rgba(0, 0, 0, 0);
          @include themify() {
            border-color: themed("$--input-focus-border");
          }
          &::placeholder {
            text-align: left;
          }
        }
      }
    }
    .el-form-item {
      width: 300px;
      margin-left: auto;
      margin-right: auto;
      &.is-error {
        & .el-input__inner,
        & .el-textarea__inner {
          &,
          &:focus {
            @include themify() {
              border-color: themed("$--color-danger");
            }
          }
        }
      }
    }
    .el-checkbox {
      color: $--color-white;
    }
    .el-checkbox__input {
      &.is-focus {
        .el-checkbox__inner {
          @include themify() {
            border: themed("$--checkbox-input-border");
            &:hover {
              border-color: themed("$--color-primary");
            }
          }
        }
      }
      &.is-checked {
        .el-checkbox__inner {
          background: transparent;
          border-color: $--color-white !important;
        }
        + .el-checkbox__label {
          color: $--color-white;
        }
      }
    }
    .el-checkbox__inner {
      background: transparent;
      &::after {
        border-color: $--color-white;
      }
    }
    .form-item-code {
      .code-input {
        width: 50%;
        float: left;
      }
      .code-btn {
        width: 120px;
        float: right;
      }
    }
  }
  .login-logo {
    position: absolute;
    top: 6%;
    left: 6%;
    width: 190px;
    height: 60px;
    color: $--color-white;
    .svg-icon {
      width: 50px;
      height: 50px;
      vertical-align: middle;
    }
    span {
      display: inline-block;
      height: 60px;
      line-height: 60px;
      font-weight: 700;
      font-size: 21px;
      vertical-align: middle;
      margin-left: 8px;
    }
  }
  .login-tempo {
    position: absolute;
    top: 40%;
    left: 0;
    width: 100%;
    height: 400px;
    margin-top: -180px;
    background-repeat: no-repeat;
    background-position: 50% 30%;
  }
  .login-dot {
    width: 100%;
    height: 190px;
    position: absolute;
    left: 0;
    bottom: 5%;
    background-repeat: no-repeat;
    background-position: center;
    z-index: 1;
  }
  .login-org {
    position: absolute;
    left: 0;
    bottom: 6%;
    color: #bcc5d2;
    width: 100%;
    text-align: center;
    z-index: 1;
  }
  .image-code {
    width: 140px;
    height: 34px;
    margin-left: 10px;
  }
}
</style>
