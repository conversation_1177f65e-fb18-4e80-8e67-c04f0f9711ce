import { postJson, postJsonBlob } from "@/utils/axios";

const getLogAccess = data => postJson("framework/log/access/find", data);
const exportLogAccess = data =>
  postJsonBlob("framework/log/access/export", data);
const getLogLogin = data => postJson("framework/log/userLogin/find", data);
const exportLogLogin = data =>
  postJsonBlob("framework/log/userLogin/export", data);
const getLogOperator = data => postJson("framework/log/operator/find", data);
const exportLogOperator = data =>
  postJsonBlob("framework/log/operator/export", data);

export {
  getLogAccess,
  exportLogAccess,
  getLogLogin,
  exportLogLogin,
  getLogOperator,
  exportLogOperator,
};
