<template>
  <!-- 异常日志 -->
  <div class="ErrorLog" :style="vmStyle">
    <el-card style="height: 50px">
      <el-form :inline="true">
        <el-form-item label="发生时间:">
          <el-date-picker
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            v-model="queryParam.startTime"
            type="datetime"
            placeholder="请选择开始时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="-">
          <el-date-picker
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            v-model="queryParam.endTime"
            type="datetime"
            placeholder="请选择结束时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="URI:">
          <el-input
            placeholder="接口URI模糊匹配"
            style="width: 222px"
            clearable
            @clear="getList(true)"
            v-model.trim="queryParam.uri"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="日志内容:">
          <el-input
            placeholder="日志内容模糊匹配"
            style="width: 222px"
            clearable
            @clear="getList(true)"
            v-model.trim="queryParam.logInfo"
          >
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click="getList(true)"
          >
            查询
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-button
            :loading="exportLoading"
            type="primary"
            icon="export el-icon-download"
            @click="exportExcel()"
          >
            导出
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card :style="tbStyle" :body-style="{ height: 'calc(100% - 88px)' }">
      <div slot="header">
        <div class="listHeader">
          <span class="listHeader-title">异常日志详单</span>
        </div>
      </div>
      <el-table
        v-loading="loading"
        height="100%"
        ref="sysSettingTable"
        border
        :data="table.rows"
        stripe
      >
        <el-table-column type="index" label="序号" align="center" width="70">
          <template scope="scope">
            <span>{{
              (queryParam.pageIndex - 1) * queryParam.pageSize +
              scope.$index +
              1
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          label="用户名"
          min-width="100px"
          prop="userName"
        ></el-table-column>
        <el-table-column
          align="center"
          label="客户端IP"
          min-width="120px"
          prop="clientIp"
        ></el-table-column>
        <el-table-column
          align="center"
          label="浏览器名称"
          min-width="120px"
          prop="browserName"
        ></el-table-column>
        <el-table-column
          align="center"
          label="浏览器版本"
          min-width="120px"
          prop="browserVersion"
        ></el-table-column>
        <el-table-column
          label="接口URI"
          min-width="255px"
          prop="uri"
        ></el-table-column>
        <el-table-column
          show-overflow-tooltip
          label="异常信息"
          min-width="365px"
          prop="logInfo"
        ></el-table-column>
        <el-table-column
          align="center"
          label="发生时间"
          min-width="150px"
          prop="loadTime"
        ></el-table-column>
        <el-table-column fixed="right" width="100" align="center" label="操作">
          <template slot-scope="{ row }">
            <div>
              <el-tooltip
                class="item"
                effect="dark"
                :content="'日志详情'"
                placement="left-start"
              >
                <el-button
                  plain
                  size="mini"
                  type="primary"
                  icon="el-icon-view"
                  @click="showLog(row)"
                ></el-button>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="queryParam.pageIndex"
        :page-sizes="[10, 20, 50, 100, 200]"
        :page-size="queryParam.pageSize"
        layout="sizes, prev, pager, next, jumper, total"
        :total="table.total"
      >
      </el-pagination>
    </el-card>
    <el-dialog
      title="日志详细"
      :visible.sync="dialogVisible"
      :before-close="handleDialogClose"
      width="80%"
    >
      <el-input rows="25" type="textarea" v-model="errContent"></el-input>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogVisible = false"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { apiGetErrLogs, apiExportErrLogs } from "./api/errLog";
import { mapGetters } from "vuex";
import moment from "moment";
export default {
  name: "ErrorLog",
  data() {
    return {
      loading: false,
      //查询参数
      queryParam: {
        uri: "",
        logInfo: "",
        startTime: "",
        endTime: "",
        pageIndex: 1,
        pageSize: 10,
      },
      userTotal: 0,
      table: {
        rows: [],
        total: 0,
      },
      exportLoading: false,
      dialogVisible: false,
      errContent: "",
    };
  },
  computed: {
    ...mapGetters(["frameStyle"]),
    vmStyle() {
      return {
        height: `calc(100vh - ${this.frameStyle.headerHeight - 16}px)`,
      };
    },
    tbStyle() {
      return {
        height: `calc(100vh - ${this.frameStyle.headerHeight + 58}px)`,
      };
    },
  },

  mounted() {
    this.queryParam.startTime = moment().format("YYYY-MM-DD") + " 00:00:00";
    this.queryParam.endTime = moment().format("YYYY-MM-DD") + " 23:59:59";
    this.getList(true);
  },

  methods: {
    showLog(row) {
      this.errContent = row?.logInfo;
      this.dialogVisible = true;
    },
    handleDialogClose() {
      this.dialogVisible = false;
      this.errContent = "";
    },
    exportExcel() {
      this.exportLoading = true;
      apiExportErrLogs(this.queryParam)
        .then(res => {
          console.log(res);
          if (res.status == 0) {
            this.exportLoading = false;
            this.$message.success({
              message: "导出成功",
            });
          } else {
            this.$message.error({
              message: res?.msg ?? "导出异常！",
            });
          }
        })
        .catch(e => {
          this.exportLoading = false;
          this.$message.error({
            message: e?.msg ?? "导出异常！",
          });
          console.log("导出异常：", e);
        });
    },
    getList(flag) {
      let _this = this;
      _this.loading = true;
      if (flag) {
        _this.queryParam.pageIndex = 1;
      }
      apiGetErrLogs(this.queryParam)
        .then(res => {
          _this.loading = false;
          if (res.status == 0) {
            _this.table.rows = res?.data?.rows ?? [];
            _this.table.total = res?.data?.totalElements ?? 0;
          }
        })
        .catch(e => {
          _this.loading = false;
          _this.$message.error({
            message: e?.msg ?? "查询异常！",
          });
          console.log("查询异常：", e);
        });
    },
    async handleSizeChange(newSize) {
      this.queryParam.pageSize = newSize;
      this.queryParam.pageIndex = 1;
      this.getList();
    },
    async handleCurrentChange(newPage) {
      this.queryParam.pageIndex = newPage;
      this.getList();
    },
  },
};
</script>

<style lang="scss" scoped>
.ErrorLog {
  @include themify() {
    background: themed("$--color-white");
  }
  .el-pagination {
    text-align: right;
  }
  .listHeader {
    .listHeader-title {
      display: inline-block;
      height: 30px;
      line-height: 30px;
    }
  }
}
</style>
