<script>
export default {
  computed: {
    keepAliveInstance() {
      return this.$store.state.keepAlive.instance;
    },
  },
  created() {
    this.redirectTo();
  },
  methods: {
    routeJump(skipType, name, params, query) {
      if (skipType === "params") {
        this.$router.replace({ name: name, params: params });
      } else if (skipType === "query") {
        this.$router.replace({ name: name, query: query });
      } else if (skipType === "") {
        this.$router.replace({ name: name });
      }
    },
    redirectTo() {
      const { skipType, view, isCurrentRoute } = this.$route.params;
      const { name, fullPath, params, query } = view;
      let key = fullPath; // 当前关闭的组件名
      let cache = this.keepAliveInstance.cache; // 缓存的组件
      let keys = this.keepAliveInstance.keys; // 缓存的组件名
      if (cache[key] != null) {
        cache[key].componentInstance.$destroy();
        // cache[key] = null;
        this.$delete(cache, key);
        let index = keys.indexOf(key);
        if (index > -1) {
          keys.splice(index, 1);
        }
        if (isCurrentRoute) {
          let timer = setTimeout(() => {
            this.routeJump(skipType, name, params, query);
            clearTimeout(timer);
          }, 10);
        } else {
          this.routeJump(skipType, name, params, query);
        }
      }
    },
  },
  render: function (h) {
    return h(); // avoid warning message
  },
};
</script>
