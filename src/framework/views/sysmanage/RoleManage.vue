<template>
  <div class="role-manage">
    <div class="filter-header">
      <div style="display: inline-block; margin-left: 5px">角色名：</div>
      <el-input
        style="display: inline-block; width: 220px; margin-right: 5px"
        placeholder="根据角色名模糊匹配"
        v-model="searchVal"
      ></el-input>
      <el-button type="primary" @click="_searchClick" class="el-icon-search">
        查询</el-button
      >
      <el-button
        type="primary"
        @click="_addUser('新增角色')"
        class="el-icon-plus"
      >
        新增</el-button
      >
      <el-button type="primary" :disabled="true" v-show="false">另存</el-button>
    </div>
    <div>
      <el-card
        class="left-role"
        :style="{
          height: wrapHeight,
        }"
        :body-style="cardBodyStyle"
      >
        <div slot="header">
          <p class="title">角色列表</p>
        </div>

        <el-table
          :data="tableData"
          style="width: 100%"
          row-key="roleId"
          ref="tableRole"
          border
        >
          <el-table-column prop="roleName" label="角色名称" width="300"
            ><template slot-scope="{ row }"
              ><el-button type="text" @click="_rowClick(row)">
                {{ row.roleName }}
              </el-button></template
            ></el-table-column
          >
          <el-table-column prop="roleType" label="角色类型" width="180">
            <template slot-scope="{ row }">
              <span>{{
                row.roleType === "MAINTAIN"
                  ? "维护角色"
                  : row.roleType === "ADMIN"
                  ? "管理员"
                  : row.roleType === "SUPERADMIN"
                  ? "超级管理员"
                  : "普通角色"
              }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="roleInfo" label="角色描述"> </el-table-column>
          <el-table-column
            prop="opeation"
            width="160"
            align="center"
            label="操作"
          >
            <template slot-scope="{ row }">
              <el-tooltip
                class="item"
                effect="dark"
                content="编辑"
                placement="left-start"
              >
                <el-button
                  type="primary"
                  size="mini"
                  icon="el-icon-edit"
                  plain
                  @click="editUser('编辑角色', row)"
                ></el-button>
              </el-tooltip>
              <el-tooltip
                class="item"
                effect="dark"
                content="删除"
                placement="left-start"
              >
                <el-button
                  type="primary"
                  size="mini"
                  icon="el-icon-delete"
                  plain
                  @click="_delete(row)"
                ></el-button>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <el-dialog
        :title="title"
        :visible.sync="dialogVisible"
        :close-on-click-modal="false"
        width="450px"
      >
        <el-form ref="form" :model="formData" label-width="80px">
          <el-form-item label="角色名字">
            <el-input
              v-model="formData.roleName"
              maxlength="25"
              show-word-limit
            ></el-input>
          </el-form-item>
          <el-form-item label="类型">
            <el-select
              v-model="formData.roleType"
              placeholder="请选择"
              style="width: 100%"
            >
              <el-option label="超级管理员" value="SUPERADMIN"> </el-option>
              <el-option label="管理员" value="ADMIN"> </el-option>
              <el-option label="普通角色" value="NORMAL"> </el-option>
              <el-option label="维护人员" value="MAINTAIN"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="描述">
            <el-input
              v-model="formData.roleInfo"
              maxlength="100"
              show-word-limit
              rows="3"
              type="textarea"
            ></el-input>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="_save"> 确 定 </el-button>
          <el-button @click="dialogVisible = false">取 消</el-button>
        </span>
      </el-dialog>
      <el-card
        class="right-tree"
        :style="{ height: wrapHeight }"
        :body-style="cardBodyStyle"
      >
        <div slot="header">
          <p class="title">
            {{ checkRow.roleName }}权限
            <el-button
              style="float: right; margin-right: 10px"
              type="primary"
              @click="_saveChecked"
              class="el-icon-check"
              :disabled="checkRow.length === 0"
            >
              保存
            </el-button>
          </p>
        </div>
        <div class="resourceTree">
          <im-tree
            ref="tree"
            :model="resourceData"
            id-key="resourceId"
            title-key="resourceName"
            children-key="childList"
            parent-key="parentId"
            :tool-options="['expand']"
            :show-icon="true"
            :show-checkbox="true"
            :highlight-current="true"
            :expand-on-click-node="false"
            :default-expand-all="true"
            :default-expanded-keys="['-1']"
            :body-height="resourceTreeHeight"
          ></im-tree>
        </div>
      </el-card>
    </div>
  </div>
</template>
<script>
import {
  apifindAll,
  apiResource,
  apiaddRole,
  apieditRole,
  apideleteRole,
  apisearchIds,
  apisaveJur,
} from "./api/rolemanage";
import { mapGetters } from "vuex";
import { ImTree } from "itsm-common";

export default {
  name: "JurisdictionManage",
  data() {
    return {
      tableData: [],
      checkRow: [],
      resourceData: [],
      rscTreeProps: {
        children: "childList",
        label: "resourceName",
      },
      dialogVisible: false,
      formData: {},
      title: "",
      roleId: "",
      editData: null,
      searchVal: "",
      cardBodyStyle: {
        height: "calc(100% - 51px)",
        overflow: "auto",
      },
    };
  },
  components: { ImTree },
  watch: {
    searchVal(val) {
      let str = val.trim();
      if (str == "") {
        this._getRoteManageData();
      }
    },
  },
  mounted() {
    this._getRoteManageData();
    this._getMenu();
  },
  methods: {
    _getRoteManageData() {
      apifindAll().then(result => {
        if (result.status == 0) {
          this.tableData = result.data;
        }
      });
    },
    _getMenu() {
      apiResource().then(result => {
        if (result.status == 0) {
          let nodes = result.data;
          let rootNode = {
            resourceId: -1,
            parentId: null,
            resourceName: "系统菜单",
            childList: [],
          };
          for (const n of nodes) {
            n["childList"] = [];
            for (const c of nodes) {
              if (c.parentId === n.resourceId) {
                n.childList.push(c);
              }
            }
            if (n.parentId === "-1") {
              rootNode.childList.push(n);
            }
          }
          this.resourceData.push(rootNode);
        }
      });
    },
    _rowClick(row) {
      this.checkRow = row;
      const loading = this.$loading({
        lock: true,
        target: ".right-tree",
        text: "Loading",
      });
      apisearchIds(row.roleId).then(result => {
        if (result.status == 0) {
          this.$refs.tree.setCheckedKeys(result.data.resourceIdList);
          loading.close();
        }
      });
    },
    _saveChecked() {
      var selectNodes = this.$refs.tree.getCheckedNodes();
      let rscIds = [];
      for (const node of selectNodes) {
        if (node.childList.length === 0) {
          rscIds.push(node.resourceId);
        }
      }
      let params = {
        roleId: this.checkRow.roleId,
        resourceIdList: rscIds,
      };
      apisaveJur(params).then(result => {
        if (result.status == 0) {
          this.$message({
            message: "保存成功",
            type: "success",
          });
          this._getRoteManageData();
        }
      });
    },
    _addUser(str) {
      this.title = str;
      this.formData = {
        roleId: "",
        roleName: "",
        roleInfo: "",
        roleType: "NORMAL",
      };
      this.dialogVisible = true;
    },
    _save() {
      if (this.formData.roleName.trim() == "") {
        this.$message({
          message: "角色名不能为空。",
          type: "warning",
        });
        return;
      }
      apiaddRole(this.formData)
        .then(() => {
          this.dialogVisible = false;
          this._getRoteManageData();
          this.$message({
            message: "保存成功",
            type: "success",
          });
        })
        .catch(err => {
          this.$message({
            message: err.msg,
            type: "error",
          });
        });
    },
    editUser(str, row) {
      this.title = str;
      this.roleId = row.roleId;
      this.formData = row;
      apieditRole(row.roleId).then(result => {
        if (result.status == 0) {
          this.editData = result.data;
        }
      });
      this.dialogVisible = true;
    },
    _delete(row) {
      this.$confirm("是否删除该角色?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          apideleteRole(row.roleId).then(result => {
            if (result.status == 0) {
              this._getRoteManageData();
              this.$message({
                type: "success",
                message: "删除成功!",
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    _searchClick() {
      let str = this.searchVal.toUpperCase();
      str.trim();
      let rows = this.tableData;
      let arr = [];
      rows.forEach(row => {
        let nameStr = row.roleName.toUpperCase();
        if (nameStr.indexOf(str) != -1) {
          arr.push(row);
        }
      });
      this.tableData = arr;
    },
  },
  computed: {
    ...mapGetters({
      frameStyle: "frameStyle",
    }),
    resourceTreeHeight() {
      return `calc(100vh - ${this.frameStyle.headerHeight}px - 160px)`;
    },
    wrapHeight() {
      return `calc(100vh - ${this.frameStyle.headerHeight}px - 48px)`;
    },
    rightBodyHeight() {
      return `calc(100vh - ${this.frameStyle.headerHeight}px - 120px)`;
    },
  },
};
</script>
<style lang="scss" scoped>
.role-manage {
  .filter-header {
    padding: 8px 4px;
  }
  .left-role {
    display: inline-block;
    width: calc(100% - 405px);
    margin-right: 5px;
  }
  .role-table {
    height: 100%;
    ::v-deep {
      .el-card__body {
        height: calc(100% - 48px);
      }
    }
  }
  .right-tree {
    display: inline-block;
    width: 400px;
  }
  .resourceTree {
    height: 100%;
    overflow-y: auto;
  }
  .title {
    font-size: 15px;
    margin: 0;
    padding: 5px 0;
    overflow: hidden;
    line-height: 28px;
  }
}
</style>
