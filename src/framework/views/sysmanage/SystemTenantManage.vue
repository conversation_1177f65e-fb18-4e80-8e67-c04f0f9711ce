<template>
  <div>
    <el-container>
      <el-aside width="230px">
        <el-button-group>
          <el-button
            type="primary"
            icon="el-icon-plus"
            plain
            @click="openAddTenantDialog"
          >
            新增
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-edit"
            plain
            :disabled="selectedTenant == null"
            @click="openEditTenantDialog"
          >
            编辑
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-delete"
            plain
            :disabled="selectedTenant == null"
            @click="deleteTenant"
          >
            删除
          </el-button>
        </el-button-group>
        <el-menu
          :default-active="userSearchForm.tenantId"
          :style="{ textAlign: `left` }"
        >
          <im-tree
            :model="tenantMenuData"
            id-key="tenantId"
            title-key="tenantName"
            :show-icon="true"
            :highlight-current="true"
            :expand-on-click-node="false"
            leaf-icon="el-icon-user"
            @current-change="changeTenant"
          ></im-tree>
        </el-menu>
      </el-aside>
      <el-main style="padding: 5px;">
        <el-row>
          <el-col :span="8" style="text-align: left;">
            <p style="color:#4270e9;font-weight: bold;">
              租户：{{ userSearchForm.tenantName }}
            </p>
          </el-col>
          <el-col :span="8" style="text-align: center;">
            <el-input
              v-model="userSearchForm.userKeyWord"
              style="width: 230px;"
              placeholder="用户账号/姓名"
              clearable
            >
              <el-button
                slot="append"
                icon="el-icon-search"
                @click="searchUserInfo"
              ></el-button>
            </el-input>
          </el-col>
          <el-col :span="8" style="text-align: right;">
            <el-button
              type="primary"
              icon="el-icon-plus"
              plain
              @click="openAddUserDialog"
            >
              添加用户
            </el-button>
          </el-col>
        </el-row>
        <el-table :data="userTable.data" row-key="userId" border stripe>
          <el-table-column prop="userName" label="用户账号"></el-table-column>
          <el-table-column prop="realName" label="姓名"></el-table-column>
          <el-table-column prop="deptName" label="组织"></el-table-column>
          <el-table-column prop="roleNames" label="角色" width="240">
          </el-table-column>
          <el-table-column
            width="110"
            align="center"
            label="用户有效性"
            prop="enableSwitch"
          >
            <template slot-scope="{ row }">
              <el-tag :type="row.enableSwitch === '0' ? 'info' : 'success'">
                {{ row.enableSwitch === "1" ? "启用" : "禁用" }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column width="120" align="left" label="操作">
            <template slot-scope="{ row }">
              <el-tooltip
                content="从当前租户移除"
                placement="left"
                :transfer="true"
                :open-delay="500"
              >
                <el-button
                  type="primary"
                  icon="el-icon-remove"
                  plain
                  @click="removeUserFromCurrentTenant(row)"
                >
                  移除用户
                </el-button>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
        <div>&nbsp;</div>
        <el-pagination
          background
          :current-page="userTable.pageNum"
          :page-sizes="[10, 15, 20, 30, 50]"
          :page-size="userTable.pageSize"
          layout="->, sizes, prev, pager, next, jumper, slot, total"
          :total="userTable.total"
          @current-change="pageChange"
          @size-change="pageSizeChange"
        ></el-pagination>
      </el-main>
    </el-container>

    <!-- 租户信息编辑对话框 -->
    <el-dialog
      :title="editTenantForm.id !== null ? '编辑租户' : '新增租户'"
      :visible.sync="editTenantDialogVisible"
      width="35%"
      style="padding: 5px;"
    >
      <el-form
        ref="tenantEditForm"
        :model="editTenantForm"
        :rules="tenantEditRules"
        label-width="100px"
        label-suffix=":"
      >
        <el-form-item label="租户标识" prop="tenantId">
          <el-input
            v-model="editTenantForm.tenantId"
            style="width: 230px;"
            onkeyup="value=value.replace(/[^\w\.\/]/ig,'')"
            placeholder="租户标识 英文数字下划线"
            maxlength="32"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="租户名称" prop="tenantName">
          <el-input
            v-model="editTenantForm.tenantName"
            style="width: 230px;"
            maxlength="32"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="服务描述">
          <el-input
            type="textarea"
            v-model="editTenantForm.tenantDesc"
            maxlength="200"
            show-word-limit
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button @click="editTenantDialogVisible = false">
          取 消
        </el-button>
        <el-button type="primary" @click="saveTenant">
          保 存
        </el-button>
      </span>
    </el-dialog>

    <!-- 添加用户对话框 -->
    <el-dialog
      title="添加用户"
      :visible.sync="addUserDialogVisible"
      width="45%"
      style="padding: 5px;"
    >
      <el-row type="flex" justify="center">
        <el-col :span="8">
          <el-input
            v-model="addUserForm.userKeyWord"
            placeholder="用户账号/姓名"
            clearable
          >
            <el-button
              slot="append"
              icon="el-icon-search"
              @click="searchAddUser"
            ></el-button>
          </el-input>
        </el-col>
      </el-row>
      <div>&nbsp;</div>
      <el-table
        :data="addUserForm.tableData"
        row-key="userId"
        :height="260"
        border
        stripe
      >
        <el-table-column prop="userName" label="用户账号"></el-table-column>
        <el-table-column prop="realName" label="姓名"></el-table-column>
        <el-table-column width="120" align="left" label="操作">
          <template slot-scope="{ row }">
            <el-button
              type="primary"
              icon="el-icon-plus"
              plain
              @click="addUserToTagGroup(row)"
            >
              添加
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div>&nbsp;</div>
      <el-tag
        v-bind:key="item.realName"
        v-for="item in dynamicTags"
        closable
        :disable-transitions="false"
        @close="tagCloseHandler(item)"
      >
        {{ item.realName }}
      </el-tag>
      <span slot="footer">
        <el-button @click="addUserDialogVisible = false">
          取 消
        </el-button>
        <el-button
          type="primary"
          @click="addUserToTenant"
          :disabled="dynamicTags.length < 1"
        >
          确 定
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { systemTenantApi } from "./api/tenant";
import { ImTree } from "itsm-common";

export default {
  name: "SystemTenantManage",
  components: { ImTree },
  data() {
    return {
      addUserDialogVisible: false,
      editTenantDialogVisible: false,
      tenantMenuData: {},
      userSearchForm: {
        tenantId: "",
        tenantName: "",
        userKeyWord: "",
      },
      selectedTenant: null,
      editTenantForm: {
        id: null,
        tenantId: null,
        tenantName: null,
        tenantDesc: null,
      },
      tenantEditRules: {
        tenantId: [
          { required: true, message: "请输入租户标识", trigger: "blur" },
          {
            min: 3,
            max: 32,
            message: "长度在 5 到 32 个字符",
            trigger: "blur",
          },
        ],
        tenantName: [
          { required: true, message: "请输入租户名称", trigger: "blur" },
          {
            min: 2,
            max: 32,
            message: "长度在 5 到 32 个字符",
            trigger: "blur",
          },
        ],
      },
      userTable: {
        data: [],
        total: 0,
        pageNum: 1,
        pageSize: 10,
      },
      addUserForm: {
        userKeyWord: null,
        needAddUser: [],
        tableData: [],
      },
      dynamicTags: [],
    };
  },
  methods: {
    //查询初始化数据
    getInitData() {
      this.getTenantData();
    },

    //查询租户信息数据
    getTenantData() {
      systemTenantApi.getAllSystemTenantVoList().then(result => {
        this.tenantMenuData = result.data;
        if (this.tenantMenuData != null && this.tenantMenuData.length > 0) {
          let selTenant = this.tenantMenuData[0];
          this.userSearchForm.tenantId = selTenant.tenantId;
          this.userSearchForm.tenantName = selTenant.tenantName;
          this.editTenantForm = selTenant;
          this.selectedTenant = selTenant;
          this.doSearchUserInfo();
        }
      });
    },

    //选择Tenant触发
    changeTenant(data) {
      this.userSearchForm.tenantId = data.tenantId;
      this.userSearchForm.tenantName = data.tenantName;
      this.editTenantForm = data;
      this.selectedTenant = data;
      this.doSearchUserInfo();
    },

    //执行用户数据查询,重置分页
    doSearchUserInfo() {
      this.userTable.pageNum = 1;
      this.searchUserInfo();
    },

    //重置租户表单
    resetEditTenantForm() {
      this.editTenantForm = {
        id: null,
        tenantId: null,
        tenantName: null,
        tenantDesc: null,
      };
      if (this.$refs["tenantEditForm"] !== undefined) {
        this.$refs["tenantEditForm"].clearValidate();
      }
    },

    //打开新增租户对话框
    openAddTenantDialog() {
      this.resetEditTenantForm();
      this.editTenantDialogVisible = true;
    },

    //打开编辑租户按钮
    openEditTenantDialog() {
      this.editTenantForm = this.selectedTenant;
      this.editTenantDialogVisible = true;
      if (this.$refs["tenantEditForm"] !== undefined) {
        this.$refs["tenantEditForm"].clearValidate();
      }
    },

    //保存租户信息
    saveTenant() {
      this.$refs["tenantEditForm"].validate(valid => {
        if (valid) {
          systemTenantApi.save(this.editTenantForm).then(() => {
            this.editTenantDialogVisible = false;
            this.getTenantData();
          });
        }
      });
    },

    //删除租户信息
    deleteTenant() {
      this.$confirm(
        "确定要删除租户'" + this.selectedTenant.tenantName + "'吗？",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      ).then(() => {
        systemTenantApi.delete({ id: this.selectedTenant.id }).then(() => {
          this.getTenantData();
          this.$message({
            message: "删除租户成功！",
            type: "success",
          });
        });
      });
    },

    //查询用户信息
    searchUserInfo() {
      this.userTable.data = [];
      systemTenantApi
        .searchUserInfo({
          tenantId: this.selectedTenant.tenantId,
          userKeyWord: this.userSearchForm.userKeyWord,
          pageNum: this.userTable.pageNum,
          pageSize: this.userTable.pageSize,
        })
        .then(result => {
          if (result.status === "0") {
            if (
              result.data.userList != null &&
              result.data.userList.length > 0
            ) {
              let pageData = result.data;
              this.userTable.data = pageData.userList;
              this.userTable.total = pageData.total;
            } else if (this.userTable.pageNum > 1) {
              let pageNum = this.userTable.pageNum - 1;
              if (pageNum < 1) {
                pageNum = 1;
              }
              this.userTable.pageNum = pageNum;
              this.searchUserInfo();
            }
          }
        });
    },

    //翻页
    pageChange(index) {
      this.userTable.pageNum = index;
      this.searchUserInfo();
    },

    //改变分页条数
    pageSizeChange(size) {
      this.userTable.pageSize = size;
      this.userTable.pageNum = 1;
      this.searchUserInfo();
    },

    //打开添加用户窗口
    openAddUserDialog() {
      this.addUserForm.userKeyWord = null;
      this.addUserForm.tableData = [];
      this.dynamicTags = [];
      this.addUserDialogVisible = true;
    },

    //搜索添加的用户
    searchAddUser() {
      systemTenantApi
        .searchAddUser({ userKeyWord: this.addUserForm.userKeyWord })
        .then(result => {
          this.addUserForm.tableData = result.data;
        });
    },

    //添加用户到标签组
    addUserToTagGroup(user) {
      if (!this.dynamicTags.includes(user)) {
        this.dynamicTags.push(user);
      } else {
        this.$notify({
          message: "'" + user.realName + "' 已添加！",
          type: "success",
        });
      }
    },

    //关闭用户标签
    tagCloseHandler(tag) {
      this.dynamicTags.splice(this.dynamicTags.indexOf(tag), 1);
    },

    //添加用户到租户
    addUserToTenant() {
      let userList = [];
      this.dynamicTags.forEach(u => {
        userList.push(u.userName);
      });

      systemTenantApi
        .addUserRelations({
          tenantId: this.selectedTenant.tenantId,
          userNameList: userList,
        })
        .then(() => {
          this.addUserDialogVisible = false;
          this.$notify({
            message: "已成功添加用户！",
            type: "success",
          });
          this.userTable.pageNum = 1;
          this.searchUserInfo();
        });
    },

    //移除用户
    removeUserFromCurrentTenant(row) {
      this.$confirm(
        "确定要将用户'" +
          row.realName +
          "' 从租户 '" +
          this.selectedTenant.tenantName +
          "' 中移除吗？",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      ).then(() => {
        systemTenantApi
          .removeUserRelation({
            tenantId: this.selectedTenant.tenantId,
            userName: row.userName,
          })
          .then(() => {
            this.searchUserInfo();
            this.$notify({
              message: "已成功移除用户！",
              type: "success",
            });
          });
      });
    },
  },
  mounted() {
    this.getInitData();
  },
};
</script>

<style scoped>
.el-tag + .el-tag {
  margin-left: 10px;
}
</style>
