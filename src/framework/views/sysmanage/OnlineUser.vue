<template>
  <!-- 在线用户统计 -->
  <div class="OnlineUser" :style="vmStyle">
    <el-card style="height: 50px">
      <el-form :inline="true">
        <el-form-item label="用户账号:">
          <el-input
            style="width: 150px"
            clearable
            @clear="getList()"
            v-model.trim="selInfo.userName"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="登录IP:">
          <el-input
            style="width: 200px"
            clearable
            @clear="getList()"
            v-model.trim="selInfo.loginIp"
          >
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="getList()">
            查询
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card :style="tbStyle" :body-style="{ height: 'calc(100% - 88px)' }">
      <div slot="header">
        <div class="listHeader">
          <span class="listHeader-title"
            >在线用户详情<span style="color: red">
              (在线用户数：{{ userTotal }}）</span
            ></span
          >
        </div>
      </div>
      <el-table
        border
        v-loading="loading"
        height="100%"
        ref="sysSettingTable"
        :data="
          table.rows.slice(
            (table.page - 1) * table.limit,
            table.page * table.limit
          )
        "
        stripe
      >
        <el-table-column type="index" width="50">
          <template scope="scope">
            <span>{{ (table.page - 1) * table.limit + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="用户名"
          min-width="120px"
          prop="userName"
        ></el-table-column>
        <el-table-column
          label="真实姓名"
          min-width="150px"
          prop="realName"
        ></el-table-column>
        <el-table-column
          label="登录IP"
          min-width="200px"
          prop="loginIp"
        ></el-table-column>
        <el-table-column
          label="登录时间"
          min-width="150px"
          prop="loginDateTime"
        ></el-table-column>
        <el-table-column
          label="登录时长"
          min-width="150px"
          prop="loginTimes"
        ></el-table-column>
      </el-table>
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="table.page"
        :page-sizes="[10, 20, 50, 100, 200]"
        :page-size="table.limit"
        layout="sizes, prev, pager, next, jumper, total"
        :total="table.total"
      >
      </el-pagination>
    </el-card>
  </div>
</template>

<script>
import { apiGetOnlineUsers } from "./api/onlineUser";
import { mapGetters } from "vuex";
export default {
  name: "OnlineUser",
  data() {
    return {
      loading: false,
      //查询参数
      selInfo: {
        userName: "",
        loginIp: "",
      },
      userTotal: 0,
      table: {
        page: 1,
        limit: 10,
        rows: [],
        total: 0,
      },
    };
  },
  computed: {
    ...mapGetters(["frameStyle"]),
    vmStyle() {
      return {
        height: `calc(100vh - ${this.frameStyle.headerHeight - 16}px)`,
      };
    },
    tbStyle() {
      return {
        height: `calc(100vh - ${this.frameStyle.headerHeight + 58}px)`,
      };
    },
  },

  mounted() {
    this.getList();
  },

  methods: {
    getList() {
      let _this = this;
      _this.loading = true;
      apiGetOnlineUsers(this.selInfo)
        .then(res => {
          _this.loading = false;
          if (res.status == 0) {
            _this.table.rows = res?.data?.onlineUserList ?? [];
            _this.table.total = res?.data?.onlineUserList?.length ?? 0;
            _this.userTotal = res?.data?.userTotal ?? 0;
            _this.table.page = 1;
          }
        })
        .catch(e => {
          _this.loading = false;
          _this.$message.error({
            message: e?.msg ?? "查询异常！",
          });
          console.log("查询异常：", e);
        });
    },
    async handleSizeChange(newSize) {
      this.table.limit = newSize;
      this.table.page = 1;
    },
    async handleCurrentChange(newPage) {
      this.table.page = newPage;
    },
  },
};
</script>

<style lang="scss" scoped>
.OnlineUser {
  @include themify() {
    background: themed("$--color-white");
  }
  .el-pagination {
    text-align: right;
  }
  .listHeader {
    .listHeader-title {
      display: inline-block;
      height: 30px;
      line-height: 30px;
    }
  }
}
</style>
