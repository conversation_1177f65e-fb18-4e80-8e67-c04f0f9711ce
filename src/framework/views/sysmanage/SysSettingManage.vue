<template>
  <!-- 系统参数管理 -->
  <div class="SysSettingManage" :style="vmStyle">
    <el-card style="height: 50px">
      <el-form :inline="true">
        <el-form-item label="系统参数名:">
          <el-input
            style="width: 150px"
            clearable
            @clear="getList()"
            v-model.trim="selInfo.settingName"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="参数key:">
          <el-input
            style="width: 345px"
            clearable
            @clear="getList()"
            v-model.trim="selInfo.settingCode"
          >
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="getList()">
            查询
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-button
            @click="add"
            size="mini"
            type="primary"
            v-has="'system:setting:add'"
            class="el-icon-plus"
          >
            添加
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card :style="tbStyle" :body-style="{ height: 'calc(100% - 88px)' }">
      <div slot="header">
        <div class="listHeader">
          <span class="listHeader-title">系统参数配置详情</span>
        </div>
      </div>
      <el-table
        border
        v-loading="loading"
        height="100%"
        ref="sysSettingTable"
        :data="
          table.rows.slice(
            (table.page - 1) * table.limit,
            table.page * table.limit
          )
        "
        stripe
      >
        <el-table-column type="index" width="50">
          <template scope="scope">
            <span>{{ (table.page - 1) * table.limit + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="app编码"
          min-width="120px"
          prop="appCode"
        ></el-table-column>
        <el-table-column
          label="参数名称"
          min-width="150px"
          prop="settingName"
        ></el-table-column>
        <el-table-column
          label="参数key"
          min-width="250px"
          prop="settingCode"
        ></el-table-column>
        <el-table-column
          label="参数值"
          min-width="150px"
          prop="settingValue"
        ></el-table-column>
        <el-table-column
          label="参数类型"
          min-width="100px"
          prop="settingDataType"
        ></el-table-column>
        <el-table-column
          label="备注"
          min-width="222px"
          prop="settingRemark"
        ></el-table-column>
        <el-table-column
          label="更新时间"
          align="center"
          min-width="160px"
          prop="loadTime"
        ></el-table-column>
        <el-table-column width="120" align="center" label="操作">
          <template slot-scope="{ row }">
            <div>
              <el-tooltip
                class="item"
                effect="dark"
                content="编辑"
                placement="left-start"
                :open-delay="800"
              >
                <el-button
                  type="primary"
                  size="mini"
                  icon="el-icon-edit"
                  plain
                  v-has="'system:setting:edit'"
                  @click="edit(row)"
                ></el-button>
              </el-tooltip>
              <el-tooltip
                class="item"
                effect="dark"
                content="删除"
                placement="left-start"
                :open-delay="800"
              >
                <el-button
                  type="danger"
                  size="mini"
                  icon="el-icon-delete"
                  plain
                  v-has="'system:setting:delete'"
                  @click="del(row)"
                ></el-button>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="table.page"
        :page-sizes="[10, 20, 50, 100, 200]"
        :page-size="table.limit"
        layout="sizes, prev, pager, next, jumper, total"
        :total="table.total"
      >
      </el-pagination>
    </el-card>
    <el-dialog
      width="520px"
      :before-close="beforeClose"
      :title="dialogTitle"
      :visible.sync="showDialog"
      :close-on-click-modal="false"
      :destroy-on-close="true"
    >
      <el-form
        :model="settingForm"
        :rules="rules"
        ref="setForm"
        :status-icon="true"
        :validate-on-rule-change="true"
      >
        <el-form-item label="APP编码：" prop="appCode" label-width="100px">
          <el-input
            clearable
            placeholder="请输入aPP编码"
            style="width: 345px"
            v-model.trim="settingForm.appCode"
          ></el-input>
        </el-form-item>
        <el-form-item label="参数名称：" prop="settingName" label-width="100px">
          <el-input
            clearable
            placeholder="请输入参数名"
            style="width: 345px"
            v-model.trim="settingForm.settingName"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="数据类型："
          prop="settingDataType"
          label-width="100px"
        >
          <el-select
            @change="changeType"
            clearable
            style="width: 345px"
            v-model.trim="settingForm.settingDataType"
            placeholder="请选择数据类型"
          >
            <el-option label="STRING" value="STRING"></el-option>
            <el-option label="INTEGER" value="INTEGER"></el-option>
            <el-option label="LONG" value="LONG"></el-option>
            <el-option label="DATETIME" value="DATETIME"></el-option>
            <el-option label="BOOLEAN" value="BOOLEAN"></el-option>
            <el-option label="SVG" value="SVG"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="参数key：" prop="settingCode" label-width="100px">
          <el-input
            clearable
            :disabled="settingForm.settingId != ''"
            placeholder="请输入key"
            style="width: 345px"
            v-model.trim="settingForm.settingCode"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="参数value："
          prop="settingValue"
          label-width="100px"
        >
          <el-input
            v-if="
              settingForm.settingDataType == '' ||
              settingForm.settingDataType == 'STRING'
            "
            clearable
            placeholder="请输入参数值"
            style="width: 345px"
            v-model.trim="settingForm.settingValue"
          ></el-input>
          <el-input-number
            v-if="
              settingForm.settingDataType == 'INTEGER' ||
              settingForm.settingDataType == 'LONG'
            "
            style="width: 345px"
            v-model="settingForm.settingValue"
            :min="0"
            label="请输入参数值"
          ></el-input-number>
          <el-date-picker
            v-if="settingForm.settingDataType == 'DATETIME'"
            v-model="settingForm.settingValue"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetime"
            style="width: 345px"
            placeholder="选择日期时间"
          >
          </el-date-picker>
          <el-select
            v-if="settingForm.settingDataType == 'BOOLEAN'"
            clearable
            placeholder="请输入参数值"
            style="width: 345px"
            v-model="settingForm.settingValue"
          >
            <el-option label="false" value="false"></el-option>
            <el-option label="true" value="true"></el-option>
          </el-select>
          <el-input
            v-if="settingForm.settingDataType == 'SVG'"
            clearable
            placeholder="请输入参数值"
            style="width: 345px"
            v-model="settingForm.settingValue"
          ></el-input>
        </el-form-item>
        <el-form-item label="备注：" prop="settingRemark" label-width="100px">
          <el-input
            clearable
            style="width: 345px"
            type="textarea"
            placeholder="请输入备注信息"
            v-model="settingForm.settingRemark"
            :autosize="{ minRows: 3, maxRows: 6 }"
            maxlength="50"
            show-word-limit
          >
          </el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="saveSetting">保 存</el-button>
        <el-button @click="beforeClose">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  apiGetSettingList,
  apiSaveSetting,
  apiDelSetting,
} from "./api/sysSettingManage";
import { mapGetters } from "vuex";
export default {
  name: "SysSettingManage",
  data() {
    return {
      loading: false,
      //查询参数
      selInfo: {
        settingName: "",
        settingCode: "",
      },
      table: {
        page: 1,
        limit: 10,
        rows: [],
        total: 0,
      },
      settingForm: {
        settingId: "",
        appCode: "",
        settingName: "",
        settingCode: "",
        settingDataType: "",
        settingValue: "",
        settingRemark: "",
      },
      rules: {
        appCode: [
          { required: true, message: "请输入app编码", trigger: "blur" },
        ],
        settingName: [
          { required: true, message: "请输入参数名称", trigger: "blur" },
        ],
        settingCode: [
          { required: true, message: "请输入参数key", trigger: "blur" },
          {
            pattern: /^[^\u4e00-\u9fa5]+$/,
            message: "key中不能包含中文",
            trigger: "blur",
          },
        ],
        settingDataType: [
          { required: true, message: "请选择数据类型", trigger: "blur" },
        ],
        settingValue: [
          { required: true, message: "请输入参数值", trigger: "blur" },
        ],
      },
      showDialog: false,
      dialogTitle: "系统参数新增",
    };
  },
  computed: {
    ...mapGetters(["frameStyle"]),
    vmStyle() {
      return {
        height: `calc(100vh - ${this.frameStyle.headerHeight - 16}px)`,
      };
    },
    tbStyle() {
      return {
        height: `calc(100vh - ${this.frameStyle.headerHeight + 58}px)`,
      };
    },
  },

  mounted() {
    this.getList();
  },

  methods: {
    getList() {
      let _this = this;
      _this.loading = true;
      apiGetSettingList(this.selInfo)
        .then(res => {
          _this.loading = false;
          if (res.status == 0) {
            _this.table.rows = res?.data ?? [];
            _this.table.total = res?.data?.length ?? 0;
            _this.table.page = 1;
          }
        })
        .catch(e => {
          _this.loading = false;
          _this.$message.error({
            message: e?.msg ?? "查询异常！",
          });
          console.log("查询异常：", e);
        });
    },
    async handleSizeChange(newSize) {
      this.table.limit = newSize;
      this.table.page = 1;
    },
    async handleCurrentChange(newPage) {
      this.table.page = newPage;
    },

    changeType(val) {
      console.log("changeType,val=", val);
      this.settingForm.settingValue = null;
    },

    add() {
      this.settingForm = {
        settingId: "",
        appCode: "DEFAULT",
        settingName: "",
        settingCode: "",
        settingDataType: "",
        settingValue: "",
        settingRemark: "",
      };
      this.showDialog = true;
      this.dialogTitle = "系统参数新增";
    },
    edit(row) {
      console.log("=== edit,", row);
      this.settingForm = {
        settingId: row?.settingId ?? "",
        appCode: row?.appCode ?? "",
        settingName: row?.settingName ?? "",
        settingCode: row?.settingCode ?? "",
        settingDataType: row?.settingDataType ?? "",
        settingValue: row?.settingValue ?? "",
        settingRemark: row?.settingRemark ?? "",
      };
      this.showDialog = true;
      this.dialogTitle = "系统参数编辑";
    },

    del(row) {
      let _this = this;
      let settingId = row?.settingId ?? "";
      console.log("=== del,row=", row);
      console.log("=== del,settingId=", settingId);
      _this
        .$confirm("确定要删除该配置吗？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
        .then(() => {
          apiDelSetting([settingId]).then(res => {
            if (res.status == 0) {
              _this.$message({
                type: "success",
                message: "删除成功!",
              });
              _this.getList();
            }
          });
        })
        .catch(() => {
          _this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    saveSetting() {
      let _this = this;
      _this.$refs["setForm"].validate(valid => {
        if (valid) {
          apiSaveSetting(_this.settingForm)
            .then(res => {
              if (res.status == 0) {
                _this.$message({
                  showClose: true,
                  type: "success",
                  message: "保存成功!",
                });
                _this.beforeClose();
                _this.getList();
              }
            })
            .catch(err => {
              this.$message({
                type: "warning",
                message: err?.msg ?? "保存失败！",
              });
            });
        } else {
          return false;
        }
      });
    },
    beforeClose() {
      this.showDialog = false;
      this.$refs.setForm.resetFields();
    },
  },
};
</script>

<style lang="scss" scoped>
.SysSettingManage {
  @include themify() {
    background: themed("$--color-white");
  }
  .el-pagination {
    text-align: right;
  }
  .listHeader {
    .listHeader-title {
      display: inline-block;
      height: 30px;
      line-height: 30px;
    }
  }
}
</style>
