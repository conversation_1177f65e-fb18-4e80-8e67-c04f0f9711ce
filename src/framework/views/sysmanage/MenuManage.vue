<template>
  <div class="MenuManage">
    <el-row>
      <el-col :span="6" style="text-align: left">
        <el-button
          type="primary"
          plain
          :icon="isExpand ? 'el-icon-caret-bottom' : 'el-icon-caret-right'"
          @click="handleExpand"
          :disabled="tableLoading"
        >
          {{ isExpand ? "折叠" : "展开" }}
        </el-button>
        &nbsp;&nbsp;
        <el-button
          size="mini"
          type="primary"
          class="el-icon-plus"
          style="margin-bottom: 5px"
          @click="createdMainMenu"
        >
          新建主菜单
        </el-button>
      </el-col>
      <el-col :span="12" style="text-align: center">
        <el-input
          size="mini"
          clearable
          placeholder="菜单名称或标识"
          style="width: 200px"
          v-model="searchForm.keyWord"
        ></el-input>
        &nbsp;&nbsp;
        <el-button
          type="primary"
          icon="el-icon-search"
          :loading="tableLoading"
          @click="searchMenuData"
        >
          查询
        </el-button>
      </el-col>
      <el-col :span="6" style="text-align: right">
        <el-button
          type="primary"
          size="mini"
          class="el-icon-download"
          @click="downLoad"
          plain
          :disabled="!checkLists.length > 0"
        >
          导出
        </el-button>
        <el-button
          type="primary"
          size="mini"
          class="el-icon-upload"
          plain
          @click="openMenuImportDialog"
        >
          导入
        </el-button>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-table
          :data="menuData"
          v-loading="tableLoading"
          element-loading-background="rgba(0, 0, 0, 0.5)"
          element-loading-text="数据正在加载中"
          element-loading-spinner="el-icon-loading"
          ref="tableMenu"
          :height="tableHeight"
          row-key="resourceId"
          @select="selectMenu"
          @select-all="selectAllMenu"
          :tree-props="{ children: 'children' }"
          border
        >
          <el-table-column type="selection" width="55" align="left">
          </el-table-column>
          <el-table-column
            label="序号"
            prop="sortIndex"
            width="100"
            align="left"
          >
          </el-table-column>
          <el-table-column
            label="名称"
            prop="resourceName"
            width="200"
            align="left"
          >
            <template slot-scope="scope">
              <span v-html="showData(scope.row.resourceName)"></span>
            </template>
          </el-table-column>
          <el-table-column
            label="图标"
            prop="resourceIcon"
            width="80"
            align="center"
          >
            <template slot-scope="{ row }">
              <base-icon
                class="iconClass"
                :icon-class="row.resourceIcon"
                v-if="row.resourceIcon"
              ></base-icon>
            </template>
          </el-table-column>
          <el-table-column
            label="标识"
            prop="resourceCode"
            width="250"
            align="left"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span v-html="showData(scope.row.resourceCode)"></span>
            </template>
          </el-table-column>
          <el-table-column
            label="路径"
            prop="resourceUrl"
            min-width="280"
            align="left"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            label="权限标识"
            prop="permCode"
            width="200"
            align="left"
          >
          </el-table-column>
          <el-table-column
            label="是否隐藏"
            prop="isHidden"
            width="100"
            align="center"
          >
            <template slot-scope="{ row }">
              {{ row.isHidden ? "是" : "否" }}
            </template>
          </el-table-column>
          <el-table-column label="" prop="opeation" width="180" align="center">
            <template slot-scope="{ row }">
              <el-tooltip
                class="item"
                effect="dark"
                content="添加子节点"
                placement="left-start"
                :open-delay="500"
              >
                <el-button
                  size="mini"
                  type="primary"
                  class="el-icon-plus"
                  plain
                  @click="createdMenu(row)"
                  :disabled="row.resourceType != 'MENU'"
                ></el-button>
              </el-tooltip>
              <el-tooltip
                class="item"
                effect="dark"
                content="编辑"
                placement="left-start"
                :open-delay="500"
              >
                <el-button
                  size="mini"
                  type="primary"
                  plain
                  class="el-icon-edit"
                  @click="editMenu(row)"
                >
                </el-button>
              </el-tooltip>
              <el-tooltip
                class="item"
                effect="dark"
                content="删除"
                placement="left-start"
                :open-delay="500"
              >
                <el-button
                  size="mini"
                  type="primary"
                  class="el-icon-delete"
                  plain
                  @click="deleteMenu(row)"
                ></el-button>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
    <!-- 编辑/新建 -->
    <el-dialog
      width="520px"
      :before-close="closeHandle"
      :title="handleTitle"
      :visible.sync="isShowMenu"
      :close-on-click-modal="false"
      :destroy-on-close="true"
    >
      <div
        :class="handleTitle == '新增信息' ? 'showIcon1' : 'showIcon2'"
        @click="iconClick"
      >
        <base-icon
          v-if="varietyMenu.resourceIcon"
          :icon-class="varietyMenu.resourceIcon"
        ></base-icon>
      </div>
      <div
        class="iconLists"
        v-show="isShowIconLists"
        v-clickoutside="closeIconLists"
      >
        <base-icon
          @click="choseIcon(item)"
          v-for="(item, i) in icons"
          :key="i"
          :icon-class="item"
          class="iconitem"
        ></base-icon>
      </div>
      <el-form
        :model="varietyMenu"
        :rules="rules"
        ref="subForm"
        class="demo-ruleForm"
        :status-icon="true"
        :validate-on-rule-change="true"
      >
        <el-form-item label="上级菜单：" prop="parentId" label-width="100px">
          <im-select-tree
            v-model="varietyMenu.parentId"
            :model="parentSelectData"
            children-key="children"
            :max-content-height="500"
            fixed-position
            filter-on-input
            :width="350"
            id-key="resourceId"
            :defaultExpandedKeys="[-1]"
            title-key="resourceName"
          >
          </im-select-tree>
        </el-form-item>
        <el-form-item
          v-if="handleTitle == '新增信息'"
          label="标识："
          prop="resourceCode"
          label-width="100px"
        >
          <el-input
            style="width: 350px; margin-bottom: 3px"
            v-model="completeIndex"
            :disabled="true"
          ></el-input>
          <el-input
            style="width: 350px"
            v-model="varietyMenu.resourceCode"
            placeholder="本节点编码，保存后不可修改"
            @input="changeIndex"
          >
            <template slot="append">
              <el-button
                icon="el-icon-document-copy"
                @click="
                  copy(curRow.resourceCode + varietyMenu.resourceCode, $event)
                "
              ></el-button>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item
          v-else
          label="标识："
          prop="resourceCode"
          label-width="100px"
        >
          <el-input
            style="width: 350px"
            v-model="varietyMenu.resourceCode"
            :disabled="true"
          >
            <template slot="append">
              <el-button
                icon="el-icon-document-copy"
                @click="copy(varietyMenu.resourceCode, $event)"
              ></el-button>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="名称：" prop="name" label-width="100px">
          <el-input
            style="width: 225px"
            v-model="varietyMenu.resourceName"
          ></el-input>
        </el-form-item>
        <el-form-item label="别名：" prop="resourceInfo" label-width="100px">
          <el-input
            style="width: 225px"
            v-model="varietyMenu.resourceInfo"
          ></el-input>
        </el-form-item>
        <el-form-item label="图标：" prop="resourceIcon" label-width="100px">
          <el-input
            style="width: 350px"
            v-model="varietyMenu.resourceIcon"
          ></el-input>
        </el-form-item>
        <el-form-item label="路径：" prop="resourceUrl" label-width="100px">
          <el-input
            style="width: 350px"
            v-model="varietyMenu.resourceUrl"
          ></el-input>
        </el-form-item>
        <el-form-item label="权限：" prop="permCode" label-width="100px">
          <el-input v-model="varietyMenu.permCode"></el-input>
        </el-form-item>

        <el-row>
          <el-col :span="12">
            <el-form-item
              label="资源类型："
              prop="resourceType"
              label-width="100px"
            >
              <el-select
                v-model="varietyMenu.resourceType"
                placeholder="请选择"
              >
                <el-option label="菜单" value="MENU"></el-option>
                <el-option label="操作" value="OPERATION"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="打开方式："
              prop="openTarget"
              label-width="80px"
              v-show="varietyMenu.resourceType !== 'OPERATION'"
            >
              <el-select v-model="varietyMenu.openTarget" placeholder="请选择">
                <el-option label="Tab页" value="Tab"></el-option>
                <el-option label="新窗口" value="NewWindow"></el-option>
                <el-option label="大屏模式" value="FullScreen"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              v-show="varietyMenu.resourceType === 'OPERATION'"
              label="请求方式："
              prop="requestMethod"
              label-width="80px"
            >
              <el-select
                v-model="varietyMenu.requestMethod"
                placeholder="请选择"
              >
                <el-option label="GET" value="GET"></el-option>
                <el-option label="POST" value="POST"></el-option>
                <el-option label="PUT" value="PUT"></el-option>
                <el-option label="DELETE" value="DELETE"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="是否隐藏："
              prop="isHidden"
              label-width="100px"
            >
              <el-select v-model="varietyMenu.isHidden" placeholder="请选择">
                <el-option label="是" :value="true"></el-option>
                <el-option label="否" :value="false"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="序号：" prop="sortIndex" label-width="80px">
              <el-input-number
                v-model="varietyMenu.sortIndex"
                :min="0"
                style="width: 145px"
              ></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item
          label="外联ID："
          v-show="false"
          prop="uniteId"
          label-width="100px"
        >
          <el-input
            style="width: 350px"
            v-model="varietyMenu.uniteId"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="标签："
          v-show="false"
          prop="resourceFlag"
          label-width="100px"
        >
          <el-radio-group
            style="float: left; margin-top: 5px"
            v-model="varietyMenu.resourceFlag"
          >
            <el-radio label="none">无</el-radio>
            <el-radio label="new">new</el-radio>
            <el-radio label="beta">beta</el-radio>
            <el-radio label="hot">hot</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="saveMenuData">保 存</el-button>
        <el-button @click="closeHandle">取 消</el-button>
      </span>
    </el-dialog>

    <!-- 导入菜单数据会话框 -->
    <el-dialog
      width="520px"
      title="导入菜单"
      :visible.sync="importDialogVisible"
      :close-on-click-modal="false"
      :destroy-on-close="true"
    >
      <el-row>
        <el-col :span="10">相同配置：</el-col>
        <el-col :span="14">
          <el-radio-group v-model="importForm.conflictStrategy">
            <el-radio :label="1">终止导入</el-radio>
            <el-radio :label="2">跳过</el-radio>
            <el-radio :label="3">覆盖</el-radio>
          </el-radio-group>
        </el-col>
      </el-row>
      <br />
      <el-row>
        <el-col :span="24" style="text-align: center">
          <el-upload
            action="xx"
            class="upload-demo"
            :drag="false"
            ref="menuUpload"
            accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            :limit="1"
            :file-list="importForm.fileList"
            name="fileList"
            :auto-upload="false"
            :on-change="handleUploadChange"
            :on-remove="upFileRemoveHandler"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">请 <em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">只能上传Excel文件</div>
          </el-upload>
        </el-col>
      </el-row>
      <span slot="footer">
        <el-button
          type="primary"
          v-show="importForm.fileList.length > 0"
          @click="doMenuImport"
          >导 入</el-button
        >
        <el-button @click="importDialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import svgIcons from "./api/svgIcons";
import elementIcons from "./api/elementIcons";
import awesomeIcons from "./api/awesomeIcons";
import clipboard from "@/utils/clipboard";
import Clickoutside from "element-ui/src/utils/clickoutside";
import { menuServiceApi } from "./api/menumanage";
import { ImSelectTree } from "itsm-common";
import { mapGetters } from "vuex";

export default {
  name: "MenuManage",
  directives: {
    Clickoutside,
  },
  data() {
    return {
      isShowMenu: false,
      isExpand: false,
      importDialogVisible: false,
      tableLoading: false,
      varietyMenu: {
        parentId: null,
        resourceCode: "",
        appId: "",
        resourceInfo: "",
        resourceName: "",
        resourceIcon: "",
        resourceUrl: "",
        openTarget: "Tab",
        resourceFlag: "none",
        permCode: "",
        requestMethod: "POST",
        resourceType: "MENU",
        isHidden: false,
        sortIndex: 0,
        uniteId: "",
      },
      //当前节点
      curRow: {},
      icons: elementIcons.concat(svgIcons).concat(awesomeIcons),
      isShowIconLists: false,
      handleTitle: "",
      isChecked: false,
      checkLists: [],
      checkAll: false,
      rules: {
        resourceCode: [
          { required: true, message: "请输入本节点编码", trigger: "blur" },
          {
            pattern: /^[^\u4e00-\u9fa5]+$/,
            message: "菜单标识中不能包含中文",
            trigger: "blur",
          },
        ],
        resourceName: [
          { required: true, message: "请输入名字", trigger: "blur" },
        ],
        resourceInfo: [],
        resourceIcon: [],
        resourceUrl: [],
        openTarget: [],
        resourceFlag: [],
        isMenu: [],
        uniteId: [],
      },
      completeIndex: "",
      menuData: [],
      sourceAllMenuData: [],
      importForm: {
        conflictStrategy: 1,
        fileList: [],
      },
      searchForm: {
        keyWord: null,
      },
    };
  },
  components: { ImSelectTree },
  mounted() {
    this.getMenuData();
  },
  methods: {
    copy(text, event) {
      clipboard(text, event);
    },
    iconClick() {
      this.isShowIconLists = true;
    },
    choseIcon(item) {
      this.varietyMenu.resourceIcon = item;
      this.isShowIconLists = false;
    },
    closeIconLists() {
      if (this.isShowIconLists) {
        this.isShowIconLists = false;
      }
    },

    //搜索菜单数据
    searchMenuData() {
      if (
        this.searchForm.keyWord != null &&
        this.searchForm.keyWord.length > 0
      ) {
        this.tableLoading = true;
        menuServiceApi
          .search({ keyWord: this.searchForm.keyWord })
          .then(result => {
            let menuData = result.data;
            if (menuData != null && menuData.length === 0) {
              this.isExpand = true;
              this.handleExpand();
            } else {
              setTimeout(() => {
                this.isExpand = false;
                this.handleExpand();
              }, 200);
            }

            this.dealMenuData(menuData);
            this.tableLoading = false;
          });
      } else {
        this.getMenuData();
      }
    },

    //获得菜单数据-全部
    getMenuData() {
      this.tableLoading = true;
      menuServiceApi.findAll().then(result => {
        let menuData = result.data;
        this.dealMenuData(menuData);
        this.sourceAllMenuData = this.menuData;
        this.tableLoading = false;

        setTimeout(() => {
          this.isExpand = true;
          this.handleExpand();
        }, 200);
      });
    },

    //处理菜单数据
    dealMenuData(menuData) {
      let obj = {
        resourceId: -1,
        parentId: null,
        resourceName: "系统菜单",
        resourceCode: "",
      };
      menuData.push(obj);
      this.menuData = this.setTreeData(menuData);
      setTimeout(() => {
        this.$refs.tableMenu.doLayout();
      }, 300);
    },

    //生成树形数据
    setTreeData(source) {
      //对源数据深度克隆
      let cloneData = JSON.parse(JSON.stringify(source));
      return cloneData.filter(father => {
        //循环所有项，并添加children属性
        let branchArr = cloneData.filter(child => {
          return father.resourceId == child.parentId;
        });
        //返回每一项的子级数组
        //给父级添加一个children属性，并赋值
        branchArr.length > 0 ? (father.children = branchArr) : "";
        //返回第一层
        return father.parentId == "-1";
      });
    },
    /**
     * 新增主菜单
     */
    createdMainMenu() {
      this.handleTitle = "新增信息";
      this.curRow = {
        resourceId: -1,
        parentId: null,
        resourceName: "系统菜单",
        resourceCode: "",
      };
      this.varietyMenu.parentId = -1;
      this.completeIndex = "";
      this.isShowMenu = true;
    },
    /**
     * 新增菜单
     */
    createdMenu(row) {
      this.handleTitle = "新增信息";
      this.varietyMenu.parentId = row.resourceId;
      Object.assign(this.curRow, row);
      this.completeIndex = row.resourceCode + "_";
      this.isShowMenu = true;
    },
    /**
     * 编辑菜单
     */
    editMenu(row) {
      this.handleTitle = "编辑信息";
      row.permCode = row.permCode ? row.permCode : "";
      row.resourceType = row.resourceType ? row.resourceType : "MENU";
      row.isHidden = row.isHidden ? row.isHidden : false;
      Object.assign(this.varietyMenu, row);
      this.isShowMenu = true;
    },
    downLoad() {
      let resourceIdArray = [];
      this.checkLists.forEach(function (res) {
        resourceIdArray.push(res.resourceId);
      });
      menuServiceApi.exportData({ resourceIdList: resourceIdArray });
    },
    /**
     * 批量删除菜单
     */
    batchDeletion() {},
    deleteMenu(curRow) {
      let _this = this;
      if (curRow.children && curRow.children.length) {
        this.$message({
          showClose: true,
          message: "该菜单存在子菜单，请先删除子菜单！",
          type: "warning",
        });
        return;
      }
      this.$confirm("确定要删除该菜单吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          menuServiceApi.deleteById(curRow.resourceId).then(result => {
            if (result.status == 0) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              _this.searchMenuData();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    saveMenuData() {
      let _this = this;
      let curRow = this.curRow;
      let varietyMenu = this.varietyMenu;
      if (this.handleTitle == "新增信息") {
        let newMenu = Object.assign({}, varietyMenu);
        newMenu.appId = "";
        newMenu.parentId = curRow.resourceId;
        newMenu.resourceCode = curRow.resourceCode
          ? curRow.resourceCode + "_" + varietyMenu.resourceCode
          : varietyMenu.resourceCode;
        if (curRow.resourceId == -1) {
          newMenu.sortIndex = this.menuData.length;
        } else {
          newMenu.sortIndex = curRow.children ? curRow.children.length : 0;
        }

        this.$refs["subForm"].validate(valid => {
          if (valid) {
            menuServiceApi
              .add(JSON.stringify(newMenu))
              .then(result => {
                console.log(result);
                if (result.status == 0) {
                  _this.isShowMenu = false;
                  this.$message({
                    type: "success",
                    message: "添加成功!",
                  });
                  _this.searchForm.keyWord = null;
                  _this.searchMenuData();
                  _this.closeHandle();
                }
              })
              .catch(error => {
                this.$message({
                  type: "warning",
                  message: error.msg,
                });
              });
          } else {
            return false;
          }
        });
      } else {
        let editMenu = Object.assign({}, varietyMenu);
        this.$refs["subForm"].validate(valid => {
          if (valid) {
            menuServiceApi
              .update(JSON.stringify(editMenu))
              .then(resulte => {
                if (resulte.status == 0) {
                  _this.searchMenuData();
                  this.$message({
                    type: "success",
                    message: "编辑成功!",
                  });
                  _this.isShowMenu = false;
                  _this.closeHandle();
                }
              })
              .catch(error => {
                this.$message({
                  type: "warning",
                  message: error.msg,
                });
              });
          } else {
            return false;
          }
        });
      }
    },

    //单行选择
    selectMenu(selection) {
      this.checkLists = selection;
    },

    //是否全选
    selectAllMenu(selection) {
      this.checkAll = !this.checkAll;
      this.checkLists = [];
      if (!this.checkAll) {
        this.$refs.tableMenu.clearSelection();
      } else {
        selection.map(el => {
          if (this.checkAll) {
            this.checkLists.push(el);
          }
          if (el.children) {
            // 解决子组件没有被勾选到
            this.setChildren(el.children);
          }
        });
      }
    },

    // 编辑多个子层级
    setChildren(children) {
      if (this.checkAll) {
        this.checkLists.push(children);
      }
      children.map(row => {
        this.toggleSelection(row);
        if (row.children) {
          this.setChildren(row.children);
        }
      });
    },

    //切换表格选择
    toggleSelection(row) {
      if (row) {
        this.$nextTick(() => {
          this.$refs.tableMenu.toggleRowSelection(row, this.checkAll);
        });
      }
    },

    closeHandle() {
      this.isShowMenu = false;
      this.isShowIconLists = false;
      this.curRow = {};
      this.varietyMenu = {
        resourceCode: "",
        appId: "",
        resourceInfo: "",
        resourceName: "",
        resourceIcon: "",
        resourceUrl: "",
        openTarget: "Tab",
        resourceFlag: "none",
        permCode: "",
        resourceType: "MENU",
        isHidden: false,
        sortIndex: 0,
        uniteId: "",
      };
    },
    changeIndex() {
      if (this.curRow.resourceCode) {
        this.completeIndex =
          this.curRow.resourceCode + "_" + this.varietyMenu.resourceCode;
      } else {
        this.completeIndex = this.varietyMenu.resourceCode;
      }
    },

    //打开菜单数据导入对话框
    openMenuImportDialog() {
      this.importForm.conflictStrategy = 1;
      this.importForm.fileList = [];
      this.importDialogVisible = true;
    },

    //上传列表改变
    handleUploadChange(file) {
      this.importForm.fileList = [file];
    },

    //文件列表异常
    upFileRemoveHandler() {
      this.importForm.fileList = [];
    },

    //执行菜单数据导入
    doMenuImport() {
      let formData = new FormData();
      formData.append("file", this.importForm.fileList[0].raw);
      formData.append("conflictStrategy", this.importForm.conflictStrategy);
      menuServiceApi.uploadMenuData(formData).then(result => {
        this.importDialogVisible = false;
        this.getMenuData();
        this.$message({
          type: "success",
          message: result.msg,
        });
      });
    },

    // 是否展开table(展开与折叠切换)
    handleExpand() {
      this.isExpand = !this.isExpand;
      this.$nextTick(() => {
        this.forArr(this.menuData, this.isExpand);
      });
    },

    // 递归菜单树
    forArr(arr, isExpand) {
      arr.forEach(i => {
        this.$refs.tableMenu.toggleRowExpansion(i, isExpand);
        if (i.children) {
          this.forArr(i.children, isExpand);
        }
      });
    },

    // 表格数据高亮
    showData(val) {
      val = val + "";
      if (
        val.indexOf(this.searchForm.keyWord) !== -1 &&
        this.searchForm.keyWord !== ""
      ) {
        return val.replace(
          this.searchForm.keyWord,
          '<span style="color:#ef0505;background:#eae685;" >' +
            this.searchForm.keyWord +
            "</span>"
        );
      } else {
        return val;
      }
    },
  },
  computed: {
    ...mapGetters({
      frameStyle: "frameStyle",
    }),
    tableHeight() {
      return `calc(100vh - ${this.frameStyle.headerHeight}px - 40px)`;
    },
    parentSelectData() {
      return {
        resourceId: -1,
        parentId: -1,
        resourceName: "系统菜单",
        resourceCode: "",
        children: this.sourceAllMenuData,
      };
    },
  },
};
</script>
<style lang="scss" scoped>
.ltWidth {
  float: left;
  width: 50%;
}

.iconClass {
  font-size: 15px;
}

.formcol1 {
  width: 100px;
  text-align: left;
  float: left;
}

.formcol2 {
  outline: none;
  @include themify() {
    border: 1px solid themed("$--border-color-lighter");
  }
  width: 330px;
  height: 27px;
  float: left;
  padding: 1px 3px;
}

.showIcon1 {
  position: absolute;
  top: 200px;
  right: 68px;
  width: 80px;
  height: 80px;
  font-size: 65px;
  text-align: center;
  line-height: 80px;
  background: #409eff;
  color: #fff;
  z-index: 1000;
}

.showIcon2 {
  position: absolute;
  top: 170px;
  right: 62px;
  width: 80px;
  height: 80px;
  font-size: 65px;
  text-align: center;
  line-height: 80px;
  background: #409eff;
  color: #fff;
  z-index: 1000;
}

.iconLists {
  position: absolute;
  top: 128px;
  right: 168px;
  width: 600px;
  height: 437px;
  @include themify() {
    background: rgba(themed("$componentBg"), 1);
    border: 1px solid themed("$--border-color-lighter");
  }
  overflow-y: auto;
  z-index: 1000;
}

.iconitem {
  width: 56px;
  height: 56px;
  float: left;
  font-size: 45px;
  line-height: 56px;
  text-align: center;

  &.svg-icon {
    width: 40px;
    height: 40px;
    margin: 8px;
  }
}

.el-dialog__body {
  padding: 10px 20px;
}

.el-form-item--mini.el-form-item {
  margin-bottom: 16px;
}

input {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.MenuManage {
  ::v-deep .el-dialog__body {
    padding-right: 50px;
  }
}
</style>
