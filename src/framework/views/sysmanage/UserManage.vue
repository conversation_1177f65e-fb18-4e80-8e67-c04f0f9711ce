<template>
  <div>
    <el-container class="UserManager">
      <el-header style="height: 50px">
        <el-card>
          <div style="overflow: hidden">
            <div class="commonItem">
              <span>用户名：</span>
              <el-input type="text" v-model="queryParam.userName"></el-input>
            </div>
            <div class="commonItem">
              <span>角色：</span>
              <el-select
                v-model="queryParam.roleId"
                clearable
                placeholder="请选择"
              >
                <el-option
                  v-for="(item, i) in roleDatas"
                  :key="i"
                  :label="item.roleName"
                  :value="item.roleId"
                ></el-option>
              </el-select>
            </div>
            <div class="commonItem">
              <span>创建者：</span>
              <el-input type="text" v-model="queryParam.creatUser"></el-input>
            </div>
            <div class="commonItem">
              <el-button
                @click="searchData"
                size="mini"
                type="primary"
                class="el-icon-search"
              >
                查询
              </el-button>
              <el-button
                @click="addUser"
                size="mini"
                type="primary"
                v-has="'system:user:add'"
                class="el-icon-plus"
              >
                添加
              </el-button>
              <el-button
                v-if="false"
                size="mini"
                type="primary"
                @click="showImport = true"
                class="el-icon-upload2"
                >导入</el-button
              >
              <el-button
                size="mini"
                type="primary"
                :loading="loadingExport"
                @click="exportUsers"
                class="el-icon-download"
                >导出</el-button
              >
            </div>
          </div>
        </el-card>

        <!-- 新增/编辑用户 对话框-->
        <el-dialog
          :title="title"
          :visible.sync="dialogFormVisible"
          width="780px"
          :close-on-click-modal="false"
        >
          <div>
            <el-form
              :model="formData"
              label-width="100px"
              :rules="rules"
              :inline-message="true"
              ref="addUserForm"
              :show-message="showMessage"
            >
              <el-row :gutter="6" class="form-row">
                <el-col :span="12">
                  <el-form-item label="用户账号：" prop="userName">
                    <el-input
                      class="lable2"
                      v-model="formData.userName"
                      :disabled="
                        formData.userId != null && formData.userId.length > 0
                      "
                      maxlength="50"
                      show-word-limit
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="用户姓名：" prop="realName">
                    <el-input
                      class="lable2"
                      v-model="formData.realName"
                      maxlength="25"
                      show-word-limit
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="6" class="form-row">
                <el-col :span="12">
                  <el-form-item label="角色：">
                    <el-select
                      class="lable2"
                      v-model="formData.roleIds"
                      multiple
                      placeholder="请选择角色"
                    >
                      <el-option
                        v-for="(item, i) in roleDatas"
                        :key="i"
                        :label="item.roleName"
                        :value="item.roleId"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="数据角色：">
                    <el-select
                      class="lable2"
                      v-model="formData.dataRoleIds"
                      multiple
                      placeholder="请选择数据角色"
                    >
                      <el-option
                        v-for="(item, i) in dataRoleList"
                        :key="i"
                        :label="item.dataRoleName"
                        :value="item.dataRoleId"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="6" class="form-row">
                <el-col :span="12">
                  <el-form-item label="租户：">
                    <el-select
                      class="lable2"
                      v-model="formData.tenantIds"
                      multiple
                      placeholder="请选择租户"
                    >
                      <el-option
                        v-for="(item, i) in tenantDatas"
                        :key="i"
                        :label="item.tenantName"
                        :value="item.tenantId"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="用户有效性：">
                    <el-switch
                      style="display: block"
                      v-model="formData.enableSwitch"
                      active-color="#13ce66"
                      inactive-color="#ff4949"
                      active-value="1"
                      inactive-value="0"
                      active-text="启用"
                      inactive-text="禁用"
                    >
                    </el-switch>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="6" class="form-row">
                <el-col :span="12">
                  <el-form-item label="区域：">
                    <el-popover
                      placement="bottom"
                      width="300"
                      trigger="click"
                      ref="zoneref"
                      popper-class="custom-popper-class"
                    >
                      <span class="zoneName" slot="reference">{{
                        formData.zoneName
                      }}</span>
                      <div style="max-height: 400px; overflow: auto">
                        <el-tree
                          style="padding-top: 5px"
                          :props="treeZoneProps"
                          lazy
                          :load="loadZoneSelectNode"
                          :default-expanded-keys="[defaultZone.zoneId]"
                          :highlight-current="true"
                          :expand-on-click-node="false"
                          node-key="zoneId"
                          @node-click="handleZoneSelectClick"
                        >
                        </el-tree>
                      </div>
                    </el-popover>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="组织：" style="position: relative">
                    <el-popover
                      placement="bottom"
                      width="300"
                      trigger="click"
                      ref="deptref"
                    >
                      <span class="deptName" slot="reference">{{
                        formData.deptName
                      }}</span>
                      <div style="max-height: 400px; overflow: auto">
                        <el-tree
                          style="padding-top: 5px"
                          :data="deptTreeSelectData"
                          :props="treeDeptProps"
                          :load="loadDeptSelectNode"
                          lazy
                          :highlight-current="true"
                          :expand-on-click-node="false"
                          :default-expanded-keys="['-1', '0']"
                          node-key="deptId"
                          @node-click="handleDeptSelectClick"
                        >
                          <span
                            class="custom-tree-node"
                            slot-scope="{ node, data }"
                          >
                            <i class="el-icon-folder" v-if="!data.leaf"></i>
                            <span style="padding-left: 3px">{{
                              node.label
                            }}</span>
                          </span>
                        </el-tree>
                      </div>
                    </el-popover>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="6" class="form-row">
                <el-col :span="12">
                  <el-form-item
                    label="用户密码："
                    :prop="this.title == '添加' ? 'password' : ''"
                  >
                    <el-input
                      class="lable2"
                      v-model.trim="formData.password"
                      show-password
                      placeholder="请输入密码"
                      type="password"
                      @blur="passBlur(formData.password)"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    label="确认密码："
                    :prop="this.title == '添加' ? 'reconfirm' : ''"
                  >
                    <el-input
                      class="lable2"
                      v-model.trim="formData.reconfirm"
                      show-password
                      placeholder="请输入密码"
                      type="password"
                      @blur="
                        reconfirmBlue(formData.reconfirm, formData.password)
                      "
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="6" class="form-row">
                <el-col :span="12">
                  <el-form-item label="Email：">
                    <el-input
                      class="lable2"
                      v-model="formData.email"
                      maxlength="50"
                      show-word-limit
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="手机号：">
                    <el-input
                      class="lable2"
                      v-model="formData.cellphone"
                      maxlength="15"
                      show-word-limit
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="6" class="form-row">
                <el-col :span="12">
                  <el-form-item label="身份证号：">
                    <el-input
                      class="lable2"
                      v-model="formData.idCard"
                      maxlength="18"
                      show-word-limit
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="性别：">
                    <el-select
                      style="width: 220px"
                      v-model="formData.gender"
                      placeholder="请选择"
                    >
                      <el-option label="男" value="male"></el-option>
                      <el-option label="女" value="female"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="6" class="form-row">
                <el-col :span="12">
                  <el-form-item label="出生日期：">
                    <el-date-picker
                      style="width: 220px"
                      v-model="formData.birthday"
                      type="date"
                      placeholder="选择日期"
                      value-format="yyyy-MM-dd"
                    ></el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="地址：">
                    <el-input
                      class="lable2"
                      v-model="formData.address"
                      maxlength="100"
                      show-word-limit
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="6" class="form-row">
                <el-col :span="12">
                  <el-form-item label="邮编：">
                    <el-input
                      class="lable2"
                      v-model="formData.postcode"
                      maxlength="25"
                      show-word-limit
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="固定电话：">
                    <el-input
                      class="lable2"
                      v-model="formData.telephone"
                      maxlength="25"
                      show-word-limit
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="6" class="form-row">
                <el-col :span="12">
                  <el-form-item label="QQ：">
                    <el-input
                      class="lable2"
                      v-model="formData.qq"
                      maxlength="10"
                      show-word-limit
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="有效期至：">
                    <el-date-picker
                      v-model="formData.expireTime"
                      type="datetime"
                      placeholder="选择日期时间"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      format="yyyy-MM-dd HH:mm:ss"
                      style="width: 220px"
                    ></el-date-picker>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>

          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="handleUserSave">保 存</el-button>
            <el-button @click="dialogFormVisible = false">取 消</el-button>
          </div>
        </el-dialog>

        <el-dialog
          title="用户批量导入"
          :visible.sync="showImport"
          width="30%"
          ref="uploadDialog"
        >
          <p>
            <el-button type="text">下载模板</el-button>
          </p>
          <div>
            <span style="float: left">上传路径：</span>
            <div style="float: left">
              <el-upload class="upload-demo" action="111111">
                <el-button size="small" type="primary">点击上传</el-button>
              </el-upload>
            </div>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button @click="dialogVisible = false">取 消</el-button>
            <el-button type="primary">导入</el-button>
          </span>
        </el-dialog>
      </el-header>
      <el-container>
        <el-aside width="240px" style="overflow-x: auto; overflow-y: auto">
          <el-card
            :style="{
              height: rightBodyHeight,
            }"
          >
            <el-tree
              style="padding-top: 5px; display: inline-block; min-width: 100%"
              :data="deptTreeData"
              :props="treeDeptProps"
              :load="loadDeptNode"
              lazy
              :highlight-current="true"
              :default-expanded-keys="['-1']"
              node-key="deptId"
              ref="treeDept"
              @node-click="handleDeptClick"
            >
              <span class="custom-tree-node" slot-scope="{ node, data }">
                <i class="el-icon-folder" v-if="!data.leaf"></i>
                <span style="padding-left: 3px">{{ node.label }}</span>
              </span>
            </el-tree>
          </el-card>
        </el-aside>
        <el-main>
          <el-card
            :style="{
              height: rightBodyHeight,
            }"
          >
            <el-table
              v-loading="loadingTable"
              :data="tableData"
              :style="{ width: '100%' }"
              :height="tableHeight"
              row-key="userId"
              ref="tableUser"
              border
              stripe
              @sort-change="handleSortChange"
            >
              <el-table-column
                prop="userName"
                label="用户账号"
                :sortable="'custom'"
              >
              </el-table-column>
              <el-table-column
                prop="realName"
                label="姓名"
                :sortable="'custom'"
              >
              </el-table-column>
              <el-table-column
                prop="deptName"
                label="组织"
                :sortable="'custom'"
              >
              </el-table-column>
              <el-table-column prop="roleName" label="角色" width="240">
              </el-table-column>
              <el-table-column prop="tenantNames" label="租户" width="160">
              </el-table-column>
              <el-table-column
                prop="zoneName"
                label="区域"
                :sortable="'custom'"
              >
              </el-table-column>
              <el-table-column
                prop="cellphone"
                label="手机号"
                :sortable="'custom'"
              >
              </el-table-column>
              <el-table-column
                prop="loginTime"
                label="最后登录时间"
                :sortable="'custom'"
                width="140"
              >
                <template slot-scope="{ row }">{{
                  row.loginTime | formatDate
                }}</template>
              </el-table-column>
              <el-table-column
                prop="createUser"
                label="创建者"
                :sortable="'custom'"
              >
              </el-table-column>
              <el-table-column
                width="110"
                align="center"
                label="用户有效性"
                prop="enableSwitch"
                :sortable="'custom'"
              >
                <template slot-scope="{ row }">
                  <el-tag :type="row.enableSwitch === '0' ? 'info' : 'success'">
                    {{ row.enableSwitch === "1" ? "启用" : "禁用" }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column width="120" align="left" label="操作">
                <template slot-scope="{ row }">
                  <div>
                    <el-tooltip
                      class="item"
                      effect="dark"
                      content="编辑"
                      placement="left-start"
                      :open-delay="800"
                    >
                      <el-button
                        type="primary"
                        size="mini"
                        icon="el-icon-edit"
                        plain
                        @click="editUser(row)"
                        v-has="'system:user:edit'"
                      ></el-button>
                    </el-tooltip>
                    <el-tooltip
                      class="item"
                      effect="dark"
                      content="删除"
                      placement="left-start"
                      :open-delay="800"
                    >
                      <el-button
                        type="primary"
                        size="mini"
                        icon="el-icon-delete"
                        plain
                        v-show="
                          row.deptId !== '-2' ||
                          userInfo.zoneType === 'UNLIMITED'
                        "
                        @click="deleteUser(row)"
                        v-has="'system:user:del'"
                      ></el-button>
                    </el-tooltip>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination
              v-loading="loadingPage"
              background
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="pageIndex"
              :page-sizes="[10, 15, 20, 30, 50]"
              :page-size="pageSize"
              layout="->, sizes, prev, pager, next, jumper, slot, total"
              :total="totalElements"
              class="pagination"
            ></el-pagination>
          </el-card>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import moment from "moment";
import { sm3 } from "sm-crypto";
import { deptServiceApi } from "./api/deptmanage";
import { zoneServiceApi } from "./api/zone";
import { dataRoleServiceApi } from "./api/datarolemanage";
import { userServiceApi } from "./api/usermanage";
import { systemTenantApi } from "./api/tenant";
import { apifindAll as findAllRoles } from "./api/rolemanage";
export default {
  name: "UserManager",
  data() {
    return {
      tableData: [],
      totalElements: 0,
      queryParam: {
        userName: "",
        roleId: "",
        creatUser: "",
        deptId: "-1",
      },
      pageIndex: 1,
      pageSize: 15,
      sortIndex: -1,
      sortType: "ASC",
      treeDeptProps: {
        label: "deptName",
        isLeaf: "leaf",
      },
      deptTreeData: [],
      deptTreeSelectData: [],
      treeZoneProps: {
        label: "zoneName",
        isLeaf: "leaf",
      },
      defaultZone: {},
      dialogFormVisible: false,
      title: "",
      formData: {},
      rules: {
        userName: [{ required: true, message: "请输入账号", trigger: "blur" }],
        realName: [
          { required: true, message: "请输入用户名", trigger: "blur" },
        ],
        password: [
          { required: true, message: "请输入密码", trigger: "blur" },
          {
            required: true,
            min: 6,
            max: 18,
            message: "长度在 8 到 18 个字符",
            trigger: "blur",
          },
        ],
        reconfirm: [{ required: true, message: "请确认密码", trigger: "blur" }],
      },
      roleDatas: [],
      tenantDatas: [],
      dataRoleList: [],
      showMessage: false,
      showImport: false,
      fileExcel: "",
      loadingTable: false,
      loadingPage: false,
      loadingExport: false,
    };
  },
  computed: {
    ...mapGetters(["userInfo", "frameStyle"]),
    rightBodyHeight() {
      return `calc(100vh - ${this.frameStyle.headerHeight}px - 50px)`;
    },
    tableHeight() {
      return `calc(100vh - ${this.frameStyle.headerHeight}px - 110px)`;
    },
  },
  created() {
    this.getRoles();
    this.getZones();
    this.getDataRoles();
    this.searchData();
    this.getTenants();
  },
  mounted() {
    this.getDepartments();
  },
  methods: {
    getDepartments() {
      this.deptTreeData = [
        {
          deptId: "-1",
          deptName: "全部组织",
          children: [],
          leaf: false,
        },
      ];
    },
    loadDeptNode(node, resolve) {
      if (node.key == null) {
        return;
      }
      deptServiceApi.findMyDeptsByParentId(node.key).then(res => {
        if (node.key === "-1") {
          this.deptTreeSelectData = res.data;
        }
        resolve(res.data);
      });
    },
    loadDeptSelectNode(node, resolve) {
      let parentId = node.key == null ? "-1" : node.key;
      deptServiceApi.findMyDeptsByParentId(parentId).then(res => {
        resolve(res.data);
      });
    },
    handleDeptClick(data) {
      this.queryParam.deptId = data.deptId;
      this.$refs.treeDept.setCurrentKey(data.deptId);
      this.searchData();
    },
    handleDeptSelectClick(data) {
      this.formData.deptName = data.deptName;
      this.formData.deptId = data.deptId;
      this.$refs.deptref.doClose();
    },
    handleZoneSelectClick(data) {
      this.formData.zoneName = data.zoneName;
      this.formData.zoneId = data.zoneId;
      this.$refs.zoneref.doClose();
    },
    getZones() {
      zoneServiceApi.findByParentId().then(result => {
        if (result.status == 0 && result.data.length > 0) {
          this.defaultZone = result.data[0];
        }
      });
    },
    loadZoneSelectNode(node, resolve) {
      let parentId = node == null ? "" : node.key;
      zoneServiceApi.findByParentId(parentId).then(res => {
        resolve(res.data);
      });
    },
    getDataRoles() {
      dataRoleServiceApi.findAll().then(dataroles => {
        if (dataroles.status == 0) {
          this.dataRoleList = dataroles.data;
        }
      });
    },
    getRoles() {
      findAllRoles().then(result => {
        if (result.status == 0) {
          this.roleDatas = result.data;
        }
      });
    },

    //查询租户数据
    getTenants() {
      systemTenantApi.getAllSystemTenantVoList().then(result => {
        this.tenantDatas = result.data;
      });
    },

    getParam() {
      let param = {
        param1: JSON.stringify(this.queryParam),
        param2: "",
        returnCount: false,
        pageIndex: this.pageIndex,
        pageSize: this.pageSize,
        sortField: "",
        sortIndex: this.sortIndex,
        sortType: this.sortType,
      };
      return param;
    },
    //查询
    searchData() {
      this.loadingTable = true;
      this.pageIndex = 1;
      let param = this.getParam();
      userServiceApi.search(param).then(res => {
        this.tableData = res.data.rows;
        setTimeout(() => {
          this.$refs.tableUser.doLayout();
        }, 200);
        param.returnCount = true;
        this.loadingTable = false;
        userServiceApi
          .search(param)
          .then(result => (this.totalElements = result.data));
      });
    },
    exportUsers() {
      this.loadingExport = true;
      let param = this.getParam();
      userServiceApi.exportExcel(param).then(() => {
        this.loadingExport = false;
        this.$message({
          message: "导出完毕。",
          type: "success",
        });
      });
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageIndex = 1;
      let param = this.getParam();
      userServiceApi.search(param).then(res => {
        this.tableData = res.data.rows;
      });
    },
    handleCurrentChange(val) {
      this.pageIndex = val;
      let param = this.getParam();
      this.loadingTable = true;
      userServiceApi.search(param).then(res => {
        this.tableData = res.data.rows;
        this.loadingTable = false;
      });
    },
    handleSortChange(sortParam) {
      this.pageIndex = 1;
      let param = this.getParam();
      param.sortField = sortParam.prop;
      param.sortType = sortParam.order === "ascending" ? "ASC" : "DESC";
      this.loadingTable = true;
      userServiceApi.search(param).then(res => {
        this.loadingTable = false;
        this.tableData = res.data.rows;
      });
    },
    //添加
    addUser() {
      this.formData = {
        userId: null,
        userName: "", //用户账号
        realName: "", //用户姓名
        address: "", //地址
        birthday: "", //生日
        email: "", //邮箱
        telephone: "", //固话
        idCard: "", //身份证号
        cellphone: "", //电话
        qq: "", //qq
        postcode: "", //邮编
        themeName: "blue", //皮肤
        deptId: this.deptTreeSelectData[0].deptId, //部门id
        deptName: this.deptTreeSelectData[0].deptName,
        gender: "male", //性别
        password: "", //密码
        reconfirm: "", //确认密码
        expireTime: moment().add(10, "y").format("YYYY-MM-DD HH:mm:ss"), //有效期
        roleIds: [], //角色
        dataRoleId: "-1", //数据角色
        zoneId: this.defaultZone.zoneId, //区域
        zoneName: this.defaultZone.zoneName,
        enableSwitch: "1",
      };
      this.title = "添加";
      this.showMessage = true;
      this.dialogFormVisible = true;
    },
    //保存添加
    handleUserSave() {
      this.$refs.addUserForm.validate(valid => {
        if (valid) {
          let pwd =
            this.formData.password == "" ? "" : sm3(this.formData.password);
          let userEdit = {
            user: this.formData,
            pwd: pwd,
          };
          userServiceApi.add(userEdit).then(result => {
            if (result.status == 0) {
              this.$message({
                message: "保存成功！！！",
                type: "success",
              });
              this.dialogFormVisible = false;
              let param = this.getParam();
              userServiceApi.search(param).then(res => {
                this.tableData = res.data.rows;
                if (this.formData.userId == null) {
                  param.returnCount = true;
                  userServiceApi
                    .search(param)
                    .then(result => (this.totalElements = result.data));
                }
              });
            }
          });
        } else {
          console.log(this.formData);
          return false;
        }
      });
    },
    //编辑
    editUser(row) {
      this.title = "编辑用户";
      this.dialogFormVisible = true;
      userServiceApi.findById(row["userId"]).then(userResult => {
        if (userResult.status == 0) {
          let data = userResult.data;
          this.formData = { ...data, roleIds: [...new Set(data.roleIds)] };
          this.$set(this.formData, "password", "");
          this.$set(this.formData, "reconfirm", "");
        }
      });
    },
    //删除
    deleteUser(row) {
      this.$confirm("确定要删除用户“" + row.userName + "”吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        userServiceApi.deleteById(row["userId"]).then(result => {
          if (result.status == 0) {
            let param = this.getParam();
            param.param2 = Math.random();
            userServiceApi.search(param).then(res => {
              this.tableData = res.data.rows;
              param.returnCount = true;
              userServiceApi
                .search(param)
                .then(result => (this.totalElements = result.data));
            });
            this.$message({
              message: "删除用户成功！！！",
              type: "success",
            });
          }
        });
      });
    },
    //密码校验
    passBlur(str) {
      str = str.trim();
      let reg = /^(?=.*[A-Za-z])(?=.*\d)(?=.*[$@$!%*#?&])[A-Za-z\d$@$!%*#?&]{8,}$/;
      if (str != "" && !reg.test(str)) {
        this.$message({
          message: "密码必须由至少8位的字母、数字、特殊字符组成",
          type: "warning",
        });
      }
      if (this.title == "添加") {
        if (str == "") {
          this.$message({
            message: "添加新用户密码不能为空",
            type: "warning",
          });
        }
      }
    },
    //确认密码校验
    reconfirmBlue(reconfirm, password) {
      reconfirm = reconfirm.trim();
      password = password.trim();
      if (reconfirm != password) {
        this.$message({
          message: "请确认两次输入密码是否相同！",
          type: "warning",
        });
      }
    },
    beforUpload(file) {
      console.log(file);
      let name = file.name;
      let arr = name.split(".");
      let str = arr[arr.length - 1];
      if (str != "xlsx" && str != "xls") {
        this.$message({
          message: "请上传excel格式文档",
          type: "warning",
        });
        return false;
      }
    },
    saveUpload() {
      let formData = new FormData();
      formData.append("fileExcel", this.fileExcel);
      userServiceApi.importExcel(formData).then(result => {
        console.log(result);
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.UserManager {
  .el-main {
    padding: 0;
    margin-left: 6px;
    margin-right: 2px;
  }
  .commonItem {
    float: left;
    height: 30px;
    line-height: 30px;
    margin-left: 8px;
    > span {
      float: left;
    }
    > .el-input {
      float: left;
      width: 150px;
    }
  }

  .el-header {
    padding: 0;
  }
  .el-form .el-form-item {
    margin-bottom: 6px;
  }
  .lable2 {
    width: 220px;
  }
  ::v-deep.el-dialog__body {
    padding: 17px 20px;
  }

  .dept {
    position: absolute;
    top: 100%;
    left: 30px;
    z-index: 1000;
    @include themify() {
      border: 1px solid themed("$--border-color-base");
    }
  }
  .deptName,
  .zoneName {
    width: 220px;
    display: inline-block;
    @include themify() {
      border: 1px solid themed("$--border-color-base");
    }
    box-sizing: border-box;
    border-radius: 4px;
    font-size: 12px;
    padding: 0 15px;
  }
  .boxHeight {
    height: calc(100vh - 200px);
  }
  .el-card {
    overflow: auto;
  }
  .form-row {
    padding-bottom: 5px;
  }
  .pagination {
    margin-top: 8px;
  }
}
</style>
