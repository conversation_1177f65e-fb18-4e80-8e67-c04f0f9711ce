<template>
  <div class="DepartmentManage">
    <div style="width: 100%; text-align: right">
      <el-button
        size="mini"
        type="primary"
        class="el-icon-plus"
        @click="handleAddRootDept"
        >添加一级组织</el-button
      >
    </div>
    <el-table
      :data="deptData"
      style="width: 100%"
      row-key="deptId"
      border
      lazy
      ref="tableDept"
      :load="loadChildren"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
    >
      <el-table-column
        prop="deptName"
        label="组织名称"
        width="360"
      ></el-table-column>
      <el-table-column prop="deptType" label="组织类型" width="150">
      </el-table-column>
      <el-table-column prop="deptCode" label="组织编码" width="150">
      </el-table-column>
      <el-table-column prop="sortIndex" label="序号" width="150" align="center">
      </el-table-column>
      <el-table-column prop="deptInfo" label="组织描述"> </el-table-column>
      <el-table-column label="" prop="opeation" width="200" align="center">
        <template slot-scope="{ row }">
          <el-tooltip
            class="item"
            effect="dark"
            content="新建"
            placement="left-start"
          >
            <el-button
              size="mini"
              type="primary"
              class="el-icon-plus"
              plain
              @click="handleAddDept(row)"
            ></el-button
          ></el-tooltip>
          <el-tooltip
            class="item"
            effect="dark"
            content="编辑"
            placement="left-start"
          >
            <el-button
              size="mini"
              type="primary"
              plain
              class="el-icon-edit"
              @click="handleEditDept(row)"
            >
            </el-button
          ></el-tooltip>
          <el-tooltip
            class="item"
            effect="dark"
            content="删除"
            placement="left-start"
          >
            <el-button
              size="mini"
              type="primary"
              class="el-icon-delete"
              :disabled="!row.leaf"
              plain
              @click="handleDelDept(row)"
            ></el-button
          ></el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      :title="isAdd ? '新建组织' : '编辑组织'"
      :visible.sync="dialogVisible"
      width="480px"
      :close-on-click-modal="false"
    >
      <el-form
        label-width="100px"
        :model="formData"
        ref="deptForm"
        :rules="deptFormRules"
      >
        <el-form-item label="组织名称" prop="deptName" required>
          <el-input
            style="width: 100%"
            v-model.trim="formData.deptName"
            maxlength="25"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="组织类型">
          <el-input
            style="width: 100%"
            v-model.trim="formData.deptType"
            maxlength="25"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="组织编码">
          <el-input
            style="width: 100%"
            v-model.trim="formData.deptCode"
            maxlength="30"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="省份编码">
          <el-input
            style="width: 100%"
            v-model="formData.zoneProvId"
            maxlength="30"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="地市编码">
          <el-input
            style="width: 100%"
            v-model="formData.zoneCityId"
            maxlength="30"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-form-item label="序号" prop="sortIndex">
          <el-input-number
            v-model="formData.sortIndex"
            :min="0"
            style="width: 100%"
          ></el-input-number>
        </el-form-item>
        <el-form-item label="组织描述">
          <el-input
            type="textarea"
            :autosize="{ minRows: 3, maxRows: 5 }"
            v-model="formData.deptInfo"
            maxlength="100"
            show-word-limit
          >
          </el-input>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSave"> 确 定 </el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { deptServiceApi } from "./api/deptmanage";

export default {
  name: "DeptManage",
  data() {
    return {
      dialogVisible: false,
      isAdd: true,
      formData: {},
      deptData: [],
      parentRow: [],
      maps: new Map(),
      deptFormRules: {
        deptName: [{ required: true, message: "组织名称不能为空" }],
      },
    };
  },
  created() {
    this.findDeptData("-1");
  },
  methods: {
    findDeptData(parentId) {
      deptServiceApi.findByParentId(parentId).then(res => {
        res.data.forEach(element => {
          element["hasChildren"] = !element.leaf;
          if (element.children == null) {
            element.children = [];
          }
        });
        this.deptData = res.data;
      });
    },
    loadChildren(row, treeNode, resolve) {
      this.maps.set(row.deptId, { row, treeNode, resolve });
      this.displayChildren(row.deptId, resolve);
    },
    displayChildren(parentId, resolve) {
      deptServiceApi.findByParentId(parentId).then(res => {
        res.data.forEach(element => {
          element["hasChildren"] = !element.leaf;
          if (element.children == null) {
            element.children = [];
          }
        });
        resolve(res.data);
      });
    },
    handleSave() {
      this.$refs.deptForm.validate(valid => {
        if (valid) {
          deptServiceApi.save(this.formData).then(res => {
            this.$message({
              message: "保存成功。",
              type: "success",
            });
            if (this.isAdd) {
              if (this.parentRow == null) {
                this.deptData.push(res.data);
              } else {
                let childList = this.$refs.tableDept.store.states
                  .lazyTreeNodeMap[this.parentRow.deptId];
                this.parentRow.leaf = false;
                this.parentRow.hasChildren = true;
                if (childList != null) {
                  childList.push(res.data);
                  this.$set(
                    this.$refs.tableDept.store.states.lazyTreeNodeMap,
                    this.parentRow.deptId,
                    childList
                  );
                }
              }
            }
            this.dialogVisible = false;
          });
        }
      });
    },
    handleAddRootDept() {
      this.handleAddDept(null);
    },
    handleAddDept(row) {
      this.isAdd = true;
      this.parentRow = row;
      this.formData = {
        deptId: "",
        deptName: "",
        parentId: row == null ? "-1" : row.deptId,
        deptCode: "",
        deptType: "",
        zoneProvId: "",
        zoneCityId: "",
        leaf: true,
        deptInfo: "",
        children: [],
      };
      this.dialogVisible = true;
    },
    handleEditDept(row) {
      this.isAdd = false;
      this.formData = row;
      this.dialogVisible = true;
    },
    handleDelDept(dept) {
      this.$confirm(
        "此操作将永久删除组织 “" + dept.deptName + "”, 确定要删除?",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      ).then(() => {
        deptServiceApi.deleteById(dept.deptId).then(() => {
          this.$message({
            message: "删除成功。",
            type: "success",
          });
          if (dept.parentId === "-1") {
            for (let i = 0; i < this.deptData.length; i++) {
              if (this.deptData[i].deptId === dept.deptId) {
                this.deptData.splice(i, 1);
                break;
              }
            }
          } else {
            let childList = this.$refs.tableDept.store.states.lazyTreeNodeMap[
              dept.parentId
            ];
            for (let i = 0; i < childList.length; i++) {
              if (childList[i].deptId === dept.deptId) {
                childList.splice(i, 1);
                break;
              }
            }
            if (childList.length === 0) {
              this.maps.get(dept.parentId).row.hasChildren = false;
              this.maps.get(dept.parentId).row.leaf = true;
            }
          }
        });
      });
    },
  },
};
</script>
<style lang="scss" scoped></style>
