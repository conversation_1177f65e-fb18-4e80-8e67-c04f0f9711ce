import * as axios from "@/utils/axios";

const basePath = "/framework/sysmanage";

export const systemTenantApi = {
  //查询租户信息
  getAllSystemTenantVoList() {
    return axios.getJson(basePath + "/tenant/getAllSystemTenantVoList");
  },

  //保存租户信息
  save(params) {
    return axios.postJson(basePath + "/tenant/saveSystemTenant", params);
  },

  //删除租户信息
  delete(params) {
    return axios.getJson(basePath + "/tenant/deleteSystemTenant", params);
  },

  //查询用户信息
  searchUserInfo(params) {
    return axios.postJson(basePath + "/tenant/searchUserInfo", params);
  },

  //查询待添加用户
  searchAddUser(params) {
    return axios.postJson(basePath + "/tenant/searchNeedAddUser", params);
  },

  //添加用户到租户
  addUserRelations(params) {
    return axios.postJson(basePath + "/tenant/addUserToTenant", params);
  },

  //从租户下移除用户
  removeUserRelation(params) {
    return axios.getJson(basePath + "/tenant/removeUserFromTenant", params);
  },
};
