const req = require.context(
  "@/assets/icons/svg/",
  false,
  /^(\.\/menu_).*?\.svg$/
);
const requireAll = requireContext => requireContext.keys();

const req2 = require.context("@plugin/", true, /.*?\/menu_.*?\.svg$/);
console.log(req2.keys());
const re = /\.\/(.*)\.svg/;

const svgIcons = requireAll(req).map(i => {
  return i.match(re)[1];
});

const svgIcons1 = requireAll(req2).map(i => {
  return i.slice(i.lastIndexOf("/") + 1, -4);
});

export default svgIcons.concat(svgIcons1);
