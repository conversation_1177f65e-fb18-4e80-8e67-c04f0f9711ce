import { get, postJson, delJson } from "@/utils/axios";

let urlList = {
  findAllUrl: "/framework/sysmanage/roles", //获取角色表格
  resourceUrl: "/framework/sysmanage/resources", //获取菜单
  addRoleUrl: "/framework/sysmanage/roles", //添加角色
  editRoleUrl: "/framework/sysmanage/roles", //编辑角色
  deleteRoleUrl: "/framework/sysmanage/roles", //删除角色
  searchIdsUrl: "/framework/sysmanage/roles/resource", //查询角色权限
  saveJurUrl: "/framework/sysmanage/roles/resource", //保存角色权限
};

const apifindAll = params => get(urlList.findAllUrl, params);
const apiResource = params => get(urlList.resourceUrl, params);
const apiaddRole = params => postJson(urlList.addRoleUrl, params);
const apieditRole = params => get(urlList.editRoleUrl + "/" + params);
const apideleteRole = params => delJson(urlList.deleteRoleUrl + "/" + params);
const apisearchIds = params => get(urlList.searchIdsUrl + "/" + params);
const apisaveJur = params => postJson(urlList.saveJurUrl, params);

export {
  apifindAll,
  apiResource,
  apiaddRole,
  apieditRole,
  apideleteRole,
  apisearchIds,
  apisaveJur,
};
