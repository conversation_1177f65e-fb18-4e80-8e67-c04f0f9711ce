import { getJson, postJson, del } from "@/utils/axios";

const sysSetting = "/framework/sysmanage/setting";

const url = {
  list: sysSetting + "/list", //配置列表
  save: sysSetting + "/save", //配置保存
  del: sysSetting + "/del", //配置删除
};

const apiGetSettingList = params => getJson(url.list, params);
const apiSaveSetting = params => postJson(url.save, params);
const apiDelSetting = params => del(url.del, params);

export { apiGetSettingList, apiSaveSetting, apiDelSetting };
