import * as axios from "@/utils/axios";

const basePath = "/framework/sysmanage/users";

export const userServiceApi = {
  findById(userId) {
    return axios.get(basePath + "/" + userId);
  },
  search(params) {
    return axios.post(basePath + "/list", params);
  },
  add(user) {
    return axios.postJson(basePath, user);
  },
  update(user) {
    return axios.putJson(basePath, user);
  },
  deleteById(userId) {
    return axios.del(basePath + "/" + userId);
  },
  importExcel(params) {
    return axios.postJson(basePath + "/import", params);
  },
  exportExcel(params) {
    return axios.postBlob(basePath + "/export", params);
  },
};
