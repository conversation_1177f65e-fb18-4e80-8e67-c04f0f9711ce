import * as axios from "@/utils/axios";

const basePath = "/framework/sysmanage/resources";

export const menuServiceApi = {
  findById(resourceId) {
    return axios.get(basePath + "/" + resourceId);
  },
  findAll() {
    return axios.get(basePath);
  },
  add(resource) {
    return axios.postJson(basePath, resource);
  },
  update(resource) {
    return axios.putJson(basePath, resource);
  },
  deleteById(resourceId) {
    return axios.del(basePath + "/" + resourceId);
  },

  //导出菜单数据
  exportData(params) {
    return axios.postJsonBlob(basePath + "/export", params);
  },

  //上传导入菜单数据
  uploadMenuData(params) {
    return axios.postFormData(basePath + "/import", params);
  },

  //搜索菜单数据
  search(params) {
    return axios.post(basePath + "/search", params);
  },
};
