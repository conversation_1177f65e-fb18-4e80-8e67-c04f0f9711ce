import * as axios from "@/utils/axios";

const basePath = "/framework/sysmanage/depts";

export const deptServiceApi = {
  findById(deptId) {
    return axios.get(basePath + "/" + deptId);
  },
  findByParentId(pId) {
    return axios.get(basePath, { parentId: pId });
  },
  findMyDeptsByParentId(pId) {
    return axios.get(basePath + "/mydept/list", { parentId: pId });
  },
  save(dept) {
    return axios.postJson(basePath, dept);
  },
  deleteById(deptId) {
    return axios.del(basePath + "/" + deptId);
  },
};
