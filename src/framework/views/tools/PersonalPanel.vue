<template>
  <component
    :is="componentId"
    class="personal-panel"
    :style="styleObj"
  ></component>
</template>

<script>
import UserMenu from "./UserMenu";
import UserText from "./UserText";
import { mapGetters } from "vuex";

export default {
  name: "PersonalPanel",
  components: {
    UserMenu,
    UserText,
  },
  props: {
    height: {
      type: String,
      default: "50px",
      required: false,
    },
  },
  data() {
    return {
      // componentId: "UserMenu",
    };
  },
  computed: {
    ...mapGetters(["personalPanelShowText"]),
    styleObj() {
      return {
        height: this.height,
        lineHeight: this.height,
      };
    },
    componentId() {
      let componentId;
      switch (this.personalPanelShowText) {
        case true:
          componentId = "UserText";
          break;
        default:
          componentId = "UserMenu";
          break;
      }
      return componentId;
    },
  },
};
</script>

<style lang="scss" scoped>
.personal-panel {
  // display: inline-block;
  box-sizing: border-box;
  float: right;
  overflow: hidden;
  padding: 0 7px;
  text-align: right;
}
</style>
