<template>
  <div class="logo inline-block" :style="styleObj">
    <router-link :to="{ name: 'home' }" class="logo-link inline-block">
      <base-icon
        v-if="!isCollapse"
        :icon-class="logo"
        class="logo-icon logo-icon-text"
        :class="{ 'logo-storage': isStorage }"
      ></base-icon>
      <base-icon
        v-else
        :icon-class="settings.logoMini"
        class="logo-icon"
      ></base-icon>
      <h1 v-if="showTitle" class="logo-title inline-block">{{ title }}</h1>
    </router-link>
  </div>
</template>

<script>
import settings from "@/settings";

export default {
  name: "Logo",
  props: {
    height: {
      type: String,
      default: "50px",
      required: false,
    },
    title: {
      type: String,
      default: "框架",
      required: false,
    },
    showTitle: {
      type: Boolean,
      default: false,
      required: false,
    },
    isCollapse: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  data() {
    return {
      styleObj: {
        height: this.height,
        lineHeight: this.height,
      },
      logo: "",
      isStorage: false,
    };
  },
  computed: {
    settings() {
      return settings;
    },
  },
  created() {
    this.logo = sessionStorage.getItem("logoImg")
      ? sessionStorage.getItem("logoImg")
      : this.settings.logo;
    this.isStorage = sessionStorage.getItem("logoImg") ? true : false;
  },
};
</script>

<style lang="scss" scoped>
.inline-block {
  display: inline-block;
}
.logo {
  box-sizing: border-box;
  padding: 0 8px;
  width: 100%;
  .logo-link {
    width: 100%;
    height: 100%;
    text-align: center;
    color: inherit;
    .logo-img {
      vertical-align: middle;
      height: 40px;
    }
    .logo-icon {
      font-size: 35px;
      vertical-align: middle;
      // color: $frameMenu-text-color;
      &.logo-icon-text {
        width: 150px;
        // height: 50px;
      }
      &.logo-storage {
        mask-size: contain !important;
      }
    }
    .logo-title {
      margin: 0;
      padding: 0;
      // color: $frameMenu-text-color;
      @include themify() {
        font-size: themed("$--font-size-medium");
      }
    }
  }
}
</style>
