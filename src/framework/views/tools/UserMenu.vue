<template>
  <div class="user-menu">
    <span class="usermenu-item">
      <el-tooltip content="全屏">
        <base-icon
          icon-class="full-screen"
          @click.prevent.stop="handleFullScreen"
        ></base-icon>
      </el-tooltip>
    </span>
    <!-- <span class="usermenu-item">
      <base-icon icon-class="el-icon-message"></base-icon>
    </span> -->
    <span class="usermenu-item">
      <el-tooltip content="系统设置">
        <base-icon
          icon-class="el-icon-setting"
          @click.prevent.stop="handleSettings"
        ></base-icon>
      </el-tooltip>
    </span>
    <el-dropdown
      trigger="hover"
      placement="bottom-end"
      size="medium"
      @command="handleCommand"
    >
      <span class="usermenu-item">
        <i class="el-icon-user"></i>
      </span>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item
          ><i class="el-icon-s-custom el-icon--left"></i
          >{{ userRealName }}</el-dropdown-item
        >
        <el-dropdown-item divided></el-dropdown-item>
        <template v-for="item in dropLists">
          <el-dropdown-item
            v-if="item.permisssion"
            :key="item.key"
            :command="item.key"
            v-has="item.permisssion"
            ><base-icon
              :icon-class="item.icon"
              class="el-icon--left"
              style="vertical-align: text-top"
            />{{ item.title }}</el-dropdown-item
          >
          <el-dropdown-item v-else :key="item.key" :command="item.key"
            ><base-icon
              :icon-class="item.icon"
              class="el-icon--left"
              style="vertical-align: text-top"
            />{{ item.title }}</el-dropdown-item
          >
        </template>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
import { mapActions, mapGetters } from "vuex";
import { apiLogout } from "../privateSetting/api/logout";

export default {
  name: "UserMenu",
  data() {
    return {
      dropLists: [
        // {
        //   key: "user",
        //   title: "个人中心",
        // },
        {
          key: "password",
          title: "修改密码",
          icon: "el-icon-lock",
          permisssion: "personal:password",
        },
        {
          key: "logout",
          title: "退出登录",
          icon: "logout",
        },
      ],
      msgCount: 17,
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
    badeHidden() {
      return this.msgCount < 1;
    },
    userRealName() {
      return this.userInfo.realName ?? "";
      // return "可视化";
    },
  },
  methods: {
    ...mapActions(["settings/toggleShowSettings", "settings/toggleFullScreen"]),
    handleFullScreen() {
      this["settings/toggleFullScreen"]();
    },
    handleSettings() {
      this["settings/toggleShowSettings"]();
    },
    handleCommand(command) {
      if (command === "logout") {
        this.logout();
      }
      if (command === "password") {
        this.$router.push({ name: "PersonalSet_ChangePwd" });
      }
    },
    // logout() {
    //   this.$router.replace({ name: "Login" });
    //   this.$store.dispatch("user/logout");
    // },
    //退出登录
    logout() {
      console.log(1111111111);
      apiLogout()
        .then(res => {
          console.log(res);
          this.$router.replace({ name: "Login" });
          this.$store.dispatch("user/logout");
        })
        .catch(e => {
          console.log(e);
          this.$router.replace({ name: "Login" });
          this.$store.dispatch("user/logout");
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.user-menu {
  span.usermenu-item {
    display: inline-block;
    padding: 0 8px;
    height: 100%;
    cursor: pointer;
    vertical-align: middle;
    @include themify() {
      font-size: themed("$--font-size-extra-large");
    }
  }
}
</style>
