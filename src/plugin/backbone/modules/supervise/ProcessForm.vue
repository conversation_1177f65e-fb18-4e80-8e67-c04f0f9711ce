<template>
  <head-fixed-layout class="supervise-create-order">
    <template #header>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="15" :md="18" class="head-title" v-if="!woId"
          >GNOC督办单拟稿</el-col
        >
        <el-col :xs="24" :sm="15" :md="18" class="head-title" v-if="woId"
          >GNOC督办单草稿</el-col
        >

        <el-col :xs="24" :sm="9" :md="6" class="head-handle-wrap">
          <el-button
            type="primary"
            @click="onSheetDel"
            v-loading.fullscreen.lock="sheetCgDelLoading"
            v-if="woId"
            >删除</el-button
          >

          <el-button
            type="primary"
            @click="onSheetSave"
            v-loading.fullscreen.lock="sheetSaveLoading"
            >保存</el-button
          >
          <el-button
            type="primary"
            @click="onSheetSubmit"
            v-loading.fullscreen.lock="sheetCommitLoading"
            >提交</el-button
          >
        </el-col>
      </el-row>
    </template>

    <el-form
      :model="sheetForm"
      ref="sheetForm"
      :rules="sheetFormRules"
      label-width="130px"
      label-position="right"
    >
      <el-card shadow="always" :body-style="{ padding: '10px' }">
        <div slot="header" class="card-title">
          <span>工单基本信息</span>
        </div>
        <el-row :gutter="10" type="flex" style="flex-wrap: wrap">
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="建单人:" prop="senderName">
              <el-input
                v-model="sheetForm.senderName"
                placeholder="请输入建单人"
                readonly
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="建单部门:" prop="senderDeptName">
              <el-input
                v-model="sheetForm.senderDeptName"
                placeholder="请输入建单部门"
                readonly
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="建单时间:" prop="sheetCreateTime">
              <el-input v-model="sheetForm.sheetCreateTime" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="16" :md="16" :offset="0">
            <el-form-item label="工单主题:" prop="sheetTitle">
              <el-input
                v-model="sheetForm.sheetTitle"
                placeholder="请输入内容"
                clearable
                style="width: 100%"
                maxlength="400"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="10" :md="8" :offset="0">
            <el-form-item label="工单来源:" required>
              <dict-select
                :value.sync="sheetForm.createType"
                :dictId="10003"
                placeholder="请选择内容"
                :notSelect="true"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="发生时间:" prop="alarmCreateTime">
              <el-date-picker
                v-model="sheetForm.alarmCreateTime"
                type="datetime"
                placeholder="请选择时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
                style="width: 100%"
                :picker-options="pickerOptions"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="紧急程度:" prop="emergencyLevel" required>
              <el-radio-group v-model="sheetForm.emergencyLevel">
                <el-radio
                  v-for="(item, i) in urgentOption"
                  :key="i"
                  :label="item.dictCode"
                  >{{ item.dictName }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
          <!-- <el-col :xs="24" :sm="10" :md="8" :offset="0">
            <el-form-item label="工单优先级:" prop="faultLevel" required>
              <dict-select
                :value.sync="sheetForm.faultLevel"
                :dictId="81005"
                placeholder="请选择"
                style="width: 100%"
              />
            </el-form-item>
          </el-col> -->
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item
              label="受理时限(分钟):"
              prop="acceptTimeLimit"
              required
            >
              <el-input
                v-model="sheetForm.acceptTimeLimit"
                placeholder="请输入受理时限"
                clearable
                style="width: 100%"
                maxlength="11"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="预估处理时限:" prop="processTimeLimit">
              <el-select
                v-model="sheetForm.processTimeLimit"
                placeholder="请选择预估处理时限"
                style="width: 100%"
              >
                <el-option
                  v-for="item in processTimeLimitOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="所属专业:" prop="bigProfessional" required>
              <dict-select
                :value.sync="sheetForm.bigProfessional"
                :dictId="811038"
                style="width: 100%"
                @change="getProfessionalTypeData"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="告警专业:" prop="professionalType" required>
              <!-- <dict-select
                :value.sync="sheetForm.professionalType"
                :dictId="10002"
                style="width: 100%"
              /> -->
              <el-select
                v-model="sheetForm.professionalType"
                placeholder="请选择告警专业"
                style="width: 100%"
                @change="changeProfessionalType"
              >
                <el-option
                  v-for="item in professionalTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="网络类型:" prop="networkType">
              <!-- <dict-select
                :value.sync="sheetForm.networkType"
                :dictId="70001"
                placeholder="请选择内容"
                style="width: 100%"
              /> -->
              <el-select
                v-model="sheetForm.networkType"
                placeholder="请选择内容"
                style="width: 100%"
              >
                <el-option
                  v-for="item in networkTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="告警类别:" prop="orgType">
              <dict-select
                :value.sync="sheetForm.orgType"
                :dictId="60002"
                placeholder="请选择内容"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="网元类型:" prop="neType">
              <el-input
                v-model="sheetForm.neType"
                placeholder="请输入网元类型"
                style="width: 100%"
                maxlength="20"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="网元名称:" prop="neName">
              <el-input
                v-model="sheetForm.neName"
                placeholder="请输入网元名称"
                clearable
                style="width: 100%"
                maxlength="11"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="省份名称:" prop="provinceCode">
              <el-select
                style="width: 100%"
                placeholder="请选择省份名称"
                v-model="sheetForm.provinceCode"
                filterable
                @change="getFaultAreaCommonCounty"
              >
                <el-option
                  v-for="(item, i) in faultRegionOptions"
                  :key="i"
                  :label="item.areaName"
                  :value="item.areaCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="地市名称:" prop="regionCode">
              <el-select
                style="width: 100%"
                placeholder="请选择地市名称"
                v-model="sheetForm.regionCode"
                filterable
                @change="getCountyData"
              >
                <el-option
                  v-for="(item, i) in faultAreaCommonCounty"
                  :key="i"
                  :label="item.areaName"
                  :value="item.areaCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="区县名称:" prop="cityCode">
              <el-select
                style="width: 100%"
                placeholder="请选择区县名称"
                v-model="sheetForm.cityCode"
                filterable
              >
                <el-option
                  v-for="(item, i) in faultCountyOptions"
                  :key="i"
                  :label="item.areaName"
                  :value="item.areaCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="机房名称:" prop="roomName">
              <el-input
                v-model="sheetForm.roomName"
                placeholder="请输入内容"
                clearable
                style="width: 100%"
                maxlength="255"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="网内网间子类型:" prop="wnwjType">
              <el-select
                style="width: 100%"
                placeholder="请选择网内网间子类型"
                v-model="sheetForm.wnwjType"
                filterable
              >
                <el-option
                  v-for="item in wnwjTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="23" :md="24" :offset="0">
            <el-form-item label="故障现象:" prop="faultPhenomenon" required>
              <el-input
                v-model="sheetForm.faultPhenomenon"
                placeholder="请输入内容"
                type="textarea"
                :rows="2"
                clearable
                style="width: 100%"
                show-word-limit
                maxlength="3000"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24" :offset="0">
            <el-form-item label="备注:" prop="falutComment">
              <el-input
                v-model="sheetForm.falutComment"
                placeholder="请输入内容"
                clearable
                style="width: 100%"
                maxlength="255"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <el-card
        shadow="always"
        :body-style="{ padding: '10px' }"
        style="margin-top: 10px"
      >
        <div slot="header" class="card-title">
          <span>工单派送信息</span>
        </div>
        <el-row :gutter="10" type="flex" style="flex-wrap: wrap">
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="附件:" prop="attachmentFile">
              <el-input
                v-model="sheetForm.attachmentFile"
                placeholder="添加附件"
                readonly
                style="width: 100%"
              >
                <el-button type="info" slot="append" @click="attachmentBrowse"
                  >+</el-button
                >
                <el-button
                  v-if="ngAttachmentArr.length > 0"
                  type="info"
                  slot="append"
                  @click="ngAttachmentBrowse"
                  title="查看拟稿上传的附件"
                  >查看</el-button
                >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="建单人主送:" prop="builderZs" required>
              <el-input v-model="sheetForm.builderZs" placeholder="添加人员">
                <template v-for="(tag, index) in organizeForm.builderZsList">
                  <el-tag
                    slot="prefix"
                    style="margin-top: 5px"
                    :key="index"
                    closable
                    @close="handleClose('builderZs', tag)"
                    v-show="index < 1"
                    v-if="tag.bz == 'user'"
                  >
                    {{ tag.name }}
                  </el-tag>
                  <el-tag
                    slot="prefix"
                    style="margin-top: 5px"
                    :key="index"
                    closable
                    @close="handleClose('builderZs', tag)"
                    v-show="index < 1"
                    v-if="tag.bz == 'org'"
                  >
                    {{ tag.orgName }}
                  </el-tag>
                </template>
                <el-popover
                  slot="prefix"
                  v-if="organizeForm.builderZsList.length >= 2"
                  width="500"
                  trigger="click"
                >
                  <el-input
                    v-model="organizeForm.builderZsName"
                    placeholder="请输入主送人员姓名/组织名称"
                  >
                    <el-button
                      type="info"
                      slot="append"
                      icon="el-icon-search"
                      @click="search('builderZs')"
                    >
                    </el-button>
                    <el-button
                      type="info"
                      slot="append"
                      icon="el-icon-close"
                      @click="clear('builderZs')"
                    >
                    </el-button>
                  </el-input>
                  <el-table
                    ref="multipleTable"
                    tooltip-effect="dark"
                    @selection-change="handleSelectionChange"
                    :data="organizeForm.builderZsListCopy"
                    max-height="240"
                  >
                    <el-table-column width="30" type="selection">
                    </el-table-column>
                    <el-table-column
                      min-width="70"
                      property="name"
                      label="姓名"
                    >
                    </el-table-column>
                    <el-table-column
                      min-width="180"
                      property="orgName"
                      label="组织"
                    >
                    </el-table-column>
                    <el-table-column
                      min-width="120"
                      property="mobilePhone"
                      label="电话"
                    >
                    </el-table-column>
                    <el-table-column fixed="right" label="操作" width="50">
                      <template slot-scope="scope">
                        <el-button
                          type="text"
                          size="small"
                          @click.native.prevent="
                            handleClose('builderZs', scope.row)
                          "
                        >
                          移除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <el-button
                    size="small"
                    type="text"
                    @click="toggleSelection('builderZs')"
                    >批量移除
                  </el-button>
                  <el-tag slot="reference" style="margin-top: 3px">
                    +{{ organizeForm.builderZsList.length - 1 }}
                  </el-tag>
                </el-popover>
                <el-button
                  type="info"
                  slot="append"
                  icon="el-icon-user"
                  @click="onOpenPeopleDialog('lordSentDetermine')"
                ></el-button>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="建单人抄送:" prop="copyMan">
              <el-input v-model="sheetForm.builderCs" placeholder="添加人员">
                <template v-for="(tag, index) in organizeForm.builderCsList">
                  <el-tag
                    slot="prefix"
                    style="margin-top: 5px"
                    :key="index"
                    closable
                    @close="handleClose('builderCs', tag)"
                    v-show="index < 1"
                    v-if="tag.bz == 'user'"
                  >
                    {{ tag.name }}
                  </el-tag>
                  <el-tag
                    slot="prefix"
                    style="margin-top: 5px"
                    :key="index"
                    closable
                    @close="handleClose('builderCs', tag)"
                    v-show="index < 1"
                    v-if="tag.bz == 'org'"
                  >
                    {{ tag.orgName }}
                  </el-tag>
                </template>
                <el-popover
                  slot="prefix"
                  v-if="organizeForm.builderCsList.length >= 2"
                  width="500"
                  trigger="click"
                >
                  <el-input
                    v-model="organizeForm.builderCsName"
                    placeholder="请输入抄送人员姓名/组织名称"
                  >
                    <el-button
                      type="info"
                      slot="append"
                      icon="el-icon-search"
                      @click="search('builderCs')"
                    >
                    </el-button>
                    <el-button
                      type="info"
                      slot="append"
                      icon="el-icon-close"
                      @click="clear('builderCs')"
                    >
                    </el-button>
                  </el-input>
                  <el-table
                    ref="multipleTable"
                    tooltip-effect="dark"
                    @selection-change="handleSelectionChange"
                    :data="organizeForm.builderCsListCopy"
                    max-height="240"
                  >
                    <el-table-column width="30" type="selection">
                    </el-table-column>
                    <el-table-column
                      min-width="70"
                      property="name"
                      label="姓名"
                    >
                    </el-table-column>
                    <el-table-column
                      min-width="180"
                      property="orgName"
                      label="组织"
                    >
                    </el-table-column>
                    <el-table-column
                      min-width="120"
                      property="mobilePhone"
                      label="电话"
                    >
                    </el-table-column>
                    <el-table-column fixed="right" label="操作" width="50">
                      <template slot-scope="scope">
                        <el-button
                          type="text"
                          size="small"
                          @click.native.prevent="
                            handleClose('builderCs', scope.row)
                          "
                        >
                          移除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <el-button
                    size="small"
                    type="text"
                    @click="toggleSelection('builderCs')"
                    >批量移除
                  </el-button>
                  <el-tag slot="reference" style="margin-top: 5px">
                    +{{ organizeForm.builderCsList.length - 1 }}
                  </el-tag>
                </el-popover>
                <el-button
                  type="info"
                  slot="append"
                  icon="el-icon-user"
                  @click="onOpenPeopleDialog('ccDetermine')"
                >
                </el-button>
              </el-input>
            </el-form-item>
          </el-col>
          <template v-if="showSeizeOrders">
            <el-col :xs="24" :sm="8" :md="8" :offset="0">
              <el-form-item label="派单模式:" prop="seizeOrders" required>
                <el-radio-group v-model="sheetForm.seizeOrders">
                  <el-radio :label="1">抢单受理</el-radio>
                  <el-radio :label="0">均可受理</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </template>
        </el-row>
      </el-card>
    </el-form>

    <dia-tissue-tree
      v-if="isDiaOrgsUserTree"
      :title="diaPeople.title"
      :visible.sync="diaPeople.visible"
      :showOrgsTree="diaPeople.showOrgsTree"
      :showContactUserTab="diaPeople.showContactUserTab"
      :showContactOrgTab="diaPeople.showContactOrgTab"
      :professionalType="mainProfessionalType"
      @on-save="onSavePeople"
    />
    <el-dialog
      width="420px"
      title="附件选择"
      :visible.sync="attachmentDialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <file-upload
        @change="changeFileData"
        @cancel="closeAttachmentDialog"
      ></file-upload>
    </el-dialog>
    <el-dialog
      width="500px"
      title="文件列表"
      :visible.sync="ngAttachmentDownloadVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <file-download
        @cancel="ngAttachmentDownloadVisible = false"
        :attachmentArr="ngAttachmentArr"
      ></file-download>
    </el-dialog>
  </head-fixed-layout>
</template>

<script>
import { mapGetters } from "vuex";
import moment from "moment";
import HeadFixedLayout from "./../workOrder/components/HeadFixedLayout.vue";
import {
  apiGetOrgInfo,
  getCurrentTime,
  apiDict,
} from "./../workOrder/api/CommonApi";
import {
  apiInitOrderDraf,
  apiArea,
  apiDeleteDraft,
  apiGetEnumList,
} from "./workOrderWaitDetail/api/CommonApi";
import { apiSuperviseBuild } from "./api/api";
import DiaTissueTree from "@/plugin/backbone/components/common/DiaTissueTree";
import FileUpload from "../workOrder/components/FileUpload.vue";
import FileDownload from "../workOrder/components/FileDownload.vue";

import DictSelect from "../workOrder/components/DictSelect.vue";
import { mixin } from "../../../../mixins";
import cloneDeep from "lodash/cloneDeep";

export default {
  name: "SuperviseProcessForm",
  components: {
    HeadFixedLayout,
    DiaTissueTree,
    DictSelect,
    FileUpload,
    FileDownload,
  },
  mixins: [mixin],

  data() {
    var validHappenTime = (rule, value, callback) => {
      if (value === "" || value === null) {
        callback(new Error("请选择发生时间"));
      } else {
        let seconds = moment(
          this.sheetForm.sheetCreateTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.sheetForm.happenTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        if (seconds < 0) {
          callback(new Error("“发生时间”不能晚于“建单时间”，请重新选择"));
        } else {
          callback();
        }
      }
    };
    return {
      mainProfessionalType: "集团督办",
      sheetForm: {
        sender: "", //建单人的登录名
        senderName: "", //建单人真实姓名
        senderDeptName: "", // 创建人部门
        senderDept: null, //建单人部门ID
        sheetCreateTime: getCurrentTime(Date.now()), // 建单时间

        sheetTitle: "", //工单主题
        createType: "", //工单来源
        alarmCreateTime: "", //发生时间
        provinceCode: "", //省份名称
        regionCode: "", //地市名称
        cityCode: "", //区县名称
        emergencyLevel: "", //紧急程度
        acceptTimeLimit: "20", // 受理时限
        processTimeLimit: "", //预估处理时限
        bigProfessional: "", //所属专业
        professionalType: "", //告警专业
        networkType: "", //网络类型
        orgType: "", //告警类别
        neType: "", //网元类型
        neName: "", //网元名称
        roomName: "", //机房名称
        wnwjType: "", //网内网间子类型
        faultPhenomenon: "", //故障现象
        falutComment: "", //备注
        attachmentFile: "", //附件
        builderZs: "", //主送
        builderZsUserId: "", //主送人ID
        builderZsOrgId: "", //主送组织ID
        builderZsUserName: "", //主送人名称
        builderZsOrgName: "", //主送组织名称

        builderCs: "", //抄送
        builderCsUserId: "", //抄送人ID
        builderCsOrgId: "", //抄送组织ID
        builderCsUserName: "", //抄送人名称
        builderCsOrgName: "", //抄送组织名称
        recipient: "", //短信接收人

        agentManDetail: "",
        copyManDetail: "",
        seizeOrders: 1,
        faultLevel: "2",
      },
      organizeForm: {
        builderZsList: [],
        builderZsListCopy: [],
        builderZsName: "",

        builderAcceptList: [],
        builderAcceptListCopy: [],
        builderAcceptName: "",

        builderCsList: [],
        builderCsListCopy: [],
        builderCsName: "",

        recipientList: [],
        recipientListCopy: [],
        recipientName: "",
      },
      importForm: {
        //附件
        attachmentFileList: [],
      },
      sheetFormRules: {
        sheetTitle: [
          { required: true, message: "工单主题不能为空" },
          {
            validator: this.checkLength,
            max: 400,
            form: "sheetForm",
            messsage: "已超过填写字数上限",
          },
        ],
        createType: [{ required: true, message: "请选择工单来源" }],
        alarmCreateTime: [{ validator: validHappenTime, required: true }],
        emergencyLevel: [{ required: true, message: "请选择紧急程度" }],
        acceptTimeLimit: [
          { required: true, message: "请输入受理时限" },
          {
            validator: this.checkLength,
            max: 11,
            form: "sheetForm",
            messsage: "已超过填写字数上限",
          },
        ],
        falutComment: [
          {
            validator: this.checkLength,
            max: 255,
            form: "sheetForm",
            messsage: "已超过填写字数上限",
          },
        ],
        sendContent: [
          {
            required: true,
            message: "请输入发送内容",
            trigger: "blur",
          },
          {
            validator: this.checkLength,
            max: 1000,
            form: "sheetForm",
            messsage: "已超过填写字数上限",
          },
        ],

        bigProfessional: [{ required: true, message: "请选择所属专业" }],
        professionalType: [{ required: true, message: "请选择告警专业" }],
        faultPhenomenon: [
          { required: true, message: "请输入故障现象" },
          {
            validator: this.checkLength,
            max: 3000,
            form: "sheetForm",
            messsage: "已超过填写字数上限",
          },
        ],
        builderZs: [{ required: true, message: "主送人不能为空" }],
        processTimeLimit: [{ required: true, message: "请选择预估处理时限" }],
        // faultLevel: [{ required: true, message: "请选择工单优先级" }],
      },
      diaPeople: {
        visible: false,
        title: "",
        saveName: "",
        saveTitleMap: {
          lordSentDetermine: "建单人主送",
          secondDetermine: "建单人次送",
          ccDetermine: "建单人抄送",
          recipientDetermine: "接收人选择",
        },
        multipleSelectEnable: true,
        singleSelectTip: "",
        showOrgsTree: true,
        showOrgsTreeMap: {
          recipientDetermine: false,
        },
        showContactUserTab: false,
        showContactUserTabMap: {
          lordSentDetermine: true,
          secondDetermine: true,
          ccDetermine: true,
          recipientDetermine: true,
        },
        showContactOrgTab: false,
        showContactOrgTabMap: {
          lordSentDetermine: true,
          secondDetermine: true,
          ccDetermine: true,
        },
      },
      attachmentDialogVisible: false,
      sheetCommitLoading: false,
      sheetSaveLoading: false,
      incidenceDialogVisible: false,
      sheetCgDelLoading: false,
      orgInfoData: {}, //组织信息数据缓存
      route: null,
      userInfoData: {},
      areaCode: "",
      category: "",
      faultRegionOptions: [],
      faultAreaCommonCounty: [],
      sheetNo: null,
      woId: null,
      processInstId: null,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      isDiaOrgsUserTree: false,
      urgentOption: [],
      processTimeLimitOptions: [
        {
          value: "1",
          label: "1小时",
        },
        {
          value: "3",
          label: "3小时",
        },
        {
          value: "4",
          label: "4小时",
        },
        {
          value: "5",
          label: "5小时",
        },
        {
          value: "24",
          label: "24小时",
        },
        {
          value: "48",
          label: "48小时",
        },
      ],
      faultCountyOptions: [],
      ngAttachmentArr: [],
      ngAttachmentDownloadVisible: false,
      professionalTypeOptions: [],
      wnwjTypeOptions: [],
      networkTypeOptions: [],
      showSeizeOrders: false,
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
    ...mapGetters(["token"]),
  },
  watch: {
    "organizeForm.builderZsList": {
      handler(newV) {
        if (newV.length > 0) {
          this.$set(this.sheetForm, "builderZs", "已选");
        } else {
          this.$set(this.sheetForm, "builderZs", "");
        }
        if (newV.length >= 2) {
          this.showSeizeOrders = true;
          this.$set(this.sheetForm, "seizeOrders", 0);
        } else {
          this.showSeizeOrders = false;
          this.$set(this.sheetForm, "seizeOrders", 1);
        }

        // let userList = newV.filter((item, index) => {
        //   return item.bz == "user";
        // });
        // let orgList = newV.filter((item, index) => {
        //   return item.bz == "org";
        // });
        // if (userList.length == 1) {
        //   this.sheetForm.seizeOrders = 0;
        // }
        // if (orgList.length == 1) {
        //   this.sheetForm.seizeOrders = 0;
        // }
      },
      deep: true,
      immediate: true,
    },
    "organizeForm.recipientList": {
      handler(newV) {
        if (newV.length > 0) {
          this.sheetForm.recipient = "已选";
        } else {
          this.sheetForm.recipient = "";
        }
      },
      deep: true,
    },
  },
  mounted() {
    this.sheetForm.createType = "2"; // 电子运维新建
    this.userInfoData = JSON.parse(this.userInfo.attr2);
    this.sheetForm.sheetCreateTime = moment(Date.now()).format(
      "YYYY-MM-DD HH:mm:ss"
    );
    this.sheetForm.happenTime = moment(Date.now()).format(
      "YYYY-MM-DD HH:mm:ss"
    );
    // 初始化 信息来源
    this.getOrgInfo();
    if (this.$route.query?.orderType == "caogao") {
      this.woId = this.$route.query.woId;
      this.initOrder();
    }
  },
  created() {
    this.getUrgentOption();
  },
  methods: {
    getUrgentOption() {
      let param = {
        dictTypeCode: "10001",
      };
      apiDict(param)
        .then(res => {
          if (res.code == 200) {
            this.urgentOption = res?.data ?? [];
            this.urgentOption.reverse();
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    changeProfessionalType(val) {
      this.sheetForm.networkType = "";
      let param = {
        dictTypeCode: 811050,
        parentCode: val,
      };
      return new Promise(resolve => {
        apiGetEnumList(param).then(res => {
          if (res.code == 200) {
            this.networkTypeOptions = res?.data ?? [];
          }
          resolve("success");
        });
      });
    },
    // 回显
    initOrder() {
      // url 中 获取 sheetNo 参数
      const url = window.location.href;
      const queryStri = url.substring(url.indexOf("?") + 1).split("&");
      let sheetNoUrl = "";
      queryStri.forEach(item => {
        if (item.indexOf("sheetNo") >= 0) {
          sheetNoUrl = decodeURI(item.split("=")[1]);
        }
      });
      let params = {
        woId: this.$route.query.woId,
        sheetNo: this.$route.query.sheetNo || sheetNoUrl,
      };
      apiInitOrderDraf(params)
        .then(async res => {
          if (res.status == "0") {
            let self = this;
            let resData = Object.assign(res.data);
            self.sheetForm = cloneDeep(resData);
            self.sheetForm.bigProfessional = String(resData.bigProfessional);
            self.sheetForm.professionalType = String(resData.professionalType);

            self.sheetForm.createType = String(resData.createType);
            self.sheetForm.emergencyLevel = String(resData.emergencyLevel);
            self.sheetForm.processTimeLimit = String(resData.processTimeLimit);
            self.sheetForm.orgType = String(resData.orgType);
            self.sheetForm.agentManDetail = resData.agentManDetail;
            self.sheetForm.builderZsOrgId = resData.agentDeptCode;
            self.sheetForm.builderZsUserId = resData.agentManId;
            self.sheetForm.builderZsUserName = resData.agentMan;
            self.sheetForm.builderZsOrgName = resData.agentDeptName;
            self.sheetForm.copyManDetail = resData.copyManDetail;
            self.sheetForm.builderCsUserId = resData.copyManId;
            self.sheetForm.builderCsOrgId = resData.copyDeptCode;
            self.sheetForm.builderCsUserName = resData.copyMan;
            self.sheetForm.builderCsOrgName = resData.copyDeptName;
            // if (resData.agentMan || resData.agentDeptName) {
            //   self.sheetForm.builderZs = "1";
            // }
            self.sheetForm.faultLevel = resData.faultLevel;
            self.woId = resData.woId; //工单id
            self.sheetNo = resData.sheetNo; //工单编号
            self.processInstId = resData.processInstId; //流程实例id

            if (resData.appendix) {
              self.ngAttachmentArr = JSON.parse(resData.appendix);
            }
            this.getFaultAreaCommonCounty(resData.provinceCode).then(() => {
              this.$set(this.sheetForm, "regionCode", resData.regionCode);
            });
            this.getCountyData(resData.regionCode).then(() => {
              this.$set(this.sheetForm, "cityCode", resData.cityCode);
            });
            await this.getProfessionalTypeData(resData.bigProfessional);
            this.$set(
              this.sheetForm,
              "professionalType",
              resData.professionalType + ""
            );
            this.$set(this.sheetForm, "wnwjType", resData.wnwjType);
            this.changeProfessionalType(resData.professionalType).then(() => {
              this.$set(this.sheetForm, "networkType", resData.networkType);
            });
            this.processing();
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    getOrgInfo() {
      apiGetOrgInfo()
        .then(res => {
          if (res.status == "0") {
            this.orgInfoData = res.data;
            this.sheetForm.senderName = res.data.trueName;
            this.sheetForm.senderDeptName = res?.data?.orgInfo?.orgName ?? "";
            this.sheetForm.senderDept = res?.data?.orgInfo?.orgId ?? "";
            this.sheetForm.senderDept = res?.data?.orgInfo?.orgId ?? "";
            this.sheetForm.sender = res?.data?.userName ?? "";

            this.areaCode = res?.data?.orgInfo?.areaCode ?? "";
            this.category = res?.data?.category ?? "";
            this.getFaultAreaOptions();
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    onSheetDel() {
      this.$confirm("是否删除工单草稿？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "error",
      }).then(() => {
        this.sheetCgDelLoading = true;
        let param = {
          woId: this.woId,
        };
        apiDeleteDraft(param)
          .then(res => {
            if (res.status == "0") {
              this.$message.success("删除成功");
              this.closeAndTurnAround("backbone_myDraft");
            } else {
              this.$message.error("删除失败");
            }
            this.sheetCgDelLoading = false;
          })
          .catch(error => {
            console.log(error);
            this.sheetCgDelLoading = false;
          });
      });
    },

    //获取地市名称数据
    getFaultAreaCommonCounty(arr) {
      this.sheetForm.regionCode = "";
      let param = {
        areaCode: arr,
      };
      return new Promise(resolve => {
        apiArea(param).then(res => {
          if (res.status == "0") {
            this.faultAreaCommonCounty = res?.data ?? [];
          }
          resolve("success");
        });
      });
    },
    //获取区县数据
    getCountyData(arr) {
      this.sheetForm.cityCode = "";
      let param = {
        areaCode: arr,
      };
      return new Promise(resolve => {
        apiArea(param).then(res => {
          if (res.status == "0") {
            this.faultCountyOptions = res?.data ?? [];
          }
          resolve("success");
        });
      });
    },
    //获取省份名称数据
    getFaultAreaOptions() {
      let param = {
        areaCode: "1003315",
      };
      apiArea(param)
        .then(res => {
          if (res.status == "0") {
            this.faultRegionOptions = res?.data ?? [];
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    //保存到草稿
    onSheetSave() {
      this.entering();
      this.$confirm("是否保存到草稿？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$refs.sheetForm.validate(valid => {
            if (valid) {
              let formData = new FormData();
              this.sheetSaveLoading = true;
              let self = this;
              if (this.importForm.attachmentFileList.length > 0) {
                for (let item of this.importForm.attachmentFileList) {
                  formData.append("orderFiles", item.raw);
                }
              }
              let param = {
                sheetInfo: {
                  woId: self.woId, //工单id
                  sheetNo: self.sheetNo, //工单编号
                  processInstId: self.processInstId, //流程实例id
                  sender: self.sheetForm.sender, //建单人
                  senderDeptName: self.sheetForm.senderDeptName, //建单部门
                  senderName: self.sheetForm.senderName, //建单人中文名
                  senderDept: self.sheetForm.senderDept, //建单部门ID
                  sheetCreateTime: self.sheetForm.sheetCreateTime, //建单时间
                  sheetTitle: self.sheetForm.sheetTitle, //工单主题
                  createType: self.sheetForm.createType, //工单来源
                  alarmCreateTime: self.sheetForm.alarmCreateTime, //发生时间
                  emergencyLevel: self.sheetForm.emergencyLevel, //紧急程度
                  acceptTimeLimit: self.sheetForm.acceptTimeLimit, //受理时限
                  processTimeLimit: self.sheetForm.processTimeLimit, //预估处理时限
                  bigProfessional: self.sheetForm.bigProfessional, //所属专业
                  professionalType: self.sheetForm.professionalType, //告警专业
                  networkType: self.sheetForm.networkType, //网络类型
                  neType: self.sheetForm.neType, //网元类型
                  neName: self.sheetForm.neName, //网元名称
                  provinceCode: self.sheetForm.provinceCode, //省份名称
                  cityCode: self.sheetForm.cityCode, //区县名称
                  regionCode: self.sheetForm.regionCode, // 地市名称
                  orgType: self.sheetForm.orgType, //告警类别

                  roomName: self.sheetForm.roomName, //机房名称
                  wnwjType: self.sheetForm.wnwjType, //网内网间子类型
                  faultPhenomenon: self.sheetForm.faultPhenomenon, //故障现象
                  falutComment: self.sheetForm.falutComment, //备注

                  agentDeptCode: self.sheetForm.builderZsOrgId, //主送是组织 传入组织ID
                  agentManId: self.sheetForm.builderZsUserId, //主送是人员 传入人员ID
                  agentMan: self.sheetForm.builderZsUserName, //主送人员 中文名称
                  agentDeptName: self.sheetForm.builderZsOrgName, //主送部门 中文名称

                  copyDeptCode: self.sheetForm.builderCsOrgId, //抄送是组织 传入组织ID
                  copyManId: self.sheetForm.builderCsUserId, //抄送是用户 传入用户ID
                  copyMan: self.sheetForm.builderCsUserName,
                  copyDeptName: self.sheetForm.builderCsOrgName,

                  seizeOrders: self.sheetForm.seizeOrders,
                  sheetStatus: 1, //1是草稿 2是提交到待办

                  agentManDetail: self.sheetForm.agentManDetail,
                  copyManDetail: self.sheetForm.copyManDetail,
                  faultLevel: self.sheetForm.faultLevel,
                },
              };
              formData.append("jsonParam", JSON.stringify(param));
              // console.log("buildZS", this.sheetForm.builderZs);
              this.submitSave(formData);
            } else {
              return false;
            }
          });
        })
        .catch(() => {});
    },
    submitSave(formData) {
      apiSuperviseBuild(formData)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("保存成功");
            this.$refs.sheetForm.resetFields();
            this.closeAndTurnAroundMyDraft();
          } else {
            this.$message.error("保存失败");
          }
          this.sheetSaveLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("保存失败");
          this.sheetSaveLoading = false;
        });
    },
    //提交工单
    onSheetSubmit() {
      this.entering();
      this.$confirm("是否提交工单？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        closeOnClickModal: false,
        type: "warning",
      }).then(() => {
        this.$refs.sheetForm.validate((valid, a) => {
          if (this.uncheck(a)) {
            let formData = new FormData();
            this.sheetCommitLoading = true;
            let self = this;
            if (this.importForm.attachmentFileList.length > 0) {
              for (let item of this.importForm.attachmentFileList) {
                formData.append("orderFiles", item.raw);
              }
            }
            let param = {
              sheetInfo: {
                woId: self.woId, //工单id
                sheetNo: self.sheetNo, //工单编号
                processInstId: self.processInstId, //流程实例id
                sender: self.sheetForm.sender, //建单人
                senderDeptName: self.sheetForm.senderDeptName, //建单部门
                senderName: self.sheetForm.senderName, //建单人中文名
                senderDept: self.sheetForm.senderDept, //建单部门ID
                sheetCreateTime: self.sheetForm.sheetCreateTime, //建单时间
                sheetTitle: self.sheetForm.sheetTitle, //工单主题
                createType: self.sheetForm.createType, //工单来源
                alarmCreateTime: self.sheetForm.alarmCreateTime, //发生时间
                emergencyLevel: self.sheetForm.emergencyLevel, //紧急程度
                acceptTimeLimit: self.sheetForm.acceptTimeLimit, //受理时限
                processTimeLimit: self.sheetForm.processTimeLimit, //预估处理时限
                bigProfessional: self.sheetForm.bigProfessional, //所属专业
                professionalType: self.sheetForm.professionalType, //告警专业
                networkType: self.sheetForm.networkType, //网络类型
                orgType: self.sheetForm.orgType, //告警类别
                neType: self.sheetForm.neType, //网元类型
                neName: self.sheetForm.neName, //网元名称
                provinceCode: self.sheetForm.provinceCode, //省份名称
                cityCode: self.sheetForm.cityCode, //区县名称
                regionCode: self.sheetForm.regionCode, //地市名称
                roomName: self.sheetForm.roomName, //机房名称
                wnwjType: self.sheetForm.wnwjType, //网内网间子类型
                faultPhenomenon: self.sheetForm.faultPhenomenon, //故障现象
                falutComment: self.sheetForm.falutComment, //备注

                agentDeptCode: self.sheetForm.builderZsOrgId, //主送是组织 传入组织ID
                agentManId: self.sheetForm.builderZsUserId, //主送是人员 传入人员ID
                agentMan: self.sheetForm.builderZsUserName, //主送人员 中文名称
                agentDeptName: self.sheetForm.builderZsOrgName, //主送部门 中文名称

                copyDeptCode: self.sheetForm.builderCsOrgId, //抄送是组织 传入组织ID
                copyManId: self.sheetForm.builderCsUserId, //抄送是用户 传入用户ID
                copyMan: self.sheetForm.builderCsUserName,
                copyDeptName: self.sheetForm.builderCsOrgName,
                seizeOrders: self.sheetForm.seizeOrders,
                sheetStatus: 2, //1是草稿 2是提交到待办

                agentManDetail: self.sheetForm.agentManDetail,
                copyManDetail: self.sheetForm.copyManDetail,
                faultLevel: self.sheetForm.faultLevel,
              },
            };
            formData.append("jsonParam", JSON.stringify(param));
            this.submitOrder(formData);
          } else {
            return false;
          }
        });
      });
    },

    submitOrder(formData) {
      apiSuperviseBuild(formData)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("工单提交成功");
            this.$refs.sheetForm.resetFields();
            this.closeAndTurnAroundTo();
          } else {
            this.$message.error("工单提交失败");
          }
          this.sheetCommitLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("工单提交失败");
          this.sheetCommitLoading = false;
        })
        .finally(() => {
          this.sheetCommitLoading = false;
        });
    },

    onOpenPeopleDialog(diaSaveName) {
      this.isDiaOrgsUserTree = false;
      this.diaPeople.title = this.diaPeople.saveTitleMap[diaSaveName];
      this.diaPeople.saveName = diaSaveName;
      this.diaPeople.showOrgsTree =
        this.diaPeople.showOrgsTreeMap[diaSaveName] ?? true;
      this.diaPeople.showContactUserTab =
        this.diaPeople.showContactUserTabMap[diaSaveName] ?? false;
      this.diaPeople.showContactOrgTab =
        this.diaPeople.showContactOrgTabMap[diaSaveName] ?? false;
      this.diaPeople.visible = true;
      this.$nextTick(() => {
        this.isDiaOrgsUserTree = true;
      });
    },
    onSavePeople(val) {
      this[this.diaPeople.saveName](val);
    },
    //建单人主送确定
    lordSentDetermine({
      usersChecked,
      orgsChecked,
      selectionUser,
      contactSelectionUser,
      contactSelectionOrg,
    }) {
      if (contactSelectionUser && contactSelectionUser.length > 0) {
        contactSelectionUser.map(item => {
          let zs = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.userName;
          });
          if (zs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderZsList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (selectionUser && selectionUser.length > 0) {
        selectionUser.map(item => {
          let zs = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.userName;
          });
          if (zs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderZsList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (usersChecked && usersChecked.length > 0) {
        usersChecked.map(item => {
          let zs = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.id;
          });
          if (zs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderZsList.push({
              bz: "user",
              id: item.id,
              name: item.name,
              orgName: item.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (contactSelectionOrg && contactSelectionOrg.length > 0) {
        contactSelectionOrg.map(item => {
          let zsOrg = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.orgId;
          });
          if (zsOrg > -1) {
            this.$message({
              message: "选择的组织已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderZsList.push({
              bz: "org",
              id: item.orgId + "",
              orgName: item.fullOrgName,
            });
          }
        });
      }
      if (orgsChecked && orgsChecked.length > 0) {
        orgsChecked.map(item => {
          let zsOrg = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.id;
          });
          if (zsOrg > -1) {
            this.$message({
              message: "选择的组织已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderZsList.push({
              bz: "org",
              id: item.id,
              orgName: item.name,
            });
          }
        });
      }
      this.organizeForm.builderZsListCopy = JSON.parse(
        JSON.stringify(this.organizeForm.builderZsList)
      );
    },
    //建单人次送确定
    secondDetermine({
      usersChecked,
      orgsChecked,
      selectionUser,
      contactSelectionUser,
      contactSelectionOrg,
    }) {
      if (contactSelectionUser && contactSelectionUser.length > 0) {
        contactSelectionUser.map(item => {
          let zs = this.organizeForm.builderAcceptList.findIndex(val => {
            return val.id === item.userName;
          });
          if (zs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderAcceptList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (selectionUser && selectionUser.length > 0) {
        selectionUser.map(item => {
          let acc = this.organizeForm.builderAcceptList.findIndex(val => {
            return val.id === item.userName;
          });
          if (acc > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderAcceptList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (usersChecked && usersChecked.length > 0) {
        usersChecked.map(item => {
          let acc = this.organizeForm.builderAcceptList.findIndex(val => {
            return val.id === item.id;
          });
          if (acc > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderAcceptList.push({
              bz: "user",
              id: item.id,
              name: item.name,
              orgName: item.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (contactSelectionOrg && contactSelectionOrg.length > 0) {
        contactSelectionOrg.map(item => {
          let accOrg = this.organizeForm.builderAcceptList.findIndex(val => {
            return val.id === item.orgId;
          });
          if (accOrg > -1) {
            this.$message({
              message: "选择的组织已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderAcceptList.push({
              bz: "org",
              id: item.orgId + "",
              orgName: item.fullOrgName,
            });
          }
        });
      }
      if (orgsChecked && orgsChecked.length > 0) {
        orgsChecked.map(item => {
          let accOrg = this.organizeForm.builderAcceptList.findIndex(val => {
            return val.id === item.id;
          });
          if (accOrg > -1) {
            this.$message({
              message: "选择的组织已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderAcceptList.push({
              bz: "org",
              id: item.id,
              orgName: item.name,
            });
          }
        });
      }

      this.organizeForm.builderAcceptListCopy = JSON.parse(
        JSON.stringify(this.organizeForm.builderAcceptList)
      );
    },
    //抄送人确定
    ccDetermine({
      usersChecked,
      orgsChecked,
      selectionUser,
      contactSelectionUser,
      contactSelectionOrg,
    }) {
      if (contactSelectionUser && contactSelectionUser.length > 0) {
        contactSelectionUser.map(item => {
          let cs = this.organizeForm.builderCsList.findIndex(val => {
            return val.id === item.userName;
          });
          if (cs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderCsList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (selectionUser && selectionUser.length > 0) {
        selectionUser.map(item => {
          let cs = this.organizeForm.builderCsList.findIndex(val => {
            return val.id === item.userName;
          });
          if (cs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderCsList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (usersChecked && usersChecked.length > 0) {
        usersChecked.map(item => {
          let cs = this.organizeForm.builderCsList.findIndex(val => {
            return val.id === item.id;
          });
          if (cs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderCsList.push({
              bz: "user",
              id: item.id,
              name: item.name,
              orgName: item.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (contactSelectionOrg && contactSelectionOrg.length > 0) {
        contactSelectionOrg.map(item => {
          let csOrg = this.organizeForm.builderCsList.findIndex(val => {
            return val.id === item.orgId;
          });
          if (csOrg > -1) {
            this.$message({
              message: "选择的组织已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderCsList.push({
              bz: "org",
              id: item.orgId + "",
              orgName: item.fullOrgName,
            });
          }
        });
      }
      if (orgsChecked && orgsChecked.length > 0) {
        orgsChecked.map(item => {
          let csOrg = this.organizeForm.builderCsList.findIndex(val => {
            return val.id === item.id;
          });
          if (csOrg > -1) {
            this.$message({
              message: "选择的组织已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderCsList.push({
              bz: "org",
              id: item.id,
              orgName: item.name,
            });
          }
        });
      }
      this.organizeForm.builderCsListCopy = JSON.parse(
        JSON.stringify(this.organizeForm.builderCsList)
      );
    },
    //接收人确定
    recipientDetermine({
      usersChecked,
      orgsChecked,
      selectionUser,
      contactSelectionUser,
    }) {
      if (contactSelectionUser && contactSelectionUser.length > 0) {
        contactSelectionUser.map(item => {
          let rec = this.organizeForm.recipientList.findIndex(val => {
            return val.id === item.userName;
          });
          if (rec > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.recipientList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (selectionUser && selectionUser.length > 0) {
        selectionUser.map(item => {
          let rec = this.organizeForm.recipientList.findIndex(val => {
            return val.id === item.userName;
          });
          if (rec > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.recipientList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      } else if (usersChecked && usersChecked.length > 0) {
        usersChecked.map(item => {
          let rec = this.organizeForm.recipientList.findIndex(val => {
            return val.id === item.id;
          });
          if (rec > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.recipientList.push({
              bz: "user",
              id: item.id,
              name: item.name,
              orgName: item.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      this.organizeForm.recipientListCopy = JSON.parse(
        JSON.stringify(this.organizeForm.recipientList)
      );
    },

    stitchingAlgorithm(orgName, userName) {
      if (orgName.length !== 0 && userName.length !== 0) {
        return orgName + "," + userName;
      } else {
        if (orgName.length !== 0) {
          return orgName;
        } else if (userName.length !== 0) {
          return userName;
        } else {
          return "";
        }
      }
    },

    closeAndTurnAroundMyDraft() {
      let self = this;
      self.$store.dispatch("tagsView/delView", self.$route).then(() => {
        self.$destroy();
      });
      self.$router.push({
        name: "backbone_myDraft",
      });
    },

    closeAndTurnAroundTo() {
      let self = this;
      self.$store.dispatch("tagsView/delView", self.$route).then(() => {
        self.$destroy();
      });
      self.$router.push({
        name: "backbone_toDoRepairOrder",
        query: {
          globalUniqueID: sessionStorage.getItem("globalUniqueID"),
          token: this.token,
        },
      });
    },

    // input 清空value
    btnClearable(val) {
      this.sheetForm[val] = "";
    },

    processing() {
      //主送人数据处理
      if (
        this.sheetForm.builderZsUserId != null &&
        this.sheetForm.builderZsUserId != "" &&
        this.sheetForm.agentManDetail != null &&
        this.sheetForm.agentManDetail != ""
      ) {
        let usersCheckedName = [];
        let usersCheckedOrgName = [];
        let usersCheckedMobilePhone = [];
        let usersCheckedId = this.sheetForm.builderZsUserId.split(",");
        let usersCheckedManDetail = this.sheetForm.agentManDetail.split(",");
        usersCheckedManDetail.forEach(element => {
          let userCheckedManDetail = element.split("-");
          usersCheckedName.push(userCheckedManDetail[0]);
          usersCheckedOrgName.push(userCheckedManDetail[1]);
          usersCheckedMobilePhone.push(userCheckedManDetail[2]);
        });
        for (var i = 0; i < usersCheckedId.length; i++) {
          this.organizeForm.builderZsList.push({
            bz: "user",
            id: usersCheckedId[i],
            name: usersCheckedName[i],
            orgName: usersCheckedOrgName[i],
            mobilePhone: usersCheckedMobilePhone[i],
          });
        }
        this.organizeForm.builderZsListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.builderZsList)
        );
      }
      //主送组织数据处理
      if (
        this.sheetForm.builderZsOrgId != null &&
        this.sheetForm.builderZsOrgId != "" &&
        this.sheetForm.builderZsOrgName != null &&
        this.sheetForm.builderZsOrgName != ""
      ) {
        let orgsCheckedName = this.sheetForm.builderZsOrgName.split(",");
        let orgsCheckedId = this.sheetForm.builderZsOrgId.split(",");
        for (var j = 0; j < orgsCheckedId.length; j++) {
          this.organizeForm.builderZsList.push({
            bz: "org",
            id: orgsCheckedId[j],
            orgName: orgsCheckedName[j],
          });
        }
        this.organizeForm.builderZsListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.builderZsList)
        );
      }
      //抄送人数据处理
      if (
        this.sheetForm.builderCsUserId != null &&
        this.sheetForm.builderCsUserId != "" &&
        this.sheetForm.copyManDetail != null &&
        this.sheetForm.copyManDetail != ""
      ) {
        let usersCheckedName = [];
        let usersCheckedOrgName = [];
        let usersCheckedMobilePhone = [];
        let usersCheckedId = this.sheetForm.builderCsUserId.split(",");
        let usersCheckedManDetail = this.sheetForm.copyManDetail.split(",");
        usersCheckedManDetail.forEach(element => {
          let userCheckedManDetail = element.split("-");
          usersCheckedName.push(userCheckedManDetail[0]);
          usersCheckedOrgName.push(userCheckedManDetail[1]);
          usersCheckedMobilePhone.push(userCheckedManDetail[2]);
        });
        for (let m = 0; m < usersCheckedId.length; m++) {
          this.organizeForm.builderCsList.push({
            bz: "user",
            id: usersCheckedId[m],
            name: usersCheckedName[m],
            orgName: usersCheckedOrgName[m],
            mobilePhone: usersCheckedMobilePhone[m],
          });
        }
        this.organizeForm.builderCsListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.builderCsList)
        );
      }
      //抄送组织数据处理
      if (
        this.sheetForm.builderCsOrgId != null &&
        this.sheetForm.builderCsOrgId != "" &&
        this.sheetForm.builderCsOrgName != null &&
        this.sheetForm.builderCsOrgName != ""
      ) {
        let orgsCheckedName = this.sheetForm.builderCsOrgName.split(",");
        let orgsCheckedId = this.sheetForm.builderCsOrgId.split(",");
        for (let n = 0; n < orgsCheckedId.length; n++) {
          this.organizeForm.builderCsList.push({
            bz: "org",
            id: orgsCheckedId[n],
            orgName: orgsCheckedName[n],
          });
        }
        this.organizeForm.builderCsListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.builderCsList)
        );
      }
    },

    //多选移除
    toggleSelection(val) {
      if (this.multipleSelection == 0) {
        this.$message({
          type: "warning",
          message: "还没有选择移除的选项，请先选择要移除的选项！",
        });
        return;
      } else {
        this.multipleSelection.forEach(row => {
          this.handleClose(val, row);
        });
      }
      this.multipleSelection;
    },
    //移除对象
    handleClose(val, tag) {
      if (val == "builderZs") {
        this.organizeForm.builderZsList.splice(
          this.arrayIndex(this.organizeForm.builderZsList, tag),
          1
        );
        this.organizeForm.builderZsListCopy.splice(
          this.arrayIndex(this.organizeForm.builderZsListCopy, tag),
          1
        );
      }

      if (val == "builderCs") {
        this.organizeForm.builderCsList.splice(
          this.arrayIndex(this.organizeForm.builderCsList, tag),
          1
        );
        this.organizeForm.builderCsListCopy.splice(
          this.arrayIndex(this.organizeForm.builderCsListCopy, tag),
          1
        );
      }
      if (val == "recipient") {
        this.organizeForm.recipientList.splice(
          this.arrayIndex(this.organizeForm.recipientList, tag),
          1
        );
        this.organizeForm.recipientListCopy.splice(
          this.arrayIndex(this.organizeForm.recipientListCopy, tag),
          1
        );
      }
    },
    //找对应对象的索引值
    arrayIndex(val1, val2) {
      for (var i = 0, len = val1.length; i < len; i++) {
        var tag = val1[i];
        if (tag.id == val2.id) {
          return i;
        }
      }
    },
    //搜索
    search(val) {
      if (val == "builderZs" && this.organizeForm.builderZsList != null) {
        this.organizeForm.builderZsListCopy = [];
        this.organizeForm.builderZsList.forEach(row => {
          if (
            row.name == this.organizeForm.builderZsName ||
            row.orgName == this.organizeForm.builderZsName
          ) {
            this.organizeForm.builderZsListCopy.push(row);
          }
        });
      }

      if (val == "builderCs" && this.organizeForm.builderCsList != null) {
        this.organizeForm.builderCsListCopy = [];
        this.organizeForm.builderCsList.forEach(row => {
          if (
            row.name == this.organizeForm.builderCsName ||
            row.orgName == this.organizeForm.builderCsName
          ) {
            this.organizeForm.builderCsListCopy.push(row);
          }
        });
      }
      if (val == "recipient" && this.organizeForm.recipientList != null) {
        this.organizeForm.recipientListCopy = [];
        this.organizeForm.recipientList.forEach(row => {
          if (
            row.name == this.organizeForm.recipientName ||
            row.orgName == this.organizeForm.recipientName
          ) {
            this.organizeForm.recipientListCopy.push(row);
          }
        });
      }
    },
    //清除搜索项
    clear(val) {
      if (val == "builderZs") {
        this.organizeForm.builderZsName = "";
        this.organizeForm.builderZsListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.builderZsList)
        );
      }

      if (val == "builderCs") {
        this.organizeForm.builderCsName = "";
        this.organizeForm.builderCsListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.builderCsList)
        );
      }
      if (val == "recipient") {
        this.organizeForm.recipientName = "";
        this.organizeForm.recipientListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.recipientList)
        );
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    entering() {
      if (
        this.organizeForm.builderZsList &&
        this.organizeForm.builderZsList.length > 0
      ) {
        //this.$set(this.sheetForm, "builderZs", "1");

        let userList = this.organizeForm.builderZsList.filter((item, index) => {
          return item.bz == "user";
        });
        let orgList = this.organizeForm.builderZsList.filter((item, index) => {
          return item.bz == "org";
        });
        if (userList && userList.length > 0) {
          let usersCheckedName = userList.map(item => {
            return item.name;
          });
          this.sheetForm.builderZsUserName = usersCheckedName.join(",");
          let usersCheckedId = userList.map(item => {
            return item.id;
          });
          this.sheetForm.builderZsUserId = usersCheckedId.join(",");
          const userDetailName = userList.map(item => {
            return item.name + "-" + item.orgName + "-" + item.mobilePhone;
          });
          this.sheetForm.agentManDetail = userDetailName.join(",");
        }
        if (orgList && orgList.length > 0) {
          let orgsCheckedName = orgList.map(item => {
            return item.orgName;
          });
          this.sheetForm.builderZsOrgName = orgsCheckedName.join(",");
          let orgsCheckedId = orgList.map(item => {
            return item.id;
          });
          this.sheetForm.builderZsOrgId = orgsCheckedId.join(",");
        }
      }

      if (
        this.organizeForm.builderCsList &&
        this.organizeForm.builderCsList.length > 0
      ) {
        let userList = this.organizeForm.builderCsList.filter((item, index) => {
          return item.bz == "user";
        });
        let orgList = this.organizeForm.builderCsList.filter((item, index) => {
          return item.bz == "org";
        });
        if (userList && userList.length > 0) {
          let usersCheckedName = userList.map(item => {
            return item.name;
          });
          this.sheetForm.builderCsUserName = usersCheckedName.join(",");
          let usersCheckedId = userList.map(item => {
            return item.id;
          });
          this.sheetForm.builderCsUserId = usersCheckedId.join(",");
          const userDetailName = userList.map(item => {
            return item.name + "-" + item.orgName + "-" + item.mobilePhone;
          });
          this.sheetForm.copyManDetail = userDetailName.join(",");
        }
        if (orgList && orgList.length > 0) {
          let orgsCheckedName = orgList.map(item => {
            return item.orgName;
          });
          this.sheetForm.builderCsOrgName = orgsCheckedName.join(",");
          let orgsCheckedId = orgList.map(item => {
            return item.id;
          });
          this.sheetForm.builderCsOrgId = orgsCheckedId.join(",");
        }
      }
    },
    //附件选择
    attachmentBrowse() {
      this.attachmentDialogVisible = true;
    },
    changeFileData(data) {
      this.sheetForm.attachmentFile = data.fileName;
      this.importForm.attachmentFileList = data.attachmentFileList;
      this.attachmentDialogVisible = false;
    },
    closeAttachmentDialog() {
      this.attachmentDialogVisible = false;
    },
    ngAttachmentBrowse() {
      this.ngAttachmentDownloadVisible = true;
    },
    async getProfessionalTypeData(val) {
      try {
        await this.getProfessionalTypeOpt(val);
        await this.getWnwjTypeOpt(val);
      } catch (error) {
        console.log(error);
      }
    },
    getProfessionalTypeOpt(val) {
      this.sheetForm.professionalType = "";
      let param = {
        dictTypeCode: 811039,
        parentCode: val,
      };
      return new Promise(resolve => {
        apiGetEnumList(param).then(res => {
          if (res.code == 200) {
            this.professionalTypeOptions = res?.data ?? [];
          }
          resolve("success");
        });
      });
    },
    getWnwjTypeOpt(val) {
      this.sheetForm.wnwjType = "";
      let param = {
        dictTypeCode: 811048,
        parentCode: val,
      };
      return new Promise(resolve => {
        apiGetEnumList(param).then(res => {
          if (res.code == 200) {
            this.wnwjTypeOptions = res?.data ?? [];
          }
          resolve("success");
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.supervise-create-order {
  .head-title {
    font-size: 24px;
    line-height: 28px;
  }
  .head-handle-wrap {
    text-align: right;
  }
  .card-title {
    font-size: 17px;
    letter-spacing: 0;
    padding-left: 10px;
  }
  .select_style {
    background: #b50b14;
    border-radius: 2px;
    border-color: #b50b14;
  }
}
</style>
