<template>
  <div>
    <el-form
      ref="stageBackForm"
      :model="stageBackForm"
      :rules="stageBackFormRules"
    >
      <el-form-item
        label="反馈内容:"
        prop="processSuggestion"
        label-width="90px"
      >
        <el-input
          type="textarea"
          :rows="3"
          placeholder="请输入内容"
          v-model="stageBackForm.processSuggestion"
          style="width: 270px"
          show-word-limit
          maxlength="1000"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="处理过程:" label-width="90px">
        <dict-select
          :value.sync="stageBackForm.opContent"
          :dictId="10048"
          style="width: 270px"
        />
      </el-form-item>
      <el-form-item
        label="是否自然灾害突发事件及其他:"
        prop="isNaturalDisaster"
        :rules="{
          required: true,
          message: '请选择是否自然灾害突发事件及其他',
        }"
        label-width="215px"
      >
        <el-radio-group
          v-model="stageBackForm.isNaturalDisaster"
          style="width: 100%"
        >
          <el-radio label="0">否</el-radio>
          <el-radio label="1">是</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="附件:" label-width="90px" prop="attachmentName">
        <el-tag
          class="fileName_style_download"
          closable
          v-for="(item, index) in fddxFileArr"
          :key="index"
          @close="closeAndDeleteFile(item)"
          @click="downloadAppendixFile(item)"
          :title="item.name"
        >
          <div class="text-truncate">{{ item.name }}</div>
        </el-tag>
        <el-tag
          class="fileName_style"
          closable
          v-for="(item, index) in importForm.relatedFilesFileList"
          :key="index"
          @close="close(item)"
          :title="item.name"
        >
          <div class="text-truncate">{{ item.name }}</div>
        </el-tag>
        <el-button size="mini" type="primary" @click="relatedFilesBrowse"
          >+上传附件</el-button
        >
      </el-form-item>
    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: right">
      <el-button
        type="primary"
        @click="handleSubmit('stageBackForm')"
        v-loading.fullscreen.lock="stageBackFullscreenLoading"
        >提交</el-button
      >
      <el-button @click="onResetStageBackForm">重 置</el-button>
    </div>
    <el-dialog
      width="420px"
      title="附件选择"
      :visible.sync="relatedFilesDialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <file-upload
        @change="changeFileData"
        @cancel="closeAttachmentDialog"
      ></file-upload>
    </el-dialog>
  </div>
</template>
<script>
import {
  apiStageFeedBack,
  apiDeleteFdFile,
  apiDownloadAppendixFile,
} from "../api/CommonApi";
import FileUpload from "@plugin/backbone/modules/workOrder/components/FileUpload.vue";

import DictSelect from "@plugin/backbone/modules/workOrder/components/DictSelect.vue";
import { mixin } from "../../../../../../mixins";
export default {
  props: {
    common: Object,
  },
  name: "StageFeedback",
  components: { DictSelect, FileUpload },
  mixins: [mixin],
  data() {
    return {
      stageBackForm: {
        woId: "",
        processInstId: "",
        processDefId: "",
        workItemId: "",
        processNode: "",
        processSuggestion: "",
        opContent: "",
        isNaturalDisaster: "0",
      },
      stageBackFormRules: {
        processSuggestion: [
          {
            required: true,
            message: "请填写反馈内容",
          },
          {
            validator: this.checkLength,
            max: 1000,
            form: "stageBackForm",
            messsage: "已超过填写字数上限",
          },
        ],
      },
      stageBackFullscreenLoading: false,
      fddxFileArr: [],
      importForm: {
        relatedFilesFileList: [],
      },
      relatedFilesDialogVisible: false,
    };
  },
  mounted() {
    this.stageBackForm.woId = this.common.woId;
    this.stageBackForm.processInstId = this.common.processInstId;
    this.stageBackForm.processDefId = this.common.processDefId;
    this.stageBackForm.workItemId = this.common.workItemId;
    this.stageBackForm.processNode = this.common.processNode;
  },
  methods: {
    handleSubmit(formName) {
      this.$refs[formName].validate((valid, a) => {
        if (this.uncheck(a)) {
          this.stageBackFullscreenLoading = true;
          let formData = new FormData();
          if (this.importForm.relatedFilesFileList.length > 0) {
            for (let item of this.importForm.relatedFilesFileList) {
              formData.append("files", item.raw);
            }
          } else {
            formData.append("files", "");
          }
          formData.append("jsonParam", JSON.stringify(this.stageBackForm));
          apiStageFeedBack(formData)
            .then(res => {
              if (res.status == "0") {
                this.$message.success("阶段反馈完成");
                this.onResetStageBackForm();
                this.$emit("stageBackDialogClose");
              } else {
                this.$message.error("阶段反馈失败");
              }
              this.stageBackFullscreenLoading = false;
            })
            .catch(error => {
              console.log(error);
              this.$message.error("阶段反馈失败");
              this.stageBackFullscreenLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    onResetStageBackForm() {
      this.stageBackForm = {
        ...this.$options.data,
        woId: this.common.woId,
        workItemId: this.common.workItemId,
        processInstId: this.common.processInstId,
        processDefId: this.common.processDefId,
        processNode: this.common.processNode,
      };
      this.importForm.relatedFilesFileList = [];
    },
    closeAndDeleteFile(tag) {
      this.deleteFile(tag);
    },
    deleteFile(tag) {
      let param = {
        attId: tag.id,
        linkId: this.backSingleForm.linkId,
      };
      apiDeleteFdFile(param)
        .then(res => {
          if (res.status == "0") {
            this.fddxFileArr.splice(this.fdFileArr.indexOf(tag), 1);
            this.backSingleForm.appendix = JSON.stringify(this.fddxFileArr);
            if (this.fddxFileArr.length == 0) {
              this.backSingleForm.attachmentName = null;
            }
            this.$message.success("附件删除成功");
          } else {
            this.$$message.error("附件删除失败");
          }
        })
        .catch(error => {
          this.$message.error("附件删除失败");
          console.log(error);
        });
    },
    downloadAppendixFile(data) {
      let param = {
        attId: data.id,
      };
      apiDownloadAppendixFile(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("文件下载成功");
          } else {
            this.$message.error("文件下载失败");
          }
        })
        .catch(error => {
          console.log(error);
          this.$message.error("文件下载失败");
        });
    },
    relatedFilesBrowse() {
      this.relatedFilesDialogVisible = true;
    },
    changeFileData(data) {
      // this.backSingleForm.relatedFiles = data.fileName;
      // this.backSingleForm.attachmentName = data.fileName;
      this.importForm.relatedFilesFileList = data.attachmentFileList;
      this.relatedFilesDialogVisible = false;
    },
    closeAttachmentDialog() {
      this.relatedFilesDialogVisible = false;
    },
    close(tag) {
      this.importForm.relatedFilesFileList.splice(
        this.importForm.relatedFilesFileList.indexOf(tag),
        1
      );
      if (this.importForm.relatedFilesFileList.length == 0) {
        this.backSingleForm.attachmentName = null;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.fileName_style_download {
  margin-right: 3px;
  cursor: pointer;
  vertical-align: middle;

  div {
    display: inline-block;
    max-width: 120px;
    vertical-align: top;
  }
}
.fileName_style {
  margin-right: 3px;
  vertical-align: middle;

  div {
    display: inline-block;
    max-width: 120px;
    vertical-align: top;
  }
}
</style>
