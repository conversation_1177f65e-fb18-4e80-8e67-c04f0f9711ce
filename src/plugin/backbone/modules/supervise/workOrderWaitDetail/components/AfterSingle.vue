<template>
  <div class="afterSingle">
    <el-form ref="afterSingleForm" :model="afterSingleForm" :rules="afterSingleFormRules" label-width="150px">
      <el-form-item label="追单内容" prop="content">
        <el-input type="textarea" :rows="2" v-model="afterSingleForm.content" style="width: 390px" show-word-limit
          maxlength="255">
        </el-input>
      </el-form-item>

      <el-form-item label="附件:">
        <div style="width: 400px">
          <el-tag class="fileName_style" closable v-for="(item, index) in importForm.attachmentFileList" :key="index"
            @close="close(item)" :title="item.name">
            <div class="text-truncate">{{ item.name }}</div>
          </el-tag>
          <el-button size="mini" type="primary" @click="attachmentBrowse">+上传附件</el-button>
        </div>
      </el-form-item>


      <el-form-item label="是否备份中心管辖:" v-if="
        common.isObject == 1 &&
        common.sheetStatus != '待定性审核' &&
        common.sheetStatus != '挂起'
      " :rules="{
          required: true,
          message: '请选择是否备份中心管辖',
        }" prop="backCenterControlled">
        <el-radio-group v-model="afterSingleForm.backCenterControlled">
          <el-radio label="1">是</el-radio>
          <el-radio label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>

    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: right">
      <el-button type="primary" @click="handleSubmit('afterSingleForm')"
        v-loading.fullscreen.lock="afterSingleFullscreenLoading">提 交</el-button>
      <el-button @click="onResetAfterSingleForm">重 置</el-button>
    </div>
    <el-dialog width="420px" title="附件选择" :visible.sync="attachmentDialogVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" append-to-body>
      <file-upload @change="changeFileData" @cancel="closeAttachmentDialog"></file-upload>
    </el-dialog>
  </div>
</template>
<script>
import { apiAfterSingle } from "../api/CommonApi";
import { workflowQueryBackControlled } from "@/plugin/backbone/modules/api/generalApi";

import FileUpload from "@/plugin/backbone/modules/workOrder/components/FileUpload.vue";
import { mixin } from "../../../../../../mixins";

export default {
  name: "AfterSingle",
  props: {
    common: Object,
  },
  components: { FileUpload },
  mixins: [mixin],
  data() {
    return {
      afterSingleForm: {
        content: null,
        backCenterControlled: "0",
      },
      afterSingleFormRules: {
        content: [
          {
            required: true,
            message: "请填写追单内容",
          },
          {
            validator: this.checkLength,
            max: 255,
            form: "afterSingleForm",
            messsage: "已超过填写字数上限",
          },
        ],
      },

      multipleSelection: [],
      afterSingleFullscreenLoading: false,
      attachmentDialogVisible: false,
      importForm: {
        //附件
        attachmentFileList: [],
      },
      rawAgentManIdArr: [],
      rawAgenManDeptCodeArr: [],
    };
  },
  mounted() {
    // 只有在展示"是否备份中心管辖"字段时才调用回填接口
    if (this.common.isObject == 1 && this.common.sheetStatus != '待定性审核' && this.common.sheetStatus != '挂起') {
      workflowQueryBackControlled({ woId: this.common.woId })
        .then(backRes => {
          if (backRes.status == "0" && backRes.data) {
            this.afterSingleForm.backCenterControlled = backRes.data;
          }
        })
        .catch(backError => {
          console.log("回填是否备份中心管辖字段失败:", backError);
        });
    }
  },
  watch: {},
  methods: {
    handleSubmit(formName) {
      // this.entering();
      this.$refs[formName].validate((valid, a) => {
        if (this.uncheck(a)) {
          this.afterSingleFullscreenLoading = true;
          let formData = new FormData();
          if (this.importForm.attachmentFileList.length > 0) {
            for (let item of this.importForm.attachmentFileList) {
              formData.append("files", item.raw);
            }
          } else {
            formData.append("files", "");
          }
          //formData.append("isSender", this.common.isObject);
          let param = {
            woId: this.common.woId,
            processInstId: this.common.processInstId,
            processDefId: this.common.processDefId,
            workItemId: null, //this.common.workItemId,
            processNode: this.common.processNode,
            appendContent: this.afterSingleForm.content,
          };
          // 只有在展示"是否备份中心管辖"字段时才传递给接口
          if (this.common.isObject == 1 && this.common.sheetStatus != '待定性审核' && this.common.sheetStatus != '挂起') {
            param.backCenterControlled = this.afterSingleForm.backCenterControlled || "0";
          }
          formData.append("jsonParam", JSON.stringify(param));
          apiAfterSingle(formData)
            .then(res => {
              if (res.status == "0") {
                this.$message.success("追单提交成功");
                this.onResetAfterSingleForm();
                this.$emit("closeAfterSingleDialog", this.common.isObject);
              } else {
                this.$message.error("追单提交失败");
              }
              this.afterSingleFullscreenLoading = false;
            })
            .catch(error => {
              console.log(error);
              this.afterSingleFullscreenLoading = false;
              this.$message.error("追单提交失败");
            });
        } else {
          return false;
        }
      });
    },
    //附件选择
    attachmentBrowse() {
      this.attachmentDialogVisible = true;
    },
    changeFileData(data) {
      this.importForm.attachmentFileList = data.attachmentFileList;
      this.attachmentDialogVisible = false;
    },
    closeAttachmentDialog() {
      this.attachmentDialogVisible = false;
    },
    onResetAfterSingleForm() {
      this.afterSingleForm = {
        content: null,
        backCenterControlled: "0",
      };

      this.importForm.attachmentFileList = [];
    },

    close(tag) {
      this.importForm.attachmentFileList.splice(
        this.importForm.attachmentFileList.indexOf(tag),
        1
      );
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-tree {
  padding-top: 15px;
  padding-left: 10px;

  // 不可全选样式
  .el-tree-node {
    .is-leaf+.el-checkbox .el-checkbox__inner {
      display: inline-block;
    }

    .el-checkbox .el-checkbox__inner {
      display: none;
    }
  }
}

.afterSingle {
  .fileName_style {
    margin-right: 3px;
    vertical-align: middle;

    div {
      display: inline-block;
      max-width: 360px;
      vertical-align: top;
    }
  }
}
</style>
