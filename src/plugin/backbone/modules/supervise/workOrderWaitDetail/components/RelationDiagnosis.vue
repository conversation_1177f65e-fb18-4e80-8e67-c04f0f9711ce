<template>
  <el-card shadow="always" class="card" :body-style="{ padding: '10px 24px' }">
    <div class="header clearfix">
      <span class="header-title">关联诊断</span>
      <template v-if="relationDiagnosis.evaluationScenario">
        <evaluation
          :woId="woId"
          :relationDiagnosis="relationDiagnosis"
        ></evaluation>
      </template>
      <div class="header-right">
        <el-button type="primary" size="mini" @click="refreshRelationDiagnosis"
          >刷新</el-button
        >
      </div>
    </div>

    <div class="content" v-loading="contentLoading">
      <el-collapse v-model="activeNames">
        <template v-if="ppResult">
          <el-collapse-item name="1">
            <span class="collapse-title" slot="title">诊断分析字段</span>
            <el-descriptions title="" class="descriptions">
              <el-descriptions-item label="预处理状态">{{
                relationDiagnosis.ppStatus
              }}</el-descriptions-item>
              <el-descriptions-item label="根因域">{{
                relationDiagnosis.ppCauseDomain
              }}</el-descriptions-item>
              <el-descriptions-item label="根因类型">{{
                relationDiagnosis.ppAlarmReason
              }}</el-descriptions-item>
              <el-descriptions-item label="设备型号">
                {{ relationDiagnosis.neModel }}
              </el-descriptions-item>
              <el-descriptions-item label="软件版本">{{
                relationDiagnosis.softwareVersion
              }}</el-descriptions-item>
              <el-descriptions-item label="硬件版本">
                {{ relationDiagnosis.hardwareVersion }}
              </el-descriptions-item>
              <el-descriptions-item label="根因位置" :span="3">
                <div
                  style="white-space: pre-wrap"
                  v-html="relationDiagnosis.ppCausePosition"
                ></div>
              </el-descriptions-item>
              <el-descriptions-item label="业务影响范围" :span="3">
                <div
                  style="white-space: pre-wrap"
                  v-html="relationDiagnosis.effectDesc"
                ></div>
              </el-descriptions-item>
              <el-descriptions-item label="预处理过程" :span="3">
                <div
                  style="white-space: pre-wrap"
                  v-html="relationDiagnosis.ppProcess"
                ></div>
              </el-descriptions-item>
              <el-descriptions-item label="预处理结果" :span="3">
                <div
                  style="white-space: pre-wrap"
                  v-html="relationDiagnosis.ppResult"
                ></div>
              </el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>

          <el-collapse-item name="2" v-if="isShowTopo">
            <span class="collapse-title" slot="title">拓扑图</span>
            <div>
              <iframe
                v-if="topoSrc"
                id="topo"
                :src="topoSrc"
                frameborder="0"
                width="100%"
                scrolling="true"
                style="height: 500px; overflow-y: auto"
              ></iframe>
            </div>
          </el-collapse-item>
        </template>
        <template v-if="analysisStatus == 1">
          <el-collapse-item name="3">
            <span class="collapse-title" slot="title"
              >诊断分析字段<span style="color: #b50b14"
                >（以下诊断分析数据来源于网络AI中心，仅供参考。按照故障原因可能性“从高到低”展示。）</span
              ></span
            >
            <el-table
              :data="tableData"
              :show-header="showHeader"
              stripe
              v-loading="tableLoading"
              style="margin-top: 10px"
              :header-cell-style="{ 'text-align': 'center' }"
              :cell-style="{ 'text-align': 'center' }"
            >
              <el-table-column prop="AnalCauseDomain" label="故障所属专业">
                <template slot-scope="scope">
                  <span style="margin-left: 5%">
                    <img :src="getImageUrl(scope.$index)" />
                  </span>
                  <span style="margin-left: 3%"
                    >故障所属专业：{{ scope.row.AnalCauseDomain }}</span
                  >
                </template>
              </el-table-column>
              <el-table-column prop="AnalAlarmType" label="故障分类">
                <template slot-scope="scope">
                  <span>故障分类：{{ scope.row.AnalAlarmType }}</span>
                </template></el-table-column
              >
              <el-table-column prop="AnalResult" label="故障原因"
                ><template slot-scope="scope">
                  <span>故障原因：{{ scope.row.AnalResult }}</span>
                </template></el-table-column
              >
            </el-table>
          </el-collapse-item>
        </template>
      </el-collapse>
    </div>
  </el-card>
</template>

<script>
import {
  apiGetRelationDiagnosis,
  apiAnalyzeRelationDiagnosis,
  apiTopoIsShow,
} from "../api/RelationDiagnosis";
import Evaluation from "@/plugin/backbone/components/Evaluate/Evaluation.vue";

export default {
  name: "RelationDiagnosis",
  components: { Evaluation },
  props: {
    woId: String,
    ppResult: {
      type: String,
      default: null,
    },
    analysisStatus: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      relationDiagnosis: {
        ppStatus: "",
        ppCauseDomain: "",
        ppAlarmReason: "",
        neModel: "",
        softwareVersion: "",
        hardwareVersion: "",
        ppCausePosition: "",
        ppProcess: "",
        ppResult: "",
      },
      activeNames: [],
      topoSrc: null,
      isShowTopo: false,
      contentLoading: false,
      timer: null,

      tableData: [],
      tableLoading: false,
      showHeader: false,
    };
  },
  mounted() {
    let self = this;
    if (self.ppResult) {
      this.getRelationDiagnosis();
    }
    if (self.analysisStatus == 1) {
      this.getAnalyzeData();
    }
  },
  destroyed() {
    this.clearTimer();
  },
  methods: {
    refreshRelationDiagnosis() {
      let self = this;
      if (self.ppResult) {
        this.getRelationDiagnosis();
      }
      if (self.analysisStatus == 1) {
        this.getAnalyzeData();
      }
    },
    getRelationDiagnosis() {
      this.contentLoading = true;
      let param = {
        woId: this.woId,
      };
      apiGetRelationDiagnosis(param)
        .then(res => {
          if (res.status == "0") {
            let self = this;
            if (res.data.rows.length > 0) {
              self.relationDiagnosis = res.data?.rows[0];
              let ppResult = self.relationDiagnosis?.ppResult ?? "";
              if (ppResult) {
                self.activeNames = ["1", "2"];
              } else {
                self.activeNames = [];
              }
              if (
                ppResult.indexOf("断点段落文字描述：") != "-1" &&
                ppResult.indexOf("断点gis的查看地址：") != "-1"
              ) {
                let frontContent = ppResult.split("断点段落文字描述：")[0];
                let endContent = ppResult.split("断点段落文字描述：")[1];
                self.relationDiagnosis.ppResult =
                  frontContent +
                  "断点段落文字描述：<span style='font-weight:700'>" +
                  endContent.split("断点gis的查看地址：")[0] +
                  "</span>断点gis的查看地址：<a target='_blank' style='color: #c43c43;' href=" +
                  endContent.split("断点gis的查看地址：")[1] +
                  ">" +
                  endContent.split("断点gis的查看地址：")[1] +
                  "</a>";
              } else if (ppResult.indexOf("断点段落文字描述：") != "-1") {
                let frontContent = ppResult.split("断点段落文字描述：")[0];
                let endContent = ppResult.split("断点段落文字描述：")[1];
                self.relationDiagnosis.ppResult =
                  frontContent +
                  "断点段落文字描述：<span style='font-weight:700'>" +
                  endContent +
                  "</span>";
              } else if (ppResult.indexOf("断点gis的查看地址：") != "-1") {
                self.relationDiagnosis.ppResult =
                  ppResult.split("断点gis的查看地址：")[0] +
                  "断点gis的查看地址：<a target='_blank' style='color: #c43c43;' href=" +
                  ppResult.split("断点gis的查看地址：")[1] +
                  ">" +
                  ppResult.split("断点gis的查看地址：")[1] +
                  "</a>";
              }

              let topoParam = {
                alarmId: res.data.rows[0].alarmStaId,
                eventTime: res.data.rows[0].alarmCreateTime,
              };
              apiTopoIsShow(topoParam)
                .then(topoRes => {
                  if (topoRes.status == 200) {
                    self.topoSrc = res.data.rows[0].url + topoRes.data.url;
                    self.isShowTopo = true;
                  } else {
                    self.isShowTopo = false;
                  }
                })
                .catch(error => {
                  console.log(error);
                  self.isShowTopo = false;
                });
            } else {
              self.activeNames = [];
            }
            if (!this._isDestroyed) {
              this.timer = setTimeout(this.getRelationDiagnosis, 60000 * 3);
            }
          }
          this.contentLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.contentLoading = false;
          self.isShowTopo = false;
        });
    },
    clearTimer() {
      clearTimeout(this.timer);
      this.timer = null;
    },
    getAnalyzeData() {
      this.tableLoading = true;
      let param = {
        woId: this.woId,
      };
      apiAnalyzeRelationDiagnosis(param)
        .then(res => {
          if (res.status == "0") {
            this.tableData = res?.data ?? [];
          }
        })
        .catch(error => {
          console.log(error);
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    getImageUrl(val) {
      return require("../../../workOrder/workOrderWaitDetail/assets/img/" +
        val +
        ".png");
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../../../workOrder/workOrderWaitDetail/assets/common.scss";
::v-deep .el-collapse-item {
  .el-collapse-item__header {
    background-color: #fafafa;
    padding-left: 20px;
  }
}
</style>
