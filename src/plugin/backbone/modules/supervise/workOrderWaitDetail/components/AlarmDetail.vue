<template>
  <el-card shadow="always" class="card" :body-style="{ padding: '10px 24px' }">
    <div class="header clearfix">
      <span class="header-title">告警详情</span>
      <div class="header-right">
        <el-button type="primary" size="mini" @click="getTableData('refresh')">刷新</el-button>
        <el-button type="primary" size="mini" @click="selectDetailDialogVisible = true">查询</el-button>
        <el-button type="primary" size="mini" v-if="tableData.length > 0" @click="syncClearAlarm"
          v-loading.fullscreen.lock="syncClearAlarmFullscreenLoading">同步清除告警</el-button>
        <el-button type="primary" size="mini" v-if="showClearAlarmApply" @click="syncClearAlarmApplyfor"
          v-loading.fullscreen.lock="ApplyforLoading">清除告警申请</el-button>
        <el-button type="primary" size="mini" v-if="isShowManualConfirm" @click="confirmDialogVisible = true"
          v-loading.fullscreen.lock="ApplyforLoading">人工确认清除告警</el-button>
      </div>
    </div>

    <div class="_el-table">
      <el-table :data="tableData" border stripe v-loading="tableLoading" @row-click="opentableList">
        <el-table-column type="index" width="60px" label="序号">
        </el-table-column>
        <el-table-column prop="professionalType" label="专业" width="120" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="neName" label="告警对象" width="150" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="alarmTitle" label="标题" width="200" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="orgType" label="告警类型" width="120" show-overflow-tooltip>
        </el-table-column>
        <el-table-column label="告警定位" width="200" :show-overflow-tooltip="true" prop="alarmLocation">
        </el-table-column>
        <el-table-column prop="isMajorAlarm" label="主告警" width="80">
        </el-table-column>
        <el-table-column prop="isAppendedAlarm" label="追加告警" width="90">
        </el-table-column>
        <el-table-column prop="alarmCreateTime" label="故障发生时间" width="160" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="loadTime" label="添加时间" width="160" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="autoClearTime" label="自动清除时间" width="160" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="manualClearTime" label="手动清除时间" width="160" show-overflow-tooltip>
        </el-table-column>
        <!-- <el-table-column
          label="人工确认清除时间"
          prop="manualAckClearTime"
          width="160"
        >
        </el-table-column> -->
      </el-table>
    </div>
    <pagination ref="pagination" fromPage="alarmDetail" :total="form.total" :appendAlarmTotal="appendAlarmTotal"
      :page.sync="form.pageNum" :limit.sync="form.pageSize" layout="->, total, sizes, prev, pager, next"
      @change="getTableData('filterQuery')" />
    <el-dialog title="快速查询" :visible.sync="selectDetailDialogVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="selectDetailDialogClose" width="550px">
      <el-form ref="selectDetailForm" :model="selectDetailForm" label-width="100px" :rules="selectDetailFormRules">
        <el-form-item label="标题:" prop="alarmTitle">
          <el-input v-model="selectDetailForm.alarmTitle" style="width: 300px" clearable maxlength="400"></el-input>
        </el-form-item>
        <el-form-item label="主告警:">
          <el-select v-model="selectDetailForm.isMajorAlarm" clearable style="width: 300px">
            <el-option label="是" value="1"></el-option>
            <el-option label="否" value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="追加告警:">
          <el-select v-model="selectDetailForm.isAppendAlarm" clearable style="width: 300px">
            <el-option label="是" value="1"></el-option>
            <el-option label="否" value="0"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="selectDetailSubmit()" v-loading.fullscreen.lock="selectDetailScrrenLoading">提
          交</el-button>
        <el-button @click="onResetTurnSingle">重 置</el-button>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="tableListVisible" :close-on-click-modal="false" :close-on-press-escape="false"
      @close="selectDetailDialogClose" width="1300px">
      <div style="height: 500px; overflow-y: auto">
        <el-descriptions class="margin-top" title="告警详情" :column="3" border>
          <el-descriptions-item>
            <template slot="label"> 网管告警流水号 </template>
            <div class="tdOverflow" :title="tableListData.alarmId">
              {{ tableListData.alarmId }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 网管告警ID </template>
            <div class="tdOverflow" :title="tableListData.alarmStaId">
              {{ tableListData.alarmStaId }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 原始告警号 </template>
            <div class="tdOverflow" :title="tableListData.oriAlarmId">
              {{ tableListData.oriAlarmId }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 告警标题 </template>
            <div class="tdOverflow" :title="tableListData.alarmTitle">
              {{ tableListData.alarmTitle }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 告警编码 </template>
            <div class="tdOverflow" :title="tableListData.nmsAlarmSerial">
              {{ tableListData.nmsAlarmSerial }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 告警产生时间 </template>
            <div class="tdOverflow" :title="tableListData.alarmCreateTime">
              {{ tableListData.alarmCreateTime }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 设备类型 </template>
            <div class="tdOverflow" :title="tableListData.neType">
              {{ tableListData.neType }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 专业类型 </template>
            <div class="tdOverflow" :title="tableListData.professionalType">
              {{ tableListData.professionalType }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 设备厂家 </template>
            <div class="tdOverflow" :title="tableListData.alarmVendor">
              {{ tableListData.alarmVendor }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 设备型号 </template>
            <div class="tdOverflow" :title="tableListData.equipType">
              {{ tableListData.equipType }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 网元名称 </template>
            <div class="tdOverflow" :title="tableListData.neName">
              {{ tableListData.neName }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 告警对象类型 </template>
            <div class="tdOverflow" :title="tableListData.locateNeClass">
              {{ tableListData.locateNeClass }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 告警对象名称 </template>
            <div class="tdOverflow" :title="tableListData.locateNeName">
              {{ tableListData.locateNeName }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 告警级别 </template>
            <div class="tdOverflow" :title="tableListData.alarmLevel">
              {{ tableListData.alarmLevel }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 告警逻辑分类 </template>
            <div class="tdOverflow" :title="tableListData.alarmType">
              {{ tableListData.alarmType }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 告警逻辑子类 </template>
            <div class="tdOverflow" :title="tableListData.alarmSubType">
              {{ tableListData.alarmSubType }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 告警省份 </template>
            <div class="tdOverflow" :title="tableListData.alarmProvince">
              {{ tableListData.alarmProvince }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 告警地市 </template>
            <div class="tdOverflow" :title="tableListData.alarmRegion">
              {{ tableListData.alarmRegion }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 告警区县 </template>
            <div class="tdOverflow" :title="tableListData.alarmCity">
              {{ tableListData.alarmCity }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 告警所属场馆 </template>
            <div class="tdOverflow" :title="tableListData.alarmArea">
              {{ tableListData.alarmArea }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 告警定位 </template>
            <div class="tdOverflow" :title="tableListData.alarmLocation">
              {{ tableListData.alarmLocation }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 告警描述 </template>
            <div class="tdOverflow" :title="tableListData.alarmDetail">
              {{ tableListData.alarmDetail }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 告警处理响应级别 </template>
            <div class="tdOverflow" :title="tableListData.alarmHandleLevel">
              {{ tableListData.alarmHandleLevel }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 一级网络级别 </template>
            <div class="tdOverflow" :title="tableListData.networkTypeTop">
              {{ tableListData.networkTypeTop }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 网络业务类型 </template>
            <div class="tdOverflow" :title="tableListData.networkType">
              {{ tableListData.networkType }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 网格名称 </template>
            <div class="tdOverflow" :title="tableListData.gridName">
              {{ tableListData.gridName }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 机房名称 </template>
            <div class="tdOverflow" :title="tableListData.roomName">
              {{ tableListData.roomName }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 局站名称 </template>
            <div class="tdOverflow" :title="tableListData.ttSiteName">
              {{ tableListData.ttSiteName }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 经度 </template>
            <div class="tdOverflow" :title="tableListData.longitude">
              {{ tableListData.longitude }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 纬度 </template>
            <div class="tdOverflow" :title="tableListData.latitude">
              {{ tableListData.latitude }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 故障影响专业 </template>
            <div class="tdOverflow" :title="tableListData.effectProfessional">
              {{ tableListData.effectProfessional }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 网元ID </template>
            <div class="tdOverflow" :title="tableListData.neId">
              {{ tableListData.neId }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 电路名称 </template>
            <div class="tdOverflow" :title="tableListData.circuitNo">
              {{ tableListData.circuitNo }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 告警类别 </template>
            <div class="tdOverflow" :title="tableListData.orgType">
              {{ tableListData.orgType }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 覆盖场景 </template>
            <div class="tdOverflow" :title="tableListData.coverScene">
              {{ tableListData.coverScene }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 业务系统 </template>
            <div class="tdOverflow" :title="tableListData.businessSystem">
              {{ tableListData.businessSystem }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 光缆段名称 </template>
            <div class="tdOverflow" :title="tableListData.opticFiberCableSegment">
              {{ tableListData.opticFiberCableSegment }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 所属传输系统 </template>
            <div class="tdOverflow" :title="tableListData.transSystemName">
              {{ tableListData.transSystemName }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 故障区间 </template>
            <div class="tdOverflow" :title="tableListData.faultSection">
              {{ tableListData.faultSection }}
            </div>
          </el-descriptions-item>
          <!-- <el-descriptions-item>
            <template slot="label"> 是否共建共享 </template>
            <div class="tdOverflow" :title="tableListData.isShare">
              {{ tableListData.isShare }}
            </div>
          </el-descriptions-item> -->
          <el-descriptions-item>
            <template slot="label"> 网管告警级别 </template>
            <div class="tdOverflow" :title="tableListData.faultSection">
              {{ tableListData.faultSection }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 告警设备标识 </template>
            <div class="tdOverflow" :title="tableListData.faultSection">
              {{ tableListData.faultSection }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 告警定位对象ID </template>
            <div class="tdOverflow" :title="tableListData.faultSection">
              {{ tableListData.faultSection }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 告警来源 </template>
            <div class="tdOverflow" :title="tableListData.faultSection">
              {{ tableListData.faultSection }}
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    <el-dialog title="人工确认清除告警" :visible="confirmDialogVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="selectDetailDialogClose" width="550px">
      <el-form :model="confirmForm" :rules="rules">
        <el-form-item label="告警清除时间:" prop="manualAckClearTime" required>
          <el-date-picker v-model="confirmForm.manualAckClearTime" type="datetime" placeholder="请选择清除时间"
            value-format="yyyy-MM-dd HH:mm:ss" style="width: 60%" />
        </el-form-item>
        <el-form-item class="foolter_button">
          <el-button type="primary" @click="syncClearAlarmConfirm">提交</el-button>
          <el-button @click="resetconfirmForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </el-card>
</template>

<script>
import Pagination from "../../../workOrder/components/Pagination.vue";
import { apiQueryAlarmDetail, apiSyncClear } from "../api/CommonApi";
import moment from "moment";
import { apiApplyCleanAlarm, apiCheckClearAlarm } from "../api/AlarmDetail";
import { mixin } from "../../../../../../mixins";
export default {
  name: "AlarmDetail",
  components: {
    Pagination,
  },
  props: {
    isShowClearAlarmApply: { type: Boolean, default: false },
    isShowManualConfirm: { type: Boolean, default: false },
    occurrenceTime: {
      type: String,
      default: "",
    },
    common: {
      type: Object,
      default: null,
    },
  },
  mixins: [mixin],
  data() {
    var validHappenTime = (rule, value, callback) => {
      if (value === "" || value === null) {
        callback(new Error("请选择清除时间"));
      } else {
        let seconds = moment(
          this.confirmForm.manualAckClearTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(moment(this.occurrenceTime, "YYYY-MM-DD HH:mm:ss"), "seconds");
        if (seconds < 0) {
          callback(new Error("“清除时间”不能早于“发生时间”，请重新选择"));
        } else {
          callback();
        }
      }
    };
    return {
      form: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      appendAlarmTotal: 0,
      selectDetailForm: {
        alarmTitle: "",
        isMajorAlarm: "",
        isAppendAlarm: "",
      },
      selectDetailFormRules: {
        alarmTitle: [
          {
            validator: this.checkLength,
            max: 400,
            form: "selectDetailForm",
            messsage: "已超过填写字数上限",
          },
        ],
      },
      //确认清除
      confirmForm: {
        opinion: "同意",
        manualAckClearTime: "",
      },
      tableData: [],
      tableLoading: false,
      selectDetailDialogVisible: false,
      oneApplyFor: false,
      confirmDialogVisible: false,
      tableListVisible: false,
      ApplyforDisabledArr: [],
      tableListData: {},

      selectDetailScrrenLoading: false,
      alarmStaIdArr: [],
      alarmCreateTime: "",
      syncClearAlarmFullscreenLoading: false,
      ApplyforLoading: false,
      confirmLoading: false,
      rules: {
        opinion: [{ required: true, message: "请选择意见", trigger: "change" }],
        manualAckClearTime: [
          {
            validator: validHappenTime,
            required: true,
          },
        ],
      },
      isBackSingle: false, //是否可以返单
    };
  },
  computed: {
    showClearAlarmApply() {
      return this.isShowClearAlarmApply;
    },
  },
  mounted() {
    this.confirmForm.manualAckClearTime = moment(Date.now()).format(
      "YYYY-MM-DD HH:mm:ss"
    );
    this.getTableData("firstQuery");
  },
  methods: {
    getTableData(type) {
      if (type == "refresh") {
        this.selectDetailForm.alarmTitle = "";
        this.selectDetailForm.isMajorAlarm = "";
        this.selectDetailForm.isAppendAlarm = "";
      }
      this.tableLoading = true;
      let realParam = {
        woId: this.common.woId,
        alarmTitle: this.selectDetailForm.alarmTitle,
        isMajorAlarm: this.selectDetailForm.isMajorAlarm,
        isAppendedAlarm: this.selectDetailForm.isAppendAlarm,
      };
      let param = {
        pageIndex: this.form.pageNum,
        pageSize: this.form.pageSize,
        param1: JSON.stringify(realParam),
      };
      apiQueryAlarmDetail(param)
        .then(res => {
          if (res.status == "0") {
            this.tableData = res?.data?.rows ?? [];
            this.form.total = res?.data?.totalElements ?? 0;
            this.appendAlarmTotal = res?.data?.appendAlarmTotal ?? 0;

            let locateNeName = null;
            if (type !== "filterQuery") {
              this.alarmStaIdArr = this.tableData.map(item => {
                return item.alarmStaId;
              });
              if (this.form.total == 0) {
                this.isBackSingle = true;
              } else {
                for (var i = 0; i < this.tableData.length; i++) {
                  if (
                    null != this.tableData[i].isMajorAlarm &&
                    this.tableData[i].isMajorAlarm == "是"
                  ) {
                    locateNeName = this.tableData[i].locateNeName;
                    this.alarmCreateTime = this.tableData[i].alarmCreateTime;
                  }
                }
              }
            }
            if (type == "firstQuery" && this.tableData.length > 0) {
              this.syncClearAlarm("init");
            }
            this.$emit("alarmClearCallBack", locateNeName);
          }
          this.tableLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.tableLoading = false;
        });
    },
    //打开详情框
    opentableList(row) {
      this.tableListVisible = true;
      this.tableListData = row;
    },
    selectDetailDialogClose() {
      this.selectDetailDialogVisible = false;
      this.tableListVisible = false;
      this.confirmDialogVisible = false;
    },
    selectDetailSubmit() {
      this.selectDetailDialogVisible = false;
      this.getTableData("filterQuery");
    },
    onResetTurnSingle() {
      this.selectDetailForm = {
        ...this.$options.data,
      };
    },
    //确认清除
    syncClearAlarmConfirm() {
      this.ApplyforLoading = true;
      let jsonParam = {
        woId: this.common.woId,
        processInstId: this.common.processInstId,
        processDefId: this.common.processDefId,
        workItemId: this.common.workItemId,
        processNode: this.common.processNode,
      };
      let formData = new FormData();
      formData.append(
        "manualAckClearTime",
        this.confirmForm.manualAckClearTime
      );
      formData.append("jsonParam", JSON.stringify(jsonParam));
      apiCheckClearAlarm(formData)
        .then(res => {
          if (res.status == "0") {
            this.$message({
              type: "success",
              message: "确认清除成功",
            });
            this.ApplyforLoading = false;
            this.confirmDialogVisible = false;
            this.getTableData("refresh");
            this.isShowManualConfirm = false;
          } else {
            this.ApplyforLoading = false;
            this.$message({
              type: "error",
              message: "确认清除失败",
            });
          }
        })
        .catch(err => {
          console.log(err);
          this.ApplyforLoading = false;
          this.$message({
            type: "error",
            message: "确认清除失败",
          });
        });
    },
    //清除申请
    syncClearAlarmApplyfor() {
      this.ApplyforLoading = true;
      let formData = new FormData();
      let jsonParam = {
        woId: this.common.woId,
        processInstId: this.common.processInstId,
        processDefId: this.common.processDefId,
        workItemId: this.common.workItemId,
        processNode: this.common.processNode,
      };
      let opType = 5;
      formData.append("opType", opType);
      formData.append("jsonParam", JSON.stringify(jsonParam));
      apiApplyCleanAlarm(formData)
        .then(res => {
          if (res.status == "0") {
            this.ApplyforLoading = false;
            this.$message.success(res.msg);

            this.oneApplyFor = true;
          } else {
            this.$message.error(res.msg);
          }
          this.ApplyforLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error(error.msg);
          this.ApplyforLoading = false;
        });
    },
    //重置
    resetconfirmForm() {
      this.confirmForm = {
        manualAckClearTime: moment(Date.now()).format("YYYY-MM-DD HH:mm:ss"),
      };
    },
    syncClearAlarm(type) {
      this.syncClearAlarmFullscreenLoading = true;
      let param = {
        uniqueId: this.alarmStaIdArr,
        alarmCreateTime: this.alarmCreateTime,
      };
      apiSyncClear(param)
        .then(res => {
          if (res.status == "0") {
            if (type != "init") {
              this.$message.success("同步清除成功");

              this.getTableData("refresh");
            }
          } else {
            if (type != "init") {
              this.$message.error("同步清除失败");
            }
          }
          this.syncClearAlarmFullscreenLoading = false;
        })
        .catch(error => {
          console.log(error);
          if (type != "init") {
            this.$message.error("同步清除失败");
          }

          this.syncClearAlarmFullscreenLoading = false;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../../../workOrder/workOrderWaitDetail/assets/common.scss";

._el-table {
  width: 100%;
}

::v-deep .el-descriptions-row {
  .is-bordered-label {
    width: 140px;
  }

  .tdOverflow {
    width: 260px;
    height: 50px;
    overflow-x: auto;
    white-space: nowrap;
    line-height: 50px;
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .tdOverflow::-webkit-scrollbar {
    display: none;
  }
}

::v-deep .el-dialog {
  .foolter_button {
    .el-form-item__content {
      float: right;
    }
  }
}
</style>
