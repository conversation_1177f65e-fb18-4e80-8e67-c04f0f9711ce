<template>
  <div class="back-single">
    <el-form
      ref="backSingleForm"
      :inline="false"
      class="demo-form-inline"
      :model="backSingleForm"
      label-width="130px"
      :rules="backSingleFormRule"
    >
      <el-card
        shadow="never"
        header="故障定性信息"
        :body-style="{ padding: '20px 8px' }"
        class="cus-card"
      >
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item
              label="故障所属专业:"
              prop="professionalType"
              :rules="{
                required: true,
                message: '请选择故障所属专业',
              }"
            >
              <dict-select
                :value.sync="backSingleForm.professionalType"
                :dictId="811039"
                style="width: 100%"
                @change="changeProfessionalType"
                :woId="common.woId"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障发生时间:" prop="alarmCreateTime" required>
              {{ backSingleForm.alarmCreateTime }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障通知时间:" prop="sheetCreateTime" required>
              {{ backSingleForm.sheetCreateTime }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="业务恢复时间:" prop="busRecoverTime">
              <el-date-picker
                v-model="backSingleForm.busRecoverTime"
                type="datetime"
                placeholder="请选择时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
                style="width: 100%"
                @change="computerBusRecoverDuration"
                :picker-options="pickerOptions"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="业务恢复历时:" required>
              {{ second2Time(backSingleForm.busRecoverDuration) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障结束时间:" prop="faultEndTime">
              <el-date-picker
                v-model="backSingleForm.faultEndTime"
                type="datetime"
                placeholder="请选择时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
                style="width: 100%"
                @change="computerFaultTreatmentTime"
                :picker-options="pickerOptions"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障处理历时:" required>
              {{ second2Time(this.backSingleForm.faultDuration) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="故障发生地区:"
              prop="faultRegion"
              :rules="{
                required: true,
                message: '请输入或者选择内容',
              }"
            >
              <el-input
                v-model="backSingleForm.faultRegion"
                placeholder="请输入或者选择内容"
                clearable
              >
                <template #append>
                  <el-button
                    type="primary"
                    icon="el-icon-check"
                    @click="faultRegionVisible = true"
                  ></el-button>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障处理部门:" prop="dept" required>
              {{ backSingleForm.dept }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="处理人:" prop="person" required>
              {{ backSingleForm.person }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否影响业务:" prop="isEffectBusiness">
              <el-radio-group
                v-model="backSingleForm.isEffectBusiness"
                style="width: 100%"
              >
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex">
          <el-col :span="16">
            <el-form-item
              label="影响范围:"
              v-if="backSingleForm.isEffectBusiness == '1'"
              prop="effectRange"
            >
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="backSingleForm.effectRange"
                style="width: 100%"
                show-word-limit
                maxlength="255"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card
        shadow="never"
        header="故障专业信息"
        :body-style="{ padding: '20px' }"
        style="margin-top: 20px"
        class="cus-card"
      >
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <template
            v-if="
              backSingleForm.professionalType != '12' &&
              backSingleForm.professionalType != '16'
            "
          >
            <el-col :span="8">
              <el-form-item
                label="故障分类:"
                prop="faultCate"
                :rules="{
                  required: true,
                  message: '请选择故障分类',
                }"
              >
                <el-select
                  v-model="backSingleForm.faultCate"
                  placeholder="请选择故障分类"
                  style="width: 100%"
                  @change="changeFaultCate"
                >
                  <el-option
                    v-for="item in faultCateOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="故障原因:"
                prop="faultReason"
                :rules="{
                  required: true,
                  message: '请选择故障原因',
                }"
              >
                <el-select
                  v-model="backSingleForm.faultReason"
                  placeholder="请选择故障原因"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in faultReasonOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </template>
          <el-col :span="8">
            <el-form-item label="故障排查对象:" prop="troubleshootingObject">
              <el-input
                v-model="backSingleForm.troubleshootingObject"
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="backSingleForm.professionalType != '0'">
            <el-form-item
              label="排查结果:"
              prop="troubleshootingResult"
              :rules="{
                required: true,
                message: '请选择或输入排查结果',
              }"
            >
              <el-select
                v-model="backSingleForm.troubleshootingResult"
                clearable
                filterable
                allow-create
                default-first-option
                @blur="selectBlur($event, 'troubleshootingResult')"
                style="width: 100%"
              >
                <el-option
                  v-for="item in troubleshootingResultOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-else>
            <el-form-item
              label="排查结果:"
              prop="troubleshootingResult"
              :rules="{
                required: true,
                message: '请输入排查结果',
              }"
            >
              <el-input
                v-model="backSingleForm.troubleshootingResult"
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="故障影响范围:"
              prop="faultImpactRange"
              :rules="{
                required: true,
                message: '请输入故障影响范围',
              }"
            >
              <el-input
                v-model="backSingleForm.faultImpactRange"
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="backSingleForm.professionalType != '0'">
            <el-form-item
              label="故障处理情况:"
              prop="faultHandingSituation"
              :rules="{
                required: true,
                message: '请选择或输入故障处理情况',
              }"
            >
              <el-select
                v-model="backSingleForm.faultHandingSituation"
                clearable
                filterable
                allow-create
                default-first-option
                @blur="selectBlur($event, 'faultHandingSituation')"
                style="width: 100%"
              >
                <el-option
                  v-for="item in faultHandingSituationOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-else>
            <el-form-item
              label="故障处理情况:"
              prop="faultHandingSituation"
              :rules="{
                required: true,
                message: '请输入故障处理情况',
              }"
            >
              <el-input
                v-model="backSingleForm.faultHandingSituation"
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              label="故障原因分析:"
              prop="faultCauseAnalysis"
              :rules="{
                required: true,
                message: '请输入故障原因分析',
              }"
            >
              <el-input
                maxlength="500"
                show-word-limit
                type="textarea"
                :rows="2"
                v-model="backSingleForm.faultCauseAnalysis"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-col :span="8">
          <el-form-item
            label="反馈人信息:"
            prop="feedbackPersonInfo"
            :rules="{
              required: true,
              message: '请输入反馈人信息',
            }"
          >
            <el-input
              v-model="backSingleForm.feedbackPersonInfo"
              style="width: 100%"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item label="相关附件:" prop="attachmentName">
            <el-tag
              class="fileName_style_download"
              closable
              v-for="(item, index) in fddxFileArr"
              :key="index"
              @close="closeAndDeleteFile(item)"
              @click="downloadAppendixFile(item)"
              :title="item.name"
            >
              <div class="text-truncate">{{ item.name }}</div>
            </el-tag>
            <el-tag
              class="fileName_style"
              closable
              v-for="(item, index) in importForm.relatedFilesFileList"
              :key="index"
              @close="close(item)"
              :title="item.name"
            >
              <div class="text-truncate">{{ item.name }}</div>
            </el-tag>
            <el-button size="mini" type="primary" @click="relatedFilesBrowse"
              >+上传附件</el-button
            >
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="备注:" prop="falutComment">
            <el-input
              type="textarea"
              :rows="2"
              placeholder="请输入内容"
              v-model="backSingleForm.falutComment"
              style="width: 100%"
              show-word-limit
              maxlength="255"
            >
            </el-input>
          </el-form-item>
        </el-col>
      </el-card>
    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: center">
      <el-button
        type="primary"
        @click="handleSubmit('backSingleForm')"
        v-loading.fullscreen.lock="backSingleFullscreenLoading"
        >提交</el-button
      >

      <el-button @click="onReset">重 置</el-button>
    </div>

    <el-dialog
      width="420px"
      title="附件选择"
      :visible.sync="relatedFilesDialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <file-upload
        @change="changeFileData"
        @cancel="closeAttachmentDialog"
      ></file-upload>
    </el-dialog>

    <!-- 行政区划 -->
    <area-tree
      :visible.sync="faultRegionVisible"
      v-model="backSingleForm.faultRegion"
    ></area-tree>
  </div>
</template>
<script>
import moment from "moment";
import { mapGetters } from "vuex";
import DictSelect from "../../../workOrder/components/DictSelect.vue";
import FileUpload from "../../../workOrder/components/FileUpload.vue";
import AreaTree from "@/plugin/backbone/components/AreaTree/AreaTree.vue";
import { apiQueryAlarmDetail } from "@plugin/backbone/modules/workOrder/workOrderWaitDetail/api/CommonApi";
import { apiBackSingle, apiEnumListNew } from "../api/BackSingle";
import { apiQualitativeDetail } from "../api/QualitativeReview";
import { apiGetOrgInfo, apiIsHaveAuth } from "../../../workOrder/api/CommonApi";
import { apiDownloadAppendixFile, apiDeleteFdFile } from "../api/CommonApi";
import { mixin } from "../../../../../../mixins";
import cloneDeep from "lodash/cloneDeep";

export default {
  name: "BackSingle",
  props: {
    basicWorkOrderData: Object,
    common: Object,
    timing: Object,
    randomNum: Number,
  },
  components: { DictSelect, FileUpload, AreaTree },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  mixins: [mixin],
  data() {
    var validBusRecoverTime = (rule, value, callback) => {
      if (this.backSingleForm.busRecoverTime) {
        // let seconds3 = moment(
        //   this.backSingleForm.busRecoverTime,
        //   "YYYY-MM-DD HH:mm:ss"
        // ).diff(moment(new Date(), "YYYY-MM-DD HH:mm:ss"), "seconds");
        // let seconds4 = moment(
        //   this.backSingleForm.busRecoverTime,
        //   "YYYY-MM-DD HH:mm:ss"
        // ).diff(
        //   moment(this.common.failureTime, "YYYY-MM-DD HH:mm:ss"),
        //   "seconds"
        // );
        // if (seconds3 > 0 || seconds4 <= 0) {
        //   callback(
        //     new Error(
        //       "当前时间>=业务恢复时间>=故障发生时间，请重新检查后选择正确时间"
        //     )
        //   );
        // } else {
        //   callback();
        // }
        let secondsBus = moment(
          this.backSingleForm.busRecoverTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.common.failureTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        if (secondsBus <= 0) {
          callback(new Error("业务恢复历时<=0，请重新检查后选择正确时间！"));
        } else {
          callback();
        }
      } else {
        callback(new Error("请选择业务恢复时间"));
      }
    };
    var validFaultEndTime = (rule, value, callback) => {
      if (value === "" || value === null) {
        callback(new Error("请选择故障结束时间"));
      } else {
        if (this.backSingleForm.faultEndTime) {
          // let seconds3 = moment(
          //   this.backSingleForm.faultEndTime,
          //   "YYYY-MM-DD HH:mm:ss"
          // ).diff(moment(new Date(), "YYYY-MM-DD HH:mm:ss"), "seconds");
          // let seconds4 = moment(
          //   this.backSingleForm.faultEndTime,
          //   "YYYY-MM-DD HH:mm:ss"
          // ).diff(
          //   moment(this.common.failureTime, "YYYY-MM-DD HH:mm:ss"),
          //   "seconds"
          // );
          // if (seconds3 > 0 || seconds4 <= 0) {
          //   callback(
          //     new Error(
          //       "当前时间>=故障结束时间>=故障发生时间，请重新检查后选择正确时间"
          //     )
          //   );
          // } else {
          //   callback();
          // }
          let secondsProcessingDuration = moment(
            this.backSingleForm.faultEndTime,
            "YYYY-MM-DD HH:mm:ss"
          ).diff(
            moment(this.common.failureTime, "YYYY-MM-DD HH:mm:ss"),
            "seconds"
          );
          if (secondsProcessingDuration <= 0) {
            callback(new Error("故障处理历时<=0，请重新检查后选择正确时间！"));
          } else {
            callback();
          }
        } else {
          callback(new Error("请选择故障结束时间"));
        }
      }
    };
    return {
      sumBtnShow: true,
      backSingleForm: {
        woId: null,
        workItemId: null,
        processInstId: null,
        processDefId: null,
        professionalType: null,
        alarmCreateTime: null,
        sheetCreateTime: null,
        busRecoverTime: null, //业务恢复时间
        busRecoverDuration: 0, //业务恢复历时
        faultEndTime: null,
        faultDuration: 0, //故障处理历时
        faultRegion: "",
        dept: null,
        person: null,

        isEffectBusiness: "0",
        effectRange: null,
        //故障专业信息
        faultCate: null,
        faultReason: null,
        relatedFiles: null,
        falutComment: null,
        actionName: "",
        attachmentName: null,
        linkId: null,
        appendix: null,
        troubleshootingObject: null, //故障排查对象
        troubleshootingResult: null, //排查结果
        faultImpactRange: null, //故障影响范围
        faultHandingSituation: null, //故障处理情况
        faultCauseAnalysis: null, //故障原因分析
        feedbackPersonInfo: null, //反馈人信息
      },
      areaCode: null, //区域编码
      category: null, //省份返单 OR 地市返单
      backSingleFullscreenLoading: false,
      //关联附件
      relatedFilesDialogVisible: false,
      importForm: {
        relatedFilesFileList: [],
      },

      colors: ["#99A9BF", "#F7BA2A", "#FF9900"], // 等同于 { 2: '#99A9BF', 4: { value: '#F7BA2A', excluded: true }, 5: '#FF9900' }
      //当前人的部门
      faultRegionOptions: [],
      userData: {},

      backSingleFormRule: {
        busRecoverTime: [{ validator: validBusRecoverTime, required: true }],
        faultEndTime: [{ validator: validFaultEndTime, required: true }],

        falutComment: [
          {
            validator: this.checkLength,
            max: 255,
            form: "backSingleForm",
            messsage: "已超过填写字数上限",
          },
        ],
        faultCauseAnalysis: [
          {
            validator: this.checkLength,
            max: 500,
            form: "backSingleForm",
            messsage: "已超过填写字数上限",
          },
        ],
        effectRange: [
          {
            required: true,
            message: "请填写影响范围",
            trigger: "blur",
          },
          {
            validator: this.checkLength,
            max: 255,
            form: "backSingleForm",
            messsage: "已超过填写字数上限",
          },
        ],
        isEffectBusiness: [
          {
            required: true,
            message: "请选择是否影响业务",
            trigger: "change",
          },
        ],
        troubleshootingObject: [
          {
            required: true,
            message: "请输入故障排查对象",
            trigger: "blur",
          },
        ],
      },
      viewsOnContentShow: false,
      fddxFileArr: [],
      pickerOptions: {
        disabledDate: this.disabledDate,
        selectableRange: this.timeRange,
      },
      timeRange: "00:00:00 - 23:59:59",
      createTimeDate: "2022-10-20",
      faultRegionVisible: false,
      faultReasonOptions: [],
      faultCateOptions: [],
      faultHandingSituationOptions: [],
      troubleshootingResultOptions: [],
    };
  },
  watch: {
    "backSingleForm.busRecoverTime": {
      handler(val) {
        if (val == null) return;
        let valDate = val.split(" ")[0];
        let originDate = this.common.failureTime.split(" ")[0];
        let now = new Date();
        now.setMinutes(now.getMinutes() + 1, now.getSeconds(), 0);
        valDate = valDate.replace(/\-/g, "/");
        originDate = originDate.replace(/\-/g, "/");
        let year = now.getFullYear();
        let month = now.getMonth() + 1;
        let date = now.getDate();
        let nowDate =
          year + "/" + this.addZero(month) + "/" + this.addZero(date);
        let valDateUnix = Date.parse(valDate);
        let originDateUnix = Date.parse(originDate);
        let nowDateUnix = Date.parse(nowDate);

        let array = this.backSingleForm.alarmCreateTime.split(" ");
        let createTime = array[1];
        let hour = now.getHours();
        let minute = now.getMinutes();
        let second = now.getSeconds();
        let nowTime =
          this.addZero(hour) +
          ":" +
          this.addZero(minute) +
          ":" +
          this.addZero(second);
        if (valDateUnix == originDateUnix && originDateUnix == nowDateUnix) {
          this.timeRange = createTime + " - " + nowTime;
          this.pickerOptions.selectableRange = this.timeRange;
        } else if (
          valDateUnix == originDateUnix &&
          originDateUnix < nowDateUnix
        ) {
          this.timeRange = createTime + " - 23:59:59";
          this.pickerOptions.selectableRange = this.timeRange;
        } else if (valDateUnix > originDateUnix && valDateUnix < nowDateUnix) {
          this.timeRange = "00:00:00 - 23:59:59";
          this.pickerOptions.selectableRange = this.timeRange;
        } else if (valDateUnix > originDateUnix && valDateUnix == nowDateUnix) {
          this.timeRange = "00:00:00 - " + nowTime;
          this.pickerOptions.selectableRange = this.timeRange;
        }
      },
      deep: true,
    },
    randomNum() {
      if (this.common.auditResult == "0") {
        this.qualitativeReviewDetail();
      }
    },
    "backSingleForm.faultEndTime": {
      handler(val) {
        if (val == null) return;
        let valDate = val.split(" ")[0];
        let originDate = this.common.failureTime.split(" ")[0];
        let now = new Date();
        now.setMinutes(now.getMinutes() + 1, now.getSeconds(), 0);
        valDate = valDate.replace(/\-/g, "/");
        originDate = originDate.replace(/\-/g, "/");
        let year = now.getFullYear();
        let month = now.getMonth() + 1;
        let date = now.getDate();
        let nowDate =
          year + "/" + this.addZero(month) + "/" + this.addZero(date);
        let valDateUnix = Date.parse(valDate);
        let originDateUnix = Date.parse(originDate);
        let nowDateUnix = Date.parse(nowDate);

        let array = this.backSingleForm.alarmCreateTime.split(" ");
        let createTime = array[1];
        let hour = now.getHours();
        let minute = now.getMinutes();
        let second = now.getSeconds();
        let nowTime =
          this.addZero(hour) +
          ":" +
          this.addZero(minute) +
          ":" +
          this.addZero(second);
        if (valDateUnix == originDateUnix && originDateUnix == nowDateUnix) {
          this.timeRange = createTime + " - " + nowTime;
          this.pickerOptions.selectableRange = this.timeRange;
        } else if (
          valDateUnix == originDateUnix &&
          originDateUnix < nowDateUnix
        ) {
          this.timeRange = createTime + " - 23:59:59";
          this.pickerOptions.selectableRange = this.timeRange;
        } else if (valDateUnix > originDateUnix && valDateUnix < nowDateUnix) {
          this.timeRange = "00:00:00 - 23:59:59";
          this.pickerOptions.selectableRange = this.timeRange;
        } else if (valDateUnix > originDateUnix && valDateUnix == nowDateUnix) {
          this.timeRange = "00:00:00 - " + nowTime;
          this.pickerOptions.selectableRange = this.timeRange;
        }
      },
      deep: true,
    },
  },
  created() {
    this.getFaultHandingSOptions();
    this.getTroubleshootingROptions();
  },
  mounted() {
    this.userData = JSON.parse(this.userInfo.attr2);
    // console.log("this.common ", this.common);
    // console.log("this.basicWorkOrderData", this.basicWorkOrderData);
    this.backSingleForm.alarmCreateTime = this.common.failureTime;
    this.backSingleForm.sheetCreateTime = this.common.failureInformTime;
    this.backSingleForm.person = this.userInfo.realName;
    this.backSingleForm.workItemId = this.common.workItemId;
    this.backSingleForm.woId = this.common.woId;
    this.backSingleForm.processInstId = this.common.processInstId;
    this.backSingleForm.processDefId = this.common.processDefId;
    this.backSingleForm.actionName = this.common.actionName;
    this.backSingleForm.professionalType = this.common.professionalType + "";
    if (this.backSingleForm.professionalType != "0") {
      //非传输网时做回填
      this.backSingleForm.troubleshootingObject = this.common.locateNeName;
      this.backSingleForm.faultImpactRange = "不影响业务";
      this.backSingleForm.feedbackPersonInfo =
        this.userData.trueName + "-" + this.userData.mobilePhone; //返单人员自动生成，姓名（手机号）
    }
    if (this.common.alarmClearTime) {
      this.backSingleForm.busRecoverTime = this.common.alarmClearTime;
      this.backSingleForm.faultEndTime = this.common.alarmClearTime;
      this.computerBusRecoverDuration();
      this.computerFaultTreatmentTime();
    }
    this.changeProfessionalType(this.backSingleForm.professionalType);
    this.getOrgInfo();

    if (this.common.auditResult == "0") {
      this.qualitativeReviewDetail();
    } else {
      this.getFaultRegionDef();
    }

    let array = this.backSingleForm.alarmCreateTime.split(" ");
    this.createTimeDate = array[0];
  },
  methods: {
    getFaultHandingSOptions() {
      let params = {
        dictTypeCode: "811047",
      };
      apiEnumListNew(params).then(res => {
        if (res.code == 200) {
          this.faultHandingSituationOptions = res?.data ?? [];
        }
      });
    },
    getTroubleshootingROptions() {
      let params = {
        dictTypeCode: "811046",
      };
      apiEnumListNew(params).then(res => {
        if (res.code == 200) {
          this.troubleshootingResultOptions = res?.data ?? [];
        }
      });
    },
    changeProfessionalType(type) {
      this.backSingleForm.faultCate = null;
      if (this.common.auditResult != "0") {
        //不是拒绝 返单的时候
        this.dealOtherAttr();
      }
      //获取故障分类数据
      let params = {
        dictTypeCode: "811042",
        parentCode: type,
      };
      return new Promise(resolve => {
        apiEnumListNew(params).then(res => {
          if (res.code == 200) {
            this.faultCateOptions = res?.data ?? [];
          }
          resolve("success");
        });
      });
    },
    dealOtherAttr() {
      if (this.backSingleForm.professionalType != "0") {
        //非传输网时做回填
        this.backSingleForm.troubleshootingObject =
          this.common?.locateNeName ?? null;
        this.backSingleForm.faultImpactRange = "不影响业务";
        this.backSingleForm.feedbackPersonInfo =
          this.userData.trueName + "-" + this.userData.mobilePhone; //返单人员自动生成，姓名（手机号）
        this.$refs.backSingleForm.clearValidate(["troubleshootingObject"]);
      } else {
        this.backSingleForm.troubleshootingObject = "";
        this.backSingleForm.faultImpactRange = "";
        this.backSingleForm.feedbackPersonInfo = "";
      }
    },
    //时间对比
    disabledDate(time) {
      let now = moment(this.createTimeDate);
      let a = moment(now).subtract(1, "days");
      let today = moment(a).valueOf();
      return time.getTime() <= today || time.getTime() - 8.64e6 >= Date.now();
    },
    addZero(s) {
      return s < 10 ? "0" + s : s;
    },

    getOrgInfo() {
      apiGetOrgInfo()
        .then(res => {
          if (res.status == "0") {
            this.backSingleForm.dept = res?.data?.orgInfo?.fullOrgName ?? "";
            this.areaCode = res?.data?.orgInfo?.areaCode ?? "";
            this.category = res?.data?.category ?? "";
            // this.getFaultAreaOptions();
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    // getFaultAreaOptions() {
    //   let param = {
    //     areaCode: this.areaCode,
    //     category: this.category,
    //   };
    //   apiGetFaultArea(param)
    //     .then(res => {
    //       if (res.status == "0") {
    //         this.faultRegionOptions = res?.data ?? [];
    //         this.backSingleForm.faultRegion = this.faultRegionOptions[0].name;
    //       }
    //     })
    //     .catch(error => {
    //       console.log(error);
    //     });
    // },
    qualitativeReviewDetail() {
      let param = {
        opType: 2,
        workItemId: this.backSingleForm.workItemId,
        woId: this.backSingleForm.woId,
      };
      apiQualitativeDetail(param)
        .then(res => {
          if (res.status == "0") {
            let self = this;
            self.backSingleForm = cloneDeep(res?.data ?? {});
            this.changeProfessionalType(
              this.backSingleForm.professionalType
            ).then(() => {
              this.$set(
                this.backSingleForm,
                "faultCate",
                res?.data?.faultCate ?? ""
              );
            });
            this.changeFaultCate(res.data.faultCate).then(() => {
              this.$set(
                this.backSingleForm,
                "faultReason",
                res?.data?.faultReason ?? ""
              );
            });
            if (res.data.appendix) {
              self.fddxFileArr = JSON.parse(res.data.appendix);
            }
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    relatedFilesBrowse() {
      this.relatedFilesDialogVisible = true;
    },
    changeFileData(data) {
      this.backSingleForm.relatedFiles = data.fileName;
      this.backSingleForm.attachmentName = data.fileName;
      this.importForm.relatedFilesFileList = data.attachmentFileList;
      this.relatedFilesDialogVisible = false;
    },
    closeAttachmentDialog() {
      this.relatedFilesDialogVisible = false;
    },
    computerBusRecoverDuration() {
      if (this.backSingleForm.busRecoverTime) {
        let days = moment(
          this.backSingleForm.busRecoverTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.backSingleForm.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        this.backSingleForm.busRecoverDuration = days;
      } else {
        this.backSingleForm.busRecoverDuration = 0;
      }
    },
    second2Time(days) {
      //return this.showTime(Math.abs(days));
      return this.showTime(days);
    },
    computerFaultTreatmentTime() {
      let days = moment(
        this.backSingleForm.faultEndTime,
        "YYYY-MM-DD HH:mm:ss"
      ).diff(
        moment(this.backSingleForm.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
        "seconds"
      );
      this.backSingleForm.faultDuration = days;
    },

    changeFaultCate(type) {
      this.backSingleForm.faultReason = "";
      let params = {
        dictTypeCode: "811043",
        parentCode: type,
      };
      return new Promise(resolve => {
        apiEnumListNew(params).then(res => {
          if (res.code == 200) {
            this.faultReasonOptions = res?.data ?? [];
          }
          resolve("success");
        });
      });
    },

    handleSubmit(formName) {
      this.$refs[formName].validate((valid, a) => {
        if (this.uncheck(a)) {
          let self = this;
          this.backSingleFullscreenLoading = true;
          let formData = new FormData();
          if (self.importForm.relatedFilesFileList.length > 0) {
            for (let item of self.importForm.relatedFilesFileList) {
              formData.append("files", item.raw);
            }
          } else {
            formData.append("files", "");
          }
          // 当前剩余受理时限
          let remainAcceptTime = this.common.acceptTimeLimit - Math.floor((new Date()-new Date(this.backSingleForm.sheetCreateTime))/60000);
          if(remainAcceptTime <= 0){
            remainAcceptTime = -1;
          }
          this.backSingleForm.remainAcceptTime = remainAcceptTime;
          formData.append("jsonParam", JSON.stringify(this.backSingleForm));
          apiBackSingle(formData)
            .then(res => {
              if (res.status == "0") {
                this.$message.success("提交返单成功");
                this.getIsHaveAuth(res.data);
              } else {
                this.$message.error("提交返单失败");
                this.backSingleFullscreenLoading = false;
              }
            })
            .catch(error => {
              console.log(error);
              this.$message.error("提交返单失败");
              this.backSingleFullscreenLoading = false;
            });
        } else {
          return false;
        }
      });
    },

    getIsHaveAuth(data) {
      //是否刷新当前页面
      let params = {
        userName: this.userInfo.userName,
        processInstId: data.processInstId,
      };
      apiIsHaveAuth(params)
        .then(res => {
          if (res.status == "0") {
            data.currentPage = res.data;
            this.$emit("closeBackSingleDialog", data);
            this.backSingleFullscreenLoading = false;
          } else {
            this.$message.error("获取是否刷新当前页面数据失败");
          }
        })
        .catch(error => {
          console.log(error);
          this.$message.error("获取是否刷新当前页面数据失败");
        });
    },
    close(tag) {
      this.importForm.relatedFilesFileList.splice(
        this.importForm.relatedFilesFileList.indexOf(tag),
        1
      );
      if (this.importForm.relatedFilesFileList.length == 0) {
        this.backSingleForm.attachmentName = null;
      }
    },
    onReset() {
      this.backSingleForm = {
        ...this.$options.data,
        alarmCreateTime: this.common.failureTime,
        sheetCreateTime: this.common.failureInformTime,
        woId: this.common.woId,
        workItemId: this.common.workItemId,
        processInstId: this.common.processInstId,
        processDefId: this.common.processDefId,
        person: this.userInfo.realName,
        dept: this.userData.orgInfo.fullOrgName,
        actionName: this.common.actionName,
        professionalType: this.common.professionalType + "",
        faultDuration: 0, //故障处理历时
        faultReason: null,
        falutComment: null,
        faultEndTime: this.common.alarmClearTime,
        faultCate: null,
        busRecoverTime: this.common.alarmClearTime, //业务恢复时间
        busRecoverDuration: 0,
        faultRegion: null,
        troubleshootingObject: null, //故障排查对象
        troubleshootingResult: null, //排查结果
        faultImpactRange: null, //故障影响范围
        faultHandingSituation: null, //故障处理情况
        faultCauseAnalysis: null, //故障原因分析
        feedbackPersonInfo: null, //反馈人信息
      };
      this.importForm.relatedFilesFileList = [];
      this.changeProfessionalType(this.backSingleForm.professionalType);
      this.computerBusRecoverDuration();
      this.computerFaultTreatmentTime();
    },
    showTime(val) {
      let isPositiveNumber = true;
      let valStr = val + "";
      if (valStr.indexOf("-") != -1) {
        //负数
        val = Math.abs(val);
        isPositiveNumber = false;
      }
      if (val) {
        if (val == 0) return "0秒";
        var time = "";
        var second = val % 60;
        var minute = parseInt(parseInt(val) / 60) % 60;
        time = second + "秒";
        if (minute >= 1) {
          time = minute + "分" + second + "秒";
        }
        var hour = parseInt(parseInt(val / 60) / 60) % 24;
        if (hour >= 1) {
          time = hour + "小时" + minute + "分" + second + "秒";
        }
        var day = parseInt(parseInt(parseInt(val / 60) / 60) / 24);
        if (day >= 1) {
          time = day + "天" + hour + "小时" + minute + "分" + second + "秒";
        }
        if (!isPositiveNumber) {
          time = "-" + time;
        }
        return time;
      } else {
        return "0秒";
      }
    },

    closeAndDeleteFile(tag) {
      this.deleteFile(tag);
    },
    deleteFile(tag) {
      let param = {
        attId: tag.id,
        linkId: this.backSingleForm.linkId,
      };
      apiDeleteFdFile(param)
        .then(res => {
          if (res.status == "0") {
            this.fddxFileArr.splice(this.fdFileArr.indexOf(tag), 1);
            this.backSingleForm.appendix = JSON.stringify(this.fddxFileArr);
            if (this.fddxFileArr.length == 0) {
              this.backSingleForm.attachmentName = null;
            }
            this.$message.success("附件删除成功");
          } else {
            this.$$message.error("附件删除失败");
          }
        })
        .catch(error => {
          this.$message.error("附件删除失败");
          console.log(error);
        });
    },
    downloadAppendixFile(data) {
      let param = {
        attId: data.id,
      };
      apiDownloadAppendixFile(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("文件下载成功");
          } else {
            this.$message.error("文件下载失败");
          }
        })
        .catch(error => {
          console.log(error);
          this.$message.error("文件下载失败");
        });
    },
    // 获取告警发生区域默认值
    getFaultRegionDef() {
      apiQueryAlarmDetail({
        pageIndex: 1,
        pageSize: 10,
        param1: JSON.stringify({ woId: this.common.woId }),
      }).then(res => {
        const { alarmProvince = "", alarmRegion = "" } =
          res?.data?.rows?.[0] || {};
        this.backSingleForm.faultRegion = alarmProvince + alarmRegion;
      });
    },
    selectBlur(e, prop) {
      if (e.target.value) {
        this.$set(this.backSingleForm, prop, e.target.value);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.back-single {
  max-height: calc(90vh - 150px);
  min-height: 400px;
  margin: 0 4px;
  padding-left: 4px;
  padding-right: 8px;
  overflow-y: auto;

  .cus-card ::v-deep .el-card__header {
    padding: 11px 24px 10px;
    font-weight: 400;

    @include themify {
      background-color: themed("$--background-color-base");
    }
  }

  .fileName_style {
    margin-right: 3px;
    vertical-align: middle;

    div {
      display: inline-block;
      max-width: 120px;
      vertical-align: top;
    }
  }

  .fileName_style_download {
    margin-right: 3px;
    cursor: pointer;
    vertical-align: middle;

    div {
      display: inline-block;
      max-width: 120px;
      vertical-align: top;
    }
  }
}
</style>
