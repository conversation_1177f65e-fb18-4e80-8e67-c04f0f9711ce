<template>
  <div class="qualitative-review">
    <el-form
      ref="qualitativeReviewForm"
      :inline="false"
      class="demo-form-inline"
      :model="qualitativeReviewForm"
      label-width="140px"
      :rules="qualitativeReviewFormRules"
    >
      <el-card
        shadow="never"
        header="故障定性信息"
        :body-style="{ padding: '20px 8px' }"
        class="cus-card"
      >
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item
              label="故障所属专业:"
              prop="professionalType"
              :rules="{
                required: true,
                message: '请选择故障所属专业',
              }"
            >
              <dict-select
                :value.sync="qualitativeReviewForm.professionalType"
                :dictId="811039"
                style="width: 100%"
                :notSelect="true"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障发生时间:" prop="alarmCreateTime" required>
              {{ qualitativeReviewForm.alarmCreateTime }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障通知时间:" prop="sheetCreateTime" required>
              {{ qualitativeReviewForm.sheetCreateTime }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="业务恢复时间:"
              prop="busRecoverTime"
              :rules="{
                required: true,
                message: '请选择内容',
              }"
            >
              <el-date-picker
                v-model="qualitativeReviewForm.busRecoverTime"
                type="datetime"
                placeholder="请选择时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
                disabled
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="业务恢复历时:" required>
              {{ second2Time(qualitativeReviewForm.busRecoverDuration) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="故障结束时间:"
              prop="faultEndTime"
              :rules="{
                required: true,
                message: '请选择内容',
              }"
            >
              <el-date-picker
                v-model="qualitativeReviewForm.faultEndTime"
                type="datetime"
                placeholder="请选择时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
                disabled
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障处理历时:" required>
              {{ second2Time(this.qualitativeReviewForm.faultDuration) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障发生地区:" prop="faultRegion" required>
              {{ qualitativeReviewForm.faultRegion }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障处理部门:" prop="dept" required>
              {{ qualitativeReviewForm.dept }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="处理人:" prop="person" required>
              {{ qualitativeReviewForm.person }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item label="是否影响业务:" prop="isEffectBusiness">
              <el-radio-group
                v-model="qualitativeReviewForm.isEffectBusiness"
                style="width: 100%"
                disabled
              >
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item
              label="影响范围:"
              v-if="qualitativeReviewForm.isEffectBusiness == '1'"
              prop="effectRange"
              :rules="[
                {
                  required:
                    qualitativeReviewForm.isEffectBusiness == '1'
                      ? true
                      : false,
                  message: '请填写影响范围',
                  trigger: 'blur',
                },
              ]"
            >
              <el-input
                type="textarea"
                :rows="2"
                disabled
                placeholder="请输入内容"
                v-model="qualitativeReviewForm.effectRange"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card
        shadow="never"
        header="故障专业信息"
        :body-style="{ padding: '20px 8px' }"
        class="cus-card"
      >
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <template
            v-if="
              qualitativeReviewForm.professionalType != '12' &&
              qualitativeReviewForm.professionalType != '16'
            "
          >
            <el-col :span="8">
              <el-form-item
                label="故障分类:"
                prop="faultCate"
                :rules="{
                  required: true,
                  message: '请选择故障分类',
                }"
              >
                <el-select
                  v-model="qualitativeReviewForm.faultCate"
                  placeholder="请选择故障分类"
                  style="width: 100%"
                  @change="changeFaultCate"
                >
                  <el-option
                    v-for="item in faultCateOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
                <!-- <dict-select
                :value.sync="qualitativeReviewForm.faultCate"
                :dictId="faultCateDict"
                style="width: 100%"
                placeholder="请选择内容"
                @change="changeFaultCate"
              /> -->
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="故障原因:"
                prop="faultReason"
                :rules="{
                  required: true,
                  message: '请选择故障原因',
                }"
              >
                <el-select
                  v-model="qualitativeReviewForm.faultReason"
                  placeholder="请选择故障原因"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in faultReasonOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
                <!-- <dict-select
                :value.sync="qualitativeReviewForm.faultReason"
                :dictId="faultReasonDict"
                style="width: 100%"
                placeholder="请选择内容"
              /> -->
              </el-form-item>
            </el-col>
          </template>
          <el-col :span="8">
            <el-form-item
              label="故障排查对象:"
              prop="troubleshootingObject"
              :rules="{
                required: true,
                message: '请输入故障排查对象',
              }"
            >
              <el-input
                v-model="qualitativeReviewForm.troubleshootingObject"
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col
            :span="8"
            v-if="qualitativeReviewForm.professionalType != '0'"
          >
            <el-form-item
              label="排查结果:"
              prop="troubleshootingResult"
              :rules="{
                required: true,
                message: '请选择或输入排查结果',
              }"
            >
              <el-select
                v-model="qualitativeReviewForm.troubleshootingResult"
                clearable
                filterable
                allow-create
                default-first-option
                @blur="selectBlur($event, 'troubleshootingResult')"
                style="width: 100%"
              >
                <el-option
                  v-for="item in troubleshootingResultOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
              <!-- <dict-select
                :value.sync="qualitativeReviewForm.troubleshootingResult"
                :dictId="811046"
                style="width: 100%"
                placeholder="请选择内容"
              /> -->
            </el-form-item>
          </el-col>
          <el-col :span="8" v-else>
            <el-form-item
              label="排查结果:"
              prop="troubleshootingResult"
              :rules="{
                required: true,
                message: '请输入排查结果',
              }"
            >
              <el-input
                v-model="qualitativeReviewForm.troubleshootingResult"
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="故障影响范围:"
              prop="faultImpactRange"
              :rules="{
                required: true,
                message: '请输入故障影响范围',
              }"
            >
              <el-input
                v-model="qualitativeReviewForm.faultImpactRange"
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col
            :span="8"
            v-if="qualitativeReviewForm.professionalType != '0'"
          >
            <el-form-item
              label="故障处理情况:"
              prop="faultHandingSituation"
              :rules="{
                required: true,
                message: '请选择或输入故障处理情况',
              }"
            >
              <el-select
                v-model="qualitativeReviewForm.faultHandingSituation"
                clearable
                filterable
                allow-create
                default-first-option
                @blur="selectBlur($event, 'faultHandingSituation')"
                style="width: 100%"
              >
                <el-option
                  v-for="item in faultHandingSituationOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
              <!-- <dict-select
                :value.sync="qualitativeReviewForm.faultHandingSituation"
                :dictId="811047"
                style="width: 100%"
                placeholder="请选择内容"
              /> -->
            </el-form-item>
          </el-col>
          <el-col :span="8" v-else>
            <el-form-item
              label="故障处理情况:"
              prop="faultHandingSituation"
              :rules="{
                required: true,
                message: '请输入故障处理情况',
              }"
            >
              <el-input
                v-model="qualitativeReviewForm.faultHandingSituation"
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              label="故障原因分析:"
              prop="faultCauseAnalysis"
              :rules="{
                required: true,
                message: '请输入故障原因分析',
              }"
            >
              <el-input
                maxlength="500"
                show-word-limit
                type="textarea"
                :rows="2"
                v-model="qualitativeReviewForm.faultCauseAnalysis"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="反馈人信息:"
              prop="feedbackPersonInfo"
              :rules="{
                required: true,
                message: '请输入反馈人信息',
              }"
            >
              <el-input
                v-model="qualitativeReviewForm.feedbackPersonInfo"
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="相关附件:" prop="attachmentName">
              <el-tag
                class="fileName_style_download"
                closable
                v-for="(item, index) in fddxFileArr"
                :key="index"
                @close="closeAndDeleteFile(item)"
                @click="downloadAppendixFile(item)"
                :title="item.name"
              >
                <div class="text-truncate">{{ item.name }}</div>
              </el-tag>
              <el-tag
                class="fileName_style"
                closable
                v-for="(item, index) in importForm.relatedFilesFileList"
                :key="index"
                @close="close(item)"
                :title="item.name"
              >
                <div class="text-truncate">{{ item.name }}</div>
              </el-tag>
              <el-button size="mini" type="primary" @click="relatedFilesBrowse"
                >+上传附件</el-button
              >
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注:" prop="falutComment">
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="qualitativeReviewForm.falutComment"
                style="width: 100%"
                show-word-limit
                maxlength="255"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card
        shadow="never"
        header="定性审核信息"
        :body-style="{ padding: '20px 8px' }"
        style="margin-top: 20px"
        class="cus-card"
      >
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item
              label="审批结果:"
              prop="auditResult"
              :rules="{
                required: true,
                message: '请选择审批结果',
              }"
            >
              <el-radio-group
                v-model="qualitativeReviewForm.auditResult"
                @change="setIsExeOverDisabled"
              >
                <el-radio label="1">同意</el-radio>
                <el-radio label="0">拒绝</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="是否故障处理结束:"
              :rules="{
                required: true,
                message: '请选择是否故障处理结束',
              }"
              prop="isExeOver"
            >
              <el-radio-group
                v-model="qualitativeReviewForm.isExeOver"
                :disabled="isExeOverDisabled"
              >
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="审批意见:" prop="auditContent">
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model.trim="qualitativeReviewForm.auditContent"
                style="width: 100%"
                show-word-limit
                maxlength="255"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: center">
      <el-button
        type="primary"
        @click="handleSubmit('qualitativeReviewForm')"
        v-loading.fullscreen.lock="qualitativeReviewFullscreenLoading"
        >提交</el-button
      >
      <el-button @click="onReset">重 置</el-button>
    </div>

    <el-dialog
      width="420px"
      title="附件选择"
      :visible.sync="relatedFilesDialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <file-upload
        @change="changeFileData"
        @cancel="closeAttachmentDialog"
      ></file-upload>
    </el-dialog>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import DictSelect from "../../../workOrder/components/DictSelect.vue";

import {
  apiQualitativeDetail,
  apiQualitativeReview,
} from "../api/QualitativeReview";
import { apiEnumListNew } from "../api/BackSingle";
import { apiDownloadAppendixFile, apiDeleteFdFile } from "../api/CommonApi";
import FileUpload from "../../../workOrder/components/FileUpload.vue";
import { mixin } from "../../../../../../mixins";
import cloneDeep from "lodash/cloneDeep";

export default {
  name: "QualitativeReview",
  props: {
    common: Object,
    workItemId: [String, Number],
  },
  components: { DictSelect, FileUpload },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  watch: {
    workItemId(val) {
      this.qualitativeReviewForm.workItemId = val;
    },
  },
  mixins: [mixin],
  data() {
    return {
      sumBtnShow: true,
      qualitativeReviewForm: {
        woId: null,
        workItemId: null,
        processInstId: null,
        processDefId: null,
        professionalType: null,
        alarmCreateTime: null,
        sheetCreateTime: null,
        busRecoverTime: null, //业务恢复时间
        busRecoverDuration: 0, //业务恢复历时
        faultEndTime: null,
        faultDuration: 0, //故障处理历时
        faultRegion: null,
        dept: null,
        person: null,
        isEffectBusiness: null,
        effectRange: null,
        //故障专业信息
        faultCate: null,
        faultReason: null,
        relatedFiles: null,
        falutComment: null,
        eqpType: null,
        eqpName: "",
        actionName: "",
        linkId: null,
        appendix: null,
        troubleshootingObject: null, //故障排查对象
        troubleshootingResult: null, //排查结果
        faultImpactRange: null, //故障影响范围
        faultHandingSituation: null, //故障处理情况
        faultCauseAnalysis: null, //故障原因分析
        feedbackPersonInfo: null, //反馈人信息
        //审核信息
        auditResult: null,
        isExeOver: null,
        auditContent: null,
        attachmentName: null,
        // emergencyLevel: null,
      },
      qualitativeReviewFormRules: {
        auditContent: [
          {
            required: true,
            message: "请填写审批意见",
          },
          {
            validator: this.checkLength,
            max: 255,
            form: "qualitativeReviewForm",
            messsage: "已超过填写字数上限",
          },
        ],
        isEffectBusiness: [
          {
            required: true,
            message: "请选择是否影响业务",
          },
        ],
      },
      //关联附件
      relatedFilesDialogVisible: false,
      importForm: {
        relatedFilesFileList: [],
      },
      qualitativeReviewFullscreenLoading: false,
      fddxFileArr: [],
      faultReasonOption: [],
      // tabMenu: [],
      isExeOverDisabled: false,
      faultCateOptions: [],
      faultReasonOptions: [],
      troubleshootingResultOptions: [],
      faultHandingSituationOptions: [],
    };
  },
  created() {
    this.getTroubleshootingROptions();
    this.getFaultHandingSOptions();
  },
  mounted() {
    this.qualitativeReviewForm.workItemId = this.workItemId;
    this.qualitativeReviewForm.woId = this.common.woId;
    this.qualitativeReviewForm.processInstId = this.common.processInstId;
    this.qualitativeReviewForm.processDefId = this.common.processDefId;
    this.qualitativeReviewDetail();
  },
  methods: {
    getFaultHandingSOptions() {
      let params = {
        dictTypeCode: "811047",
      };
      apiEnumListNew(params).then(res => {
        if (res.code == 200) {
          this.faultHandingSituationOptions = res?.data ?? [];
        }
      });
    },
    getTroubleshootingROptions() {
      let params = {
        dictTypeCode: "811046",
      };
      apiEnumListNew(params).then(res => {
        if (res.code == 200) {
          this.troubleshootingResultOptions = res?.data ?? [];
        }
      });
    },
    setIsExeOverDisabled() {
      if (this.qualitativeReviewForm.auditResult == "1") {
        this.$set(this.qualitativeReviewForm, "isExeOver", "1");
        this.$set(
          this,
          "isExeOverDisabled",
          this.qualitativeReviewForm.seizeOrders == "1"
        );
      } else {
        this.$set(this.qualitativeReviewForm, "isExeOver", "0");
        this.$set(this, "isExeOverDisabled", false);
      }
    },
    changeProfessionalType(type) {
      this.qualitativeReviewForm.faultCate = "";
      //获取故障分类数据
      let params = {
        dictTypeCode: "811042",
        parentCode: type,
      };
      return new Promise(resolve => {
        apiEnumListNew(params).then(res => {
          if (res.code == 200) {
            this.faultCateOptions = res?.data ?? [];
          }
          resolve("success");
        });
      });
    },

    changeFaultCate(type) {
      if (type) {
        this.qualitativeReviewForm.faultReason = "";
        let params = {
          dictTypeCode: "811043",
          parentCode: type,
        };
        return new Promise(resolve => {
          apiEnumListNew(params).then(res => {
            if (res.code == 200) {
              this.faultReasonOptions = res?.data ?? [];
            }
            resolve("success");
          });
        });
      }
    },
    qualitativeReviewDetail() {
      let param = {
        opType: 2,
        workItemId: this.qualitativeReviewForm.workItemId,
        woId: this.qualitativeReviewForm.woId,
      };
      apiQualitativeDetail(param)
        .then(res => {
          if (res.status == "0") {
            this.qualitativeReviewForm = cloneDeep(res?.data ?? {});
            this.changeProfessionalType(
              this.qualitativeReviewForm.professionalType
            ).then(() => {
              this.$set(
                this.qualitativeReviewForm,
                "faultCate",
                res?.data?.faultCate ?? ""
              );
            });
            this.changeFaultCate(res.data.faultCate).then(() => {
              this.$set(
                this.qualitativeReviewForm,
                "faultReason",
                res?.data?.faultReason ?? ""
              );
            });
            if (res.data.appendix) {
              this.fddxFileArr = JSON.parse(res.data.appendix);
              this.qualitativeReviewForm.attachmentName = "xlFile";
            }
            this.$set(this.qualitativeReviewForm, "auditResult", "1");
            this.$set(this.qualitativeReviewForm, "isExeOver", "1");
            if (this.qualitativeReviewForm.seizeOrders == "0") {
              this.$set(this, "isExeOverDisabled", false);
            } else {
              this.$set(this, "isExeOverDisabled", true);
            }
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    relatedFilesBrowse() {
      this.relatedFilesDialogVisible = true;
    },
    changeFileData(data) {
      this.qualitativeReviewForm.relatedFiles = data.fileName;
      this.qualitativeReviewForm.attachmentName = data.fileName;
      this.importForm.relatedFilesFileList = data.attachmentFileList;
      this.relatedFilesDialogVisible = false;
    },
    closeAttachmentDialog() {
      this.relatedFilesDialogVisible = false;
    },
    handleSubmit(formName) {
      this.$refs[formName].validate((valid, a) => {
        if (this.uncheck(a)) {
          this.qualitativeReviewFullscreenLoading = true;
          this.$set(this.qualitativeReviewForm, "actionName", "定性审核");
          let formData = new FormData();
          if (this.importForm.relatedFilesFileList.length > 0) {
            for (let item of this.importForm.relatedFilesFileList) {
              formData.append("files", item.raw);
            }
          } else {
            formData.append("files", "");
          }
          formData.append(
            "jsonParam",
            JSON.stringify(this.qualitativeReviewForm)
          );
          formData.append(
            "checkParam",
            JSON.stringify(this.qualitativeReviewForm)
          );
          apiQualitativeReview(formData)
            .then(res => {
              if (res.status == "0") {
                this.$message.success("故障定性审核完成");
                this.$emit("qualitativeReviewSubmit", res.data);
              } else {
                this.$message.error("故障定性审核失败");
              }
              this.qualitativeReviewFullscreenLoading = false;
            })
            .catch(error => {
              console.log(error);
              this.$message.error("故障定性审核失败");
              this.qualitativeReviewFullscreenLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    closeAndDeleteFile(tag) {
      this.deleteFile(tag);
    },
    deleteFile(tag) {
      let param = {
        attId: tag.id,
        linkId: this.qualitativeReviewForm.linkId,
      };
      apiDeleteFdFile(param)
        .then(res => {
          if (res.status == "0") {
            this.fddxFileArr.splice(this.fddxFileArr.indexOf(tag), 1);
            this.qualitativeReviewForm.appendix = JSON.stringify(
              this.fddxFileArr
            );
            if (
              this.importForm.relatedFilesFileList.length == 0 &&
              this.fddxFileArr.length == 0
            ) {
              this.qualitativeReviewForm.attachmentName = null;
            }
            this.$message.success("附件删除成功");
          } else {
            this.$$message.error("附件删除失败");
          }
        })
        .catch(error => {
          this.$message.error("附件删除失败");
          console.log(error);
        });
    },
    closeFile(tag) {
      this.importForm.relatedFilesFileList.splice(
        this.importForm.relatedFilesFileList.indexOf(tag),
        1
      );
      if (
        this.importForm.relatedFilesFileList.length == 0 &&
        this.fddxFileArr.length == 0
      ) {
        this.qualitativeReviewForm.attachmentName = null;
      }
    },
    downloadAppendixFile(data) {
      let param = {
        attId: data.id,
      };
      apiDownloadAppendixFile(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("文件下载成功");
          } else {
            this.$message.error("文件下载失败");
          }
        })
        .catch(error => {
          console.log(error);
          this.$message.error("文件下载失败");
        });
    },
    onReset() {
      // this.qualitativeReviewForm.faultCate = null;
      // this.qualitativeReviewForm.faultReason = null;
      // this.qualitativeReviewForm.troubleshootingObject = null;
      // this.qualitativeReviewForm.troubleshootingResult = null;
      // this.qualitativeReviewForm.faultImpactRange = null;
      // this.qualitativeReviewForm.faultHandingSituation = null;
      // this.qualitativeReviewForm.faultCauseAnalysis = null;
      // this.qualitativeReviewForm.feedbackPersonInfo = null;
      // this.qualitativeReviewForm.falutComment = null;
      // this.importForm.relatedFilesFileList = [];

      // this.qualitativeReviewForm.emergencyLevel = this.common.emergencyLevel;
      this.qualitativeReviewForm.auditResult = "1";
      this.qualitativeReviewForm.isExeOver = "1";
      this.qualitativeReviewForm.auditContent = null;

      this.qualitativeReviewDetail();
    },

    second2Time(days) {
      return this.showTime(Math.abs(days));
    },
    showTime(val) {
      if (val) {
        if (val == 0) return "0秒";
        var time = "";
        var second = val % 60;
        var minute = parseInt(parseInt(val) / 60) % 60;
        time = second + "秒";
        if (minute >= 1) {
          time = minute + "分" + second + "秒";
        }
        var hour = parseInt(parseInt(val / 60) / 60) % 24;
        if (hour >= 1) {
          time = hour + "小时" + minute + "分" + second + "秒";
        }
        var day = parseInt(parseInt(parseInt(val / 60) / 60) / 24);
        if (day >= 1) {
          time = day + "天" + hour + "小时" + minute + "分" + second + "秒";
        }

        return time;
      } else {
        return "0秒";
      }
    },
    selectBlur(e, prop) {
      if (e.target.value) {
        this.$set(this.qualitativeReviewForm, prop, e.target.value);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.qualitative-review {
  max-height: calc(90vh - 150px);
  min-height: 400px;
  margin: 0 4px;
  padding-left: 4px;
  padding-right: 8px;
  overflow-y: auto;
  .cus-card ::v-deep .el-card__header {
    padding: 11px 24px 10px;
    font-weight: 400;
    @include themify {
      background-color: themed("$--background-color-base");
    }
  }
  .fileName_style_download {
    margin-right: 3px;
    cursor: pointer;
    vertical-align: middle;
    div {
      display: inline-block;
      max-width: 120px;
      vertical-align: top;
    }
  }
  .fileName_style {
    margin-right: 3px;
    vertical-align: middle;
    div {
      display: inline-block;
      max-width: 120px;
      vertical-align: top;
    }
  }
}
</style>
