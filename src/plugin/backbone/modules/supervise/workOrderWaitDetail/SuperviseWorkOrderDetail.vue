<template>
  <head-fixed-layout class="supervise-detail" v-loading="pageLoading">
    <template #header>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="24" :md="12" :lg="14" class="head-title">
          <text-collapse :text="`【${bigProfessional}督办单】${headInfo.title}`" :max-lines="2"></text-collapse>
        </el-col>
        <el-col :xs="24" :sm="24" :md="12" :lg="10" class="head-handle-wrap">
          <el-button-group>
            <el-button type="button" @click="onHeadHandleClick('jcxx')">基础信息</el-button>
            <el-button type="button" @click="onHeadHandleClick('gjxq')">告警详情</el-button>
            <el-button type="button" v-if="ppResult" @click="onHeadHandleClick('glzd')">关联诊断</el-button>
            <el-button type="button" :style="getStatusStyle()" @click="onHeadHandleClick('fkdxq')">反馈单详情</el-button>

            <el-dropdown @command="onHeadHandleClick" class="el-button more-dropdown" size="medium">
              <el-button type="button">更多</el-button>
              <el-dropdown-menu slot="dropdown">
                <template v-for="(item, i) in headMoreDrops">
                  <template>
                    <el-dropdown-item :command="item.command" :key="i">
                      {{ item.title }}
                    </el-dropdown-item>
                  </template>
                </template>
              </el-dropdown-menu>
            </el-dropdown>
          </el-button-group>
          <br />
          <!-- 按钮动态展示 -->
          <el-button size="mini" type="primary" v-for="(item, i) in processButtonArr" class="btnleft__group" :key="i"
            @click="buttonClick(item.value, item.desc)" v-loading.fullscreen.lock="processFullscreenLoading">{{
              item.value
            }}</el-button>
        </el-col>
      </el-row>
      <el-divider direction="horizontal" content-position="left" class="divider"></el-divider>
      <div class="head-info2">
        <el-row :gutter="20" tag="p">
          <el-col :xs="24" :sm="24" :md="12" :lg="12" tag="p" class="head-sheetId">
            工单编号: {{ headInfo.sheetId }}
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="12" tag="p">
            <el-row :gutter="20" type="flex">
              <el-col :xs="24" :sm="12" :md="12" :lg="7" tag="p" class="head-up-down">
                <div>当前处理人</div>
                <div class="text-truncate" @click="currentProcessor" style="
                    cursor: pointer;
                    text-decoration: underline;
                    color: #b50b14;
                    user-select: unset;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    font-size: 18px;
                    line-height: 28px;
                  " :title="headInfo.currentHandler">
                  {{ headInfo.currentHandler }}
                </div>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="4" tag="p" class="head-up-down">
                <div>工单状态</div>
                <div>
                  {{ headInfo.sheetStatus }}
                </div>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="7" tag="p" class="head-up-down"
                v-if="headInfo.sheetStatus == '待受理'">
                <div>剩余受理时间</div>
                <div class="text-primary" :title="setAcceptTimeLimit">
                  {{ setAcceptTimeLimit }}
                </div>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="7" tag="p" class="head-up-down" v-if="
                [
                  '处理中',
                  '待定性',
                  '待确认',
                  '待定性审核',
                  '挂起',
                  '上传故障报告',
                  '故障报告审核',
                ].includes(headInfo.sheetStatus)
              ">
                <div>剩余处理时间</div>
                <div class="text-primary" :title="setEstimatedProTimeLimit">
                  {{ setEstimatedProTimeLimit }}
                </div>
              </el-col>
              <el-col :xs="24" :sm="12" :md="12" :lg="6" tag="p" class="head-up-down">
                <div>工单总耗时</div>
                <div class="text-primary" :title="headInfo.totalWorkingTime">
                  {{ headInfo.totalWorkingTime }}
                </div>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
        <el-row :gutter="20" tag="p">
          <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">
            建单人: {{ headInfo.buildSingleMan }}
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">
            建单部门: {{ headInfo.buildSingleDept }}
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">
            建单时间: {{ headInfo.buildSingleTime }}
          </el-col>
        </el-row>
        <el-row :gutter="20" tag="p">
          <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">
            工单来源: {{ headInfo.sourceInfo }}
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">
            发生时间: {{ headInfo.occurrenceTime }}
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">
            紧急程度: {{ headInfo.emergencyDegree }}
          </el-col>
        </el-row>
      </div>
    </template>
    <base-info ref="jcxx" v-if="basicWorkOrderData" :basicWorkOrderData="basicWorkOrderData" :woId="common.woId"
      :workItemId="common.workItemId" />
    <alarm-detail ref="gjxq" v-if="common.woId && headInfo.occurrenceTime" :common="common"
      :occurrenceTime="headInfo.occurrenceTime" :isShowClearAlarmApply="isShowClearAlarmApply"
      :isShowManualConfirm="isShowManualConfirm" @alarmClearCallBack="alarmClearCallBack" />
    <relation-diagnosis ref="glzd" v-if="common.woId && ppResult" :woId="common.woId" :ppResult="ppResult"
      :analysisStatus="analysisStatus" />
    <feedback-sheet ref="fkdxq" v-if="showFkdxq && common.woId" :isShowAudit="isShowQualitativeReviewButton"
      :common="common" :woId="common.woId" :qualitativeType="qualitativeType"
      @qualitativeReviewSubmit="qualitativeReviewSubmit" />
    <deal-details ref="clxq" v-if="showClxq" :woId="common.woId" :common="common" />
    <process-log ref="lcrz" v-if="showLcrz" :woId="common.woId" />
    <flow-chart ref="lct" v-if="showLct" :common="common" />
    <el-dialog title="返单" :visible.sync="dialogBackSingleVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="dialogBackSingleClose" :fullscreen="false" width="83%" top="5vh">
      <span slot="title">
        <span style="line-height: 24px; font-size: 18px; color: #333">返单</span>
      </span>
      <back-single ref="backSingleForm" :basicWorkOrderData="basicWorkOrderData" :common="common" :timing="timing"
        :randomNum="randomNum" @closeBackSingleDialog="dialogBackSingleSubmitClose"></back-single>
    </el-dialog>
    <el-dialog title="处理人" :visible.sync="dialogCurrentProcessorVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="currentProcessorClose" :fullscreen="false" width="60%" top="5vh">
      <current-processor :common="common" :persons="basicWorkOrderData.operatePersonId">
      </current-processor>
    </el-dialog>
    <el-dialog title="阶段反馈" :visible.sync="dialogStageFeedbackVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="dialogStageFeedbackClose" width="450px">
      <stage-feedback :common="common" @stageBackDialogClose="stageBackDialogCommitClose"></stage-feedback>
    </el-dialog>
    <el-dialog title="追单" :visible.sync="dialogAfterSingleVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="dialogAfterSingleClose" width="600px">
      <after-single ref="afterSingleForm" :common="common"
        @closeAfterSingleDialog="dialogAfterSingleSubmitClose"></after-single>
    </el-dialog>
    <el-dialog title="撤单" :visible.sync="dialogRevokeVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="dialogRevokeClose" width="480px">
      <el-form ref="revokeForm" :model="revokeForm" :rules="revokeFormRules" label-width="90px">
        <el-form-item label="审核意见:" prop="auditOpinion">
          <el-input type="textarea" :rows="2" placeholder="请填写审核意见" v-model="revokeForm.auditOpinion"
            style="width: 300px" show-word-limit maxlength="1000">
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleRevokeSubmit('revokeForm')"
          v-loading.fullscreen.lock="revokeSubmitLoading">提 交</el-button>
        <el-button @click="onResetRevoke">重 置</el-button>
      </div>
    </el-dialog>
  </head-fixed-layout>
</template>
<script>
import { mapGetters } from "vuex";
import moment from "moment";

import HeadFixedLayout from "../../workOrder/components/HeadFixedLayout.vue";
import TextCollapse from "@plugin/backbone/components/TextCollapse.vue";
import BaseInfo from "./components/BaseInfo.vue";
import AlarmDetail from "./components/AlarmDetail.vue";
import DealDetails from "./components/DealDetails.vue";
import ProcessLog from "@plugin/backbone/modules/workOrder/workOrderWaitDetail/components/ProcessLog.vue";
import FlowChart from "@plugin/backbone/modules/workOrder/workOrderWaitDetail/components/FlowChart.vue";
import BackSingle from "./components/BackSingle.vue";
import FeedbackSheet from "./components/FeedbackSheet.vue";
import StageFeedback from "./components/StageFeedback.vue";
import AfterSingle from "./components/AfterSingle.vue";
import RelationDiagnosis from "./components/RelationDiagnosis.vue";

import CurrentProcessor from "../../workOrder/components/CurrentProcessor.vue";
import {
  apiGetShowButton,
  apiGetWorkOrderInfo,
  apiAccept,
  apiHaveRead,
  apiRevoke,
} from "./api/CommonApi";
import { mixin } from "../../../../../mixins";
export default {
  components: {
    HeadFixedLayout,
    TextCollapse,
    BaseInfo,
    CurrentProcessor,
    AlarmDetail,
    DealDetails,
    ProcessLog,
    FlowChart,
    BackSingle,
    FeedbackSheet,
    StageFeedback,
    AfterSingle,
    RelationDiagnosis,
  },
  computed: {
    ...mapGetters(["userInfo"]),
    ...mapGetters(["token"]),
    setAcceptTimeLimit() {
      let time = this.headInfo.acceptTimeLimit;
      return this.setTime(time);
    },
    setEstimatedProTimeLimit() {
      let time = this.headInfo.estimatedProTimeLimit;
      return this.setTime(time);
    },
  },
  mixins: [mixin],
  data() {
    return {
      headInfo: {
        title: "",
        sheetId: "",
        currentHandler: "",
        currentLink: "",
        sheetStatus: null,
        acceptTimeLimit: "",
        processingTimeLimit: "",
        processTimeLimit: "",
        totalWorkingTime: "",
        buildSingleMan: "",
        buildSingleDept: "",
        buildSingleTime: "",
        sourceInfo: "",
        occurrenceTime: "",
        emergencyDegree: "",
      },
      headMoreDrops: [
        { command: "clxq", title: "处理详情" },
        { command: "lcrz", title: "流程日志" },
        { command: "lct", title: "流程图" },
      ],
      processButtonArr: [],
      basicWorkOrderData: {},
      dealData: [],
      alarmsData: [],
      messageNum: "", // chatops 未读条数
      isTitleCollapse: false,
      common: {
        woId: "", //工单ID
        workItemId: "", //工作项ID
        processInstId: null, //流程实例ID
        processDefId: null, //流程定义ID
        failureTime: "", //故障发生时间
        failureInformTime: "", //故障通知时间
        actionName: "", //操作按钮名称
        processNode: "", //环节名称
        sheetNo: "", //工单编号
        isSender: null, //是否为建单人
        agentManId: "", //主送人Id
        agentMan: "", //主送人
        copyManId: "", //抄送人Id
        copyMan: "", //抄送人
        sheetStatus: "", //工单状态
        agentDeptCode: "",
        agentDeptName: "",
        copyDeptCode: "",
        copyDeptName: "",
        professionalType: null,
        networkType: null,
        hangOver: null, //挂起历时
        faultCauseDescription: null, //故障原因描述
        networkTypeName: null, //网络类型中文名
        professionalTypeName: null, //专业类型中文名
        faultLevel: null,
        acceptMan: null,
        acceptDeptName: null,
        processTimeLimit: null, //预估处理时限
        alarmClearTime: null, //告警清除时间
        auditResult: null, //审核结果
        isFeedBack: null,
        isObject: null, //追单 0：追信息 1：追人
      },
      timing: {
        hangTime: "", //挂起时间
      },
      showFkdxq: false,
      showClxq: false,
      showLcrz: false,
      showLct: false,
      showRelation: true,
      showAlarm: false,
      processFullscreenLoading: false,
      isShowQualitativeReviewButton: false,
      //阶段反馈
      dialogStageFeedbackVisible: false,

      //当前处理人
      dialogCurrentProcessorVisible: false,
      //挂起
      dialogHangUpVisible: false,
      //解挂
      dialogSolutionToHangVisible: false,
      //挂起审核
      auditTitle: null,
      opContent: null,
      dialogPendingReviewVisible: false,

      //类型
      type: "single",
      //定性审核
      dialogQualitativeAuditVisible: false,

      //追单
      dialogAfterSingleVisible: false,

      // 故障定性
      dialogQualitativeVisible: false,

      //定性类型
      qualitativeType: "",
      route: null,
      //从哪个页面进入的，不同页面进入，显示的按钮会不同，如：待阅工单只展示已阅、传阅按钮
      fromPage: null,
      pageLoading: false,
      networkType: null,
      turnFlag: null, // 转派了，就不显示转派按钮。'Y'不显示
      regularFdbkFlag: null, // 操作了，阶段反馈，才能处理完成，'Y'显示
      locationFlag: null, //现场打点完了，才显示，'Y'显示
      rsaEncrypt: "", // chatops 未读加密
      rsaEncryptUrl: "", // chatops iframe 路径
      chatopsVisible: false, // chatops 弹出框
      ppResult: null,
      analysisStatus: 0,
      random: 0,
      isShowClearAlarmApply: false,
      isShowManualConfirm: false,
      dialogBackSingleVisible: false,
      randomNum: 0,
      //撤单
      dialogRevokeVisible: false,
      revokeSubmitLoading: false,
      revokeForm: {
        auditOpinion: null,
      },
      revokeFormRules: {
        auditOpinion: [
          {
            required: true,
            message: "请填写审核意见",
          },
          {
            validator: this.checkLength,
            max: 1000,
            form: "revokeForm",
            messsage: "已超过填写字数上限",
          },
        ],
      },
      bigProfessional: "",
    };
  },
  created() {
    this.route = this.$route;
  },
  mounted() {
    this.common.workItemId = this.$route.query.workItemId;
    this.common.processInstId = this.$route.query.processInstId;
    this.common.woId = this.$route.query.woId;
    this.common.processDefId = this.$route.query.processDefId;
    this.fromPage = this.$route.query.fromPage;
    this.getShowButton().then(() => {
      this.getWorkOrderInfo();
    });
  },

  methods: {
    getShowButton() {
      return new Promise(resolve => {
        let param = {
          woId: this.common.woId,
          workItemId: this.common.workItemId,
          processInstId: this.common.processInstId,
          fromPage: this.fromPage,
        };
        apiGetShowButton(param)
          .then(res => {
            if (res.status == "0") {
              let self = this;
              self.processButtonArr = res?.data?.user ?? [];
              let alarmButton = res?.data?.admin ?? [];
              let qualitativeReviewButton = res?.data?.admin ?? [];
              let reportReviewButton = res?.data?.admin ?? [];
              if (
                qualitativeReviewButton.length > 0 &&
                qualitativeReviewButton[0].value == "定性审核"
              ) {
                this.isShowQualitativeReviewButton = true;
                this.qualitativeType = "定性审核";
              } else {
                this.isShowQualitativeReviewButton = false;
              }
              if (
                reportReviewButton.length > 0 &&
                reportReviewButton[0].value == "故障报告审核"
              ) {
                this.isShowReportReview = true;
              } else {
                this.isShowReportReview = false;
              }
              if (
                alarmButton.length > 0 &&
                alarmButton.some(item => item.value == "清除告警申请")
              ) {
                this.isShowClearAlarmApply = true;
              } else {
                this.isShowClearAlarmApply = false;
              }
              if (
                alarmButton.length > 0 &&
                alarmButton.some(item => item.value == "人工确认清除告警")
              ) {
                this.isShowManualConfirm = true;
              } else {
                this.isShowManualConfirm = false;
              }
            }
            resolve("success");
          })
          .catch(error => {
            console.log(error);
          });
      });
    },
    getWorkOrderInfo() {
      this.pageLoading = true;
      let formData = new FormData();
      formData.append("woId", this.common.woId);
      formData.append(
        "processInstId",
        this.common.processInstId ? this.common.processInstId : ""
      );
      formData.append(
        "workItemId",
        this.common.workItemId ? this.common.workItemId : ""
      );
      apiGetWorkOrderInfo(formData)
        .then(res => {
          if (res.status == "0") {
            let self = this;
            self.basicWorkOrderData = res?.data?.rows?.[0] ?? {};
            self.headInfo.title = self.basicWorkOrderData.sheetTitle;

            self.headInfo.sheetId = self.basicWorkOrderData.sheetNo;
            self.headInfo.currentHandler =
              self.basicWorkOrderData.operatePerson; // 当前处理人
            self.headInfo.acceptTimeLimit =
              self.basicWorkOrderData.remainAcceptTime;
            self.headInfo.estimatedProTimeLimit =
              self.basicWorkOrderData.remainHandleTime;
            self.headInfo.sheetStatus = self.basicWorkOrderData.sheetStatus;
            self.headInfo.buildSingleMan = self.basicWorkOrderData.senderName;
            self.headInfo.buildSingleDept =
              self.basicWorkOrderData.senderDeptName;
            self.headInfo.buildSingleTime =
              self.basicWorkOrderData.sheetCreateTime;
            this.getWorkOrderSpentTime(self.basicWorkOrderData); //工单总耗时
            self.headInfo.sourceInfo = self.basicWorkOrderData.createType;
            self.headInfo.occurrenceTime =
              self.basicWorkOrderData.alarmCreateTime;
            self.headInfo.emergencyDegree =
              self.basicWorkOrderData.emergencyLevel;
            self.common.failureTime = self.basicWorkOrderData.alarmCreateTime;
            self.common.failureInformTime =
              self.basicWorkOrderData.sheetCreateTime;
            self.common.processNode = self.basicWorkOrderData.processNode;
            self.common.sheetNo = self.basicWorkOrderData.sheetNo;
            self.common.isSender =
              self.basicWorkOrderData.senderName == self.userInfo.realName
                ? 1
                : 0;
            self.common.agentManId = self.basicWorkOrderData.agentManId;
            self.common.agentMan = self.basicWorkOrderData.agentMan;
            self.common.agentDeptCode = self.basicWorkOrderData.agentDeptCode;
            self.common.agentDeptName = self.basicWorkOrderData.agentDeptName;
            self.common.copyDeptCode = self.basicWorkOrderData.copyDeptCode;
            self.common.copyDeptName = self.basicWorkOrderData.copyDeptName;
            // 添加受理时限
            self.common.acceptTimeLimit = self.basicWorkOrderData.acceptTimeLimit;
            self.common.copyManId = self.basicWorkOrderData.copyManId;
            self.common.copyMan = self.basicWorkOrderData.copyMan;
            self.common.sheetStatus = self.headInfo.sheetStatus;
            self.timing.hangTime = self.basicWorkOrderData.suspendDuration;
            self.common.professionalType =
              self.basicWorkOrderData.professionalTypeId;
            self.common.professionalTypeName =
              self.basicWorkOrderData.professionalType;
            self.common.hangOver = self.basicWorkOrderData.suspendDuration;
            self.common.faultCauseDescription =
              self.basicWorkOrderData.ppResult;
            self.common.busName = self.basicWorkOrderData.busName;
            self.common.auditResult = self.basicWorkOrderData.auditResult;
            self.common.isUploadReport = self.basicWorkOrderData.isUploadReport;
            self.common.remainAcceptTime =
              self.basicWorkOrderData.remainAcceptTime;
            self.common.alarmClearTime = self.basicWorkOrderData.alarmClearTime;
            self.common.mecNodeName = self.basicWorkOrderData.mecNodeName;
            self.common.emergencyLevel = self.basicWorkOrderData.emergencyLevel;
            self.bigProfessional = self.basicWorkOrderData.bigProfessional;
            this.ppResult = self.basicWorkOrderData.ppResultOri;
            this.analysisStatus = self.basicWorkOrderData.analysisStatus;
            this.showClxq = false;
            this.showLcrz = false;
            this.showLct = false;
            this.$nextTick(() => {
              this.showClxq = true;
              this.showLcrz = true;
              this.showLct = true;
            });
            if (
              self.basicWorkOrderData.auditResult == "0" ||
              self.basicWorkOrderData.feedbackResult == "0" ||
              self.headInfo.sheetStatus == "待定性审核" ||
              self.headInfo.sheetStatus == "待定性" ||
              self.headInfo.sheetStatus == "待归档" ||
              self.headInfo.sheetStatus == "已归档" ||
              self.headInfo.sheetStatus == "作废" ||
              self.headInfo.sheetStatus == "故障报告审核" ||
              self.headInfo.sheetStatus == "上传故障报告"
            ) {
              this.showFkdxq = false;
              this.$nextTick(() => {
                this.showFkdxq = true;
              });
            } else {
              this.showFkdxq = false;
            }
          }
        })
        .catch(error => {
          console.log(error);
        })
        .finally(() => {
          this.pageLoading = false;
        });
    },
    getWorkOrderSpentTime(basicData) {
      if (
        basicData.sheetStatus == "异常归档" ||
        basicData.sheetStatus == "已归档" ||
        basicData.sheetStatus == "作废"
      ) {
        let days = moment(
          basicData.sheetFilingTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.headInfo.buildSingleTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        this.headInfo.totalWorkingTime = this.showTime(Math.abs(days));
      } else {
        let days = moment(new Date(), "YYYY-MM-DD HH:mm:ss").diff(
          moment(this.headInfo.buildSingleTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        this.headInfo.totalWorkingTime = this.showTime(Math.abs(days));
      }
    },
    buttonClick(name, desc) {
      this.common.actionName = name;
      switch (name) {
        case "受理":
          this.accept(name);
          break;
        case "返单":
          this.randomNum = Math.random();
          this.dialogBackSingleVisible = true;
          break;
        case "已阅":
          this.haveRead();
          break;
        case "定性审核":
          this.showDxsh = false;
          this.$nextTick(() => {
            this.showDxsh = true;
          });
          this.dialogQualitativeReviewVisible = true;
          break;
        case "阶段反馈":
          this.dialogStageFeedbackVisible = true;
          break;
        case "申请挂起":
          this.dialogHangUpVisible = true;
          break;
        case "挂起":
          this.dialogHangUpVisible = true;
          break;
        case "解挂":
          this.dialogSolutionToHangVisible = true;
          break;
        case "挂起审核":
          this.opContent = 1;
          this.dialogPendingReviewVisible = true;
          break;

        case "故障定性":
          this.dialogQualitativeVisible = true;
          break;
        case "追单":
          this.common.isObject = desc;
          this.dialogAfterSingleVisible = true;
          break;
        case "撤单":
          this.dialogRevokeVisible = true;
          break;
      }
    },
    accept(buttonName) {
      this.processFullscreenLoading = true;
      sessionStorage.setItem(this.common.woId, "受理");
      let param = {
        woId: this.common.woId,
        workItemId: this.common.workItemId,
        actionName: buttonName,
        processInstId: this.common.processInstId,
      };
      apiAccept(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("您已受理成功");
            this.common.workItemId = res.data.workItemId;
            this.common.processDefId = res.data.processDefId;
            this.common.processInstId = res.data.processInstId;
            this.getWorkOrderInfo();
            this.JumpDetails_Supervise(this.common);
          } else {
            this.$message.error("受理失败");
          }
          this.processFullscreenLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("受理失败");
          this.processFullscreenLoading = false;
        });
    },
    //已阅
    haveRead() {
      this.processFullscreenLoading = true;
      let param = {
        woId: this.common.woId,
      };
      apiHaveRead(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("已阅");
            this.closeAndTurnAroundRead();
          } else {
            this.$message.error("已阅失败");
          }
          this.processFullscreenLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("已阅失败");
          this.processFullscreenLoading = false;
        });
    },
    //返单提交
    dialogBackSingleSubmitClose(data) {
      // this.common.workItemId = data.workItemId;
      // this.common.processInstId = data.processInstId;
      // this.common.processDefId = data.processDefId;
      // this.getShowButton().then(() => {
      //   this.getWorkOrderInfo();
      // });
      // this.dialogBackSingleVisible = false;
      // if (data.currentPage) {
      //   this.JumpDetails_Supervise(this.common);
      // } else {
      //   this.closeAndTurnAround();
      // }
      this.dialogBackSingleVisible = false;
      this.closeAndTurnAround();
    },
    //定性审核提交
    qualitativeReviewSubmit() {
      this.closeAndTurnAround();
    },
    alarmClearCallBack(data) {
      this.$set(this.common, "locateNeName", data || "");
      console.log("locateNeName", data);
    },
    dialogBackSingleClose() {
      this.dialogBackSingleVisible = false;
      this.$refs.backSingleForm.onReset();
    },
    dialogStageFeedbackClose() {
      this.dialogStageFeedbackVisible = false;
    },
    stageBackDialogCommitClose() {
      this.showClxq = false;
      this.showLcrz = false;
      this.$nextTick(() => {
        this.showLcrz = true;
        this.showClxq = true;
      });
      this.dialogStageFeedbackVisible = false;
    },
    //追单
    dialogAfterSingleClose() {
      this.$refs.afterSingleForm.onResetAfterSingleForm();
      this.dialogAfterSingleVisible = false;
    },
    //追单
    dialogAfterSingleSubmitClose(val) {
      this.getShowButton().then(() => {
        this.getWorkOrderInfo();
      });
      this.dialogAfterSingleVisible = false;
      if (val == 1) {
        this.closeAndTurnAround();
      }
    },
    handleRevokeSubmit(formName) {
      this.$refs[formName].validate((valid, a) => {
        if (this.uncheck(a)) {
          this.revokeSubmitLoading = true;
          let param = {
            processNode: this.common.processNode,
            sheetNo: this.common.sheetNo,
          };
          apiRevoke(param)
            .then(res => {
              if (res.status == "0") {
                this.$message.success("撤单成功");
                this.dialogRevokeClose();
                this.closeAndTurnAround();
              } else {
                this.$message.error("撤单失败");
              }
              this.revokeSubmitLoading = false;
            })
            .catch(error => {
              console.log(error);
              this.$message.error("撤单失败");
              this.revokeSubmitLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    //撤单
    dialogRevokeClose() {
      this.onResetRevoke();
    },
    onResetRevoke() {
      this.revokeForm = {
        ...this.$options.data,
      };
    },
    setTime(time) {
      if (time == "-") {
        return "-";
      } else if (time <= 0) {
        const absTime = Math.abs(time);
        let format = "已超时";
        if (absTime < 60) {
          format += `${absTime}分`;
        } else {
          const minFormat = absTime % 60 == 0 ? "" : `${absTime % 60}分`;
          format += `${Math.floor(absTime / 60)}小时${minFormat}`;
        }
        return format;
      } else if (time > 0) {
        if (time < 60) {
          return `${time}分`;
        } else {
          const minFormat = time % 60 == 0 ? "" : `${time % 60}分`;
          return `${Math.floor(time / 60)}小时${minFormat}`;
        }
      }
    },
    getStatusStyle() {
      if (this.common.sheetStatus == "待定性审核") {
        return "color:#b50b14;border-color:#e9b6b9;background-color:#f8e7e8";
      }
    },
    onHeadHandleClick(command) {
      this.$refs[command]?.$el?.scrollIntoView?.({ behavior: "smooth" });
    },
    currentProcessor() {
      this.dialogCurrentProcessorVisible = true;
    },
    currentProcessorClose() {
      this.dialogCurrentProcessorVisible = false;
    },
    JumpDetails_Supervise(data) {
      let self = this;
      self.$store.dispatch("tagsView/delView", self.route).then(() => {
        self.$destroy();
      });
      this.$router.replace({
        name: "supervise_orderDetail",
        query: {
          workItemId: data.workItemId,
          processInstId: data.processInstId,
          woId: data.woId,
          processDefId: data.processDefId,
          fromPage: "待办工单",
          networkType: data.networkType,
        },
      });
    },
    closeAndTurnAround() {
      let self = this;
      self.$store.dispatch("tagsView/delView", self.route).then(() => {
        self.$destroy();
      });
      self.$router.push({
        name: "backbone_toDoRepairOrder",
        query: {
          globalUniqueID: sessionStorage.getItem("globalUniqueID"),
          token: this.token,
        },
      });
    },
    closeAndTurnAroundRead() {
      let self = this;
      self.$store.dispatch("tagsView/delView", self.route).then(() => {
        self.$destroy();
      });
      self.$router.push({
        name: "backbone_toReadRepairOrder",
      });
    },
    showTime(val) {
      var time = "";
      var second = val % 60;
      var minute = parseInt(parseInt(val) / 60) % 60;
      time = second + "秒";
      if (minute >= 1) {
        time = minute + "分" + second + "秒";
      }
      var hour = parseInt(parseInt(val / 60) / 60) % 24;
      if (hour >= 1) {
        time = hour + "小时" + minute + "分";
      }
      var day = parseInt(parseInt(parseInt(val / 60) / 60) / 24);
      if (day >= 1) {
        time = day + "天" + hour + "小时" + minute + "分";
      }
      return time;
    },
  },
};
</script>

<style lang="scss" scoped>
.supervise-detail {
  .head-title {
    position: relative;
    font-size: 20px;
    line-height: 28px;
    font-weight: 700;
  }

  .head-handle-wrap {
    text-align: right;

    .more-dropdown {
      padding: 0;

      @include themify() {
        border: none;
        border-left: 1px solid themed("$--button-default-border-color");
      }
    }

    .btnleft__group {
      margin-left: 5px;
      margin-bottom: 0px;
      margin-top: 10px;
    }
  }

  .divider {
    margin: 12px 0 16px;
  }

  .head-info2 {
    p {
      margin: 0 auto 5px;
    }
  }

  .head-sheetId {
    font-size: 20px;
    line-height: 28px;
  }

  .head-up-down {
    text-align: center;

    &>div:first-child {
      line-height: 20px;

      @include themify() {
        color: themed("$--color-text-regular");
      }
    }

    &>div:last-child {
      font-weight: 400;
      font-size: 18px;
      line-height: 28px;
      white-space: nowrap;

      &.text-primary {
        color: #c43c43;
      }
    }
  }

  .card-title {
    font-weight: 400;
    font-size: 18px;
    box-shadow: 0 0 5px #aaa;
    padding-left: 10px;
  }
}

::v-deep .detail-dialog .el-dialog__body {
  padding: 20px;
}

.outer-link {
  position: fixed;
  top: 55%;
  right: 15px;
  z-index: 99;
  padding: 3px 0px 0px 0px;
  border-radius: 999px;
  box-shadow: 0 0 8px #999;
  background: rgba(255, 255, 255, 0.7);
  width: 56px;
  height: 56px;

  .icon {
    display: block;
    width: 36px;
    height: 36px;
    cursor: pointer;
    background: url("../../../assets/icon_chatops.png") center no-repeat;
    background-size: 68% 60%;
    margin-left: 10px;
  }

  .num {
    position: absolute;
    top: -10px;
    right: -5px;
    padding: 2px 8px;
    color: #fff;
    background: #b50b14;
    border-radius: 999px;
  }

  .name {
    color: #b50b14;
    font-size: 11px;
    position: absolute;
    margin-top: -4px;
    font-weight: 500;
    text-align: center;
    width: 56px;
  }
}
</style>
