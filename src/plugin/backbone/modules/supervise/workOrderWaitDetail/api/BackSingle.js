import { getJson, postFormData } from "@/utils/axios";
const faultAreaUrl = "backbone/info/faultArea"; //故障发生地区字典数据
const getEvaluationUrl = "/backbone/workflow/getEvaluation"; //是否已评价

const backSingleUrl = "/superviseFlow/workflow/ackDefine/feedback";
const qualitativeUrl = "/superviseFlow/workflow/ackDefine/define";
const enumListNewUrl = "commonDict/enum/listNew";

const apiGetFaultArea = params => getJson(faultAreaUrl, params);
const apiBackSingle = params => postFormData(backSingleUrl, params);
const apiGetEvaluation = params => getJson(getEvaluationUrl, params);
const apiQualitative = params => postFormData(qualitativeUrl, params);
const apiEnumListNew = params => getJson(enumListNewUrl, params);

export {
  apiGetFaultArea,
  apiBackSingle,
  apiGetEvaluation,
  apiQualitative,
  apiEnumListNew,
};
