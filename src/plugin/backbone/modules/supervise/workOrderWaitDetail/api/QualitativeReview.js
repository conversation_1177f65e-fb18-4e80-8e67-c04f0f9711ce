import { getJson, postFormData } from "@/utils/axios";

const qualitativeDetailUrl = "superviseFlow/workflow/ackDefine/detail"; //定性审核初始化数据
const qualitativeReviewUrl = "superviseFlow/workflow/ackDefine/defineCheck"; //定性审核

const apiQualitativeDetail = params => getJson(qualitativeDetailUrl, params);
const apiQualitativeReview = params =>
  postFormData(qualitativeReviewUrl, params);

export { apiQualitativeDetail, apiQualitativeReview };
