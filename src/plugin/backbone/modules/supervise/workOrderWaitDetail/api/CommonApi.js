import {
  getJson,
  postJson,
  getJsonBlob,
  postFormData,
  delJson,
  postJsonBlob,
} from "@/utils/axios";

const areaUrl = "commonDict/info/faultArea"; //公共下拉接口地址(省份,地市,区县区域)
const apiDictUrl = "commonDict/enum/list"; //公用枚举
const showButtonUrl = "superviseFlow/workflow/op/getBtnList"; //详情页面中的按钮展示
const feedbackUrl = "superviseFlow/workflow/queryFeedback"; //工单详情-定性详情
const processUrl = "superviseFlow/workflow/getProcessInfo"; //工单详情，处理详情
const workOrderInfoUrl = "superviseFlow/workflow/queryByWoId"; //详情页面中的工单基本信息
const exportWorkOrderUrl = "superviseFlow/workflow/queryByWoId/excel"; //工单基本信息导出
const alarmDetailUrl = "superviseFlow/workflow/queryAlarm"; //告警详情表格
const relationDiagnosisUrl = "backbone/workflow/queryRelation"; //关联诊断

const drafOrderUrl = "superviseFlow/workflow/op/getOrderOne"; // 草稿回显

const acceptUrl = "superviseFlow/workflow/accept"; //集团通用流程受理
const auditUrl = "superviseFlow/workflow/op/checkInterrupt"; //挂起申请审核
const abendUrl = "superviseFlow/workflow/op/applyAbnormalEnd"; //异常终止申请
const abendAuditUrl = "superviseFlow/workflow/op/checkAbnormalEnd"; //异常终止审核
const afterSingleUrl = "superviseFlow/workflow/op/addWoInfo"; //追单
const revokeUrl = "superviseFlow/workflow/removeProcessInst"; //撤单
const transferSubmitUrl = "/superviseFlow/workflow/op/transfer"; //转派
const stageFeedbackUrl = "superviseFlow/workflow/op/stageFeedback"; //阶段反馈

const syncClearUrl = "backbone/workflow/clearQuery"; //同步清除
const backSingleUrl = "backbone/workflow/ackDefine/feedback"; //返单
const qualitativeDetailUrl = "wireless/operation/ackDefineDetail"; //返查定性详情(返单)
const qualitativeUrl = "backbone/workflow/ackDefine/define"; //定性提交
const haveReadUrl = "backbone/workflow/read/setReadStatus"; //已阅
const circulatedUrl = "backbone/workflow/read/addReadInfo"; //传阅
const circulatedUserTreeUrl = "backbone/tree/searchRedeployUser"; //传阅用户树数据
const circulatedOrgTreeUrl = "backbone/tree/searchRedeployOrg"; //传阅组织树数据
const queryAttachmentListUrl = "backbone/info/attachmentList"; //附件组编码查询附件列表
const downloadAppendixUrl = "commonDict/attach/download"; //公共下载附件接口
const deleteFdFileUrl = "backbone/workflow/ackDefine/deleteAttach"; //删除附件接口
const manualFileUrl = "backbone/workflow/queryCircuitAttachment/excel";
const fluenceExcelUrl = "backbone/workflow/queryCutInfluenceExcel/excel"; //光缆excel
const provinceDictUrl = "backbone/info/provinceDict";

const oneKeyIvrNoticeUrl = "backbone/workflow/oneKeyIvr/oneKeyIvrNotice"; //一键IVR
const deleteDraftUrl = "backbone/workflow/op/delDraft";
const orgInfoUrl = "backbone/info/orgInfo"; //当前用户的信息地址
const topoUrl = "/netfm3topo/topoapi/topoinfo"; //topo图是否呈现接口
const enumOrgTypeUrlNew = "commonDict/enum/listNew"; //督办单 所属专业-》告警专业

const apiArea = params => getJson(areaUrl, params);
const apiDict = params => getJson(apiDictUrl, params);
const apiGetShowButton = params =>
  postJson(showButtonUrl, params, { timeout: 0 });
const apiTransfer = params => postJson(transferSubmitUrl, params); //转派
const apiDeleteDraft = params => postJson(deleteDraftUrl, params);

const apiGetWorkOrderInfo = params =>
  postJson(workOrderInfoUrl, params, { timeout: 0 });
const apiExportWorkOrder = params => getJsonBlob(exportWorkOrderUrl, params);
const apiAccept = params => postJson(acceptUrl, params);
const apiQueryAlarmDetail = params => postJson(alarmDetailUrl, params);
const apiSyncClear = params => postJson(syncClearUrl, params);
const apiGetRelationDiagnosis = params => getJson(relationDiagnosisUrl, params);
const apiBackSingle = params => postFormData(backSingleUrl, params);
const apiQualitativeDetail = params => getJson(qualitativeDetailUrl, params);
const apiQualitative = params => postFormData(qualitativeUrl, params);
const apiStageFeedBack = params => postFormData(stageFeedbackUrl, params);
const apiRevoke = params => postJson(revokeUrl, params);
const apiAudit = params => postJson(auditUrl, params);
const apiAbend = params => postJson(abendUrl, params);
const apiAbendAudit = params => postJson(abendAuditUrl, params);
const apiAfterSingle = params => postFormData(afterSingleUrl, params);
const apiHaveRead = params => postJson(haveReadUrl, params);
const apiCirculated = params => postJson(circulatedUrl, params);
const apiCirculatedUserTree = params => getJson(circulatedUserTreeUrl, params);
const apiCirculatedOrgTree = params => getJson(circulatedOrgTreeUrl, params);
const apiDownloadAppendixFile = params =>
  getJsonBlob(downloadAppendixUrl, params);
const apiQueryAttachment = params => getJson(queryAttachmentListUrl, params);
const apiDeleteFdFile = params => delJson(deleteFdFileUrl, params);
const apiDownloadManualFile = params => postJsonBlob(manualFileUrl, params);
const apiGetProvinceDict = params => getJson(provinceDictUrl, params);
const apioneKeyIvrNotice = params => postJson(oneKeyIvrNoticeUrl, params);
const apifluenceExcel = params => postJsonBlob(fluenceExcelUrl, params);
const apiGetProcessInfo = params => getJson(processUrl, params);
const apiInitOrderDraf = params => postJson(drafOrderUrl, params);
const apiqueryFeedback = params => getJson(feedbackUrl, params);

const apiFileUpload = (url, params) => postFormData(url, params);
const apiGetOrgInfo = params => getJson(orgInfoUrl, params);
const apiTopoIsShow = params => getJson(topoUrl, params);
const apiGetEnumList = params => getJson(enumOrgTypeUrlNew, params);

// 时间戳 ，转化成 日期时间格式。如果是当前时间，传参 Date.now()
function getCurrentTime(timeVal) {
  const time = new Date(timeVal);
  const yy = time.getFullYear();
  const mm =
    time.getMonth() + 1 < 10
      ? "0" + (time.getMonth() + 1)
      : time.getMonth() + 1;
  const dd = time.getDate() < 10 ? "0" + time.getDate() : time.getDate();
  const hh = time.getHours() < 10 ? "0" + time.getHours() : time.getHours();
  const mf =
    time.getMinutes() < 10 ? "0" + time.getMinutes() : time.getMinutes();
  const ss =
    time.getSeconds() < 10 ? "0" + time.getSeconds() : time.getSeconds();
  const gettime = yy + "-" + mm + "-" + dd + " " + hh + ":" + mf + ":" + ss;
  return gettime;
}

export {
  apiDeleteDraft,
  apiArea,
  apiGetShowButton,
  apiGetWorkOrderInfo,
  apiExportWorkOrder,
  apiAccept,
  apiQueryAlarmDetail,
  apiSyncClear,
  apiGetRelationDiagnosis,
  apiBackSingle,
  apiQualitativeDetail,
  apiQualitative,
  apiStageFeedBack,
  apiRevoke,
  apiAudit,
  apiAbend,
  apiAbendAudit,
  apiAfterSingle,
  apiHaveRead,
  apiCirculated,
  apiCirculatedUserTree,
  apiCirculatedOrgTree,
  apiDownloadAppendixFile,
  apiQueryAttachment,
  apiDeleteFdFile,
  apiDownloadManualFile,
  apiGetProvinceDict,
  apioneKeyIvrNotice,
  apifluenceExcel,
  apiTransfer,
  getCurrentTime,
  apiGetProcessInfo,
  apiInitOrderDraf,
  apiqueryFeedback,
  apiFileUpload,
  apiDict,
  apiGetOrgInfo,
  apiTopoIsShow,
  apiGetEnumList,
};
