<template>
  <head-fixed-layout class="draft-eidt-page" v-loading="pageLoading">
    <template #header>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="24" :md="12" :lg="14" class="head-title">
          <text-collapse
            :text="`【共建共享工单】${headInfo.title}`"
            :max-lines="2"
          ></text-collapse>
        </el-col>
        <el-col :xs="24" :sm="24" :md="12" :lg="10" class="head-handle-wrap">
          <el-button-group>
            <el-button type="button" @click="onHeadHandleClick('jcxx')"
              >基础信息</el-button
            >
            <el-button type="button" @click="onHeadHandleClick('gjxq')"
              >故障处理列表</el-button
            >
          </el-button-group>
        </el-col>
      </el-row>
      <el-divider
        direction="horizontal"
        content-position="left"
        class="divider"
      ></el-divider>
      <div class="head-info2">
        <el-row :gutter="20" tag="p">
          <el-col
            :xs="24"
            :sm="24"
            :md="14"
            :lg="13"
            tag="p"
            class="head-sheetId"
          >
            工单编号: {{ headInfo.sheetNo }}
          </el-col>
        </el-row>
        <el-row :gutter="20" tag="p">
          <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">
            派单人: {{ headInfo.assignerName }}
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">
            派单方: {{ headInfo.assignSys }}
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">
            建单时间: {{ headInfo.sheetCreateTime }}
          </el-col>
        </el-row>
      </div>
    </template>

    <!--    基础信息-->
    <base-info
      ref="jcxx"
      v-if="basicWorkOrderData"
      :basicWorkOrderData="basicWorkOrderData"
    />
    <!-- 处理详情 -->
    <alarm-detail ref="gjxq" :sheet-no="sheetNo" :deal-data="dealData" />
  </head-fixed-layout>
</template>

<script>
import { mapGetters } from "vuex";
import moment from "moment";
import BaseInfo from "./components/BaseInfo.vue";
import AlarmDetail from "./components/AlarmDetail.vue";
import HeadFixedLayout from "../../workOrder/components/HeadFixedLayout.vue";
import TextCollapse from "@plugin/backbone/components/TextCollapse.vue";

import { apiGetWorkOrderInfo } from "./api/CommonApi";
export default {
  name: "WorkOrderWaitDetail",
  components: {
    HeadFixedLayout,
    BaseInfo,
    AlarmDetail,
    TextCollapse,
  },
  data() {
    return {
      headInfo: {
        sheetTitle: "",
        sheetNo: "",
        assignerName: "",
        assignSys: "",
        sheetCreateTime: "",
      },

      basicWorkOrderData: {},
      dealData: [],

      pageLoading: true,
      route: null,
      //从哪个页面进入的，不同页面进入，显示的按钮会不同，如：待阅工单只展示已阅、传阅按钮
      sheetNo: null,
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
    ...mapGetters(["token"]),
  },
  created() {
    this.route = this.$route;
    this.sheetNo = this.$route.query.sheetNo;

    //外部链接访问
    if (this.$route.query.outSystem) {
      this.sheetNo = this.$route.query.sheetNo;
    }
  },
  mounted() {
    this.getWorkOrderInfo();
  },
  methods: {
    //获取工单详情
    getWorkOrderInfo() {
      this.pageLoading = true;
      // 第三方打开时， url 中 获取 sheetNo 参数
      // const url = window.location.href;
      // const queryStri = url.substring(url.indexOf("?") + 1).split("&");
      // let sheetNoUrl = "";
      // queryStri.forEach(item => {
      //   if (item.indexOf("sheetNo") >= 0) {
      //     sheetNoUrl = decodeURI(item.split("=")[1]);
      //   }
      // });

      let param = {
        sheetNo: this.sheetNo,
        // processInstId: this.common.processInstId,
        // workItemId: this.common.workItemId,
        // woCode: sheetNoUrl,
      };

      apiGetWorkOrderInfo(param)
        .then(res => {
          if (res.status == "0") {
            let self = this;
            self.basicWorkOrderData = res?.data?.shareOrderInfo ?? {};
            self.dealData = res?.data?.shareDealOrderInfos ?? [];

            self.headInfo.title = self.basicWorkOrderData.faultTitle;
            self.headInfo.sheetNo = self.basicWorkOrderData.sheetNo;
            self.headInfo.assignerName = self.basicWorkOrderData.assignerName;
            self.headInfo.assignSys = self.basicWorkOrderData.assignSys;
            self.headInfo.sheetCreateTime = self.basicWorkOrderData.assignTime;

            // self.headInfo.title = self.basicWorkOrderData.sheetTitle;
          }
        })
        .catch(error => {
          console.log(error);
        })
        .finally(() => {
          this.pageLoading = false;
        });
    },

    closeAndTurnAround() {
      let self = this;
      self.$store.dispatch("tagsView/delView", self.route).then(() => {
        self.$destroy();
      });
      self.$router.push({
        name: "backbone_toDoRepairOrder",
        query: {
          globalUniqueID: sessionStorage.getItem("globalUniqueID"),
          token: this.token,
        },
      });
    },
    closeAndTurnAroundRead() {
      let self = this;
      self.$store.dispatch("tagsView/delView", self.route).then(() => {
        self.$destroy();
      });
      self.$router.push({
        name: "backbone_toReadRepairOrder",
        query: {
          globalUniqueID: sessionStorage.getItem("globalUniqueID"),
          token: this.token,
        },
      });
    },
    onHeadHandleClick(command) {
      this.$refs[command]?.$el?.scrollIntoView?.({ behavior: "smooth" });
    },
  },
};
</script>
<style lang="scss" scoped>
.draft-eidt-page {
  .head-title {
    position: relative;
    font-size: 24px;
    line-height: 28px;
    font-weight: 700;
  }
  .head-handle-wrap {
    text-align: right;
    .more-dropdown {
      padding: 0;
      @include themify() {
        border: none;
        border-left: 1px solid themed("$--button-default-border-color");
      }
    }
    .btnleft__group {
      margin-left: 5px;
      margin-bottom: 3px;
      margin-top: 10px;
    }
  }
  .divider {
    margin: 12px 0 16px;
  }
  .head-info2 {
    p {
      margin: 0 auto 5px;
    }
  }
  .head-sheetId {
    font-size: 20px;
    line-height: 28px;
  }
  .head-up-down {
    text-align: center;
    & > div:first-child {
      line-height: 20px;
      @include themify() {
        color: themed("$--color-text-regular");
      }
    }
    & > div:last-child {
      font-weight: 400;
      font-size: 16px;
      line-height: 24px;
    }
  }
  .card-title {
    font-weight: 400;
    font-size: 18px;
    box-shadow: 0 0 5px #aaa;
    padding-left: 10px;
  }
}
::v-deep .detail-dialog .el-dialog__body {
  padding: 20px;
}
.outer-link {
  position: fixed;
  top: 55%;
  right: 15px;
  z-index: 99;
  padding: 10px;
  border-radius: 999px;
  box-shadow: 0 0 8px #999;
  background: rgba(255, 255, 255, 0.7);
  .icon {
    display: block;
    width: 36px;
    height: 36px;
    cursor: pointer;
    background: url("../../../assets/icon_chatops.png") center no-repeat;
    background-size: 70% 70%;
  }
  .num {
    position: absolute;
    top: -10px;
    right: -5px;
    padding: 2px 8px;
    color: #fff;
    background: #b50b14;
    border-radius: 999px;
  }
}
</style>
