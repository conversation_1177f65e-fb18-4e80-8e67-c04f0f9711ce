<template>
  <el-card shadow="always" class="card" :body-style="{ padding: '10px 24px' }">
    <div class="header clearfix">
      <span class="header-title">基础信息</span>
      <div class="header-right">
        <!-- <el-button
          type="primary"
          size="mini"
          @click="exportBasicWorkOrderInfo"
          v-loading.fullscreen.lock="exportLoading"
          >导出Excel表</el-button
        > -->
      </div>
    </div>
    <div class="content">
      <el-descriptions title="工单基本信息" class="descriptions">
        <el-descriptions-item label="派单方系统单号">{{
          basicWorkOrderData.sheetNo
        }}</el-descriptions-item>
        <el-descriptions-item label="协作单号">{{
          basicWorkOrderData.xzSheetNo
        }}</el-descriptions-item>
        <el-descriptions-item label="专业">
          {{ basicWorkOrderData.spec }}
        </el-descriptions-item>

        <el-descriptions-item label="站址信息">{{
          basicWorkOrderData.siteInfo
        }}</el-descriptions-item>
        <el-descriptions-item label="站址名称">{{
          basicWorkOrderData.siteName
        }}</el-descriptions-item>
        <el-descriptions-item label="站址经纬度">
          {{ basicWorkOrderData.sitePostion }}
        </el-descriptions-item>
        <el-descriptions-item label="站址类别">
          {{ basicWorkOrderData.siteKind }}
        </el-descriptions-item>
        <el-descriptions-item label="基站ID">
          {{ basicWorkOrderData.baseStaId }}
        </el-descriptions-item>
        <el-descriptions-item label="基站名称">
          {{ basicWorkOrderData.baseStaName }}
        </el-descriptions-item>
        <el-descriptions-item label="所属区域">
          {{ basicWorkOrderData.baseStaArea }}
        </el-descriptions-item>
        <el-descriptions-item label="基站级别">
          {{ basicWorkOrderData.baseStaLevel }}
        </el-descriptions-item>
        <el-descriptions-item label="基站类型">
          {{ basicWorkOrderData.baseStaType }}
        </el-descriptions-item>
        <el-descriptions-item label="告警ID和名称">
          {{ basicWorkOrderData.alarmIdName }}
        </el-descriptions-item>
        <el-descriptions-item label="告警类别" span="2">
          {{ basicWorkOrderData.alarmKind }}
        </el-descriptions-item>
        <el-descriptions-item label="告警详情" span="3">
          <text-collapse
            :text="basicWorkOrderData.alarmDetail"
            :max-lines="2"
          ></text-collapse>
        </el-descriptions-item>
        <el-descriptions-item label="告警级别">
          {{ basicWorkOrderData.alarmGrade }}
        </el-descriptions-item>
        <el-descriptions-item label="告警发生时间">
          {{ basicWorkOrderData.alarmOccurTime }}
        </el-descriptions-item>
        <el-descriptions-item label="处理时限">
          {{ basicWorkOrderData.dealLimit }}
        </el-descriptions-item>
        <el-descriptions-item label="故障历时">
          {{ basicWorkOrderData.faultDuration }}
        </el-descriptions-item>
        <el-descriptions-item label="故障原因">
          {{ basicWorkOrderData.faultCauseKind }}
        </el-descriptions-item>

        <el-descriptions-item label="工单进度情况">
          {{ basicWorkOrderData.woStatus }}
        </el-descriptions-item>

        <el-descriptions-item label="派单人邮箱">
          {{ basicWorkOrderData.assignerEmail }}
        </el-descriptions-item>

        <el-descriptions-item label="派单人手机号">
          {{ basicWorkOrderData.assignerTel }}
        </el-descriptions-item>

        <el-descriptions-item label="派单级别">
          {{ basicWorkOrderData.assignLevel }}
        </el-descriptions-item>

        <el-descriptions-item label="派单对象">
          {{ basicWorkOrderData.assignObj }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <el-dialog
      width="500px"
      title="文件列表"
      :visible.sync="impactServiceVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <file-download
        @cancel="impactServiceVisible = false"
        :attachmentArr="impactServiceArr"
      ></file-download>
    </el-dialog>
    <el-dialog
      width="500px"
      title="文件列表"
      :visible.sync="attachmentVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <file-download
        @cancel="attachmentVisible = false"
        :attachmentArr="attachmentArr"
      ></file-download>
    </el-dialog>
  </el-card>
</template>

<script>
import TextCollapse from "@plugin/backbone/components/TextCollapse.vue";

import {
  apiExportWorkOrder,
  apiDownloadManualFile,
  apifluenceExcel,
} from "../api/CommonApi";
import { apiFileDownload } from "../api/CommonApi";
export default {
  components: {
    TextCollapse,
  },
  props: {
    basicWorkOrderData: Object,
    woId: String,
    workItemId: [String, Number],
  },
  name: "BaseInfo",
  data() {
    return {
      builderZs: "",
      builderCs: "",
      notifier: "",
      recipient: "",
      exportLoading: false,
      impactServiceArr: [],
      impactServiceVisible: false,
      attachmentArr: [],
      attachmentVisible: false,
      manualFileName: "电路信息",
      OpticCableName: "影响系统信息",
      OpticCableLoading: false,
      manualFileFullscreenLoading: false,
      appendixFileLoading: false,
      professionalType: "",
    };
  },
  mounted() {
    if (this.basicWorkOrderData.appendixFileUrl) {
      this.impactServiceArr = JSON.parse(
        this.basicWorkOrderData.appendixFileUrl
      );
    }
    if (this.basicWorkOrderData.appendix) {
      this.attachmentArr = JSON.parse(this.basicWorkOrderData.appendix);
    }
  },
  methods: {
    exportBasicWorkOrderInfo() {
      this.exportLoading = true;
      let param = {
        woId: this.woId,
        workItemId: this.workItemId,
      };
      apiExportWorkOrder(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("导出成功");
          } else {
            this.$message.error("导出失败");
          }
          this.exportLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("导出失败");
          this.exportLoading = false;
        });
    },
    queryImpactServiceList() {
      this.impactServiceVisible = true;
    },
    queryAttachmentList() {
      this.attachmentVisible = true;
    },
    //电缆附件下载
    downloadOpticCable() {
      this.OpticCableLoading = true;
      let param = {
        param1: JSON.stringify({
          woId: this.woId,
        }),
      };
      apifluenceExcel(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("文件下载成功");
          } else {
            this.$message.success("文件下载失败");
          }
          this.OpticCableLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("文件下载失败");
          this.OpticCableLoading = false;
        });
    },
    // downloadAppendixFile(data) {
    //   this.appendixFileLoading = true;
    //   let param = {
    //     attId: data.id,
    //   };
    //   apiDownloadAppendixFile(param)
    //     .then(res => {
    //       if (res.status == "0") {
    //         this.$message.success("文件下载成功");
    //       } else {
    //         this.$message.error("文件下载失败");
    //       }
    //       this.appendixFileLoading = false;
    //     })
    //     .catch(error => {
    //       console.log(error);
    //       this.$message.error("文件下载失败");
    //       this.appendixFileLoading = false;
    //     });
    // },
    downloadManualFile() {
      this.manualFileFullscreenLoading = true;
      let param = {
        param1: JSON.stringify({
          woId: this.woId,
        }),
      };
      apiDownloadManualFile(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("文件下载成功");
          } else {
            this.$message.success("文件下载失败");
          }
          this.manualFileFullscreenLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("文件下载失败");
          this.manualFileFullscreenLoading = false;
        });
    },
    stitchingAlgorithm(orgName, userName) {
      if (
        null != orgName &&
        orgName.length !== 0 &&
        null != userName &&
        userName.length !== 0
      ) {
        return orgName + "," + userName;
      } else {
        if (null != orgName && orgName.length !== 0) {
          return orgName;
        } else if (null != userName && userName.length !== 0) {
          return userName;
        } else {
          return "";
        }
      }
    },
    handleDownload(attId) {
      let param = {
        attId: attId,
        watermark: "", //水印
      };
      apiFileDownload(param)
        .then(res => {})
        .catch(err => {
          console.log(err);
        });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../assets/common.scss";
::v-deep .el-descriptions-item__content {
  position: relative;
  /*white-space: pre-wrap;*/
  word-break: break-all;
  padding-right: 8px;
}
::v-deep .el-descriptions-item__cell {
  vertical-align: top;
}
</style>
