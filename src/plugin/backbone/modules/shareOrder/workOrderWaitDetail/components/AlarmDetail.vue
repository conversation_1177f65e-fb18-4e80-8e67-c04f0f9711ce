<template>
  <el-card shadow="always" class="card" :body-style="{ padding: '10px 24px' }">
    <div class="header clearfix">
      <span class="header-title">故障处理列表</span>
      <div class="header-right">
        <el-button type="primary" size="mini" @click="getTableData()"
          >刷新</el-button
        >
<!--        >-->
      </div>
    </div>

    <div class="_el-table">
      <el-table
        :data="dealData"
        border
        stripe
        v-loading="tableLoading"
      >
<!--        @row-click="opentableList"-->
        <el-table-column
          prop="sheetType"
          label="工单类别"
          width="150"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="operType"
          label="操作类别"
          width="120"
        ></el-table-column>
        <el-table-column
          label="操作人姓名"
          prop="operName"
          width="130"
        ></el-table-column>
        <el-table-column
          label="操作人邮箱"
          prop="operEmail"
          width="150"
        ></el-table-column>
        <el-table-column
          label="操作人手机"
          prop="operTel"
          width="130"
        ></el-table-column>
        <el-table-column
          label="操作时间"
          prop="operTime"
          min-width="160"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="问题原因类别"
          prop="proCauseKind"
          min-width="160"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="解决方案"
          prop="solution"
          min-width="160"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="用户使用情况"
          prop="isRecover"
          min-width="160"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="故障恢复时间"
          prop="recoverTime"
          min-width="180"
          show-overflow-tooltip
        ></el-table-column>
      </el-table>
    </div>

  </el-card>
</template>

<script>

import { apiGetWorkOrderInfo } from "../api/CommonApi";

export default {
  name: "AlarmDetail",
  components: {
  },
  props: {
    sheetNo: String,
    dealData:Array,
  },
  data() {
    return {
      // tableData: [],
      tableLoading: false,
    };
  },
  mounted() {
  },
  methods: {

    //获取工单详情
    getTableData() {
      this.pageLoading = true;
      // 第三方打开时， url 中 获取 sheetNo 参数
      // const url = window.location.href;
      // const queryStri = url.substring(url.indexOf("?") + 1).split("&");
      // let sheetNoUrl = "";
      // queryStri.forEach(item => {
      //   if (item.indexOf("sheetNo") >= 0) {
      //     sheetNoUrl = decodeURI(item.split("=")[1]);
      //   }
      // });

      let param = {
        sheetNo: this.sheetNo,
        // processInstId: this.common.processInstId,
        // workItemId: this.common.workItemId,
        // woCode: sheetNoUrl,
      };

      apiGetWorkOrderInfo(param)
        .then(res => {
          if (res.status == "0") {

            let self = this;
            self.dealData = res?.data?.shareDealOrderInfos ?? [];

          }
        })
        .catch(error => {
          console.log(error);
        })
        .finally(() => {
          this.pageLoading = false;
        });
    },

    // 回车\r\n转为<br/>标签
    return2Br(str) {
      if (str) {
        return str.replaceAll("\\r\\n", "<br/>");
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../assets/common.scss";
._el-table {
  width: 100%;

}

.ellipsis {
  display: -webkit-box;
  overflow: hidden;
  white-space: normal !important;
  text-overflow: ellipsis;
  word-wrap: break-word;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.el-popover {
  max-height: 120px;
  overflow: auto;
}
::v-deep .el-descriptions-row {
  .is-bordered-label {
    width: 140px;
  }
  .tdOverflow {
    width: 260px;
    height: 50px;
    overflow-x: auto;
    // text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 50px;
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .tdOverflow::-webkit-scrollbar {
    display: none;
  }
}
</style>
