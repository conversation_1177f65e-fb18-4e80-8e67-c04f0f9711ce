.card {
  margin-bottom: 10px;
  &:last-child {
    margin-bottom: 0;
  }
}
.header {
  margin-bottom: 8px;
}
.header-title {
  font-size: 16px;
  letter-spacing: 0;
}
.header-right {
  float: right;
}
.content ::v-deep {
  @include themify() {
    border: 1px solid themed("$--border-color-light");
  }
  .el-divider {
    margin: 6px 0 12px;
  }
  .collapse-title {
    flex: 1 0 90%;
    order: 1;
  }
  .descriptions {
    .el-descriptions__header {
      height: 47px;
      padding-left: 24px;
      background: #fafafa;
      border-top: 1px solid #e9e9e9;
      border-bottom: 1px solid #e9e9e9;
      border-radius: 2px 2px 0 0;
      border-radius: 2px 2px 0px 0px;
    }
  }
  .el-collapse-item__header {
    flex: 1 0 auto;
    order: -1;
  }
}
.____content ::v-deep {
  margin: 0 40px;
  @include themify() {
    border: 1px solid themed("$--border-color-light");
  }
  .el-divider {
    margin: 6px 0 12px;
  }
  .collapse-title {
    flex: 1 0 90%;
    order: 1;
  }
  .descriptions {
    .el-descriptions__header {
      height: 47px;
      padding-left: 24px;
      background: #fafafa;
      border-top: 1px solid #e9e9e9;
      border-bottom: 1px solid #e9e9e9;
      border-radius: 2px 2px 0 0;
      border-radius: 2px 2px 0px 0px;
    }
  }
  .el-collapse-item__header {
    flex: 1 0 auto;
    order: -1;
  }
}
::v-deep .el-table__body-wrapper {
  .el-table__cell {
    .cell {
      display: flex;
      align-items: center;
      .hang__order {
        color: #b50b14;
        width: 20px;
        height: 20px;
        border: 1px solid #b50b14;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 4px;
      }
    }
  }
}

.descriptions ::v-deep {
  .el-descriptions__title {
    font-size: 14px;
    line-height: 22px;
    font-weight: unset;
  }
  .el-descriptions__body {
    padding: 0 2em;
  }
  .el-descriptions-item__label {
    white-space: nowrap;
  }
}
.sf_sheetType{
  background: #4B508A;
  border-color: #4B508A;
  border-radius: 2px;
  color: #fff;
}
.ggw_sheetType {
  background: #b98c6d;
  border-color: #b98c6d;
  border-radius: 2px;
  color: #fff;
}
.sheetNo_style {
  color: #409eff;
  user-select: unset;
}
.search-input-button {
  background: #b50b14;
  border-color: #b50b14;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.1);
  border-radius: 0 2px 2px 0;
}

.link_style {
  white-space: nowrap;
  text-overflow: ellipsis;
  color: #c43c43;
  // line-height: 28px;
}
.fileName_style {
  margin-right: 3px;
  vertical-align: middle;
  margin-bottom: 3px;
  cursor: pointer;
  div {
    display: inline-block;
    max-width: 140px;
    vertical-align: top;
  }
}
