<template>
  <el-dialog
    :title="title"
    :visible="visible"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="1510px"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="180px">
      <el-card
        hadow="never"
        :body-style="{ padding: '20px 8px 0px' }"
        class="cus-card"
      >
        <el-row type="flex" style="flex-wrap: wrap">
          <el-col :span="8">
            <el-form-item label="制定人:" prop="createUserName">
              <el-input
                :disabled="true"
                v-model="form.createUserName"
                style="width: 300px"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="制定人所属部门:" prop="createUserDept">
              <el-input
                :disabled="true"
                v-model="form.createUserDept"
                style="width: 290px"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="规则类型:"
              prop="ruleType"
              :rules="{
                required: true,
                message: '规则类型不可以为空！',
              }"
            >
              <el-input
                disabled
                v-model="form.ruleType"
                style="width: 280px"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="流程类型"
              prop="flowType"
              :rules="{
                required: true,
                message: '流程类型不可以为空！',
              }"
            >
              <dict-select
                :value.sync="form.flowType"
                :dictId="
                  form.ruleType == '集团规则' || form.ruleType == '大区规则'
                    ? 10203
                    : 10204
                "
                placeholder="请选择流程类型"
                style="width: 300px"
                @change="changeFlowType"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="专业:"
              prop="professionalType"
              :rules="{
                required: true,
                message: '专业不可以为空！',
              }"
            >
              <dict-select
                :value.sync="form.professionalType"
                :dictId="professionalDictId"
                placeholder="请选择专业"
                style="width: 290px"
                @change="changeProfessionalType"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="form.createUserDept == '中国联通总部'">
            <el-form-item
              label="省份:"
              prop="provinces"
              :rules="{
                required: true,
                message: '省份不可以为空',
              }"
            >
              <el-select
                v-model="form.provinces"
                placeholder="请选择省份"
                multiple
                collapse-tags
                style="width: 280px"
              >
                <el-option
                  v-for="(item, i) in dictData"
                  :key="i"
                  :label="item.dictName"
                  :value="item.dictCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="form.createUserDept != '中国联通总部'">
            <el-form-item
              label="省份:"
              prop="province"
              :rules="{
                required: true,
                message: '省份不可以为空',
              }"
            >
              <dict-select
                :notSelect="true"
                :value.sync="form.province"
                :dictId="form.ruleType == '大区规则' ? 10210 : 10211"
                placeholder="请选择省份"
                style="width: 280px"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="工单环节"
              prop="processNode"
              :rules="{
                required: true,
                message: '工单环节不可以为空！',
              }"
            >
              <dict-select
                :value.sync="form.processNode"
                :dictId="1100"
                placeholder="请选择工单环节！"
                @change="changeProcessNode()"
                style="width: 300px"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="催办权限人员:"
              prop="urgeTodoManPrivilegesName"
            >
              <!-- <el-input
                v-model="form.urgeTodoManPrivilegesName"
                placeholder="添加人员"
                style="width: 286px"
                readonly
              >
                <el-button
                  type="info"
                  slot="append"
                  icon="el-icon-user"
                  @click="onOpenPeopleDialog('urgentAuthorizedPersonnel')"
                ></el-button>
              </el-input> -->
              <el-input
                placeholder="请选择催办权限人员"
                v-model="form.urgeTodoManPrivilegesName"
                maxlength="0"
              >
                <el-tag
                  slot="prefix"
                  v-for="(tag, index) in form.urgeTodoManPrivilegeList"
                  :key="index"
                  closable="true"
                  style="margin-top: 5px"
                  @close="handleCloseAgen(tag)"
                  v-show="index < 1"
                >
                  {{ tag.name }}
                </el-tag>

                <el-popover
                  slot="prefix"
                  v-if="
                    form.urgeTodoManPrivilegeList != null &&
                    form.urgeTodoManPrivilegeList.length >= 2
                  "
                  width="500"
                  trigger="click"
                >
                  <el-input
                    v-model="urgeTodoManPrivilegeName"
                    placeholder="请输入催办权限人员姓名"
                  >
                    <el-button
                      type="info"
                      slot="append"
                      icon="el-icon-search"
                      @click="search('CloseAgen')"
                    >
                    </el-button>
                    <el-button
                      type="info"
                      slot="append"
                      icon="el-icon-close"
                      @click="clear('CloseAgen')"
                    >
                    </el-button>
                  </el-input>
                  <el-table
                    ref="multipleTable"
                    tooltip-effect="dark"
                    @selection-change="handleSelectionChange"
                    :data="urgeTodoManPrivilegeListCopy"
                    max-height="240"
                  >
                    <el-table-column width="30" type="selection">
                    </el-table-column>
                    <el-table-column
                      min-width="70"
                      property="name"
                      label="姓名"
                    >
                    </el-table-column>
                    <el-table-column
                      min-width="180"
                      property="orgName"
                      label="组织"
                    >
                    </el-table-column>
                    <el-table-column
                      min-width="120"
                      property="mobilePhone"
                      label="电话"
                    >
                    </el-table-column>
                    <el-table-column fixed="right" label="操作" width="50">
                      <template slot-scope="scope">
                        <el-button
                          type="text"
                          size="small"
                          @click.native.prevent="handleCloseAgen(scope.row)"
                        >
                          移除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <el-button
                    size="small"
                    type="text"
                    @click="toggleSelection('CloseAgen')"
                    >批量移除
                  </el-button>
                  <el-tag slot="reference" style="margin-top: 5px">
                    +{{ form.urgeTodoManPrivilegeList.length - 1 }}
                  </el-tag>
                </el-popover>

                <!-- <el-tag
                  slot="prefix"
                  v-if="form.urgeTodoManPrivilegeList.length >= 3"
                  >
                  +{{form.urgeTodoManPrivilegeList.length - 2}}
                </el-tag> -->
                <el-button
                  type="info"
                  slot="append"
                  @click="onOpenPeopleDialog('urgentAuthorizedPersonnel')"
                  icon="el-icon-user"
                >
                </el-button>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="紧急程度:"
              prop="emergencyLevel"
              :rules="{
                required: true,
                message: '紧急程度不可以为空！',
              }"
            >
              <el-checkbox-group
                v-model="form.emergencyLevel"
                style="width: 298px"
              >
                <el-checkbox label="一般" name="type"></el-checkbox>
                <el-checkbox label="次要" name="type"></el-checkbox>
                <el-checkbox label="重要" name="type"></el-checkbox>
                <el-checkbox label="紧急" name="type"></el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="预警开始时间:"
              prop="alertTimeStart"
              :rules="{
                required: true,
                message: '预警开始时间不可以为空！',
              }"
            >
              <el-time-picker
                v-model="form.alertTimeStart"
                value-format="HH:mm:ss"
                placeholder="请选择开始时间"
                style="width: 300px"
              >
              </el-time-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="预警结束时间:" prop="alertTimeEnd">
              <el-time-picker
                v-model="form.alertTimeEnd"
                value-format="HH:mm:ss"
                placeholder="请选择结束时间"
                style="width: 290px"
              >
              </el-time-picker>
            </el-form-item>
          </el-col>
          <template v-if="form.professionalType === '23'">
            <el-col :span="8">
              <el-form-item
                label="二级专业："
                prop="addedField"
                :rules="{
                  required: true,
                  message: '二级专业不可以为空！',
                }"
              >
                <dict-select
                  :value.sync="form.addedField"
                  :dictId="11002"
                  placeholder="请选择二级专业"
                  style="width: 286px"
                />
              </el-form-item>
            </el-col>
          </template>
        </el-row>
      </el-card>
      <el-card
        hadow="never"
        header="预警规则"
        :body-style="{ padding: '20px 8px 0px' }"
        style="margin-top: 20px"
      >
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="24">
            <el-form-item label="预警对象:">
              主送【
              <el-checkbox
                v-model="toMan"
                :disabled="form.processNode == '反馈环节'"
                >到人</el-checkbox
              >
              <el-checkbox
                v-model="toOrg"
                :disabled="form.processNode == '反馈环节'"
                >到组织</el-checkbox
              >
              】
              <el-checkbox
                v-if="
                  form.professionalType == '22' || form.professionalType == '23'
                "
                v-model="copyMan"
                :disabled="form.processNode == '反馈环节'"
                >抄送</el-checkbox
              >
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="第一次预警（分钟）:" prop="firstAccAlertTime">
              <el-input v-model="form.firstAccAlertTime"></el-input>
              <span class="item__unit">分钟</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="第二次预警（分钟）:" prop="secondAccAlertTime">
              <el-input
                :disabled="!form.firstAccAlertTime"
                v-model="form.secondAccAlertTime"
              ></el-input>
              <span class="item__unit">分钟</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="第三次预警（分钟）:" prop="thirdAccAlertTime">
              <el-input
                :disabled="!form.secondAccAlertTime"
                v-model="form.thirdAccAlertTime"
              ></el-input>
              <span class="item__unit">分钟</span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="预警内容:" prop="alertMsg">
              <el-input v-model="form.alertMsg" disabled />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card
        hadow="never"
        header="催办规则"
        :body-style="{ padding: '20px 8px 0px' }"
        style="margin-top: 20px"
      >
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item label="维护负责人:">
              <!-- <el-input
                v-model="form.firstUrgeUserName"
                placeholder="添加人员"
                style="width: 100%"
              >
                <el-button
                  type="info"
                  slot="append"
                  icon="el-icon-user"
                  @click="onOpenPeopleDialog('maintenanceManager')"
                >
                </el-button>
              </el-input> -->

              <el-input
                placeholder="请选择维护负责人"
                v-model="form.firstUrgeUserName"
                maxlength="0"
              >
                <el-tag
                  slot="prefix"
                  v-for="(tag, index) in form.firstUrgeUserList"
                  :key="index"
                  style="margin-top: 5px"
                  closable="true"
                  @close="handleFirstUrgeUser(tag)"
                  v-show="index < 1"
                >
                  {{ tag.name }}
                </el-tag>

                <el-popover
                  slot="prefix"
                  v-if="
                    form.firstUrgeUserList != null &&
                    form.firstUrgeUserList.length >= 2
                  "
                  placement="right"
                  width="500"
                  trigger="click"
                >
                  <el-input
                    v-model="firstUrgeUserName"
                    placeholder="请输入维护负责人姓名"
                  >
                    <el-button
                      type="info"
                      slot="append"
                      icon="el-icon-search"
                      @click="search('FirstUrgeUser')"
                    >
                    </el-button>
                    <el-button
                      type="info"
                      slot="append"
                      icon="el-icon-close"
                      @click="clear('FirstUrgeUser')"
                    >
                    </el-button>
                  </el-input>
                  <el-table
                    ref="multipleTable"
                    tooltip-effect="dark"
                    @selection-change="handleSelectionChange"
                    :data="firstUrgeUserListCopy"
                    style="width: 100%"
                    max-height="240"
                  >
                    <el-table-column width="30" type="selection">
                    </el-table-column>
                    <el-table-column
                      min-width="70"
                      property="name"
                      label="姓名"
                    >
                    </el-table-column>
                    <el-table-column
                      min-width="180"
                      property="orgName"
                      label="组织"
                    >
                    </el-table-column>
                    <el-table-column
                      min-width="120"
                      property="mobilePhone"
                      label="电话"
                    >
                    </el-table-column>
                    <el-table-column fixed="right" label="操作" width="50">
                      <template slot-scope="scope">
                        <el-button
                          type="text"
                          size="small"
                          @click.native.prevent="handleFirstUrgeUser(scope.row)"
                        >
                          移除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <el-button
                    size="small"
                    type="text"
                    @click="toggleSelection('FirstUrgeUser')"
                    >批量移除
                  </el-button>
                  <el-tag slot="reference" style="margin-top: 5px">
                    +{{ form.firstUrgeUserList.length - 1 }}
                  </el-tag>
                </el-popover>

                <el-button
                  type="info"
                  size="small"
                  slot="append"
                  @click="onOpenPeopleDialog('maintenanceManager')"
                  icon="el-icon-user"
                >
                </el-button>
              </el-input>

              <!-- <el-input v-model="form.firstUrgeUserName" placeholder="添加人员" style="width: 100%" readonly>
                    <el-button type="info" slot="append" icon="el-icon-user" @click="onOpenFirstUrge('ccDetermine')">
                    </el-button>
                  </el-input> -->
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否通知主送对象:">
              <el-radio-group
                v-model="form.firstUrgeIsSendMain"
                :disabled="this.firstDisabled"
              >
                <el-radio :label="0" @click.native="clickitem('0', 'first')"
                  >否</el-radio
                >
                <el-radio :label="1" @click.native="clickitem('1', 'first')"
                  >是</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="一级超时催办(分钟):"
              prop="firstUrgeTime"
              label-width="150px"
            >
              <el-input
                placeholder="请输入内容"
                v-model="form.firstUrgeTime"
                style="width: 100%"
                :disabled="this.firstDisabled"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="中心主任:">
              <!-- <el-input
                v-model="form.secondUrgeUserName"
                placeholder="添加人员"
                style="width: 100%"
              >
                <el-button
                  type="info"
                  slot="append"
                  icon="el-icon-user"
                  @click="onOpenPeopleDialog('directorOfTheCentre')"
                >
                </el-button>
              </el-input> -->

              <el-input
                v-model="form.secondUrgeUserName"
                placeholder="请选择中心主任"
                maxlength="0"
              >
                <el-tag
                  slot="prefix"
                  v-for="(tag, index) in form.secondUrgeUserList"
                  :key="index"
                  closable="true"
                  style="margin-top: 5px"
                  @close="handleSecondUrgeUser(tag)"
                  v-show="index < 1"
                >
                  {{ tag.name }}
                </el-tag>

                <el-popover
                  slot="prefix"
                  v-if="
                    form.secondUrgeUserList != null &&
                    form.secondUrgeUserList.length >= 2
                  "
                  placement="right"
                  width="500"
                  trigger="click"
                >
                  <el-input
                    v-model="secondUrgeUserName"
                    placeholder="请输入中心主任姓名"
                  >
                    <el-button
                      type="info"
                      slot="append"
                      icon="el-icon-search"
                      @click="search('SecondUrgeUser')"
                    >
                    </el-button>
                    <el-button
                      type="info"
                      slot="append"
                      icon="el-icon-close"
                      @click="clear('SecondUrgeUser')"
                    >
                    </el-button>
                  </el-input>
                  <el-table
                    ref="multipleTable"
                    tooltip-effect="dark"
                    @selection-change="handleSelectionChange"
                    :data="secondUrgeUserListCopy"
                    style="width: 100%"
                    max-height="240"
                  >
                    <el-table-column width="30" type="selection">
                    </el-table-column>
                    <el-table-column
                      min-width="70"
                      property="name"
                      label="姓名"
                    >
                    </el-table-column>
                    <el-table-column
                      min-width="180"
                      property="orgName"
                      label="组织"
                    >
                    </el-table-column>
                    <el-table-column
                      min-width="120"
                      property="mobilePhone"
                      label="电话"
                    >
                    </el-table-column>
                    <el-table-column fixed="right" label="操作" width="50">
                      <template slot-scope="scope">
                        <el-button
                          type="text"
                          size="small"
                          @click.native.prevent="
                            handleSecondUrgeUser(scope.row)
                          "
                        >
                          移除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <el-button
                    size="small"
                    type="text"
                    @click="toggleSelection('SecondUrgeUser')"
                    >批量移除
                  </el-button>
                  <el-tag slot="reference" style="margin-top: 5px">
                    +{{ form.secondUrgeUserList.length - 1 }}
                  </el-tag>
                </el-popover>

                <el-button
                  type="info"
                  slot="append"
                  icon="el-icon-user"
                  @click="onOpenPeopleDialog('directorOfTheCentre')"
                >
                </el-button>
              </el-input>

              <!-- <el-input v-model="form.secondUrgeUserName" placeholder="添加人员" style="width: 100%" readonly>
                    <el-button type="info" slot="append" icon="el-icon-user" @click="onOpenFirstUrge('ccDetermine')">
                    </el-button>
                  </el-input> -->
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否通知主送对象:">
              <el-radio-group
                v-model="form.secondUrgeIsSendMain"
                :disabled="this.secondDisabled"
              >
                <el-radio :label="0" @click.native="clickitem('0', 'second')"
                  >否</el-radio
                >
                <el-radio :label="1" @click.native="clickitem('1', 'second')"
                  >是</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="二级超时催办(分钟):"
              prop="secondUrgeTime"
              label-width="150px"
            >
              <el-input
                placeholder="请输入内容"
                v-model="form.secondUrgeTime"
                style="width: 100%"
                :disabled="this.secondDisabled"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="主管领导">
              <!-- <el-input
                v-model="form.thirdUrgeUserName"
                placeholder="添加人员"
                style="width: 100%"
              >
                <el-button
                  type="info"
                  slot="append"
                  icon="el-icon-user"
                  @click="onOpenPeopleDialog('competentLeadership')"
                >
                </el-button>
              </el-input> -->
              <el-input
                v-model="form.thirdUrgeUserName"
                placeholder="请选择主管领导"
                maxlength="0"
              >
                <el-tag
                  slot="prefix"
                  style="margin-top: 5px"
                  v-for="(tag, index) in form.thirdUrgeUserList"
                  :key="index"
                  closable="true"
                  @close="handleThirdUrgeUser(tag)"
                  v-show="index < 1"
                >
                  {{ tag.name }}
                </el-tag>

                <el-popover
                  slot="prefix"
                  v-if="
                    form.secondUrgeUserList != null &&
                    form.thirdUrgeUserList.length >= 2
                  "
                  placement="right"
                  width="500"
                  trigger="click"
                >
                  <!-- <div style="width: 460px; border-style:solid; border-width:1px;">
                    <el-tag
                    slot="prefix"
                    style="margin-top: 5px; margin-bottom: 5px;"
                    v-for="(tag,index) in form.thirdUrgeUserList"
                    :key="index"
                    closable="true"
                    @close="handleThirdUrgeUser(tag)"
                    >
                    {{tag.name}}
                  </el-tag>
                  </div> -->
                  <el-input
                    v-model="thirdUrgeUserName"
                    placeholder="请输入主管领导姓名"
                  >
                    <el-button
                      type="info"
                      slot="append"
                      icon="el-icon-search"
                      @click="search('ThirdUrgeUser')"
                    >
                    </el-button>
                    <el-button
                      type="info"
                      slot="append"
                      icon="el-icon-close"
                      @click="clear('ThirdUrgeUser')"
                    >
                    </el-button>
                  </el-input>
                  <el-table
                    ref="multipleTable"
                    tooltip-effect="dark"
                    @selection-change="handleSelectionChange"
                    :data="thirdUrgeUserListCopy"
                    max-height="240"
                  >
                    <el-table-column width="30" type="selection">
                    </el-table-column>
                    <el-table-column
                      min-width="70"
                      property="name"
                      label="姓名"
                    >
                    </el-table-column>
                    <el-table-column
                      min-width="180"
                      property="orgName"
                      label="组织"
                    >
                    </el-table-column>
                    <el-table-column
                      min-width="120"
                      property="mobilePhone"
                      label="电话"
                    >
                    </el-table-column>
                    <el-table-column fixed="right" label="操作" width="50">
                      <template slot-scope="scope">
                        <el-button
                          type="text"
                          size="small"
                          @click.native.prevent="handleThirdUrgeUser(scope.row)"
                        >
                          移除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <el-button
                    size="small"
                    type="text"
                    @click="toggleSelection('ThirdUrgeUser')"
                    >批量移除
                  </el-button>
                  <el-tag slot="reference" style="margin-top: 5px">
                    +{{ form.thirdUrgeUserList.length - 1 }}
                  </el-tag>
                </el-popover>

                <!-- <el-tag
                  slot="prefix"
                  v-if="form.thirdUrgeUserList.length >= 3"
                  >
                  +{{form.thirdUrgeUserList.length - 2}}
                </el-tag> -->
                <el-button
                  type="info"
                  slot="append"
                  icon="el-icon-user"
                  @click="onOpenPeopleDialog('competentLeadership')"
                >
                </el-button>
              </el-input>

              <!-- <el-input v-model="form.thirdUrgeUserName" placeholder="添加人员" style="width: 100%" readonly>
                    <el-button type="info" slot="append" icon="el-icon-user" @click="onOpenFirstUrge('ccDetermine')">
                    </el-button>
                  </el-input> -->
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否通知主送对象:">
              <el-radio-group
                v-model="form.thirdUrgeIsSendMain"
                :disabled="this.thirdDisabled"
              >
                <el-radio :label="0" @click.native="clickitem('0', 'third')"
                  >否</el-radio
                >
                <el-radio :label="1" @click.native="clickitem('1', 'third')"
                  >是</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="三级超时催办(分钟):"
              prop="thirdUrgeTime"
              label-width="150px"
            >
              <el-input
                placeholder="请输入内容"
                v-model="form.thirdUrgeTime"
                style="width: 100%"
                :disabled="this.thirdDisabled"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="催办内容:" prop="urgeMsg">
              <el-input v-model="form.urgeMsg" disabled style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="_save"> 确 定 </el-button>
      <el-button @click="_cancel()">取 消</el-button>
    </span>
    <dia-orgs-user-tree
      :title="diaPeople.title"
      :visible.sync="diaPeople.visible"
      :showOrgsTree="diaPeople.showOrgsTree"
      @on-save="onSavePeople"
      :appendToBody="diaPeople.appendToBody"
    />
  </el-dialog>
</template>

<script>
import { apiDict } from "../api/CommonApi";
import DiaOrgsUserTree from "../../workOrder/components/DiaOrgsUserTree.vue";
import DictSelect from "../../workOrder/components/DictSelect.vue";
import { apiAddRule, apiupdateRule } from "../api/WorkOrderVoiceremind";
import moment from "moment";
import Bus from "../../workOrder/bus";
export default {
  name: "RemindAdd",
  components: {
    DiaOrgsUserTree,
    DictSelect,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: "新增",
    },
  },

  data() {
    const checkAge = (rule, value, callback) => {
      if (isNaN(value)) {
        callback(new Error("请输入数字"));
      } else {
        callback();
      }
    };
    var validHappenTime = (rule, value, callback) => {
      if (value) {
        let seconds = moment(this.form.alertTimeStart, "HH:mm:ss").diff(
          moment(value, "HH:mm:ss"),
          "seconds"
        );
        if (seconds > 0) {
          callback(new Error("结束时间不能小于开始时间"));
        } else {
          callback();
        }
      }
    };
    return {
      toMan: "",
      toOrg: "",
      copyMan: "",
      multipleSelection: [],
      urgeTodoManPrivilegeListCopy: [],
      urgeTodoManPrivilegeName: "",
      firstUrgeUserListCopy: [],
      firstUrgeUserName: "",
      secondUrgeUserListCopy: [],
      secondUrgeUserName: "",
      thirdUrgeUserListCopy: [],
      thirdUrgeUserName: "",
      firstDisabled: true,
      secondDisabled: true,
      thirdDisabled: true,
      form: {
        createUserName: "", //规则指定人
        createUserDept: "",
        provinces: [],
        flowType: "",
        ruleId: "",
        province: "",
        processNode: "受理环节",
        professionalType: "",
        ruleType: "",
        emergencyLevel: [],
        alertTimeStart: "",
        alertTimeEnd: "",
        addedField: "",
        urgeTodoManPrivilegeList: [],
        urgeTodoManPrivilegesName: "",
        accAlertScope: [],
        firstAccAlertTime: "",
        secondAccAlertTime: "",
        thirdAccAlertTime: "",
        alertMsg:
          "【姓名】您好，你有一张故障工单【工单编号】待受理，已发送至待办列表，即将在【受理超时时间】超时，请尽快处理。",
        firstUrgeUserList: [],
        firstUrgeUserName: "",
        firstUrgeIsSendMain: null,
        firstUrgeTime: null,
        secondUrgeUserList: [],
        secondUrgeUserName: "",
        secondUrgeIsSendMain: null,
        secondUrgeTime: null,
        thirdUrgeUserList: [],
        thirdUrgeUserName: "",
        thirdUrgeIsSendMain: null,
        thirdUrgeTime: null,
        urgeMsg:
          "【姓名】您好，你部门有一张故障工单【工单编号】已超过【超时催办时间】分钟的受理超时催办时间，请督促执行人【主送人】尽快处理。",
      },
      dictData: [],
      professionalDictId: "10002",
      createUserId: JSON.parse(sessionStorage.userInfo).userName,
      diaPeople: {
        visible: false,
        title: "",
        saveName: "",
        appendToBody: true, //嵌套dialog
        saveTitleMap: {
          urgentAuthorizedPersonnel: "催办权限人员",
          maintenanceManager: "维护负责人",
          directorOfTheCentre: "中心主任",
          competentLeadership: "主管领导",
        },
        showOrgsTree: false,
        showOrgsTreeMap: {
          recipientDetermine: false,
        },
        showContactUserTab: false,
        showContactUserTabMap: {
          recipientDetermine: true,
        },
      },
      provinceDice: {
        notSelect: true,
        dictId: "",
      },
      rules: {
        firstAccAlertTime: [
          { validator: checkAge, trigger: "blur", required: true },
        ],
        secondAccAlertTime: [{ validator: checkAge, trigger: "blur" }],
        thirdAccAlertTime: [{ validator: checkAge, trigger: "blur" }],
        firstUrgeTime: [{ validator: checkAge, trigger: "blur" }],
        secondUrgeTime: [{ validator: checkAge, trigger: "blur" }],
        thirdUrgeTime: [{ validator: checkAge, trigger: "blur" }],
        alertTimeEnd: [
          { validator: validHappenTime, trigger: "blur", required: true },
        ],
      },
    };
  },
  watch: {
    "form.processNode": {
      handler(newV) {
        if (newV == "反馈环节") {
          this.toMan = "";
          this.toOrg = "";
          this.copyMan = "";
        }
        if (
          newV == "反馈环节" &&
          this.form.professionalType == 3 &&
          this.form.flowType == 1
        ) {
          this.$message({
            message: "传输网不可以创建反馈环节规则",
            type: "warning",
          });
          this.form.professionalType = "";
        }
        if (
          newV == "反馈环节" &&
          this.form.professionalType == 7 &&
          this.form.flowType == 9
        ) {
          this.$message({
            message: "无线网不可以创建反馈环节规则",
            type: "warning",
          });
          this.form.professionalType = "";
        }
      },
      deep: true,
    },
    "form.professionalType": {
      handler(newV) {
        if (this.title != "编辑") {
          this.form.addedField = "";
        }
        if (
          newV == 3 &&
          this.form.processNode == "反馈环节" &&
          this.form.flowType == 1
        ) {
          this.$message({
            message: "传输网不可以创建反馈环节规则",
            type: "warning",
          });
          this.form.processNode = "";
        }
        if (
          newV == 7 &&
          this.form.processNode == "反馈环节" &&
          this.form.flowType == 9
        ) {
          this.$message({
            message: "无线网不可以创建反馈环节规则",
            type: "warning",
          });
          this.form.processNode = "";
        }
      },
      deep: true,
    },
    "form.urgeTodoManPrivilegeList": {
      handler(newV) {
        if (newV != null && newV.length > 0) {
          this.form.urgeTodoManPrivilegesName = "已选";
        } else {
          this.form.urgeTodoManPrivilegesName = "";
        }
      },
      deep: true,
    },
    "form.firstUrgeUserList": {
      handler(newV) {
        if (newV != null && newV.length > 0) {
          this.form.firstUrgeUserName = "已选";
          this.firstDisabled = false;
        } else {
          this.form.firstUrgeUserName = "";
          this.form.firstUrgeIsSendMain = "";
          this.form.firstUrgeTime = "";
          this.firstDisabled = true;
        }
      },
      deep: true,
    },
    "form.secondUrgeUserList": {
      handler(newV) {
        if (newV != null && newV.length > 0) {
          this.form.secondUrgeUserName = "已选";
          this.secondDisabled = false;
        } else {
          this.form.secondUrgeUserName = "";
          this.form.secondUrgeIsSendMain = "";
          this.form.secondUrgeTime = "";
          this.secondDisabled = true;
        }
      },
      deep: true,
    },
    "form.thirdUrgeUserList": {
      handler(newV) {
        if (newV != null && newV.length > 0) {
          this.form.thirdUrgeUserName = "已选";
          this.thirdDisabled = false;
        } else {
          this.form.thirdUrgeUserName = "";
          this.form.thirdUrgeIsSendMain = "";
          this.form.thirdUrgeTime = "";
          this.thirdDisabled = true;
        }
      },
      deep: true,
    },
  },
  mounted() {
    if (sessionStorage.userInfo != null) {
      this.userData = JSON.parse(sessionStorage.userInfo);
      this.form.createUserName = this.userData.realName;
      if (this.userData.attr2 != null) {
        this.locationData = JSON.parse(this.userData.attr2);
        if (this.locationData.dqInfo != null) {
          if (this.locationData.dqInfo.orgType === "DAQU") {
            this.form.ruleType = "大区规则";
            this.form.province = this.locationData.dqInfo.orgCode + "";
            this.form.createUserDept = this.locationData.dqInfo.orgName;
          }
        } else {
          if (this.locationData.category === "UNI") {
            this.form.ruleType = "集团规则";
            this.form.createUserDept =
              this.locationData.orgInfo.fullOrgName.split("-")[0];
          } else {
            this.form.ruleType = "省分规则";
            this.form.province = this.locationData.orgInfo.proCode + "";
            this.form.createUserDept = this.locationData.orgInfo.orgName;
          }
        }

        // if(this.form.createUserDept == "中国联通总部"){
        //   this.form.ruleType = "集团规则";
        // }else{
        //   this.form.ruleType = "省分规则";
        //   this.form.province = this.locationData.orgInfo.proCode + "";
        // }
      } else {
        console.log("缺少用户信息");
      }
    }
    Bus.$on("editor", row => {
      this.setEditor(row);
    });
  },
  methods: {
    getDictData(value) {
      let param = {
        dictTypeCode: value,
      };
      apiDict(param)
        .then(res => {
          if (res.code == 200) {
            this.dictData = res?.data ?? [];
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    changeFlowType(type) {
      if (type != "edit") {
        this.form.professionalType = "";
        this.form.provinces = [];
      }
      if (this.form.flowType == "4") {
        this.professionalDictId = "60012";
      } else if (this.form.flowType == "1") {
        this.professionalDictId = "10206";
      } else if (this.form.flowType == "2") {
        this.professionalDictId = "10207";
      } else if (this.form.flowType == "3") {
        this.professionalDictId = "10208";
      } else if (this.form.flowType == "7" || this.form.flowType == "9") {
        this.professionalDictId = "10209";
      } else if (this.form.flowType == "10") {
        this.professionalDictId = "60009";
      } else if (this.form.flowType == "11") {
        this.professionalDictId = "60010";
      } else if (this.form.flowType == "12") {
        this.professionalDictId = "60011";
      } else if (this.form.flowType == "8") {
        this.professionalDictId = "60012";
      } else if (this.form.flowType == "" || this.form.flowType == null) {
        this.professionalDictId = "10002";
      }
    },
    changeProfessionalType(type) {
      if (type != "edit") {
        this.form.provinces = [];
        this.form.copyman = "";
      }

      if (this.form.createUserDept == "中国联通总部") {
        //集团IT云专业“省份”字段为“联通系统集成有限公司”
        if ( this.form.professionalType == "23"){
          this.dictData = [{ dictCode: "-1", dictName: "联通系统集成有限公司" }];
        } else if (this.form.professionalType == "22") {
          //集团通信云专业“省份”字段为8+1个大区
          this.getDictData("10210");
        } else if (
          // 集团5GC(20)、IP承载A网(12)、IP承载B网(13)、VIMS(19)、平台(25)专业，“省份”字段为31个省+9个大区
          this.form.professionalType == "19" ||
          this.form.professionalType == "20" ||
          this.form.professionalType == "12" ||
          this.form.professionalType == "13" ||
          this.form.professionalType == "25") {
            this.getDictData("10212")
        } else {
          //集团其他专业“省份”字段为31个省
          this.getDictData("10211")
        }
      }
      // if (
      //   this.form.createUserDept == "中国联通总部" && this.form.professionalType == "22"
      // ) {
      //   this.getDictData("10210");
      // } else if (
      //   this.form.createUserDept == "中国联通总部" &&
      //   this.form.professionalType == "23"
      // ) {
      //   this.dictData = [{ dictCode: "-1", dictName: "联通系统集成有限公司" }];
      // } else if(this.form.createUserDept == "中国联通总部" &&
      // (this.form.professionalType == "19" ||
      //     this.form.professionalType == "20" ||
      //     this.form.professionalType == "12" ||
      //     this.form.professionalType == "13" ||
      //     this.form.professionalType == "25") ) {
      //       this.getDictData("10212");
      // } else if (this.form.createUserDept == "中国联通总部") {
      //   this.getDictData("10211");
      // }
    },
    // json串处理
    getListNames(UserArr) {
      let userNames = "";
      if (UserArr.length > 0) {
        UserArr.map(item => {
          userNames += item.name + ",";
        });
        return userNames.substring(0, userNames.length - 1);
      }
      return userNames;
    },
    //编辑打开弹窗
    setEditor(row) {
      let r = { ...row };
      r.professionalType = r.professionalType + "";
      r.emergencyLevel = this.setEmergencyLevel(r.emergencyLevel);
      if (r.urgeTodoManPrivilegeList != null) {
        r.urgeTodoManPrivilegesName = this.getListNames(
          r.urgeTodoManPrivilegeList
        );
        this.urgeTodoManPrivilegeListCopy = JSON.parse(
          JSON.stringify(r.urgeTodoManPrivilegeList)
        );
      }
      if (r.urgeTodoManPrivilegeList == null) {
        r.urgeTodoManPrivilegeList = [];
      }
      if (r.firstUrgeUserList != null) {
        r.firstUrgeUserName = this.getListNames(r.firstUrgeUserList);
        this.firstUrgeUserListCopy = JSON.parse(
          JSON.stringify(r.firstUrgeUserList)
        );
      }
      if (r.firstUrgeUserList == null) {
        r.firstUrgeUserList = [];
      }
      if (r.secondUrgeUserList != null) {
        r.secondUrgeUserName = this.getListNames(r.secondUrgeUserList);
        this.secondUrgeUserListCopy = JSON.parse(
          JSON.stringify(r.secondUrgeUserList)
        );
      }
      if (r.secondUrgeUserList == null) {
        r.secondUrgeUserList = [];
      }
      if (r.thirdUrgeUserList != null) {
        r.thirdUrgeUserName = this.getListNames(r.thirdUrgeUserList);
        this.thirdUrgeUserListCopy = JSON.parse(
          JSON.stringify(r.thirdUrgeUserList)
        );
      }
      if (r.thirdUrgeUserList == null) {
        r.thirdUrgeUserList = [];
      }
      this.setAccAlertScope(r.accAlertScope);
      this.form = { ...r };
      this.changeFlowType("edit");
      this.changeProfessionalType("edit");
      this.$emit("update:title", "编辑");
      this.$emit("update:visible", true);
    },
    //x号关闭
    handleClose() {
      this.$refs.form.resetFields();
      this.form.urgeTodoManPrivilegeList = [];
      this.multipleSelection = [];
      this.form.firstUrgeUserList = [];
      this.form.secondUrgeUserList = [];
      this.form.thirdUrgeUserList = [];
      this.form.peopleItems = [];
      this.toMan = "";
      this.toOrg = "";
      this.copyMan = "";
      this.empty();
      this.$emit("update:visible", false);
    },
    //表单提交
    _save() {
      if (this.form.ruleType == "") {
        this.$message({
          message: "请选择规则类型",
          type: "warning",
        });
        return;
      }
      if (this.form.flowType == "") {
        this.$message({
          message: "请选择流程类型",
          type: "warning",
        });
        return;
      }
      if (this.form.professionalType == "") {
        this.$message({
          message: "请选择专业",
          type: "warning",
        });
        return;
      }
      if (this.form.province == "" && this.form.provinces.length == 0) {
        this.$message({
          message: "请选择省份",
          type: "warning",
        });
        return;
      }
      if (this.form.processNode == "") {
        this.$message({
          message: "请选择工单环节",
          type: "warning",
        });
        return;
      }
      if (this.form.emergencyLevel == "") {
        this.$message({
          message: "请选择紧急程度",
          type: "warning",
        });
        return;
      }
      if (
        this.form.alertTimeStart === "" ||
        this.form.alertTimeStart === null
      ) {
        this.$message({
          message: "请选择预警开始时间",
          type: "warning",
        });
        return;
      }
      if (this.form.alertTimeEnd === "" || this.form.alertTimeEnd === null) {
        this.$message({
          message: "请选择预警结束时间",
          type: "warning",
        });
        return;
      }
      if (
        moment(this.form.alertTimeStart, "HH:mm:ss").diff(
          moment(this.form.alertTimeEnd, "HH:mm:ss"),
          "seconds"
        ) > 0
      ) {
        this.$message({
          message: "结束时间不能小于开始时间",
          type: "warning",
        });
        return;
      }
      if (this.form.professionalType == "23") {
        if (this.form.addedField == "") {
          this.$message({
            message: "请选择二级专业",
            type: "warning",
          });
          return;
        }
      }
      if (this.form.processNode == "受理环节") {
        if (this.toMan == "" && this.toOrg == "") {
          this.$message({
            message: "预警对象中的主送不可以为空",
            type: "warning",
          });
          return;
        }
      }
      if (this.form.firstAccAlertTime == "") {
        this.$message({
          message: "第一次预警时间不可以为空",
          type: "warning",
        });
        return;
      } else if (isNaN(this.form.firstAccAlertTime)) {
        this.$message({
          message: "第一次预警时间必须为数字",
          type: "warning",
        });
        return;
      }
      if (
        this.form.secondAccAlertTime != "" &&
        isNaN(this.form.secondAccAlertTime)
      ) {
        this.$message({
          message: "第二次预警时间必须为数字",
          type: "warning",
        });
        return;
      }
      if (
        this.form.thirdAccAlertTime != "" &&
        isNaN(this.form.thirdAccAlertTime)
      ) {
        this.$message({
          message: "第三次预警时间必须为数字",
          type: "warning",
        });
        return;
      }
      if (
        this.form.firstUrgeUserList != null &&
        this.form.firstUrgeUserList.length > 0
      ) {
        if (
          this.form.firstUrgeIsSendMain === null ||
          this.form.firstUrgeIsSendMain === ""
        ) {
          this.$message({
            message: "请选择是否通知主送对象",
            type: "warning",
          });
          return;
        }
        if (
          this.form.firstUrgeTime === null ||
          this.form.firstUrgeTime === ""
        ) {
          this.$message({
            message: "请输入一级超时催办",
            type: "warning",
          });
          return;
        } else if (isNaN(this.form.firstUrgeTime)) {
          this.$message({
            message: "一级超时催办必须为数字",
            type: "warning",
          });
          return;
        }
      }
      if (
        this.form.secondUrgeUserList != null &&
        this.form.secondUrgeUserList.length > 0
      ) {
        if (
          this.form.secondUrgeIsSendMain === null ||
          this.form.secondUrgeIsSendMain === ""
        ) {
          this.$message({
            message: "请选择是否通知主送对象",
            type: "warning",
          });
          return;
        }
        if (
          this.form.secondUrgeTime === null ||
          this.form.secondUrgeTime === ""
        ) {
          this.$message({
            message: "请输入二级超时催办",
            type: "warning",
          });
          return;
        } else if (isNaN(this.form.secondUrgeTime)) {
          this.$message({
            message: "二级超时催办必须为数字",
            type: "warning",
          });
          return;
        }
      }
      if (
        this.form.thirdUrgeUserList != null &&
        this.form.thirdUrgeUserList.length > 0
      ) {
        if (
          this.form.thirdUrgeIsSendMain === null ||
          this.form.thirdUrgeIsSendMain === ""
        ) {
          this.$message({
            message: "请选择是否通知主送对象",
            type: "warning",
          });
          return;
        }
        if (
          this.form.thirdUrgeTime === null ||
          this.form.thirdUrgeTime === ""
        ) {
          this.$message({
            message: "请输入三级超时催办",
            type: "warning",
          });
          return;
        } else if (isNaN(this.form.thirdUrgeTime)) {
          this.$message({
            message: "三级超时催办必须为数字",
            type: "warning",
          });
          return;
        }
      }
      // this.form.accAlertScope = [];
      let alertScope = [];
      if (this.toMan != "") {
        alertScope.push(1);
      }
      if (this.toOrg != "") {
        alertScope.push(2);
      }
      if (this.copyMan != "") {
        alertScope.push(3);
      }
      if (alertScope != null && alertScope.length > 0) {
        this.form.accAlertScope = alertScope;
      }
      if (this.form.provinces.length == 0) {
        this.form.provinces.push(this.form.province);
      }
      let seniorParam = {
        provinces: this.form.provinces,
        flowType: this.form.flowType,
        createUserName: this.form.createUserName,
        createUserDept: this.form.createUserDept,
        province: this.form.province,
        processNode: this.form.processNode,
        professionalType: this.form.professionalType,
        addedField: this.form.addedField,
        ruleType: this.form.ruleType,
        emergencyLevel: this.setEmergencyLevel(this.form.emergencyLevel),
        alertTimeStart: this.form.alertTimeStart,
        alertTimeEnd: this.form.alertTimeEnd,
        urgeTodoManPrivileges: this.form.urgeTodoManPrivilegeList,
        accAlertScope: this.form.accAlertScope,
        firstAccAlertTime: this.form.firstAccAlertTime,
        secondAccAlertTime: this.form.secondAccAlertTime,
        thirdAccAlertTime: this.form.thirdAccAlertTime,
        alertMsg: this.form.alertMsg,
        firstUrgeUser: this.form.firstUrgeUserList,
        firstUrgeIsSendMain: this.form.firstUrgeIsSendMain,
        firstUrgeTime: this.form.firstUrgeTime,
        secondUrgeUser: this.form.secondUrgeUserList,
        secondUrgeIsSendMain: this.form.secondUrgeIsSendMain,
        secondUrgeTime: this.form.secondUrgeTime,
        thirdUrgeUser: this.form.thirdUrgeUserList,
        thirdUrgeIsSendMain: this.form.thirdUrgeIsSendMain,
        thirdUrgeTime: this.form.thirdUrgeTime,
        urgeMsg: this.form.urgeMsg,
      };
      if (this.title == "编辑") {
        seniorParam.ruleId = this.form.ruleId;
      }
      let param = {
        param1: JSON.stringify(seniorParam),
      };
      if (this.title == "新增") {
        apiAddRule(param)
          .then(res => {
            if (res.status == "0") {
              this.$emit("update:visible", false);
              this.$refs["form"].resetFields();
              this.empty();
              this.form.peopleItems = [];
              this.$message({
                message: "添加成功",
                type: "success",
              });
              Bus.$emit("getList");
            } else if (res.status == "200") {
              this.$message.warning("此规则已存在");
            } else {
              this.$message.error("添加失败");
            }
          })
          .catch(error => {
            if (error.status == "500") {
              this.$message.error(error.msg);
            } else {
              this.$message.error("添加失败");
            }
          });
      } else {
        apiupdateRule(param).then(res => {
          if (res.status == "0") {
            this.$emit("update:visible", false);
            this.$refs.form.resetFields();
            this.empty();
            this.form.peopleItems = [];
            Bus.$emit("getList");
            this.$message({
              message: "修改成功",
              type: "success",
            });
          } else if (res.status == "200") {
            this.$message.warning("此规则已存在");
          } else {
            this.$message.error("修改失败");
          }
        });
      }
    },
    setEmergencyLevel(arr) {
      let l = [];
      if (Array.isArray(arr)) {
        arr.forEach(el => {
          if (el == "一般") {
            l.push(0);
          }
          if (el == "次要") {
            l.push(1);
          }
          if (el == "重要") {
            l.push(2);
          }
          if (el == "紧急") {
            l.push(3);
          }
          if (el == 0) {
            l.push("一般");
          }
          if (el == 1) {
            l.push("次要");
          }
          if (el == 2) {
            l.push("重要");
          }
          if (el == 3) {
            l.push("紧急");
          }
        });
      }
      return l;
    },
    setAccAlertScope(arr) {
      if (Array.isArray(arr)) {
        arr.forEach(el => {
          if (el == 1) {
            this.toMan = true;
          }
          if (el == 2) {
            this.toOrg = true;
          }
          if (el == 3) {
            this.copyMan = true;
          }
        });
      }
    },
    //取消
    _cancel() {
      this.$refs.form.resetFields();
      this.toMan = "";
      this.toOrg = "";
      this.copyMan = "";
      (this.multipleSelection = []), (this.form.urgeTodoManPrivilegeList = []);
      this.form.peopleItems = [];
      this.form.firstUrgeUserList = [];
      this.form.secondUrgeUserList = [];
      this.form.thirdUrgeUserList = [];
      this.empty();
      this.$emit("update:visible", false);
    },
    //多选移除
    toggleSelection(val) {
      if (this.multipleSelection == 0) {
        this.$message({
          type: "warning",
          message: "还没有选择移除的选项，请先选择要移除的选项！",
        });
        return;
      } else {
        this.multipleSelection.forEach(row => {
          if (val == "CloseAgen") {
            this.handleCloseAgen(row);
          }
          if (val == "FirstUrgeUser") {
            this.handleFirstUrgeUser(row);
          }
          if (val == "SecondUrgeUser") {
            this.handleSecondUrgeUser(row);
          }
          if (val == "ThirdUrgeUser") {
            this.handleThirdUrgeUser(row);
          }
        });
      }
      this.multipleSelection;
    },
    //移除催办人员
    handleCloseAgen(tag) {
      this.form.urgeTodoManPrivilegeList.splice(
        this.arrayIndex(this.form.urgeTodoManPrivilegeList, tag),
        1
      );
      this.urgeTodoManPrivilegeListCopy.splice(
        this.arrayIndex(this.urgeTodoManPrivilegeListCopy, tag),
        1
      );
    },
    //移除维护负责人
    handleFirstUrgeUser(tag) {
      this.form.firstUrgeUserList.splice(
        this.arrayIndex(this.form.firstUrgeUserList, tag),
        1
      );
      this.firstUrgeUserListCopy.splice(
        this.arrayIndex(this.firstUrgeUserListCopy, tag),
        1
      );
    },
    //移除中心主任
    handleSecondUrgeUser(tag) {
      this.form.secondUrgeUserList.splice(
        this.arrayIndex(this.form.secondUrgeUserList, tag),
        1
      );
      this.secondUrgeUserListCopy.splice(
        this.arrayIndex(this.secondUrgeUserListCopy, tag),
        1
      );
    },
    //移除主管领导
    handleThirdUrgeUser(tag) {
      this.form.thirdUrgeUserList.splice(
        this.arrayIndex(this.form.thirdUrgeUserList, tag),
        1
      );
      this.thirdUrgeUserListCopy.splice(
        this.arrayIndex(this.thirdUrgeUserListCopy, tag),
        1
      );
    },

    //找对应对象的索引值
    arrayIndex(val1, val2) {
      for (var i = 0, len = val1.length; i < len; i++) {
        var tag = val1[i];
        if (tag.id == val2.id) {
          return i;
        }
      }
    },
    //搜索
    search(val) {
      if (val == "CloseAgen" && this.urgeTodoManPrivilegeName != null) {
        this.urgeTodoManPrivilegeListCopy = [];
        this.form.urgeTodoManPrivilegeList.forEach(row => {
          if (row.name == this.urgeTodoManPrivilegeName) {
            this.urgeTodoManPrivilegeListCopy.push(row);
          }
        });
      }
      if (val == "FirstUrgeUser" && this.firstUrgeUserName != null) {
        this.firstUrgeUserListCopy = [];
        this.form.firstUrgeUserList.forEach(row => {
          if (row.name == this.firstUrgeUserName) {
            this.firstUrgeUserListCopy.push(row);
          }
        });
      }
      if (val == "SecondUrgeUser" && this.secondUrgeUserName != null) {
        this.secondUrgeUserListCopy = [];
        this.form.secondUrgeUserList.forEach(row => {
          if (row.name == this.secondUrgeUserName) {
            this.secondUrgeUserListCopy.push(row);
          }
        });
      }
      if (val == "ThirdUrgeUser" && this.thirdUrgeUserName != null) {
        this.thirdUrgeUserListCopy = [];
        this.form.thirdUrgeUserList.forEach(row => {
          if (row.name == this.thirdUrgeUserName) {
            this.thirdUrgeUserListCopy.push(row);
          }
        });
      }
    },
    //清除搜索项
    clear(val) {
      if (val == "CloseAgen") {
        this.urgeTodoManPrivilegeName = "";
        this.urgeTodoManPrivilegeListCopy = JSON.parse(
          JSON.stringify(this.form.urgeTodoManPrivilegeList)
        );
      }
      if (val == "FirstUrgeUser") {
        this.firstUrgeUserName = "";
        this.firstUrgeUserListCopy = JSON.parse(
          JSON.stringify(this.form.firstUrgeUserList)
        );
      }
      if (val == "SecondUrgeUser") {
        this.secondUrgeUserName = "";
        this.secondUrgeUserListCopy = JSON.parse(
          JSON.stringify(this.form.secondUrgeUserList)
        );
      }
      if (val == "ThirdUrgeUser") {
        this.thirdUrgeUserName = "";
        this.thirdUrgeUserListCopy = JSON.parse(
          JSON.stringify(this.form.thirdUrgeUserList)
        );
      }
    },
    //人员树总开关
    onOpenPeopleDialog(diaSaveName) {
      this.diaPeople.title = this.diaPeople.saveTitleMap[diaSaveName];
      this.diaPeople.saveName = diaSaveName;
      // this.diaPeople.showOrgsTree = this.diaPeople.showOrgsTreeMap[diaSaveName] ?? true;
      // this.diaPeople.showContactUserTab = this.diaPeople.showContactUserTabMap[diaSaveName] ?? false;
      this.diaPeople.visible = true;
    },

    onSavePeople(val) {
      this[this.diaPeople.saveName](val);
    },

    //一键催办权限人员确定
    urgentAuthorizedPersonnel({ usersChecked, selectionUser }) {
      if (selectionUser && selectionUser.length > 0) {
        selectionUser.map(item => {
          let zs = this.form.urgeTodoManPrivilegeList.findIndex(val => {
            return val.id === item.userName;
          });
          if (zs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.form.urgeTodoManPrivilegeList.push({
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      } else {
        //let urgesCheckedIdName = [];
        usersChecked.map(item => {
          let zs = this.form.urgeTodoManPrivilegeList.findIndex(val => {
            return val.id === item.id;
          });
          if (zs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.form.urgeTodoManPrivilegeList.push({
              id: item.id,
              name: item.name,
              orgName: item.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      this.urgeTodoManPrivilegeListCopy = JSON.parse(
        JSON.stringify(this.form.urgeTodoManPrivilegeList)
      );
    },
    //维护负责人确定
    maintenanceManager({ usersChecked, selectionUser }) {
      if (selectionUser && selectionUser.length > 0) {
        selectionUser.map(item => {
          let zs = this.form.firstUrgeUserList.findIndex(val => {
            return val.id === item.userName;
          });
          if (zs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.form.firstUrgeUserList.push({
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      } else {
        //let urgesCheckedIdName = [];
        usersChecked.map(item => {
          let zs = this.form.firstUrgeUserList.findIndex(val => {
            return val.id === item.id;
          });
          if (zs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.form.firstUrgeUserList.push({
              id: item.id,
              name: item.name,
              orgName: item.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      this.firstUrgeUserListCopy = JSON.parse(
        JSON.stringify(this.form.firstUrgeUserList)
      );
    },
    //中心主任确定
    directorOfTheCentre({ usersChecked, selectionUser }) {
      if (selectionUser && selectionUser.length > 0) {
        selectionUser.map(item => {
          let zs = this.form.secondUrgeUserList.findIndex(val => {
            return val.id === item.userName;
          });
          if (zs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.form.secondUrgeUserList.push({
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      } else {
        //let urgesCheckedIdName = [];
        usersChecked.map(item => {
          let zs = this.form.secondUrgeUserList.findIndex(val => {
            return val.id === item.id;
          });
          if (zs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.form.secondUrgeUserList.push({
              id: item.id,
              name: item.name,
              orgName: item.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      this.secondUrgeUserListCopy = JSON.parse(
        JSON.stringify(this.form.secondUrgeUserList)
      );
    },
    //主管领导确认
    competentLeadership({ usersChecked, selectionUser }) {
      if (selectionUser && selectionUser.length > 0) {
        selectionUser.map(item => {
          let zs = this.form.thirdUrgeUserList.findIndex(val => {
            return val.id === item.userName;
          });
          if (zs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.form.thirdUrgeUserList.push({
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      } else {
        //let urgesCheckedIdName = [];
        usersChecked.map(item => {
          let zs = this.form.thirdUrgeUserList.findIndex(val => {
            return val.id === item.id;
          });
          if (zs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.form.thirdUrgeUserList.push({
              id: item.id,
              name: item.name,
              orgName: item.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      this.thirdUrgeUserListCopy = JSON.parse(
        JSON.stringify(this.form.thirdUrgeUserList)
      );
    },
    changeProcessNode() {
      if (this.form.processNode === "受理环节") {
        this.form.alertMsg =
          "【姓名】您好，你有一张故障工单【工单编号】待受理，已发送至待办列表，即将在【受理超时时间】超时，请尽快处理。";
        this.form.urgeMsg =
          "【姓名】您好，你部门有一张故障工单【工单编号】已超过【超时催办时间】分钟的受理超时催办时间，请督促执行人【主送人】尽快处理。";
      } else if (this.form.processNode === "反馈环节") {
        this.form.alertMsg =
          "【姓名】您好，你有一张故障工单【工单编号】待反馈，已发送至待办列表，即将在【反馈超时时间】超时，请尽快处理。";
        this.form.urgeMsg =
          "【姓名】您好，你部门有一张故障工单【工单编号】已超过【超时催办时间】分钟的反馈超时催办时间，请督促执行人【主送人】尽快处理。";
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    empty() {
      this.toMan = "";
      this.toOrg = "";
      this.copyMan = "";
      this.urgeTodoManPrivilegeListCopy = [];
      this.urgeTodoManPrivilegeName = [];
      this.firstUrgeUserListCopy = [];
      this.firstUrgeUserName = "";
      this.secondUrgeUserListCopy = [];
      this.secondUrgeUserName = "";
      this.thirdUrgeUserListCopy = [];
      this.thirdUrgeUserName = "";
      this.form.processNode = "受理环节";
      this.form.professionalType = "";
      this.form.emergencyLevel = [];
      this.form.alertTimeStart = "";
      this.form.alertTimeEnd = "";
      this.form.addedField = "";
      this.form.urgeTodoManPrivilegeList = [];
      this.form.urgeTodoManPrivilegesName = null;
      this.form.accAlertScope = null;
      this.form.firstAccAlertTime = "";
      this.form.secondAccAlertTime = "";
      this.form.thirdAccAlertTime = "";
      this.form.firstUrgeUserList = [];
      this.form.firstUrgeUserName = null;
      this.form.firstUrgeIsSendMain = null;
      this.form.firstUrgeTime = null;
      this.form.secondUrgeUserList = [];
      this.form.secondUrgeUserName = null;
      this.form.secondUrgeIsSendMain = null;
      this.form.secondUrgeTime = null;
      this.form.thirdUrgeUserList = [];
      this.form.thirdUrgeUserName = null;
      this.form.thirdUrgeIsSendMain = null;
      this.form.thirdUrgeTime = null;
      this.dictData = [];
      this.professionalDictId = "10002";
      this.changeFlowType();
      this.changeProfessionalType();
    },
    clickitem(val1, val2) {
      if (this.flag === true) {
        if (val2 == "first") {
          val1 == this.form.firstUrgeIsSendMain
            ? this.$set(this.form, "firstUrgeIsSendMain", "")
            : (this.form.firstUrgeIsSendMain = val1);
        }
        if (val2 == "second") {
          val1 == this.form.secondUrgeIsSendMain
            ? this.$set(this.form, "secondUrgeIsSendMain", "")
            : this.$set(this.form, "secondUrgeIsSendMain", val1) + "";
        }
        if (val2 == "third") {
          val1 == this.form.thirdUrgeIsSendMain
            ? this.$set(this.form, "thirdUrgeIsSendMain", "")
            : this.$set(this.form, "thirdUrgeIsSendMain", val1) + "";
        }
      }
      this.flag = true;
      setTimeout(() => {
        this.flag = false;
      }, 300);
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-dialog {
  .elTagBox {
    display: flex;
    flex-wrap: wrap;
    .el-tag {
      margin-bottom: 10px;
    }
  }
  .alertContent {
    width: 340px;
    border: 1px solid #ebeef5;
    cursor: not-allowed;
    padding: 10px;
    span {
      font-size: 12px;
      color: #909399;
      margin-bottom: 10px;
    }
  }
}
</style>
