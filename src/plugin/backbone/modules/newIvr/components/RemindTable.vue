<template>
  <div style="height: 93%">
    <el-table
      ref="treetable"
      :data="tableData"
      :header-cell-style="{ textAlign: 'center' }"
      :cell-style="{ textAlign: 'center' }"
      :border="false"
      stripe
      @selection-change="onSelectionChange"
      height="100%"
      v-loading="tableLoading"
    >
      <el-table-column
        type="selection"
        width="50"
        :index="indexMethod"
        :selectable="selectDisableRoom"
      />
      <el-table-column prop="createUserName" label="规则制定人" width="90" />
      <el-table-column
        prop="createUserDept"
        label="制定人所属组织"
        width="180"
      />
      <el-table-column prop="ruleType" label="规则类型" width="90" />
      <el-table-column prop="flowType" label="流程类型" width="90" />
      <el-table-column prop="professionalType" label="专业" width="90" />
      <el-table-column
        prop="province"
        label="省份"
        width="90"
        show-overflow-tooltip
      />
      <el-table-column prop="addedField" label="二级专业" width="90" />
      <el-table-column prop="processNode" label="工单环节" width="90" />
      <el-table-column prop="emergencyLevel" label="紧急程度" width="90" />
      <el-table-column prop="alertTimeStart" label="预警开始时间" width="120" />
      <el-table-column prop="alertTimeEnd" label="预警结束时间" width="120" />
      <el-table-column prop="ruleStatus" label="规则状态" width="100">
        <template slot-scope="scope">
          <el-tag
            v-if="scope.row.ruleStatus == '启用'"
            type="success"
            effect="dark"
          >
            {{ scope.row.ruleStatus }}
          </el-tag>
          <el-tag
            v-if="scope.row.ruleStatus == '禁用'"
            type="info"
            effect="dark"
          >
            {{ scope.row.ruleStatus }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="firstAccAlertTime"
        label="第一次预警(分钟)"
        width="100"
      />
      <el-table-column
        prop="secondAccAlertTime"
        label="第二次预警(分钟)"
        width="100"
      />
      <el-table-column
        prop="thirdAccAlertTime"
        label="第三次预警(分钟)"
        width="100"
      />
      <el-table-column prop="accAlertScope" label="预警对象" width="200" />
      <el-table-column
        prop="urgeTodoManPrivileges"
        label="一键催办权限人员"
        width="140"
      >
        <template slot-scope="scope">
          {{ listToStr(scope.row.urgeTodoManPrivileges) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="firtUrgeTime"
        label="一级超时催办(分钟)"
        width="120"
      />
      <el-table-column
        prop="firstUrgeIsSendMain"
        label="一级超时是否通知主送对象"
        width="120"
      >
        <template slot-scope="scope">
          <span v-if="scope.row.firstUrgeIsSendMain == 0">否 </span>
          <span v-else-if="scope.row.firstUrgeIsSendMain == 1">是</span>
        </template>
      </el-table-column>
      <el-table-column prop="firstUrgeUser" label="维护负责人" width="120">
        <template slot-scope="scope">
          {{ listToStr(scope.row.firstUrgeUser) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="secondUrgeTime"
        label="二级超时催办(分钟)"
        width="120"
      />
      <el-table-column
        prop="secondUrgeIsSendMain"
        label="二级超时是否通知主送对象"
        width="120"
      >
        <template slot-scope="scope">
          <span v-if="scope.row.secondUrgeIsSendMain == 0">否</span>
          <span v-else-if="scope.row.secondUrgeIsSendMain == 1">是</span>
        </template>
      </el-table-column>
      <el-table-column prop="secondUrgeUser" label="中心主任" width="120">
        <template slot-scope="scope">
          {{ listToStr(scope.row.secondUrgeUser) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="thirdUrgeTime"
        label="三级超时催办(分钟)"
        width="120"
      />
      <el-table-column
        prop="thirdUrgeIsSendMain"
        label="三级超时是否通知主送对象"
        width="120"
      >
        <template slot-scope="scope">
          <span v-if="scope.row.thirdUrgeIsSendMain == 0">否</span>
          <span v-else-if="scope.row.thirdUrgeIsSendMain == 1">是</span>
        </template>
      </el-table-column>
      <el-table-column prop="thirdUrgeUser" label="主管领导" width="120">
        <template slot-scope="scope">
          {{ listToStr(scope.row.thirdUrgeUser) }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="150">
        <template slot-scope="scope">
          <el-button
            @click="clickEditor(scope.row)"
            type="text"
            size="small"
            :disabled="scope.row.createUserId == createUserId ? false : true"
            >编辑</el-button
          >
          <!-- <el-popconfirm title="确定删除这条规则么?" @confirm="clickDelete(scope.row)"> -->
          <el-button
            @click="clickDelete(scope.row)"
            type="text"
            size="small"
            slot="reference"
            style="margin-left: 10px"
            :disabled="scope.row.createUserId == createUserId ? false : true"
            >删除</el-button
          >
          <!-- </el-popconfirm> -->
        </template>
      </el-table-column>
    </el-table>
    <pagination
      ref="pagination"
      :total="form.total"
      :page.sync="form.pageNum"
      :limit.sync="form.pageSize"
      @change="seniorQuery"
    />
  </div>
</template>

<script>
import Pagination from "./Pagination.vue";
import {
  apiQueryRule,
  apiQueryRuleByRuleId,
  apiDeleteRule,
  apiupdateRuleStatus,
} from "../api/WorkOrderVoiceremind";
import Bus from "../../workOrder/bus";
export default {
  name: "RemindTable",
  components: {
    Pagination,
  },
  props: {
    professionalType: String,
    flowType: String,
    province: String,
    // province: {
    //   type: String,
    //   default: "",
    // },
    ruleType: {
      type: String,
      default: "集团规则",
    },
  },
  data() {
    return {
      province: null,
      tableLoading: false,
      form: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      createUserId: JSON.parse(sessionStorage.userInfo).userName,
      tableData: [],
      sclectList: [],
    };
  },
  mounted() {
    if (sessionStorage.userInfo != null) {
      this.userData = JSON.parse(sessionStorage.userInfo);
      if (this.userData.attr2 != null) {
        this.locationData = JSON.parse(this.userData.attr2);
        // if (this.locationData.category === "UNI") {
        //   this.province = "集团总部";
        // } else {
        //   this.province = "集团总部," + this.locationData.provinceName;
        // }
      } else {
        console.log("缺少用户信息");
      }
    }
    this.getTablelListData();
    Bus.$on("getList", () => {
      this.getTablelListData();
    });
  },
  methods: {
    //求情数据
    getTablelListData() {
      this.tableLoading = true;
      let seniorParam = {
        professionalType: this.professionalType,
        province: this.province, //省份
        flowType: this.flowType,
      };
      let param = {
        pageIndex: this.form.pageNum,
        pageSize: this.form.pageSize,
        param1: JSON.stringify(seniorParam),
      };
      apiQueryRule(param)
        .then(res => {
          if (res.status == "0") {
            this.tableLoading = false;
            let r = res?.data?.rows ?? [];
            this.form.total = res.data.totalElements || 0;
            r.forEach(el => {
              if (el.urgeTodoManPrivileges != "") {
                el.urgeTodoManPrivileges = JSON.parse(el.urgeTodoManPrivileges);
              }
              if (el.firstUrgeUser != "") {
                el.firstUrgeUser = JSON.parse(el.firstUrgeUser);
              }
              if (el.secondUrgeUser != "") {
                el.secondUrgeUser = JSON.parse(el.secondUrgeUser);
              }
              if (el.thirdUrgeUser != "") {
                el.thirdUrgeUser = JSON.parse(el.thirdUrgeUser);
              }
            });
            this.tableData = r;
            this.$nextTick(() => this.$refs.treetable.doLayout());
          } else {
            this.tableLoading = false;
          }
        })
        .catch(err => {
          this.tableLoading = false;
          console.log(err);
        });
    },
    //编辑弹出diglog
    clickEditor(scope) {
      let param = {
        ruleId: scope.ruleId,
      };
      apiQueryRuleByRuleId(param).then(res => {
        if (res.status == "0") {
          let rowData = res.data || {};
          Bus.$emit("editor", rowData);
        }
      });
    },
    //删除
    clickDelete(row) {
      this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let r = [];
          r[0] = row.ruleId;
          this.deldetRule(r);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    handlecreateType(item) {
      return this.typeList[item - 1];
    },
    //分页器
    seniorQuery() {
      this.getTablelListData();
    },
    selectDisableRoom(row) {
      if (row.createUserId == this.createUserId) {
        return true;
      } else {
        return false;
      }
    },
    //序号选择处理
    onSelectionChange(row) {
      if (row.length == 0) {
        this.sclectList = [];
      } else {
        let s = [];
        row.forEach(el => {
          s.push(el.ruleId);
        });
        this.sclectList = s;
      }
    },
    //批量删除
    batchDelete() {
      if (this.sclectList == 0) {
        this.$message({
          type: "warning",
          message: "请先选择规则",
        });
        return;
      }
      this.$confirm("确定要进行批量删除操作?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.deldetRule(this.sclectList);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    //启用禁用接口
    gettRemindSatus(stats) {
      if (this.sclectList.length == 0) {
        this.$message({
          type: "warning",
          message: "请先选择规则",
        });
        return;
      }
      let msg = stats == 0 ? "禁用" : "启用";
      let p = [];
      let r = [...this.sclectList];
      r.forEach(el => {
        p.push({
          ruleId: el,
          ruleStatus: stats,
        });
      });
      apiupdateRuleStatus(p).then(res => {
        if (res.status == "0") {
          this.$message({
            message: msg + "规则成功",
            type: "success",
          });
          this.getTablelListData();
        } else {
          this.$message.error(msg + "规则失败");
        }
      });
    },
    //删除接口
    deldetRule(r) {
      let param1 = {
        ruleIds: r,
      };
      let param = {
        param1: JSON.stringify(param1),
      };
      apiDeleteRule(param).then(res => {
        if (res.status == "0") {
          this.$message({
            message: "删除成功",
            type: "success",
          });
          this.getTablelListData();
        } else {
          this.$message.error("删除失败");
        }
      });
    },
    //选择框
    indexMethod(index) {
      const currentPage = this.form.pageNum;
      const pageSize = this.form.pageSize;
      return (currentPage - 1) * pageSize + (index + 1);
    },
    //受理/返单对象处理
    getAlertScope(key) {
      if (key.length) {
        return "主送,抄送";
      } else if (key == "1") {
        return "主送";
      } else if (key == "2") {
        return "抄送";
      } else {
        return "";
      }
    },
    listToStr(arr) {
      let list = [];
      if (arr != undefined && arr.length > 0) {
        arr.map(item => {
          list.push(item.name);
        });
        return list.join(",");
      }
      return "";
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../assets/ivrCommon.scss";
</style>
