<template>
  <el-select
    v-model="selectVal"
    :placeholder="placeholder"
    :disabled="notSelect"
    clearable
    filterable
    @change="onChange"
  >
    <el-option
      v-for="(item, i) in dictData"
      :key="i"
      :label="item.dictName"
      :value="item.dictCode"
    >
    </el-option>
  </el-select>
</template>

<script>
import { apiDict } from "../api/CommonApi";
export default {
  name: "DictSelect",
  props: {
    value: {
      type: [Number, String],
    },
    dictId: [Number, String],
    placeholder: String,
    notSelect: {
      default: false,
    },
    dictType: [Number, String],
    dictModule: [Number, String],
  },
  data() {
    return {
      selectVal: this.value,
      dictData: [],
    };
  },
  watch: {
    dictId(newValue) {
      if (newValue) this.getDictData();
    },
    dictType(newValue) {
      if (newValue) this.getDictData();
    },
    dictModule(newValue) {
      if (newValue) this.getDictData();
    },
    value(newValue) {
      this.selectVal = newValue;
      // this.$emit("update:value", this.selectVal);
    },
    notSelect(newValue) {
      this.notSelect = newValue || false;
    },
    deep: true,
    immediate: true,
  },
  mounted() {
    if (this.dictId) this.getDictData();
  },
  methods: {
    getDictData() {
      let param = {
        dictTypeCode: this.dictId,
      };
      if (this.dictType) {
        param.dictType = this.dictType;
      }
      if (this.dictModule) {
        param.dictModule = this.dictModule;
      }
      apiDict(param)
        .then(res => {
          if (res.code == 200) {
            this.dictData = res?.data ?? [];
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    onChange() {
      this.$emit("update:value", this.selectVal);
      this.$emit("change", this.selectVal);
    },
  },
};
</script>
