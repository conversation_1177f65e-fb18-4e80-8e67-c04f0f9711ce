<template>
  <div class="pagination-container">
    <el-pagination
      :current-page.sync="currentPage"
      :page-size.sync="currentPageSize"
      :page-sizes="[5, 10, 20]"
      :total="total"
      :layout="layout"
      background
      :pager-count="7"
      v-bind="$attrs"
      class="pagination"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    >
    </el-pagination>
  </div>
</template>

<script>
export default {
  name: "CustomPagination",
  props: {
    total: {
      required: true,
      type: Number,
    },
    page: {
      type: Number,
      default: 1,
    },
    limit: {
      type: Number,
      default: 10,
    },
    layout: {
      type: String,
      default: "->, total,sizes, prev, pager, next, jumper",
    },
  },
  computed: {
    currentPage: {
      get() {
        return this.page;
      },
      set(val) {
        this.$emit("update:page", val);
      },
    },
    currentPageSize: {
      get() {
        return this.limit;
      },
      set(val) {
        this.$emit("update:limit", val);
      },
    },
  },
  methods: {
    handleSizeChange(val) {
      this.currentPage = 1;
      this.$emit("change", { page: this.currentPage, limit: val });
    },
    handleCurrentChange(val) {
      this.$emit("change", { page: val, limit: this.currentPageSize });
    },
  },
};
</script>

<style scoped>
.pagination-container {
  margin-top: 10px;
  padding: 0;
}
.pagination-container ::v-deep .el-pagination {
  height: 40px;
  padding: 4px 0;
}
</style>
