import { postJson, getJson } from "@/utils/axios";
const queryRuleUrl = "/backbone/workflow/ivr/faultIvrRule/queryIvrRuleList"; //查询
const addRuleUrl = "/backbone/workflow/ivr/faultIvrRule/addIvrRule"; //添加
const deleteRUle = "/backbone/workflow/ivr/faultIvrRule/deleteRules"; //删除
const queryRuleByRuleIdUrl =
  "/backbone/workflow/ivr/faultIvrRule/queryIvrRuleByRuleId"; //编辑
const updateRuleUrl = "/backbone/workflow/ivr/faultIvrRule/updateIvrRule"; //编辑保存 updateRuleStatus
const updateRuleStatusUrl =
  "/backbone/workflow/ivr/faultIvrRule/modifyIvrRuleStatusBatch"; //启用 ,禁用

const apiQueryRule = params => postJson(queryRuleUrl, params);
const apiAddRule = params => postJson(addRuleUrl, params);
const apiDeleteRule = params => postJson(deleteRUle, params);
const apiQueryRuleByRuleId = params => getJson(queryRuleByRuleIdUrl, params);
const apiupdateRule = params => postJson(updateRuleUrl, params);
const apiupdateRuleStatus = params => postJson(updateRuleStatusUrl, params);
export {
  apiQueryRule,
  apiAddRule,
  apiDeleteRule,
  apiQueryRuleByRuleId,
  apiupdateRule,
  apiupdateRuleStatus,
};
