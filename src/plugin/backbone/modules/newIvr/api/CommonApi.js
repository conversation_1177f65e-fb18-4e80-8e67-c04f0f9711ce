import { getJson, postJson } from "@/utils/axios";
const dictUrl = "commonDict/enum/list"; //公共下拉接口地址
const orgInfoUrl = "backbone/info/orgInfo"; //当前用户的信息地址
const orgTreeLazyUrl = "backbone/tree/orgTreeNew"; // 组织树的懒加载接口地址  新接口
const userTreeUrlNew = "backbone/tree/userTreeNew"; //用户树的查询 新接口
const userFilterUrl = "backbone/tree/userFilter"; //用户树过滤的接口 通用
const redployUserTreeUrl = "backbone/tree/searchRedeployUser";
const redployOrgTreeUrl = "backbone/tree/searchRedeployOrg";
const contactUserUrl = "backbone/tree/contactUser";
const saveContactUserUrl = "backbone/tree/saveContactUser";
const proOrCityUrl = "backbone/info/woListProOrCity";
const regionInfoUrl = "backbone/info/regionInfoByAreaCode";
const userInfoUrl = "backbone/info/userInfoByUserNames";

const apiDict = params => getJson(dictUrl, params);
const apiGetOrgInfo = params => getJson(orgInfoUrl, params);
const apiGetOrgLazyTree = params =>
  getJson(orgTreeLazyUrl, params, { timeout: 0 });
const apiGetUserTreeNew = params =>
  getJson(userTreeUrlNew, params, { timeout: 0 });
const apiRedeployUserTree = params =>
  getJson(redployUserTreeUrl, params, { timeout: 0 });
const apiRedeployOrgTree = params =>
  getJson(redployOrgTreeUrl, params, { timeout: 0 });
const apiUserFilter = params => postJson(userFilterUrl, params, { timeout: 0 });
const apiGetContactUser = params =>
  postJson(contactUserUrl, params, { timeout: 0 });
const apiSaveContactUser = params =>
  postJson(saveContactUserUrl, params, { timeout: 0 });
const apiGetWoListProOrCity = params =>
  getJson(proOrCityUrl, params, { timeout: 0 });
const apiGetRegionInfo = params =>
  getJson(regionInfoUrl, params, { timeout: 0 });
const apiUserInfoByUserNames = params =>
  postJson(userInfoUrl, params, { timeout: 0 });

// 时间戳 ，转化成 日期时间格式。如果是当前时间，传参 Date.now()
function getCurrentTime(timeVal) {
  const time = new Date(timeVal);
  const yy = time.getFullYear();
  const mm =
    time.getMonth() + 1 < 10
      ? "0" + (time.getMonth() + 1)
      : time.getMonth() + 1;
  const dd = time.getDate() < 10 ? "0" + time.getDate() : time.getDate();
  const hh = time.getHours() < 10 ? "0" + time.getHours() : time.getHours();
  const mf =
    time.getMinutes() < 10 ? "0" + time.getMinutes() : time.getMinutes();
  const ss =
    time.getSeconds() < 10 ? "0" + time.getSeconds() : time.getSeconds();
  const gettime = yy + "-" + mm + "-" + dd + " " + hh + ":" + mf + ":" + ss;
  return gettime;
}
export {
  apiDict,
  apiGetOrgInfo,
  apiGetOrgLazyTree,
  apiGetUserTreeNew,
  apiUserFilter,
  apiRedeployUserTree,
  apiRedeployOrgTree,
  apiGetContactUser,
  apiSaveContactUser,
  getCurrentTime,
  apiGetWoListProOrCity,
  apiGetRegionInfo,
  apiUserInfoByUserNames,
};
