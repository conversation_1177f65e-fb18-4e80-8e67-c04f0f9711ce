<template>
  <div style="padding: 0">
    <head-content-layout class="page-wrap">
      <template #header>
        <el-row style="display: flex">
          <el-col :xs="24" :sm="12" :md="14" :lg="18" style="display: flex">
            <div>
              <div style="display: inline-block; margin-left: 5px">流程类型：</div>
              <dict-select
                :value.sync="seachData.flowType"
                :dictId="10202"
                placeholder="请选择流程类型"
                style="width: 240px"
                @change="changeFlowType"
              />
            </div>
            <div>
              <div style="display: inline-block; margin-left: 20px">专业：</div>
              <dict-select
                :value.sync="seachData.professionalType"
                :dictId="professionalDictId"
                placeholder="请选择专业"
                style="width: 270px"
                @change="changeProfessionalType"
              />
            </div>
            <div>
              <div style="display: inline-block; margin-left: 20px">省份：</div>
              <el-select
                v-model="seachData.province"
                placeholder="请选择省份"
                style="width: 280px"
                clearable
              >
                <el-option
                  v-for="(item, i) in dictData"
                  :key="i"
                  :label="item.dictName"
                  :value="item.dictCode"
                >
                </el-option>
              </el-select>
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :md="10" :lg="6" style="text-align: right">
            <div>
              <el-button type="primary" @click="seniorQuery">查询</el-button>
              <el-button @click="onResetForm">重置</el-button>
            </div>
          </el-col>
        </el-row>
        <div class="order__menuList">
          <el-button type="primary" @click="createSeup">+ 新建</el-button>
          <el-button @click="batchDelete">批量删除</el-button>
          <el-button @click="handlebatch('启用')">启用</el-button>
          <el-button @click="handlebatch('禁用')">禁用</el-button>
        </div>
      </template>
      <template #table>
        <remind-table
          :professionalType="seachData.professionalType"
          :province="seachData.province"
          :flowType="seachData.flowType"
          ref="remindTable"
        />
      </template>
      <template #dialog>
          <remind-add :title.sync="title" :visible.sync="visible" />
      </template>
    </head-content-layout>

  </div>
</template>

<script>
import { apiDict } from "./api/CommonApi";
import HeadContentLayout from "./components/HeadContentLayout.vue";
import DictSelect from "./components/DictSelect.vue";
import RemindTable from "./components/RemindTable.vue";
import RemindAdd from "./components/RemindAdd.vue";
export default {
  name: "WorkOrderVoiceremind",
  components: {
    HeadContentLayout,
    DictSelect,
    RemindTable,
    RemindAdd,
  },
  data() {
    return {
      //查询条件
      seachData: {
        professionalType: "", //专业
        province: "", //省份
        ruleType: "", //规则类型
        flowType: "",//流程类型
      },
      professionalDictId: 10002,
      provinceId: 10211,
      dictData: [],
      //新增,编辑
      visible: false,
      title: "新增",
    };
  },
  mounted() {
    this.getDictData("10211");
  },
  methods: {
    //查询
    seniorQuery() {
      this.$refs.remindTable.getTablelListData();
    },
    changeFlowType(){
      this.seachData.professionalType = "";
      this.seachData.province = "";
      if(this.seachData.flowType == '4' ){
        this.professionalDictId = '10205';
      } else if (this.seachData.flowType == '1'){
        this.professionalDictId = '10206';
      } else if (this.seachData.flowType == '2'){
        this.professionalDictId = '10207';
      } else if (this.seachData.flowType == '3'){
        this.professionalDictId = '10208';
      } else if (this.seachData.flowType == '7'){
        this.professionalDictId = '10209';
      } else if (this.seachData.flowType == '8'){
        this.professionalDictId = '60012';
      } else if (this.seachData.flowType == '10'){
        this.professionalDictId = '60009';
      } else if (this.seachData.flowType == '11'){
        this.professionalDictId = '60010';
      } else if (this.seachData.flowType == '12'){
        this.professionalDictId = '60011';
      } else if (this.seachData.flowType == ''){
        this.professionalDictId = '10002';
      }
      this.changeProfessionalType();
    },
    changeProfessionalType(){
      this.seachData.province = "";
      if( this.seachData.professionalType == '22' ){
         //集团通信云专业“省份”字段为8+1个大区
        this.getDictData("10210");
      } else if(this.seachData.professionalType == '23'){
         //集团IT云专业“省份”字段为“联通系统集成有限公司”
        this.dictData = [{'dictCode': '-1','dictName': '联通系统集成有限公司'}];
      } else if(
        this.seachData.professionalType == '19' ||
        this.seachData.professionalType == '20' ||
        this.seachData.professionalType == '12' ||
        this.seachData.professionalType == '13' ||
        this.seachData.professionalType == '25'
      ) {
        // 集团5GC(20)、IP承载A网(12)、IP承载B网(13)、VIMS(19)、平台(25)专业，“省份”字段为31个省+9个大区
        this.getDictData("10212");
      } else {
        //集团其他专业“省份”字段为31个省
        this.getDictData("10211");
      }
    },
    getDictData(value) {
      let param = {
        dictTypeCode: value,
      };
      apiDict(param)
        .then(res => {
          if (res.code == 200) {
            this.dictData = res?.data ?? [];
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    //重置
    onResetForm() {
      this.seachData = {
        professionalType: "", //专业
        province: "", //省份
        ruleType: "", //规则类型
        flowType: "",//流程类型
      };
    },
    //新增
    createSeup() {
      this.visible = true;
      this.title = "新增";
    },
    //批量删除
    batchDelete() {
      this.$refs.remindTable.batchDelete();
    },
    //启用禁用
    handlebatch(type) {
      if (type == "启用") {
        this.$refs.remindTable.gettRemindSatus(1);
      } else {
        this.$refs.remindTable.gettRemindSatus(0);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "./assets/ivrCommon.scss";
.order__menuList {
  margin-top: 10px;
  padding-bottom: 10px;
}
::v-deep .el-dialog__body {
  padding: 0;
  padding-right: 50px;
  padding-left: 30px;
}
::v-deep .el-form-item__content {
  position: relative;
  .item__unit {
    position: absolute;
    display: block;
    top: 0;
    left: 106px;
    color: #dcdfe6;
  }
}
::v-deep .el-dialog__wrapper {
  .el-dialog {
    margin-top: 6vh !important;
    .el-dialog__body {
      .el-form {
        .el-form-item,
        .el-form-item--mini {
          margin: 0;
          margin-bottom: 14px;
        }
      }
    }
  }
}
</style>
