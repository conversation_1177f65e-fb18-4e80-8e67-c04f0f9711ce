<template>
  <head-fixed-layout class="draft-eidt-page">
    <template #header>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="15" :md="18" class="head-title"
          >省分通用故障工单拟稿</el-col
        >
        <el-col :xs="24" :sm="9" :md="6" class="head-handle-wrap">
          <el-button
            type="primary"
            @click="onSheetSave"
            v-loading.fullscreen.lock="sheetSaveLoading"
            >保存</el-button
          >
          <el-button
            type="primary"
            @click="onSheetSubmit"
            v-loading.fullscreen.lock="sheetCommitLoading"
            >提交</el-button
          >
        </el-col>
      </el-row>
    </template>

    <el-form
      :model="sheetForm"
      ref="sheetForm"
      :rules="sheetFormRules"
      label-width="130px"
      label-position="right"
    >
      <el-card shadow="always" :body-style="{ padding: '10px' }">
        <div slot="header" class="card-title">
          <span>工单基本信息</span>
        </div>
        <el-row :gutter="10" type="flex" style="flex-wrap: wrap">
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="建单时间:" prop="sheetCreateTime">
              <el-input v-model="sheetForm.sheetCreateTime" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="建单人:" prop="senderName">
              <el-input
                v-model="sheetForm.senderName"
                placeholder="请输入建人"
                readonly
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="建单人部门:" prop="senderDeptName">
              <el-input
                v-model="sheetForm.senderDeptName"
                placeholder="请输入建单部门"
                readonly
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="16" :md="16" :offset="0">
            <el-form-item label="工单主题:" prop="sheetTitle">
              <el-input
                v-model="sheetForm.sheetTitle"
                placeholder="请输入内容"
                clearable
                style="width: 100%"
                maxlength="400"
                show-word-limit
                @keyup.native="descTip(400,'sheetTitle','showgdzt')"
              >
              </el-input>
              <div class="el-form-item__error"  v-if="showgdzt">已超过填写字数上限</div>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="10" :md="8" :offset="0">
            <el-form-item label="工单来源:" required>
              <el-select
                v-model="sheetForm.createType"
                placeholder="请选择内容"
                style="width: 100%"
                disabled
              >
                <el-option
                  v-for="(item, i) in createTypeList"
                  :key="i"
                  :label="item.dictName"
                  :value="item.dictCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="发生时间:" prop="alarmCreateTime">
              <el-date-picker
                v-model="sheetForm.alarmCreateTime"
                type="datetime"
                placeholder="请选择时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
                style="width: 100%"
                :picker-options="pickerOptions"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="紧急程度:" prop="emergencyLevel" required>
              <el-radio-group v-model="sheetForm.emergencyLevel">
                <el-radio
                  v-for="item in urgentOption"
                  :key="item.label"
                  :label="item.label"
                  >{{ item.value }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="预估处理时限(小时):" prop="processTimeLimit" class="specialWid">
              <el-input
                v-model="sheetForm.processTimeLimit"
                placeholder="请输入内容"
                clearable
                style="width: 100%"
                oninput="value=value.replace(/^(0+)|[^\d]+/g,'')"
                @blur="sheetForm.processTimeLimit = $event.target.value"
                maxlength="10"
                @keyup.native="descTip(10,'processTimeLimit','showygclsx')"
              >
              </el-input>
              <div class="el-form-item__error"  v-if="showygclsx">已超过填写字数上限</div>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item
              label="受理时限(分钟):"
              prop="acceptTimeLimit"
              required
            >
              <el-input
                v-model="sheetForm.acceptTimeLimit"
                placeholder="请输入受理时限"
                clearable
                style="width: 100%"
                maxlength="10"
                @keyup.native="descTip(10,'acceptTimeLimit','showslsx')"
              >
              </el-input>
              <div class="el-form-item__error"  v-if="showslsx">已超过填写字数上限</div>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="所属专业:" prop="professionalType" required>
              <el-select
                v-model="sheetForm.professionalType"
                placeholder="请选择"
                style="width: 100%"
                @change="professionalTypeChange"
              >
                <el-option
                  v-for="(item, i) in professionalTypeList"
                  :key="i"
                  :label="item.dictName"
                  :value="item.dictCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="网络类型:" prop="networkType">
              <el-select
                v-model="sheetForm.networkType"
                placeholder="请选择"
                style="width: 100%"
                @focus="
                  getDictData(
                    sheetForm.professionalType + '_comonpro_network_type',
                    'networkTypeList'
                  )
                "
              >
                <el-option
                  v-for="(item, i) in networkTypeList"
                  :key="i"
                  :label="item.dictName"
                  :value="item.dictCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="告警类别:" prop="orgType">
              <el-select style="width: 100%" v-model="sheetForm.orgType">
                <el-option
                  v-for="(item, i) in faultRegionOptions"
                  :key="i"
                  :label="item.dictName"
                  :value="item.dictCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="业务中断:" prop="businessDown">
              <el-radio-group v-model="sheetForm.businessDown">
                <el-radio :label="'1'">是</el-radio>
                <el-radio :label="'0'">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="告警地市:" prop="cityName" required>
              <el-select
                style="width: 100%" v-model="sheetForm.cityName"
                clearable
                placeholder="请选择"
                @change="alarmRegionChange"
              >
                <el-option
                  v-for="(item, i) in alarmRegionOptions"
                  :key="i"
                  :label="item"
                  :value="item"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="告警区县:" prop="regionName" required>
              <el-select
                style="width: 100%"
                v-model="sheetForm.regionName"
                clearable
                placeholder="请选择"
                :disabled="sheetForm.cityName == ''"
              >
                <el-option
                  v-for="(item, i) in alarmCityOptions"
                  :key="i"
                  :label="item"
                  :value="item"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="工单优先级:" prop="faultLevel" required>
              <el-select
                v-model="sheetForm.faultLevel"
                placeholder="请选择"
                style="width: 100%"
              >
                <el-option
                  v-for="(item, i) in woPriorityList"
                  :key="i"
                  :label="item.dictName"
                  :value="item.dictCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0" v-if="sheetForm.professionalType == 7">
            <el-form-item label="覆盖场景:" prop="coverScene">
              <el-select
                v-model="sheetForm.coverScene"
                filterable
                placeholder="请选择"
                style="width: 100%"
              >
                <el-option
                  v-for="(item, i) in coverSceneList"
                  :key="i"
                  :label="item.dictName"
                  :value="item.dictCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
<!--          省分云24-->
          <template v-if="sheetForm.professionalType == 24">
            <el-col :xs="24" :sm="8" :md="8" :offset="0">
              <el-form-item label="云池类型:" prop="cloudPoolType">
                <el-input
                  v-model="sheetForm.cloudPoolType"
                  placeholder="请输入内容"
                  style="width: 100%"
                  maxlength="100"
                  @keyup.native="descTip(100,'cloudPoolType','showyclx')"
                ></el-input>
                <div class="el-form-item__error"  v-if="showyclx">已超过填写字数上限</div>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :offset="0">
              <el-form-item label="业务名称:" prop="busName">
                <el-input
                  v-model="sheetForm.busName"
                  placeholder="请输入内容"
                  style="width: 100%"
                  maxlength="100"
                  @keyup.native="descTip(100,'busName','showywmc')"
                ></el-input>
                <div class="el-form-item__error"  v-if="showywmc">已超过填写字数上限</div>
              </el-form-item>
            </el-col>
          </template>
          <el-col :xs="24" :sm="24" :md="24" :offset="0">
            <el-form-item label="故障现象:" prop="faultPhenomenon" required>
              <el-input
                maxlength="3000"
                @keyup.native="descTip(3000,'faultPhenomenon','showgzxx')"
                show-word-limit
                type="textarea"
                :rows="2"
                v-model="sheetForm.faultPhenomenon"
                placeholder="请输入内容"
                clearable
                style="width: 100%"
              >
              </el-input>
              <div class="el-form-item__error"  v-if="showgzxx">已超过填写字数上限</div>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="24" :md="24" :offset="0">
            <el-form-item label="工作内容:" prop="workContent">
              <el-input
                v-model="sheetForm.workContent"
                placeholder="请输入内容"
                clearable
                style="width: 100%"
                maxlength="2000"
                show-word-limit
                @keyup.native="descTip(2000,'workContent','showgznr')"
              >
              </el-input>
              <div class="el-form-item__error"  v-if="showgznr">已超过填写字数上限</div>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="影响业务列表:" prop="appendixFileUrl">
              <el-input
                v-model="sheetForm.appendixFileUrl"
                placeholder="添加附件"
                readonly
                style="width: 100%"
              >
                <el-button type="info" slot="append" @click="incidenceBrowse"
                  >+</el-button
                >
                <el-button
                  v-if="ngIncidenceFileArr.length > 0"
                  type="info"
                  slot="append"
                  @click="ngIncidenceBrowse"
                  title="查看拟稿上传的影响业务列表"
                  >查看</el-button
                >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="16" :md="16" :offset="0">
            <el-form-item
              label="影响范围:"
              prop="effectRange"
              :rules="[
                {
                  required: sheetForm.isShare == '1' ? true : false,
                  message: '请输入内容',
                  trigger: 'blur',
                },
              ]"
            >
              <el-input
                v-model="sheetForm.effectRange"
                placeholder="请输入内容"
                clearable
                style="width: 100%"
                maxlength="1000"
                show-word-limit
                @keyup.native="descTip(1000,'effectRange','showyxfw')"
              >
              </el-input>
              <div class="el-form-item__error"  v-if="showyxfw">已超过填写字数上限</div>
            </el-form-item>
          </el-col>
          <el-col :span="24" :offset="0">
            <el-form-item label="备注:" prop="falutComment">
              <el-input
                v-model="sheetForm.falutComment"
                placeholder="请输入内容"
                clearable
                style="width: 100%"
                show-word-limit
                maxlength="255"
                @keyup.native="descTip(255,'falutComment','showbz')"
              >
              </el-input>
              <div class="el-form-item__error"  v-if="showbz">已超过填写字数上限</div>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="附件:" prop="appendix">
              <el-input
                v-model="sheetForm.appendix"
                placeholder="添加附件"
                readonly
                style="width: 100%"
              >
                <el-button type="info" slot="append" @click="attachmentBrowse"
                  >+</el-button
                >
                <el-button
                  v-if="ngAttachmentArr.length > 0"
                  type="info"
                  slot="append"
                  @click="ngAttachmentBrowse"
                  title="查看拟稿上传的附件"
                  >查看</el-button
                >
              </el-input>
            </el-form-item>
          </el-col>
<!--          无线网7和传输3-->
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :offset="0"
            v-if="
              sheetForm.professionalType == 7 || sheetForm.professionalType == 3
            "
          >
            <el-form-item label="是否共建共享:" prop="isShare">
              <el-radio-group v-model="sheetForm.isShare">
                <el-radio :label="'1'">是</el-radio>
                <el-radio :label="'0'">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <template
        v-if="
          (sheetForm.professionalType == 7 ||
            sheetForm.professionalType == 3) &&
          sheetForm.isShare == '1'
        "
      >
        <el-card
          shadow="always"
          :body-style="{ padding: '10px' }"
          style="margin-top: 10px"
        >
          <div slot="header" class="card-title">
            <span>共建共享信息</span>
          </div>
          <!-- 无线网 -->
          <template v-if="sheetForm.professionalType == 7">
            <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
              <el-col :span="8">
                <el-form-item
                  label="站址信息:"
                  prop="stInfo"
                  :rules="[
                    {
                      required:
                        sheetForm.professionalType == 7 &&
                        sheetForm.isShare == '1',
                      message: '请输入内容',
                      trigger: 'blur',
                    },
                  ]"
                >
                  <el-input
                    v-model="sheetForm.stInfo"
                    placeholder="请输入内容"
                    style="width: 100%"
                    maxlength="255"
                    @keyup.native="descTip(255,'stInfo','showzzxx')"
                  >
                  </el-input>
                  <div class="el-form-item__error"  v-if="showzzxx">已超过填写字数上限</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label="站址名称:"
                  prop="stName"
                  :rules="[
                    {
                      required:
                        sheetForm.professionalType == 7 &&
                        sheetForm.isShare == '1',
                      message: '请输入内容',
                      trigger: 'blur',
                    },
                  ]"
                >
                  <el-input
                    v-model="sheetForm.stName"
                    placeholder="请输入内容"
                    style="width: 100%"
                    maxlength="255"
                    @keyup.native="descTip(255,'stName','showzzmc')"
                  >
                  </el-input>
                  <div class="el-form-item__error"  v-if="showzzmc">已超过填写字数上限</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label="站址经纬度:"
                  prop="longLatitude"
                  :rules="[
                    {
                      required:
                        sheetForm.professionalType == 7 &&
                        sheetForm.isShare == '1',
                      message: '请输入内容',
                      trigger: 'blur',
                    },
                  ]"
                >
                  <el-input
                    v-model="sheetForm.longLatitude"
                    placeholder="请输入内容"
                    style="width: 100%"
                    maxlength="255"
                    @keyup.native="descTip(255,'longLatitude','showzzjwd')"
                  >
                  </el-input>
                  <div class="el-form-item__error"  v-if="showzzjwd">已超过填写字数上限</div>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="8" :md="8" :offset="0">
                <el-form-item
                  label="站址类别:"
                  prop="stCategory"
                  :rules="[
                    {
                      required:
                        sheetForm.professionalType == 7 &&
                        sheetForm.isShare == '1',
                      message: '请选择内容',
                      trigger: 'blur',
                    },
                  ]"
                >
                  <el-select
                    v-model="sheetForm.stCategory"
                    placeholder="请选择内容"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="(item, i) in stCategoryList"
                      :key="i"
                      :label="item.dictName"
                      :value="item.dictCode"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label="基站ID:"
                  prop="bsCode"
                  :rules="[
                    {
                      required:
                        sheetForm.professionalType == 7 &&
                        sheetForm.isShare == '1',
                      message: '请输入内容',
                      trigger: 'blur',
                    },
                  ]"
                >
                  <el-input
                    v-model="sheetForm.bsCode"
                    placeholder="请输入内容"
                    style="width: 100%"
                    maxlength="255"
                    @keyup.native="descTip(255,'bsCode','showjzid')"
                  >
                  </el-input>
                  <div class="el-form-item__error"  v-if="showjzid">已超过填写字数上限</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label="基站名称:"
                  prop="bsName"
                  :rules="[
                    {
                      required:
                        sheetForm.professionalType == 7 &&
                        sheetForm.isShare == '1',
                      message: '请输入内容',
                      trigger: 'blur',
                    },
                  ]"
                >
                  <el-input
                    v-model="sheetForm.bsName"
                    placeholder="请输入内容"
                    style="width: 100%"
                    maxlength="255"
                    @keyup.native="descTip(255,'bsName','showjzmc')"
                  >
                  </el-input>
                  <div class="el-form-item__error"  v-if="showjzmc">已超过填写字数上限</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label="所属区域:"
                  prop="alarmRegion"
                  :rules="[
                    {
                      required:
                        sheetForm.professionalType == 7 &&
                        sheetForm.isShare == '1',
                      message: '请输入内容',
                      trigger: 'blur',
                    },
                  ]"
                >
                  <el-input
                    v-model="sheetForm.alarmRegion"
                    placeholder="请输入内容"
                    style="width: 100%"
                    maxlength="255"
                    @keyup.native="descTip(255,'alarmRegion','showssqy')"
                  >
                  </el-input>
                  <div class="el-form-item__error"  v-if="showssqy">已超过填写字数上限</div>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="8" :md="8" :offset="0">
                <el-form-item
                  label="所属场景:"
                  prop="sceneName"
                  :rules="[
                    {
                      required:
                        sheetForm.professionalType == 7 &&
                        sheetForm.isShare == '1',
                      message: '请选择内容',
                      trigger: 'blur',
                    },
                  ]"
                >
                  <el-select
                    v-model="sheetForm.sceneName"
                    placeholder="请选择内容"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="(item, i) in sceneNameList"
                      :key="i"
                      :label="item.dictName"
                      :value="item.dictCode"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="8" :md="8" :offset="0">
                <el-form-item
                  label="基站级别:"
                  prop="bsLevel"
                  :rules="[
                    {
                      required:
                        sheetForm.professionalType == 7 &&
                        sheetForm.isShare == '1',
                      message: '请选择内容',
                      trigger: 'blur',
                    },
                  ]"
                >
                  <el-select
                    v-model="sheetForm.bsLevel"
                    placeholder="请选择内容"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="(item, i) in bsLevelList"
                      :key="i"
                      :label="item.dictName"
                      :value="item.dictCode"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="8" :md="8" :offset="0">
                <el-form-item
                  label="基站类型:"
                  prop="bsType"
                  :rules="[
                    {
                      required:
                        sheetForm.professionalType == 7 &&
                        sheetForm.isShare == '1',
                      message: '请选择内容',
                      trigger: 'blur',
                    },
                  ]"
                >
                  <el-select
                    v-model="sheetForm.bsType"
                    placeholder="请选择内容"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="(item, i) in bsTypeList"
                      :key="i"
                      :label="item.dictName"
                      :value="item.dictCode"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label="告警ID和名称:"
                  prop="alarmId"
                  :rules="[
                    {
                      required:
                        sheetForm.professionalType == 7 &&
                        sheetForm.isShare == '1',
                      message: '请输入内容',
                      trigger: 'blur',
                    },
                  ]"
                >
                  <el-input
                    v-model="sheetForm.alarmId"
                    placeholder="请输入内容"
                    style="width: 100%"
                    maxlength="255"
                    @keyup.native="descTip(255,'alarmId','showgjidmc')"
                  >
                  </el-input>
                  <div class="el-form-item__error"  v-if="showgjidmc">已超过填写字数上限</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label="告警类别:"
                  prop="alarmType"
                  :rules="[
                    {
                      required:
                        sheetForm.professionalType == 7 &&
                        sheetForm.isShare == '1',
                      message: '请输入内容',
                      trigger: 'blur',
                    },
                  ]"
                >
                  <el-input
                    v-model="sheetForm.alarmType"
                    placeholder="请输入内容"
                    style="width: 100%"
                    maxlength="255"
                    @keyup.native="descTip(255,'alarmType','showgjlb')"
                  >
                  </el-input>
                  <div class="el-form-item__error"  v-if="showgjlb">已超过填写字数上限</div>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item
                  label="告警详情:"
                  prop="alarmDetail"
                  :rules="[
                    {
                      required:
                        sheetForm.professionalType == 7 &&
                        sheetForm.isShare == '1',
                      message: '请输入内容',
                      trigger: 'blur',
                    },
                  ]"
                >
                  <el-input
                    placeholder="请输入内容"
                    v-model="sheetForm.alarmDetail"
                    style="width: 100%"
                    show-word-limit
                    maxlength="255"
                    @keyup.native="descTip(255,'alarmDetail','showgjxq')"
                  >
                  </el-input>
                  <div class="el-form-item__error"  v-if="showgjxq">已超过填写字数上限</div>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="8" :md="8" :offset="0">
                <el-form-item
                  label="告警级别:"
                  prop="alarmLevel"
                  :rules="[
                    {
                      required:
                        sheetForm.professionalType == 7 &&
                        sheetForm.isShare == '1',
                      message: '请选择内容',
                      trigger: 'blur',
                    },
                  ]"
                >
                  <el-select
                    v-model="sheetForm.alarmLevel"
                    placeholder="请选择内容"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="(item, i) in alarmLevelList"
                      :key="i"
                      :label="item.dictName"
                      :value="item.dictCode"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="8" :md="8" :offset="0">
                <el-form-item
                  label="告警发生时间:"
                  prop="alarmOccurTime"
                  :rules="[
                    {
                      required:
                        sheetForm.professionalType == 7 &&
                        sheetForm.isShare == '1',
                      message: '请选择时间',
                      trigger: 'blur',
                    },
                  ]"
                >
                  <el-date-picker
                    v-model="sheetForm.alarmOccurTime"
                    type="datetime"
                    placeholder="请选择时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    clearable
                    style="width: 100%"
                    :picker-options="pickerOptions"
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="8" :md="8" :offset="0">
                <el-form-item
                  label="故障原因:"
                  prop="faultReason"
                  :rules="[
                    {
                      required:
                        sheetForm.professionalType == 7 &&
                        sheetForm.isShare == '1',
                      message: '请选择内容',
                      trigger: 'blur',
                    },
                  ]"
                >
                  <el-select
                    v-model="sheetForm.faultReason"
                    placeholder="请选择内容"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="(item, i) in faultReasonList"
                      :key="i"
                      :label="item.dictName"
                      :value="item.dictCode"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="8" :md="8" :offset="0">
                <el-form-item
                  label="交互方式:"
                  prop="faultHandleMode"
                  :rules="[
                    {
                      required:
                        sheetForm.professionalType == 7 &&
                        sheetForm.isShare == '1',
                      message: '请选择内容',
                      trigger: 'blur',
                    },
                  ]"
                >
                  <el-select
                    v-model="sheetForm.faultHandleMode"
                    placeholder="请选择内容"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="(item, i) in faultHandleModeList"
                      :key="i"
                      :label="item.dictName"
                      :value="item.dictCode"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </template>

          <!-- 传输网 -->
          <template v-if="sheetForm.professionalType == 3">
            <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
              <el-col :span="8">
                <el-form-item
                  label="网元名称:"
                  prop="neName"
                  :rules="[
                    {
                      required:
                        sheetForm.professionalType == 3 &&
                        sheetForm.isShare == '1',
                      message: '请输入内容',
                      trigger: 'blur',
                    },
                  ]"
                >
                  <el-input
                    placeholder="请输入内容"
                    v-model="sheetForm.neName"
                    style="width: 100%"
                    maxlength="32"
                    @keyup.native="descTip(32,'neName','showwymc')"
                  >
                  </el-input>
                  <div class="el-form-item__error"  v-if="showwymc">已超过填写字数上限</div>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="8" :md="8" :offset="0">
                <el-form-item
                  label="网元等级:"
                  prop="neLevel"
                  :rules="[
                    {
                      required:
                        sheetForm.professionalType == 3 &&
                        sheetForm.isShare == '1',
                      message: '请选择内容',
                      trigger: 'blur',
                    },
                  ]"
                >
                  <el-select
                    v-model="sheetForm.neLevel"
                    placeholder="请选择内容"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="(item, i) in neLevelList"
                      :key="i"
                      :label="item.dictName"
                      :value="item.dictCode"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label="告警ID:"
                  prop="alarmId"
                  :rules="[
                    {
                      required:
                        sheetForm.professionalType == 3 &&
                        sheetForm.isShare == '1',
                      message: '请输入内容',
                      trigger: 'blur',
                    },
                  ]"
                >
                  <el-input
                    v-model="sheetForm.alarmId"
                    placeholder="请输入内容"
                    style="width: 100%"
                    maxlength="250"
                    @keyup.native="descTip(250,'alarmId','showgjid')"
                  >
                  </el-input>
                  <div class="el-form-item__error"  v-if="showgjid">已超过填写字数上限</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label="告警名称:"
                  prop="alarmName"
                  :rules="[
                    {
                      required:
                        sheetForm.professionalType == 3 &&
                        sheetForm.isShare == '1',
                      message: '请输入内容',
                      trigger: 'blur',
                    },
                  ]"
                >
                  <el-input
                    v-model="sheetForm.alarmName"
                    placeholder="请输入内容"
                    style="width: 100%"
                    maxlength="250"
                    @keyup.native="descTip(250,'alarmName','showgjmc')"
                  >
                  </el-input>
                  <div class="el-form-item__error"  v-if="showgjmc">已超过填写字数上限</div>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="8" :md="8" :offset="0">
                <el-form-item
                  label="告警发生时间:"
                  prop="alarmOccurTime"
                  :rules="[
                    {
                      required:
                        sheetForm.professionalType == 3 &&
                        sheetForm.isShare == '1',
                      message: '请选择时间',
                      trigger: 'blur',
                    },
                  ]"
                >
                  <el-date-picker
                    v-model="sheetForm.alarmOccurTime"
                    type="datetime"
                    placeholder="请选择时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    clearable
                    style="width: 100%"
                    :picker-options="pickerOptions"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label="告警定位:"
                  prop="alarmLocation"
                  :rules="[
                    {
                      required:
                        sheetForm.professionalType == 3 &&
                        sheetForm.isShare == '1',
                      message: '请输入内容',
                      trigger: 'blur',
                    },
                  ]"
                >
                  <el-input
                    v-model="sheetForm.alarmLocation"
                    placeholder="请输入内容"
                    style="width: 100%"
                    maxlength="50"
                    @keyup.native="descTip(50,'alarmLocation','showgjdw')"
                  >
                  </el-input>
                  <div class="el-form-item__error"  v-if="showgjdw">已超过填写字数上限</div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label="故障种类:"
                  prop="alarmType"
                  :rules="[
                    {
                      required:
                        sheetForm.professionalType == 3 &&
                        sheetForm.isShare == '1',
                      message: '请输入内容',
                      trigger: 'blur',
                    },
                  ]"
                >
                  <el-input
                    v-model="sheetForm.alarmType"
                    placeholder="请输入内容"
                    style="width: 100%"
                    maxlength="255"
                    @keyup.native="descTip(255,'alarmType','showgzzl')"
                  >
                  </el-input>
                  <div class="el-form-item__error"  v-if="showgzzl">已超过填写字数上限</div>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item
                  label="告警详情:"
                  prop="alarmDetail"
                  :rules="[
                    {
                      required:
                        sheetForm.professionalType == 3 &&
                        sheetForm.isShare == '1',
                      message: '请输入内容',
                      trigger: 'blur',
                    },
                  ]"
                >
                  <el-input
                    placeholder="请输入内容"
                    v-model="sheetForm.alarmDetail"
                    style="width: 100%"
                    show-word-limit
                    maxlength="255"
                    @keyup.native="descTip(255,'alarmDetail','showgjxq')"
                  >
                  </el-input>
                  <div class="el-form-item__error"  v-if="showgjxq">已超过填写字数上限</div>
                </el-form-item>
              </el-col>
            </el-row>
          </template>
        </el-card>
      </template>

      <!--      工单派送信息-->
      <el-card
        shadow="always"
        :body-style="{ padding: '10px' }"
        style="margin-top: 10px"
      >
        <div slot="header" class="card-title">
          <span>工单派送信息</span>
        </div>
        <el-row :gutter="10" type="flex" style="flex-wrap: wrap">
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="建单人主送:" prop="buildZs" required>
              <el-input readonly v-model="sheetForm.buildZs" placeholder="添加人员">
                <template v-for="(tag, index) in organizeForm.builderZsList">
                  <el-tag
                    slot="prefix"
                    style="margin-top: 5px"
                    :key="index"
                    :closable="true"
                    @close="handleClose('builderZs', tag)"
                    v-show="index < 1"
                    v-if="tag.bz == 'user'"
                  >
                    {{ tag.name }}
                  </el-tag>
                  <el-tag
                    slot="prefix"
                    style="margin-top: 5px"
                    :key="index"
                    :closable="true"
                    @close="handleClose('builderZs', tag)"
                    v-show="index < 1"
                    v-if="tag.bz == 'org'"
                  >
                    {{ tag.orgName }}
                  </el-tag>
                </template>
                <el-popover
                  slot="prefix"
                  v-if="organizeForm.builderZsList.length >= 2"
                  width="500"
                  trigger="click"
                >
                  <el-input
                    v-model="organizeForm.builderZsName"
                    placeholder="请输入主送人员姓名/组织名称"
                  >
                    <el-button
                      type="info"
                      slot="append"
                      icon="el-icon-search"
                      @click="search('builderZs')"
                    >
                    </el-button>
                    <el-button
                      type="info"
                      slot="append"
                      icon="el-icon-close"
                      @click="clear('builderZs')"
                    >
                    </el-button>
                  </el-input>
                  <el-table
                    ref="multipleTable"
                    tooltip-effect="dark"
                    @selection-change="handleSelectionChange"
                    :data="organizeForm.builderZsListCopy"
                    max-height="240"
                  >
                    <el-table-column width="30" type="selection">
                    </el-table-column>
                    <el-table-column
                      min-width="70"
                      property="name"
                      label="姓名"
                    >
                    </el-table-column>
                    <el-table-column
                      min-width="180"
                      property="orgName"
                      label="组织"
                    >
                    </el-table-column>
                    <el-table-column
                      min-width="120"
                      property="mobilePhone"
                      label="电话"
                    >
                    </el-table-column>
                    <el-table-column fixed="right" label="操作" width="50">
                      <template slot-scope="scope">
                        <el-button
                          type="text"
                          size="small"
                          @click.native.prevent="
                            handleClose('builderZs', scope.row)
                          "
                        >
                          移除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <el-button
                    size="small"
                    type="text"
                    @click="toggleSelection('builderZs')"
                    >批量移除
                  </el-button>
                  <el-tag slot="reference" style="margin-top: 3px">
                    +{{ organizeForm.builderZsList.length - 1 }}
                  </el-tag>
                </el-popover>
                <el-button
                  type="info"
                  slot="append"
                  icon="el-icon-user"
                  @click="onOpenPeopleDialog('lordSentDetermine')"
                ></el-button>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="建单人抄送:" prop="buildCs">
              <el-input readonly v-model="sheetForm.buildCs" placeholder="添加人员">
                <template v-for="(tag, index) in organizeForm.builderCsList">
                  <el-tag
                    slot="prefix"
                    style="margin-top: 5px"
                    :key="index"
                    :closable="true"
                    @close="handleClose('builderCs', tag)"
                    v-show="index < 1"
                    v-if="tag.bz == 'user'"
                  >
                    {{ tag.name }}
                  </el-tag>
                  <el-tag
                    slot="prefix"
                    style="margin-top: 5px"
                    :key="index"
                    :closable="true"
                    @close="handleClose('builderCs', tag)"
                    v-show="index < 1"
                    v-if="tag.bz == 'org'"
                  >
                    {{ tag.orgName }}
                  </el-tag>
                </template>
                <el-popover
                  slot="prefix"
                  v-if="organizeForm.builderCsList.length >= 2"
                  width="500"
                  trigger="click"
                >
                  <el-input
                    v-model="organizeForm.builderCsName"
                    placeholder="请输入抄送人员姓名/组织名称"
                  >
                    <el-button
                      type="info"
                      slot="append"
                      icon="el-icon-search"
                      @click="search('builderCs')"
                    >
                    </el-button>
                    <el-button
                      type="info"
                      slot="append"
                      icon="el-icon-close"
                      @click="clear('builderCs')"
                    >
                    </el-button>
                  </el-input>
                  <el-table
                    ref="multipleTable"
                    tooltip-effect="dark"
                    @selection-change="handleSelectionChange"
                    :data="organizeForm.builderCsListCopy"
                    max-height="240"
                  >
                    <el-table-column width="30" type="selection">
                    </el-table-column>
                    <el-table-column
                      min-width="70"
                      property="name"
                      label="姓名"
                    >
                    </el-table-column>
                    <el-table-column
                      min-width="180"
                      property="orgName"
                      label="组织"
                    >
                    </el-table-column>
                    <el-table-column
                      min-width="120"
                      property="mobilePhone"
                      label="电话"
                    >
                    </el-table-column>
                    <el-table-column fixed="right" label="操作" width="50">
                      <template slot-scope="scope">
                        <el-button
                          type="text"
                          size="small"
                          @click.native.prevent="
                            handleClose('builderCs', scope.row)
                          "
                        >
                          移除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <el-button
                    size="small"
                    type="text"
                    @click="toggleSelection('builderCs')"
                    >批量移除
                  </el-button>
                  <el-tag slot="reference" style="margin-top: 5px">
                    +{{ organizeForm.builderCsList.length - 1 }}
                  </el-tag>
                </el-popover>
                <el-button
                  type="info"
                  slot="append"
                  icon="el-icon-user"
                  @click="onOpenPeopleDialog('ccDetermine')"
                >
                </el-button>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="定性审核人:" prop="checkPerson">
              <el-input readonly v-model="sheetForm.checkPerson" placeholder="添加人员">
                <el-tag
                  slot="prefix"
                  style="margin-top: 5px"
                  v-for="(tag, index) in organizeForm.auditorList"
                  :key="index"
                  :closable="true"
                  @close="handleClose('auditor', tag)"
                  v-show="index < 1"
                >
                  {{ tag.name }}
                </el-tag>
                <el-popover
                  slot="prefix"
                  v-if="organizeForm.auditorList.length >= 2"
                  width="500"
                  trigger="click"
                >
                  <el-input
                    v-model="organizeForm.auditorName"
                    placeholder="请输入定性审核人员姓名/组织名称"
                  >
                    <el-button
                      type="info"
                      slot="append"
                      icon="el-icon-search"
                      @click="search('auditor')"
                    >
                    </el-button>
                    <el-button
                      type="info"
                      slot="append"
                      icon="el-icon-close"
                      @click="clear('auditor')"
                    >
                    </el-button>
                  </el-input>
                  <el-table
                    ref="multipleTable"
                    tooltip-effect="dark"
                    @selection-change="handleSelectionChange"
                    :data="organizeForm.auditorListCopy"
                    max-height="240"
                  >
                    <el-table-column width="30" type="selection">
                    </el-table-column>
                    <el-table-column
                      min-width="70"
                      property="name"
                      label="姓名"
                    >
                    </el-table-column>
                    <el-table-column
                      min-width="180"
                      property="orgName"
                      label="组织"
                    >
                    </el-table-column>
                    <el-table-column
                      min-width="120"
                      property="mobilePhone"
                      label="电话"
                    >
                    </el-table-column>
                    <el-table-column fixed="right" label="操作" width="50">
                      <template slot-scope="scope">
                        <el-button
                          type="text"
                          size="small"
                          @click.native.prevent="
                            handleClose('auditor', scope.row)
                          "
                        >
                          移除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <el-button
                    size="small"
                    type="text"
                    @click="toggleSelection('auditor')"
                    >批量移除
                  </el-button>
                  <el-tag slot="reference" style="margin-top: 5px">
                    +{{ organizeForm.auditorList.length - 1 }}
                  </el-tag>
                </el-popover>
                <el-button
                  type="info"
                  slot="append"
                  icon="el-icon-user"
                  @click="onOpenPeopleDialog('auditorDetermine')"
                ></el-button>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0" v-if="organizeForm.builderZsList.length>1">
            <el-form-item prop="isSendSms">
              <span slot="label" style="display:inline-block;">
                <el-tooltip
                  popper-class="my-popper"
                  effect="dark"
                  :content="toopTip"
                  placement="bottom">
                  <em class='el-icon-question' style="color:#b50b14 "></em>
                </el-tooltip>
                派单模式:
              </span>
              <el-radio-group v-model="sheetForm.seizeOrders">
                <el-radio :label="'1'">抢单受理</el-radio>
                <el-radio :label="'0'">均可受理</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="是否通知他人:" prop="isSendSms">
              <el-radio-group v-model="sheetForm.isSendSms">
                <el-radio :label="'1'">是</el-radio>
                <el-radio :label="'0'">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col
            :xs="24"
            :sm="8"
            :md="8"
            :offset="0"
            v-if="sheetForm.isSendSms == '1'"
          >
            <el-form-item
              label="接收人:"
              prop="acceptPerson"
              :rules="[
                {
                  required: sheetForm.isSendSms == '1' ? true : false,
                  message: '请选择',
                },
              ]"
            >
              <el-input readonly v-model="sheetForm.acceptPerson" placeholder="添加人员">
                <el-tag
                  slot="prefix"
                  style="margin-top: 5px"
                  v-for="(tag, index) in organizeForm.recipientList"
                  :key="index"
                  :closable="true"
                  @close="handleClose('recipient', tag)"
                  v-show="index < 1"
                >
                  {{ tag.name }}
                </el-tag>

                <el-popover
                  slot="prefix"
                  v-if="organizeForm.recipientList.length >= 2"
                  width="500"
                  trigger="click"
                >
                  <el-input
                    v-model="organizeForm.recipientName"
                    placeholder="请输入接收人员姓名/组织名称"
                  >
                    <el-button
                      type="info"
                      slot="append"
                      icon="el-icon-search"
                      @click="search('recipient')"
                    >
                    </el-button>
                    <el-button
                      type="info"
                      slot="append"
                      icon="el-icon-close"
                      @click="clear('recipient')"
                    >
                    </el-button>
                  </el-input>
                  <el-table
                    ref="multipleTable"
                    tooltip-effect="dark"
                    @selection-change="handleSelectionChange"
                    :data="organizeForm.recipientListCopy"
                    max-height="240"
                  >
                    <el-table-column width="30" type="selection">
                    </el-table-column>
                    <el-table-column
                      min-width="70"
                      property="name"
                      label="姓名"
                    >
                    </el-table-column>
                    <el-table-column
                      min-width="180"
                      property="orgName"
                      label="组织"
                    >
                    </el-table-column>
                    <el-table-column
                      min-width="120"
                      property="mobilePhone"
                      label="电话"
                    >
                    </el-table-column>
                    <el-table-column fixed="right" label="操作" width="50">
                      <template slot-scope="scope">
                        <el-button
                          type="text"
                          size="small"
                          @click.native.prevent="
                            handleClose('recipient', scope.row)
                          "
                        >
                          移除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <el-button
                    size="small"
                    type="text"
                    @click="toggleSelection('recipient')"
                    >批量移除
                  </el-button>
                  <el-tag slot="reference" style="margin-top: 5px">
                    +{{ organizeForm.recipientList.length - 1 }}
                  </el-tag>
                </el-popover>
                <el-button
                  type="info"
                  slot="append"
                  icon="el-icon-user"
                  @click="onOpenPeopleDialog('recipientDetermine')"
                ></el-button>
              </el-input>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :offset="0" v-if="sheetForm.isSendSms == '1'">
            <el-form-item
              label="发送内容:"
              prop="sendContent"
              :rules="[
                {
                  required: sheetForm.isSendSms == '1' ? true : false,
                  message: '请输入内容',
                  trigger: 'blur',
                },
              ]"
            >
              <el-input
                v-model="sheetForm.sendContent"
                placeholder="请输入内容"
                type="textarea"
                :rows="4"
                clearable
                style="width: 100%"
                show-word-limit
                maxlength="1000"
                @keyup.native="descTip(1000,'sendContent','showfsnr')"
              ></el-input>
              <div class="el-form-item__error"  v-if="showfsnr">已超过填写字数上限</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
    </el-form>

    <dia-tissue-tree
      v-if="isDiaOrgsUserTree"
      :title="diaPeople.title"
      :visible.sync="diaPeople.visible"
      :showOrgsTree="diaPeople.showOrgsTree"
      :professionalType="mainProfessionalType"
      @on-save="onSavePeople"
      :appendToBody="true"
      :userAttribution="userAttribution"
      :show-contact-user-tab="true"
      :show-contact-org-tab="diaPeople.showOrgsTree"
    />
    <el-dialog
      width="420px"
      title="附件选择"
      :visible.sync="attachmentDialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <file-upload
        @change="changeFileData"
        @cancel="closeAttachmentDialog"
      ></file-upload>
    </el-dialog>
    <el-dialog
      width="420px"
      title="影响业务列表附件选择"
      :visible.sync="incidenceDialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <file-upload
        @change="changeIncidenceFileData"
        @cancel="closeIncidenceDialog"
      ></file-upload>
    </el-dialog>
    <el-dialog
      width="500px"
      title="文件列表"
      :visible.sync="ngIncidenceFileDownloadVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <file-download
        @cancel="ngIncidenceFileDownloadVisible = false"
        :attachmentArr="ngIncidenceFileArr"
      ></file-download>
    </el-dialog>
    <el-dialog
      width="500px"
      title="文件列表"
      :visible.sync="ngAttachmentDownloadVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <file-download
        @cancel="ngAttachmentDownloadVisible = false"
        :attachmentArr="ngAttachmentArr"
      ></file-download>
    </el-dialog>
  </head-fixed-layout>
</template>

<script>
import { mapGetters } from "vuex";
import moment from "moment";
import HeadFixedLayout from "./../workOrder/components/HeadFixedLayout.vue";
// import FileUpload from "./../workOrder/components/FileUpload.vue";
import FileUpload from "./workOrderWaitDetail/components/FileUpload";
import FileDownload from "./../workOrder/components/FileDownload.vue";
// import DiaOrgsUserTreeOther from "./workOrderWaitDetail/components/DiaOrgsUserTreeOther.vue";
import DiaTissueTree from "@/plugin/backbone/components/common/DiaTissueTree";

import {
  apiGetOrgInfo, apiGetRegionInfoSfty,
  apiSaveContactUser,
  getCurrentTime,
} from "./../workOrder/api/CommonApi";
import {
  apiInitOrderDraf,
  apiGetFaultArea,
  apiDict,
  apiGroupCode,
} from "./workOrderWaitDetail/api/CommonApi";
import { apiCommonDraftOrBuild } from "./../workOrder/api/WorkOrderDraftEdit";
export default {
  name: "WorkOrderDraftEdit",
  components: {
    HeadFixedLayout,
    FileUpload,
    FileDownload,
    DiaTissueTree,
  },

  data() {

    var validTitle = (rule, value, callback) => {
      // if (value.length > 2) {
      //   callback(new Error("最多输入2个字符"));
      //   this.sheetForm.sheetTitle = value.slice(0,2);
      // }
      // else {
      //   callback();
      // }
    };
    var validHappenTime = (rule, value, callback) => {
      if (value === "" || value === null) {
        callback(new Error("请选择发生时间"));
      } else {
        let seconds = moment(
          this.sheetForm.sheetCreateTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.sheetForm.happenTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        if (seconds < 0) {
          callback(new Error("“发生时间”不能晚于“建单时间”，请重新选择"));
        } else {
          callback();
        }
      }
    };
    var validRecipient = (rule, value, callback) => {
      if (
        this.sheetForm.isSendSms == "1" &&
        this.sheetForm.recipientDetailUserName == ""
      ) {
        callback(new Error("请选择短信通知人"));
      } else {
        callback();
      }
    };
    var validEffectRange = (rule, value, callback) => {
      if (this.sheetForm.isShare == "1") {
        callback(new Error("请填写影响范围"));
      } else {
        callback();
      }
    };
    return {
      toopTip:'',
      showgdzt:false,
      showslsx:false,
      showygclsx:false,
      showyclx:false,
      showywmc:false,
      showgzxx:false,
      showbz:false,
      showgznr:false,
      showyxfw:false,
      showfsnr:false,
      showwymc:false,
      showgjid:false,
      showgjmc:false,
      showgjdw:false,
      showgzzl:false,
      showgjxq:false,
      showzzxx:false,
      showzzmc:false,
      showzzjwd:false,
      showjzid:false,
      showjzmc:false,
      showssqy:false,
      showgjlb:false,
      showgjidmc:false,
      showTime:5000,
      userAttribution: "cpNigao",
      isDiaOrgsUserTree: false,
      sheetForm: {
        woId: "", //工单id
        linkId: "",
        sender: "", //建单人的登录名

        coverScene: "", //覆盖场景
        faultLevel:"6",//工单优先级
        senderName: "", //建单人真实姓名
        senderDeptName: "", // 建单部门
        sheetCreateTime: getCurrentTime(Date.now()), // 建单时间
        sheetTitle: "", //工单主题
        createType: "", //工单来源
        orgType: "", //告警类别----
        alarmCreateTime: getCurrentTime(Date.now()), //发生时间
        emergencyLevel: "3", //紧急程度
        acceptTimeLimit: "30", // 受理时限
        processTimeLimit: "", //预估处理时限-----
        professionalType: "", //所属专业
        networkType: "", //网络类型
        businessDown: "1", //业务中断
        cloudPoolType: "", //云池类型 当所属专业=省份云时，显示云池类型字段
        busName: "", //业务名称 当所属专业=省份云时，显示业务名称字段
        faultPhenomenon: "", //故障现象
        workContent: "", //工作内容
        appendixFileUrl: "", //影响业务列表
        effectRange: "", //影响范围
        falutComment: "", //备注
        appendix: "", //附件名称
        att_group_key: "", //附件组编码

        buildZs: "",
        buildCs: "",
        checkPerson: "",
        acceptPerson: "",
        agentManDetail: "",
        copyManDetail: "",
        auditorManDetail: "",
        recipientDetailUserName: "",
        agentDeptName: "", //主送部门
        agentDeptCode: "", //主送部门id
        agentMan: "", //主送人员
        agentManId: "", // 主送人员id

        copyDeptName: "", //抄送受理部门
        copyDeptCode: "", //抄送受理部门id
        copyMan: "", //抄送受理人员
        copyManId: "", //抄送受理人员id

        auditorId: "", //定性审核人
        auditorName: "",

        seizeOrders:"1",//默认抢单模式
        isSendSms: "0", //短信通知
        smsToUsername: "", //短信通知人姓名
        smsToDeptName: "", //短信通知部门
        smsToDeptCode: "", //短信通知部门id
        smsToUserid: "", //短信通知人Id
        smsToUserphone: "", //短信通知人手机号码
        sendContent:
          "1、光缆：**， ××××光缆在××省××（网元）与××（网元）之间中断，影响××××波分系统，承载的SDH系统保护倒换，已经派单给省公司，并电话通知处理，现在该省正在利用备用纤芯倒接;(该段落OLP正常切换;)(分公司正在协调处理;)2、设备：××， ××××系统中断，初步怀疑是××网元××板卡问题，承载的业务目前不受影响，已经派单给省公司，并电话通知处理，现在分公司正在利用备用波道倒接；(该系统承载的SDH系统正常倒换，业务不受影响；)(该系统承载的SDH系统倒换不正常，已经影响业务；)(影响的业务有（无）××重保电路。)",

        isShare: "0", //传输、无线专业显示 是否共建共享

        stInfo: "", //站址信息
        stName: "", //站址名称
        longLatitude: "", //站址经纬度 latitude
        stCategory: "", //站址类别
        bsCode: "", //基站ID
        bsName: "", //基站名称
        alarmRegion: "", //所属区域
        sceneName: "", //所属场景
        bsLevel: "", //基站级别
        bsType: "", //基站类型
        alarmId: "", //告警ID和名称
        alarmType: "", //告警类别
        alarmDetail: "", //告警详情
        alarmLevel: "", //告警级别
        alarmOccurTime: "", //告警发生时间
        faultReason: "", //故障原因
        faultHandleMode: "", //交互方式

        cityName: "", // 故障地市
        regionName: "", //故障区县
        neName: "", //网元名称
        neLevel: "", //网元等级
        // alarmId:"",//告警ID
        alarmName: "", //告警名称
        // alarmCreateTime:"",//告警发生时间
        alarmLocation: "", //告警定位
        // alarmType:"",//故障种类
        // alarmDetail:"",//告警详情
      },
      organizeForm: {
        builderZsList: [],
        builderZsListCopy: [],
        builderZsName: "",
        builderCsList: [],
        builderCsListCopy: [],
        builderCsName: "",
        recipientList: [],
        recipientListCopy: [],
        recipientName: "",
        auditorList: [],
        auditorListCopy: [],
        auditorName: "",
      },
      sheetFormRules: {
        sheetTitle: [{ required: true, message: "工单主题不能为空" }],
        createType: [{ required: true, message: "请选择工单来源" }],
        orgType: [{ required: true, message: "请选择告警类别" }],
        alarmCreateTime: [{ validator: validHappenTime, required: true }],
        emergencyLevel: [{ required: true, message: "请选择紧急程度" }],
        acceptTimeLimit: [{ required: true, message: "请输入受理时限" }],
        professionalType: [{ required: true, message: "请选择所属专业" }],
        businessDown: [{ required: true, message: "请选择" }],
        cityName: [{ required: true, message: "请选择" }],
        regionName: [{ required: true, message: "请选择" }],
        faultPhenomenon: [{ required: true, message: "请输入故障现象" }],
        buildZs: [{ required: true, message: "主送人不能为空" }],
        // effectRange: [{ validator: validEffectRange }],
        // smsToUsername: [{ validator: validRecipient }],
        processTimeLimit: [{ required: true, message: "请输入预估处理时限" }],
        // alarmRegion: [{ required: true, message: "请选择" }],
        // regionName: [{ required: true, message: "请选择" }],
      },
      createTypeList: [], // 工单来源 下拉数据
      professionalTypeList: [], //所属专业 下拉数据
      networkTypeList: [], // 网络类型 下拉数据
      faultRegionOptions: [], //告警类别
      woPriorityList: [], // 工单优先级 下拉数据
      coverSceneList: [], // 覆盖场景 下拉数据
      urgentOption: [
        {
          label: "0",
          value: "一般",
        },
        {
          label: "3",
          value: "紧急",
        },
        {
          label: "2",
          value: "严重",
        },
      ], //紧急程度数据
      // estimatedProTimeLimitOption: [], //预估处理时限下拉
      stCategoryList: [], //站址类别 下拉数据
      sceneNameList: [], //所属场景 下拉数据
      bsLevelList: [], //基站级别 下拉数据
      bsTypeList: [], //基站类型 下拉数据
      alarmLevelList: [], //告警级别 下拉数据
      faultReasonList: [], //故障原因 下拉数据
      faultHandleModeList: [], //交互方式 下拉数据
      neLevelList: [], //网元等级 下拉数据

      diaPeople: {
        visible: false,
        title: "",
        saveName: "",
        saveTitleMap: {
          lordSentDetermine: "建单人主送",
          ccDetermine: "建单人抄送",
          recipientDetermine: "接收人选择",
          auditorDetermine: "定性审核人",
        },
        showOrgsTree: true,
        showOrgsTreeMap: {
          recipientDetermine: false,
          auditorDetermine: false,
        },
        // showContactUserTab: false,
        // showContactUserTabMap: {
        //   recipientDetermine: true,
        // },
      },

      attachmentDialogVisible: false,
      incidenceDialogVisible: false,
      ngIncidenceFileDownloadVisible: false,
      ngIncidenceFileArr: [],
      ngAttachmentDownloadVisible: false,
      ngAttachmentArr: [],

      importForm: {
        //附件
        attachmentFileList: [],
        //影响业务列表附件
        incidenceFileList: [],
      },
      sheetCommitLoading: false,
      sheetSaveLoading: false,
      orgInfoData: {}, //组织信息数据缓存
      route: null,
      userInfoData: {},
      areaCode: "",
      category: "",

      multipleSelection:[],//多选人员
      alarmRegionOptions:[],//告警地市数据组
      alarmCityOptions:[],//告警区县数据组
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
    ...mapGetters(["token"]),
    mainProfessionalType() {
      return this.$route.query.mainProfessionalType;
    },
  },
  watch: {
    "organizeForm.builderZsList": {
      handler(newV) {
        if (newV.length > 0) {
          this.sheetForm.buildZs = "已选";
        } else {
          this.sheetForm.buildZs = "";
        }
      },
      deep: true,
    },
    "organizeForm.builderCsList": {
      handler(newV) {
        if (newV.length > 0) {
          this.sheetForm.buildCs = "已选";
        } else {
          this.sheetForm.buildCs = "";
        }
      },
      deep: true,
    },
    "organizeForm.auditorList": {
      handler(newV) {
        if (newV.length > 0) {
          this.sheetForm.checkPerson = "已选";
        } else {
          this.sheetForm.checkPerson = "";
        }
      },
      deep: true,
    },
    "organizeForm.recipientList": {
      handler(newV) {
        if (newV.length > 0) {
          this.sheetForm.acceptPerson = "已选";
        } else {
          this.sheetForm.acceptPerson = "";
        }
      },
      deep: true,
    },
  },
  async created() {
    this.route = this.$route;
    //获取字典值

    await this.getDictData("create_type", "createTypeList"); //工单来源
    this.sheetForm.createType = "2"; // 电子运维新建
    this.getDictData("com_professional_type", "professionalTypeList"); //所属专业
    this.getDictData("comonpro_org_type", "faultRegionOptions"); //告警类别
    // this.getDictData("process_limited_com", "estimatedProTimeLimitOption"); //预估处理时限

    this.getDictData("comonpro_st_category", "stCategoryList"); //站址类别
    this.getDictData("comonpro_scene_name", "sceneNameList"); //所属场景
    this.getDictData("comonpro_bs_level", "bsLevelList"); //基站级别
    this.getDictData("comonpro_bs_type", "bsTypeList"); //基站类型
    this.getDictData("comonpro_alarm_level", "alarmLevelList"); //告警级别
    this.getDictData("comonpro_fault_reason", "faultReasonList"); //故障原因
    await this.getDictData("comonpro_fault_handle", "faultHandleModeList"); //交互方式
    await this.getDictData("comonpro_ne_level", "neLevelList"); //网元等级

    this.woPriorityList = [
      // {dictName:'故障影响1级',dictCode:'1'},
      // {dictName:'故障影响2级',dictCode:'2'},
      {dictName:'故障影响3级',dictCode:'3'},
      {dictName:'故障影响4级',dictCode:'4'},
      {dictName:'故障影响5级',dictCode:'5'},
      {dictName:'故障影响6级',dictCode:'6'},
      {dictName:'故障影响7级',dictCode:'7'},
      {dictName:'故障影响8级',dictCode:'8'}
      ];
    //覆盖场景枚举待修改
    this.getDictData("cover_scene", "coverSceneList");

    this.toopTip = "抢单受理：由抢单人或抢单组织处理工单，其他人员无待办，如未指定定性审核人，则不会进入定性审核环节。\n均可受理：主送多人或多组织时，涉及的人或组织均有独立子流程，均可处理工单，工单将经过定性审核环节。"

    // 专业：dictType=com_professional_type
    // 工单来源：dictType=create_type
    // 网络类型：和专业的联动枚举 dictType传值专业枚举id+comonpro_network_type 举例：14_comonpro_network_type
    // 告警类别：dictType=comonpro_org_type
    // 预估处理时限：dictType=process_limited_com
    // 网元等级：comonpro_ne_level
    // 站址类别：comonpro_st_category
    // 所属场景：comonpro_scene_name
    // 基站级别：comonpro_bs_level
    // 基站类型：comonpro_bs_type
    // 告警级别：comonpro_alarm_level
    // 故障原因：comonpro_fault_reason
    // 交互方式：comonpro_fault_handle
  },
  mounted() {
    this.sheetForm.special = "3";
    this.userInfoData = JSON.parse(this.userInfo.attr2);
    this.sheetForm.sheetCreateTime = moment(Date.now()).format(
      "YYYY-MM-DD HH:mm:ss"
    );
    this.getAlarmCityOptions();
    // this.sheetForm.happenTime = moment(Date.now()).format(
    //   "YYYY-MM-DD HH:mm:ss"
    // );
    // 初始化 工单来源
    this.getOrgInfo();
    if (this.$route.query?.orderType == "caogao") {
      this.sheetForm.woId = this.$route.query.woId;
      this.initOrder();
    }

    window["__process_response_from_getDevice"] = value => {
      let objStr = "";
      let result = JSON.parse(value);
      let objArray = JSON.parse(result?.system);
      objArray.forEach(obj => {
        objStr += obj.sysName + ",";
      });
      if (objStr) {
        objStr = objStr.substr(0, objStr.length - 1);
      }

      this.sheetForm.title += this.sheetForm.title ? "," + objStr : objStr;
    };
  },
  methods: {

    descTip(count,name,showName){
      if (this.sheetForm[name].length>=count){
        this[showName] = true;
        setTimeout(() => {
          this[showName] = false;
        }, this.showTime);
      }
      else{
        this[showName] = false;
      }
    },
    //告警获取地市
    getAlarmCityOptions(){
      // this.multipleSelections = [];
      let param = {
        cityName: "",
      };
      apiGetRegionInfoSfty(param)
        .then(res => {
          if (res.status == "0") {
            this.alarmRegionOptions = res?.data ?? [];
            // if (this.userInfoData.category == "CITY" && this.sheetForm.cityName == '') {
            //   this.sheetForm.cityName = this.alarmRegionOptions[0];
            //   this.getAlarmAreaOptions();
            // }
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    //告警获取区县
    getAlarmAreaOptions(){
      let param = {
        cityName: this.sheetForm.cityName,
      };
      apiGetRegionInfoSfty(param)
        .then(res => {
          if (res.status == "0") {
            this.alarmCityOptions = res?.data ?? [];
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    alarmRegionChange(){
      console.log(this.sheetForm.cityName);
      if (this.sheetForm.cityName==''){
        this.sheetForm.regionName = '';
        this.alarmCityOptions = [];
        return;
      }
      this.getAlarmAreaOptions();
    },

    // 回显
    initOrder() {
      // url 中 获取 sheetNo 参数
      // woId: data.woId,
      //   orderType: "caogao",
      //   sheetNo: data.sheetNo,
      //   professionalType: data.professionalType,

      const url = window.location.href;
      const queryStri = url.substring(url.indexOf("?") + 1).split("&");
      let sheetNoUrl = "";
      queryStri.forEach(item => {
        if (item.indexOf("sheetNo") >= 0) {
          sheetNoUrl = decodeURI(item.split("=")[1]);
        }
      });
      let params = {
        woId: this.$route.query.woId,
        sheetNo: this.$route.query.sheetNo || sheetNoUrl,
      };
      apiInitOrderDraf(params)
        .then(async res => {
          if (res.status == "0") {
            // let resData = Object.assign(
            //   res.data.sheetInfo,
            //   res.data.provinceSheetInfo
            // );
            // this.sheetForm = resData;
            let self = this;
            let echoData = res?.data?.sheetInfo ?? {};
            let provinceSheetInfo = res?.data?.provinceSheetInfo ?? {};
            let shareSheetInfo = res?.data?.shareSheetInfo ?? {};

            this.sheetForm.sheetNo = echoData.sheetNo;
            this.sheetForm.woId = echoData.woId;
            this.sheetForm.linkId = provinceSheetInfo.linkId;
            this.sheetForm.sender = echoData.sender;
            this.sheetForm.senderName = echoData.senderName;
            this.sheetForm.senderDeptName = echoData.senderDeptName;
            this.sheetForm.sheetCreateTime = echoData.sheetCreateTime;
            this.sheetForm.sheetTitle = echoData.sheetTitle;
            this.sheetForm.orgType = String(echoData.orgType);
            this.sheetForm.createType = String(echoData.createType);
            this.sheetForm.alarmCreateTime = echoData.alarmCreateTime;
            this.sheetForm.emergencyLevel = String(echoData.emergencyLevel);
            this.sheetForm.acceptTimeLimit = echoData.acceptTimeLimit;
            this.sheetForm.processTimeLimit = echoData.processTimeLimit;
            this.sheetForm.professionalType = echoData.professionalType;
            // this.sheetForm.networkType = String(echoData.networkType);
            this.sheetForm.businessDown = String(
              provinceSheetInfo.businessDown
            );
            this.sheetForm.cloudPoolType = echoData.cloudPoolType;
            this.sheetForm.busName = echoData.busName;
            this.sheetForm.faultPhenomenon = echoData.faultPhenomenon;
            this.sheetForm.workContent = echoData.workContent;
            this.sheetForm.effectRange = echoData.effectRange;
            this.sheetForm.falutComment = echoData.falutComment;
            this.sheetForm.cityName = echoData.cityName; //告警地市
            this.sheetForm.regionName = echoData.regionName; //告警区县
            if (this.sheetForm.cityName != null && this.sheetForm.cityName != "") {
              this.getAlarmAreaOptions();
            }


            // debugger;
            // echoData.appendix = '[{"id":"a8f7f195354c44e3a230da7f8547f880","name":"省份数据xlsx.xlsx"}]';
            // echoData.appendixFileUrl = '[{"id":"a8f7f195354c44e3a230da7f8547f880","name":"省份数据xlsx.xlsx"}]';

            if (
              echoData.appendixFileUrl &&
              echoData.appendixFileUrl.indexOf(":") > -1
            ) {
              self.ngIncidenceFileArr = JSON.parse(echoData.appendixFileUrl);
            }
            if (echoData.appendix && echoData.appendix.indexOf(":") > -1) {
              self.ngAttachmentArr = JSON.parse(echoData.appendix);
            }

            this.sheetForm.agentManDetail = echoData.agentManDetail;
            this.sheetForm.agentManId = echoData.agentManId;
            this.sheetForm.agentDeptCode = echoData.agentDeptCode;
            this.sheetForm.agentMan = echoData.agentMan;
            this.sheetForm.agentDeptName = echoData.agentDeptName;

            this.sheetForm.copyManDetail = echoData.copyManDetail;
            this.sheetForm.copyManId = echoData.copyManId;
            this.sheetForm.copyDeptCode = echoData.copyDeptCode;
            this.sheetForm.copyMan = echoData.copyMan;
            this.sheetForm.copyDeptName = echoData.copyDeptName;

            this.sheetForm.auditorName = echoData.auditorName;
            this.sheetForm.auditorId = echoData.auditorId;
            this.sheetForm.auditorManDetail =
              provinceSheetInfo.auditorManDetail;

            this.sheetForm.smsToUsername = echoData.smsToUsername;
            this.sheetForm.smsToUserid = echoData.smsToUserid;
            this.sheetForm.recipientDetailUserName =
              echoData.recipientDetailUserName;

            this.sheetForm.sendContent =
              echoData?.sendContent ?? this.sheetForm.sendContent;
            this.sheetForm.isSendSms = String(echoData.isSendSms); //是否短信通知

            this.sheetForm.seizeOrders = echoData?.seizeOrders;//是否抢单模式

            // this.sheetForm.processInstId = echoData.processInstId;
            this.sheetForm.sheetNo = echoData.sheetNo;

            this.sheetForm.isShare = String(echoData.isShare); //传输、无线专业显示 是否共建共享

            this.sheetForm.stInfo = shareSheetInfo.stInfo; //站址信息
            this.sheetForm.stName = shareSheetInfo.stName; //站址名称
            this.sheetForm.longLatitude = shareSheetInfo.longLatitude; //站址经纬度 latitude
            this.sheetForm.stCategory = String(shareSheetInfo.stCategory); //站址类别
            this.sheetForm.bsCode = shareSheetInfo.bsCode; //基站ID
            this.sheetForm.bsName = shareSheetInfo.bsName; //基站名称
            this.sheetForm.alarmRegion = shareSheetInfo.alarmRegion; //所属区域
            this.sheetForm.sceneName = String(shareSheetInfo.sceneName); //所属场景
            this.sheetForm.bsLevel = String(shareSheetInfo.bsLevel); //基站级别
            this.sheetForm.bsType = String(shareSheetInfo.bsType); //基站类型
            this.sheetForm.alarmId = shareSheetInfo.alarmId; //告警ID和名称
            this.sheetForm.alarmType = shareSheetInfo.alarmType; //告警类别
            this.sheetForm.alarmDetail = shareSheetInfo.alarmDetail; //告警详情
            this.sheetForm.alarmLevel = String(shareSheetInfo.alarmLevel); //告警级别
            this.sheetForm.alarmOccurTime = shareSheetInfo.alarmOccurTime; //告警发生时间
            this.sheetForm.faultReason = String(shareSheetInfo.faultReason); //故障原因
            this.sheetForm.faultHandleMode = String(echoData.faultHandleMode); //交互方式

            this.sheetForm.neName = shareSheetInfo.neName; //网元名称
            this.sheetForm.neLevel = String(shareSheetInfo.neLevel); //网元等级
            this.sheetForm.alarmName = shareSheetInfo.alarmName; //告警名称
            this.sheetForm.alarmLocation = shareSheetInfo.alarmLocation; //告警定位

            this.processing();
            // console.log("this.sheetForm");
            // console.log(this.sheetForm);
            // 获取网络类型
            await this.getDictData(
              this.sheetForm.professionalType + "_comonpro_network_type",
              "networkTypeList"
            );
            this.sheetForm.networkType = echoData.networkType;
            if (this.sheetForm.sendContent == "") {
              this.sheetForm.sendContent =
                "1、光缆：**， ××××光缆在××省××（网元）与××（网元）之间中断，影响××××波分系统，承载的SDH系统保护倒换，已经派单给省公司，并电话通知处理，现在该省正在利用备用纤芯倒接;(该段落OLP正常切换;)(分公司正在协调处理;)2、设备：××， ××××系统中断，初步怀疑是××网元××板卡问题，承载的业务目前不受影响，已经派单给省公司，并电话通知处理，现在分公司正在利用备用波道倒接；(该系统承载的SDH系统正常倒换，业务不受影响；)(该系统承载的SDH系统倒换不正常，已经影响业务；)(影响的业务有（无）××重保电路。)";
            }
            this.$nextTick(() => {
              this.$refs.sheetForm.clearValidate();
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    getOrgInfo() {
      apiGetOrgInfo()
        .then(res => {
          if (res.status == "0") {
            this.orgInfoData = res.data;
            this.sheetForm.senderName = res.data.trueName;
            this.sheetForm.senderDeptName = res?.data?.orgInfo?.orgName ?? "";
            this.sheetForm.deptId = res?.data?.orgInfo?.orgId ?? "";
            this.sheetForm.senderDept = res?.data?.orgInfo?.orgId ?? "";
            this.sheetForm.sender = res?.data?.userName ?? "";

            this.areaCode = res?.data?.orgInfo?.areaCode ?? "";
            this.category = res?.data?.category ?? "";
            // this.getFaultAreaOptions();
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    //获取告警类别
    // getFaultAreaOptions() {
    //   let param = {
    //     areaCode: this.areaCode,
    //     category: this.category,
    //   };
    //   apiGetFaultArea(param)
    //     .then(res => {
    //       if (res.status == "0") {
    //         this.faultRegionOptions = res?.data ?? [];
    //       }
    //     })
    //     .catch(error => {
    //       console.log(error);
    //     });
    // },

    //保存
    onSheetSave() {
      if (this.sheetForm.sheetTitle == null || this.sheetForm.sheetTitle == ''){
        this.$message.warning('请填写主题后再做保存');
        return false;
      }
      if (this.sheetForm.acceptTimeLimit == null || this.sheetForm.acceptTimeLimit == ''){
        this.$message.warning('受理时限不能为空');
        return false;
      }
      // else{
        console.log(this.sheetForm);
        this.entering();
        this.$confirm("是否保存到草稿？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            this.saveDraftSubmit();
          })
          .catch(error => {
            console.log(error);
          });
      // }
    },

    saveDraftSubmit(){
      let formData = new FormData();
      if (this.importForm.attachmentFileList.length > 0) {
        for (let item of this.importForm.attachmentFileList) {
          formData.append("orderFiles", item.raw);
        }
      }
      if (this.importForm.incidenceFileList.length > 0) {
        for (let item of this.importForm.incidenceFileList) {
          formData.append("affectFiles", item.raw);
        }
      }

      let params = {};
      params.sheetInfo = JSON.parse(JSON.stringify(this.sheetForm));
      if (params.sheetInfo.isSendSms == "0") {
        params.sheetInfo.smsToUsername = "";
        params.sheetInfo.smsToDeptName = ""; //短信通知部门
        params.sheetInfo.smsToDeptCode = ""; //短信通知部门id
        params.sheetInfo.smsToUserid = ""; //短信通知人Id
        params.sheetInfo.smsToUserphone = "";
        params.sheetInfo.recipientDetailUserName = "";
        params.sheetInfo.sendContent = "";
      }
      params.sheetInfo.sheetStatus = 1; //1是草稿 2是提交到待办

      if (
        this.organizeForm.builderZsList &&
        this.organizeForm.builderZsList.length < 2
      )
      {
        params.sheetInfo.seizeOrders = '';
      }

      formData.append("jsonParam", JSON.stringify(params));
      console.log("---formData");
      console.log(formData);
      //保存到草稿

      this.sheetSaveLoading = true;
      apiCommonDraftOrBuild(formData)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("保存成功");
            this.closeAndTurnAroundMyDraft();
          } else {
            this.$message.error(res.data);
          }
          this.sheetSaveLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("保存失败");
          this.sheetSaveLoading = false;
        });
    },

    //提交
    onSheetSubmit() {
      this.entering();
      let self = this;
      this.$confirm("是否提交工单？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        closeOnClickModal: false,
        type: "warning",
      }).then(() => {
        this.$refs.sheetForm.validate(valid => {
          if (valid) {
            self.saveTodoSubmit();
          } else {
            return false;
          }
        });
      });
    },

    saveTodoSubmit(){
      let formData = new FormData();
      if (this.importForm.attachmentFileList.length > 0) {
        for (let item of this.importForm.attachmentFileList) {
          formData.append("orderFiles", item.raw);
        }
      }
      if (this.importForm.incidenceFileList.length > 0) {
        for (let item of this.importForm.incidenceFileList) {
          formData.append("affectFiles", item.raw);
        }
      }
      this.sheetCommitLoading = true;

      let params = {};
      params.sheetInfo = JSON.parse(JSON.stringify(this.sheetForm));
      if (params.sheetInfo.isSendSms == "0") {
        params.sheetInfo.smsToUsername = "";
        params.sheetInfo.smsToDeptName = ""; //短信通知部门
        params.sheetInfo.smsToDeptCode = ""; //短信通知部门id
        params.sheetInfo.smsToUserid = ""; //短信通知人Id
        params.sheetInfo.smsToUserphone = "";
        params.sheetInfo.recipientDetailUserName = "";
        params.sheetInfo.sendContent = "";
      }
      params.sheetInfo.sheetStatus = 2; //1是草稿 2是提交到待办
      if (
        this.organizeForm.builderZsList &&
        this.organizeForm.builderZsList.length < 2
      )
      {
        params.sheetInfo.seizeOrders = '';
      }

      formData.append("jsonParam", JSON.stringify(params));

      apiCommonDraftOrBuild(formData)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("工单提交成功");
            this.$refs.sheetForm.resetFields();
            this.closeAndTurnAroundTo();
          } else {
            this.$message.error(res.data);
          }
          this.sheetCommitLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error(error.data);
          this.sheetCommitLoading = false;
        });
    },

    closeAndTurnAroundMyDraft() {
      let self = this;
      self.$store.dispatch("tagsView/delView", self.route).then(() => {
        self.$destroy();
      });
      self.$router.push({
        name: "backbone_myDraft",
      });
    },
    closeAndTurnAroundTo() {
      let self = this;
      self.$store.dispatch("tagsView/delView", self.route).then(() => {
        self.$destroy();
      });
      self.$router.push({
        name: "backbone_toDoRepairOrder",
        query: {
          globalUniqueID: sessionStorage.getItem("globalUniqueID"),
          token: this.token,
        },
      });
    },

    // saveContactUser() {
    //   if (this.sheetForm.recipientUserId) {
    //     apiSaveContactUser({
    //       operateUser: this.userInfoData.userName,
    //       userNames: this.sheetForm.recipientUserId,
    //     })
    //       .then(res => {
    //         if (res.status == "0") {
    //           console.log(res);
    //         }
    //       })
    //       .catch(error => {
    //         console.log(error);
    //       });
    //   }
    // },

    onOpenPeopleDialog(diaSaveName) {
      this.isDiaOrgsUserTree = false;
      this.diaPeople.title = this.diaPeople.saveTitleMap[diaSaveName];
      this.diaPeople.saveName = diaSaveName;
      this.diaPeople.showOrgsTree =
        this.diaPeople.showOrgsTreeMap[diaSaveName] ?? true;
      // this.diaPeople.showContactUserTab =
      //   this.diaPeople.showContactUserTabMap[diaSaveName] ?? false;
      this.diaPeople.visible = true;
      this.$nextTick(() => {
        this.isDiaOrgsUserTree = true;
      });
    },

    onSavePeople(val) {
      this[this.diaPeople.saveName](val);
    },
    resetForm() {
      this.$refs["sheetForm"].resetFields();
      this.organizeForm.builderZsList = [];
      this.organizeForm.builderZsListCopy = [];
      this.organizeForm.builderZsName = "";
      this.organizeForm.builderCsList = [];
      this.organizeForm.builderCsListCopy = [];
      this.organizeForm.builderCsName = "";
      this.organizeForm.auditorList = [];
      this.organizeForm.auditorListCopy = [];
      this.organizeForm.auditorName = "";
      this.organizeForm.recipientList = [];
      this.organizeForm.recipientListCopy = [];
      this.organizeForm.recipientName = "";
    },

    //光缆名称选择
    // opticalCableSelect() {
    //   let jsonStr =
    //     '{"userId":"302785","username":"yangxm97","callSysId":"0001","callSysName":"电子运维"}';
    //   this.$nextTick(() => {
    //     document.querySelector("#fiberOpticCable").value = jsonStr;
    //     document.querySelector("#sub__fiberOpticCable").submit();
    //   });
    // },
    //工单主题
    // getInterfaceZxData() {
    //   let jsonStr =
    //     '{"userId":"302785","username":"yangxm97","callSysId":"0001","callSysName":"电子运维"}';
    //   this.$nextTick(() => {
    //     document.querySelector("#requestJson").value = jsonStr;
    //     document.querySelector("#deploy_simulation_page").submit();
    //   });
    // },
    //附件选择

    //附件选择
    attachmentBrowse() {
      this.attachmentDialogVisible = true;
    },
    changeFileData(data) {
      this.sheetForm.appendix = data.fileName;
      this.importForm.attachmentFileList = data.attachmentFileList;
      this.attachmentDialogVisible = false;
    },
    closeAttachmentDialog() {
      this.attachmentDialogVisible = false;
    },
    //影响业务列表选择
    incidenceBrowse() {
      this.incidenceDialogVisible = true;
    },
    changeIncidenceFileData(data) {
      this.sheetForm.appendixFileUrl = data.fileName;
      this.importForm.incidenceFileList = data.attachmentFileList;
      this.incidenceDialogVisible = false;
    },
    closeIncidenceDialog() {
      this.incidenceDialogVisible = false;
    },
    ngIncidenceBrowse() {
      this.ngIncidenceFileDownloadVisible = true;
    },
    ngAttachmentBrowse() {
      this.ngAttachmentDownloadVisible = true;
    },
    // stitchingAlgorithm(orgName, userName) {
    //   if (orgName.length !== 0 && userName.length !== 0) {
    //     return orgName + "," + userName;
    //   } else {
    //     if (orgName.length !== 0) {
    //       return orgName;
    //     } else if (userName.length !== 0) {
    //       return userName;
    //     } else {
    //       return "";
    //     }
    //   }
    // },
    // filterLordSentOrgNode(value, data) {
    //   if (!value) return true;
    //   return data.name.indexOf(value) !== -1;
    // },

    //所属专业改变
    professionalTypeChange() {
      this.sheetForm.networkType = "";
      this.sheetForm.stInfo = ""; //站址信息
      this.sheetForm.stName = ""; //站址名称
      this.sheetForm.longLatitude = ""; //站址经纬度 latitude
      this.sheetForm.stCategory = ""; //站址类别
      this.sheetForm.bsCode = ""; //基站ID
      this.sheetForm.bsName = ""; //基站名称
      this.sheetForm.alarmRegion = ""; //所属区域
      this.sheetForm.sceneName = ""; //所属场景
      this.sheetForm.bsLevel = ""; //基站级别
      this.sheetForm.bsType = ""; //基站类型
      this.sheetForm.alarmId = ""; //告警ID和名称
      this.sheetForm.alarmType = ""; //告警类别
      this.sheetForm.alarmDetail = ""; //告警详情
      this.sheetForm.alarmLevel = ""; //告警级别
      this.sheetForm.alarmOccurTime = ""; //告警发生时间
      this.sheetForm.faultReason = ""; //故障原因
      this.sheetForm.faultHandleMode = ""; //交互方式

      // this.sheetForm.regionName = ""; //地市
      this.sheetForm.neName = ""; //网元名称
      this.sheetForm.neLevel = ""; //网元等级
      // alarmId:"",//告警ID
      this.sheetForm.alarmName = ""; //告警名称
      // alarmCreateTime:"",//告警发生时间
      this.sheetForm.alarmLocation = ""; //告警定位
    },
    // 下拉查询
    getDictData(dictId, selectName) {
      this[selectName] = [];
      let param = {
        dictType: dictId,
      };
      apiDict(param)
        .then(res => {
          if (res.code == 200) {
            this[selectName] = res.data;
          }
        })
        .catch(error => {
          console.log(error);
          return false;
        });
    },
    // input 清空value
    btnClearable(val) {
      this.sheetForm[val] = "";
    },

    //建单人主送确定
    lordSentDetermine({ usersChecked, orgsChecked, selectionUser, contactSelectionUser, contactSelectionOrg }) {
      // console.log(usersChecked);
      // console.log(orgsChecked);
      console.log(selectionUser);

      if (contactSelectionUser && contactSelectionUser.length > 0) {
        contactSelectionUser.map(item => {
          let transferCs = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.userName;
          });
          if (transferCs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderZsList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }

      if (selectionUser && selectionUser.length > 0) {
        selectionUser.map(item => {
          let zs = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.userName;
          });
          if (zs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderZsList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      } else if (usersChecked && usersChecked.length > 0) {
        usersChecked.map(item => {
          let zs = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.id;
          });
          if (zs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderZsList.push({
              bz: "user",
              id: item.id,
              name: item.name,
              orgName: item.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }

      if (contactSelectionOrg && contactSelectionOrg.length > 0) {
        contactSelectionOrg.map(item => {
          let transferOrg = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.orgId;
          });
          if (transferOrg > -1) {
            this.$message({
              message: "选择的组织已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderZsList.push({
              bz: "org",
              id: item.orgId + "",
              orgName: item.fullOrgName,
            });
          }
        });
      }

      if (orgsChecked && orgsChecked.length > 0) {

        orgsChecked.map(item => {
          let zsOrg = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.id;
          });
          if (zsOrg > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderZsList.push({
              bz: "org",
              id: item.id,
              orgName: item.name,
            });
          }
        });
      }

      this.organizeForm.builderZsListCopy = JSON.parse(
        JSON.stringify(this.organizeForm.builderZsList)
      );
    },
    //抄送人确定
    ccDetermine({ usersChecked, orgsChecked, selectionUser, contactSelectionUser, contactSelectionOrg }) {

      if (contactSelectionUser && contactSelectionUser.length > 0) {
        contactSelectionUser.map(item => {
          let transferCs = this.organizeForm.builderCsList.findIndex(val => {
            return val.id === item.userName;
          });
          if (transferCs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderCsList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }

      if (selectionUser && selectionUser.length > 0) {
        selectionUser.map(item => {
          let cs = this.organizeForm.builderCsList.findIndex(val => {
            return val.id === item.userName;
          });
          if (cs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderCsList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      } else if (usersChecked && usersChecked.length > 0) {
        usersChecked.map(item => {
          let cs = this.organizeForm.builderCsList.findIndex(val => {
            return val.id === item.id;
          });
          if (cs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderCsList.push({
              bz: "user",
              id: item.id,
              name: item.name,
              orgName: item.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }

      if (contactSelectionOrg && contactSelectionOrg.length > 0) {
        contactSelectionOrg.map(item => {
          let transferCs = this.organizeForm.builderCsList.findIndex(val => {
            return val.id === item.orgId;
          });
          if (transferCs > -1) {
            this.$message({
              message: "选择的组织已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderCsList.push({
              bz: "org",
              id: item.orgId + "",
              orgName: item.fullOrgName,
            });
          }
        });
      }
      if (orgsChecked && orgsChecked.length > 0) {

        orgsChecked.map(item => {
          let csOrg = this.organizeForm.builderCsList.findIndex(val => {
            return val.id === item.id;
          });
          if (csOrg > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderCsList.push({
              bz: "org",
              id: item.id,
              orgName: item.name,
            });
          }
        });
      }

      this.organizeForm.builderCsListCopy = JSON.parse(
        JSON.stringify(this.organizeForm.builderCsList)
      );
    },
    //定性审核人确定
    auditorDetermine({ usersChecked, orgsChecked, selectionUser, contactSelectionUser, contactSelectionOrg }) {
      // console.log(usersChecked);
      // console.log(orgsChecked);
      // console.log(selectionUser);

      if (contactSelectionUser && contactSelectionUser.length > 0) {
        contactSelectionUser.map(item => {
          let transferCs = this.organizeForm.auditorList.findIndex(val => {
            return val.id === item.userName;
          });
          if (transferCs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.auditorList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }

      if (selectionUser && selectionUser.length > 0) {
        selectionUser.map(item => {
          let cs = this.organizeForm.auditorList.findIndex(val => {
            return val.id === item.userName;
          });
          if (cs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.auditorList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      } else if (usersChecked && usersChecked.length > 0)  {
        usersChecked.map(item => {
          let cs = this.organizeForm.auditorList.findIndex(val => {
            return val.id === item.id;
          });
          if (cs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.auditorList.push({
              bz: "user",
              id: item.id,
              name: item.name,
              orgName: item.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }

      this.organizeForm.auditorListCopy = JSON.parse(
        JSON.stringify(this.organizeForm.auditorList)
      );
    },
    //接收人确定
    recipientDetermine({ usersChecked, selectionUser, contactSelectionUser }) {
      if (contactSelectionUser && contactSelectionUser.length > 0) {
        contactSelectionUser.map(item => {
          let rec = this.organizeForm.recipientList.findIndex(val => {
            return val.id === item.userName;
          });
          if (rec > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.recipientList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (selectionUser && selectionUser.length > 0) {
        selectionUser.map(item => {
          let rec = this.organizeForm.recipientList.findIndex(val => {
            return val.id === item.userName;
          });
          if (rec > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.recipientList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      } else if (usersChecked && usersChecked.length > 0) {
        usersChecked.map(item => {
          let rec = this.organizeForm.recipientList.findIndex(val => {
            return val.id === item.id;
          });
          if (rec > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.recipientList.push({
              bz: "user",
              id: item.id,
              name: item.name,
              orgName: item.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      this.organizeForm.recipientListCopy = JSON.parse(
        JSON.stringify(this.organizeForm.recipientList)
      );
    },
    //多选移除
    toggleSelection(val) {
      if (this.multipleSelection.length == 0) {
        this.$message({
          type: "warning",
          message: "还没有选择移除的选项，请先选择要移除的选项！",
        });
        return;
      } else {
        this.multipleSelection.forEach(row => {
          this.handleClose(val, row);
        });
      }
      // this.multipleSelection;
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    //移除对象
    handleClose(val, tag) {
      if (val == "builderZs") {
        this.organizeForm.builderZsList.splice(
          this.arrayIndex(this.organizeForm.builderZsList, tag),
          1
        );
        this.organizeForm.builderZsListCopy.splice(
          this.arrayIndex(this.organizeForm.builderZsListCopy, tag),
          1
        );
      }
      if (val == "builderCs") {
        this.organizeForm.builderCsList.splice(
          this.arrayIndex(this.organizeForm.builderCsList, tag),
          1
        );
        this.organizeForm.builderCsListCopy.splice(
          this.arrayIndex(this.organizeForm.builderCsListCopy, tag),
          1
        );
      }
      if (val == "auditor") {
        this.organizeForm.auditorList.splice(
          this.arrayIndex(this.organizeForm.auditorList, tag),
          1
        );
        this.organizeForm.auditorListCopy.splice(
          this.arrayIndex(this.organizeForm.auditorListCopy, tag),
          1
        );
      }
      if (val == "recipient") {
        this.organizeForm.recipientList.splice(
          this.arrayIndex(this.organizeForm.recipientList, tag),
          1
        );
        this.organizeForm.recipientListCopy.splice(
          this.arrayIndex(this.organizeForm.recipientListCopy, tag),
          1
        );
      }
    },
    //找对应对象的索引值
    arrayIndex(val1, val2) {
      for (var i = 0, len = val1.length; i < len; i++) {
        var tag = val1[i];
        if (tag.id == val2.id) {
          return i;
        }
      }
    },
    //搜索
    search(val) {
      if (val == "builderZs" && this.organizeForm.builderZsList != null) {
        this.organizeForm.builderZsListCopy = [];
        this.organizeForm.builderZsList.forEach(row => {
          if (
            row.name == this.organizeForm.builderZsName ||
            row.orgName == this.organizeForm.builderZsName
          ) {
            this.organizeForm.builderZsListCopy.push(row);
          }
        });
      }
      if (val == "builderCs" && this.organizeForm.builderCsList != null) {
        this.organizeForm.builderCsListCopy = [];
        this.organizeForm.builderCsList.forEach(row => {
          if (
            row.name == this.organizeForm.builderCsName ||
            row.orgName == this.organizeForm.builderCsName
          ) {
            this.organizeForm.builderCsListCopy.push(row);
          }
        });
      }
      if (val == "auditor" && this.organizeForm.auditorList != null) {
        this.organizeForm.auditorListCopy = [];
        this.organizeForm.auditorList.forEach(row => {
          if (
            row.name == this.organizeForm.auditorName ||
            row.orgName == this.organizeForm.auditorName
          ) {
            this.organizeForm.auditorListCopy.push(row);
          }
        });
      }
      if (val == "recipient" && this.organizeForm.recipientList != null) {
        this.organizeForm.recipientListCopy = [];
        this.organizeForm.recipientList.forEach(row => {
          if (
            row.name == this.organizeForm.recipientName ||
            row.orgName == this.organizeForm.recipientName
          ) {
            this.organizeForm.recipientListCopy.push(row);
          }
        });
      }
    },
    //清除搜索项
    clear(val) {
      if (val == "builderZs") {
        this.organizeForm.builderZsName = "";
        this.organizeForm.builderZsListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.builderZsList)
        );
      }
      if (val == "builderCs") {
        this.organizeForm.builderCsName = "";
        this.organizeForm.builderCsListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.builderCsList)
        );
      }
      if (val == "auditor") {
        this.organizeForm.auditorName = "";
        this.organizeForm.auditorListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.auditorList)
        );
      }
      if (val == "recipient") {
        this.organizeForm.recipientName = "";
        this.organizeForm.recipientListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.recipientList)
        );
      }
    },
    entering() {
      this.sheetForm.agentMan = "";
      this.sheetForm.agentManId = "";
      this.sheetForm.agentManDetail = "";
      this.sheetForm.agentDeptName = "";
      this.sheetForm.agentDeptCode = "";
      this.sheetForm.copyMan = "";
      this.sheetForm.copyManId = "";
      this.sheetForm.copyManDetail = "";
      this.sheetForm.copyDeptName = "";
      this.sheetForm.copyDeptCode = "";
      this.sheetForm.auditorName = "";
      this.sheetForm.auditorId = "";
      this.sheetForm.auditorManDetail = "";
      this.sheetForm.smsToUsername = "";
      this.sheetForm.smsToUserid = "";
      this.sheetForm.recipientDetailUserName = "";

      if (
        this.organizeForm.builderZsList &&
        this.organizeForm.builderZsList.length > 0
      ) {
        let userList = this.organizeForm.builderZsList.filter((item, index) => {
          return item.bz == "user";
        });
        let orgList = this.organizeForm.builderZsList.filter((item, index) => {
          return item.bz == "org";
        });
        if (userList && userList.length > 0) {
          let usersCheckedName = userList.map(item => {
            return item.name;
          });
          this.sheetForm.agentMan = usersCheckedName.join(",");
          let usersCheckedId = userList.map(item => {
            return item.id;
          });
          this.sheetForm.agentManId = usersCheckedId.join(",");
          const userDetailName = userList.map(item => {
            return item.name + "-" + item.orgName + "-" + item.mobilePhone;
          });
          this.sheetForm.agentManDetail = userDetailName.join(",");
        }
        if (orgList && orgList.length > 0) {
          let orgsCheckedName = orgList.map(item => {
            return item.orgName;
          });
          this.sheetForm.agentDeptName = orgsCheckedName.join(",");
          let orgsCheckedId = orgList.map(item => {
            return item.id;
          });
          this.sheetForm.agentDeptCode = orgsCheckedId.join(",");
        }
      }
      if (
        this.organizeForm.builderCsList &&
        this.organizeForm.builderCsList.length > 0
      ) {
        let userList = this.organizeForm.builderCsList.filter((item, index) => {
          return item.bz == "user";
        });
        let orgList = this.organizeForm.builderCsList.filter((item, index) => {
          return item.bz == "org";
        });
        if (userList && userList.length > 0) {
          let usersCheckedName = userList.map(item => {
            return item.name;
          });
          this.sheetForm.copyMan = usersCheckedName.join(",");
          let usersCheckedId = userList.map(item => {
            return item.id;
          });
          this.sheetForm.copyManId = usersCheckedId.join(",");

          const userDetailName = userList.map(item => {
            return item.name + "-" + item.orgName + "-" + item.mobilePhone;
          });
          this.sheetForm.copyManDetail = userDetailName.join(",");
        }
        if (orgList && orgList.length > 0) {
          let orgsCheckedName = orgList.map(item => {
            return item.orgName;
          });
          this.sheetForm.copyDeptName = orgsCheckedName.join(",");
          let orgsCheckedId = orgList.map(item => {
            return item.id;
          });
          this.sheetForm.copyDeptCode = orgsCheckedId.join(",");
        }
      }
      if (
        this.organizeForm.auditorList &&
        this.organizeForm.auditorList.length > 0
      ) {
        let userList = this.organizeForm.auditorList.filter((item, index) => {
          return item.bz == "user";
        });

        if (userList && userList.length > 0) {
          let usersCheckedName = userList.map(item => {
            return item.name;
          });
          this.sheetForm.auditorName = usersCheckedName.join(",");
          let usersCheckedId = userList.map(item => {
            return item.id;
          });
          this.sheetForm.auditorId = usersCheckedId.join(",");
          const userDetailName = userList.map(item => {
            return item.name + "-" + item.orgName + "-" + item.mobilePhone;
          });
          this.sheetForm.auditorManDetail = userDetailName.join(",");
        }
      }

      if (
        this.organizeForm.recipientList &&
        this.organizeForm.recipientList.length > 0
      ) {
        let userList = this.organizeForm.recipientList.filter((item, index) => {
          return item.bz == "user";
        });
        if (userList && userList.length > 0) {
          let usersCheckedName = userList.map(item => {
            return item.name;
          });
          this.sheetForm.smsToUsername = usersCheckedName.join(",");
          let usersCheckedId = userList.map(item => {
            return item.id;
          });
          this.sheetForm.smsToUserid = usersCheckedId.join(",");
          const userDetailName = userList.map(item => {
            return item.name + "-" + item.orgName + "-" + item.mobilePhone;
          });
          this.sheetForm.recipientDetailUserName = userDetailName.join(",");
        }
      }
    },
    processing() {
      //主送人数据处理
      if (
        this.sheetForm.agentManId != null &&
        this.sheetForm.agentManId != "" &&
        this.sheetForm.agentManDetail != null &&
        this.sheetForm.agentManDetail != ""
      ) {
        let usersCheckedName = [];
        let usersCheckedOrgName = [];
        let usersCheckedMobilePhone = [];
        let usersCheckedId = this.sheetForm.agentManId.split(",");
        let usersCheckedManDetail = this.sheetForm.agentManDetail.split(",");
        usersCheckedManDetail.forEach(element => {
          let userCheckedManDetail = element.split("-");
          usersCheckedName.push(userCheckedManDetail[0]);
          usersCheckedOrgName.push(userCheckedManDetail[1]);
          usersCheckedMobilePhone.push(userCheckedManDetail[2]);
        });
        for (var i = 0; i < usersCheckedId.length; i++) {
          this.organizeForm.builderZsList.push({
            bz: "user",
            id: usersCheckedId[i],
            name: usersCheckedName[i],
            orgName: usersCheckedOrgName[i],
            mobilePhone: usersCheckedMobilePhone[i],
          });
        }
        this.organizeForm.builderZsListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.builderZsList)
        );
      }
      //主送组织数据处理
      if (
        this.sheetForm.agentDeptCode != null &&
        this.sheetForm.agentDeptCode != "" &&
        this.sheetForm.agentDeptName != null &&
        this.sheetForm.agentDeptName != ""
      ) {
        let orgsCheckedName = this.sheetForm.agentDeptName.split(",");
        let orgsCheckedId = this.sheetForm.agentDeptCode.split(",");
        for (var j = 0; j < orgsCheckedId.length; j++) {
          this.organizeForm.builderZsList.push({
            bz: "org",
            id: orgsCheckedId[j],
            orgName: orgsCheckedName[j],
          });
        }
        this.organizeForm.builderZsListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.builderZsList)
        );
      }
      //抄送人数据处理
      if (
        this.sheetForm.copyManId != null &&
        this.sheetForm.copyManId != "" &&
        this.sheetForm.copyManDetail != null &&
        this.sheetForm.copyManDetail != ""
      ) {
        let usersCheckedName = [];
        let usersCheckedOrgName = [];
        let usersCheckedMobilePhone = [];
        let usersCheckedId = this.sheetForm.copyManId.split(",");
        let usersCheckedManDetail = this.sheetForm.copyManDetail.split(",");
        usersCheckedManDetail.forEach(element => {
          let userCheckedManDetail = element.split("-");
          usersCheckedName.push(userCheckedManDetail[0]);
          usersCheckedOrgName.push(userCheckedManDetail[1]);
          usersCheckedMobilePhone.push(userCheckedManDetail[2]);
        });
        for (var k = 0; k < usersCheckedId.length; k++) {
          this.organizeForm.builderCsList.push({
            bz: "user",
            id: usersCheckedId[k],
            name: usersCheckedName[k],
            orgName: usersCheckedOrgName[k],
            mobilePhone: usersCheckedMobilePhone[k],
          });
        }
        this.organizeForm.builderCsListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.builderCsList)
        );
      }
      //抄送组织数据处理
      if (
        this.sheetForm.copyDeptCode != null &&
        this.sheetForm.copyDeptCode != "" &&
        this.sheetForm.copyDeptName != null &&
        this.sheetForm.copyDeptName != ""
      ) {
        let orgsCheckedName = this.sheetForm.copyDeptName.split(",");
        let orgsCheckedId = this.sheetForm.copyDeptCode.split(",");
        for (var l = 0; l < orgsCheckedId.length; l++) {
          this.organizeForm.builderCsList.push({
            bz: "org",
            id: orgsCheckedId[l],
            orgName: orgsCheckedName[l],
          });
        }
        this.organizeForm.builderCsListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.builderCsList)
        );
      }
      //接收人数据处理
      if (
        this.sheetForm.smsToUserid != null &&
        this.sheetForm.smsToUserid != "" &&
        this.sheetForm.recipientDetailUserName != null &&
        this.sheetForm.recipientDetailUserName != ""
      ) {
        let usersCheckedName = [];
        let usersCheckedOrgName = [];
        let usersCheckedMobilePhone = [];
        let usersCheckedId = this.sheetForm.smsToUserid.split(",");
        let usersCheckedManDetail = this.sheetForm.recipientDetailUserName.split(
          ","
        );
        usersCheckedManDetail.forEach(element => {
          let userCheckedManDetail = element.split("-");
          usersCheckedName.push(userCheckedManDetail[0]);
          usersCheckedOrgName.push(userCheckedManDetail[1]);
          usersCheckedMobilePhone.push(userCheckedManDetail[2]);
        });
        for (var a = 0; a < usersCheckedId.length; a++) {
          this.organizeForm.recipientList.push({
            bz: "user",
            id: usersCheckedId[a],
            name: usersCheckedName[a],
            orgName: usersCheckedOrgName[a],
            mobilePhone: usersCheckedMobilePhone[a],
          });
        }
        this.organizeForm.recipientListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.recipientList)
        );
      }
      //定性审核人数据处理
      if (
        this.sheetForm.auditorId != null &&
        this.sheetForm.auditorId != "" &&
        this.sheetForm.auditorManDetail != null &&
        this.sheetForm.auditorManDetail != ""
      ) {
        let usersCheckedName = [];
        let usersCheckedOrgName = [];
        let usersCheckedMobilePhone = [];
        let usersCheckedId = this.sheetForm.auditorId.split(",");
        let usersCheckedManDetail = this.sheetForm.auditorManDetail.split(",");
        usersCheckedManDetail.forEach(element => {
          let userCheckedManDetail = element.split("-");
          usersCheckedName.push(userCheckedManDetail[0]);
          usersCheckedOrgName.push(userCheckedManDetail[1]);
          usersCheckedMobilePhone.push(userCheckedManDetail[2]);
        });
        for (var b = 0; b < usersCheckedId.length; b++) {
          this.organizeForm.auditorList.push({
            bz: "user",
            id: usersCheckedId[b],
            name: usersCheckedName[b],
            orgName: usersCheckedOrgName[b],
            mobilePhone: usersCheckedMobilePhone[b],
          });
        }
        this.organizeForm.auditorListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.auditorList)
        );
      }
    },
  },
};
</script>

<style>
  .my-popper{
    white-space: pre !important;
  }
</style>

<style lang="scss" scoped>
.draft-eidt-page {
  .head-title {
    font-size: 24px;
    line-height: 28px;
  }
  .head-handle-wrap {
    text-align: right;
  }
  .card-title {
    font-size: 16px;
    letter-spacing: 0;
    padding-left: 10px;
  }

  ::v-deep .el-tree {
    padding-top: 15px;
    padding-left: 10px;
    // 不可全选样式
    .el-tree-node {
      .is-leaf + .el-checkbox .el-checkbox__inner {
        display: inline-block;
      }
      .el-checkbox .el-checkbox__inner {
        display: none;
      }
    }
  }

  .select_style {
    background: #b50b14;
    border-radius: 2px;
    border-color: #b50b14;
  }

}
</style>
