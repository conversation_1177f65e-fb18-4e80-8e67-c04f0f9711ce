<template>
  <el-card shadow="always" class="card" :body-style="{ padding: '10px 24px' }">
    <div class="header clearfix">
      <span class="header-title">关联诊断</span>
      <template v-if="isShowEva && basicWorkOrderData.professionalType != 3">
        <evaluation
          :woId="woId"
          :relationDiagnosis="relationDiagnosis"
        ></evaluation>
      </template>
      <div class="header-right">
        <el-button type="primary" size="mini" @click="getRelationDiagnosis"
          >刷新</el-button
        >
      </div>
    </div>
    <div class="content" v-loading="contentLoading">
      <div v-if="relationDiagnosis.isPPShow">
        <el-collapse v-model="activeNames">
          <el-collapse-item name="1">
            <span class="collapse-title" slot="title">诊断分析字段</span>
            <el-descriptions class="descriptions" style="padding-top: 15px">
              <el-descriptions-item label="预处理状态">{{
                relationDiagnosis.PPStatusName
              }}</el-descriptions-item>
              <el-descriptions-item label="根因域">{{
                relationDiagnosis.PPCauseDomainName
              }}</el-descriptions-item>
              <el-descriptions-item label="根因类型">{{
                relationDiagnosis.PPAlarmReasonName
              }}</el-descriptions-item>
              <el-descriptions-item label="设备型号">
                {{ relationDiagnosis.equipType }}
              </el-descriptions-item>
              <el-descriptions-item label="软件版本">{{
                relationDiagnosis.softwareVersion
              }}</el-descriptions-item>
              <el-descriptions-item label="硬件版本">
                {{ relationDiagnosis.hardwareVersion }}
              </el-descriptions-item>
              <el-descriptions-item label="根因位置" :span="1">
                <div
                  style="white-space: pre-wrap"
                  v-html="relationDiagnosis.ppCausePosition"
                >
                  {{ relationDiagnosis.ppCausePosition }}
                </div>
              </el-descriptions-item>

              <el-descriptions-item label="业务影响范围" :span="2">
                {{ relationDiagnosis.effectRange }}
              </el-descriptions-item>
              <el-descriptions-item label="预处理过程" :span="3">
                <div
                  style="white-space: pre-wrap"
                  v-html="relationDiagnosis.PPProcess"
                >
                  {{ relationDiagnosis.PPProcess }}
                </div>
              </el-descriptions-item>
              <el-descriptions-item label="预处理结果" :span="3">
                <div
                  style="white-space: pre-wrap"
                  v-html="relationDiagnosis.PPResult"
                ></div>
              </el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
        </el-collapse>
      </div>

      <el-collapse
        v-model="gzTabOpen"
        v-if="
          (relationDiagnosis.isPPShow && isShowOtdr) ||
          (relationDiagnosis.isPPShow && isShowTopo) ||
          (relationDiagnosis.isPPShow && isShowFlowChart) ||
          isShowGuangshuai
        "
      >
        <el-collapse-item name="1">
          <span class="collapse-title" slot="title">故障定位</span>
          <div class="gz-position">
            <el-radio-group v-model="gzTabNow" size="mini">
              <el-radio-button
                label="OTDR断点图"
                v-if="isShowOtdr"
              ></el-radio-button>
              <el-radio-button
                label="拓扑图"
                v-if="isShowTopo"
              ></el-radio-button>
              <el-radio-button
                label="诊断流程图"
                v-if="isShowFlowChart"
              ></el-radio-button>
              <el-radio-button
                label="光衰趋势图"
                v-if="isShowGuangshuai"
              ></el-radio-button>
            </el-radio-group>
            <section v-if="gzTabNow == 'OTDR断点图'">
              <OtdrGis :otdrData="otdrData"></OtdrGis>
            </section>
            <section v-if="gzTabNow == '拓扑图'">
              <iframe
                v-if="topoSrc"
                id="topo"
                :src="topoSrc"
                width="100%"
                title=""
                style="height: 500px; overflow: auto; border: none"
              ></iframe>
            </section>
            <section v-if="gzTabNow == '诊断流程图'" style="height: 600px">
              <FlowView :workOrderId="woId" type="pc"></FlowView>
            </section>
            <section v-if="gzTabNow == '光衰趋势图'" style="height: 500px">
              <Guangshuai :guangshuaiData="guangshuaiData"></Guangshuai>
            </section>
          </div>
        </el-collapse-item>
      </el-collapse>

      <div v-if="relationDiagnosis.isRoomShow">
        <el-collapse v-model="activeNames">
          <el-collapse-item name="1">
            <span class="collapse-title" slot="title">机房设备信息</span>
            <el-descriptions class="descriptions" style="padding-top: 15px">
              <el-descriptions-item
                label="产权归属"
                v-if="
                  relationDiagnosis.siteProperty !== null &&
                  relationDiagnosis.siteProperty !== ''
                "
                >{{ relationDiagnosis.siteProperty }}</el-descriptions-item
              >
              <el-descriptions-item
                label="是否共享"
                v-if="
                  relationDiagnosis.isShare !== null &&
                  relationDiagnosis.isShare !== ''
                "
                >{{ relationDiagnosis.isShare }}</el-descriptions-item
              >
              <el-descriptions-item
                label="共享单位"
                v-if="
                  relationDiagnosis.shareUnit !== null &&
                  relationDiagnosis.shareUnit !== ''
                "
                >{{ relationDiagnosis.shareUnit }}</el-descriptions-item
              >
              <el-descriptions-item
                label="设备总数（个）"
                v-if="
                  relationDiagnosis.equCount !== null &&
                  relationDiagnosis.equCount !== ''
                "
              >
                {{ relationDiagnosis.equCount }}
              </el-descriptions-item>
              <el-descriptions-item
                label="核心网设备数（个）"
                v-if="
                  relationDiagnosis.equHx !== null &&
                  relationDiagnosis.equHx !== ''
                "
                >{{ relationDiagnosis.equHx }}</el-descriptions-item
              >
              <el-descriptions-item
                label="动环网设备数（个）"
                v-if="
                  relationDiagnosis.equDh !== null &&
                  relationDiagnosis.equDh !== ''
                "
              >
                {{ relationDiagnosis.equDh }}
              </el-descriptions-item>
              <el-descriptions-item
                label="数据网设备数（个）"
                v-if="
                  relationDiagnosis.equSj !== null &&
                  relationDiagnosis.equSj !== ''
                "
              >
                {{ relationDiagnosis.equSj }}
              </el-descriptions-item>
              <el-descriptions-item
                label="无线网设备数（个）"
                v-if="
                  relationDiagnosis.equWx !== null &&
                  relationDiagnosis.equWx !== ''
                "
                >{{ relationDiagnosis.equWx }}</el-descriptions-item
              >
              <el-descriptions-item
                label="无线网设备影响用户数（个）"
                v-if="
                  relationDiagnosis.wxAffectUsers !== null &&
                  relationDiagnosis.wxAffectUsers !== ''
                "
              >
                {{ relationDiagnosis.wxAffectUsers }}
              </el-descriptions-item>
              <el-descriptions-item
                label="接入网OLT设备数（个）"
                v-if="
                  relationDiagnosis.equJr !== null &&
                  relationDiagnosis.equJr !== ''
                "
              >
                {{ relationDiagnosis.equJr }}
              </el-descriptions-item>
              <el-descriptions-item
                label="接入网OLT设备影响用户数（个）"
                v-if="
                  relationDiagnosis.dhAffectUsers !== null &&
                  relationDiagnosis.dhAffectUsers !== ''
                "
                >{{ relationDiagnosis.dhAffectUsers }}</el-descriptions-item
              >
              <el-descriptions-item
                label="传输网设备数（个）"
                v-if="
                  relationDiagnosis.equCs !== null &&
                  relationDiagnosis.equCs !== ''
                "
              >
                {{ relationDiagnosis.equCs }}
              </el-descriptions-item>
              <el-descriptions-item
                label="传输网设备影响电路数（个）"
                v-if="
                  relationDiagnosis.csAffectCircuit !== null &&
                  relationDiagnosis.csAffectCircuit !== ''
                "
                >{{ relationDiagnosis.csAffectCircuit }}</el-descriptions-item
              >
            </el-descriptions>
          </el-collapse-item>
          <el-collapse-item name="2" v-if="relationDiagnosis.isDiagnosisShow">
            <span class="collapse-title" slot="title">诊断结果</span>
            <el-descriptions class="descriptions" style="padding-top: 15px">
              <el-descriptions-item
                label="预测机房备电时长"
                :span="3"
                v-if="
                  relationDiagnosis.forecastDuration !== null &&
                  relationDiagnosis.forecastDuration !== ''
                "
              >
                <div style="color: #b50b14">
                  {{ relationDiagnosis.forecastDuration }}
                </div>
              </el-descriptions-item>
              <el-descriptions-item
                label="厂商"
                v-if="
                  relationDiagnosis.vendor !== null &&
                  relationDiagnosis.vendor !== ''
                "
                >{{ relationDiagnosis.vendor }}</el-descriptions-item
              >
              <el-descriptions-item
                label="负载电流（A）"
                v-if="
                  relationDiagnosis.current !== null &&
                  relationDiagnosis.current !== ''
                "
                >{{ relationDiagnosis.current }}</el-descriptions-item
              >
              <el-descriptions-item
                label="电池电压（V）"
                v-if="
                  relationDiagnosis.outputXoltage !== null &&
                  relationDiagnosis.outputXoltage !== ''
                "
              >
                {{ relationDiagnosis.outputXoltage }}
              </el-descriptions-item>
              <el-descriptions-item
                label="蓄电池额定容量（AH）"
                v-if="
                  relationDiagnosis.batteryCapacity !== null &&
                  relationDiagnosis.batteryCapacity !== ''
                "
                >{{ relationDiagnosis.batteryCapacity }}</el-descriptions-item
              >
              <el-descriptions-item
                label="温度（°C）"
                v-if="
                  relationDiagnosis.temperature !== null &&
                  relationDiagnosis.temperature !== ''
                "
              >
                {{ relationDiagnosis.temperature }}
              </el-descriptions-item>
              <el-descriptions-item
                label="湿度（%rh）"
                v-if="
                  relationDiagnosis.humidness !== null &&
                  relationDiagnosis.humidness !== ''
                "
              >
                {{ relationDiagnosis.humidness }}
              </el-descriptions-item>
              <el-descriptions-item
                label="投产时间"
                v-if="
                  relationDiagnosis.startUpTime !== null &&
                  relationDiagnosis.startUpTime !== ''
                "
                >{{ relationDiagnosis.startUpTime }}</el-descriptions-item
              >
              <el-descriptions-item
                label="寿命使用真实日期"
                v-if="
                  relationDiagnosis.usingDate !== null &&
                  relationDiagnosis.usingDate !== ''
                "
              >
                {{ relationDiagnosis.usingDate }}
              </el-descriptions-item>
              <el-descriptions-item
                label="历史告警统计放电时长（分钟）"
                v-if="
                  relationDiagnosis.historyAlarmDuration !== null &&
                  relationDiagnosis.historyAlarmDuration !== ''
                "
              >
                {{ relationDiagnosis.historyAlarmDuration }}
              </el-descriptions-item>
              <el-descriptions-item
                label="蓄电池理论放电时长（分钟）"
                v-if="
                  relationDiagnosis.batteryDischargeDuration !== null &&
                  relationDiagnosis.batteryDischargeDuration !== ''
                "
                >{{
                  relationDiagnosis.batteryDischargeDuration
                }}</el-descriptions-item
              >
              <el-descriptions-item
                label="最早投入使用时间"
                v-if="
                  relationDiagnosis.installTimeDuration !== null &&
                  relationDiagnosis.installTimeDuration !== ''
                "
              >
                {{ relationDiagnosis.installTimeDuration }}
              </el-descriptions-item>
              <el-descriptions-item
                label="最晚使用寿命"
                v-if="
                  relationDiagnosis.servicelTimeDuration !== null &&
                  relationDiagnosis.servicelTimeDuration !== ''
                "
                >{{
                  relationDiagnosis.servicelTimeDuration
                }}</el-descriptions-item
              >
            </el-descriptions>
          </el-collapse-item>
        </el-collapse>
      </div>
      <div v-else-if="relationDiagnosis.isAIShow">
        <el-collapse v-model="activeNames">
          <el-collapse-item name="1">
            <span class="collapse-title" slot="title">
              诊断分析字段<span style="color: #b50b14"
                >（以下诊断分析数据来源于网络AI中心，仅供参考。按照故障原因可能性"从高到低"展示。）</span
              >
            </span>
            <div style="padding: 20px 20px 0px 20px">
              <el-row
                class="aiClass bgCla"
                v-if="
                  relationDiagnosis.analCauseDomainName1 != null &&
                  relationDiagnosis.analCauseDomainName1 != ''
                "
              >
                <el-col :span="1" class="imgCol">
                  <img src="../assets/1.png" />
                </el-col>
                <el-col :span="7">
                  <span
                    >故障所属专业：{{
                      relationDiagnosis.analCauseDomainName1
                    }}</span
                  >
                </el-col>
                <el-col :span="7">
                  <span
                    >故障分类：{{ relationDiagnosis.analAlarmTypeName1 }}</span
                  >
                </el-col>
                <el-col :span="7">
                  <span>故障原因：{{ relationDiagnosis.analResult1 }}</span>
                </el-col>
              </el-row>
              <el-row
                class="aiClass"
                v-if="
                  relationDiagnosis.analCauseDomainName2 != null &&
                  relationDiagnosis.analCauseDomainName2 != ''
                "
              >
                <el-col :span="1" class="imgCol">
                  <img src="../assets/2.png" />
                </el-col>
                <el-col :span="7">
                  <span
                    >故障所属专业：{{
                      relationDiagnosis.analCauseDomainName2
                    }}</span
                  >
                </el-col>
                <el-col :span="7">
                  <span
                    >故障分类：{{ relationDiagnosis.analAlarmTypeName2 }}</span
                  >
                </el-col>
                <el-col :span="7">
                  <span>故障原因：{{ relationDiagnosis.analResult2 }}</span>
                </el-col>
              </el-row>
              <el-row
                class="aiClass bgCla"
                v-if="
                  relationDiagnosis.analCauseDomainName3 != null &&
                  relationDiagnosis.analCauseDomainName3 != ''
                "
              >
                <el-col :span="1" class="imgCol">
                  <img src="../assets/3.png" />
                </el-col>
                <el-col :span="7">
                  <span
                    >故障所属专业：{{
                      relationDiagnosis.analCauseDomainName3
                    }}</span
                  >
                </el-col>
                <el-col :span="7">
                  <span
                    >故障分类：{{ relationDiagnosis.analAlarmTypeName3 }}</span
                  >
                </el-col>
                <el-col :span="7">
                  <span>故障原因：{{ relationDiagnosis.analResult3 }}</span>
                </el-col>
              </el-row>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>

      <!--      无线网才有这个模块  -->
      <template v-if="basicWorkOrderData.professionalType == 7">
        <command-query :basicWorkOrderData="basicWorkOrderData">
        </command-query>
      </template>
      <!--      传输网才有这个模块  -->
      <template v-if="basicWorkOrderData.professionalType == 3">
        <status-query
          :basicWorkOrderData="basicWorkOrderData"
          :transRelationData="transRelationData"
          v-if="showTransRelation"
        >
        </status-query>
      </template>
    </div>
  </el-card>
</template>

<script>
import {
  apiGetRelationDiagnosis,
  apiGetOtdrData,
  apiGetTransRelationDiagnosis,
  apiGuangshuai,
} from "../api/CommonApi";
import { getFlowConnects } from "../../../flow/flow-api";
import CommandQuery from "../../../../components/CommandQuery/CommandQuery";
import StatusQuery from "../../../../components/CommandQuery/CommandQueryGGL";
import Evaluation from "../../../../components/Evaluate/Evaluation";
// OTDR断点图
import OtdrGis from "@/plugin/backbone/modules/otdr/components/OtdrGis";
// 诊断流程图
import FlowView from "@/plugin/backbone/modules/flow/views/FlowView.vue";
// 光衰趋势图
import Guangshuai from "@/components/Guangshuai/Line";
export default {
  name: "RelationDiagnosis",
  components: {
    CommandQuery,
    StatusQuery,
    Evaluation,
    OtdrGis,
    FlowView,
    Guangshuai,
  },
  props: {
    woId: String,
    basicWorkOrderData: Object,
  },
  data() {
    return {
      relationDiagnosis: {},
      // http://************:8088/nfm3/netfm3topo//topoapi/custom/dict/topoParams?alarmId=590290398_4240619801_726313641_3239917128&eventTime=2023-02-15+17:20:00
      // topoBasicUrl: "/nfm3/netfm3topo/#/layoutTopo/Topo_IframeSysTopo",
      // topoBasicUrl: "/nfm3/netfm3topo/#/topoapi/custom/dict/topoParams",//?alarmId=13448841614902864978669551118735541374&eventTime=2023-02-14+13:06:03
      topoSrc: "",
      contentLoading: false,
      timer: null,
      activeNames: [],
      gzTabOpen: ["1"],
      gzTabNow: "OTDR断点图",
      isShowEva: false,
      isShowTopo: false,
      isShowFlowChart: false,
      isShowOtdr: false,
      isShowGuangshuai: false,

      otdrData: {},
      transRelationData: {},
      showTransRelation: false,
      isShowGuangshuai: true,
      guangshuaiData: [],
    };
  },
  computed: {
    setForecastDuration() {
      let time = this.relationDiagnosis.forecastDuration;
      if (time == "-") {
        return "-";
      } else if (time == "") {
        return "";
      } else {
        if (Math.floor(time / (24 * 60)) == 0) {
          if (Math.floor(Math.abs(time) / 60) == 0) {
            return `${time % 60}分钟`;
          } else {
            return `${Math.floor(time / 60)}小时${time % 60}分钟`;
          }
        } else {
          return `${Math.floor(time / (24 * 60))}天${Math.floor(
            (time % 1440) / 60
          )}小时${(time % 1440) % 60}分钟`;
        }
      }
    },
  },
  async mounted() {
    await this.getRelationDiagnosis();

    this.getTransRelation();
    getFlowConnects(this.woId).then(resp => {
      this.isShowFlowChart = resp?.data?.adxStatus == "YES";
      if (!this.isShowOtdr && !this.isShowTopo && this.isShowFlowChart) {
        this.gzTabNow = "诊断流程图";
      }
    });

    // 获取光衰数据
    this.getGuangshuai();
  },
  destroyed() {
    this.clearTimer();
  },
  methods: {
    getTransRelation() {
      let self = this;
      let param = {
        woId: this.woId,
      };
      apiGetTransRelationDiagnosis(param)
        .then(res => {
          if (res.status == "0") {
            self.transRelationData = res?.data ?? {};
            self.showTransRelation = true;
          }
        })
        .catch(error => {
          console.log(error);
          self.showTransRelation = true;
        });
    },
    handleOTDR() {
      // ppPointLocation有值的情况下调用接口获取otdrData
      if (
        this.relationDiagnosis.ppPointLocation &&
        this.relationDiagnosis.ppPointLocation != "" &&
        this.relationDiagnosis.ppPointLocation != "无"
      ) {
        let alarmParams = {
          alarmUniqueId: this.basicWorkOrderData.alarmStaId,
          endTime: this.basicWorkOrderData.alarmCreateTime,
        };
        debugger;
        this.getOtdrData(alarmParams).then(res => {
          if (res) {
            this.isShowOtdr = true;
            if (this.isShowOtdr) {
              this.gzTabNow = "OTDR断点图";
            }
          } else {
            //展示老GIS的链接
            this.showOldGisUrl(this.relationDiagnosis.PPResult);
          }
        });
      }
    },
    getRelationDiagnosis() {
      this.contentLoading = true;
      let param = {
        woId: this.woId,
      };
      let self = this;
      apiGetRelationDiagnosis(param)
        .then(res => {
          if (res.status == "0") {
            if (res.data.length > 0) {
              self.relationDiagnosis = res.data[0];
              self.isShowEva = true;
              self.handleOTDR();
              self.handleData();
            }
          }
          this.contentLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.contentLoading = false;
        });
    },
    handleData() {
      let self = this;
      // 数据来源为动环机房数据时isRoomShow，反馈问题为：蓄电池备电时长不准确、蓄电池基础信息不准确、机房基础信息不准确、其他。
      // 数据来源为上海AI中心的预诊断数据时isAIShow，无反馈问题；
      // 数据来源为排障中心预诊断数据isPPShow，反馈问题为：预处理信息不准确、故障定位图不准确、其他。

      // 预处理状态 有值,折叠板展开显示
      if (self.relationDiagnosis.PPStatusName != "") {
        self.activeNames = ["1"];
      }
      if (
        !self.relationDiagnosis.isPPShow &&
        self.relationDiagnosis.isRoomShow
      ) {
        self.activeNames = ["1", "2"];
      }
      if (
        !self.relationDiagnosis.isPPShow &&
        !self.relationDiagnosis.isRoomShow &&
        self.relationDiagnosis.isAIShow
      ) {
        self.activeNames = ["1"];
      }

      self.handleData1();
      self.handleData2();
    },
    handleData1() {
      let self = this;
      let PPResult = self.relationDiagnosis?.PPResult ?? "";
      if (
        PPResult.indexOf("断点段落文字描述：") != "-1" &&
        PPResult.indexOf("断点gis的查看地址：") != "-1"
      ) {
        let frontContent = PPResult.split("断点段落文字描述：")[0];
        let endContent = PPResult.split("断点段落文字描述：")[1];
        self.relationDiagnosis.PPResult =
          frontContent +
          "断点段落文字描述：<span style='font-weight:700'>" +
          endContent.split("断点gis的查看地址：")[0] +
          "</span>";
        //断点gis的查看地址：<a target='_blank' style='color: #c43c43;' href=" +
        // endContent.split("断点gis的查看地址：")[1] +
        // ">" +
        // endContent.split("断点gis的查看地址：")[1] +
        // "</a>";
      } else if (PPResult.indexOf("断点段落文字描述：") != "-1") {
        let frontContent = PPResult.split("断点段落文字描述：")[0];
        let endContent = PPResult.split("断点段落文字描述：")[1];
        self.relationDiagnosis.PPResult =
          frontContent +
          "断点段落文字描述：<span style='font-weight:700'>" +
          endContent +
          "</span>";
      } else if (PPResult.indexOf("断点gis的查看地址：") != "-1") {
        self.relationDiagnosis.PPResult = "";
      }
    },
    handleData2() {
      let self = this;
      // 添加topoUrl字段
      // 1.判断isPPShow 为true
      // 2.判断topoUrl字段 不为空则显示拓扑图
      // 返回示例：
      // topoUrl:"/layoutTopo/Topo_IframeSysTopo?alarmId=590290xx_xx06&eventTime=2024-01-15 09:52:40"
      if (
        self.relationDiagnosis.topoUrl != null &&
        self.relationDiagnosis.topoUrl !== "" &&
        self.relationDiagnosis.topoUrl !== "undefined" &&
        self.relationDiagnosis.topoUrl !== "无"
      ) {
        self.isShowTopo = true;
        if (!this.isShowOtdr && this.isShowTopo) {
          this.gzTabNow = "拓扑图";
        }
        self.topoSrc = self.relationDiagnosis.topoUrl;
      }
    },
    clearTimer() {
      clearTimeout(this.timer);
      this.timer = null;
    },
    //获取OTDR数据
    getOtdrData(alarmParams) {
      return new Promise((resolve, reject) => {
        let formData = new FormData();
        formData.append("alarmUniqueId", alarmParams.alarmUniqueId);
        formData.append("endTime", alarmParams.endTime);
        apiGetOtdrData(formData)
          .then(res => {
            this.otdrData = res?.data ?? null;
            resolve(this.otdrData);
          })
          .catch(error => {
            console.log(error);
            resolve();
          });
      });
    },
    showOldGisUrl(PPResult) {
      let self = this;
      if (
        PPResult.indexOf("断点段落文字描述：") != "-1" &&
        PPResult.indexOf("断点gis的查看地址：") != "-1"
      ) {
        let frontContent = PPResult.split("断点段落文字描述：")[0];
        let endContent = PPResult.split("断点段落文字描述：")[1];
        self.relationDiagnosis.PPResult =
          frontContent +
          "断点段落文字描述：<span style='font-weight:700'>" +
          endContent.split("断点gis的查看地址：")[0] +
          "</span>断点gis的查看地址：<a target='_blank' style='color: #c43c43;' href=" +
          endContent.split("断点gis的查看地址：")[1] +
          ">" +
          endContent.split("断点gis的查看地址：")[1] +
          "</a>";
      } else if (PPResult.indexOf("断点段落文字描述：") != "-1") {
        let frontContent = PPResult.split("断点段落文字描述：")[0];
        let endContent = PPResult.split("断点段落文字描述：")[1];
        self.relationDiagnosis.PPResult =
          frontContent +
          "断点段落文字描述：<span style='font-weight:700'>" +
          endContent +
          "</span>";
      } else if (PPResult.indexOf("断点gis的查看地址：") != "-1") {
        self.relationDiagnosis.PPResult =
          PPResult.split("断点gis的查看地址：")[0] +
          "断点gis的查看地址：<a target='_blank' style='color: #c43c43;' href=" +
          PPResult.split("断点gis的查看地址：")[1] +
          ">" +
          PPResult.split("断点gis的查看地址：")[1] +
          "</a>";
      }
    },
    // 获取光衰数据
    getGuangshuai() {
      apiGuangshuai({
        woId: this.woId,
      })
        .then(res => {
          if (res?.data?.trendStatus == "YES") {
            this.isShowGuangshuai = true;
            this.gzTabNow = "光衰趋势图";
            this.guangshuaiData = res.data.trendData || [];
          }
        })
        .catch(err => {
          console.log(err);
        });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../assets/common.scss";
::v-deep .el-collapse-item {
  .el-collapse-item__header {
    background-color: #fafafa;
    padding-left: 20px;
  }
}

// ::v-deep .el-collapse-item__content {
//   padding-bottom: 10px;
// }
::v-deep .el-collapse-item__content {
  line-height: unset;
}
.aiClass {
  height: 36px;
  .imgCol {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .el-col {
    height: 36px;
    font-size: 13px;

    span {
      line-height: 36px;
    }

    img {
      width: 24px;
      height: 24px;
      /*margin-right: 6px;*/
    }
  }
}

.bgCla {
  background-color: #e9edf3;
}
.gz-position {
  padding: 5px 20px;
  section {
    margin-top: 10px;
  }
}
</style>
