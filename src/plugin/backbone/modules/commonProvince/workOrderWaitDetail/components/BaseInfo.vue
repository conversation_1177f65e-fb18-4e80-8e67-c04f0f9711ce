<template>
  <el-card shadow="always" class="card" :body-style="{ padding: '10px 24px' }">
    <div class="header clearfix">
      <span class="header-title">基础信息</span>
      <div class="header-right">
        <!-- <el-button
          type="primary"
          size="mini"
          @click="exportBasicWorkOrderInfo"
          v-loading.fullscreen.lock="exportLoading"
          >导出Excel表</el-button
        > -->
      </div>
    </div>
    <div class="content">
      <el-descriptions title="工单基本信息" class="descriptions" :column="3">
        <el-descriptions-item label="所属专业">{{
          basicWorkOrderData.professionalTypeName
        }}</el-descriptions-item>
        <el-descriptions-item label="网络类型">{{
          basicWorkOrderData.networkTypeName
        }}</el-descriptions-item>
        <el-descriptions-item label="业务中断">
          {{ basicWorkOrderData.businessDown }}
        </el-descriptions-item>
        <el-descriptions-item label="受理时限(单位：分钟)">{{
          basicWorkOrderData.acceptTimeLimit
        }}</el-descriptions-item>
        <el-descriptions-item label="预估处理时限(单位：小时)">{{
          basicWorkOrderData.processTimeLimit
        }}</el-descriptions-item>
        <el-descriptions-item label="告警类别">
          {{ basicWorkOrderData.orgTypeName }}
        </el-descriptions-item>
        <el-descriptions-item label="告警地市">
          {{ basicWorkOrderData.cityName }}
        </el-descriptions-item>

        <!--        <template v-if="basicWorkOrderData.professionalType == 24">-->
        <el-descriptions-item label="告警区县">
          {{ basicWorkOrderData.regionName }}
        </el-descriptions-item>
        <!--        </template>-->
        <!--        <template v-else>-->
        <!--          <el-descriptions-item label="告警区县" :span="2">-->
        <!--            {{basicWorkOrderData.regionName}}-->
        <!--          </el-descriptions-item>-->
        <!--        </template>-->

        <el-descriptions-item
          label="网格名称"
          v-if="
            basicWorkOrderData.gridName != null &&
            basicWorkOrderData.gridName != '' &&
            basicWorkOrderData.gridName != '无'
          "
        >
          {{ basicWorkOrderData.gridName }}
        </el-descriptions-item>
        <el-descriptions-item
          label="所属场景"
          v-if="
            basicWorkOrderData.sceneName != null &&
            basicWorkOrderData.sceneName != '' &&
            basicWorkOrderData.sceneName != '无'
          "
        >
          {{ basicWorkOrderData.sceneName }}
        </el-descriptions-item>
        <el-descriptions-item
          label="站址名称"
          v-if="
            basicWorkOrderData.stName_formal != null &&
            basicWorkOrderData.stName_formal != '' &&
            basicWorkOrderData.stName_formal != '无'
          "
        >
          {{ basicWorkOrderData.stName_formal }}
        </el-descriptions-item>
        <el-descriptions-item
          label="机房名称"
          v-if="
            basicWorkOrderData.roomName != null &&
            basicWorkOrderData.roomName != '' &&
            basicWorkOrderData.roomName != '无'
          "
        >
          {{ basicWorkOrderData.roomName }}
        </el-descriptions-item>

        <!--非动环网IDC显示-->
        <el-descriptions-item
          label="经纬度"
          v-if="
            basicWorkOrderData.professionalType != 4 &&
            basicWorkOrderData.professionalType != 29 &&
            basicWorkOrderData.latitude_longitude != null &&
            basicWorkOrderData.latitude_longitude != '' &&
            basicWorkOrderData.latitude_longitude != '无'
          "
        >
          {{ basicWorkOrderData.latitude_longitude }}
        </el-descriptions-item>
        <!--动环网IDC显示机房信息-->
        <el-descriptions-item
          label="机房ID"
          :span="1"
          v-if="
            (basicWorkOrderData.professionalType == 4 ||
              basicWorkOrderData.professionalType == 29) &&
            basicWorkOrderData.roomId != null &&
            basicWorkOrderData.roomId != '' &&
            basicWorkOrderData.roomId != '无'
          "
        >
          {{ basicWorkOrderData.roomId }}
        </el-descriptions-item>
        <el-descriptions-item
          label="机房类型"
          :span="1"
          v-if="
            (basicWorkOrderData.professionalType == 4 ||
              basicWorkOrderData.professionalType == 29) &&
            basicWorkOrderData.roomType != null &&
            basicWorkOrderData.roomType != '' &&
            basicWorkOrderData.roomType != '无'
          "
        >
          {{ basicWorkOrderData.roomType }}
        </el-descriptions-item>
        <el-descriptions-item
          label="机房等级"
          :span="1"
          v-if="
            (basicWorkOrderData.professionalType == 4 ||
              basicWorkOrderData.professionalType == 29) &&
            basicWorkOrderData.roomLevel != null &&
            basicWorkOrderData.roomLevel != '' &&
            basicWorkOrderData.roomLevel != '无'
          "
        >
          {{ basicWorkOrderData.roomLevel }}
        </el-descriptions-item>

        <el-descriptions-item label="工单优先级" :span="1">{{
          basicWorkOrderData.woPriority
          }}</el-descriptions-item>
        <!--        无线网-->
        <template v-if="basicWorkOrderData.professionalType == 7">
          <el-descriptions-item label="覆盖场景" :span="1">{{
            basicWorkOrderData.coverScene
            }}</el-descriptions-item>
        </template>
        <!--end-->
        <el-descriptions-item
          label="说明"
          v-if="
            basicWorkOrderData.provSpecialField1 != null &&
            basicWorkOrderData.provSpecialField1 != '' &&
            basicWorkOrderData.provSpecialField1 != '无'
          "
        >
          {{ basicWorkOrderData.provSpecialField1 }}
        </el-descriptions-item>
        <!--        省分云-->
        <template v-if="basicWorkOrderData.professionalType == 24">
          <el-descriptions-item label="云池类型">{{
            basicWorkOrderData.cloudPoolType
          }}</el-descriptions-item>
          <el-descriptions-item label="业务名称">{{
            basicWorkOrderData.busName
          }}</el-descriptions-item>
        </template>
        <el-descriptions-item
          label="铁塔单号"
          v-if="
            basicWorkOrderData.TowerSheetNo != null &&
            basicWorkOrderData.TowerSheetNo != '' &&
            basicWorkOrderData.TowerSheetNo != '无'
          "
        >
          {{ basicWorkOrderData.TowerSheetNo }}
        </el-descriptions-item>
        <!--        :span="2"-->
      </el-descriptions>
      <el-descriptions class="descriptions" :column="3">
        <el-descriptions-item label="故障现象" :span="3">
          <text-collapse
            :text="return2Br(basicWorkOrderData.faultPhenomenon)"
            :max-lines="2"
          ></text-collapse>
        </el-descriptions-item>
        <!--        动环网IDC显示-->
        <el-descriptions-item
          label="经纬度"
          v-if="
            (basicWorkOrderData.professionalType == 4 ||
              basicWorkOrderData.professionalType == 29) &&
            basicWorkOrderData.latitude_longitude != null &&
            basicWorkOrderData.latitude_longitude != '' &&
            basicWorkOrderData.latitude_longitude != '无'
          "
        >
          {{ basicWorkOrderData.latitude_longitude }}
        </el-descriptions-item>
        <!--        非动环网IDC显示-->
        <el-descriptions-item label="工作内容" :span="1">
          {{ basicWorkOrderData.workContent }}
        </el-descriptions-item>
        <el-descriptions-item
          label="影响业务列表"
          :span="1"
          v-if="impactServiceArr.length > 0"
        >
          <el-tag
            v-for="(item, index) in impactServiceArr"
            class="fileName_style"
            :key="index"
            @click="handleDownload(item.id)"
            :title="item.name"
            ><div class="text-truncate">{{ item.name }}</div></el-tag
          >
        </el-descriptions-item>
        <el-descriptions-item label="影响业务列表" v-else>
          无
        </el-descriptions-item>
        <el-descriptions-item label="影响范围" :span="1">
          {{ basicWorkOrderData.effectRange }}
        </el-descriptions-item>
        <el-descriptions-item label="备注" :span="3">{{
          basicWorkOrderData.falutComment
        }}</el-descriptions-item>
        <!-- 无线或传输显示 -->
        <template
          v-if="
            basicWorkOrderData.professionalType == 7 ||
            basicWorkOrderData.professionalType == 3
          "
        >
          <el-descriptions-item label="是否共建共享" :span="1">
            {{ basicWorkOrderData.isShare }}
          </el-descriptions-item>
        </template>
      </el-descriptions>

      <el-descriptions
        title="共建共享信息"
        class="descriptions"
        v-if="basicWorkOrderData.isShare == '是'"
      >
        <el-descriptions-item label="共建共享单号" :span="3">{{
          basicWorkOrderData.shareSheetNo
        }}</el-descriptions-item>
        <!-- 无线 -->
        <template v-if="basicWorkOrderData.professionalType == 7">
          <el-descriptions-item label="站址信息" :span="1">{{
            basicWorkOrderData.stInfo
          }}</el-descriptions-item>
          <el-descriptions-item label="站址名称" :span="1">{{
            basicWorkOrderData.stName
          }}</el-descriptions-item>
          <el-descriptions-item label="站址经纬度" :span="1">{{
            basicWorkOrderData.longLatitude
          }}</el-descriptions-item>
          <el-descriptions-item label="站址类别" :span="1">{{
            basicWorkOrderData.stCategoryName
          }}</el-descriptions-item>
          <el-descriptions-item label="基站ID" :span="1">{{
            basicWorkOrderData.bsCode
          }}</el-descriptions-item>
          <el-descriptions-item label="基站名称" :span="1">{{
            basicWorkOrderData.bsName
          }}</el-descriptions-item>
          <el-descriptions-item label="所属区域" :span="1">{{
            basicWorkOrderData.alarmRegion
          }}</el-descriptions-item>
          <el-descriptions-item label="所属场景" :span="1">{{
            basicWorkOrderData.sceneName
          }}</el-descriptions-item>
          <el-descriptions-item label="基站级别" :span="1">{{
            basicWorkOrderData.bsLevelName
          }}</el-descriptions-item>
          <el-descriptions-item label="基站类型" :span="1">{{
            basicWorkOrderData.bsTypeName
          }}</el-descriptions-item>
          <el-descriptions-item label="告警ID和名称" :span="1">{{
            basicWorkOrderData.alarmId
          }}</el-descriptions-item>
          <el-descriptions-item label="告警类别" :span="1">{{
            basicWorkOrderData.alarmType
          }}</el-descriptions-item>
          <el-descriptions-item label="告警详情" :span="3">
            <text-collapse
              :text="return2Br(basicWorkOrderData.alarmDetail)"
              :max-lines="2"
            ></text-collapse>
          </el-descriptions-item>
          <el-descriptions-item label="告警级别" :span="1">{{
            basicWorkOrderData.alarmLevelName
          }}</el-descriptions-item>
          <el-descriptions-item label="告警发生时间" :span="1">{{
            basicWorkOrderData.alarmOccurTime
          }}</el-descriptions-item>
          <el-descriptions-item label="故障原因" :span="1">{{
            basicWorkOrderData.faultReasonName
          }}</el-descriptions-item>
          <el-descriptions-item label="交互方式" :span="1">{{
            basicWorkOrderData.faultHandleMode
          }}</el-descriptions-item>
        </template>
        <!-- 传输 -->
        <template v-if="basicWorkOrderData.professionalType == 3">
          <!--          <el-descriptions-item label="地市" :span="1">{{-->
          <!--            basicWorkOrderData.regionName-->
          <!--          }}</el-descriptions-item>-->
          <el-descriptions-item label="网元名称" :span="1">{{
            basicWorkOrderData.neName
          }}</el-descriptions-item>
          <el-descriptions-item label="网元等级" :span="1">{{
            basicWorkOrderData.neLevelName
          }}</el-descriptions-item>
          <el-descriptions-item label="告警ID" :span="1">{{
            basicWorkOrderData.alarmId
          }}</el-descriptions-item>
          <el-descriptions-item label="告警名称" :span="1">{{
            basicWorkOrderData.alarmName
          }}</el-descriptions-item>
          <el-descriptions-item label="告警发生时间" :span="1">{{
            basicWorkOrderData.alarmCreateTime
          }}</el-descriptions-item>
          <el-descriptions-item label="告警定位" :span="1">{{
            basicWorkOrderData.alarmLocation
          }}</el-descriptions-item>
          <el-descriptions-item label="故障种类" :span="1">{{
            basicWorkOrderData.alarmType
          }}</el-descriptions-item>
          <el-descriptions-item label="告警详情" :span="3">
            <text-collapse
              :text="return2Br(basicWorkOrderData.alarmDetail)"
              :max-lines="2"
            ></text-collapse>
          </el-descriptions-item>
        </template>
      </el-descriptions>
      <el-descriptions title="工单派送信息" class="descriptions" :colon="false">
        <el-descriptions-item
          label="附件:"
          :span="1"
          v-if="attachmentArr.length > 0"
        >
          <el-tag
            v-for="(item, index) in attachmentArr"
            class="fileName_style"
            :key="index"
            @click="handleDownload(item.id)"
            :title="item.name"
            ><div class="text-truncate">{{ item.name }}</div></el-tag
          >
        </el-descriptions-item>
        <el-descriptions-item label="附件:" v-else> 无 </el-descriptions-item>
        <el-descriptions-item label="建单人主送:">
          {{ basicWorkOrderData.agentMan }}
        </el-descriptions-item>
        <el-descriptions-item label="建单人抄送:">
          {{ basicWorkOrderData.copyMan }}
        </el-descriptions-item>
        <el-descriptions-item label="定性审核人:">
          {{ basicWorkOrderData.auditorManDetail }}
        </el-descriptions-item>
        <el-descriptions-item
          v-if="
            basicWorkOrderData.seizeOrders != null &&
            basicWorkOrderData.seizeOrders != '' &&
            basicWorkOrderData.seizeOrders != '无'
          "
        >
          <span
            style="display: inline-block; margin-right: 8px; margin-left: -10px"
          >
            <el-tooltip
              popper-class="my-popper"
              effect="dark"
              :content="toopTip"
              placement="bottom"
            >
              <em class="el-icon-question" style="color: #b50b14" ></em>
            </el-tooltip>
            派单模式:
          </span>
          {{ basicWorkOrderData.seizeOrders }}
        </el-descriptions-item>

        <el-descriptions-item label="是否通知他人:">{{
          basicWorkOrderData.isSendSms
        }}</el-descriptions-item>
        <el-descriptions-item
          :span="1"
          v-if="basicWorkOrderData.isSendSms == '是'"
          label="接收人:"
          >{{
            basicWorkOrderData.recipientDetailUserName
          }}</el-descriptions-item
        >
        <el-descriptions-item
          v-if="basicWorkOrderData.isSendSms == '是'"
          label="发送内容:"
          :span="3"
          >{{ basicWorkOrderData.sendContent }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <!--    文件查看下载列表-->
    <el-dialog
      width="500px"
      title="文件列表"
      :visible.sync="impactServiceVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <file-download
        @cancel="impactServiceVisible = false"
        :attachmentArr="impactServiceArr"
      ></file-download>
    </el-dialog>
    <el-dialog
      width="500px"
      title="文件列表"
      :visible.sync="attachmentVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <file-download
        @cancel="attachmentVisible = false"
        :attachmentArr="attachmentArr"
      ></file-download>
    </el-dialog>
  </el-card>
</template>

<script>
import TextCollapse from "@plugin/backbone/components/TextCollapse.vue";

import { apiDownloadManualFile, apifluenceExcel } from "../api/CommonApi";
import { apiFileDownload } from "../api/CommonApi";
export default {
  components: { TextCollapse },
  props: {
    basicWorkOrderData: Object,
    woId: String,
    workItemId: [String, Number],
  },
  name: "BaseInfo",
  data() {
    return {
      toopTip: "",
      builderZs: "",
      builderCs: "",
      notifier: "",
      recipient: "",
      exportLoading: false,
      impactServiceArr: [],
      impactServiceVisible: false,
      attachmentArr: [],
      attachmentVisible: false,
      manualFileName: "电路信息",
      OpticCableName: "影响系统信息",
      OpticCableLoading: false,
      manualFileFullscreenLoading: false,
      appendixFileLoading: false,
      professionalType: "",
    };
  },
  watch: {
    basicWorkOrderData: {
      handler() {
        if (
          this.basicWorkOrderData.appendixFileUrl &&
          this.basicWorkOrderData.appendixFileUrl.indexOf(":") > -1
        ) {
          this.impactServiceArr = JSON.parse(
            this.basicWorkOrderData.appendixFileUrl
          );
        }
        if (
          this.basicWorkOrderData.appendix &&
          this.basicWorkOrderData.appendix.indexOf(":") > -1
        ) {
          this.attachmentArr = JSON.parse(this.basicWorkOrderData.appendix);
        }
      },
      deep: true,
    },
  },
  created() {
    this.toopTip =
      "抢单受理：由抢单人或抢单组织处理工单，其他人员无待办，如未指定定性审核人，则不会进入定性审核环节。\n均可受理：主送多人或多组织时，涉及的人或组织均有独立子流程，均可处理工单，工单将经过定性审核环节。";

    // debugger
    // this.basicWorkOrderData.appendixFileUrl = '[{"id":"dbd2fd5c5e4642a8a9a9fa6219cbfdb3","name":"省分通用工单列表信息_20230210163815.xlsx"}]';
    // if (this.basicWorkOrderData.appendixFileUrl && this.basicWorkOrderData.appendixFileUrl.indexOf(':')>-1) {
    //   this.impactServiceArr = JSON.parse(
    //     this.basicWorkOrderData.appendixFileUrl
    //   );
    // }
    // if (this.basicWorkOrderData.appendix && this.basicWorkOrderData.appendix.indexOf(':')>-1) {
    //   this.attachmentArr = JSON.parse(this.basicWorkOrderData.appendix);
    // }
  },
  methods: {
    // exportBasicWorkOrderInfo() {
    //   this.exportLoading = true;
    //   let param = {
    //     woId: this.woId,
    //     workItemId: this.workItemId,
    //   };
    //   apiExportWorkOrder(param)
    //     .then(res => {
    //       if (res.status == "0") {
    //         this.$message.success("导出成功");
    //       } else {
    //         this.$message.error("导出失败");
    //       }
    //       this.exportLoading = false;
    //     })
    //     .catch(error => {
    //       console.log(error);
    //       this.$message.error("导出失败");
    //       this.exportLoading = false;
    //     });
    // },
    queryImpactServiceList() {
      this.impactServiceVisible = true;
    },
    queryAttachmentList() {
      this.attachmentVisible = true;
    },
    //电缆附件下载
    downloadOpticCable() {
      this.OpticCableLoading = true;
      let param = {
        param1: JSON.stringify({
          woId: this.woId,
        }),
      };
      apifluenceExcel(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("文件下载成功");
          } else {
            this.$message.success(res.msg);
          }
          this.OpticCableLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("文件下载失败");
          this.OpticCableLoading = false;
        });
    },
    // downloadAppendixFile(data) {
    //   this.appendixFileLoading = true;
    //   let param = {
    //     attId: data.id,
    //   };
    //   apiDownloadAppendixFile(param)
    //     .then(res => {
    //       if (res.status == "0") {
    //         this.$message.success("文件下载成功");
    //       } else {
    //         this.$message.error("文件下载失败");
    //       }
    //       this.appendixFileLoading = false;
    //     })
    //     .catch(error => {
    //       console.log(error);
    //       this.$message.error("文件下载失败");
    //       this.appendixFileLoading = false;
    //     });
    // },
    downloadManualFile() {
      this.manualFileFullscreenLoading = true;
      let param = {
        param1: JSON.stringify({
          woId: this.woId,
        }),
      };
      apiDownloadManualFile(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("文件下载成功");
          } else {
            this.$message.success(res.msg);
          }
          this.manualFileFullscreenLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("文件下载失败");
          this.manualFileFullscreenLoading = false;
        });
    },

    // 回车\r\n转为<br/>标签
    return2Br(str) {
      if (str) {
        return str.replaceAll("\\r\\n", "<br/>");
      }
    },
    handleDownload(attId) {
      let param = {
        attId: attId,
        watermark: "", //水印
      };
      apiFileDownload(param)
        .then(res => {})
        .catch(err => {
          console.log(err);
        });
    },
  },
};
</script>

<style>
.my-popper {
  white-space: pre !important;
}
</style>

<style lang="scss" scoped>
@import "../assets/common.scss";
::v-deep .el-descriptions-item__content {
  position: relative;
  word-break: break-all;
  padding-right: 8px;
}
::v-deep .el-descriptions-item__cell {
  vertical-align: top;
}
/*<<<<<<< HEAD*/

.gz-content {
  font-size: 13px;
  font-weight: 400;
  line-height: 20px;
  overflow: hidden;
  margin-top: -1px;
}

.expande {
  overflow: hidden;
  height: auto;
  /*display: inline;*/
}

.gz-close {
  overflow: hidden;
  height: 40px;
}

.expande-button-wrap {
  position: absolute;
  bottom: 0;
  right: -12px;
  height: 20px;
  background: white;
}

.expande-button {
  text-align: right;
  vertical-align: middle;
  line-height: 20px;
  padding-left: 3px;
}
.expande-button i {
  font-size: 18px;
  vertical-align: bottom;
  color: #b50b14;
}
</style>
