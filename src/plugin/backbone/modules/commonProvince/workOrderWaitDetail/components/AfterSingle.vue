<template>
  <div class="afterSingle">
    <el-form ref="afterSingleForm" :model="afterSingleForm" label-width="120px">
      <el-form-item
        label="追单内容:"
        prop="content"
        :rules="{
          required: true,
          message: '请填写追单内容',
        }"
      >
        <el-input
          show-word-limit
          maxlength="255"
          @keyup.native="descTip(255,'content','showTip1')"
          type="textarea"
          :rows="2"
          v-model="afterSingleForm.content"
          style="width: 420px"
        >
        </el-input>
        <div class="el-form-item__error"  v-if="showTip1">已超过填写字数上限</div>
      </el-form-item>
      <el-form-item
        label="系统名称:"
        v-if="isShowReturnProvince"
      >
        <el-input
          show-word-limit
          maxlength="255"
          @keyup.native="descTip(255,'systemName','showTip2')"
          type="textarea"
          :rows="2"
          placeholder="多个系统以逗号分隔"
          v-model="afterSingleForm.systemName"
          style="width: 420px"
        >
        </el-input>
        <div class="el-form-item__error"  v-if="showTip2">已超过填写字数上限</div>
      </el-form-item>
      <el-form-item label="附件:">
        <div style="width: 400px">
          <el-tag
            class="fileName_style"
            readonly
            v-for="(item, index) in importForm.attachmentFileList"
            :key="index"
            @close="close(item)"
            :title="item.name"
            ><div class="text-truncate">{{ item.name }}</div></el-tag
          >
          <el-button size="mini" type="primary" @click="attachmentBrowse"
            >+上传附件</el-button
          >
        </div>
      </el-form-item>
      <el-form-item
        label="是否流转地市:"
        v-if="isShowReturnProvince"
        prop="transferStatusRadio"
        :rules="{
          required: true,
          message: '请选择是否流转地市',
        }"
      >
        <el-radio-group v-model="afterSingleForm.transferStatusRadio">
          <el-radio label="是">是</el-radio>
          <el-radio label="否">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        label="主送:"
        prop="mainSending"
        v-if="afterSingleForm.transferStatusRadio == '是'"
        :rules="{
          required: true,
          message: '请选择主送人',
        }"
      >
        <el-input
          v-model="afterSingleForm.mainSending"
          placeholder="添加人员"
          style="width: 420px"
          readonly
        >
          <template v-slot:append>
            <el-button
              type="info"
              icon="el-icon-user"
              @click="onOpenPeopleDialog('lordSentDetermine')"
            ></el-button>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item
        label="抄送:"
        prop="cc"
        v-if="afterSingleForm.transferStatusRadio == '是'"
      >
        <el-input
          v-model="afterSingleForm.cc"
          placeholder="添加人员"
          style="width: 420px"
          readonly
        >
          <template v-slot:append>
            <el-button
              type="info"
              icon="el-icon-user"
              @click="onOpenPeopleDialog('ccDetermine')"
            ></el-button>
          </template>
        </el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: right">
      <el-button
        type="primary"
        @click="handleSubmit('afterSingleForm')"
        v-loading.fullscreen.lock="afterSingleFullscreenLoading"
        >提 交</el-button
      >
      <el-button @click="onResetAfterSingleForm">重 置</el-button>
    </div>
    <el-dialog
      width="420px"
      title="附件选择"
      :visible.sync="attachmentDialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <file-upload
        @change="changeFileData"
        @cancel="closeAttachmentDialog"
      ></file-upload>
    </el-dialog>
    <dia-tissue-tree
      v-if="isDiaOrgsUserTree"
      :title="diaPeople.title"
      :visible.sync="diaPeople.visible"
      :showOrgsTree="diaPeople.showOrgsTree"
      @on-save="onSavePeople"
      :appendToBody="true"
      :userAttribution="userAttribution"
      :show-contact-user-tab="true"
      :show-contact-org-tab="diaPeople.showOrgsTree"
    />
  </div>
</template>
<script>
  import { apiAfterSingle, apiGroupCode, apiFileUpload, apiAfterSingleInit } from "../api/CommonApi";
  import FileUpload from "./FileUpload";
// import FileUpload from "../../../workOrder/components/FileUpload";
  import DiaTissueTree from "@/plugin/backbone/components/common/DiaTissueTree";
  import { mapGetters } from "vuex";

export default {
  name: "AfterSingle",
  props: {
    common: Object,
    actionName:String,
    // mainProfessionalType:String,
  },
  components: { FileUpload,DiaTissueTree },
  data() {
    return {
      showTip1:false,
      showTip2:false,
      showTime:5000,
      isDiaOrgsUserTree:false,
      userAttribution: "cpAfterSingle",
      afterSingleForm: {
        content: null,
        systemName: null,
        mainSending: null,
        lordSentUserName: null,
        lordSentOrgName: null,
        lordSentUserId: null,
        lordSentOrgId: null,
        cc: null,
        transferStatusRadio: "否",
        ccUserName: null,
        ccOrgName: null,
        ccUserId: null,
        ccOrgId: null,
        agentManDetail: null,
        copyManDetail: null,
      },
      afterSingleFullscreenLoading: false,
      attachmentDialogVisible: false,
      importForm: {
        //附件
        attachmentFileList: [],
      },
      diaPeople: {
        visible: false,
        title: "",
        saveName: "",
        saveTitleMap: {
          lordSentDetermine: "主送",
          ccDetermine: "抄送",
        },
        showOrgsTree: true,
      },
      organizeForm: {
        builderZsList: [],
        // builderZsListCopy: [],
        // builderZsName: "",
        builderCsList: [],
        // builderCsListCopy: [],
        // builderCsName: "",
      },
      isShowReturnProvince:false,
      rawAgentManIdArr: [],
      rawAgenManDeptCodeArr: [],
    };
  },
  async created() {
    this.afterSingleInit();
  },
  mounted() {
    // if (this.common.isSender == 1) {
    //   this.afterSingleForm.mainSending = this.stitchingAlgorithm(
    //     this.common.agentDeptName,
    //     this.common.agentMan
    //   );
    //   this.afterSingleForm.lordSentUserName = this.common.agentMan;
    //   this.afterSingleForm.lordSentOrgName = this.common.agentDeptName;
    //   if (this.common.agentManId) {
    //     this.afterSingleForm.lordSentUserId = this.common.agentManId;
    //     this.rawAgentManIdArr = this.common.agentManId.split(",");
    //   }
    //   if (this.common.agentDeptCode) {
    //     this.afterSingleForm.lordSentOrgId = this.common.agentDeptCode;
    //     this.rawAgenManDeptCodeArr = this.common.agentDeptCode.split(",");
    //   }
    // }
  },

  computed: {
    ...mapGetters(["userInfo"]),
  },
  watch: {},
  methods: {
    descTip(count,name,showName){
      if (this.afterSingleForm[name].length>=count){
        this[showName] = true;
        setTimeout(() => {
          this[showName] = false;
        }, this.showTime);
      }
      else{
        this[showName] = false;
      }
    },
    //返单初始化
    afterSingleInit() {
      let param = {
        woId: this.common.woId,
        userName: this.userInfo.userName,
      };
      apiAfterSingleInit(param)
        .then(res => {
          if (res.status == "0") {
            this.isShowReturnProvince = res?.data?.isShowReturnProvince == '是' ? true : false;
            if (this.isShowReturnProvince)
            {
              // console.log(this.common);
              // debugger
              // this.afterSingleForm.mainSending = this.stitchingAlgorithm(
              //   this.common.agentDeptName,
              //   this.common.agentMan
              // );
              this.afterSingleForm.mainSending = this.common.agentMan;
              this.afterSingleForm.lordSentUserName = this.common.agentMan;
              this.afterSingleForm.lordSentOrgName = this.common.agentDeptName;
              if (this.common.agentManId) {
                this.afterSingleForm.lordSentUserId = this.common.agentManId;
                this.rawAgentManIdArr = this.common.agentManId.split(",");
              }
              if (this.common.agentDeptCode) {
                this.afterSingleForm.lordSentOrgId = this.common.agentDeptCode;
                this.rawAgenManDeptCodeArr = this.common.agentDeptCode.split(",");
              }
            }

          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    handleSubmit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          let self = this;
          this.afterSingleFullscreenLoading = true;
          // 如果有附件，先上传附件
          let isUpload = false;
          let formData = new FormData();
          if (self.importForm.attachmentFileList.length > 0) {
            isUpload = true;
            for (let item of self.importForm.attachmentFileList) {
              formData.append("files", item.raw);
            }
          } else {
            isUpload = false;
            formData.append("files", "");
          }

          let param = {
            attatchGroupKey:'',
            woId: this.common.woId,
            processInstId: this.common.processInstId,
            // processDefId: this.common.processDefId,
            workItemId: this.common.workItemId,
            processNode: this.common.processNode,
            actionName:this.actionName,
            appendMsg: this.afterSingleForm.content,
            systemName: this.afterSingleForm.systemName,
            isReturnProvince: this.afterSingleForm.transferStatusRadio,
            sendToFirst1:
              this.afterSingleForm.transferStatusRadio == '否'
                ? ''
                : this.afterSingleForm.lordSentUserId,
            sendToFirstName1:
              this.afterSingleForm.transferStatusRadio == '否'
                ? ''
                : this.afterSingleForm.lordSentUserName,
            sendToFirstDept:
              this.afterSingleForm.transferStatusRadio == '否'
                ? ''
                : this.afterSingleForm.lordSentOrgId,
            sendToFirstDeptName:
              this.afterSingleForm.transferStatusRadio == '否'
                ? ''
                : this.afterSingleForm.lordSentOrgName,
            ccTo1:
              this.afterSingleForm.transferStatusRadio == '否'
                ? ''
                : this.afterSingleForm.ccUserId,
            ccToName1:
              this.afterSingleForm.transferStatusRadio == '否'
                ? ''
                : this.afterSingleForm.ccUserName,
            copyDeptCode:
              this.afterSingleForm.transferStatusRadio == '否'
                ? ''
                : this.afterSingleForm.ccOrgId,
            copyDeptName:
              this.afterSingleForm.transferStatusRadio == '否'
                ? ''
                : this.afterSingleForm.ccOrgName,
          };

          // 上传附件
          if (isUpload) {
            apiGroupCode()
              .then(res => {
                if (res.status == 0) {
                  let processId = res.data?.linkId;

                  let uploadUrl = `/commonprovince/attach/upload?groupKey=${this.common.woId}&processId=${processId}&processNode=${this.common.processNode}&sheetCreateTime=${this.common.sheetCreateTime}`;
                  apiFileUpload(uploadUrl, formData)
                    .then(res => {
                      if (res.code == 200) {
                        param.attatchGroupKey = processId;
                        this.submit(param);
                        this.importForm.attachmentFileList = [];
                      } else {
                        this.$message.error(res.msg);
                        this.afterSingleFullscreenLoading = false;
                        return false;
                      }
                    })
                    .catch(error => {
                      console.log(error);
                      this.afterSingleFullscreenLoading = false;
                    });
                }
              })
              .catch(error => {
                console.log(error);
                this.afterSingleFullscreenLoading = false;
              });

          }
          else
          {
            this.submit(param);
          }

        } else {
          return false;
        }
      });
    },
    submit(param){
      apiAfterSingle(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success(res.msg);
            this.$emit("closeAfterSingleDialog", this.common.isSender);
          } else {
            this.$message.error(res.msg);
          }
          this.afterSingleFullscreenLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.afterSingleFullscreenLoading = false;
          this.$message.error(error.msg);
        });
    },
    //附件关闭
    close(tag) {
      this.importForm.attachmentFileList.splice(
        this.importForm.attachmentFileList.indexOf(tag),
        1
      );
    },
    //附件选择
    attachmentBrowse() {
      this.attachmentDialogVisible = true;
    },
    changeFileData(data) {
      this.importForm.attachmentFileList = data.attachmentFileList;
      this.attachmentDialogVisible = false;
    },
    closeAttachmentDialog() {
      this.attachmentDialogVisible = false;
    },
    onResetAfterSingleForm() {
      this.afterSingleForm = {
        content: null,
        systemName: null,
        mainSending: null,
        lordSentUserName: null,
        lordSentOrgName: null,
        lordSentUserId: null,
        lordSentOrgId: null,
        cc: null,
        transferStatusRadio: "否",
        ccUserName: null,
        ccOrgName: null,
        ccUserId: null,
        ccOrgId: null,
      };
      this.importForm.attachmentFileList = [];
    },
    onOpenPeopleDialog(diaSaveName) {
      this.isDiaOrgsUserTree = false;
      this.diaPeople.title = this.diaPeople.saveTitleMap[diaSaveName];
      this.diaPeople.saveName = diaSaveName;
      this.diaPeople.showOrgsTree = true;
      this.diaPeople.visible = true;
      this.$nextTick(() => {
        this.isDiaOrgsUserTree = true;
      });
    },
    onSavePeople(val) {
      this[this.diaPeople.saveName](val);
    },
    //主送确定
    lordSentDetermine({ usersChecked, orgsChecked, selectionUser, contactSelectionUser, contactSelectionOrg, }) {
      var usersArrTmp = [];
      if (contactSelectionUser && contactSelectionUser.length > 0) {
        contactSelectionUser.map(item=>{
          usersArrTmp.push({
            bz: "user",
            id: item.userName,
            name: item.trueName,
            orgName: item.orgEntity.orgName,
            mobilePhone: item.mobilePhone,
          });
        });
      }

      if (selectionUser && selectionUser.length > 0) {
        selectionUser.map(item => {

          let zs = usersArrTmp.findIndex(val => {
            return val.id === item.userName
          });
          if(zs > -1) {

          } else {
            usersArrTmp.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }

        });
      } else if (usersChecked && usersChecked.length > 0) {
        usersChecked.map(item => {

          let zs = usersArrTmp.findIndex(val => {
            return val.id === item.id
          });
          if(zs > -1) {

          } else {
            usersArrTmp.push({
              bz: "user",
              id: item.id,
              name: item.name,
              orgName: item.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }

      let usersCheckedName = usersArrTmp.map(item => {
        return item.name;
      });
      this.afterSingleForm.lordSentUserName = usersCheckedName.join(",");
      let usersCheckedId = usersArrTmp.map(item => {
        return item.id;
      });
      this.afterSingleForm.lordSentUserId = usersCheckedId.join(",");
      this.afterSingleForm.agentManDetail = usersArrTmp
        .map(item => {
          return (
            item.name +
            "-" +
            item.orgName +
            "-" +
            item.mobilePhone
          );
        })
        .join(",");


      var orgArrTmp = [];
      if (contactSelectionOrg && contactSelectionOrg.length > 0) {
        contactSelectionOrg.map(item => {
          orgArrTmp.push({
            bz: "org",
            id: item.orgId + "",
            orgName: item.fullOrgName,
          });
        });
      }
      if (orgsChecked && orgsChecked.length > 0) {
        orgsChecked.map(item => {
          let transferOrg = orgArrTmp.findIndex(val => {
            return val.id === item.id;
          });
          if (transferOrg > -1) {

          } else {
            orgArrTmp.push({
              bz: "org",
              id: item.id,
              orgName: item.name,
            });
          }
        });
      }

      let orgsCheckedName = orgArrTmp.map(item => {
        return item.orgName;
      });
      this.afterSingleForm.lordSentOrgName = orgsCheckedName.join(",");

      this.afterSingleForm.mainSending = this.stitchingAlgorithm(
        this.afterSingleForm.lordSentOrgName,
        this.afterSingleForm.agentManDetail
      );
      let orgsCheckedId = orgArrTmp.map(item => {
        return item.id;
      });
      this.afterSingleForm.lordSentOrgId = orgsCheckedId.join(",");
    },
    //抄送
    ccDetermine({ usersChecked, orgsChecked, selectionUser, contactSelectionUser, contactSelectionOrg }) {

      var usersArrTmp = [];
      if (contactSelectionUser && contactSelectionUser.length > 0) {
        contactSelectionUser.map(item=>{
          usersArrTmp.push({
            bz: "user",
            id: item.userName,
            name: item.trueName,
            orgName: item.orgEntity.orgName,
            mobilePhone: item.mobilePhone,
          });
        });
      }

      if (selectionUser && selectionUser.length > 0) {
        selectionUser.map(item => {

          let zs = usersArrTmp.findIndex(val => {
            return val.id === item.userName
          });
          if(zs > -1) {

          } else {
            usersArrTmp.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }

        });
      }
      else if (usersChecked && usersChecked.length > 0) {
        usersChecked.map(item => {

          let zs = usersArrTmp.findIndex(val => {
            return val.id === item.id
          });
          if(zs > -1) {

          } else {
            usersArrTmp.push({
              bz: "user",
              id: item.id,
              name: item.name,
              orgName: item.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }

      let usersCheckedName = usersArrTmp.map(item => {
        return item.name;
      });
      this.afterSingleForm.ccUserName = usersCheckedName.join(",");
      let usersCheckedId = usersArrTmp.map(item => {
        return item.id;
      });
      this.afterSingleForm.ccUserId = usersCheckedId.join(",");
      this.afterSingleForm.copyManDetail = usersArrTmp
        .map(item => {
          return (
            item.name +
            "-" +
            item.orgName +
            "-" +
            item.mobilePhone
          );
        })
        .join(",");


      var orgArrTmp = [];
      if (contactSelectionOrg && contactSelectionOrg.length > 0) {
        contactSelectionOrg.map(item => {
          orgArrTmp.push({
            bz: "org",
            id: item.orgId + "",
            orgName: item.fullOrgName,
          });
        });
      }
      if (orgsChecked && orgsChecked.length > 0) {
        orgsChecked.map(item => {
          let transferOrg = orgArrTmp.findIndex(val => {
            return val.id === item.id;
          });
          if (transferOrg > -1) {

          } else {
            orgArrTmp.push({
              bz: "org",
              id: item.id,
              orgName: item.name,
            });
          }
        });
      }

      let orgsCheckedName = orgArrTmp.map(item => {
        return item.orgName;
      });
      this.afterSingleForm.ccOrgName = orgsCheckedName.join(",");
      this.afterSingleForm.cc = this.stitchingAlgorithm(
        this.afterSingleForm.ccOrgName,
        this.afterSingleForm.copyManDetail
      );
      let orgsCheckedId = orgArrTmp.map(item => {
        return item.id;
      });
      this.afterSingleForm.ccOrgId = orgsCheckedId.join(",");
    },

    close(tag) {
      this.importForm.attachmentFileList.splice(
        this.importForm.attachmentFileList.indexOf(tag),
        1
      );
    },
    stitchingAlgorithm(orgName, userName) {
      if (
        null != orgName &&
        orgName.length !== 0 &&
        null != userName &&
        userName.length !== 0
      ) {
        return orgName + "," + userName;
      } else {
        if (null != orgName && orgName.length !== 0) {
          return orgName;
        } else if (null != userName && userName.length !== 0) {
          return userName;
        } else {
          return "";
        }
      }
    },

    stitchingAlgorithmArr(orgName, userName) {
      if (orgName.length !== 0 && userName.length !== 0) {
        return orgName.join(",") + "," + userName.join(",");
      } else {
        if (orgName.length !== 0) {
          return orgName.join(",");
        } else if (userName.length !== 0) {
          return userName.join(",");
        } else {
          return "";
        }
      }
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-tree {
  padding-top: 15px;
  padding-left: 10px;
  // 不可全选样式
  .el-tree-node {
    .is-leaf + .el-checkbox .el-checkbox__inner {
      display: inline-block;
    }
    .el-checkbox .el-checkbox__inner {
      display: none;
    }
  }
}
.afterSingle {
  .fileName_style {
    margin-right: 3px;
    vertical-align: middle;
    div {
      display: inline-block;
      max-width: 360px;
      vertical-align: top;
    }
  }
}
</style>
