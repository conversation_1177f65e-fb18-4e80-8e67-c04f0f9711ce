<template>
  <div class="back-single-internation">
    <el-form
      ref="faultHandleForm"
      :inline="false"
      class="demo-form-inline"
      :model="backSingleForm"
      label-width="130px"
    >
      <el-card
        shadow="never"
        :body-style="{ padding: '20px 8px' }"
        class="cus-card"
      >
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="24">
            <el-form-item label-width="180px" label="系统派单方生成的单号:" prop="sheetNo" >
              {{ backSingleForm.sheetNo }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="操作人姓名:" prop="operName" >
              {{ backSingleForm.operName }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="操作人邮箱:" prop="operEmail" >
              {{ backSingleForm.operEmail }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="操作人手机:" prop="operTel" >
              {{ backSingleForm.operTel }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="操作类别:"
              prop="operType"
              :rules="{
                required: true,
                message: '请选择',
              }"
            >
              <el-select
                v-model="backSingleForm.operType"
                placeholder="请选择内容"
                style="width: 100%"
              >
                <el-option
                  v-for="(item, i) in operTypeArr"
                  :key="i"
                  :label="item.dictName"
                  :value="item.dictName"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="操作时间:">
              {{ backSingleForm.operTime }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="问题原因类别:"
              prop="proCauseKind"
            >
              <el-select
                v-model="backSingleForm.proCauseKind"
                placeholder="请选择内容"
                style="width: 100%"
              >
                <el-option
                  v-for="(item, i) in proCauseKindArr"
                  :key="i"
                  :label="item.dictName"
                  :value="item.dictName"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="用户使用情况:"
              prop="isRecover"
            >
              <el-select
                v-model="backSingleForm.isRecover"
                placeholder="请选择内容"
                style="width: 100%"
              >
                <el-option
                  v-for="(item, i) in isRecoverArr"
                  :key="i"
                  :label="item.dictName"
                  :value="item.dictName"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障恢复时间:" prop="recoverTime">
              <el-date-picker
                v-model="backSingleForm.recoverTime"
                type="datetime"
                placeholder="请选择时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="24">
            <el-form-item
              label="解决方案:"
              prop="solution"
            >
              <el-input
                maxlength="500"
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="backSingleForm.solution"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: center">
      <el-button
        type="primary"
        @click="handleSubmit('faultHandleForm')"
        v-loading.fullscreen.lock="backSingleFullscreenLoading"
        >提 交</el-button
      >
      <!-- <el-button type="primary" @click="nextStepEvaluation()">下一步</el-button> -->
      <el-button @click="onReset">重 置</el-button>
    </div>

  </div>
</template>
<script>
import moment from "moment";
import { mapGetters } from "vuex";
import DictSelect from "../../../workOrder/components/DictSelect.vue";
import { apiActionPublic } from "../api/CommonApi";
import { apiDict, apiGetOrgInfo } from "../../../workOrder/api/CommonApi";
export default {
  name: "FaultHandle",
  props: {
    common: Object,
    orderInfo:Object,
  },
  components: { DictSelect },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  data() {

    return {
      backSingleForm: {
        woId: null,
        workItemId: null,
        processInstId: null,
        actionName:'故障处理',
        sheetNo: null,
        operName: null,
        operEmail: null,
        operTel: null,
        operType: null,
        operTime: null,
        proCauseKind: null,
        isRecover: null,
        recoverTime: null,
        solution: null,
      },
      operTypeArr:[
        {'dictName':'接单'},
        {'dictName':'反馈'},
        {'dictName':'回单'}
        ],
      proCauseKindArr:[],
      isRecoverArr:[],
      backSingleFullscreenLoading: false,

      // colors: ["#99A9BF", "#F7BA2A", "#FF9900"], // 等同于 { 2: '#99A9BF', 4: { value: '#F7BA2A', excluded: true }, 5: '#FF9900' }
      //当前人的部门
      userData: null,
      // viewsOnContentShow: false,
    };
  },
  async created() {
    this.getDictData("commonpro_share_ProCauseKind", "proCauseKindArr");//问题原因类别
    this.getDictData("commonpro_share_isRecover", "isRecoverArr");//用户使用情况

  },
  mounted() {
    // console.log(this.orderInfo);

    this.backSingleForm.workItemId = this.common.workItemId;
    this.backSingleForm.woId = this.common.woId;
    this.backSingleForm.processInstId = this.common.processInstId;
    //  this.getOrgInfo();
    this.userData = JSON.parse(this.userInfo.attr2);
    this.backSingleForm.operName = this.userInfo.realName;
    this.backSingleForm.operTel = this.userData.mobilePhone;
    this.backSingleForm.operEmail = this.userData.email;
    this.backSingleForm.sheetNo = this.orderInfo.shareSheetNo;
    this.backSingleForm.operTime = moment(Date.now()).format(
      "YYYY-MM-DD HH:mm:ss"
    );
    // this.backSingleForm.dept = this.userData.orgInfo.fullOrgName;
  },
  methods: {
    // getSeaLandOptions() {
    //   let param = {
    //     dictTypeCode: "10057",
    //   };
    //   apiDict(param)
    //     .then(res => {
    //       if (res.code == "200") {
    //         this.seaLandCheckBoxArr = res?.data ?? [];
    //       }
    //     })
    //     .catch(error => {
    //       console.log(error);
    //     });
    // },
    // getOrgInfo() {
    //   apiGetOrgInfo()
    //     .then(res => {
    //       if (res.status == "0") {
    //         this.backSingleForm.dept = res?.data?.orgInfo?.fullOrgName ?? "";
    //       }
    //     })
    //     .catch(error => {
    //       console.log(error);
    //     });
    // },

    // 下拉查询
    getDictData(dictId, selectName) {
      this[selectName] = [];
      let param = {
        dictType: dictId,
      };
      apiDict(param)
        .then(res => {
          if (res.code == 200) {
            this[selectName] = res.data;
          }
        })
        .catch(error => {
          console.log(error);
          return false;
        });
    },

    handleSubmit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          let self = this;
          this.backSingleFullscreenLoading = true;
          apiActionPublic(self.backSingleForm)
            .then(res => {
              if (res.status == "0") {
                this.$message.success(res.msg);
                //   this.onReset();
                this.$emit("closeFaultHandleDialog", res.data);
              } else {
                this.$message.error(res.msg);
              }
              this.backSingleFullscreenLoading = false;
            })
            .catch(error => {
              console.log(error);
              this.$message.error(error.msg);
              this.backSingleFullscreenLoading = false;
            });
        } else {
          return false;
        }
      });
    },

    onReset() {
      this.backSingleForm = {
        woId: this.common.woId,
        workItemId: this.common.workItemId,
        processInstId: this.common.processInstId,
        actionName:'故障处理',
        sheetNo: this.orderInfo.shareSheetNo,
        operName: this.userInfo.realName,
        operEmail: this.userData.email,
        operTel: this.userData.mobilePhone,
        operType: null,
        operTime: moment(Date.now()).format(
          "YYYY-MM-DD HH:mm:ss"
        ),
        proCauseKind: null,
        isRecover: null,
        recoverTime: null,
        solution: null,
      };
    },

    // showTime(val) {
    //   if (val) {
    //     if (val == 0) return "0秒";
    //     var time = "";
    //     var second = val % 60;
    //     var minute = parseInt(parseInt(val) / 60) % 60;
    //     time = second + "秒";
    //     if (minute >= 1) {
    //       time = minute + "分" + second + "秒";
    //     }
    //     var hour = parseInt(parseInt(val / 60) / 60) % 24;
    //     if (hour >= 1) {
    //       time = hour + "小时" + minute + "分" + second + "秒";
    //     }
    //     var day = parseInt(parseInt(parseInt(val / 60) / 60) / 24);
    //     if (day >= 1) {
    //       time = day + "天" + hour + "小时" + minute + "分" + second + "秒";
    //     }
    //
    //     return time;
    //   } else {
    //     return "0秒";
    //   }
    // },


  },
};
</script>
<style lang="scss" scoped>
.back-single-internation {
  max-height: calc(90vh - 150px);
  min-height: 400px;
  margin: 0 4px;
  padding-left: 4px;
  padding-right: 8px;
  overflow-y: auto;
  .cus-card ::v-deep .el-card__header {
    padding: 11px 24px 10px;
    font-weight: 400;
    @include themify {
      background-color: themed("$--background-color-base");
    }
  }
  .fileName_style {
    margin-right: 3px;
    vertical-align: middle;
    div {
      display: inline-block;
      max-width: 120px;
      vertical-align: top;
    }
  }
}
</style>
