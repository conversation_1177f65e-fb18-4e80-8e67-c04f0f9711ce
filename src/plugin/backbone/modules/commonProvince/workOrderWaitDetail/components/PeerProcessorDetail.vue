<template>
  <el-card shadow="always" class="card" :body-style="{ padding: '10px 24px' }">

    <div class="_el-table">
      <el-table
        :data="tableData"
        border
        stripe
        v-loading="tableLoading"
      >
        <el-table-column
          prop="trueName"
          label="姓名"
          min-width="13%"
          show-overflow-tooltip
        >
        </el-table-column>

        <el-table-column
          prop="orgName"
          label="组织"
          min-width="37%"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="phone"
          label="电话"
          min-width="20%"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="email"
          label="邮箱"
          min-width="30%"
          show-overflow-tooltip
        ></el-table-column>
      </el-table>
    </div>
    <pagination
      ref="pagination"
      :total="form.total"
      :page.sync="form.pageNum"
      :limit.sync="form.pageSize"
      layout="->, total, sizes, prev, pager, next"
      @change="getTableData()"
    />
  </el-card>
</template>

<script>
import Pagination from "../../../workOrder/components/Pagination.vue";
import { apiGetPeerProcessorList } from "../api/CommonApi";
import moment from "moment";

export default {
  name: "PeerProcessorDetail",
  components: {
    Pagination,
  },
  props: {
    // woId: String,
    common:Object,
    persons:String,
  },
  data() {

    return {
      form: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      tableData: [],
      // tableListData: {},
      tableLoading: false,
      tableListVisible: false,

      syncClearAlarmFullscreenLoading: false,
      confirmDialogVisible:false,
    };
  },
  mounted() {

    this.getTableData();
  },
  methods: {
    getTableData() {

      this.tableLoading = true;
      let param = {
        pageIndex: this.form.pageNum,
        pageSize: this.form.pageSize,
        // userIds: this.persons,
        woId:this.common.woId
      };
      apiGetPeerProcessorList(param)
        .then(res => {
          if (res.status == "200") {
            this.tableData = res?.data?.userInfoList ?? [];
            this.form.total = res?.data?.total ?? 0;
          }
          this.tableLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.tableLoading = false;
        });
    },

    // 回车\r\n转为<br/>标签
    return2Br(str) {
      if (str) {
        return str.replaceAll("\\r\\n", "<br/>");
      }
    },

  },
};
</script>

<style lang="scss" scoped>
@import "../assets/common.scss";
._el-table {
  width: 100%;
}
::v-deep .el-descriptions-row {
  .is-bordered-label {
    width: 140px;
  }
  .tdOverflow {
    width: 100%;
    height: 50px;
    overflow-x: auto;
    // text-overflow: ellipsis;
    /*white-space: nowrap;*/
    /*line-height: 50px;*/
    -ms-overflow-style: none;
    scrollbar-width: none;
    display: table-cell;
    vertical-align: middle;

  }

  .tdOverflow::-webkit-scrollbar {
    display: none;
  }
}
</style>
