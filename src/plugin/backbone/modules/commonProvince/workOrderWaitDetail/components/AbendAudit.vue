<template>
  <div>
    <el-form ref="abendAuditForm" :model="abendAuditForm" label-width="100px">
      <el-form-item
        label="审核结果:"
        prop="opResult"
        :rules="{
          required: true,
          message: '请选择审核结果',
        }"
      >
        <el-radio-group v-model="abendAuditForm.opResult" style="width: 260px">
          <el-radio label="同意">同意</el-radio>
          <el-radio label="拒绝">拒绝</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="审核意见:" prop="processSuggestion">
        <el-input
          type="textarea"
          :rows="2"
          placeholder="请填写审核意见"
          v-model="abendAuditForm.processSuggestion"
          style="width: 260px"
          show-word-limit
          maxlength="500"
          @keyup.native="descTip(500,'processSuggestion','showTip')"
        >
        </el-input>
        <div class="el-form-item__error"  v-if="showTip">已超过填写字数上限</div>
      </el-form-item>
    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: right">
      <el-button
        type="primary"
        @click="handleAbendAuditSubmit('abendAuditForm')"
        v-loading.fullscreen.lock="abendAuditFullScreenLoading"
        >提 交</el-button
      >
      <el-button @click="onResetAbendAudit">重 置</el-button>
    </div>
  </div>
</template>
<script>
import { apiAbendAudit, apiItCloudAbendAudit } from "../api/CommonApi";

export default {
  name: "Abend",
  props: {
    common: Object,
  },
  data() {
    return {
      showTip:false,
      showTime:5000,
      abendAuditForm: {
        opResult: null,
        processSuggestion: null,
      },
      abendAuditFullScreenLoading: false,
    };
  },
  mounted() {},
  methods: {
    descTip(count,name,showName){
      if (this.abendAuditForm[name].length>=count){
        this[showName] = true;
        setTimeout(() => {
          this[showName] = false;
        }, this.showTime);
      }
      else{
        this[showName] = false;
      }
    },
    handleAbendAuditSubmit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.abendAuditFullScreenLoading = true;
          let param = {
            woId: this.common.woId,
            actionName:'异常终止审核',
            processInstId: this.common.processInstId,
            processDefId: this.common.processDefId,
            workItemId: this.common.workItemId,
            processNode: this.common.processNode,
            abortAuditResult: this.abendAuditForm.opResult,
            abortAuditOpinion: this.abendAuditForm.processSuggestion,
          };

          apiAbendAudit(param)
            .then(res => {
              if (res.status == "0") {
                this.$message.success(res.msg);
                this.onResetAbendAudit();
                this.$emit("closeDialogAbendAudit");
              } else {
                this.$message.error(res.msg);
              }
              this.abendAuditFullScreenLoading = false;
            })
            .catch(error => {
              console.log(error);
              this.abendAuditFullScreenLoading = false;
              this.$message.error(error.msg);
            });
        } else {
          return false;
        }
      });
    },
    onResetAbendAudit() {
      this.abendAuditForm = {
        ...this.$options.data,
      };
    },
  },
};
</script>
