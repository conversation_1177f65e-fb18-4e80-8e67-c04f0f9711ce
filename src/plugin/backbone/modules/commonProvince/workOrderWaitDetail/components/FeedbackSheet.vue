<template>
  <el-card shadow="always" class="card" :body-style="{ padding: '10px 24px' }">
    <div class="header clearfix">
      <!--       v-show="isShowAudit"-->
      <div class="header-right" v-show="btnFlag">
        <el-button type="primary" size="mini" @click="qualitativeReview"
          >定性审核</el-button
        >
      </div>
      <div class="header-tabMenu">
        <span class="header-title" style="margin-top: 5px; margin-right: 10px"
          >反馈单详情</span
        >

        <div style="display: inline-block">
          <el-radio-group v-model="radio1" @change="handleClick" size="mini">
            <el-radio-button
              v-for="(tab, key) in tabMenu"
              :key="key"
              :label="key"
            >
              {{ tab }}
            </el-radio-button>
          </el-radio-group>
        </div>
      </div>
    </div>
    <div class="content">
      <el-descriptions title="故障定性信息" class="descriptions" :column="3">
        <template v-if="list.professionalTypeName == '铁塔'">
          <el-descriptions-item label="故障所属专业">{{
            list.professionalTypeName
          }}</el-descriptions-item>
          <el-descriptions-item label="回单人">{{
            list.recoveryPerson
          }}</el-descriptions-item>
          <el-descriptions-item label="回单人联系电话">{{
            list.recoveryPhone
          }}</el-descriptions-item>
          <el-descriptions-item label="回单时间">{{
            list.faultNoticeTime
          }}</el-descriptions-item>
          <el-descriptions-item label="实时直流电压值">{{
            list.siteOfflineReason
          }}</el-descriptions-item>
          <el-descriptions-item label="铁塔站址资源编码">{{
            list.faultReason
          }}</el-descriptions-item>
          <el-descriptions-item label="站址运维ID">{{
            list.vendor
          }}</el-descriptions-item>
          <el-descriptions-item label="站址名称">{{
            list.eqpType
          }}</el-descriptions-item>
          <el-descriptions-item label="运营商是否购买发电服务">
            {{ list.hardwareFlag }}
          </el-descriptions-item>
          <el-descriptions-item label="是否是发电工单">{{
            list.maintenanceSubject
          }}</el-descriptions-item>
          <el-descriptions-item label="发电开始时间">{{
            list.alarmCreateTime
          }}</el-descriptions-item>
          <el-descriptions-item label="发电结束时间">{{
            list.faultOverTime
          }}</el-descriptions-item>
          <el-descriptions-item label="接单时间">{{
            list.faultRecoveryTime
          }}</el-descriptions-item>
          <el-descriptions-item label="所属地市">{{
            list.mobileFaultDutyId
          }}</el-descriptions-item>
          <el-descriptions-item label="所属区县">{{
            list.boardType
          }}</el-descriptions-item>
          <el-descriptions-item label="运营商共享情况">{{
            list.repairQuality
          }}</el-descriptions-item>
          <el-descriptions-item label="站址服务等级">{{
            list.fdbkQuality
          }}</el-descriptions-item>
          <el-descriptions-item label="是否为免责站址">{{
            list.satisfaction
          }}</el-descriptions-item>
          <el-descriptions-item label="申告工单是否铁塔原因">{{
            list.evaluationOpinion
          }}</el-descriptions-item>
          <el-descriptions-item label="申告工单故障分类">{{
            list.faultSource
          }}</el-descriptions-item>
          <el-descriptions-item label="是否申请减免">{{
            list.diskName
          }}</el-descriptions-item>
          <el-descriptions-item
            label="申请减免原因"
            v-if="list.diskName != '不需减免'"
            >{{ list.interSystem }}</el-descriptions-item
          >
          <el-descriptions-item
            label="申请减免分钟数"
            v-if="list.diskName != '不需减免'"
            >{{ list.effectSystem }}</el-descriptions-item
          >
          <el-descriptions-item label="是否上站">{{
            list.interCircuit
          }}</el-descriptions-item>
          <el-descriptions-item
            label="同行处理人"
            :span="1"
            v-if="list.peerProcessorName != '无'"
          >
            <span class="sheetNo_class" @click="openPeerProcessor">{{
              list.peerProcessorName
            }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="同行处理人" v-else>
            无
          </el-descriptions-item>
          <el-descriptions-item label="处理结果描述">{{
            list.faultRange
          }}</el-descriptions-item>
          <el-descriptions-item label="附件" v-if="list.files.length > 0">
            <el-tag
              v-for="(item, index) in list.files"
              class="fileName_style"
              :key="index"
              @click="handleDownload(item.attId)"
              :title="item.attOrigName"
              ><div class="text-truncate">{{ item.attOrigName }}</div></el-tag
            >
          </el-descriptions-item>
          <el-descriptions-item label="附件" v-else> 无 </el-descriptions-item>
        </template>
        <template v-else>
          <el-descriptions-item label="故障所属专业">{{
            list.professionalTypeName
          }}</el-descriptions-item>
          <el-descriptions-item label="故障发生时间">{{
            list.alarmCreateTime
          }}</el-descriptions-item>
          <el-descriptions-item label="故障通知时间">{{
            list.faultNoticeTime
          }}</el-descriptions-item>
          <el-descriptions-item label="业务恢复时间">{{
            list.businessRecoveryTime
          }}</el-descriptions-item>
          <el-descriptions-item label="业务恢复历时">{{
            recoveryDuration
          }}</el-descriptions-item>
          <el-descriptions-item label="故障结束时间">{{
            list.faultOverTime
          }}</el-descriptions-item>
          <el-descriptions-item label="故障处理历时">{{
            faultDuration
          }}</el-descriptions-item>
          <el-descriptions-item label="故障发生地区">
            {{ list.faultRegion }}
          </el-descriptions-item>
          <el-descriptions-item label="故障处理部门">
            {{ list.dept }}
          </el-descriptions-item>
          <el-descriptions-item label="处理人">{{
            list.recoveryPerson
          }}</el-descriptions-item>
          <el-descriptions-item label="挂起历时">{{
            suspendDuration
          }}</el-descriptions-item>
          <el-descriptions-item label="故障处理净历时">{{
            faultCleanDuration
          }}</el-descriptions-item>

          <el-descriptions-item label="是否影响业务">{{
            list.isEffectBusiness
          }}</el-descriptions-item>
          <el-descriptions-item
            label="影响范围"
            v-if="list.isEffectBusiness == '是'"
          >
            {{ list.effectRange }}
          </el-descriptions-item>
          <el-descriptions-item label="是否基站退服" v-if="list.professionalTypeName == '无线网'">{{
            list.isSiteOffline
          }}</el-descriptions-item>
          <el-descriptions-item
            label="退服原因"
            v-if="list.professionalTypeName == '无线网' && list.isSiteOffline == '是'"
            >{{ list.siteOfflineReason }}</el-descriptions-item
          >
          <el-descriptions-item
            label="同行处理人"
            :span="1"
            v-if="list.peerProcessorName != '无'"
          >
            <span class="sheetNo_class" @click="openPeerProcessor">{{
              list.peerProcessorName
            }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="同行处理人" v-else>
            无
          </el-descriptions-item>
          <el-descriptions-item
            label="是否有财产损失"
            v-if="
              list.ifPropertyLoss != null &&
              list.ifPropertyLoss != '' &&
              list.ifPropertyLoss != '无'
            "
            >{{ list.ifPropertyLoss }}</el-descriptions-item
          >
          <el-descriptions-item
            label="保单号"
            v-if="list.ifPropertyLoss == '是'"
            >{{ list.insuranceId }}</el-descriptions-item
          >
          <el-descriptions-item
            label="派单是否准确"
            v-if="
              list.isDispatchAccurate != null &&
              list.isDispatchAccurate != '' &&
              list.isDispatchAccurate != '无'
            "
            >{{ list.isDispatchAccurate }}</el-descriptions-item
          >
        </template>
      </el-descriptions>
      <el-descriptions
        title="故障专业信息"
        class="descriptions"
        v-if="list.professionalTypeName != '铁塔'"
      >
        <!--当故障处理方式=自动恢复，不显示以下字段。-->
        <template v-if="list.solvedType != '自动恢复'">

          <el-descriptions-item label="故障处理方式" :span="1">
            {{ list.solvedType }}</el-descriptions-item
          >
          <!-- 省分云 -->
          <template v-if="list.professionalTypeName == '省分云'">
            <el-descriptions-item label="业务名称">
              {{ list.businessName }}</el-descriptions-item
            >
            <el-descriptions-item label="紧急程度" :span="1">
              {{ list.emergencyLevel }}</el-descriptions-item
            >
            <el-descriptions-item label="故障分类" :span="1">
              {{ list.faultCate }}</el-descriptions-item
            >
            <el-descriptions-item label="故障原因" :span="1">
              {{ list.faultReason }}</el-descriptions-item
            >
            <el-descriptions-item label="设备类型">
              {{ list.eqpType }}</el-descriptions-item
            >
            <el-descriptions-item label="是否硬件故障" :span="1">
              {{ list.hardwareFlag }}</el-descriptions-item
            >
            <el-descriptions-item label="设备名称">
              {{ list.eqpName }}</el-descriptions-item
            >
            <!--                <el-descriptions-item label="故障原因描述" :span="3">-->
            <!--                  {{ list.faultMajor.falutReasonDesc }}</el-descriptions-item-->
            <!--                >-->
            <!--                <el-descriptions-item label="备注" :span="3">-->
            <!--                  {{ list.faultMajor.falutComment }}</el-descriptions-item-->
            <!--                >-->
            <!--                <el-descriptions-item label="附件" :span="3">-->
            <!--                  {{ list.faultMajor.falutComment }}</el-descriptions-item-->
            <!--                >-->
          </template>
          <!-- 增值平台/其他/IT监控 -->
          <template
            v-if="
              list.professionalTypeName == '增值平台' ||
              list.professionalTypeName == '其他' ||
              list.professionalTypeName == 'IT监控'
            "
          >
            <el-descriptions-item label="故障分类" :span="1">
              {{ list.faultCate }}</el-descriptions-item
            >
            <el-descriptions-item label="故障原因" :span="1">
              {{ list.faultReason }}</el-descriptions-item
            >
            <!--                <el-descriptions-item label="故障原因描述" :span="3">-->
            <!--                  {{ list.faultMajor.falutReasonDesc }}</el-descriptions-item-->
            <!--                >-->
            <!--                <el-descriptions-item label="备注" :span="3">-->
            <!--                  {{ list.faultMajor.falutComment }}</el-descriptions-item-->
            <!--                >-->
            <!--                <el-descriptions-item label="附件" :span="3">-->
            <!--                  {{ list.faultMajor.falutComment }}</el-descriptions-item-->
            <!--                >-->
          </template>
          <!-- 接入网 -->
          <template v-if="list.professionalTypeName == '接入网'">
            <el-descriptions-item label="故障分类" :span="1">
              {{ list.faultCate }}</el-descriptions-item
            >
            <el-descriptions-item label="故障原因" :span="1">
              {{ list.faultReason }}</el-descriptions-item
            >
            <el-descriptions-item label="故障厂家" :span="1">
              {{ list.vendor }}</el-descriptions-item
            >
            <el-descriptions-item label="设备类型">
              {{ list.eqpType }}</el-descriptions-item
            >
            <el-descriptions-item label="设备名称">
              {{ list.eqpName }}</el-descriptions-item
            >
            <el-descriptions-item label="故障定责" :span="1">
              {{ list.mobileFaultDutyId }}</el-descriptions-item
            >
            <!--                <el-descriptions-item label="故障原因描述" :span="3">-->
            <!--                  {{ list.faultMajor.falutReasonDesc }}</el-descriptions-item-->
            <!--                >-->
            <!--                <el-descriptions-item label="备注" :span="3">-->
            <!--                  {{ list.faultMajor.falutComment }}</el-descriptions-item-->
            <!--                >-->
            <!--                <el-descriptions-item label="附件" :span="3">-->
            <!--                  {{ list.faultMajor.falutComment }}</el-descriptions-item-->
            <!--                >-->
          </template>
          <!-- 传输网/集客 -->
          <template
            v-if="
              list.professionalTypeName == '集客' ||
              list.professionalTypeName == '传输网'
            "
          >
            <el-descriptions-item label="故障状态">
              {{ list.faultStatus }}</el-descriptions-item
            >
            <el-descriptions-item label="网络类型">
              {{ list.networkType }}</el-descriptions-item
            >
            <el-descriptions-item label="故障分类" :span="1">
              {{ list.faultCate }}</el-descriptions-item
            >
            <el-descriptions-item
              label="设备类型"
              v-if="list.faultCate != '线路故障'"
            >
              {{ list.eqpType }}</el-descriptions-item
            >
            <el-descriptions-item label="故障原因" :span="1">
              {{ list.faultReason }}</el-descriptions-item
            >
            <el-descriptions-item
              label="板卡类型"
              v-if="list.faultCate != '线路故障'"
              :span="1"
            >
              {{ list.boardType }}</el-descriptions-item
            >
            <el-descriptions-item
              label="光缆名称"
              v-if="list.faultCate == '线路故障'"
              :span="1"
            >
              {{ list.deviceName }}</el-descriptions-item
            >
            <el-descriptions-item
              label="设备名称"
              v-if="list.faultCate != '线路故障'"
            >
              {{ list.eqpName }}</el-descriptions-item
            >
            <el-descriptions-item label="故障区间" :span="1">
              {{ list.faultRange }}</el-descriptions-item
            >
            <el-descriptions-item label="维护主体" :span="1">
              {{ list.maintenanceSubject }}</el-descriptions-item
            >
            <el-descriptions-item label="受影响系统" :span="1">
              {{ list.effectSystem }}</el-descriptions-item
            >
            <el-descriptions-item label="受影响电路" :span="1">
              {{ list.effectCircuit }}</el-descriptions-item
            >
            <el-descriptions-item
              label="承载业务系统"
              v-if="list.faultCate != '线路故障'"
              :span="1"
            >
              {{ list.bearerSystem }}</el-descriptions-item
            >
            <el-descriptions-item
              label="故障厂家"
              v-if="list.faultCate != '线路故障'"
              :span="1"
            >
              {{ list.vendor }}</el-descriptions-item
            >
            <!--                <el-descriptions-item label="故障原因描述" :span="3">-->
            <!--                  {{ list.faultMajor.falutReasonDesc }}</el-descriptions-item-->
            <!--                >-->
            <!--                <el-descriptions-item label="备注" :span="3">-->
            <!--                  {{ list.faultMajor.falutComment }}</el-descriptions-item-->
            <!--                >-->
            <!--                <el-descriptions-item label="相关附件" :span="1">-->
            <!--                  {{ list.faultMajor.falutComment }}</el-descriptions-item-->
            <!--                >-->
            <el-descriptions-item label="是否进行路由调整" :span="1">
              {{ list.isRouterChange }}</el-descriptions-item
            >
            <el-descriptions-item
              label="路由工单编号"
              v-if="list.isRouterChange == '是'"
              :span="1"
            >
              {{ list.routerChangeJobCode }}</el-descriptions-item
            >
          </template>
          <!-- 无线网 -->
          <template v-if="list.professionalTypeName == '无线网'">
            <el-descriptions-item label="故障分类" :span="1">
              {{ list.faultCate }}</el-descriptions-item
            >
            <el-descriptions-item label="故障原因" :span="1">
              {{ list.faultReason }}</el-descriptions-item
            >
            <el-descriptions-item
              label="故障厂家"
              v-if="list.faultCate == '基站设备' || list.faultCate == '传输系统'"
              :span="1"
            >
              {{ list.vendor }}</el-descriptions-item
            >
            <el-descriptions-item label="设备类型" :span="1">
              {{ list.eqpType }}</el-descriptions-item
            >
            <el-descriptions-item label="设备名称" :span="1">
              {{ list.eqpName }}</el-descriptions-item
            >
            <el-descriptions-item label="维护主体" :span="1">
              {{ list.maintenanceSubject }}</el-descriptions-item
            >
            <el-descriptions-item label="故障定责" :span="1">
              {{ list.mobileFaultDutyId }}</el-descriptions-item
            >
          </template>
          <!-- 核心网/固网/移动数通网/数据网/承载A/承载B/WLAN/动环网/5GC/IP互联网/核心网其他 -->
          <template
            v-if="
              list.professionalTypeName == '核心网' ||
              list.professionalTypeName == '核心网其他' ||
              list.professionalTypeName == '5GC' ||
              list.professionalTypeName == '智能网' ||
              list.professionalTypeName == 'IDC' ||
              list.professionalTypeName == 'vIMS' ||
              list.professionalTypeName == '固网' ||
              list.professionalTypeName == '移动数通网' ||
              list.professionalTypeName == '数据网' ||
              list.professionalTypeName == 'IP互联网' ||
              list.professionalTypeName == 'IP承载A网' ||
              list.professionalTypeName == 'IP承载B网' ||
              list.professionalTypeName == 'WLAN' ||
              list.professionalTypeName == '动环网'
            "
          >
            <el-descriptions-item label="故障分类">
              {{ list.faultCate }}</el-descriptions-item
            >
            <el-descriptions-item label="故障原因">
              {{ list.faultReason }}</el-descriptions-item
            >
            <el-descriptions-item label="故障厂家">
              {{ list.vendor }}</el-descriptions-item
            >
            <el-descriptions-item label="设备类型">
              {{ list.eqpType }}</el-descriptions-item
            >
            <el-descriptions-item label="设备名称">
              {{ list.eqpName }}</el-descriptions-item
            >
          </template>

          <el-descriptions-item label="故障原因描述" :span="3">
            {{ list.falutReasonDesc }}</el-descriptions-item
          >
          <el-descriptions-item label="备注" :span="3">
            {{ list.falutComment }}</el-descriptions-item
          >
          <el-descriptions-item
            label="附件"
            :span="3"
            v-if="list.files.length > 0"
          >
            <el-tag
              v-for="(item, index) in list.files"
              class="fileName_style"
              :key="index"
              @click="handleDownload(item.attId)"
              :title="item.attOrigName"
              ><div class="text-truncate">{{ item.attOrigName }}</div></el-tag
            >
          </el-descriptions-item>
          <el-descriptions-item label="附件" v-else> 无 </el-descriptions-item>
        </template>
        <template v-if="list.solvedType == '自动恢复'">

          <el-descriptions-item label="故障处理方式" :span="1">
            {{ list.solvedType }}</el-descriptions-item
          >

          <el-descriptions-item label="故障原因描述" :span="2">
            {{ list.falutReasonDesc }}</el-descriptions-item
          >
        </template>
      </el-descriptions>
      <el-descriptions
        title="故障定性审核信息"
        class="descriptions"
        v-if="
          list.auditResult != null &&
          list.auditResult != '' &&
          list.auditResult != '无'
        "
      >
        <el-descriptions-item label="审批结果">
          {{ list.auditResult }}
        </el-descriptions-item>
        <el-descriptions-item label="是否上传故障报告">
          {{ list.isUpdateFaultFile }}</el-descriptions-item
        >
        <el-descriptions-item label="是否故障处理结束">
          {{ list.isFaultClose }}
        </el-descriptions-item>
        <el-descriptions-item
          label="处理时限"
          v-if="
            list.professionalTypeName == '传输网' ||
            list.professionalTypeName == '集客'
          "
        >
          {{ list.resolvingTimeLimite }}</el-descriptions-item
        >
        <el-descriptions-item
          label="第二故障源流水号"
          v-if="
            list.professionalTypeName == '传输网' ||
            list.professionalTypeName == '集客'
          "
        >
          {{ list.secondFaultSourceNumber }}</el-descriptions-item
        >
        <el-descriptions-item
          label="业务影响范围"
          v-if="
            list.professionalTypeName == '传输网' ||
            list.professionalTypeName == '集客'
          "
        >
          {{ list.influenceBusinessScope }}</el-descriptions-item
        >
        <el-descriptions-item
          label="故障所在省"
          v-if="
            list.professionalTypeName == '传输网' ||
            list.professionalTypeName == '集客'
          "
        >
          {{ list.faultProvince }}</el-descriptions-item
        >
        <el-descriptions-item label="审批意见">
          {{ list.auditOpinion }}</el-descriptions-item
        >
      </el-descriptions>
    </div>
    <el-dialog
      title="同行处理人"
      :visible.sync="dialogPeerProcessorVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="PeerProcessorClose"
      :fullscreen="false"
      width="60%"
      top="5vh"
    >
      <PeerProcessorDetail
        :common="common"
        :persons="list.peerProcessor"
      ></PeerProcessorDetail>
    </el-dialog>
    <el-dialog
      title="定性审核"
      :visible.sync="dialogQualitativeReviewVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="qualitativeReviewClose"
      :fullscreen="false"
      width="83%"
      top="5vh"
    >
      <qualitative-review
        ref="qualitativeReview"
        :common="common"
        :workItemId="workItemId"
        :index="selectedTabIndex"
        v-if="showDxsh"
        @qualitativeReviewSubmit="qualitativeReviewSubmit"
      ></qualitative-review>
    </el-dialog>
    <el-dialog
      width="420px"
      title="文件下载"
      :visible.sync="attachmentVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <file-download
        @cancel="attachmentVisible = false"
        :attachmentArr="attachmentArr"
      ></file-download>
    </el-dialog>
  </el-card>
</template>

<script>
import PeerProcessorDetail from "./PeerProcessorDetail";
import QualitativeReview from "./QualitativeReview.vue";
import {
  apiqueryFeedback,
  apiDownloadAppendixFile,
  apiFileDownload,
} from "../api/CommonApi";
import FileDownload from "../../../workOrder/components/FileDownload.vue";
export default {
  name: "FeedbackSheet",
  props: {
    isShowAudit: Boolean, //判断定性审核按钮
    common: Object,
    woId: String,
    // isShowQualitative: Boolean, //判断定性按钮
    // qualitativeType: String,
  },
  components: {
    PeerProcessorDetail,
    QualitativeReview,
    FileDownload,
  },
  data() {
    return {
      tabMenu: [],
      list: {},
      selectedTabIndex: null,
      listAll: [],
      btnFlag: false,
      dialogPeerProcessorVisible: false,
      dialogQualitativeReviewVisible: false,
      //故障定性
      recoveryDuration: null, //业务恢复历时
      faultDuration: null, //故障处理历时
      suspendDuration: null, //挂起历时
      faultCleanDuration: null, //故障处理净历时
      attachmentArr: [], //附件
      attachmentVisible: false,
      // 多个省的 定性审核区分workItemId
      workItemId: null,
      showDxsh: false,
      appendixFileLoading: false,
      isUploadReport: null,
      radio1: 0,
      // isRouteNoShow: true,
      // isRouteAdjustShow: true,
    };
  },
  mounted() {
    if (this.woId) {
      this.getFeedbackData();
    }
    console.log(this.common);
  },
  methods: {
    handleClick(index) {
      this.selectedTabIndex = index;
      this.deal(this.listAll[index]);
      // this.setBtnFlag();
    },
    handleDownload(attId) {
      let param = {
        attId: attId,
        watermark: "", //水印
      };
      apiFileDownload(param)
        .then(res => {})
        .catch(err => {
          console.log(err);
        });
    },
    deal(data) {
      this.list = data;
      this.recoveryDuration = this.showTime(data.recoveryDuration);
      this.faultDuration = this.showTime(data.faultDuration);
      this.suspendDuration = this.showTime(data.suspendDuration);
      this.faultCleanDuration = this.showTime(data.faultCleanDuration);
      if (data.appendix) {
        this.attachmentArr = JSON.parse(data.appendix);
      }
      this.workItemId = data.workItemId;
      this.isUploadReport = data.isUploadReport;
      if (this.isShowAudit) {
        if (
          this.list.auditResult == null ||
          this.list.auditResult == "" ||
          this.list.auditResult == "无"
        ) {
          this.btnFlag = true;
        } else {
          this.btnFlag = false;
        }
      }
    },
    getFeedbackData() {
      let initIndex = 0;
      let param = {
        woId: this.woId,
        isEdit: 0, //isEdit    0:查看 1:编辑
      };
      apiqueryFeedback(param)
        .then(res => {
          if (res.status == 0) {
            let self = this;
            self.listAll = res?.data ?? [];
            if (self.listAll.length > 0) {
              // self.list = self.listAll[0];

              // if (self.list.appendix) {
              //   self.attachmentArr = JSON.parse(self.list.appendix);
              // }
              self.listAll.forEach(item => {
                this.tabMenu.push(item.recoveryPerson);
              });
              self.radio1 = initIndex;
              self.handleClick(initIndex);
            }
          }
        })
        .catch(err => {
          console.log(err);
        });
    },
    setBtnFlag() {
      // if (this.qualitativeType == "定性审核") {
      //   if (this.isShowAudit == false) {
      //     this.btnFlag = false;
      //   } else {
      //     this.btnFlag = this.list.operatingStatus == "1" ? true : false;
      //   }
      // } else if (this.qualitativeType == "定性") {
      //   if (this.isShowQualitative == false) {
      //     this.btnFlag = false;
      //   } else {
      //     this.btnFlag = this.list.operatingStatus == "1" ? true : false;
      //   }
      // }
    },

    openPeerProcessor() {
      this.dialogPeerProcessorVisible = true;
    },

    PeerProcessorClose() {
      this.dialogPeerProcessorVisible = false;
    },
    qualitativeReviewClose() {
      this.dialogQualitativeReviewVisible = false;
      this.$refs.qualitativeReview.onReset();
    },
    //定性审核提交
    qualitativeReviewSubmit(data) {
      this.$emit("qualitativeReviewSubmit", data);
      this.dialogQualitativeReviewVisible = false;
    },
    qualitativeReview() {
      this.showDxsh = false;
      this.$nextTick(() => {
        this.showDxsh = true;
      });
      this.dialogQualitativeReviewVisible = true;
    },

    queryAttachmentList() {
      this.attachmentVisible = true;
    },
    closeAttachmentDialog() {
      this.attachmentVisible = false;
    },
    downloadAppendixFile(data) {
      this.appendixFileLoading = true;
      let param = {
        attId: data.id,
      };
      apiDownloadAppendixFile(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("文件下载成功");
          } else {
            this.$message.error(res.msg);
          }
          this.appendixFileLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("文件下载失败");
          this.appendixFileLoading = false;
        });
    },
    showTime(val) {
      // debugger
      if (val == 0) {
        return "0";
      }
      if (val == "" || null == val) {
        return "";
      }
      var time = "";
      var second = val % 60;
      var minute = parseInt(parseInt(val) / 60) % 60;
      time = second + "秒";
      if (minute >= 1) {
        time = minute + "分" + second + "秒";
      }
      var hour = parseInt(parseInt(val / 60) / 60) % 24;
      if (hour >= 1) {
        time = hour + "小时" + minute + "分" + second + "秒";
      }
      var day = parseInt(parseInt(parseInt(val / 60) / 60) / 24);
      if (day >= 1) {
        time = day + "天" + hour + "小时" + minute + "分" + second + "秒";
      }
      return time;
    },
    toRoutePage() {
      let url = window.location.protocol + "//" + window.location.host;
      // let url = "http://************:8088"; //测试环境地址
      // let url = "http://************"; //生产环境地址
      if (this.list.routeProcessInstId != null) {
        url +=
          "/ibpm-ui/#/contentTo?content=Y&needi=Y&no=detail&hideHeader=1&reqFrom=handle&processInstId=" +
          this.list.routeProcessInstId +
          "&jobCode=" +
          this.list.routeNo +
          "&globalUniqueID=" +
          sessionStorage.getItem("globalUniqueID");
      } else {
        url +=
          "/EOM_LIFE_UI/#/transfer/draftform?hideHeader=1&hideTagsNav=1&actonShow=false&jobcode=" +
          this.list.routeNo;
      }
      window.open(url);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../assets/common.scss";

.header-tabMenu {
  margin-top: 10px;
}

.sheetNo_class {
  color: #b50b14;
  user-select: unset;
  padding: 0px;
  border-bottom: solid 1px #b50b14;
}

.descriptions {
  ::v-deep .el-descriptions-item__content {
    /*overflow: hidden;*/
    /*text-overflow: ellipsis;*/
    /*white-space: nowrap;*/

    position: relative;
    word-break: break-all;
    padding-right: 8px;
  }
}

::v-deep .el-descriptions-item__cell {
  vertical-align: top;
}
</style>
