<template>
  <div class="hang-up">
    <el-form ref="hangUpForm" :model="hangUpForm" label-width="90px">
      <el-form-item :label="personName" prop="hangUpPerson">
        <div style="width: 250px">{{ hangUpForm.hangUpPerson }}</div>
      </el-form-item>
      <el-form-item :label="dynamicTime" prop="hangUpTime">
        <div style="width: 250px">
          {{ hangUpForm.hangUpTime }}
        </div>
      </el-form-item>
      <el-form-item label="支撑材料:">
        <div style="width: 360px">
          <el-tag
            class="fileName_style"
            v-for="(item, index) in hangUpForm.hangUpFilesList"
            :key="index"
            @close="close(item)"
            :title="item.name"
          >
            <div class="text-truncate">{{ item.name }}</div>
          </el-tag>
          <el-button size="mini" type="primary" @click="attachmentBrowse"
          >+上传附件</el-button
          >
        </div>
      </el-form-item>
      <el-form-item
        label="操作意见:"
        prop="processSuggestion"
        :rules="{
          required: true,
          message: '请填写操作意见',
        }"
      >
        <el-input
          type="textarea"
          :rows="2"
          placeholder="请填写操作意见"
          v-model="hangUpForm.processSuggestion"
          style="width: 360px"
          show-word-limit
          maxlength="500"
          @keyup.native="descTip(500,'processSuggestion','showTip')"
        >
        </el-input>
        <div class="el-form-item__error"  v-if="showTip">已超过填写字数上限</div>
      </el-form-item>
    </el-form>

    <div slot="footer" style="padding: 10px 20px; text-align: right">
      <el-button
        type="primary"
        @click="handleHangUpSubmit('hangUpForm')"
        v-loading.fullscreen.lock="hangUpFullScreenLoading"
      >提 交</el-button
      >
      <el-button @click="onResetHangUp">重 置</el-button>
    </div>
    <el-dialog
      width="420px"
      title="附件选择"
      :visible.sync="hangUpFileDialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <file-upload
        @change="changeFileData"
        @cancel="closeAttachmentDialog"
      ></file-upload>
    </el-dialog>
  </div>
</template>
<script>
import moment from "moment";
import { mapGetters } from "vuex";
import { apiActionPublic,apiGroupCode,apiFileUpload } from "../api/CommonApi";
import FileUpload from "./FileUpload.vue";

export default {
  name: "HangUp",
  props: {
    common: Object,
    opType: Number,
    actionName:String,
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  components: { FileUpload },
  data() {
    return {
      showTip:false,
      showTime:5000,
      personName: "",
      dynamicTime: "",
      //挂起
      hangUpForm: {
        hangUpPerson: null,
        hangUpTime: null,
        processSuggestion: null,
        hangUpFilesList: [],
        fileName: null,
      },
      hangUpFileDialogVisible: false,
      hangUpFullScreenLoading: false,
    };
  },
  created() {},
  mounted() {
    this.hangUpForm.hangUpPerson = this.userInfo.realName;
    this.hangUpForm.hangUpTime = moment().format("YYYY-MM-DD HH:mm:ss");
    if (this.opType == 1) {
      this.personName = "挂起人:";
      this.dynamicTime = "挂起时间:";
    } else if (this.opType == 2) {
      this.personName = "解挂人:";
      this.dynamicTime = "解挂时间:";
    }
    console.log(this.common.professionalTypeName);
  },
  methods: {

    descTip(count,name,showName){
      if (this.hangUpForm[name].length>=count){
        this[showName] = true;
        setTimeout(() => {
          this[showName] = false;
        }, this.showTime);
      }
      else{
        this[showName] = false;
      }
    },
    handleHangUpSubmit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          let self = this;
          this.hangUpFullScreenLoading = true;
          // 如果有附件，先上传附件
          let isUpload = false;
          let formData = new FormData();
          if (self.hangUpForm.hangUpFilesList.length > 0) {
            isUpload = true;
            for (let item of self.hangUpForm.hangUpFilesList) {
              formData.append("files", item.raw);
            }
          } else {
            isUpload = false;
            formData.append("files", "");
          }

          let jsonParam = {
            groupKey:'',
            woId: this.common.woId,
            actionName:this.actionName,
            processInstId: this.common.processInstId,
            // processDefId: this.common.processDefId,
            workItemId: this.common.workItemId,
            processNode: this.common.processNode,
            operationalAdvice: this.hangUpForm.processSuggestion,
            hangUserEn: this.userInfo.userName,
            hangTime: this.hangUpForm.hangUpTime,
          };
          // 上传附件
          if (isUpload) {
            apiGroupCode()
              .then(res => {
                if (res.status == 0) {
                  let processId = res.data?.linkId;

                  let uploadUrl = `/commonprovince/attach/upload?groupKey=${this.common.woId}&processId=${processId}&processNode=${this.common.processNode}&sheetCreateTime=${this.common.sheetCreateTime}`;
                  apiFileUpload(uploadUrl, formData)
                    .then(res => {
                      if (res.code == 200) {
                        jsonParam.groupKey = processId;
                        this.submit(jsonParam);
                        this.hangUpForm.hangUpFilesList = [];
                      } else {
                        this.$message.error(res.msg);
                        // this.importForm.relatedFilesFileList = [];
                        this.hangUpFullScreenLoading = false;
                        return false;
                      }
                    })
                    .catch(error => {
                      console.log(error);
                      this.hangUpFullScreenLoading = false;
                    });
                }
              })
              .catch(error => {
                console.log(error);
                this.hangUpFullScreenLoading = false;
              });
          }
          else
          {
            this.submit(jsonParam);
          }

        } else {
          return false;
        }
      });
    },
    submit(param){
      apiActionPublic(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success(res.msg);

            this.onResetHangUp();
            this.$emit("closeDialogHangUp");
          } else {
            this.$message.error(res.msg);
          }
          this.hangUpFullScreenLoading = false;
        })
        .catch(error => {
          console.log(error);
          if (this.opType == 1) {
            this.$message.error(error.msg);
          } else if (this.opType == 2) {
            this.$message.error(error.msg);
          }
          this.hangUpFullScreenLoading = false;
        });
    },
    onResetHangUp() {
      this.hangUpForm = {
        // ...this.$options.data,
        processSuggestion: null,
        hangUpFilesList: [],
        fileName: null,
        hangUpPerson: this.userInfo.realName,
        hangUpTime: moment().format("YYYY-MM-DD HH:mm:ss"),
      };
    },
    attachmentBrowse() {
      this.hangUpFileDialogVisible = true;
    },
    changeFileData(data) {
      this.hangUpForm.fileName = data.fileName;
      this.hangUpForm.hangUpFilesList = data.attachmentFileList;
      this.hangUpFileDialogVisible = false;
    },
    closeAttachmentDialog() {
      this.hangUpFileDialogVisible = false;
    },
    close(tag) {
      this.hangUpForm.hangUpFilesList.splice(
        this.hangUpForm.hangUpFilesList.indexOf(tag),
        1
      );
    },
  },
};
</script>
<style lang="scss" scoped>
  .hang-up {
    .fileName_style {
      margin-right: 3px;
      vertical-align: middle;

      div {
        display: inline-block;
        max-width: 320px;
        vertical-align: top;
      }
    }
  }
</style>
