<template>
  <el-scrollbar class="qualitative" ref="scroll">
    <el-form
      ref="qualitativeForm"
      :inline="false"
      class="demo-form-inline"
      :model="qualitativeForm"
      label-width="130px"
      :rules="qualitativeFormRule"
    >
      <el-card
        shadow="never"
        header="故障定性信息"
        :body-style="{ padding: '20px 8px' }"
        class="cus-card"
      >
        <el-row type="flex" style="flex-wrap: wrap" :gutter="30">
          <el-col :span="8">
            <el-form-item label="故障所属专业:" prop="professionalType">
              <el-select
                v-model="qualitativeForm.professionalType"
                placeholder="请选择内容"
                style="width: 100%"
                @change="professionalTypeChange"
              >
                <el-option
                  v-for="(item, i) in professionalList"
                  :key="i"
                  :label="item.dictName"
                  :value="{ value: item.dictCode, label: item.dictName }"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="故障等级:"
              prop="faultLevel"
              :rules="{
                required: true,
                message: '请选择故障等级',
              }"
            >
              <el-select
                v-model="qualitativeForm.faultLevel"
                placeholder="请选择内容"
                style="width: 100%"
              >
                <el-option
                  v-for="(item, i) in levelList"
                  :key="i"
                  :label="item.dictName"
                  :value="item.dictName"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="故障发生时间:"
              prop="alarmCreateTime"
              :rules="{
                required: true,
                message: '请选择故障发生时间',
              }"
            >
              <el-input
                v-model="qualitativeForm.alarmCreateTime"
                readonly
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="通知时间:"
              prop="faultNoticeTime"
              :rules="{
                required: true,
                message: '请选择通知时间',
              }"
            >
              <el-input
                v-model="qualitativeForm.faultNoticeTime"
                readonly
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障恢复时间:" prop="faultRecoveryTime">
              <el-date-picker
                v-model="qualitativeForm.faultRecoveryTime"
                type="datetime"
                placeholder="请选择时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
                style="width: 100%"
                :picker-options="recoveryPicker"
                :default-time="recoveryDefaultTime"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障处理历时:" prop="faultDuration">
              {{ qualitativeForm.faultDuration }}
              <!-- {{ second2Time(qualitativeForm.faultDuration) }} -->
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障恢复历时:" prop="recoveryDuration">
              <!-- {{ second2Time(qualitativeForm.recoveryDuration) }} -->
              {{ qualitativeForm.recoveryDuration }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="挂起历时:" prop="suspendDuration">
              {{ qualitativeForm.suspendDuration }}分钟
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障处理部门:" required>
              <el-input
                v-model="qualitativeForm.dept"
                placeholder="故障处理部门"
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障修复人:" prop="recoveryPerson">
              <el-input
                v-model="qualitativeForm.recoveryPerson"
                placeholder="故障修复人"
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="修复人电话:" prop="recoveryPhone">
              <el-input
                v-model="qualitativeForm.recoveryPhone"
                placeholder="修复人电话"
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="是否影响业务:"
              prop="isEffectBusiness"
              :rules="[
                {
                  required: true,
                  message: '请选择是否影响业务',
                },
              ]"
            >
              <el-radio-group v-model="qualitativeForm.isEffectBusiness">
                <el-radio label="否">否</el-radio>
                <el-radio label="是">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="是否基站退服:"
              prop="isSiteOffline"
              :rules="{
                required: true,
                message: '请选择是否基站退服',
                trigger: 'change',
              }"
            >
              <el-radio-group v-model="qualitativeForm.isSiteOffline">
                <el-radio label="否">否</el-radio>
                <el-radio label="是">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="qualitativeForm.isSiteOffline == '是'">
            <el-form-item label="退服原因:">
              <el-select
                v-model="qualitativeForm.siteOfflineReason"
                placeholder="请选择内容"
                style="width: 100%"
              >
                <el-option
                  v-for="(item, i) in siteOfflineReasonList"
                  :key="i"
                  :label="item.dictName"
                  :value="item.dictName"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="是否铁塔原因:"
              prop="isTowerReason"
              :rules="{
                required: true,
                message: '请选择是否基站退服',
                trigger: 'change',
              }"
            >
              <el-radio-group
                v-model="qualitativeForm.isTowerReason"
                style="width: 250px"
              >
                <el-radio label="否">否</el-radio>
                <el-radio label="是">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="附件:" prop="attachmentFile">
              <el-input
                v-model="qualitativeForm.attachmentFile"
                placeholder="添加附件"
                clearable
                readonly
                style="width: 100%"
              >
                <el-button type="info" slot="append" @click="attachmentBrowse"
                  >+</el-button
                >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card
        shadow="never"
        header="故障专业信息"
        :body-style="{ padding: '20px 8px' }"
        style="margin-top: 20px"
        class="cus-card"
      >
        <!-- 无线网、核心网、动环网、数据网、铁塔、接入网 -->
        <template
          v-if="
            qualitativeForm.professionalTypeId == 7 ||
            qualitativeForm.professionalTypeId == 1 ||
            qualitativeForm.professionalTypeId == 4 ||
            qualitativeForm.professionalTypeId == 5 ||
            qualitativeForm.professionalTypeId == 9 ||
            qualitativeForm.professionalTypeId == 10
          "
        >
          <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
            <el-col :span="8">
              <el-form-item
                label="故障发生地区:"
                prop="pList01.faultRegion"
                :rules="{
                  required: true,
                  message: '请选择内容',
                  trigger: ['blur'],
                }"
              >
                <el-select
                  style="width: 100%"
                  v-model="qualitativeForm.pList01.faultRegion"
                >
                  <el-option
                    v-for="(item, i) in faultRegionOptions"
                    :key="i"
                    :label="item.name"
                    :value="item.name"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="故障分类:"
                prop="pList01.faultCate"
                :rules="{
                  required: true,
                  message: '请选择故障分类',
                  trigger: ['blur'],
                }"
              >
                <el-select
                  v-model="qualitativeForm.pList01.faultCate"
                  placeholder="请选择内容"
                  style="width: 100%"
                  @focus="
                    getDictData(
                      qualitativeForm.professionalTypeId + '_faultCate',
                      'faultCateList'
                    )
                  "
                  @change="faultCateChange"
                >
                  <el-option
                    v-for="(item, i) in faultCateList"
                    :key="i"
                    :label="item.dictName"
                    :value="{ value: item.dictCode, label: item.dictName }"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="故障原因:"
                :rules="{
                  required: true,
                  message: '请选择故障原因',
                  trigger: ['blur'],
                }"
                prop="pList01.faultReason"
              >
                <el-select
                  v-model="qualitativeForm.pList01.faultReason"
                  placeholder="请选择内容"
                  style="width: 100%"
                >
                  <el-option
                    v-for="(item, i) in faultReasonList"
                    :key="i"
                    :label="item.dictName"
                    :value="item.dictName"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="故障厂家:"
                prop="pList01.vendor"
                :rules="{
                  required: true,
                  message: '请选择故障厂家',
                  trigger: ['blur'],
                }"
              >
                <el-select
                  style="width: 100%"
                  v-model="qualitativeForm.pList01.vendor"
                >
                  <el-option
                    v-for="(item, i) in vendorList"
                    :key="i"
                    :label="item.dictName"
                    :value="item.dictName"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="设备类型:" prop="pList01.eqpType">
                <el-select
                  style="width: 100%"
                  v-model="qualitativeForm.pList01.eqpType"
                >
                  <el-option
                    v-for="(item, i) in deviceTypeList"
                    :key="i"
                    :label="item.dictName"
                    :value="item.dictName"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="设备名称:">
                <el-input
                  v-model="qualitativeForm.pList01.eqpName"
                  placeholder="请输入内容"
                  style="width: 100%"
                >
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </template>
        <!-- 传输网 -->
        <template v-if="qualitativeForm.professionalTypeId == 3">
          <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
            <el-col :span="8">
              <el-form-item
                label="故障状态:"
                prop="pList02.faultStatus"
                :rules="{
                  required: true,
                  message: '请选择故障状态',
                }"
              >
                <el-select
                  style="width: 100%"
                  v-model="qualitativeForm.pList02.faultStatus"
                >
                  <el-option
                    v-for="(item, i) in faultStatusList"
                    :key="i"
                    :label="item.dictName"
                    :value="item.dictName"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="网络类型:"
                prop="pList02.networkType"
                :rules="{
                  required: true,
                  message: '请选择网络类型',
                }"
              >
                <el-select
                  v-model="qualitativeForm.pList02.networkType"
                  placeholder="请选择内容"
                  style="width: 100%"
                  @focus="
                    getDictData(
                      qualitativeForm.professionalTypeId + '_networkType',
                      'networkTypeList'
                    )
                  "
                >
                  <el-option
                    v-for="(item, i) in networkTypeList"
                    :key="i"
                    :label="item.dictName"
                    :value="item.dictName"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="故障源:">
                <el-input
                  placeholder="请输入内容"
                  v-model="qualitativeForm.pList02.faultSource"
                  style="width: 100%"
                >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="处理方法:">
                <el-input
                  placeholder="请输入内容"
                  v-model="qualitativeForm.pList02.processMethod"
                  style="width: 100%"
                >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="故障分类:"
                prop="pList02.faultCate"
                :rules="{
                  required: true,
                  message: '请选择故障分类',
                  trigger: ['blur'],
                }"
              >
                <el-select
                  v-model="qualitativeForm.pList02.faultCate"
                  placeholder="请选择内容"
                  style="width: 100%"
                  @focus="
                    getDictData(
                      qualitativeForm.professionalTypeId + '_faultCate',
                      'faultCateList'
                    )
                  "
                  @change="faultCateChange"
                >
                  <el-option
                    v-for="(item, i) in faultCateList"
                    :key="i"
                    :label="item.dictName"
                    :value="{ value: item.dictCode, label: item.dictName }"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="故障原因:"
                :rules="{
                  required: true,
                  message: '请选择故障原因',
                  trigger: ['blur'],
                }"
                prop="pList02.faultReason"
              >
                <el-select
                  v-model="qualitativeForm.pList02.faultReason"
                  placeholder="请选择内容"
                  style="width: 100%"
                >
                  <el-option
                    v-for="(item, i) in faultReasonList"
                    :key="i"
                    :label="item.dictName"
                    :value="item.dictName"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="设备类型:" prop="pList02.eqpType">
                <el-select
                  style="width: 100%"
                  v-model="qualitativeForm.pList02.eqpType"
                >
                  <el-option
                    v-for="(item, i) in deviceTypeList"
                    :key="i"
                    :label="item.dictName"
                    :value="item.dictName"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="设备名称:">
                <el-input
                  v-model="qualitativeForm.pList02.eqpName"
                  placeholder="请输入内容"
                  style="width: 100%"
                >
                </el-input>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="机盘名称:">
                <el-input
                  placeholder="请输入内容"
                  v-model="qualitativeForm.pList02.diskName"
                  style="width: 100%"
                >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="业务中断系统:">
                <el-input
                  placeholder="请输入内容"
                  v-model="qualitativeForm.pList02.interSystem"
                  style="width: 100%"
                >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="受影响的系统:">
                <el-input
                  placeholder="请输入内容"
                  v-model="qualitativeForm.pList02.effectSystem"
                  style="width: 100%"
                >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="中断电路:">
                <el-input
                  placeholder="请输入内容"
                  v-model="qualitativeForm.pList02.interCircuit"
                  style="width: 100%"
                >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="故障区间:">
                <el-input
                  placeholder="请输入内容"
                  v-model="qualitativeForm.pList02.faultRange"
                  style="width: 100%"
                >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="受影响的电路:">
                <el-input
                  placeholder="请输入内容"
                  v-model="qualitativeForm.pList02.effectCircuit"
                  style="width: 100%"
                >
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </template>
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="24">
            <el-form-item
              label="故障处理过程及原因描述:"
              prop="falutReasonDesc"
              :rules="{
                required: true,
                message: '故障原因描述不能为空',
              }"
            >
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="qualitativeForm.falutReasonDesc"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="备注:">
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="qualitativeForm.falutComment"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
    </el-form>
    <div style="padding: 10px 20px; text-align: center">
      <el-button
        type="primary"
        @click="handleSubmit('qualitativeForm')"
        v-loading.fullscreen.lock="qualitativeFullscreenLoading"
        >提交</el-button
      >
      <el-button @click="onReset">重 置</el-button>
    </div>
    <el-dialog
      width="420px"
      title="附件选择"
      append-to-body
      :visible.sync="attachmentDialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <file-upload
        v-if="attachmentDialogVisible"
        @change="changeFileData"
        @cancel="closeAttachmentDialog"
      ></file-upload>
    </el-dialog>
  </el-scrollbar>
</template>
<script>
import moment, { now } from "moment";
import { mapGetters } from "vuex";
import {
  apiGetFaultArea,
  apiActionPublic,
  getCurrentTime,
  apiInitQualitative,
  apiDict,
  apiGroupCode,
  apiFileUpload,
} from "../api/CommonApi";

import { apiGetOrgInfo } from "../../../workOrder/api/CommonApi";
import { apiDeleteFdFile } from "../../../workOrder/workOrderWaitDetail/api/CommonApi";
import FileUpload from "./FileUpload.vue";
export default {
  name: "Qualitative",
  props: {
    common: Object,
    workItemId: [String, Number],
    isUploadReport: [String, Number],
    qData: Object,
  },
  components: { FileUpload },

  computed: {
    ...mapGetters(["userInfo"]),
  },
  data() {
    var validLastClearTime = (rule, value, callback) => {
      if (
        this.qualitativeForm.lastClearTime &&
        this.qualitativeForm.faultEndTime
      ) {
        let seconds = moment(
          this.qualitativeForm.faultEndTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.qualitativeForm.lastClearTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );

        let seconds2 = moment(
          this.qualitativeForm.lastClearTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.common.failureInformTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        if (seconds < 0 || seconds2 <= 0) {
          callback(
            new Error(
              "故障结束时间>=故障代通时间>故障通知时间，请重新检查后选择正确时间"
            )
          );
        } else {
          callback();
        }
      } else if (this.qualitativeForm.lastClearTime) {
        let seconds3 = moment(
          this.qualitativeForm.lastClearTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(moment(new Date(), "YYYY-MM-DD HH:mm:ss"), "seconds");
        let seconds4 = moment(
          this.qualitativeForm.lastClearTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.common.failureInformTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        if (seconds3 > 0 || seconds4 <= 0) {
          callback(
            new Error(
              "当前时间>=故障代通时间>故障通知时间，请重新检查后选择正确时间"
            )
          );
        }
      } else {
        callback();
      }
    };
    var validFaultEndTime = (rule, value, callback) => {
      if (value === "" || value === null) {
        callback(new Error("请选择故障结束时间"));
      } else {
        if (this.qualitativeForm.lastClearTime) {
          let clSeconds = moment(
            this.qualitativeForm.faultEndTime,
            "YYYY-MM-DD HH:mm:ss"
          ).diff(
            moment(this.qualitativeForm.lastClearTime, "YYYY-MM-DD HH:mm:ss"),
            "seconds"
          );
          if (clSeconds < 0) {
            callback(
              new Error(
                "当前时间>=故障结束时间>=故障代通时间，请重新检查后选择正确时间"
              )
            );
          }
        }

        let seconds = moment(
          this.qualitativeForm.faultEndTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.common.failureInformTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );

        let seconds2 = moment(new Date(), "YYYY-MM-DD HH:mm:ss").diff(
          moment(this.qualitativeForm.faultEndTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        if (seconds <= 0 || seconds2 < 0) {
          callback(
            new Error(
              "当前时间>=故障结束时间>故障通知时间，请重新检查后选择正确时间"
            )
          );
        } else {
          callback();
        }
      }
    };

    return {
      qualitativeForm: {
        professionalType: "",
        faultLevel: "",
        alarmCreateTime: "",
        faultNoticeTime: "",
        faultRecoveryTime: "",
        faultDuration: "",
        recoveryDuration: "",
        suspendDuration: "",
        dept: "",
        recoveryPerson: "",
        recoveryPhone: "",
        isEffectBusiness: "否",
        isSiteOffline: "否",
        isTowerReason: "否",

        falutReasonDesc: "",
        falutComment: "",
        attachmentFile: "", //附件

        // 不同故障的专业信息
        pList01: {
          faultRegion: "",
          faultCate: "",
          faultReason: "",
          vendor: "",
          eqpType: "",
          eqpName: "",
        },
        pList02: {
          faultStatus: "",
          networkType: "",
          faultSource: "",
          faultCate: "",
          faultReason: "",
          eqpType: "",
          eqpName: "",
          diskName: "",
          interSystem: "",
          effectSystem: "",
          interCircuit: "",
          faultRange: "",
          effectCircuit: "",
        },
      },
      userData: null,
      recoveryPicker: {},
      initData: {}, // 初始化数据
      formInit: {},
      //关联附件
      importForm: {
        attachmentFileList: [],
      },
      attachmentDialogVisible: false,

      qualitativeFullscreenLoading: false,
      startTimeRange: "",
      startTimeRangeByFaultEndTime: "",
      recoveryDefaultTime: "",
      // 下拉框
      professionalList: [], //故障专业
      levelList: [], // 故障等级
      faultCateList: [], //故障分类
      faultReasonList: [], //故障原因
      vendorList: [], // 设备厂家
      deviceTypeList: [], // 设备类型
      networkTypeList: [], //网络类型
      faultStatusList: [], // 故障状态
      faultRegionOptions: [], //地区
      siteOfflineReasonList: [], //退服原因

      qualitativeFormRule: {
        professionalType: [
          { required: true, message: "请选择故障所属专业", trigger: "blur" },
        ],
        faultRecoveryTime: [
          {
            required: true,
            message: "请选择故障恢复时间",
            trigger: "blur",
          },
        ],
        alarmCreateTime: [
          {
            required: true,
            message: "请选择故障发生时间",
            trigger: "blur",
          },
        ],
        lastClearTime: [{ validator: validLastClearTime, trigger: "blur" }],
        faultEndTime: [{ validator: validFaultEndTime, required: true }],
      },
    };
  },

  watch: {
    "qualitativeForm.faultRecoveryTime": {
      immediate: false,
      handler: function (newVal, oldVal) {
        if (
          (newVal != null || newVal != "") &&
          oldVal != undefined &&
          newVal != oldVal
        ) {
          this.computerFaultTreatmentTime();
          const time1 = new Date(
            this.qualitativeForm.alarmCreateTime.slice(0, 10) + " 00:00:00"
          ).getTime();
          const time2 = new Date(newVal.slice(0, 10) + " 00:00:00").getTime();
          console.log(getCurrentTime(Date.now()).slice(0, 10) + " 00:00:00");
          const time3 = new Date(
            getCurrentTime(Date.now()).slice(0, 10) + " 00:00:00"
          ).getTime();
          const nowTimeStr = getCurrentTime(Date.now()).slice(-8);
          if (time2 > time1) {
            this.recoveryPicker.selectableRange = `"00:00:00" - "23:59:59"`;
          } else {
            this.recoveryPicker.selectableRange = `${this.recoveryDefaultTime} - "23:59:59"`;
          }
          if (time2 == time3) {
            this.recoveryPicker.selectableRange = `"00:00:00" - ${nowTimeStr}`;
          }
        }
      },
    },
  },
  async created() {
    this.formInit = JSON.parse(JSON.stringify(this.qualitativeForm));
    // 初始化 下拉框
    await this.getDictData("professional_type", "professionalList");
    await this.getDictData("fault_level", "levelList");
    this.getDictData("fault_vendor", "vendorList");
    this.getDictData("device_type", "deviceTypeList");
    this.getDictData("fault_status", "faultStatusList");
    this.getDictData("site_offline_reason", "siteOfflineReasonList");
    // 初始化 数据
    await this.getInit();
    this.userData = JSON.parse(this.userInfo.attr2);
    // 获取地区，请求参数
    this.getOrgInfo();
  },
  methods: {
    dealDisabledDate(time) {
      let self = this;
      let beginDate = moment(self.common.failureInformTime).format(
        "YYYY-MM-DD"
      );
      let endDate = moment(Date.now()).format("YYYY-MM-DD");
      let arr = this.getAllDays(beginDate, endDate);
      const timeFormat = moment(time).format("YYYY-MM-DD");
      if (arr.indexOf(timeFormat) >= 0) {
        return false;
      }
      return true;
    },
    dealDisabledDateDt(time) {
      let self = this;
      let beginDate;
      let endDate;
      if (self.qualitativeForm.faultEndTime) {
        beginDate = moment(self.common.failureInformTime).format("YYYY-MM-DD");
        endDate = moment(self.qualitativeForm.faultEndTime).format(
          "YYYY-MM-DD"
        );
      } else {
        beginDate = moment(self.common.failureInformTime).format("YYYY-MM-DD");
        endDate = moment(Date.now()).format("YYYY-MM-DD");
      }
      let arr = this.getAllDays(beginDate, endDate);
      const timeFormat = moment(time).format("YYYY-MM-DD");
      if (arr.indexOf(timeFormat) >= 0) {
        return false;
      }
      return true;
    },
    getAllDays(begin_date, end_date) {
      const errArr = [],
        resultArr = [],
        dateReg = /^[2]\d{3}-[01]\d-[0123]\d$/;

      if (
        typeof begin_date !== "string" ||
        begin_date === "" ||
        !dateReg.test(begin_date)
      ) {
        return errArr;
      }

      if (
        typeof end_date !== "string" ||
        end_date === "" ||
        !dateReg.test(end_date)
      ) {
        return errArr;
      }

      try {
        const beginTimestamp = Date.parse(new Date(begin_date)),
          endTimestamp = Date.parse(new Date(end_date));

        // 开始日期小于结束日期
        if (beginTimestamp > endTimestamp) {
          return errArr;
        }

        // 开始日期等于结束日期
        if (beginTimestamp === endTimestamp) {
          resultArr.push(begin_date);
          return resultArr;
        }

        let tempTimestamp = beginTimestamp,
          tempDate = begin_date;

        // 新增日期是否和结束日期相等， 相等跳出循环
        while (tempTimestamp !== endTimestamp) {
          resultArr.push(tempDate);

          // 增加一天
          tempDate = moment(tempTimestamp).add(1, "d").format("YYYY-MM-DD");

          // 将增加时间变为时间戳
          tempTimestamp = Date.parse(new Date(tempDate));
        }

        // 将最后一天放入数组
        resultArr.push(end_date);
        return resultArr;
      } catch (err) {
        return errArr;
      }
    },
    getInit() {
      let param = {
        woId: this.common.woId,
      };
      apiInitQualitative(param)
        .then(res => {
          if (res.status == "0") {
            // this.qualitativeForm = JSON.parse(JSON.stringify(res.data));
            this.initData = JSON.parse(JSON.stringify(res.data));
            // 回显 - 定性信息
            Object.keys(this.qualitativeForm).forEach(item => {
              Object.keys(res.data).forEach(i => {
                if (res.data[i] !== null && item == i) {
                  this.qualitativeForm[item] = res.data[i];
                }
              });
            });
            this.computerFaultTreatmentTime();
            // 云表部分字段赋值回显
            this.qualitativeForm.workItemId = this.workItemId;
            this.qualitativeForm.woId = this.common.woId;
            this.qualitativeForm.processInstId = this.common.processInstId;
            this.qualitativeForm.processDefId = this.common.processDefId;

            if (this.qualitativeForm.professionalType == "无线网") {
              this.qualitativeForm.professionalTypeId = 7;
            } else {
              this.professionalList.forEach(item => {
                if (this.qualitativeForm.professionalType == item.dictName) {
                  this.qualitativeForm.professionalTypeId = item.dictCode;
                }
              });
            }
            /*
              专业信息
              —— 无线网、核心网、动环网、数据网、铁塔、接入网 pList01
              —— 传输网 pList02
            */

            if (
              this.qualitativeForm.professionalTypeId == 7 ||
              this.qualitativeForm.professionalTypeId == 1 ||
              this.qualitativeForm.professionalTypeId == 4 ||
              this.qualitativeForm.professionalTypeId == 5 ||
              this.qualitativeForm.professionalTypeId == 9 ||
              this.qualitativeForm.professionalTypeId == 10
            ) {
              Object.keys(this.qualitativeForm.pList01).forEach(item => {
                Object.keys(res.data).forEach(i => {
                  if (res.data[i] !== null && item == i) {
                    this.qualitativeForm.pList01[item] = res.data[i];
                  }
                });
              });
            } else if (this.qualitativeForm.professionalTypeId == 3) {
              Object.keys(this.qualitativeForm.pList02).forEach(item => {
                Object.keys(res.data).forEach(i => {
                  if (res.data[i] !== null && item == i) {
                    this.qualitativeForm.pList02[item] = res.data[i];
                  }
                });
              });
            }

            if (res.data.isEffectBusiness == null) {
              this.qualitativeForm.isEffectBusiness = "否";
            }
            if (res.data.isSiteOffline == null) {
              this.qualitativeForm.isSiteOffline = "否";
            }
            if (res.data.isTowerReason == null) {
              this.qualitativeForm.isTowerReason = "否";
            }
            if (res.data.isSiteOffline == "是") {
              this.qualitativeForm.siteOfflineReason =
                res.data.siteOfflineReason;
            }
            // 设置 时间组件，数据显示
            this.setRecoveryPicker();
            this.$nextTick(() => {
              this.$refs.qualitativeForm.clearValidate();
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    getOrgInfo() {
      apiGetOrgInfo()
        .then(res => {
          if (res.status == "0") {
            this.qualitativeForm.areaCode = res?.data?.orgInfo?.areaCode ?? "";
            this.qualitativeForm.category = res?.data?.category ?? "";
            this.getFaultAreaOptions();
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    getFaultAreaOptions() {
      let param = {
        areaCode: this.qualitativeForm.areaCode,
        category: this.qualitativeForm.category,
      };
      apiGetFaultArea(param)
        .then(res => {
          if (res.status == "0") {
            this.faultRegionOptions = res?.data ?? [];
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    computerFaultGenerationAter() {
      let days = moment(
        this.qualitativeForm.lastClearTime,
        "YYYY-MM-DD HH:mm:ss"
      ).diff(
        moment(this.qualitativeForm.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
        "seconds"
      );
      this.qualitativeForm.lastClearDuration = days;
    },
    second2Time(days) {
      return this.showTimeNew(Math.abs(days));
    },
    computerFaultTreatmentTime() {
      let dealTime = moment(
        this.qualitativeForm.faultRecoveryTime,
        "YYYY-MM-DD HH:mm:ss"
      ).diff(
        moment(this.qualitativeForm.faultNoticeTime, "YYYY-MM-DD HH:mm:ss"),
        "seconds"
      );

      let recoveryTime = moment(
        this.qualitativeForm.faultRecoveryTime,
        "YYYY-MM-DD HH:mm:ss"
      ).diff(
        moment(this.qualitativeForm.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
        "seconds"
      );
      this.qualitativeForm.faultDuration = this.second2Time(dealTime);
      this.qualitativeForm.recoveryDuration = this.second2Time(recoveryTime);
    },
    handleSubmit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.qualitativeFullscreenLoading = true;
          this.$set(this.qualitativeForm, "actionName", "故障定性");
          // 如果有附件，先上传附件
          let isUpload = false;
          let formData = new FormData();
          if (
            this.qualitativeForm.attachmentFile != "" &&
            this.importForm.attachmentFileList.length > 0
          ) {
            isUpload = true;
            for (let item of this.importForm.attachmentFileList) {
              formData.append("files", item.raw);
            }
          } else {
            isUpload = false;
            formData.append("files", "");
          }
          let paramter = {
            woId: this.common.woId,
            processInstId: this.common.processInstId,
            workItemId: this.common.workItemId,
            actionName: this.common.actionName,
          };
          // 如果是否基站退服，是否，清除退服原因
          if (
            this.qualitativeForm.isSiteOffline == "否" &&
            this.qualitativeForm.siteOfflineReason != undefined
          ) {
            delete this.qualitativeForm.siteOfflineReason;
          }
          let params = {};
          if (
            this.qualitativeForm.professionalTypeId == 7 ||
            this.qualitativeForm.professionalTypeId == 1 ||
            this.qualitativeForm.professionalTypeId == 4 ||
            this.qualitativeForm.professionalTypeId == 5 ||
            this.qualitativeForm.professionalTypeId == 9 ||
            this.qualitativeForm.professionalTypeId == 10
          ) {
            params = Object.assign(
              paramter,
              this.qualitativeForm,
              this.qualitativeForm.pList01
            );
          } else if (this.qualitativeForm.professionalTypeId == 3) {
            params = Object.assign(
              paramter,
              this.qualitativeForm,
              this.qualitativeForm.pList02
            );
          } else {
            params = Object.assign(paramter, this.qualitativeForm);
          }
          delete params.pList01;
          delete params.pList02;
          // 获取上传附件id
          if (isUpload) {
            apiGroupCode()
              .then(res => {
                if (res.status == 0) {
                  let processId = res.data?.linkId;

                  let uploadUrl = `/province/attach/upload?groupKey=${this.common.woId}&processId=${processId}&processNode=${this.common.processNode}&sheetCreateTime=${this.common.sheetCreateTime}`;
                  apiFileUpload(uploadUrl, formData)
                    .then(res => {
                      if (res.code == 200) {
                        params.linkId = processId;
                        this.submitFn(params);
                      } else {
                        this.$message.error("上传失败，请重新上传");
                        return false;
                      }
                    })
                    .catch(error => {
                      console.log(error);
                    });
                }
              })
              .catch(error => {
                console.log(error);
              });
          } else {
            this.submitFn(params);
          }
        } else {
          return false;
        }
      });
    },
    submitFn(params) {
      apiActionPublic(params)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("提交定性成功");
            this.onReset();
            this.$emit("closeDialogQualitative", res.data);
          } else {
            this.$message.error(res.msg);
          }
          this.qualitativeFullscreenLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error(error.msg);
          this.qualitativeFullscreenLoading = false;
        });
    },
    closeAndDeleteFile(tag) {
      this.deleteFile(tag, "xl");
    },
    closeAndDeleteFileDl(tag) {
      this.deleteFile(tag, "dl");
    },
    deleteFile(tag, type) {
      let param = {
        attId: tag.id,
        linkId: this.qualitativeForm.linkId,
      };
      apiDeleteFdFile(param)
        .then(res => {
          if (res.status == "0") {
            if (type == "xl") {
              this.fdFileXlArr.splice(this.fdFileXlArr.indexOf(tag), 1);
              this.qualitativeForm.appendix = JSON.stringify(this.fdFileXlArr);
            } else if (type == "dl") {
              this.fdFileDlArr.splice(this.fdFileDlArr.indexOf(tag), 1);
              this.qualitativeForm.appendix = JSON.stringify(this.fdFileDlArr);
            }
            this.$message.success("附件删除成功");
          } else {
            this.$message.error("附件删除失败");
          }
        })
        .catch(error => {
          this.$message.error("附件删除失败");
          console.log(error);
        });
    },
    closeFile(tag) {
      this.importForm.relatedFilesFileList.splice(
        this.importForm.relatedFilesFileList.indexOf(tag),
        1
      );
    },

    dealTimeout() {
      //故障代通时间（无故障代通时间 用故障结束时间） - 故障开始时间
      if (this.qualitativeForm.lastClearTime) {
        let seconds = moment(
          this.qualitativeForm.lastClearTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.qualitativeForm.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        return seconds;
      } else {
        let seconds = moment(
          this.qualitativeForm.faultEndTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.qualitativeForm.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        return seconds;
      }
    },
    onReset() {
      this.qualitativeForm = JSON.parse(JSON.stringify(this.formInit));
      this.$nextTick(() => {
        this.$refs.qualitativeForm.clearValidate();
      });
      this.getInit();
      this.$refs.scroll.wrap.scrollTop = 0;
    },
    showTimeNew(val) {
      if (val) {
        if (val == 0) return "0秒";
        var time = "";
        var second = val % 60;
        var minute = parseInt(parseInt(val) / 60) % 60;
        time = second + "秒";
        if (minute >= 1) {
          time = minute + "分" + second + "秒";
        }
        var hour = parseInt(parseInt(val / 60) / 60) % 24;
        if (hour >= 1) {
          time = hour + "小时" + minute + "分" + second + "秒";
        }
        var day = parseInt(parseInt(parseInt(val / 60) / 60) / 24);
        if (day >= 1) {
          time = day + "天" + hour + "小时" + minute + "分" + second + "秒";
        }
        return time;
      } else {
        return "0秒";
      }
    },

    // 设置恢复时间，组件显示
    setRecoveryPicker() {
      // 故障恢复时间 >= 故障发生时间 且 故障恢复时间 <= 当前时间
      const alarmCreateTime = this.qualitativeForm.alarmCreateTime;
      this.recoveryDefaultTime = getCurrentTime(
        new Date(alarmCreateTime).getTime() + 60 * 1000
      ).slice(-8);
      this.recoveryPicker = {
        disabledDate: time => {
          return (
            time.getTime() <
              new Date(alarmCreateTime).getTime() - 3600 * 1000 * 24 ||
            time.getTime() > new Date(Date.now()).getTime()
          );
        },
        selectableRange: `${this.recoveryDefaultTime} - "23:59:59"`,
      };
    },

    getDictData(dictId, selectName) {
      this[selectName] = [];
      let param = {
        dictType: dictId,
      };

      apiDict(param)
        .then(res => {
          if (res.code == 200) {
            this[selectName] = res.data;
          }
        })
        .catch(error => {
          console.log(error);
          return false;
        });
    },

    // 专业类型——下拉枚举联动
    professionalTypeChange(val) {
      const { value, label } = val;
      this.qualitativeForm.professionalType = label;

      this.qualitativeForm.professionalTypeId = value;
      this.$set(
        this.qualitativeForm,
        this.qualitativeForm.professionalType,
        label
      );
      //无线网、核心网、动环网、数据网、铁塔、接入网
      if (
        this.qualitativeForm.professionalTypeId == 7 ||
        this.qualitativeForm.professionalTypeId == 1 ||
        this.qualitativeForm.professionalTypeId == 4 ||
        this.qualitativeForm.professionalTypeId == 5 ||
        this.qualitativeForm.professionalTypeId == 9 ||
        this.qualitativeForm.professionalTypeId == 10
      ) {
        this.qualitativeForm.pList02.faultStatus = this.initData.faultStatus;
        this.qualitativeForm.pList02.networkType = this.initData.networkType;
        this.qualitativeForm.pList02.faultCate = this.initData.faultCate;
        this.qualitativeForm.pList02.falutReason = this.initData.falutReason;
      } else if (this.qualitativeForm.professionalTypeId == 3) {
        //传输网
        this.qualitativeForm.pList01.faultCate = this.initData.faultCate;
        this.qualitativeForm.pList01.falutReason = this.initData.falutReason;
      }
      this.$nextTick(() => {
        this.$refs["qualitativeForm"].clearValidate();
      });
    },
    faultCateChange(val) {
      const { value, label } = val;
      if (
        this.qualitativeForm.professionalTypeId == 7 ||
        this.qualitativeForm.professionalTypeId == 1 ||
        this.qualitativeForm.professionalTypeId == 4 ||
        this.qualitativeForm.professionalTypeId == 5 ||
        this.qualitativeForm.professionalTypeId == 9 ||
        this.qualitativeForm.professionalTypeId == 10
      ) {
        this.qualitativeForm.pList01.faultCate = label;
        this.qualitativeForm.pList01.faultCateId = value;
        this.faultReasonList = [];
        // 获取故障原因，下拉列表
        this.getDictData(
          this.qualitativeForm.professionalTypeId +
            "_" +
            this.qualitativeForm.pList01.faultCateId +
            "_faultReason",
          "faultReasonList"
        );
      } else if (this.qualitativeForm.professionalTypeId == 3) {
        //传输网
        this.qualitativeForm.pList02.faultCate = label;
        this.qualitativeForm.pList02.faultCateId = value;
        this.faultReasonList = [];
        // 获取故障原因，下拉列表
        this.getDictData(
          this.qualitativeForm.professionalTypeId +
            "_" +
            this.qualitativeForm.pList02.faultCateId +
            "_faultReason",
          "faultReasonList"
        );
      }
    },
    //附件选择
    attachmentBrowse() {
      this.attachmentDialogVisible = true;
    },
    changeFileData(data) {
      this.qualitativeForm.attachmentFile = data.fileName;
      this.importForm.attachmentFileList = data.attachmentFileList;
      this.attachmentDialogVisible = false;
    },
    closeAttachmentDialog() {
      this.attachmentDialogVisible = false;
    },
  },
};
</script>
<style lang="scss" scoped>
.qualitative {
  height: 500px;
  .cus-card ::v-deep .el-card__header {
    padding: 11px 24px 10px;
    font-weight: 400;
    @include themify {
      background-color: themed("$--background-color-base");
    }
  }
}
</style>
