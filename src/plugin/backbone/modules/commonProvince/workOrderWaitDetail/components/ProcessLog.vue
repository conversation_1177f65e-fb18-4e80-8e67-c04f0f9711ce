<template>
  <el-card shadow="always" class="card" :body-style="{ padding: '10px 24px' }">
        <div class="header clearfix">
          <span class="header-title">流程日志</span>
        </div>
        <div class="content">
          <el-table
            :data="tableData"
            style="width: 100%"
            row-key="id"
            :tree-props="{ children: 'data', hasdata: 'hasdata' }"
            v-loading="tableLoading"
          >
            <el-table-column width="400" prop="deptname" label="部门名称">
            </el-table-column>
            <el-table-column prop="handlelink" label="环节名称">
            </el-table-column>

            <el-table-column prop="createtime" label="到达时间">
            </el-table-column>
            <el-table-column prop="actionname" label="处理步骤">
            </el-table-column>

            <el-table-column prop="handleuser" label="处理人">
            </el-table-column>
            <el-table-column prop="completetime" label="完成时间">
            </el-table-column>
            <el-table-column prop="processsuggestion" label="处理意见">
            </el-table-column>
          </el-table>
        </div>
<!--      </el-collapse-item>-->
<!--    </el-collapse>-->
  </el-card>
</template>

<script>
import { apiGetProcessLog } from "../api/CommonApi";

export default {
  name: "ProcessLog",
  props: {
    woId: String,
  },
  data() {
    return {
      tableData: [],
      tableLoading: false,
      activeNames: ["1"],
    };
  },
  mounted() {
    this.getLogData();
  },
  methods: {
    getLogData() {
      this.tableLoading = true;
      let param = {
        woId: this.woId,
      };
      apiGetProcessLog(param)
        .then(res => {
          if (res.status == 0) {
            let row = res?.data ?? [];
            if (row.length > 0) {
              row.forEach(el => {
                el.id = this.randomNumber();
                if (el.data.length > 0) {
                  el.data.forEach(tl => {
                    tl.id = this.randomNumber();
                  });
                }
              });
            }

            this.tableData = row;
            this.tableLoading = false;
          } else {
            this.tableLoading = false;
          }
        })
        .catch(err => {
          console.log(err);
          this.tableLoading = false;
        });
    },
    randomNumber() {
      let num = "";
      for (var i = 0; i < 4; i++) {
        num += Math.floor(Math.random() * 10);
      }
      return num;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../assets/common.scss";
.custom-theme-default .el-collapse {
  border-top: none;
}

::v-deep .el-collapse-item__wrap {
  border-bottom: 0;
}
</style>
