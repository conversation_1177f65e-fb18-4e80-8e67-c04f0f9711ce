<template>
  <div class="afterSingle">
    <el-form ref="afterSingleForm" :model="afterSingleForm" label-width="120px">
      <el-form-item
        label="描述:"
        prop="content"
      >
        <el-input
          show-word-limit
          maxlength="255"
          @keyup.native="descTip(255,'content','showTip')"
          type="textarea"
          :rows="2"
          v-model="afterSingleForm.content"
          style="width: 420px"
        >
        </el-input>
        <div class="el-form-item__error"  v-if="showTip">已超过填写字数上限</div>
      </el-form-item>
      <el-form-item label="上传故障报告:" required>
        <div style="width: 400px">
          <el-tag
            class="fileName_style"
            readonly
            v-for="(item, index) in importForm.attachmentFileList"
            :key="index"
            @close="close(item)"
            :title="item.name"
          ><div class="text-truncate">{{ item.name }}</div></el-tag
          >
          <el-button size="mini" type="primary" @click="attachmentBrowse"
          >+上传附件</el-button
          >
        </div>
      </el-form-item>
    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: right">
      <el-button
        type="primary"
        @click="handleSubmit('afterSingleForm')"
        v-loading.fullscreen.lock="afterSingleFullscreenLoading"
      >提 交</el-button
      >
      <el-button @click="onResetAfterSingleForm">重 置</el-button>
    </div>
    <el-dialog
      width="420px"
      title="附件选择"
      :visible.sync="attachmentDialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <file-upload
        @change="changeFileData"
        @cancel="closeAttachmentDialog"
      ></file-upload>
    </el-dialog>
  </div>
</template>
<script>
  import { apiActionPublic, apiUpInit, apiFileUpload, apiGroupCode } from "../api/CommonApi";

  // import FileUpload from "../../../workOrder/components/FileUpload";
  import FileUpload from "./FileUpload";

  export default {
    name: "UploadFaultFileHanlde",
    props: {
      common: Object,
      actionName:String,
      // reportId:String,
    },
    components: { FileUpload },
    data() {
      return {
        showTip:false,
        showTime:5000,
        afterSingleForm: {
          content: null
        },
        afterSingleFullscreenLoading: false,
        attachmentDialogVisible: false,
        importForm: {
          //附件
          attachmentFileList: [],
        },
      };
    },
    mounted() {
    },
    async created() {
      // this.upInit();
    },
    watch: {},
    methods: {
      descTip(count,name,showName){
        if (this.afterSingleForm[name].length>=count){
          this[showName] = true;
          setTimeout(() => {
            this[showName] = false;
          }, this.showTime);
        }
        else{
          this[showName] = false;
        }
      },

      //上传故障报告初始化
      // upInit() {
      //   let param = {
      //     woId: this.common.woId,
      //     reportId:this.reportId,
      //     processInstId:this.common.processInstId,
      //     // userName: this.userInfo.userName,
      //   };
      //   apiUpInit(param)
      //     .then(res => {
      //       if (res.status == "0") {
      //
      //       }
      //     })
      //     .catch(error => {
      //       console.log(error);
      //     });
      // },

      handleSubmit(formName) {
        this.$refs[formName].validate(valid => {
          if (valid) {
            let self = this;
            // 如果有附件，先上传附件
            let formData = new FormData();
            if (self.importForm.attachmentFileList.length > 0) {

              this.afterSingleFullscreenLoading = true;
              for (let item of self.importForm.attachmentFileList) {
                formData.append("files", item.raw);
              }
              let param = {
                attatchGroupKey:'',
                woId: this.common.woId,
                processInstId: this.common.processInstId,
                // processDefId: this.common.processDefId,
                workItemId: this.common.workItemId,
                // processNode: this.common.processNode,
                actionName:this.actionName,
                detail: this.afterSingleForm.content,
              };
              apiGroupCode()
                .then(res => {
                  if (res.status == 0) {
                    let processId = res.data?.linkId;

                    let uploadUrl = `/commonprovince/attach/upload?groupKey=${this.common.woId}&processId=${processId}&processNode=${this.common.processNode}&sheetCreateTime=${this.common.sheetCreateTime}`;
                    apiFileUpload(uploadUrl, formData)
                      .then(res => {
                        if (res.code == 200) {
                          param.attatchGroupKey = processId;
                          this.submit(param);
                          this.importForm.attachmentFileList = [];
                        } else {
                          this.$message.error(res.msg);
                          this.afterSingleFullscreenLoading = false;
                          return false;
                        }
                      })
                      .catch(error => {
                        console.log(error);
                        this.afterSingleFullscreenLoading = false;
                      });
                  }
                })
                .catch(error => {
                  console.log(error);
                  this.afterSingleFullscreenLoading = false;
                });
            } else {
              this.$message.error("请上传故障报告");
              return false;
            }


          } else {
            return false;
          }
        });
      },
      submit(param){
        apiActionPublic(param)
          .then(res => {
            if (res.status == "0") {
              this.$message.success(res.msg);
              this.$emit("closeUpFileDialog",1);
            } else {
              this.$message.error(res.msg);
            }
            this.afterSingleFullscreenLoading = false;
          })
          .catch(error => {
            console.log(error);
            this.afterSingleFullscreenLoading = false;
            this.$message.error(error.msg);
          });
      },
      //附件选择
      attachmentBrowse() {
        this.attachmentDialogVisible = true;
      },
      changeFileData(data) {
        this.importForm.attachmentFileList = data.attachmentFileList;
        this.attachmentDialogVisible = false;
      },
      closeAttachmentDialog() {
        this.attachmentDialogVisible = false;
      },
      onResetAfterSingleForm() {
        this.afterSingleForm = {
          content: null,
          systemName: null,
          mainSending: null,
          lordSentUserName: null,
          lordSentOrgName: null,
          lordSentUserId: null,
          lordSentOrgId: null,
          cc: null,
          transferStatusRadio: "0",
          ccUserName: null,
          ccOrgName: null,
          ccUserId: null,
          ccOrgId: null,
        };
        this.importForm.attachmentFileList = [];
      },
      onOpenPeopleDialog(diaSaveName) {
        this.diaPeople.title = this.diaPeople.saveTitleMap[diaSaveName];
        this.diaPeople.saveName = diaSaveName;
        this.diaPeople.showOrgsTree =
          this.diaPeople.showOrgsTreeMap[diaSaveName] ?? true;
        this.diaPeople.visible = true;
      },
      onSavePeople(val) {
        this[this.diaPeople.saveName](val);
      },
      //主送确定
      lordSentDetermine({ usersChecked, orgsChecked, selectionUser }) {
        if (selectionUser && selectionUser.length > 0) {
          let usersCheckedName = selectionUser.map(item => {
            return item.trueName;
          });
          this.afterSingleForm.lordSentUserName = usersCheckedName.join(",");
          let usersCheckedId = selectionUser.map(item => {
            return item.userName;
          });
          this.afterSingleForm.lordSentUserId = usersCheckedId.join(",");
          this.afterSingleForm.agentManDetail = selectionUser
            .map(item => {
              return (
                item.trueName +
                "-" +
                item.orgEntity.orgName +
                "-" +
                item.mobilePhone
              );
            })
            .join(",");
        } else {
          let usersCheckedName = usersChecked.map(item => {
            return item.name;
          });
          this.afterSingleForm.lordSentUserName = usersCheckedName.join(",");
          let usersCheckedId = usersChecked.map(item => {
            return item.id;
          });
          this.afterSingleForm.lordSentUserId = usersCheckedId.join(",");
          this.afterSingleForm.agentManDetail = usersChecked
            .map(item => {
              return item.name + "-" + item.orgName + "-" + item.mobilePhone;
            })
            .join(",");
        }

        let orgsCheckedName = orgsChecked.map(item => {
          return item.name;
        });
        this.afterSingleForm.lordSentOrgName = orgsCheckedName.join(",");
        this.afterSingleForm.mainSending = this.stitchingAlgorithm(
          this.afterSingleForm.lordSentOrgName,
          this.afterSingleForm.agentManDetail
        );
        let orgsCheckedId = orgsChecked.map(item => {
          return item.id;
        });
        this.afterSingleForm.lordSentOrgId = orgsCheckedId.join(",");
      },
      //抄送
      ccDetermine({ usersChecked, orgsChecked, selectionUser }) {
        if (selectionUser && selectionUser.length > 0) {
          let usersCheckedName = selectionUser.map(item => {
            return item.trueName;
          });
          this.afterSingleForm.ccUserName = usersCheckedName.join(",");
          let usersCheckedId = selectionUser.map(item => {
            return item.userName;
          });
          this.afterSingleForm.ccUserId = usersCheckedId.join(",");
          this.afterSingleForm.copyManDetail = selectionUser
            .map(item => {
              return (
                item.trueName +
                "-" +
                item.orgEntity.orgName +
                "-" +
                item.mobilePhone
              );
            })
            .join(",");
        } else {
          let usersCheckedName = usersChecked.map(item => {
            return item.name;
          });
          this.afterSingleForm.ccUserName = usersCheckedName.join(",");
          let usersCheckedId = usersChecked.map(item => {
            return item.id;
          });
          this.afterSingleForm.ccUserId = usersCheckedId.join(",");
          this.afterSingleForm.copyManDetail = usersChecked
            .map(item => {
              return item.name + "-" + item.orgName + "-" + item.mobilePhone;
            })
            .join(",");
        }

        let orgsCheckedName = orgsChecked.map(item => {
          return item.name;
        });
        this.afterSingleForm.ccOrgName = orgsCheckedName.join(",");
        this.afterSingleForm.cc = this.stitchingAlgorithm(
          this.afterSingleForm.ccOrgName,
          this.afterSingleForm.copyManDetail
        );
        let orgsCheckedId = orgsChecked.map(item => {
          return item.id;
        });
        this.afterSingleForm.ccOrgId = orgsCheckedId.join(",");
      },
      close(tag) {
        this.importForm.attachmentFileList.splice(
          this.importForm.attachmentFileList.indexOf(tag),
          1
        );
      },
      stitchingAlgorithm(orgName, userName) {
        if (
          null != orgName &&
          orgName.length !== 0 &&
          null != userName &&
          userName.length !== 0
        ) {
          return orgName + "," + userName;
        } else {
          if (null != orgName && orgName.length !== 0) {
            return orgName;
          } else if (null != userName && userName.length !== 0) {
            return userName;
          } else {
            return "";
          }
        }
      },

      stitchingAlgorithmArr(orgName, userName) {
        if (orgName.length !== 0 && userName.length !== 0) {
          return orgName.join(",") + "," + userName.join(",");
        } else {
          if (orgName.length !== 0) {
            return orgName.join(",");
          } else if (userName.length !== 0) {
            return userName.join(",");
          } else {
            return "";
          }
        }
      },
    },
  };
</script>
<style lang="scss" scoped>
  ::v-deep .el-tree {
    padding-top: 15px;
    padding-left: 10px;
    // 不可全选样式
    .el-tree-node {
      .is-leaf + .el-checkbox .el-checkbox__inner {
        display: inline-block;
      }
      .el-checkbox .el-checkbox__inner {
        display: none;
      }
    }
  }
  .afterSingle {
    .fileName_style {
      margin-right: 3px;
      vertical-align: middle;
      div {
        display: inline-block;
        max-width: 360px;
        vertical-align: top;
      }
    }
  }
</style>
