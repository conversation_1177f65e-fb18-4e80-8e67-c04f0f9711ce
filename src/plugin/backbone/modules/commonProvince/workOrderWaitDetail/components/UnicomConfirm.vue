<template>
  <div class="back-single-internation">
    <el-form
      ref="UnicomConfirmForm"
      :inline="false"
      class="demo-form-inline"
      :model="backSingleForm"
      label-width="130px"
    >
      <el-card
        shadow="never"
        :body-style="{ padding: '20px 8px' }"
        class="cus-card"
      >
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="24">
            <el-form-item label-width="180px" label="系统派单方生成的单号:" prop="sheetNo" >
              {{ backSingleForm.sheetNo }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="是否退单:"
              prop="isChargeBack"
              :rules="{
                required: true,
                message: '请选择',
              }"
            >
              <el-radio-group
                v-model="backSingleForm.isChargeBack"
                style="width: 100%"
              >
                <el-radio label="否">否</el-radio>
                <el-radio label="是">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="故障是否恢复:"
              prop="isRecover"
            >
              <el-radio-group
                v-model="backSingleForm.isRecover"
                style="width: 100%"
              >
                <el-radio label="否">否</el-radio>
                <el-radio label="是">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障恢复时间:" prop="recoverTime">
              <el-date-picker
                v-model="backSingleForm.recoverTime"
                type="datetime"
                placeholder="请选择时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="申告处理是否超时:"
              prop="isTimeOut"
            >
              <el-radio-group
                v-model="backSingleForm.isTimeOut"
                style="width: 100%"
              >
                <el-radio label="否">否</el-radio>
                <el-radio label="是">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="故障类别:"
              prop="proCauseKind"
            >
              <el-select
                v-model="backSingleForm.faultType"
                placeholder="请选择内容"
                style="width: 100%"
              >
                <el-option
                  v-for="(item, i) in faultTypeArr"
                  :key="i"
                  :label="item.dictName"
                  :value="item.dictName"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="结单时间:" prop="statemOfTime">
              <el-date-picker
                v-model="backSingleForm.statemOfTime"
                type="datetime"
                placeholder="请选择时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="24">
            <el-form-item
              label="故障原因:"
              prop="faultCause"
            >
              <el-input
                maxlength="500"
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="backSingleForm.faultCause"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="24">
            <el-form-item
              label="故障总结:"
              prop="faultSummary"
            >
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="backSingleForm.faultSummary"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: center">
      <el-button
        type="primary"
        @click="handleSubmit('UnicomConfirmForm')"
        v-loading.fullscreen.lock="backSingleFullscreenLoading"
      >提 交</el-button
      >
      <!-- <el-button type="primary" @click="nextStepEvaluation()">下一步</el-button> -->
      <el-button @click="onReset">重 置</el-button>
    </div>

  </div>
</template>
<script>
  import moment from "moment";
  import { mapGetters } from "vuex";
  import DictSelect from "../../../workOrder/components/DictSelect.vue";
  import { apiActionPublic } from "../api/CommonApi";
  import { apiDict, apiGetOrgInfo } from "../../../workOrder/api/CommonApi";
  export default {
    name: "UnicomConfirm",
    props: {
      common: Object,
      orderInfo:Object,
    },
    components: { DictSelect },
    computed: {
      ...mapGetters(["userInfo"]),
    },
    data() {

      return {
        backSingleForm: {
          woId: null,
          workItemId: null,
          processInstId: null,
          actionName:'联通确认',
          sheetNo: null,
          isChargeBack: null,
          isRecover: null,
          recoverTime: null,
          isTimeOut: null,
          faultType: null,
          statemOfTime: null,
          faultCause: null,
          faultSummary: null,
        },
        operTypeArr:[
          {'dictName':'接单'},
          {'dictName':'反馈'},
          {'dictName':'回单'}
        ],
        faultTypeArr:[],
        backSingleFullscreenLoading: false,

        // colors: ["#99A9BF", "#F7BA2A", "#FF9900"], // 等同于 { 2: '#99A9BF', 4: { value: '#F7BA2A', excluded: true }, 5: '#FF9900' }
        //当前人的部门
        userData: null,
        // viewsOnContentShow: false,
      };
    },
    async created() {
      this.getDictData("commonpro_share_faultType", "faultTypeArr");//故障类别

    },
    mounted() {

      this.backSingleForm.workItemId = this.common.workItemId;
      this.backSingleForm.woId = this.common.woId;
      this.backSingleForm.processInstId = this.common.processInstId;
      this.userData = JSON.parse(this.userInfo.attr2);
      this.backSingleForm.sheetNo = this.orderInfo.shareSheetNo;
    },
    methods: {

      // 下拉查询
      getDictData(dictId, selectName) {
        this[selectName] = [];
        let param = {
          dictType: dictId,
        };
        apiDict(param)
          .then(res => {
            if (res.code == 200) {
              this[selectName] = res.data;
            }
          })
          .catch(error => {
            console.log(error);
            return false;
          });
      },

      handleSubmit(formName) {
        this.$refs[formName].validate(valid => {
          if (valid) {
            let self = this;
            this.backSingleFullscreenLoading = true;
            apiActionPublic(self.backSingleForm)
              .then(res => {
                if (res.status == "0") {
                  this.$message.success(res.msg);
                  //   this.onReset();
                  this.$emit("closeLTConfirmDialog", res.data);
                } else {
                  this.$message.error(res.msg);
                }
                this.backSingleFullscreenLoading = false;
              })
              .catch(error => {
                console.log(error);
                this.$message.error(error.msg);
                this.backSingleFullscreenLoading = false;
              });
          } else {
            return false;
          }
        });
      },

      onReset() {
        this.backSingleForm = {
          woId: this.common.woId,
          workItemId: this.common.workItemId,
          processInstId: this.common.processInstId,
          actionName:'联通确认',
          sheetNo: this.orderInfo.shareSheetNo,
          isChargeBack: null,
          isRecover: null,
          recoverTime: null,
          isTimeOut: null,
          faultType: null,
          statemOfTime: null,
          faultCause: null,
          faultSummary: null,
        };
      },

    },
  };
</script>
<style lang="scss" scoped>
  .back-single-internation {
    max-height: calc(90vh - 150px);
    min-height: 400px;
    margin: 0 4px;
    padding-left: 4px;
    padding-right: 8px;
    overflow-y: auto;
    .cus-card ::v-deep .el-card__header {
      padding: 11px 24px 10px;
      font-weight: 400;
      @include themify {
        background-color: themed("$--background-color-base");
      }
    }
    .fileName_style {
      margin-right: 3px;
      vertical-align: middle;
      div {
        display: inline-block;
        max-width: 120px;
        vertical-align: top;
      }
    }
  }
</style>
