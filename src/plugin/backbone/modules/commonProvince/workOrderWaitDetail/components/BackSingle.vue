<template>
  <div class="back-single">
    <el-form
      ref="backSingleForm"
      :inline="false"
      class="demo-form-inline"
      :model="backSingleForm"
      label-width="130px"
      :rules="backSingleForm.professionalType!='铁塔'?backSingleFormRule:backSingleFormRuleTT"
    >
      <template v-if="backSingleForm.professionalType == '铁塔'">
        <el-card
          shadow="never"
          header="故障定性信息"
          :body-style="{ padding: '20px 8px' }"
          class="cus-card"
        >
          <el-row type="flex" style="flex-wrap: wrap" :gutter="20" key="tieta">
            <el-col :span="8">
              <el-form-item label="故障所属专业:"
                            prop="professionalTypeId"
                            :rules="{
                required: true,
                message: '请选择',
              }"
              >
                <el-select
                  v-model="backSingleForm.professionalTypeId"
                  placeholder="请选择内容"
                  style="width: 100%"
                  @change="professionalTypeChange"
                >
                  <el-option
                    v-for="(item, i) in processionTypeArr"
                    :key="i"
                    :label="item.dictName"
                    :value="item.dictCode"
                  >
                    <span style="float: left">{{item.dictName}}</span>
                    <span v-if="tjProfessionalTypeList.indexOf(item.dictName)>-1" style="float: right;display: flex;align-items: center;height: 100%">
                      <img src="../assets/tj.png" style="width: 18px;height: 18px"/>
                    </span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="回单人:"
                prop="recoveryPerson"
                :rules="{
                required: true,
                message: '请输入',
              }"
              >
                <el-input
                  v-model="backSingleForm.recoveryPerson"
                  style="width: 100%"
                  placeholder="请输入"
                  maxlength="255"
                  @keyup.native="descTip(255,'recoveryPerson','showhdr')"
                ></el-input>
                <div class="el-form-item__error"  v-if="showhdr">已超过填写字数上限</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="回单人联系电话:"
                prop="recoveryPhone"
                :rules="{
                required: true,
                message: '请输入',
              }"
              >
                <el-input
                  v-model="backSingleForm.recoveryPhone"
                  style="width: 100%"
                  placeholder="请输入"
                  maxlength="255"
                  @keyup.native="descTip(255,'recoveryPhone','showhdrdh')"
                ></el-input>
                <div class="el-form-item__error"  v-if="showhdrdh">已超过填写字数上限</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="实时直流电压值:"
                prop="siteOfflineReason"
              >
                <el-input
                  v-model="backSingleForm.siteOfflineReason"
                  style="width: 100%"
                  placeholder="请输入"
                  maxlength="255"
                  @keyup.native="descTip(255,'siteOfflineReason','showsszldyz')"
                ></el-input>
                <div class="el-form-item__error"  v-if="showsszldyz">已超过填写字数上限</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="铁塔站址资源编码:"
                prop="faultReason"
                :rules="{
                required: true,
                message: '请输入',
              }"
              >
                <el-input
                  v-model="backSingleForm.faultReason"
                  style="width: 100%"
                  placeholder="请输入"
                  maxlength="255"
                  @keyup.native="descTip(255,'faultReason','showttzzzybm')"
                ></el-input>
                <div class="el-form-item__error"  v-if="showttzzzybm">已超过填写字数上限</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="站址运维ID:"
                prop="vendor"
                :rules="{
                required: true,
                message: '请输入',
              }"
              >
                <el-input
                  v-model="backSingleForm.vendor"
                  style="width: 100%"
                  placeholder="请输入"
                  maxlength="255"
                  @keyup.native="descTip(255,'vendor','showzzid')"
                ></el-input>
                <div class="el-form-item__error"  v-if="showzzid">已超过填写字数上限</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="站址名称:"
                prop="eqpType"
                :rules="{
                required: true,
                message: '请输入',
              }"
              >
                <el-input
                  v-model="backSingleForm.eqpType"
                  style="width: 100%"
                  placeholder="请输入"
                  maxlength="255"
                  @keyup.native="descTip(255,'eqpType','showzzmc')"
                ></el-input>
                <div class="el-form-item__error"  v-if="showzzmc">已超过填写字数上限</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="运营商是否购买发电服务:"
                prop="hardwareFlag"
                :rules="{
                required: true,
                message: '请输入',
              }"
              >
                <el-radio-group
                  v-model="backSingleForm.hardwareFlag"
                  style="width: 100%"
                >
                  <el-radio label="否">否</el-radio>
                  <el-radio label="是">是</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="是否是发电工单:"
                prop="maintenanceSubject"
                :rules="{
                required: true,
                message: '请输入',
              }"
              >
                <el-radio-group
                  v-model="backSingleForm.maintenanceSubject"
                  style="width: 100%"
                >
                  <el-radio label="否">否</el-radio>
                  <el-radio label="是">是</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="发电开始时间:"
                prop="alarmCreateTime"
              >
                <el-date-picker
                  v-model="backSingleForm.alarmCreateTime"
                  type="datetime"
                  placeholder="请选择时间"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  clearable
                  style="width: 100%"
                  :picker-options="pickerOptions"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="发电结束时间:"
                prop="faultOverTime"
              >
                <el-date-picker
                  v-model="backSingleForm.faultOverTime"
                  type="datetime"
                  placeholder="请选择时间"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  clearable
                  style="width: 100%"
                  :picker-options="pickerOptions"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="接单时间:"
                prop="faultRecoveryTime"
                :rules="{
                required: true,
                message: '请选择',
              }"
              >
                <el-date-picker
                  v-model="backSingleForm.faultRecoveryTime"
                  type="datetime"
                  placeholder="请选择时间"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  clearable
                  disabled
                  style="width: 100%"
                  :picker-options="pickerOptions"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="所属地市:"
                prop="mobileFaultDutyId"
                :rules="{
                required: true,
                message: '请输入',
              }"
              >
                <el-input
                  v-model="backSingleForm.mobileFaultDutyId"
                  style="width: 100%"
                  placeholder="请输入"
                  maxlength="255"
                  @keyup.native="descTip(255,'mobileFaultDutyId','showssds')"
                ></el-input>
                <div class="el-form-item__error"  v-if="showssds">已超过填写字数上限</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="所属区县:"
                prop="boardType"
                :rules="{
                required: true,
                message: '请输入',
              }"
              >
                <el-input
                  v-model="backSingleForm.boardType"
                  style="width: 100%"
                  placeholder="请输入"
                  maxlength="255"
                  @keyup.native="descTip(255,'boardType','showssqx')"
                ></el-input>
                <div class="el-form-item__error"  v-if="showssqx">已超过填写字数上限</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="运营商共享情况:"
                prop="repairQuality"
                :rules="{
                required: true,
                message: '请输入',
              }"
              >
                <el-select
                  v-model="backSingleForm.repairQuality"
                  placeholder="请选择内容"
                  multiple
                  collapse-tags
                  style="width: 100%"
                >
                  <el-option
                    v-for="(item, i) in carrierSharingArr"
                    :key="i"
                    :label="item.dictName"
                    :value="item.dictName"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="站址服务等级:"
                prop="fdbkQuality"
                :rules="{
                required: true,
                message: '请输入',
              }"
              >
                <el-select
                  v-model="backSingleForm.fdbkQuality"
                  placeholder="请选择内容"
                  style="width: 100%"
                >
                  <el-option
                    v-for="(item, i) in siteServiceLevelArr"
                    :key="i"
                    :label="item.dictName"
                    :value="item.dictName"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="是否为免责站址:"
                prop="satisfaction"
                :rules="{
                required: true,
                message: '请输入',
              }"
              >
                <el-radio-group
                  v-model="backSingleForm.satisfaction"
                  style="width: 100%"
                >
                  <el-radio label="否">否</el-radio>
                  <el-radio label="是">是</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="申告工单是否铁塔原因:"
                prop="evaluationOpinion"
                :rules="{
                required: true,
                message: '请输入',
              }"
              >
                <el-select
                  v-model="backSingleForm.evaluationOpinion"
                  placeholder="请选择内容"
                  style="width: 100%"
                >
                  <el-option
                    v-for="(item, i) in ifTowerReasonArr"
                    :key="i"
                    :label="item.dictName"
                    :value="item.dictName"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="申告工单故障分类:"
                prop="faultSource"
                :rules="{
                required: true,
                message: '请输入',
              }"
              >
                <el-select
                  v-model="backSingleForm.faultSource"
                  placeholder="请选择内容"
                  style="width: 100%"
                >
                  <el-option
                    v-for="(item, i) in declarationFaultClassificationArr"
                    :key="i"
                    :label="item.dictName"
                    :value="item.dictName"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="是否申请减免:"
                prop="diskName"
                :rules="{
                required: true,
                message: '请输入',
              }"
              >
                <el-select
                  v-model="backSingleForm.diskName"
                  placeholder="请选择内容"
                  style="width: 100%"
                >
                  <el-option
                    v-for="(item, i) in ifApplyForReliefArr"
                    :key="i"
                    :label="item.dictName"
                    :value="item.dictName"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="backSingleForm.diskName!='不需减免'">
              <el-form-item
                label="申请减免原因:"
                prop="interSystem"
                :rules="{
                required: backSingleForm.diskName=='不需减免'?false:true,
                message: '请输入',
              }"
              >
                <el-input
                  v-model="backSingleForm.interSystem"
                  style="width: 100%"
                  placeholder="请输入"
                  maxlength="255"
                  @keyup.native="descTip(255,'interSystem','showsqjmyy')"
                ></el-input>
                <div class="el-form-item__error"  v-if="showsqjmyy">已超过填写字数上限</div>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="backSingleForm.diskName!='不需减免'">
              <el-form-item
                label="申请减免分钟数:"
                prop="effectSystem"
                :rules="{
                required: backSingleForm.diskName=='不需减免'?false:true,
                message: '请输入',
              }"
              >
                <el-input
                  v-model="backSingleForm.effectSystem"
                  style="width: 100%"
                  placeholder="请输入"
                  maxlength="255"
                  @keyup.native="descTip(255,'effectSystem','showsqjmfzs')"
                ></el-input>
                <div class="el-form-item__error"  v-if="showsqjmfzs">已超过填写字数上限</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="是否上站:"
                prop="interCircuit"
                :rules="{
                required: true,
                message: '请输入',
              }"
              >
                <el-radio-group
                  v-model="backSingleForm.interCircuit"
                  style="width: 100%"
                >
                  <el-radio label="否">否</el-radio>
                  <el-radio label="是">是</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="同行处理人:" prop="buildZs">
                <el-input readonly v-model="backSingleForm.buildZs" placeholder="添加人员">
                  <template v-for="(tag, index) in organizeForm.builderZsList">
                    <el-tag
                      slot="prefix"
                      style="margin-top: 5px"
                      :key="index"
                      :closable="true"
                      @close="handleClose('builderZs', tag)"
                      v-show="index < 1"
                      v-if="tag.bz == 'user'"
                    >
                      {{ tag.name }}
                    </el-tag>
                    <el-tag
                      slot="prefix"
                      style="margin-top: 5px"
                      :key="index"
                      :closable="true"
                      @close="handleClose('builderZs', tag)"
                      v-show="index < 1"
                      v-if="tag.bz == 'org'"
                    >
                      {{ tag.orgName }}
                    </el-tag>
                  </template>
                  <el-popover
                    slot="prefix"
                    v-if="organizeForm.builderZsList.length >= 2"
                    width="500"
                    trigger="click"
                  >
                    <el-input
                      v-model="organizeForm.builderZsName"
                      placeholder="请输入人员姓名/组织名称"
                    >
                      <el-button
                        type="info"
                        slot="append"
                        icon="el-icon-search"
                        @click="search('builderZs')"
                      >
                      </el-button>
                      <el-button
                        type="info"
                        slot="append"
                        icon="el-icon-close"
                        @click="clear('builderZs')"
                      >
                      </el-button>
                    </el-input>
                    <el-table
                      ref="multipleTable"
                      tooltip-effect="dark"
                      @selection-change="handleSelectionChange"
                      :data="organizeForm.builderZsListCopy"
                      max-height="240"
                    >
                      <el-table-column width="30" type="selection">
                      </el-table-column>
                      <el-table-column
                        min-width="70"
                        property="name"
                        label="姓名"
                      >
                      </el-table-column>
                      <el-table-column
                        min-width="180"
                        property="orgName"
                        label="组织"
                      >
                      </el-table-column>
                      <el-table-column
                        min-width="120"
                        property="mobilePhone"
                        label="电话"
                      >
                      </el-table-column>
                      <el-table-column fixed="right" label="操作" width="50">
                        <template slot-scope="scope">
                          <el-button
                            type="text"
                            size="small"
                            @click.native.prevent="
                            handleClose('builderZs', scope.row)
                          "
                          >
                            移除
                          </el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                    <el-button
                      size="small"
                      type="text"
                      @click="toggleSelection('builderZs')"
                    >批量移除
                    </el-button>
                    <el-tag slot="reference" style="margin-top: 3px">
                      +{{ organizeForm.builderZsList.length - 1 }}
                    </el-tag>
                  </el-popover>
                  <el-button
                    type="info"
                    slot="append"
                    icon="el-icon-user"
                    @click="onOpenPeopleDialog('lordSentDetermine')"
                  ></el-button>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item
                label="处理结果描述:"
                prop="faultRange"
                :rules="{
                required: true,
                message: '请输入',
              }"
              >
                <el-input
                  type="textarea"
                  :rows="2"
                  v-model="backSingleForm.faultRange"
                  style="width: 100%"
                  placeholder="请输入"
                  show-word-limit
                  maxlength="255"
                  @keyup.native="descTip(255,'faultRange','showcljgms')"
                ></el-input>
                <div class="el-form-item__error"  v-if="showcljgms">已超过填写字数上限</div>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="附件:">
                <el-tag
                  class="fileName_style"
                  readonly
                  v-for="(item, index) in importForm.relatedFilesFileList"
                  :key="index"
                  @close="close(item)"
                  :title="item.name"
                ><div class="text-truncate">{{ item.name }}</div></el-tag
                >
                <el-button size="mini" type="primary" @click="relatedFilesBrowse"
                >+上传附件</el-button
                >
              </el-form-item>
            </el-col>

          </el-row>
        </el-card>
      </template>
      <template v-else>
        <el-card
          shadow="never"
          header="故障定性信息"
          :body-style="{ padding: '20px 8px' }"
          class="cus-card"
        >
          <el-row type="flex" style="flex-wrap: wrap" :gutter="20" key="00">
            <el-col :span="8">
              <el-form-item label="故障所属专业:"
                prop="professionalTypeId"
                :rules="{
                required: true,
                message: '请选择',
              }"
              >
                <el-select
                  v-model="backSingleForm.professionalTypeId"
                  placeholder="请选择内容"
                  style="width: 100%"
                  @change="professionalTypeChange"
                >
                  <el-option
                    v-for="(item, i) in processionTypeArr"
                    :key="i"
                    :label="item.dictName"
                    :value="item.dictCode"
                  >
                    <span style="float: left">{{item.dictName}}</span>
                    <span v-if="tjProfessionalTypeList.indexOf(item.dictName)>-1" style="float: right;display: flex;align-items: center;height: 100%">
                      <img src="../assets/tj.png" style="width: 18px;height: 18px"/>
                    </span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="故障发生时间:" prop="alarmCreateTime" required>
                {{ backSingleForm.alarmCreateTime }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="故障通知时间:" prop="faultNoticeTime" required>
                {{ backSingleForm.faultNoticeTime }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="业务恢复时间:"
                prop="faultRecoveryTime"
              >
                <el-date-picker
                  v-model="backSingleForm.faultRecoveryTime"
                  type="datetime"
                  placeholder="请选择时间"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  clearable
                  style="width: 100%"
                  @change="computerFaultGenerationAter"
                  :picker-options="pickerOptions"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="业务恢复历时:">
                {{ second2Time(backSingleForm.recoveryDuration) }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="故障结束时间:"
                prop="faultOverTime"
              >
                <el-date-picker
                  v-model="backSingleForm.faultOverTime"
                  type="datetime"
                  placeholder="请选择时间"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  clearable
                  style="width: 100%"
                  @change="computerFaultTreatmentTime"
                  :picker-options="pickerOptions"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="故障处理历时:" required>
                {{ second2Time(this.backSingleForm.faultDuration) }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="故障发生地区:"
                prop="faultRegion"
                :rules="{
                required: true,
                message: '请选择内容',
              }"
              >
                <el-input
                  v-model="backSingleForm.faultRegion"
                  placeholder="请选择内容"
                  clearable
                >
                  <template #append>
                    <el-button
                      type="primary"
                      icon="el-icon-check"
                      @click="onOpenFaultRegionTree()"
                    ></el-button>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="故障处理部门:" prop="dept" required>
                {{ backSingleForm.dept }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="处理人:" prop="recoveryPerson" required>
                {{ backSingleForm.recoveryPerson }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="挂起历时:" required>
                {{ second2Time(this.backSingleForm.suspendDuration) }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="故障处理净历时:" required>
                {{ second2Time(this.backSingleForm.faultCleanDuration) }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
            <el-col :span="8">
              <el-form-item label="是否影响业务:"
                prop="isEffectBusiness"
                :rules="[
                {
                  required:  true ,
                  message: '请选择是否影响业务',
                },
              ]"
              >
                <el-radio-group
                  v-model="backSingleForm.isEffectBusiness"
                  style="width: 100%"
                >
                  <el-radio label="否">否</el-radio>
                  <el-radio label="是">是</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="16" v-if="backSingleForm.isEffectBusiness == '是'">
              <el-form-item label="影响范围:"
                key="yxfw"
                prop="effectRange"
                :rules="[
                {
                  required:
                    backSingleForm.isEffectBusiness == '是' ? true : false,
                  message: '请填写影响范围',
                  trigger: 'blur',
                },
              ]"
              >
                <el-input
                  type="textarea"
                  :rows="2"
                  placeholder="请输入"
                  v-model="backSingleForm.effectRange"
                  style="width: 100%"
                  show-word-limit
                  maxlength="255"
                  @keyup.native="descTip(255,'effectRange','showyxfw')"
                >
                </el-input>
                <div class="el-form-item__error"  v-if="showyxfw">已超过填写字数上限</div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
            <el-col :span="8" v-if="backSingleForm.professionalType == '无线网'">
              <el-form-item label="是否基站退服:"
                prop="isSiteOffline"
                :rules="[
                {
                  required: true ,
                  message: '请选择是否基站退服',
                },
              ]"
              >
                <el-radio-group
                  v-model="backSingleForm.isSiteOffline"
                  style="width: 100%"
                >
                  <el-radio label="否">否</el-radio>
                  <el-radio label="是">是</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="16" v-if="backSingleForm.isSiteOffline == '是'">
              <el-form-item label="退服原因:"
                            key="tfyy"
                prop="siteOfflineReason"
                :rules="[
                {
                  required:
                    backSingleForm.isSiteOffline == '是' ? true : false,
                  message: '请选择退服原因',
                  trigger: 'blur',
                },
              ]"
              >
                <el-select
                  v-model="backSingleForm.siteOfflineReason"
                  placeholder="请选择内容"
                  style="width: 100%"
                >
                  <el-option
                    v-for="(item, i) in siteOfflineReasonArr"
                    :key="i"
                    :label="item.dictName"
                    :value="item.dictName"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="同行处理人:" prop="buildZs">
                <el-input readonly v-model="backSingleForm.buildZs" placeholder="添加人员">
                  <template v-for="(tag, index) in organizeForm.builderZsList">
                    <el-tag
                      slot="prefix"
                      style="margin-top: 5px"
                      :key="index"
                      :closable="true"
                      @close="handleClose('builderZs', tag)"
                      v-show="index < 1"
                      v-if="tag.bz == 'user'"
                    >
                      {{ tag.name }}
                    </el-tag>
                    <el-tag
                      slot="prefix"
                      style="margin-top: 5px"
                      :key="index"
                      :closable="true"
                      @close="handleClose('builderZs', tag)"
                      v-show="index < 1"
                      v-if="tag.bz == 'org'"
                    >
                      {{ tag.orgName }}
                    </el-tag>
                  </template>
                  <el-popover
                    slot="prefix"
                    v-if="organizeForm.builderZsList.length >= 2"
                    width="500"
                    trigger="click"
                  >
                    <el-input
                      v-model="organizeForm.builderZsName"
                      placeholder="请输入人员姓名/组织名称"
                    >
                      <el-button
                        type="info"
                        slot="append"
                        icon="el-icon-search"
                        @click="search('builderZs')"
                      >
                      </el-button>
                      <el-button
                        type="info"
                        slot="append"
                        icon="el-icon-close"
                        @click="clear('builderZs')"
                      >
                      </el-button>
                    </el-input>
                    <el-table
                      ref="multipleTable"
                      tooltip-effect="dark"
                      @selection-change="handleSelectionChange"
                      :data="organizeForm.builderZsListCopy"
                      max-height="240"
                    >
                      <el-table-column width="30" type="selection">
                      </el-table-column>
                      <el-table-column
                        min-width="70"
                        property="name"
                        label="姓名"
                      >
                      </el-table-column>
                      <el-table-column
                        min-width="180"
                        property="orgName"
                        label="组织"
                      >
                      </el-table-column>
                      <el-table-column
                        min-width="120"
                        property="mobilePhone"
                        label="电话"
                      >
                      </el-table-column>
                      <el-table-column fixed="right" label="操作" width="50">
                        <template slot-scope="scope">
                          <el-button
                            type="text"
                            size="small"
                            @click.native.prevent="
                            handleClose('builderZs', scope.row)
                          "
                          >
                            移除
                          </el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                    <el-button
                      size="small"
                      type="text"
                      @click="toggleSelection('builderZs')"
                    >批量移除
                    </el-button>
                    <el-tag slot="reference" style="margin-top: 3px">
                      +{{ organizeForm.builderZsList.length - 1 }}
                    </el-tag>
                  </el-popover>
                  <el-button
                    type="info"
                    slot="append"
                    icon="el-icon-user"
                    @click="onOpenPeopleDialog('lordSentDetermine')"
                  ></el-button>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="isShowCC=='是'">
              <el-form-item label="是否有财产损失:"
                            prop="ifPropertyLoss"
                            key="ccss"
                            :rules="[
                {
                  required: true ,
                  message: '请选择是否有财产损失',
                },
              ]"
              >
                <el-radio-group
                  v-model="backSingleForm.ifPropertyLoss"
                  style="width: 100%"
                >
                  <el-radio label="否">否</el-radio>
                  <el-radio label="是">是</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="backSingleForm.ifPropertyLoss == '是'">
              <el-form-item label="保单号:"
                            key="bdh"
                            prop="insuranceId"
                            :rules="[
                {
                  required:
                    backSingleForm.ifPropertyLoss == '是' ? true : false,
                  message: '请填写保单号',
                  trigger: 'blur',
                },
              ]"
              >
                <el-input
                  placeholder="请输入"
                  v-model="backSingleForm.insuranceId"
                  style="width: 100%"
                  maxlength="255"
                  @keyup.native="descTip(255,'insuranceId','showbdh')"
                ></el-input>
                <div class="el-form-item__error"  v-if="showbdh">已超过填写字数上限</div>

              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="isShowPD=='是'">
              <el-form-item label="派单是否准确:"
                            key="zq"
                            prop="isDispatchAccurate"
                            :rules="[
                {
                  required: true ,
                  message: '请选择派单是否准确',
                },
              ]"
              >
                <el-radio-group
                  v-model="backSingleForm.isDispatchAccurate"
                  style="width: 100%"
                >
                  <el-radio label="否">否</el-radio>
                  <el-radio label="是">是</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>

        </el-card>
        <el-card
          shadow="never"
          header="故障专业信息"
          :body-style="{ padding: '20px' }"
          style="margin-top: 20px"
          class="cus-card"
          v-if="backSingleForm.professionalType != null && backSingleForm.professionalType != ''"
        >
          <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
            <el-col :span="24">
              <el-form-item
                label="故障处理方式:"
                prop="solvedType"
                :rules="{
                required: true,
                message: '请选择故障处理方式',
              }"
              >
                <el-radio-group v-model="backSingleForm.solvedType"
                                style="width: 100%">
                  <el-radio
                    v-for="(item, i) in solvedTypeArr"
                    :key="i"
                    :label="item.dictName"
                    @change="solveTypeChange"
                  >{{ item.dictName }}</el-radio
                  >
                </el-radio-group>

              </el-form-item>
            </el-col>
          </el-row>

          <template v-if="backSingleForm.solvedType != '自动恢复'">

<!--            vIMS/IDC/5GC/核心网/固网/移动数通网/数据网/IP承载A网/IP承载B网/WLAN/动环网/IP互联网/核心网其他-->
            <template v-if="
            backSingleForm.professionalType == 'IDC' ||
            backSingleForm.professionalType == 'vIMS' ||
            backSingleForm.professionalType == '核心网' ||
            backSingleForm.professionalType == '核心网其他' ||
            backSingleForm.professionalType == '智能网' ||
            backSingleForm.professionalType == '5GC' ||
            backSingleForm.professionalType == '固网' ||
            backSingleForm.professionalType == '移动数通网' ||
            backSingleForm.professionalType == '数据网' ||
            backSingleForm.professionalType == 'IP互联网' ||
            backSingleForm.professionalType == 'IP承载A网' ||
            backSingleForm.professionalType == 'IP承载B网' ||
            backSingleForm.professionalType == 'WLAN' ||
            backSingleForm.professionalType == '动环网'">
              <el-row
                type="flex"
                style="flex-wrap: wrap"
                :gutter="20"
                key="11"
              >
                <el-col :span="8">
                  <el-form-item label="故障分类:"
                                prop="faultCateId"
                                :rules="{
                    required: true,
                    message: '请选择故障分类',
                    }"
                  >
                    <el-select
                      v-model="backSingleForm.faultCateId"
                      placeholder="请选择"
                      style="width: 100%"
                      @change="faultCateChange"
                      @focus="
                      getDictData(
                    backSingleForm.professionalTypeId + '_commonpro_faultCate',
                    'faultCateArr'
                    )
                    ">
                      <el-option
                        v-for="(item, i) in faultCateArr"
                        :key="i"
                        :label="item.dictName"
                        :value="item.dictCode"
                      >
                        <span style="float: left">{{item.dictName}}</span>
                        <span v-if="tjFaultCateList.indexOf(item.dictName)>-1" style="float: right;display: flex;align-items: center;height: 100%">
                          <img src="../assets/tj.png" style="width: 18px;height: 18px"/>
                        </span>
                      </el-option>
                    </el-select>


                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="故障原因:"
                                :rules="{  required: true, message: '请选择故障原因',}"
                                prop="faultReason"
                  >

                    <el-select
                      v-model="backSingleForm.faultReason"
                      placeholder="请选择"
                      style="width: 100%"
                      @focus="
                      getDictData(
                    backSingleForm.professionalTypeId +'_'+backSingleForm.faultCateId+ '_commonpro_faultReason',
                    'faultReasonArr'
                    )
                    ">
                      <el-option
                        v-for="(item, i) in faultReasonArr"
                        :key="i"
                        :label="item.dictName"
                        :value="item.dictName"
                      >
                        <span style="float: left">{{item.dictName}}</span>
                        <span v-if="tjFaultReasonList.indexOf(item.dictName)>-1" style="float: right;display: flex;align-items: center;height: 100%">
                         <img src="../assets/tj.png" style="width: 18px;height: 18px"/>
                        </span>
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="故障厂家:"
                                prop="vendor"
                                :rules="{
                required: true,
                message: '请选择故障厂家',
              }"
                  >

                    <el-select
                      v-model="backSingleForm.vendor"
                      placeholder="请选择"
                      style="width: 100%"
                      @focus="
                      getDictData(
                    backSingleForm.professionalTypeId + '_commonpro_vendor',
                    'vendorOptions'
                    )
                    ">
                      <el-option
                        v-for="(item, i) in vendorOptions"
                        :key="i"
                        :label="item.dictName"
                        :value="item.dictName"
                      >
                      </el-option>
                    </el-select>

                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="设备类型:"
                                :rules="{required: true,message: '请选择设备类型',}"
                                prop="eqpType"
                  >

                    <el-select
                      v-model="backSingleForm.eqpType"
                      placeholder="请选择"
                      style="width: 100%"
                      @focus="
                      getDictData(
                    backSingleForm.professionalTypeId + '_commonpro_eqpType',
                    'eqpTypeArr'
                    )
                    ">
                      <el-option
                        v-for="(item, i) in eqpTypeArr"
                        :key="i"
                        :label="item.dictName"
                        :value="item.dictName"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="设备名称:">
                    <el-input
                      placeholder="请输入"
                      v-model="backSingleForm.eqpName"
                      style="width: 100%"
                      maxlength="255"
                      @keyup.native="descTip(255,'eqpName','showsbmc')"
                    >
                      <el-button
                        style="
                    background: #b50b14;
                    border-color: #b50b14;
                    color: #fff;
                    padding: 9px 20px;
                    top: -0.5px;
                    position: relative;
                  "
                        slot="append"
                        @click="deviceSelect"
                      >选择</el-button
                      >
                    </el-input>
                    <div class="el-form-item__error"  v-if="showsbmc">已超过填写字数上限</div>
                    <form
                      id="sub__device"
                      name="sub__device"
                      hidden="true"
                      method="post"
                      action="/resweb_jituan/union/resAssign.out?method=selectEquipment"
                      target="_blank"
                    >
                      <input type="hidden" name="device" id="device" />
                    </form>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="故障原因描述:"
                                prop="falutReasonDesc"
                                :rules="{
                required: true ,
                message: '请填写故障原因描述',
              }"
                  >
                    <el-input
                      type="textarea"
                      :rows="2"
                      v-model="backSingleForm.falutReasonDesc"
                      style="width: 100%"
                      show-word-limit
                      maxlength="255"
                      @keyup.native="descTip(255,'falutReasonDesc','showgzyyms')"
                    >
                    </el-input>
                    <div class="el-form-item__error"  v-if="showgzyyms">已超过填写字数上限</div>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="备注:">
                    <el-input
                      type="textarea"
                      :rows="2"
                      placeholder="请输入"
                      v-model="backSingleForm.falutComment"
                      style="width: 100%"
                      show-word-limit
                      maxlength="255"
                      @keyup.native="descTip(255,'falutComment','showbz')"
                    >
                    </el-input>
                    <div class="el-form-item__error"  v-if="showbz">已超过填写字数上限</div>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="附件:">
                    <el-tag
                      class="fileName_style"
                      readonly
                      v-for="(item, index) in importForm.relatedFilesFileList"
                      :key="index"
                      @close="close(item)"
                      :title="item.name"
                    ><div class="text-truncate">{{ item.name }}</div></el-tag
                    >
                    <el-button size="mini" type="primary" @click="relatedFilesBrowse"
                    >+上传附件</el-button
                    >
                  </el-form-item>
                </el-col>

              </el-row>
            </template>
            <template v-else-if="
            backSingleForm.professionalType == '无线网'">
              <el-row
                type="flex"
                style="flex-wrap: wrap"
                :gutter="20"
                key="22"
              >
                <el-col :span="8">
                  <el-form-item label="故障分类:"
                                prop="faultCateId"
                                :rules="{
                    required: true,
                    message: '请选择故障分类',
                    }"
                  >
                    <el-select
                      v-model="backSingleForm.faultCateId"
                      placeholder="请选择"
                      style="width: 100%"
                      @change="faultCateChange"
                      @focus="
                      getDictData(
                    backSingleForm.professionalTypeId + '_commonpro_faultCate',
                    'faultCateArr'
                    )
                    ">
                      <el-option
                        v-for="(item, i) in faultCateArr"
                        :key="i"
                        :label="item.dictName"
                        :value="item.dictCode"
                      >
                        <span style="float: left">{{item.dictName}}</span>
                        <span v-if="tjFaultCateList.indexOf(item.dictName)>-1" style="float: right;display: flex;align-items: center;height: 100%">
                          <img src="../assets/tj.png" style="width: 18px;height: 18px"/>
                        </span>
                      </el-option>
                    </el-select>


                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="故障原因:"
                                :rules="{  required: true, message: '请选择故障原因',}"
                                prop="faultReason"
                  >

                    <el-select
                      v-model="backSingleForm.faultReason"
                      placeholder="请选择"
                      style="width: 100%"
                      @focus="
                      getDictData(
                    backSingleForm.professionalTypeId +'_'+backSingleForm.faultCateId+ '_commonpro_faultReason',
                    'faultReasonArr'
                    )
                    ">
                      <el-option
                        v-for="(item, i) in faultReasonArr"
                        :key="i"
                        :label="item.dictName"
                        :value="item.dictName"
                      >
                        <span style="float: left">{{item.dictName}}</span>
                        <span v-if="tjFaultReasonList.indexOf(item.dictName)>-1" style="float: right;display: flex;align-items: center;height: 100%">
                         <img src="../assets/tj.png" style="width: 18px;height: 18px"/>
                        </span>
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8"
                        v-if="backSingleForm.faultCate == '基站设备' ||
                                backSingleForm.faultCate == '传输系统'">
                  <el-form-item label="故障厂家:"
                                prop="vendor"
                                key="gzcj"
                                :rules="{
                                  required: (backSingleForm.faultCate == '基站设备' ||
                                backSingleForm.faultCate == '传输系统') ? true : false,
                                message: '请选择故障厂家',
                                }">

                    <el-select
                      v-model="backSingleForm.vendor"
                      placeholder="请选择"
                      style="width: 100%"
                      @focus="
                      getDictData(
                    backSingleForm.professionalTypeId+'_'+backSingleForm.faultCateId + '_commonpro_vendor',
                    'vendorOptions'
                    )
                    ">
                      <el-option
                        v-for="(item, i) in vendorOptions"
                        :key="i"
                        :label="item.dictName"
                        :value="item.dictName"
                      >
                      </el-option>
                    </el-select>

                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="设备类型:"
                                :rules="{required: true,message: '请选择设备类型',}"
                                prop="eqpType"
                  >

                    <el-select
                      v-model="backSingleForm.eqpType"
                      placeholder="请选择"
                      style="width: 100%"
                      @focus="
                      getDictData(
                    backSingleForm.professionalTypeId + '_commonpro_eqpType',
                    'eqpTypeArr'
                    )
                    ">
                      <el-option
                        v-for="(item, i) in eqpTypeArr"
                        :key="i"
                        :label="item.dictName"
                        :value="item.dictName"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="设备名称:">
                    <el-input
                      placeholder="请输入"
                      v-model="backSingleForm.eqpName"
                      style="width: 100%"
                      maxlength="255"
                      @keyup.native="descTip(255,'eqpName','showsbmc')"
                    >
                      <el-button
                        style="
                    background: #b50b14;
                    border-color: #b50b14;
                    color: #fff;
                    padding: 9px 20px;
                    top: -0.5px;
                    position: relative;
                  "
                        slot="append"
                        @click="deviceSelect"
                      >选择</el-button
                      >
                    </el-input>
                    <div class="el-form-item__error"  v-if="showsbmc">已超过填写字数上限</div>
                    <form
                      id="sub__device"
                      name="sub__device"
                      hidden="true"
                      method="post"
                      action="/resweb_jituan/union/resAssign.out?method=selectEquipment"
                      target="_blank"
                    >
                      <input type="hidden" name="device" id="device" />
                    </form>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="维护主体:"
                    :rules="{ required: true, message: '请选择维护主体',}"
                    prop="maintenanceSubject"
                  >
                    <el-select
                      v-model="backSingleForm.maintenanceSubject"
                      placeholder="请选择"
                      style="width: 100%"
                      @focus="
                      getDictData(
                    backSingleForm.professionalTypeId + '_commonpro_maintenanceSubject',
                    'maintenanceSubjectArr'
                    )
                    ">
                      <el-option
                        v-for="(item, i) in maintenanceSubjectArr"
                        :key="i"
                        :label="item.dictName"
                        :value="item.dictName"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="故障定责:"
                                prop="mobileFaultDutyId"
                                :rules="{ required: true, message: '请选择故障定责',}"
                  >
                    <el-select
                      v-model="backSingleForm.mobileFaultDutyId"
                      placeholder="请选择"
                      style="width: 100%"
                      @focus="
                      getDictData(
                    backSingleForm.professionalTypeId + '_commonpro_faultDuty',
                    'mobileFaultDutyArr'
                    )
                    ">
                      <el-option
                        v-for="(item, i) in mobileFaultDutyArr"
                        :key="i"
                        :label="item.dictName"
                        :value="item.dictName"
                      >
                      </el-option>
                    </el-select>

                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="故障原因描述:"
                                prop="falutReasonDesc"
                                :rules="{
                required: true ,
                message: '请填写故障原因描述',
              }"
                  >
                    <el-input
                      show-word-limit
                      maxlength="255"
                      @keyup.native="descTip(255,'falutReasonDesc','showgzyyms')"
                      type="textarea"
                      :rows="2"
                      v-model="backSingleForm.falutReasonDesc"
                      style="width: 100%"
                    >
                    </el-input>
                    <div class="el-form-item__error"  v-if="showgzyyms">已超过填写字数上限</div>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="备注:">
                    <el-input
                      show-word-limit
                      maxlength="255"
                      @keyup.native="descTip(255,'falutComment','showbz')"
                      type="textarea"
                      :rows="2"
                      placeholder="请输入"
                      v-model="backSingleForm.falutComment"
                      style="width: 100%"
                    >
                    </el-input>
                    <div class="el-form-item__error"  v-if="showbz">已超过填写字数上限</div>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="附件:">
                    <el-tag
                      class="fileName_style"
                      readonly
                      v-for="(item, index) in importForm.relatedFilesFileList"
                      :key="index"
                      @close="close(item)"
                      :title="item.name"
                    ><div class="text-truncate">{{ item.name }}</div></el-tag
                    >
                    <el-button size="mini" type="primary" @click="relatedFilesBrowse"
                    >+上传附件</el-button
                    >
                  </el-form-item>
                </el-col>

              </el-row>
            </template>
            <template v-else-if="
            backSingleForm.professionalType == '传输网'||
            backSingleForm.professionalType == '集客' ">
              <el-row
                type="flex"
                style="flex-wrap: wrap"
                :gutter="20"
                key="33"
              >
                <el-col :span="8">
                  <el-form-item label="故障状态:"
                                prop="faultStatus"
                                :rules="{
                required: true,
                message: '请选择故障状态',
              }"
                  >
                    <el-select
                      v-model="backSingleForm.faultStatus"
                      placeholder="请选择内容"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="(item, i) in faultStatusArr"
                        :key="i"
                        :label="item.dictName"
                        :value="item.dictName"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="网络类型:"
                                prop="networkType"
                                :rules="{required: true, message: '请选择网络类型',}"
                  >
                    <el-select
                      v-model="backSingleForm.networkType"
                      placeholder="请选择"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="(item, i) in networkTypeArr"
                        :key="i"
                        :label="item.dictName"
                        :value="item.dictName"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="故障分类:"
                                prop="faultCateId"
                                :rules="{
                    required: true,
                    message: '请选择故障分类',
                    }"
                  >
                    <el-select
                      v-model="backSingleForm.faultCateId"
                      placeholder="请选择"
                      style="width: 100%"
                      @change="faultCateChange"
                      @focus="
                      getDictData(
                    backSingleForm.professionalTypeId + '_commonpro_faultCate',
                    'faultCateArr'
                    )
                    ">
                      <el-option
                        v-for="(item, i) in faultCateArr"
                        :key="i"
                        :label="item.dictName"
                        :value="item.dictCode"
                      >
                        <span style="float: left">{{item.dictName}}</span>
                        <span v-if="tjFaultCateList.indexOf(item.dictName)>-1" style="float: right;display: flex;align-items: center;height: 100%">
                          <img src="../assets/tj.png" style="width: 18px;height: 18px"/>
                        </span>
                      </el-option>
                    </el-select>


                  </el-form-item>
                </el-col>
                <el-col :span="8"
                        v-if="backSingleForm.faultCate == '设备故障' ||
                        backSingleForm.faultCate == '网管故障' ||
                        backSingleForm.faultCate == '动力配套故障' ||
                        backSingleForm.faultCate == '其他'">
                  <el-form-item label="设备类型:"
                                key="101"
                                prop="eqpType"
                    :rules="{
                required: true,
                message: '请选择设备类型',
              }"
                  >
                    <el-select
                      v-model="backSingleForm.eqpType"
                      placeholder="请选择"
                      style="width: 100%"
                      @change="eqpTypeChange"
                      @focus="
                      getDictData(
                    backSingleForm.professionalTypeId + '_commonpro_eqpType',
                    'eqpTypeArr'
                    )
                    ">
                      <el-option
                        v-for="(item, i) in eqpTypeArr"
                        :key="i"
                        :label="item.dictName"
                        :value="item.dictName"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="故障原因:"
                                :rules="{ required: true, message: '请选择内容',}"
                                prop="faultReason"
                  >
                    <el-select
                      v-model="backSingleForm.faultReason"
                      placeholder="请选择"
                      style="width: 100%"
                      @focus="
                      getDictData(
                    backSingleForm.professionalTypeId +'_'+backSingleForm.faultCateId+ '_commonpro_faultReason',
                    'faultReasonArr'
                    )
                    ">
                      <el-option
                        v-for="(item, i) in faultReasonArr"
                        :key="i"
                        :label="item.dictName"
                        :value="item.dictName"
                      >
                        <span style="float: left">{{item.dictName}}</span>
                        <span v-if="tjFaultReasonList.indexOf(item.dictName)>-1" style="float: right;display: flex;align-items: center;height: 100%">
                         <img src="../assets/tj.png" style="width: 18px;height: 18px"/>
                        </span>
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8"
                        v-if="backSingleForm.faultCate != '线路故障'">
                  <el-form-item label="板卡类型:"
                                prop="boardType"
                                key="111"
                                :rules="{
                required:
                 backSingleForm.faultCate != '线路故障' ? true
                    : false,
                message: '请选择板卡类型',
              }"
                  >
                    <el-select
                      v-model="backSingleForm.boardType"
                      placeholder="请选择"
                      style="width: 100%"
                      @focus="
                      getDictData(
                    backSingleForm.professionalTypeId+'_'+backSingleForm.eqpTypeId + '_commonpro_boardType',
                    'boardTypeArr'
                    )
                    ">
                      <el-option
                        v-for="(item, i) in boardTypeArr"
                        :key="i"
                        :label="item.dictName"
                        :value="item.dictName"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8"
                        v-if="backSingleForm.faultCate == '线路故障'">
                  <el-form-item label="光缆名称:"
                                key="122"
                    prop="deviceName"
                  >
                    <el-input v-model="backSingleForm.deviceName" style="width: 100%">
                      <el-button
                        style="
                    background: #b50b14;
                    border-color: #b50b14;
                    color: #fff;
                    padding: 9px 20px;
                    top: -0.5px;
                    position: relative;
                  "
                        slot="append"
                        @click="opticalCableSelect"
                      >选择</el-button
                      ></el-input
                    >
                    <form
                      id="sub__fiberOpticCable"
                      name="sub__fiberOpticCable"
                      hidden="true"
                      method="post"
                      action="/resweb_jituan/union/resAssign.out?method=selectFiberSection&requestJson={}"
                      target="_blank"
                    >
                      <input
                        type="hidden"
                        name="fiberOpticCable"
                        id="fiberOpticCable"
                      />
                    </form>
                  </el-form-item>
                </el-col>
                <el-col :span="8"
                        v-if="backSingleForm.faultCate == '设备故障' ||
                        backSingleForm.faultCate == '网管故障' ||
                        backSingleForm.faultCate == '动力配套故障' ||
                        backSingleForm.faultCate == '其他' ">
                  <el-form-item label="设备名称:" key="102">
                    <el-input
                      placeholder="请输入"
                      v-model="backSingleForm.eqpName"
                      style="width: 100%"
                      maxlength="255"
                      @keyup.native="descTip(255,'eqpName','showsbmc')"
                    >
                      <el-button
                        style="
                    background: #b50b14;
                    border-color: #b50b14;
                    color: #fff;
                    padding: 9px 20px;
                    top: -0.5px;
                    position: relative;
                  "
                        slot="append"
                        @click="deviceSelect"
                      >选择</el-button
                      >
                    </el-input>
                    <div class="el-form-item__error"  v-if="showsbmc">已超过填写字数上限</div>
                    <form
                      id="sub__device"
                      name="sub__device"
                      hidden="true"
                      method="post"
                      action="/resweb_jituan/union/resAssign.out?method=selectEquipment"
                      target="_blank"
                    >
                      <input type="hidden" name="device" id="device" />
                    </form>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="故障区间:"
                    :rules="{
                    required: backSingleForm.faultCate == '线路故障'?true:false,
                    message: '请输入故障区间',
                    }"
                    prop="faultRange"
                  >
                    <el-input
                      placeholder="请输入"
                      v-model="backSingleForm.faultRange"
                      style="width: 100%"
                      maxlength="255"
                      @keyup.native="descTip(255,'faultRange','showgzqj')"
                    >
                    </el-input>
                    <div class="el-form-item__error"  v-if="showgzqj">已超过填写字数上限</div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="维护主体:"
                    :rules="{
                required: backSingleForm.faultCate == '线路故障'?true:false,
                message: '请选择维护主体',
              }"
                    prop="maintenanceSubject"
                  >
                    <el-select
                      v-model="backSingleForm.maintenanceSubject"
                      placeholder="请选择"
                      style="width: 100%"
                      @focus="
                      getDictData(
                    backSingleForm.professionalTypeId + '_commonpro_maintenanceSubject',
                    'maintenanceSubjectArr'
                    )
                    ">
                      <el-option
                        v-for="(item, i) in maintenanceSubjectArr"
                        :key="i"
                        :label="item.dictName"
                        :value="item.dictName"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="受影响系统:">
                    <el-input
                      placeholder="请输入"
                      v-model="backSingleForm.effectSystem"
                      style="width: 100%"
                      maxlength="255"
                      @keyup.native="descTip(255,'effectSystem','showsyxxt')"
                    >
                    </el-input>
                    <div class="el-form-item__error"  v-if="showsyxxt">已超过填写字数上限</div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="受影响电路:">
                    <el-input
                      placeholder="请输入"
                      v-model="backSingleForm.effectCircuit"
                      style="width: 100%"
                      maxlength="255"
                      @keyup.native="descTip(255,'effectCircuit','showsyxdl')"
                    >
                    </el-input>
                    <div class="el-form-item__error"  v-if="showsyxdl">已超过填写字数上限</div>
                  </el-form-item>
                </el-col>
                <el-col :span="8" v-if="backSingleForm.faultCate == '设备故障' ||
                                    backSingleForm.faultCate == '网管故障' ||
                                    backSingleForm.faultCate == '动力配套故障' ||
                                    backSingleForm.faultCate == '其他' ">
                  <el-form-item label="承载业务系统:"
                                prop="bearerSystem"
                                key="103"
                                :rules="{
                required: true,
                message: '承载业务系统不能为空',
              }"
                  >
                    <el-input
                      placeholder="请输入"
                      v-model="backSingleForm.bearerSystem"
                      style="width: 100%"
                      maxlength="255"
                      @keyup.native="descTip(255,'bearerSystem','showczywxt')"
                    >
                    </el-input>
                    <div class="el-form-item__error"  v-if="showczywxt">已超过填写字数上限</div>
                  </el-form-item>
                </el-col>
                <el-col :span="24" v-if="backSingleForm.faultCate == '设备故障' ||
                                    backSingleForm.faultCate == '网管故障' ||
                                    backSingleForm.faultCate == '动力配套故障' ||
                                    backSingleForm.faultCate == '其他' ">
                  <el-form-item label="故障厂家:"
                                key="104"
                    prop="vendor"
                    :rules="{
                required: true,
                message: '请选择故障厂家',
              }"
                  >
                    <el-select
                      v-model="backSingleForm.vendor"
                      placeholder="请选择"
                      style="width: 100%"
                      @focus="
                      getDictData(
                    backSingleForm.professionalTypeId + '_commonpro_vendor',
                    'vendorOptions'
                    )
                    ">
                      <el-option
                        v-for="(item, i) in vendorOptions"
                        :key="i"
                        :label="item.dictName"
                        :value="item.dictName"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="故障原因描述:"
                    prop="falutReasonDesc"
                    :rules="{
                required:  true,
                message: '请填写故障原因描述',
              }"
                  >
                    <el-input
                      show-word-limit
                      maxlength="255"
                      @keyup.native="descTip(255,'falutReasonDesc','showgzyyms')"
                      type="textarea"
                      :rows="2"
                      v-model="backSingleForm.falutReasonDesc"
                      style="width: 100%"
                    >
                    </el-input>
                    <div class="el-form-item__error"  v-if="showgzyyms">已超过填写字数上限</div>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="备注:">
                    <el-input
                      show-word-limit
                      maxlength="255"
                      @keyup.native="descTip(255,'falutComment','showbz')"
                      type="textarea"
                      :rows="2"
                      placeholder="请输入"
                      v-model="backSingleForm.falutComment"
                      style="width: 100%"
                    >
                    </el-input>
                    <div class="el-form-item__error"  v-if="showbz">已超过填写字数上限</div>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="附件:">
                    <el-tag
                      class="fileName_style"
                      readonly
                      v-for="(item, index) in importForm.relatedFilesFileList"
                      :key="index"
                      @close="close(item)"
                      :title="item.name"
                    ><div class="text-truncate">{{ item.name }}</div></el-tag
                    >
                    <el-button size="mini" type="primary" @click="relatedFilesBrowse"
                    >+上传附件</el-button
                    >
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="是否路由调整:"
                    prop="isRouterChange"
                  >
                    <el-radio-group
                      v-model="backSingleForm.isRouterChange"
                      @change="isRouteChange"
                    >
                      <el-radio label="否">否</el-radio>
                      <el-radio label="是">是</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="16" v-if="backSingleForm.isRouterChange == '是'">
                  <el-form-item label="路由工单编号:"
                                key="105"
                                :rules="{
                    required: backSingleForm.isRouterChange == '是' ? true : false,
                    message: '请输入',
                    }"
                                prop="routerChangeJobCode"
                  >
                    <el-input
                      placeholder="请输入"
                      v-model="backSingleForm.routerChangeJobCode"
                      style="width: 100%"
                      maxlength="255"
                      @keyup.native="descTip(255,'routerChangeJobCode','showlygdbh')"
                    >
                    </el-input>
                    <div class="el-form-item__error"  v-if="showlygdbh">已超过填写字数上限</div>
                  </el-form-item>
                </el-col>
              </el-row>
            </template>
            <template v-else-if="
            backSingleForm.professionalType == '接入网'">
              <el-row
                type="flex"
                style="flex-wrap: wrap"
                :gutter="20"
                key="44"
              >
                <el-col :span="8">
                  <el-form-item label="故障分类:"
                                prop="faultCate"
                                :rules="{
                required: true,
                message: '请选择故障分类',
              }"
                  >
                    <el-select
                      v-model="backSingleForm.faultCateId"
                      placeholder="请选择"
                      style="width: 100%"
                      @change="faultCateChange"
                      @focus="
                      getDictData(
                    backSingleForm.professionalTypeId + '_commonpro_faultCate',
                    'faultCateArr'
                    )
                    ">
                      <el-option
                        v-for="(item, i) in faultCateArr"
                        :key="i"
                        :label="item.dictName"
                        :value="item.dictCode"
                      >
                        <span style="float: left">{{item.dictName}}</span>
                        <span v-if="tjFaultCateList.indexOf(item.dictName)>-1" style="float: right;display: flex;align-items: center;height: 100%">
                          <img src="../assets/tj.png" style="width: 18px;height: 18px"/>
                        </span>
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="故障原因:"
                                :rules="{
                required: true,
                message: '请选择故障原因',
              }"
                                prop="faultReason"
                  >
                    <el-select
                      v-model="backSingleForm.faultReason"
                      placeholder="请选择"
                      style="width: 100%"
                      @focus="
                      getDictData(
                    backSingleForm.professionalTypeId +'_'+backSingleForm.faultCateId+ '_commonpro_faultReason',
                    'faultReasonArr'
                    )
                    ">
                      <el-option
                        v-for="(item, i) in faultReasonArr"
                        :key="i"
                        :label="item.dictName"
                        :value="item.dictName"
                      >
                        <span style="float: left">{{item.dictName}}</span>
                        <span v-if="tjFaultReasonList.indexOf(item.dictName)>-1" style="float: right;display: flex;align-items: center;height: 100%">
                         <img src="../assets/tj.png" style="width: 18px;height: 18px"/>
                        </span>
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="故障厂家:"
                                prop="vendor"
                                :rules="{
                required: true,
                message: '请选择故障厂家',
              }"
                  >
                    <el-select
                      v-model="backSingleForm.vendor"
                      placeholder="请选择"
                      style="width: 100%"
                      @focus="
                      getDictData(
                    backSingleForm.professionalTypeId + '_commonpro_vendor',
                    'vendorOptions'
                    )
                    ">
                      <el-option
                        v-for="(item, i) in vendorOptions"
                        :key="i"
                        :label="item.dictName"
                        :value="item.dictName"
                      >
                      </el-option>
                    </el-select>

                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="设备类型:"
                                :rules="{
                required: true,
                message: '请选择设备类型',
              }"
                                prop="eqpType"
                  >
                    <el-select
                      v-model="backSingleForm.eqpType"
                      placeholder="请选择"
                      style="width: 100%"
                      @focus="
                      getDictData(
                    backSingleForm.professionalTypeId + '_commonpro_eqpType',
                    'eqpTypeArr'
                    )
                    ">
                      <el-option
                        v-for="(item, i) in eqpTypeArr"
                        :key="i"
                        :label="item.dictName"
                        :value="item.dictName"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="设备名称:">
                    <el-input
                      placeholder="请输入"
                      v-model="backSingleForm.eqpName"
                      style="width: 100%"
                      maxlength="255"
                      @keyup.native="descTip(255,'eqpName','showsbmc')"
                    >
                      <el-button
                        style="
                    background: #b50b14;
                    border-color: #b50b14;
                    color: #fff;
                    padding: 9px 20px;
                    top: -0.5px;
                    position: relative;
                  "
                        slot="append"
                        @click="deviceSelect"
                      >选择</el-button
                      >
                    </el-input>
                    <div class="el-form-item__error"  v-if="showsbmc">已超过填写字数上限</div>
                    <form
                      id="sub__device"
                      name="sub__device"
                      hidden="true"
                      method="post"
                      action="/resweb_jituan/union/resAssign.out?method=selectEquipment"
                      target="_blank"
                    >
                      <input type="hidden" name="device" id="device" />
                    </form>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="故障定责:"
                                prop="mobileFaultDutyId"
                                :rules="{
                required: true,
                message: '请选择故障定责',
              }"
                  >
                    <el-select
                      v-model="backSingleForm.mobileFaultDutyId"
                      placeholder="请选择"
                      style="width: 100%"
                      @focus="
                      getDictData(
                    backSingleForm.professionalTypeId + '_commonpro_faultDuty',
                    'mobileFaultDutyArr'
                    )
                    ">
                      <el-option
                        v-for="(item, i) in mobileFaultDutyArr"
                        :key="i"
                        :label="item.dictName"
                        :value="item.dictName"
                      >
                      </el-option>
                    </el-select>

                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="故障原因描述:"
                                prop="falutReasonDesc"
                                :rules="{
                required: true ,
                message: '请填写故障原因描述',
              }"
                  >
                    <el-input
                      show-word-limit
                      maxlength="255"
                      @keyup.native="descTip(255,'falutReasonDesc','showgzyyms')"
                      type="textarea"
                      :rows="2"
                      v-model="backSingleForm.falutReasonDesc"
                      style="width: 100%"
                    >
                    </el-input>
                    <div class="el-form-item__error"  v-if="showgzyyms">已超过填写字数上限</div>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="备注:">
                    <el-input
                      show-word-limit
                      maxlength="255"
                      @keyup.native="descTip(255,'falutComment','showbz')"
                      type="textarea"
                      :rows="2"
                      placeholder="请输入"
                      v-model="backSingleForm.falutComment"
                      style="width: 100%"
                    >
                    </el-input>
                    <div class="el-form-item__error"  v-if="showbz">已超过填写字数上限</div>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="附件:">
                    <el-tag
                      class="fileName_style"
                      readonly
                      v-for="(item, index) in importForm.relatedFilesFileList"
                      :key="index"
                      @close="close(item)"
                      :title="item.name"
                    ><div class="text-truncate">{{ item.name }}</div></el-tag
                    >
                    <el-button size="mini" type="primary" @click="relatedFilesBrowse"
                    >+上传附件</el-button
                    >
                  </el-form-item>
                </el-col>

              </el-row>
            </template>
            <template v-else-if="
            backSingleForm.professionalType == '增值平台'||
            backSingleForm.professionalType == '其他'||
            backSingleForm.professionalType == 'IT监控' ">
              <el-row
                type="flex"
                style="flex-wrap: wrap"
                :gutter="20"
                key="55"
              >
                <el-col :span="8">
                  <el-form-item label="故障分类:"
                                prop="faultCate"
                                :rules="{
                required: true,
                message: '请选择故障分类',
              }"
                  >
                    <el-select
                      v-model="backSingleForm.faultCateId"
                      placeholder="请选择"
                      style="width: 100%"
                      @change="faultCateChange"
                      @focus="
                      getDictData(
                    backSingleForm.professionalTypeId + '_commonpro_faultCate',
                    'faultCateArr'
                    )
                    ">
                      <el-option
                        v-for="(item, i) in faultCateArr"
                        :key="i"
                        :label="item.dictName"
                        :value="item.dictCode"
                      >
                        <span style="float: left">{{item.dictName}}</span>
                        <span v-if="tjFaultCateList.indexOf(item.dictName)>-1" style="float: right;display: flex;align-items: center;height: 100%">
                          <img src="../assets/tj.png" style="width: 18px;height: 18px"/>
                        </span>
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="故障原因:"
                                :rules="{
                required: true,
                message: '请选择故障原因',
              }"
                                prop="faultReason"
                  >
                    <el-select
                      v-model="backSingleForm.faultReason"
                      placeholder="请选择"
                      style="width: 100%"
                      @focus="
                      getDictData(
                    backSingleForm.professionalTypeId +'_'+backSingleForm.faultCateId+ '_commonpro_faultReason',
                    'faultReasonArr'
                    )
                    ">
                      <el-option
                        v-for="(item, i) in faultReasonArr"
                        :key="i"
                        :label="item.dictName"
                        :value="item.dictName"
                      >
                        <span style="float: left">{{item.dictName}}</span>
                        <span v-if="tjFaultReasonList.indexOf(item.dictName)>-1" style="float: right;display: flex;align-items: center;height: 100%">
                         <img src="../assets/tj.png" style="width: 18px;height: 18px"/>
                        </span>
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="故障原因描述:"
                                prop="falutReasonDesc"
                                :rules="{
                required: true ,
                message: '请填写故障原因描述',
              }"
                  >
                    <el-input
                      show-word-limit
                      maxlength="255"
                      @keyup.native="descTip(255,'falutReasonDesc','showgzyyms')"
                      type="textarea"
                      :rows="2"
                      v-model="backSingleForm.falutReasonDesc"
                      style="width: 100%"
                    >
                    </el-input>
                    <div class="el-form-item__error"  v-if="showgzyyms">已超过填写字数上限</div>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="备注:">
                    <el-input
                      show-word-limit
                      maxlength="255"
                      @keyup.native="descTip(255,'falutComment','showbz')"
                      type="textarea"
                      :rows="2"
                      placeholder="请输入"
                      v-model="backSingleForm.falutComment"
                      style="width: 100%"
                    >
                    </el-input>
                    <div class="el-form-item__error"  v-if="showbz">已超过填写字数上限</div>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="附件:">
                    <el-tag
                      class="fileName_style"
                      readonly
                      v-for="(item, index) in importForm.relatedFilesFileList"
                      :key="index"
                      @close="close(item)"
                      :title="item.name"
                    ><div class="text-truncate">{{ item.name }}</div></el-tag
                    >
                    <el-button size="mini" type="primary" @click="relatedFilesBrowse"
                    >+上传附件</el-button
                    >
                  </el-form-item>
                </el-col>

              </el-row>
            </template>
            <template v-else-if="
            backSingleForm.professionalType == '省分云'">
              <el-row
                type="flex"
                style="flex-wrap: wrap"
                :gutter="20"
                key="66"
              >

                <el-col :span="8">
                  <el-form-item label="业务名称:"
                    prop="businessName"
                  >
                    <el-input
                      v-model="backSingleForm.businessName"
                      style="width: 100%"
                      placeholder="请输入"
                      maxlength="255"
                      @keyup.native="descTip(255,'businessName','showywmc')"
                    ></el-input>
                    <div class="el-form-item__error"  v-if="showywmc">已超过填写字数上限</div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="紧急程度:" prop="emergencyLevel"
                                :rules="{
                    required: true,
                    message: '请选择',
                    }">
                    <el-select
                      v-model="backSingleForm.emergencyLevel"
                      placeholder="请选择"
                      style="width: 100%">
                      <el-option
                        v-for="(item, i) in emergencyLevelArr"
                        :key="i"
                        :label="item.dictName"
                        :value="item.dictName"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="故障分类:"
                                prop="faultCate"
                                :rules="{
                required: true,
                message: '请选择故障分类',
              }"
                  >
                    <el-select
                      v-model="backSingleForm.faultCateId"
                      placeholder="请选择"
                      style="width: 100%"
                      @change="faultCateChange"
                      @focus="
                      getDictData(
                    backSingleForm.professionalTypeId + '_commonpro_faultCate',
                    'faultCateArr'
                    )
                    ">
                      <el-option
                        v-for="(item, i) in faultCateArr"
                        :key="i"
                        :label="item.dictName"
                        :value="item.dictCode"
                      >
                        <span style="float: left">{{item.dictName}}</span>
                        <span v-if="tjFaultCateList.indexOf(item.dictName)>-1" style="float: right;display: flex;align-items: center;height: 100%">
                          <img src="../assets/tj.png" style="width: 18px;height: 18px"/>
                        </span>
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="故障原因:"
                                :rules="{
                required: true,
                message: '请选择故障原因',
              }"
                                prop="faultReason"
                  >
                    <el-select
                      v-model="backSingleForm.faultReason"
                      placeholder="请选择"
                      style="width: 100%"
                      @focus="
                      getDictData(
                    backSingleForm.professionalTypeId +'_'+backSingleForm.faultCateId+ '_commonpro_faultReason',
                    'faultReasonArr'
                    )
                    ">
                      <el-option
                        v-for="(item, i) in faultReasonArr"
                        :key="i"
                        :label="item.dictName"
                        :value="item.dictName"
                      >
                        <span style="float: left">{{item.dictName}}</span>
                        <span v-if="tjFaultReasonList.indexOf(item.dictName)>-1" style="float: right;display: flex;align-items: center;height: 100%">
                         <img src="../assets/tj.png" style="width: 18px;height: 18px"/>
                        </span>
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="设备类型:"
                                :rules="{
                required: true,
                message: '请选择设备类型',
              }"
                                prop="eqpType"
                  >
                    <el-select
                      v-model="backSingleForm.eqpType"
                      placeholder="请选择"
                      style="width: 100%"
                      @focus="
                      getDictData(
                    backSingleForm.professionalTypeId + '_commonpro_eqpType',
                    'eqpTypeArr'
                    )
                    ">
                      <el-option
                        v-for="(item, i) in eqpTypeArr"
                        :key="i"
                        :label="item.dictName"
                        :value="item.dictName"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="是否硬件故障:"
                    prop="hardwareFlag"
                    :rules="{
                required: true,
                message: '请选择',
              }"
                  >
                    <el-radio-group
                      v-model="backSingleForm.hardwareFlag"
                    >
                      <el-radio label="否">否</el-radio>
                      <el-radio label="是">是</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="设备名称:">
                    <el-input
                      placeholder="请输入"
                      v-model="backSingleForm.eqpName"
                      style="width: 100%"
                      maxlength="255"
                      @keyup.native="descTip(255,'eqpName','showsbmc')"
                    >
                      <el-button
                        style="
                    background: #b50b14;
                    border-color: #b50b14;
                    color: #fff;
                    padding: 9px 20px;
                    top: -0.5px;
                    position: relative;
                  "
                        slot="append"
                        @click="deviceSelect"
                      >选择</el-button
                      >
                    </el-input>
                    <div class="el-form-item__error"  v-if="showsbmc">已超过填写字数上限</div>
                    <form
                      id="sub__device"
                      name="sub__device"
                      hidden="true"
                      method="post"
                      action="/resweb_jituan/union/resAssign.out?method=selectEquipment"
                      target="_blank"
                    >
                      <input type="hidden" name="device" id="device" />
                    </form>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="故障原因描述:"
                                prop="falutReasonDesc"
                                :rules="{
                required: true ,
                message: '请填写故障原因描述',
              }"
                  >
                    <el-input
                      show-word-limit
                      maxlength="255"
                      @keyup.native="descTip(255,'falutReasonDesc','showgzyyms')"
                      type="textarea"
                      :rows="2"
                      v-model="backSingleForm.falutReasonDesc"
                      style="width: 100%"
                    >
                    </el-input>
                    <div class="el-form-item__error"  v-if="showgzyyms">已超过填写字数上限</div>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="备注:">
                    <el-input
                      show-word-limit
                      maxlength="255"
                      @keyup.native="descTip(255,'falutComment','showbz')"
                      type="textarea"
                      :rows="2"
                      placeholder="请输入"
                      v-model="backSingleForm.falutComment"
                      style="width: 100%"
                    >
                    </el-input>
                    <div class="el-form-item__error"  v-if="showbz">已超过填写字数上限</div>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="附件:">
                    <el-tag
                      class="fileName_style"
                      readonly
                      v-for="(item, index) in importForm.relatedFilesFileList"
                      :key="index"
                      @close="close(item)"
                      :title="item.name"
                    ><div class="text-truncate">{{ item.name }}</div></el-tag
                    >
                    <el-button size="mini" type="primary" @click="relatedFilesBrowse"
                    >+上传附件</el-button
                    >
                  </el-form-item>
                </el-col>

              </el-row>
            </template>
          </template>
          <template v-if="backSingleForm.solvedType == '自动恢复'">
            <el-row
              type="flex"
              style="flex-wrap: wrap"
              :gutter="20"
              key="66"
            >
              <el-col :span="24">
                <el-form-item label="故障原因描述:"
                              prop="falutReasonDesc">
                  <el-input
                    show-word-limit
                    maxlength="255"
                    @keyup.native="descTip(255,'falutReasonDesc','showgzyyms')"
                    type="textarea"
                    :rows="2"
                    v-model="backSingleForm.falutReasonDesc"
                    style="width: 100%"
                  >
                  </el-input>
                  <div class="el-form-item__error"  v-if="showgzyyms">已超过填写字数上限</div>
                </el-form-item>
              </el-col>
            </el-row>
          </template>
        </el-card>
      </template>
    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: center">
      <el-button type="primary" v-if="sumBtnShow" @click="nextStepEvaluation()"
      >下一步</el-button
      >
      <el-button type="primary" v-else @click="handleSubmit('backSingleForm')"
      >提交</el-button
      >
      <el-button @click="onReset">重 置</el-button>
    </div>

<!--    <dia-orgs-user-tree-other-->
<!--      :title="diaPeople.title"-->
<!--      :visible.sync="diaPeople.visible"-->
<!--      :showOrgsTree="diaPeople.showOrgsTree"-->
<!--      :professionalType="common.professionalTypeName"-->
<!--      :limit-peson="10"-->
<!--      @on-save="onSavePeople"-->
<!--      :appendToBody="true"-->
<!--    />-->
    <dia-tissue-tree
      v-if="isDiaOrgsUserTree"
      :title="diaPeople.title"
      :visible.sync="diaPeople.visible"
      :showOrgsTree="diaPeople.showOrgsTree"
      :professionalType="common.professionalTypeName"
      :limit-peson="10"
      @on-save="onSavePeople"
      :appendToBody="true"
      :userAttribution="userAttribution"
      :show-contact-user-tab="true"
      :show-contact-org-tab="diaPeople.showOrgsTree"
    />
    <area-tree
      :visible.sync="faultRegionTreeVisible"
      level="3"
      v-model="backSingleForm.faultRegion"
    ></area-tree>
    <el-dialog
      width="420px"
      title="附件选择"
      :visible.sync="relatedFilesDialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <file-upload
        @change="changeFileData"
        @cancel="closeAttachmentDialog"
      ></file-upload>
    </el-dialog>
    <el-dialog
      width="620px"
      title="满意度评价"
      :visible.sync="evaluationDialogVisible"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      append-to-body
    >
      <el-form ref="evaluation" :model="evaluation">
        <el-form-item label="派单准确度:" label-width="90px">
          <el-rate v-model="evaluation.orderAccuracy" :colors="colors">
          </el-rate>
        </el-form-item>
        <el-form-item label="诊断准确度:" label-width="90px">
          <el-rate v-model="evaluation.diagnosticrAccuracy" :colors="colors">
          </el-rate>
        </el-form-item>
        <el-form-item
          v-if="
            evaluation.orderAccuracy <= 3 || evaluation.diagnosticrAccuracy <= 3
          "
          label="反馈问题:"
          key="167"
          label-width="90px"
          prop="feedbackProblemCheckList"
          :rules="[
            {
              required:
                evaluation.orderAccuracy > 3 &&
                evaluation.diagnosticrAccuracy > 3
                  ? false
                  : true,
              message: '请选择要反馈的问题',
              trigger: 'blur',
            },
          ]"
        >
          <el-checkbox-group
            v-model="evaluation.feedbackProblemCheckList"
            @change="feedbackChange"
          >
            <el-checkbox label="派单超时"></el-checkbox>
            <el-checkbox label="工单基础信息错误"></el-checkbox>
            <el-checkbox label="诊断信息不完整"></el-checkbox>
            <el-checkbox label="诊断信息错误"></el-checkbox>
            <el-checkbox label="其他"></el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item
          v-if="
            viewsOnContentShow &&
            (evaluation.orderAccuracy <= 3 ||
              evaluation.diagnosticrAccuracy <= 3)
          "
          prop="viewsOnContent"
          key="134"
          :rules="{
            required: true,
            message: '请填写内容',
          }"
        >
          <el-input
            maxlength="500"
            type="textarea"
            :rows="3"
            placeholder="为了下次给您更好的服务，请留下宝贵意见，对您和我们都很重要"
            v-model="evaluation.viewsOnContent"
          >
          </el-input>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button
          type="primary"
          @click="handleSubmit('evaluation')"
          v-loading.fullscreen.lock="backSingleFullscreenLoading"
        >提 交</el-button
        >
        <el-button @click="evaluationDialogVisible = false">上一步</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import moment from "moment";
import { mapGetters } from "vuex";
import DictSelect from "../../../workOrder/components/DictSelect.vue";
import { apiBackSingle, apiGetFaultArea,apiBackSingleInit,apiFileUpload ,getCurrentTime} from "../api/CommonApi";
import { apiDict, apiGetOrgInfo } from "../../../workOrder/api/CommonApi";
import AreaTree from "../../../../components/AreaTree/AreaTree";
// import FileUpload from "../../../workOrder/components/FileUpload.vue";
import FileUpload from "./FileUpload";
// import DiaOrgsUserTreeOther from "./DiaOrgsUserTreeOther.vue";
import DiaTissueTree from "@/plugin/backbone/components/common/DiaTissueTree";



import { config } from "../api/TroubleshootingTime";
export default {
  name: "BackSingle",
  props: {
    common: Object,
    timing: Object,
  },
  components: {
    DictSelect,
    FileUpload,
    DiaTissueTree,
    AreaTree
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  data() {
    var validFaultRecoveryTime = (rule, value, callback) => {
      // debugger
      if (this.backSingleForm.professionalType == '铁塔'){
        callback();
      }
      else{
        if (value === "" || value === null) {
          callback(new Error("请选择时间"));
        }
        else{
          if (this.backSingleForm.faultRecoveryTime) {
            let seconds3 = moment(
              this.backSingleForm.faultRecoveryTime,
              "YYYY-MM-DD HH:mm:ss"
            ).diff(moment(new Date(), "YYYY-MM-DD HH:mm:ss"), "seconds");
            let seconds4 = moment(
              this.backSingleForm.faultRecoveryTime,
              "YYYY-MM-DD HH:mm:ss"
            ).diff(
              moment(this.common.failureTime, "YYYY-MM-DD HH:mm:ss"),
              "seconds"
            );
            if (seconds3 > 0 || seconds4 <= 0) {
              callback(
                new Error(
                  "当前时间>=业务恢复时间>=故障发生时间，请重新检查后选择正确时间"
                )
              );
            } else {
              callback();
            }
          } else {
            callback();
          }
        }
      }
    };
    var validFaultOverTime = (rule, value, callback) => {
      // debugger
      if (this.backSingleForm.professionalType == '铁塔'){
        callback();
      }
      else{
        if (value === "" || value === null) {
          callback(new Error("请选择时间"));
        } else {
          if (this.backSingleForm.faultOverTime) {
            let seconds3 = moment(
              this.backSingleForm.faultOverTime,
              "YYYY-MM-DD HH:mm:ss"
            ).diff(moment(new Date(), "YYYY-MM-DD HH:mm:ss"), "seconds");
            let seconds4 = moment(
              this.backSingleForm.faultOverTime,
              "YYYY-MM-DD HH:mm:ss"
            ).diff(
              moment(this.common.failureTime, "YYYY-MM-DD HH:mm:ss"),
              "seconds"
            );
            if (seconds3 > 0 || seconds4 <= 0) {
              callback(
                new Error(
                  "当前时间>=故障结束时间>=故障发生时间，请重新检查后选择正确时间"
                )
              );
            } else {
              callback();
            }
          } else {
            callback();
          }
        }
      }

    };
    return {
      showyxfw:false,
      showgzqj:false,
      showsyxxt:false,
      showsyxdl:false,
      showhdr:false,
      showhdrdh:false,
      showsszldyz:false,
      showttzzzybm:false,
      showzzid:false,
      showzzmc:false,
      showssds:false,
      showssqx:false,
      showsqjmyy:false,
      showsqjmfzs:false,
      showcljgms:false,
      showbdh:false,
      showczywxt:false,
      showlygdbh:false,
      showywmc:false,
      showsbmc:false,
      showgzyyms:false,
      showbz:false,
      showTipTime:5000,
      tjProfessionalTypeList:[],
      tjFaultCateList:[],
      tjFaultReasonList:[],
      userAttribution: "cpBackSingle",
      isDiaOrgsUserTree: false,
      towerAcceptTime:'',//铁塔第一次接单时间
      fbInitData:{},//初始化返回的信息
      sumBtnShow: true,//下一步按钮是否显示
      timeRange: "00:00:00 - 23:59:59",
      backSingleForm: {
        linkId:null,
        woId: null,
        workItemId: null,
        processInstId: null,
        actionName:'返单',

        //非铁塔
        professionalTypeId: null,
        professionalType: '',
        alarmCreateTime: null,
        faultNoticeTime: null,
        faultRecoveryTime: null,
        recoveryDuration: 0, //业务恢复历时
        faultOverTime: null,
        faultDuration: 0, //故障处理历时
        faultRegion: '',
        dept: null,
        recoveryPerson: null,
        suspendDuration: 0, //挂起历时 单位秒
        faultCleanDuration: 0, //故障处理净历时 单位秒
        isEffectBusiness:'是',
        effectRange: null,
        isSiteOffline: '否',
        siteOfflineReason: null,
        solvedType:'',
        faultCateId:null,
        faultCate: null,
        faultReason:null,
        vendor: null,
        //设备故障
        eqpTypeId: null,
        eqpType: null,
        eqpName: "",
        falutReasonDesc:null,
        // falutReasonDesc:
        //   this.common.faultCauseDescription ||
        //   "样例：距xx机房xxkm处，光缆因xxx（原因）中断。 处理方式：xxxx （熔接或者倒带后）恢复。",
        falutComment: null,
        maintenanceSubject:null,
        mobileFaultDutyId:null,
        faultStatus: null,
        networkType: null,
        boardType:null,
        deviceName:null,
        faultRange: null,
        effectSystem: null,
        effectCircuit: null,
        bearerSystem:null,
        isRouterChange: "否", //是否路由调整  1116
        routerChangeJobCode: "", //路由调整单号
        businessName:null,
        emergencyLevel:null,
        hardwareFlag:null,
        ifPropertyLoss:'否',//是否有财产损失
        insuranceId:"",//保单号
        isDispatchAccurate:'是',//派单是否准确

        //铁塔
        // recoveryPerson:null,
        recoveryPhone:null,
        // faultNoticeTime:null,
        repairQuality:[],
        fdbkQuality:null,
        satisfaction:'否',
        evaluationOpinion:null,
        faultSource:null,
        diskName:null,
        interSystem:null,
        // effectSystem:null,
        interCircuit:'否',
        // faultRange:null,
        files:null,
        peerProcessor: "", // 同行处理人id
        peerProcessorName:'', // 同行处理人name
        peerProcessorInfo:'',//组织名称-姓名-电话
        buildZs:'',//占位
      },
      backSingleFullscreenLoading: false,
      faultRegionTreeVisible:false,//故障发生地区选择树
      //关联附件
      relatedFilesDialogVisible: false,
      importForm: {
        relatedFilesFileList: [],
      },
      evaluationDialogVisible: false,
      evaluation: {
        orderAccuracy: 5,
        diagnosticrAccuracy: 5,
        viewsOnContent: null,
        feedbackProblemCheckList: [],
      },
      colors: ["#99A9BF", "#F7BA2A", "#FF9900"], // 等同于 { 2: '#99A9BF', 4: { value: '#F7BA2A', excluded: true }, 5: '#FF9900' }
      //当前人的部门
      currentOrgName: null,
      faultRegionOptions: [],
      userData: null,
      faultReasonDictId: 0, //故障原因 多字典ID
      cardTypeDictId: 0, //板卡类型 多字典ID
      startTimeRange: "",
      startTimeRangeByFaultOverTime: "",
      backSingleFormRule: {
        faultRecoveryTime: [{ validator: validFaultRecoveryTime,required:true, trigger: "blur" }],
        faultOverTime: [
          { validator: validFaultOverTime,required:true, trigger: "blur" },
        ],
      },
      backSingleFormRuleTT: {},
      viewsOnContentShow: false,
      areaCode: null, //区域编码
      category: null, //省份返单 OR 地市返单
      createTimeDate: "",
      pickerOptions: {
        disabledDate: this.disabledDate,
        selectableRange: this.timeRange,
      },
      // isRouteNoShow: true,
      // isRouteAdjustShow: true,
      processionTypeArr:[],//故障所属专业
      siteOfflineReasonArr:[],//退服原因
      solvedTypeArr:[],//故障处理方式
      faultCateArr:[],//故障分类
      faultReasonArr:[],//故障原因
      vendorOptions: [],//故障厂家
      eqpTypeArr:[],//设备类型
      maintenanceSubjectArr:[],//维护主体
      mobileFaultDutyArr:[],//故障定责
      faultStatusArr:[],//故障状态
      networkTypeArr:[],//网络类型
      boardTypeArr:[],//板卡类型
      emergencyLevelArr:[],//紧急程度
      carrierSharingArr:[],//运营商共享情况
      siteServiceLevelArr:[],//站址服务等级
      ifTowerReasonArr:[],//申告工单是否铁塔原因
      declarationFaultClassificationArr:[],//申告工单故障分类
      ifApplyForReliefArr:[],//是否申请减免
      fileLinkId:'',
      isShowCC:'否',
      isShowPD:'否',
      organizeForm: {
        builderZsList: [],
        builderZsListCopy: [],
        builderZsName: ""
      },
      multipleSelection:[],//人员多选
      diaPeople: {
        visible: false,
        title: "",
        saveName: "",
        saveTitleMap: {
          lordSentDetermine: "同行处理人",
        },
        showOrgsTree: false,
        // showOrgsTreeMap: {
        //   lordSentDetermine:false,
        // },
      },
    };
  },
  watch: {
    "organizeForm.builderZsList": {
      handler(newV) {
        if (newV.length > 0) {
          this.backSingleForm.buildZs = "已选";
        } else {
          this.backSingleForm.buildZs = "";
        }
      },
      deep: true,
    },
    "backSingleForm.faultRecoveryTime": {
      handler(val) {
        if (this.backSingleForm.professionalType != '铁塔')
        {
          let valDate = val.split(" ")[0];
          let originDate = this.common.failureTime.split(" ")[0];
          let now = new Date();
          now.setMinutes(now.getMinutes() + 1, now.getSeconds(), 0);
          valDate = valDate.replace(/\-/g, "/");
          originDate = originDate.replace(/\-/g, "/");
          let year = now.getFullYear();
          let month = now.getMonth() + 1;
          let date = now.getDate();
          let nowDate =
            year + "/" + this.addZero(month) + "/" + this.addZero(date);
          let valDateUnix = Date.parse(valDate);
          let originDateUnix = Date.parse(originDate);
          let nowDateUnix = Date.parse(nowDate);

          let array = this.backSingleForm.alarmCreateTime.split(" ");
          let createTime = array[1];
          let hour = now.getHours();
          let minute = now.getMinutes();
          let second = now.getSeconds();
          let nowTime =
            this.addZero(hour) +
            ":" +
            this.addZero(minute) +
            ":" +
            this.addZero(second);
          if (valDateUnix == originDateUnix && originDateUnix == nowDateUnix) {
            this.timeRange = createTime + " - " + nowTime;
            this.pickerOptions.selectableRange = this.timeRange;
          } else if (
            valDateUnix == originDateUnix &&
            originDateUnix < nowDateUnix
          ) {
            this.timeRange = createTime + " - 23:59:59";
            this.pickerOptions.selectableRange = this.timeRange;
          } else if (valDateUnix > originDateUnix && valDateUnix < nowDateUnix) {
            this.timeRange = "00:00:00 - 23:59:59";
            this.pickerOptions.selectableRange = this.timeRange;
          } else if (valDateUnix > originDateUnix && valDateUnix == nowDateUnix) {
            this.timeRange = "00:00:00 - " + nowTime;
            this.pickerOptions.selectableRange = this.timeRange;
          }
          console.log(this.timeRange);
        }
      },
      deep: true,
    },
    "backSingleForm.faultOverTime": {
      handler(val) {
        if (this.backSingleForm.professionalType != '铁塔')
        {
          let valDate = val.split(" ")[0];
          let originDate = this.common.failureTime.split(" ")[0];
          let now = new Date();
          now.setMinutes(now.getMinutes() + 1, now.getSeconds(), 0);
          valDate = valDate.replace(/\-/g, "/");
          originDate = originDate.replace(/\-/g, "/");
          let year = now.getFullYear();
          let month = now.getMonth() + 1;
          let date = now.getDate();
          let nowDate =
            year + "/" + this.addZero(month) + "/" + this.addZero(date);
          let valDateUnix = Date.parse(valDate);
          let originDateUnix = Date.parse(originDate);
          let nowDateUnix = Date.parse(nowDate);

          let array = this.backSingleForm.alarmCreateTime.split(" ");
          let createTime = array[1];
          let hour = now.getHours();
          let minute = now.getMinutes();
          let second = now.getSeconds();
          let nowTime =
            this.addZero(hour) +
            ":" +
            this.addZero(minute) +
            ":" +
            this.addZero(second);
          if (valDateUnix == originDateUnix && originDateUnix == nowDateUnix) {
            this.timeRange = createTime + " - " + nowTime;
            this.pickerOptions.selectableRange = this.timeRange;
          } else if (
            valDateUnix == originDateUnix &&
            originDateUnix < nowDateUnix
          ) {
            this.timeRange = createTime + " - 23:59:59";
            this.pickerOptions.selectableRange = this.timeRange;
          } else if (valDateUnix > originDateUnix && valDateUnix < nowDateUnix) {
            this.timeRange = "00:00:00 - 23:59:59";
            this.pickerOptions.selectableRange = this.timeRange;
          } else if (valDateUnix > originDateUnix && valDateUnix == nowDateUnix) {
            this.timeRange = "00:00:00 - " + nowTime;
            this.pickerOptions.selectableRange = this.timeRange;
          }
        }
      },
      deep: true,
    },
  },
  async created() {
    await this.getDictData("com_professional_type", "processionTypeArr");//所属专业
    this.getDictData("fault_status", "faultStatusArr");//故障状态
    this.getDictData("com_pro_network_type", "networkTypeArr");//网络类型
    this.getDictData("site_offline_reason", "siteOfflineReasonArr");//退服原因
    this.getDictData("com_emergency_level", "emergencyLevelArr");//紧急程度
    this.getDictData("carrier_sharing", "carrierSharingArr");//运营商共享情况
    this.getDictData("site_service_level", "siteServiceLevelArr");//站址服务等级
    this.getDictData("if_tower_reason", "ifTowerReasonArr");//申告工单是否铁塔原因
    this.getDictData("declaration_fault_classification", "declarationFaultClassificationArr");//申告工单故障分类
    this.getDictData("if_apply_for_relief", "ifApplyForReliefArr");//是否申请减免
    this.getEvaluation();
  },
  mounted() {
    this.backSingleForm.recoveryPerson = this.userInfo.realName;
    this.backSingleForm.workItemId = this.common.workItemId;
    this.backSingleForm.woId = this.common.woId;
    this.backSingleForm.processInstId = this.common.processInstId;
    // this.backSingleForm.processDefId = this.common.processDefId;
    // this.backSingleForm.actionName = this.common.actionName;
    // this.backSingleForm.professionalType = this.common.professionalType + "";
    // this.backSingleForm.networkType = this.common.networkType;
    this.backSingleForm.suspendDuration = this.common.hangOver;
    if (this.backSingleForm.suspendDuration == null){
      this.backSingleForm.suspendDuration = 0;
    }
    this.getOrgInfo();
    this.userData = JSON.parse(this.userInfo.attr2);
    this.backSingleForm.recoveryPhone = this.userData.mobilePhone;

    this.resourceBackInit(this.backSingleForm);
    // this.setParentObjectData(this.backSingleForm);
    let array = this.backSingleForm.alarmCreateTime.split(" ");
    this.createTimeDate = array[0];
  },
  methods: {

    descTip(count,name,showName){
      if (this.backSingleForm[name].length>=count){
        this[showName] = true;
        setTimeout(() => {
          this[showName] = false;
        }, this.showTipTime);
      }
      else{
        this[showName] = false;
      }
    },
    //时间对比
    disabledDate(time) {
      let now = moment(this.createTimeDate);
      let a = moment(now).subtract(1, "days");
      let today = moment(a).valueOf();
      return time.getTime() <= today || time.getTime() - 8.64e6 >= Date.now();
    },
    addZero(s) {
      return s < 10 ? "0" + s : s;
    },
    //返单初始化查询是否已评价和专业
    getEvaluation() {
      let param = {
        woId: this.common.woId,
        processInstId: this.common.processInstId,
      };
      apiBackSingleInit(param)
        .then(res => {
          if (res.status == "0") {
            this.tjProfessionalTypeList = res?.data?.professionalTypeList;
            this.tjFaultReasonList = res?.data?.faultReasonList;
            this.tjFaultCateList = res?.data?.faultCateList;
            this.towerAcceptTime =  res?.data?.towerAcceptTime;
            this.fbInitData = res?.data?.fdbkForm;
            this.sumBtnShow = res?.data?.ifShowNextStepButton;
            this.isShowCC = res?.data?.ifPropertyLoss;
            this.isShowPD = res?.data?.isDispatchAccurate;
            this.backSingleForm.professionalTypeId = res?.data?.fdbkForm.professionalTypeId;
            this.backSingleForm.professionalType = res?.data?.fdbkForm.professionalType;
            this.backSingleForm.networkType = res?.data?.fdbkForm.networkType;
            this.backSingleForm.businessName = res?.data?.fdbkForm.businessName;
            this.backSingleForm.emergencyLevel = res?.data?.fdbkForm.emergencyLevel;
            this.backSingleForm.faultRegion = `${res?.data?.fdbkForm.mobileFaultDutyId==null?'':res?.data?.fdbkForm.mobileFaultDutyId}${res?.data?.fdbkForm.boardType==null?"":res?.data?.fdbkForm.boardType}`;

            if (this.backSingleForm.professionalType != '铁塔')
            {
              this.backSingleForm.alarmCreateTime = this.common.failureTime;
              this.backSingleForm.faultNoticeTime = this.common.failureInformTime;
              //业务恢复时间、处理结束时间默认取主告警清除时间
              this.backSingleForm.faultRecoveryTime = res?.data?.fdbkForm.faultRecoveryTime;
              this.backSingleForm.faultOverTime = res?.data?.fdbkForm.faultOverTime;
              if (this.backSingleForm.faultRecoveryTime != ''){
                this.computerFaultGenerationAter();
              }
              if (this.backSingleForm.faultOverTime != ''){
                this.computerFaultTreatmentTime();
              }
              this.backSingleForm.hardwareFlag = "";
              this.backSingleForm.maintenanceSubject = "";
              this.backSingleForm.eqpType = res?.data?.fdbkForm.eqpType;
              this.backSingleForm.eqpTypeId = res?.data?.fdbkForm.eqpTypeId;
              this.backSingleForm.vendor = res?.data?.fdbkForm.vendor;
            }
            else{
              this.backSingleForm.alarmCreateTime = '';
              this.backSingleForm.faultNoticeTime = getCurrentTime(Date.now()),
              //铁塔专业返单默认
              // 接单时间默认填充第一次受理时间，不可修改；所属地市默认填充告警地市，可修改；所属区县默认填充告警区县，可修改
              this.backSingleForm.faultRecoveryTime = res?.data?.towerAcceptTime;
              this.backSingleForm.mobileFaultDutyId = res?.data?.fdbkForm.mobileFaultDutyId;
              this.backSingleForm.boardType = res?.data?.fdbkForm.boardType;
              this.backSingleForm.hardwareFlag = "否";
              this.backSingleForm.maintenanceSubject = "否";
            }

            // let ids = this.processionTypeArr.filter(item => item.dictCode == this.backSingleForm.professionalTypeId);
            // this.backSingleForm.professionalType = ids[0].dictName;
            this.fileLinkId = res?.data?.fdbkForm.linkId;
            this.getDictData(
              this.backSingleForm.professionalTypeId + "_commonpro_fault_handle_method",
              "solvedTypeArr"
            );
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    getOrgInfo() {
      apiGetOrgInfo()
        .then(res => {
          if (res.status == "0") {
            this.backSingleForm.dept = res?.data?.orgInfo?.fullOrgName ?? "";
            this.areaCode = res?.data?.orgInfo?.areaCode ?? "";
            this.category = res?.data?.category ?? "";
            this.getFaultAreaOptions();
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    getFaultAreaOptions() {
      let param = {
        areaCode: this.areaCode,
        category: this.category,
      };
      apiGetFaultArea(param)
        .then(res => {
          if (res.status == "0") {
            this.faultRegionOptions = res?.data ?? [];
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    //可输入select
    // inputArea(e){
    //   if (e.target.value){
    //     this.backSingleForm.faultRegion = e.target.value;
    //   }
    // },

    //故障发生地区树
    onOpenFaultRegionTree(){
      this.faultRegionTreeVisible = true;
    },

    // 下拉查询
    getDictData(dictId, selectName) {


      this[selectName] = [];
      let param = {
        dictType: dictId,
      };
      apiDict(param)
        .then(res => {

          if (res.code == 200) {
            this[selectName] = res.data;
          }
        })
        .catch(error => {
          console.log(error);

          return false;
        });
    },

    //故障处理方式改变
    solveTypeChange(){
      // console.log(this.backSingleForm.solvedType);
    },
    //所属专业改变
    professionalTypeChange() {
      let ids = this.processionTypeArr.filter(item => item.dictCode == this.backSingleForm.professionalTypeId);
      this.backSingleForm.professionalType = ids[0].dictName;
      this.getDictData(
        this.backSingleForm.professionalTypeId + "_commonpro_fault_handle_method",
        "solvedTypeArr"
      );

      if (this.backSingleForm.professionalType != '铁塔')
      {
        this.backSingleForm.alarmCreateTime = this.common.failureTime;
        this.backSingleForm.faultNoticeTime = this.common.failureInformTime;

        this.backSingleForm.vendor = this.fbInitData.vendor;
        this.backSingleForm.eqpType = this.fbInitData.eqpType;
        this.backSingleForm.eqpTypeId = this.fbInitData.eqpTypeId;
        //业务恢复时间、处理结束时间默认取主告警清除时间
        this.backSingleForm.faultRecoveryTime = this.fbInitData.faultRecoveryTime;
        if (this.backSingleForm.faultRecoveryTime != ''){
          this.computerFaultGenerationAter();
        }
        this.backSingleForm.mobileFaultDutyId = '';
        this.backSingleForm.boardType = "";
        this.backSingleForm.hardwareFlag = "";
        this.backSingleForm.maintenanceSubject = "";
      }
      else{

        this.backSingleForm.alarmCreateTime = '';
        this.backSingleForm.faultNoticeTime = getCurrentTime(Date.now()),
        //铁塔专业返单默认
        // 接单时间默认填充第一次受理时间，不可修改；所属地市默认填充告警地市，可修改；所属区县默认填充告警区县，可修改
        this.backSingleForm.faultRecoveryTime = this.towerAcceptTime;
        this.backSingleForm.mobileFaultDutyId = this.fbInitData.mobileFaultDutyId;
        this.backSingleForm.boardType = this.fbInitData.boardType;
        this.backSingleForm.hardwareFlag = "否";
        this.backSingleForm.maintenanceSubject = "否";
      }

      this.backSingleForm.solvedType = "";
      this.backSingleForm.faultCate = "";
      this.backSingleForm.faultCateId = "";
      this.backSingleForm.faultReason = "";
      // this.backSingleForm.vendor = "";
      // this.backSingleForm.eqpType = "";
      // this.backSingleForm.eqpTypeId = "";
      this.backSingleForm.eqpName = "";
      this.backSingleForm.falutReasonDesc = "";
      this.backSingleForm.falutComment = "";
      this.backSingleForm.faultStatus = "";
      this.backSingleForm.networkType = (this.backSingleForm.professionalType == '传输网' || this.backSingleForm.professionalType == '集客')? this.fbInitData.networkType:"";
      this.backSingleForm.deviceName = "";
      this.backSingleForm.faultRange = "";
      this.backSingleForm.effectSystem = "";
      this.backSingleForm.effectCircuit = "";
      this.backSingleForm.bearerSystem = "";
      this.backSingleForm.isRouterChange = ""; //是否路由调整  1116
      this.backSingleForm.routerChangeJobCode = ""; //路由调整单号
      this.backSingleForm.businessName = this.backSingleForm.professionalType == '省分云'? this.fbInitData.businessName:"";
      this.backSingleForm.emergencyLevel = this.backSingleForm.professionalType == '省分云'? this.fbInitData.emergencyLevel:"";
      this.backSingleForm.siteOfflineReason = '';
      // console.log(this.backSingleForm);
    },

    //故障分类改变
    faultCateChange() {
      let ids = this.faultCateArr.filter(item => item.dictCode == this.backSingleForm.faultCateId);
      this.backSingleForm.faultCate = ids[0].dictName;
      // this.$message.error('faultCate'+this.backSingleForm.faultCate);
      this.backSingleForm.faultReason = "";
      if (this.backSingleForm.professionalType == '无线网'){
        this.backSingleForm.vendor = "";//无线网专业特殊，和专业、故障分类三级联动
      }
      if (this.backSingleForm.professionalType == '传输网'||
        this.backSingleForm.professionalType == '集客'){
        if (this.backSingleForm.faultCate == '线路故障')
        {
          this.backSingleForm.falutReasonDesc = "距   机房  km处，光缆因     （原因）中断。 处理方式：   （熔接或者倒带后）恢复";
        }
        else if (this.backSingleForm.faultCate == '设备故障')
        {
          this.backSingleForm.falutReasonDesc = "  机房    板卡的故障。处理方式：   （更换板卡或者倒带后）恢复";
        }
        else{
          this.backSingleForm.falutReasonDesc = "";
        }

      }
    },

    //设备类型改变
    eqpTypeChange() {
      let ids = this.eqpTypeArr.filter(item => item.dictName == this.backSingleForm.eqpType);
      this.backSingleForm.eqpTypeId = ids[0].dictCode;
      this.backSingleForm.boardType = "";
    },

    relatedFilesBrowse() {
      this.relatedFilesDialogVisible = true;
    },
    changeFileData(data) {
      this.backSingleForm.relatedFiles = data.fileName;
      this.importForm.relatedFilesFileList = data.attachmentFileList;
      this.relatedFilesDialogVisible = false;
    },
    closeAttachmentDialog() {
      this.relatedFilesDialogVisible = false;
    },
    //计算业务恢复历时
    computerFaultGenerationAter() {
      if (this.backSingleForm.faultRecoveryTime) {
        let days = moment(
          this.backSingleForm.faultRecoveryTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.backSingleForm.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        this.backSingleForm.recoveryDuration = days;
      } else {
        this.backSingleForm.recoveryDuration = 0;
      }
      // this.faultCateChange();
    },
    second2Time(days) {
      return this.showTime(Math.abs(days));
    },
    showTime(val) {
      if (val) {
        if (val == 0) return "0秒";
        var time = "";
        var second = val % 60;
        var minute = parseInt(parseInt(val) / 60) % 60;
        time = second + "秒";
        if (minute >= 1) {
          time = minute + "分" + second + "秒";
        }
        var hour = parseInt(parseInt(val / 60) / 60) % 24;
        if (hour >= 1) {
          time = hour + "小时" + minute + "分" + second + "秒";
        }
        var day = parseInt(parseInt(parseInt(val / 60) / 60) / 24);
        if (day >= 1) {
          time = day + "天" + hour + "小时" + minute + "分" + second + "秒";
        }

        return time;
      } else {
        return "0秒";
      }
    },
    //计算故障处理历时
    computerFaultTreatmentTime() {
      let days = moment(
        this.backSingleForm.faultOverTime,
        "YYYY-MM-DD HH:mm:ss"
      ).diff(
        moment(this.backSingleForm.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
        "seconds"
      );
      this.backSingleForm.faultDuration = days;

      //故障处理净历时  故障结束时间-故障发生时间-挂起历时（故障结束时间-故障发生时间）-挂起历时(秒) 换算成-天-小时-分钟-秒
      if (this.backSingleForm.suspendDuration == 0) {
        this.backSingleForm.faultCleanDuration = this.backSingleForm.faultDuration;
      } else {
        let seconds = moment(
          this.backSingleForm.faultOverTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.backSingleForm.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        this.backSingleForm.faultCleanDuration =
          seconds - this.backSingleForm.suspendDuration;
      }
      // this.faultCateChange();
    },
    //挂起历时
    // computerSuspendDuration() {
    //   if (this.timing.hangTime != "" && null != this.timing.hangTime) {
    //     if (
    //       this.timing.liftHangTime != "" &&
    //       null != this.timing.liftHangTime
    //     ) {
    //       let seconds = moment(
    //         this.timing.liftHangTime,
    //         "YYYY-MM-DD HH:mm:ss"
    //       ).diff(
    //         moment(this.timing.hangTime, "YYYY-MM-DD HH:mm:ss"),
    //         "seconds"
    //       );
    //       this.backSingleForm.suspendDuration = seconds;
    //     } else {
    //       this.backSingleForm.suspendDuration = 0;
    //     }
    //   } else {
    //     this.backSingleForm.suspendDuration = 0;
    //   }
    // },

    //下一步评价
    nextStepEvaluation() {

      // this.entering();
      // console.log(this.backSingleForm);
      // debugger
      this.$refs.backSingleForm.validate(valid => {
        if (valid) {
          this.evaluationDialogVisible = true;
        } else {
          return false;
        }
      });
    },

    handleSubmit(formName) {
      this.entering();
      this.$refs[formName].validate(valid => {
        if (valid) {
          let self = this;
          this.backSingleFullscreenLoading = true;
          // 如果有附件，先上传附件
          let isUpload = false;
          let formData = new FormData();
          if (self.importForm.relatedFilesFileList.length > 0) {
            isUpload = true;
            for (let item of self.importForm.relatedFilesFileList) {
              formData.append("files", item.raw);
            }
          } else {
            isUpload = false;
            formData.append("files", "");
          }

          let evaluateParam = {
            sendAccuracy: this.evaluation.orderAccuracy,
            diagnoseAccuracy: this.evaluation.diagnosticrAccuracy,
            evaluateContent: this.evaluation.viewsOnContent,
            problemClass:
              this.evaluation.feedbackProblemCheckList.length > 0
                ? this.evaluation.feedbackProblemCheckList.join(",")
                : "",
          };

          if(this.isShowCC == '否'){
            delete this.backSingleForm.ifPropertyLoss;
            delete this.backSingleForm.insuranceId;
          }
          if(this.isShowPD == '否'){
            delete this.backSingleForm.isDispatchAccurate;
          }
          this.backSingleForm.repairQuality = this.backSingleForm.repairQuality.join(',');
          let param = Object.assign(
            evaluateParam,
            this.backSingleForm
          );
          // 上传附件
          if (isUpload) {
            let uploadUrl = `/commonprovince/attach/upload?groupKey=${this.common.woId}&processId=${this.fileLinkId}&processNode=${this.common.processNode}&sheetCreateTime=${this.common.sheetCreateTime}`;
            apiFileUpload(uploadUrl, formData)
              .then(res => {
                if (res.code == 200) {
                  param.linkId = this.fileLinkId;
                  this.submit(param);
                  this.importForm.relatedFilesFileList = [];
                } else {
                  this.$message.error(res.msg);
                  // this.importForm.relatedFilesFileList = [];
                  this.backSingleFullscreenLoading = false;
                  return false;
                }
              })
              .catch(error => {
                console.log(error);
                this.backSingleFullscreenLoading = false;
              });
          }
          else
          {
            this.submit(param);
          }

        } else {
          return false;
        }
      });
    },

    submit(param){
      apiBackSingle(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success('返单成功');
            // this.onReset();
            // this.onResetEvaluation();
            this.evaluationDialogVisible = false;
            this.$emit("closeBackSingleDialog");
          } else {
            this.$message.error(res.msg);
          }
          this.backSingleFullscreenLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error(error.msg);
          this.backSingleFullscreenLoading = false;
        });
    },

    close(tag) {
      this.importForm.relatedFilesFileList.splice(
        this.importForm.relatedFilesFileList.indexOf(tag),
        1
      );
    },

    onReset() {
      this.backSingleForm = {
        // ...this.$options.data,
        // alarmCreateTime: this.common.failureTime,
        // faultNoticeTime: this.common.failureInformTime,
        woId: this.common.woId,
        workItemId: this.common.workItemId,
        processInstId: this.common.processInstId,
        processDefId: this.common.processDefId,
        recoveryPerson: this.userInfo.realName,
        dept: this.userData.orgInfo.fullOrgName,
        actionName:'返单',
        // professionalType: "3",
        // recoveryDuration: 0, //故障代通历时
        // faultDuration: 0, //故障处理净历时
        // suspendDuration: this.common.hangOver, //挂起历时 单位秒
        // faultCleanDuration: 0, //故障处理净历时 单位秒

        //非铁塔
        professionalTypeId: this.fbInitData.professionalTypeId,
        professionalType: this.fbInitData.professionalType,
        alarmCreateTime: this.fbInitData.professionalType!='铁塔'?this.common.failureTime:'',
        faultNoticeTime: this.fbInitData.professionalType!='铁塔'?this.common.failureInformTime:getCurrentTime(Date.now()),
        faultRecoveryTime: this.fbInitData.professionalType!='铁塔'?this.fbInitData.faultRecoveryTime:this.towerAcceptTime,
        recoveryDuration: 0, //业务恢复历时
        faultOverTime: null,
        faultDuration: 0, //故障处理历时
        faultRegion: `${this.fbInitData.mobileFaultDutyId==null?'':this.fbInitData.mobileFaultDutyId}${this.fbInitData.boardType==null?"":this.fbInitData.boardType}`,
        // dept: null,
        // recoveryPerson: null,
        suspendDuration: this.common.hangOver, //挂起历时 单位秒
        faultCleanDuration: 0, //故障处理净历时 单位秒
        isEffectBusiness:'是',
        effectRange: null,
        isSiteOffline: '否',
        siteOfflineReason: null,
        solvedType:null,
        faultCateId:null,
        faultCate: null,
        faultReason:null,
        vendor: this.fbInitData.professionalType!='铁塔'?this.fbInitData.vendor:null,
        //设备故障
        eqpTypeId: this.fbInitData.professionalType!='铁塔'?this.fbInitData.eqpTypeId:null,
        eqpType: this.fbInitData.professionalType!='铁塔'?this.fbInitData.eqpType:null,
        eqpName: "",
        falutReasonDesc:null,
        // falutReasonDesc:
        //   this.common.faultCauseDescription ||
        //   "样例：距xx机房xxkm处，光缆因xxx（原因）中断。 处理方式：xxxx （熔接或者倒带后）恢复。",
        falutComment: null,
        maintenanceSubject:this.fbInitData.professionalType=='铁塔'?'否':'',
        mobileFaultDutyId:this.fbInitData.professionalType!='铁塔'?'':this.fbInitData.mobileFaultDutyId,
        faultStatus: null,
        networkType: (this.backSingleForm.professionalType == '传输网' || this.backSingleForm.professionalType == '集客')? this.fbInitData.networkType:"",
        boardType:this.fbInitData.professionalType!='铁塔'?'':this.fbInitData.boardType,
        deviceName:null,
        faultRange: null,
        effectSystem: null,
        effectCircuit: null,
        bearerSystem:null,
        isRouterChange: "否", //是否路由调整  1116
        routerChangeJobCode: "", //路由调整单号
        businessName:this.backSingleForm.professionalType == '省分云'? this.fbInitData.businessName:"",
        emergencyLevel:this.backSingleForm.professionalType == '省分云'? this.fbInitData.emergencyLevel:"",
        hardwareFlag:this.fbInitData.professionalType=='铁塔'?'否':'',

        //铁塔
        // recoveryPerson:null,
        recoveryPhone:this.userData.mobilePhone,
        // faultNoticeTime:null,
        // faultReason:null,
        // auditResult:null,
        // isFaultClose:null,
        // isUpdateFaultFile:null,
        // resolvingTimeLimite:null,
        // secondFaultSourceNumber:null,
        // refreshTime:null,
        // // faultOverTime:null,
        // // faultRecoveryTime:null,
        // faultProvince:null,
        // influenceBusinessScope:null,
        repairQuality:[],
        fdbkQuality:null,
        satisfaction:'否',
        evaluationOpinion:null,
        faultSource:null,
        diskName:null,
        interSystem:null,
        // effectSystem:null,
        interCircuit:'否',
        // faultRange:null,
        files:null,
      };
      if (this.fbInitData.professionalType!='铁塔' && this.backSingleForm.faultRecoveryTime != ''){
        this.computerFaultGenerationAter();
      }

      this.organizeForm.builderZsList = [];
      this.organizeForm.builderZsListCopy = [];
      this.organizeForm.builderZsName = "";
      console.log(this.backSingleForm);
    },
    onResetEvaluation() {
      this.evaluation = {
        ...this.$options.data,
      };
    },
    //光缆名称选择
    opticalCableSelect() {
      let jsonStr =
        '{"userId":"302785","username":"yangxm97","callSysId":"0001","callSysName":"电子运维"}';
      this.$nextTick(() => {
        document.querySelector("#fiberOpticCable").value = jsonStr;
        document.querySelector("#sub__fiberOpticCable").submit();
      });
    },
    //设备名称选择
    deviceSelect() {
      let jsonStr =
        '{"userId":"302785","username":"yangxm97","callSysId":"0001","callSysName":"电子运维"}';
      this.$nextTick(() => {
        document.querySelector("#device").value = jsonStr;
        document.querySelector("#sub__device").submit();
      });
    },
    //资源系统回调方法初始化
    resourceBackInit() {
      window["__process_response_from_getDevice"] = value => {
        let form = this.backSingleForm;
        let result = JSON.parse(value);
        let objArray = JSON.parse(result?.objects);

        if (form.faultCate == "1") {
          let opticFiberArray = form.opticFiber
            ? form.opticFiber.split(",")
            : [];
          let effectSystemArray = form.effectSystem
            ? form.effectSystem.split(",")
            : [];
          let effectSystemArrayTemp = [];

          objArray.forEach(obj => {
            if (opticFiberArray.indexOf(obj.resName) < 0) {
              opticFiberArray.push(obj.resName);
            }

            effectSystemArrayTemp = JSON.parse(obj.affectedSystem);
            effectSystemArrayTemp.forEach(sys => {
              if (sys.sysName && effectSystemArray.indexOf(sys.sysName) < 0) {
                effectSystemArray.push(sys.sysName);
              }
            });
          });

          form.opticFiber = opticFiberArray.join(",");
          form.effectSystem = effectSystemArray.join(",");
          form.eqpName = "";
        } else {
          let eqpNameArray = form.eqpName ? form.eqpName.split(",") : [];

          objArray.forEach(obj => {
            if (eqpNameArray.indexOf(obj.resName) < 0) {
              eqpNameArray.push(obj.resName);
            }
          });

          form.eqpName = eqpNameArray.join(",");
          form.opticFiber = "";
          form.effectSystem = "";
        }
      };
    },
    feedbackChange(val) {
      if (val.length > 0) {
        if (val.join(",").indexOf("其他") != -1) {
          this.viewsOnContentShow = true;
        } else {
          this.viewsOnContentShow = false;
        }
      }
    },
    isRouteChange() {
      // if (this.backSingleForm.isRouteAdjust == "1") {
      //   this.isRouteTrue();
      // } else {
      //   this.backSingleForm.isRouteAdjust = "0";
      //   this.backSingleForm.routeNo = "";
      // }
    },

    onOpenPeopleDialog(diaSaveName) {
      this.isDiaOrgsUserTree = false;
      this.diaPeople.title = this.diaPeople.saveTitleMap[diaSaveName];
      this.diaPeople.saveName = diaSaveName;
      // this.diaPeople.showOrgsTree = this.diaPeople.showOrgsTreeMap[diaSaveName] ?? true;
      this.diaPeople.visible = true;
      this.$nextTick(() => {
        this.isDiaOrgsUserTree = true;
      });
    },

    onSavePeople(val) {
      this[this.diaPeople.saveName](val);
    },
    //同行处理人确定
    lordSentDetermine({ usersChecked, orgsChecked, selectionUser, contactSelectionUser, contactSelectionOrg, }) {
      // console.log(usersChecked);
      // console.log(orgsChecked);
      console.log(selectionUser);

      if (contactSelectionUser && contactSelectionUser.length > 0) {
        contactSelectionUser.map(item => {
          let transferCs = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.userName;
          });
          if (transferCs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderZsList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }

      if (selectionUser && selectionUser.length > 0) {
        selectionUser.map(item => {
          let zs = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.userName;
          });
          if (zs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderZsList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      } else if (usersChecked && usersChecked.length > 0) {
        usersChecked.map(item => {
          let zs = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.id;
          });
          if (zs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderZsList.push({
              bz: "user",
              id: item.id,
              name: item.name,
              orgName: item.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (contactSelectionOrg && contactSelectionOrg.length > 0) {
        contactSelectionOrg.map(item => {
          let transferCs = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.orgId;
          });
          if (transferCs > -1) {
            this.$message({
              message: "选择的组织已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderZsList.push({
              bz: "org",
              id: item.orgId + "",
              orgName: item.fullOrgName,
            });
          }
        });
      }

      if (orgsChecked && orgsChecked.length > 0) {
        orgsChecked.map(item => {
          let zsOrg = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.id;
          });
          if (zsOrg > -1) {
            this.$message({
              message: "选择的组织已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderZsList.push({
              bz: "org",
              id: item.id,
              orgName: item.name,
            });
          }
        });
      }

      this.organizeForm.builderZsListCopy = JSON.parse(
        JSON.stringify(this.organizeForm.builderZsList)
      );
    },
    //多选移除
    toggleSelection(val) {
      if (this.multipleSelection.length == 0) {
        this.$message({
          type: "warning",
          message: "还没有选择移除的选项，请先选择要移除的选项！",
        });
        return;
      } else {
        this.multipleSelection.forEach(row => {
          this.handleClose(val, row);
        });
      }
      // this.multipleSelection;
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    //移除对象
    handleClose(val, tag) {
      if (val == "builderZs") {
        this.organizeForm.builderZsList.splice(
          this.arrayIndex(this.organizeForm.builderZsList, tag),
          1
        );
        this.organizeForm.builderZsListCopy.splice(
          this.arrayIndex(this.organizeForm.builderZsListCopy, tag),
          1
        );
      }
    },
    //找对应对象的索引值
    arrayIndex(val1, val2) {
      for (var i = 0, len = val1.length; i < len; i++) {
        var tag = val1[i];
        if (tag.id == val2.id) {
          return i;
        }
      }
    },
    //搜索
    search(val) {
      if (val == "builderZs" && this.organizeForm.builderZsList != null) {
        this.organizeForm.builderZsListCopy = [];
        this.organizeForm.builderZsList.forEach(row => {
          if (
            row.name == this.organizeForm.builderZsName ||
            row.orgName == this.organizeForm.builderZsName
          ) {
            this.organizeForm.builderZsListCopy.push(row);
          }
        });
      }
    },
    //清除搜索项
    clear(val) {
      if (val == "builderZs") {
        this.organizeForm.builderZsName = "";
        this.organizeForm.builderZsListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.builderZsList)
        );
      }
    },
    entering() {
      this.backSingleForm.peerProcessorName = "";
      this.backSingleForm.peerProcessor = "";
      this.backSingleForm.peerProcessorInfo = "";
      if (
        this.organizeForm.builderZsList &&
        this.organizeForm.builderZsList.length > 0
      ) {
        let userList = this.organizeForm.builderZsList.filter((item, index) => {
          return item.bz == "user";
        });
        if (userList && userList.length > 0) {
          let usersCheckedName = userList.map(item => {
            return item.name;
          });
          this.backSingleForm.peerProcessorName = usersCheckedName.join(",");
          let usersCheckedId = userList.map(item => {
            return item.id;
          });
          this.backSingleForm.peerProcessor = usersCheckedId.join(",");
          const userDetailName = userList.map(item => {
            return item.orgName + "-" + item.name + "-" + item.mobilePhone;
          });
          this.backSingleForm.peerProcessorInfo = userDetailName.join(",");
        }
      }
    },

    //路由调整跳转拟稿
    // isRouteTrue() {
    //   let routeURI = window.location.protocol + "//" + window.location.host;
    //   // let routeURI = "http://************:8088"; //测试环境地址
    //   // let routeURI = "http://************"; //生产环境地址
    //   routeURI += "/EOM_LIFE_ADJUST/#/transfer/draftform";
    //   // let routeURI = "/EOM_LIFE_ADJUST/#/transfer/draftform";
    //   routeURI +=
    //     "?hideHeader=1&hideTagsNav=1&actonShow=true&routeSource=网络故障3.0";
    //   routeURI +=
    //     "&influenceCategory=一干&otherSystemNo=" +
    //     this.common.sheetNo +
    //     "&otherSystemId=" +
    //     this.common.woId +
    //     "&globalUniqueID=" +
    //     sessionStorage.getItem("globalUniqueID");
    //   // let param = {
    //   //   routeSource: "网络故障3.0",
    //   //   influenceCategory: "一干",
    //   //   otherSystemNo: this.common.sheetNo,
    //   //   otherSystemId: this.common.woId,
    //   //   globalUniqueID: sessionStorage.getItem("globalUniqueID"),
    //   // };
    //   // this.$nextTick(() => {
    //   //   document.querySelector("#sub__route").action = routeURI;
    //   //   document.querySelector("#route").val = JSON.stringify(param);
    //   //   document.querySelector("#sub__route").submit();
    //   // });
    //   window.open(routeURI);
    // },
    //路由跳转回调方法
    // setParentObjectData() {
    //   window["_setParentObjectData"] = value => {
    //     let form = this.backSingleForm;
    //     if (value.success) {
    //       //"success": true,
    //       // "jobTitle": "中国联通总部-中国联通总部-传输",
    //       // "processInstID": "1182565",
    //       // "jobCode": "CSLYTZAPP-20221019-0000"
    //       form.routeProcessInstId = value.processInstID;
    //       form.routeNo = value.jobCode;
    //       form.routeOrderTitle = value.jobTitle;
    //     }
    //   };
    // },
    // toRoutePage() {
    //   let url = window.location.protocol + "//" + window.location.host;
    //   // let url = "http://************:8088"; //测试环境地址
    //   // let url = "http://************"; //生产环境地址
    //   if (this.backSingleForm.routeProcessInstId != null) {
    //     url +=
    //       "/ibpm-ui/#/contentTo?content=Y&needi=Y&no=detail&hideHeader=1&reqFrom=handle&processInstId=" +
    //       this.backSingleForm.routeProcessInstId +
    //       "&jobCode=" +
    //       this.backSingleForm.routeNo +
    //       "&globalUniqueID=" +
    //       sessionStorage.getItem("globalUniqueID");
    //   } else {
    //     url +=
    //       "/EOM_LIFE_UI/#/transfer/draftform?hideHeader=1&hideTagsNav=1&actonShow=false&jobcode=" +
    //       this.backSingleForm.routeNo;
    //   }
    //   window.open(url);
    // },
  },
};
</script>
<style lang="scss" scoped>

  .tttttcla{
    /*color: yellowgreen !important;*/
  }

  .back-single {
    max-height: calc(90vh - 150px);
    min-height: 400px;
    margin: 0 4px;
    padding-left: 4px;
    padding-right: 8px;
    overflow-y: auto;
    .cus-card ::v-deep .el-card__header {
      padding: 11px 24px 10px;
      font-weight: 400;
      @include themify {
        background-color: themed("$--background-color-base");
      }
    }
    .fileName_style {
      margin-right: 3px;
      vertical-align: middle;
      div {
        display: inline-block;
        max-width: 120px;
        vertical-align: top;
      }
    }
  }
</style>
