<template>
  <div>
    <el-form
      ref="form"
      :model="form"
      label-width="100px"
      class="audit-form"
      :rules="rules"
    >
      <el-form-item label="工单准确性" prop="woAccuracy">
        <el-rate
          v-model="form.woAccuracy"
          show-text
          :texts="descriText"
        ></el-rate>
      </el-form-item>
      <el-form-item label="派单及时性" prop="disOnTime">
        <el-rate
          v-model="form.disOnTime"
          show-text
          :texts="descriText"
        ></el-rate>
      </el-form-item>
      <el-form-item label="中台评价" prop="ztEvaluation">
        <el-rate
          v-model="form.ztEvaluation"
          show-text
          :texts="descriText"
        ></el-rate>
      </el-form-item>
      <!-- <el-form-item
        label="评价意见"
        prop="opinion"
        :rules="[
          {
            required:
              form.woAccuracy < 3 || form.disOnTime < 3 || form.ztEvaluation < 3
                ? true
                : '',
            message: '请输入内容',
          },
        ]"
      > -->
      <el-form-item label="评价意见" prop="opinion">
        <el-input
          type="textarea"
          :rows="2"
          placeholder="请填写评价意见"
          v-model="form.opinion"
          style="width: 100%"
        >
        </el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: right">
      <el-button
        type="primary"
        @click="handleAuditSubmit('form')"
        v-loading.fullscreen.lock="fullScreenLoading"
        >提 交</el-button
      >
      <el-button @click="onResetAudit">重 置</el-button>
    </div>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import { apiActionPublic, getCurrentTime } from "../api/CommonApi";
export default {
  name: "Audit",
  props: {
    common: Object,
    actionName: String,
  },
  data() {
    return {
      form: {
        auditOpinion: "",
        processSuggestion: null,
        woAccuracy: 5,
        disOnTime: 5,
        ztEvaluation: 5,
      },
      fullScreenLoading: false,
      descriText: ["很不满意", "不满意", "一般", "很满意", "非常满意"],
      rules: {
        woAccuracy: [{ required: true, message: "请选择" }],
        disOnTime: [{ required: true, message: "请选择" }],
        ztEvaluation: [{ required: true, message: "请选择" }],
      },
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  mounted() {
    console.log(this.common.professionalTypeName);
  },
  methods: {
    handleAuditSubmit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.fullScreenLoading = true;
          let param = {
            woId: this.common.woId,
            workItemId: this.common.workItemId,
            processInstId: this.common.processInstId,
            actionName: this.actionName,
            auditUser: this.userInfo.userName,
            auditTime: getCurrentTime(Date.now()),
            woAccuracy: this.form.woAccuracy,
            disOnTime: this.form.disOnTime,
            ztEvaluation: this.form.ztEvaluation,
            opinion: this.form.opinion,
          };
          apiActionPublic(param)
            .then(res => {
              if (res.status == "0") {
                this.$message.success("派单评价成功");
                this.$emit("closeDialogOrderEvaluate");
              } else {
                this.$message.error(res.msg);
              }
              this.fullScreenLoading = false;
            })
            .catch(error => {
              console.log(error);
              this.fullScreenLoading = false;
              this.$message.error(error.msg);
            });
        } else {
          return false;
        }
      });
    },
    onResetAudit() {
      this.form = {
        ...this.$options.data,
      };
    },
  },
};
</script>
<style scoped>
.custom-theme-default .el-rate {
  padding-top: 5px;
}
</style>
