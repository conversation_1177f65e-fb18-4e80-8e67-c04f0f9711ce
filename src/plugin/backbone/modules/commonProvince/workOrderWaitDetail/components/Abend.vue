<template>
  <div>
    <el-form ref="abendForm" :model="abendForm" label-width="120px">
      <el-form-item
        label="异常终止原因:"
        prop="opResult"
        :rules="{
          required: true,
          message: '请选择异常终止原因',
        }"
      >
        <dict-select
          :value.sync="abendForm.opResult"
          :dictId="10049"
          placeholder="请选择异常终止原因"
          style="width: 250px"
        />
      </el-form-item>
      <el-form-item label="异常终止说明:" prop="processSuggestion">
        <el-input
          type="textarea"
          :rows="2"
          placeholder="请填写异常终止说明"
          v-model="abendForm.processSuggestion"
          style="width: 250px"
          show-word-limit
          maxlength="255"
          @keyup.native="descTip(255,'processSuggestion','showTip')"
        >
        </el-input>
        <div class="el-form-item__error"  v-if="showTip">已超过填写字数上限</div>
      </el-form-item>
    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: right">
      <el-button
        type="primary"
        @click="handleAbendSubmit('abendForm')"
        v-loading.fullscreen.lock="abendFullScreenLoading"
        >提 交</el-button
      >
      <el-button @click="onResetAbend">重 置</el-button>
    </div>
  </div>
</template>
<script>
import { apiAbend, apiItCloudAbend } from "../api/CommonApi";
import DictSelect from "../../../workOrder/components/DictSelect.vue";

export default {
  name: "Abend",
  props: {
    common: Object,
  },
  components: { DictSelect },
  data() {
    return {
      showTip:false,
      showTime:5000,
      abendForm: {
        opResult: null,
        processSuggestion: null,
      },
      abendFullScreenLoading: false,
    };
  },
  mounted() {},
  methods: {
    descTip(count,name,showName){
      if (this.abendForm[name].length>=count){
        this[showName] = true;
        setTimeout(() => {
          this[showName] = false;
        }, this.showTime);
      }
      else{
        this[showName] = false;
      }
    },
    handleAbendSubmit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.abendFullScreenLoading = true;
          let param = {
            woId: this.common.woId,
            actionName:'异常终止',
            processInstId: this.common.processInstId,
            processDefId: this.common.processDefId,
            workItemId: this.common.workItemId,
            processNode: this.common.processNode,
            abortApplyReason: this.abendForm.opResult,
            abortApplyExplain: this.abendForm.processSuggestion,
          };
          apiAbend(param)
            .then(res => {
              if (res.status == "0") {
                this.$message.success(res.msg);
                this.onResetAbend();
                this.$emit("closeDialogAbend");
              } else {
                this.$message.error(res.msg);
              }
              this.abendFullScreenLoading = false;
            })
            .catch(error => {
              console.log(error);
              this.abendFullScreenLoading = false;
              this.$message.error(error.msg);
            });
        } else {
          return false;
        }
      });
    },
    onResetAbend() {
      this.abendForm = {
        ...this.$options.data,
      };
    },
  },
};
</script>
