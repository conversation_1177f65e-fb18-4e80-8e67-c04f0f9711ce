<template>
  <div class="stage-feedback">
    <el-form ref="stageBackForm" :model="stageBackForm" label-width="90px">
      <el-form-item
        label="反馈内容:"
        prop="stageContent"
        :rules="{
          required: true,
          message: '请填写反馈内容',
        }"
      >
        <el-input
          type="textarea"
          :rows="3"
          placeholder="请输入内容"
          v-model="stageBackForm.stageContent"
          style="width: 100%"
          show-word-limit
          maxlength="900"
          @keyup.native="descTip(900,'stageContent','showTip')"
        >
        </el-input>
        <div class="el-form-item__error"  v-if="showTip">已超过填写字数上限</div>
      </el-form-item>
      <el-form-item label="处理过程:">
        <el-select
          v-model="stageBackForm.stageType"
          placeholder="请选择内容"
          style="width: 100%"
        >
          <el-option
            v-for="(item, i) in opList"
            :key="i"
            :label="item.dictName"
            :value="item.dictCode"
          >
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: right">
      <el-button
        type="primary"
        @click="handleSubmit('stageBackForm')"
        v-loading.fullscreen.lock="stageBackFullscreenLoading"
      >提交</el-button
      >
      <el-button @click="onResetStageBackForm">重 置</el-button>
    </div>
  </div>
</template>
<script>
  import { apiDict, apiStageFeedBack } from "../api/CommonApi";
  import DictSelect from "../../../workOrder/components/DictSelect.vue";
  export default {
    props: {
      common: Object,
    },
    name: "StageFeedback",
    components: { DictSelect },
    data() {
      return {
        showTip:false,
        showTime:5000,
        stageBackForm: {
          woId: "",
          processInstId: "",
          processDefId: "",
          workItemId: "",
          processNode: "",
          stageContent: "",
          stageType: "",
          actionName:'阶段反馈'
        },
        stageBackFullscreenLoading: false,
        opList:[],
      };
    },
    mounted() {
      this.getDictData();
      this.stageBackForm.woId = this.common.woId;
      this.stageBackForm.processInstId = this.common.processInstId;
      this.stageBackForm.processDefId = this.common.processDefId;
      this.stageBackForm.workItemId = this.common.workItemId;
      this.stageBackForm.processNode = this.common.processNode;
    },
    methods: {
      descTip(count,name,showName){
        if (this.stageBackForm[name].length>=count){
          this[showName] = true;
          setTimeout(() => {
            this[showName] = false;
          }, this.showTime);
        }
        else{
          this[showName] = false;
        }
      },
      // 下拉查询
      getDictData() {

        let param = {
          dictType: 'feedback_process',
        };
        apiDict(param)
          .then(res => {
            if (res.code == 200) {
              this.opList = res.data;
            }
          })
          .catch(error => {
            console.log(error);
            return false;
          });
      },
      handleSubmit(formName) {
        this.$refs[formName].validate(valid => {
          if (valid) {
            this.stageBackFullscreenLoading = true;
            apiStageFeedBack(this.stageBackForm)
              .then(res => {
                if (res.status == "0") {
                  this.$message.success(res.msg);
                  this.onResetStageBackForm();
                  this.$emit("stageBackDialogClose");
                } else {
                  this.$message.error(res.msg);
                }
                this.stageBackFullscreenLoading = false;
              })
              .catch(error => {
                console.log(error);
                this.$message.error(error.msg);
                this.stageBackFullscreenLoading = false;
              });
          } else {
            return false;
          }
        });
      },
      onResetStageBackForm() {
        this.stageBackForm = {
          // ...this.$options.data,
          woId: this.common.woId,
          workItemId: this.common.workItemId,
          processInstId: this.common.processInstId,
          processDefId: this.common.processDefId,
          processNode: this.common.processNode,
          stageContent: "",
          stageType: "",
          actionName:'阶段反馈'
        };
      },
    },
  };
</script>
