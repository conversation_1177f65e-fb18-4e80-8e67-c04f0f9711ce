<template>
  <div>
    <el-form
      ref="auditForm"
      :model="auditForm"
      label-width="80px"
      class="audit-form"
      :rules="rules"
    >
      <el-form-item label="审核结果" prop="auditResult">
        <el-radio-group v-model="auditForm.auditResult" style="width: 260px">
          <el-radio label="Y">同意</el-radio>
          <el-radio label="N">拒绝</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="审核意见" prop="auditOpinion">
        <el-input
          maxlength="500"
          type="textarea"
          :rows="2"
          placeholder="请填写审核意见"
          v-model="auditForm.auditOpinion"
          style="width: 100%"
        >
        </el-input>
      </el-form-item>
      <template v-if="auditForm.auditResult == 'Y'">
        <h4>包机人评定：</h4>
        <el-form-item label="修障质量" prop="repairQuality">
          <el-rate
            v-model="auditForm.repairQuality"
            show-text
            :texts="descriText"
          ></el-rate>
        </el-form-item>
        <el-form-item label="反馈质量" prop="fdbkQuality">
          <el-rate
            v-model="auditForm.fdbkQuality"
            show-text
            :texts="descriText"
          ></el-rate>
        </el-form-item>
        <el-form-item label="满意度" prop="satisfaction">
          <el-rate
            v-model="auditForm.satisfaction"
            show-text
            :texts="descriText"
          ></el-rate>
        </el-form-item>

        <el-form-item label="评价意见" prop="evaluationOpinion">
          <el-input
            maxlength="500"
            type="textarea"
            :rows="2"
            placeholder="请填写评价意见"
            v-model="auditForm.evaluationOpinion"
            style="width: 100%"
          >
          </el-input>
        </el-form-item>
      </template>
    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: right">
      <el-button
        type="primary"
        @click="handleAuditSubmit('auditForm')"
        v-loading.fullscreen.lock="auditFullScreenLoading"
        >提 交</el-button
      >
      <el-button @click="onResetAudit">重 置</el-button>
    </div>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import { apiActionPublic, getCurrentTime } from "../api/CommonApi";
export default {
  name: "Audit",
  props: {
    common: Object,
    actionName: String,
  },
  data() {
    return {
      auditForm: {
        auditOpinion: "",
        repairQuality: 5,
        fdbkQuality: 5,
        satisfaction: 5,
        evaluationOpinion: "",
      },
      auditFullScreenLoading: false,
      descriText: ["很不满意", "不满意", "一般", "很满意", "非常满意"],
      rules: {
        auditResult: [{ required: true, message: "请选择审核结果" }],
        auditOpinion: [
          {
            required: true,
            message: "请输入审核意见",
            trigger: ["blur"],
          },
        ],
        repairQuality: [{ required: true, message: "请打分" }],
        fdbkQuality: [{ required: true, message: "请打分" }],
        satisfaction: [{ required: true, message: "请打分" }],
      },
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  mounted() {
    console.log(this.common.professionalTypeName);
  },
  watch: {
    "auditForm.auditResult": {
      handler: function (val) {
        if (val == "Y") {
          this.auditForm.auditOpinion = "同意";
        } else {
          this.auditForm.auditOpinion = "";
        }
      },
    },
  },
  methods: {
    handleAuditSubmit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.auditFullScreenLoading = true;
          let paramter = {
            woId: this.common.woId,
            workItemId: this.common.workItemId,
            processInstId: this.common.processInstId,
            actionName: this.actionName,
            auditUser: this.userInfo.userName,
            auditTime: getCurrentTime(Date.now()),
          };
          if (this.auditForm.auditResult == "N") {
            delete this.auditForm.repairQuality;
            delete this.auditForm.fdbkQuality;
            delete this.auditForm.satisfaction;
            delete this.auditForm.evaluationOpinion;
          }
          let param = Object.assign(paramter, this.auditForm);
          apiActionPublic(param)
            .then(res => {
              if (res.status == "0") {
                this.$message.success("审核提交成功");
                this.$emit("closeDialogQualitativeAudit");
              } else {
                this.$message.error(res.msg);
              }
              this.auditFullScreenLoading = false;
            })
            .catch(error => {
              console.log(error);
              this.auditFullScreenLoading = false;
              this.$message.error(error.msg);
            });
        } else {
          return false;
        }
      });
    },
    onResetAudit() {
      this.auditForm = {
        ...this.$options.data,
      };
    },
  },
};
</script>
<style scoped>
.custom-theme-default .el-rate {
  padding-top: 5px;
}
</style>
