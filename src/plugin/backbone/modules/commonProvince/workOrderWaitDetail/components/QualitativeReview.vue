<template>
  <div class="qualitative-review">
    <el-form
      ref="qualitativeReviewForm"
      :inline="false"
      class="demo-form-inline"
      :model="qualitativeReviewForm"
      label-width="140px"
    >
      <el-card
        shadow="never"
        header="故障定性信息"
        :body-style="{ padding: '20px 8px' }"
        style="margin-top: 20px"
        class="cus-card"
      >
        <el-descriptions class="descriptions">
          <template v-if="list.professionalTypeName == '铁塔'">
            <el-descriptions-item label="故障所属专业">{{
              list.professionalTypeName
            }}</el-descriptions-item>
            <el-descriptions-item label="回单人">{{
              list.recoveryPerson
            }}</el-descriptions-item>
            <el-descriptions-item label="回单人联系电话">{{
              list.recoveryPhone
            }}</el-descriptions-item>
            <el-descriptions-item label="回单时间">{{
              list.faultNoticeTime
            }}</el-descriptions-item>
            <el-descriptions-item label="实时直流电压值">{{
              list.siteOfflineReason
            }}</el-descriptions-item>
            <el-descriptions-item label="铁塔站址资源编码">{{
              list.faultReason
            }}</el-descriptions-item>
            <el-descriptions-item label="站址运维ID">{{
              list.vendor
            }}</el-descriptions-item>
            <el-descriptions-item label="站址名称">{{
              list.eqpType
            }}</el-descriptions-item>
            <el-descriptions-item label="运营商是否购买发电服务">
              {{ list.hardwareFlag }}
            </el-descriptions-item>
            <el-descriptions-item label="是否是发电工单">{{
              list.maintenanceSubject
            }}</el-descriptions-item>
            <el-descriptions-item label="发电开始时间">{{
              list.alarmCreateTime
            }}</el-descriptions-item>
            <el-descriptions-item label="发电结束时间">{{
              list.faultOverTime
            }}</el-descriptions-item>
            <el-descriptions-item label="接单时间">{{
              list.faultRecoveryTime
            }}</el-descriptions-item>
            <el-descriptions-item label="所属地市">{{
              list.mobileFaultDutyId
            }}</el-descriptions-item>
            <el-descriptions-item label="所属区县">{{
              list.boardType
            }}</el-descriptions-item>
            <el-descriptions-item label="运营商共享情况">{{
              list.repairQuality
            }}</el-descriptions-item>
            <el-descriptions-item label="站址服务等级">{{
              list.fdbkQuality
            }}</el-descriptions-item>
            <el-descriptions-item label="是否为免责站址">{{
              list.satisfaction
            }}</el-descriptions-item>
            <el-descriptions-item label="申告工单是否铁塔原因">{{
              list.evaluationOpinion
            }}</el-descriptions-item>
            <el-descriptions-item label="申告工单故障分类">{{
              list.faultSource
            }}</el-descriptions-item>
            <el-descriptions-item label="是否申请减免">{{
              list.diskName
            }}</el-descriptions-item>
            <el-descriptions-item
              label="申请减免原因"
              v-if="list.diskName != '不需减免'"
              >{{ list.interSystem }}</el-descriptions-item
            >
            <el-descriptions-item
              label="申请减免分钟数"
              v-if="list.diskName != '不需减免'"
              >{{ list.effectSystem }}</el-descriptions-item
            >
            <el-descriptions-item label="是否上站">{{
              list.interCircuit
            }}</el-descriptions-item>
            <el-descriptions-item label="同行处理人">{{
              list.peerProcessorName
            }}</el-descriptions-item>
            <el-descriptions-item label="处理结果描述">{{
              list.faultRange
            }}</el-descriptions-item>
            <el-descriptions-item label="附件">
              <el-tag
                v-for="(item, index) in list.files"
                class="fileName_style"
                :key="index"
                @click="handleDownload(item.attId)"
                :title="item.attOrigName"
                ><div class="text-truncate">{{ item.attOrigName }}</div></el-tag
              >
            </el-descriptions-item>
          </template>
          <template v-else>
            <el-descriptions-item label="故障所属专业">{{
              list.professionalTypeName
            }}</el-descriptions-item>
            <el-descriptions-item label="故障发生时间">{{
              list.alarmCreateTime
            }}</el-descriptions-item>
            <el-descriptions-item label="故障通知时间">{{
              list.faultNoticeTime
            }}</el-descriptions-item>
            <el-descriptions-item label="业务恢复时间">{{
              list.businessRecoveryTime
            }}</el-descriptions-item>
            <el-descriptions-item label="业务恢复历时">{{
              recoveryDuration
            }}</el-descriptions-item>
            <el-descriptions-item label="故障结束时间">{{
              list.faultOverTime
            }}</el-descriptions-item>
            <el-descriptions-item label="故障处理历时">{{
              faultDuration
            }}</el-descriptions-item>
            <el-descriptions-item label="故障发生地区">
              {{ list.faultRegion }}
            </el-descriptions-item>
            <el-descriptions-item label="故障处理部门">
              {{ list.dept }}
            </el-descriptions-item>
            <el-descriptions-item label="处理人">{{
              list.recoveryPerson
            }}</el-descriptions-item>
            <el-descriptions-item label="挂起历时">{{
              suspendDuration
            }}</el-descriptions-item>
            <el-descriptions-item label="故障处理净历时">{{
              faultCleanDuration
            }}</el-descriptions-item>

            <el-descriptions-item label="是否影响业务">{{
              list.isEffectBusiness
            }}</el-descriptions-item>
            <el-descriptions-item
              label="影响范围"
            >
              {{ list.effectRange }}
            </el-descriptions-item>
            <el-descriptions-item label="是否基站退服"  v-if="list.professionalTypeName == '无线网'">{{
              list.isSiteOffline
            }}</el-descriptions-item>
            <el-descriptions-item
              label="退服原因"
              v-if="list.professionalTypeName == '无线网' && list.isSiteOffline == '是'"
              >{{ list.siteOfflineReason }}</el-descriptions-item
            >
            <el-descriptions-item label="同行处理人">{{
              list.peerProcessorName
            }}</el-descriptions-item>
            <el-descriptions-item
              label="是否有财产损失"
              v-if="
                list.ifPropertyLoss != null &&
                list.ifPropertyLoss != '' &&
                list.ifPropertyLoss != '无'
              "
              >{{ list.ifPropertyLoss }}</el-descriptions-item
            >
            <el-descriptions-item
              label="保单号"
              v-if="list.ifPropertyLoss == '是'"
              >{{ list.insuranceId }}</el-descriptions-item
            >
            <el-descriptions-item
              label="派单是否准确"
              v-if="
                list.isDispatchAccurate != null &&
                list.isDispatchAccurate != '' &&
                list.isDispatchAccurate != '无'
              "
              >{{ list.isDispatchAccurate }}</el-descriptions-item
            >
          </template>
        </el-descriptions>
      </el-card>
      <el-card
        shadow="never"
        header="故障专业信息"
        :body-style="{ padding: '20px 8px' }"
        style="margin-top: 20px"
        v-if="list.professionalTypeName != '铁塔'"
        class="cus-card"
      >
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item label="故障处理方式:" prop="solvedType">
              {{ list.solvedType }}
            </el-form-item>
          </el-col>
        </el-row>

        <!--当故障处理方式=自动恢复，不显示以下字段。-->
        <template v-if="list.solvedType != '自动恢复'">
          <!--5GC/核心网/固网/移动数通网/数据网/IP承载A网/IP承载B网/WLAN/动环网/IP互联网/核心网其他-->
          <template
            v-if="
              list.professionalTypeName == 'IDC' ||
              list.professionalTypeName == 'vIMS' ||
              list.professionalTypeName == '核心网' ||
              list.professionalTypeName == '核心网其他' ||
              list.professionalTypeName == '智能网' ||
              list.professionalTypeName == '5GC' ||
              list.professionalTypeName == '固网' ||
              list.professionalTypeName == '移动数通网' ||
              list.professionalTypeName == '数据网' ||
              list.professionalTypeName == 'IP互联网' ||
              list.professionalTypeName == 'IP承载A网' ||
              list.professionalTypeName == 'IP承载B网' ||
              list.professionalTypeName == 'WLAN' ||
              list.professionalTypeName == '动环网'
            "
          >
            <el-row type="flex" style="flex-wrap: wrap" :gutter="20" key="11">
              <el-col :span="8">
                <el-form-item
                  label="故障分类:"
                  prop="faultCateId"
                  :rules="{
                    required: true,
                    message: '请选择故障分类',
                  }"
                >
                  <el-select
                    v-model="qualitativeReviewForm.faultCateId"
                    placeholder="请选择"
                    style="width: 100%"
                    @change="faultCateChange"
                  >
                    <el-option
                      v-for="(item, i) in faultCateArr"
                      :key="i"
                      :label="item.dictName"
                      :value="item.dictCode"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label="故障原因:"
                  :rules="{ required: true, message: '请选择故障原因' }"
                  prop="faultReason"
                >
                  <el-select
                    v-model="qualitativeReviewForm.faultReason"
                    placeholder="请选择"
                    style="width: 100%"
                    @focus="
                      getDictData(
                        list.professionalTypeId +
                          '_' +
                          qualitativeReviewForm.faultCateId +
                          '_commonpro_faultReason',
                        'faultReasonArr'
                      )
                    "
                  >
                    <el-option
                      v-for="(item, i) in faultReasonArr"
                      :key="i"
                      :label="item.dictName"
                      :value="item.dictName"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label="故障厂家:"
                  prop="vendor"
                  :rules="{
                    required: true,
                    message: '请选择故障厂家',
                  }"
                >
                  <el-select
                    v-model="qualitativeReviewForm.vendor"
                    placeholder="请选择"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="(item, i) in vendorOptions"
                      :key="i"
                      :label="item.dictName"
                      :value="item.dictName"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label="设备类型:"
                  :rules="{ required: true, message: '请选择设备类型' }"
                  prop="eqpType"
                >
                  <el-select
                    v-model="qualitativeReviewForm.eqpType"
                    placeholder="请选择"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="(item, i) in eqpTypeArr"
                      :key="i"
                      :label="item.dictName"
                      :value="item.dictName"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="设备名称:">

                  <UniversalPopupWindowEquipmentOrLines :currentSearchQueryType="3" :isComProvince=true :woId="common.woId"
                                                        @changeCableEquipInputData="changeCableEquipInputData" :cableEquipInputData="qualitativeReviewForm.eqpName"
                                                        @event-select-data="eventEquipLineSelectData" :title="'设备'" />
<!--                  <el-input-->
<!--                    placeholder="请输入"-->
<!--                    v-model="qualitativeReviewForm.eqpName"-->
<!--                    style="width: 100%"-->
<!--                    maxlength="255"-->
<!--                    @keyup.native="descTip(255, 'eqpName', 'showsbmc')"-->
<!--                  >-->
<!--                    <el-button-->
<!--                      style="-->
<!--                        background: #b50b14;-->
<!--                        border-color: #b50b14;-->
<!--                        color: #fff;-->
<!--                        padding: 9px 20px;-->
<!--                        top: -0.5px;-->
<!--                        position: relative;-->
<!--                      "-->
<!--                      slot="append"-->
<!--                      @click="deviceSelect"-->
<!--                      >选择</el-button-->
<!--                    >-->
<!--                  </el-input>-->
<!--                  <div class="el-form-item__error" v-if="showsbmc">-->
<!--                    已超过填写字数上限-->
<!--                  </div>-->
                  <form
                    id="sub__device"
                    name="sub__device"
                    hidden="true"
                    method="post"
                    action="/resweb_jituan/union/resAssign.out?method=selectEquipment"
                    target="_blank"
                  >
                    <input type="hidden" name="device" id="device" />
                  </form>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item
                  label="故障原因描述:"
                  prop="falutReasonDesc"
                  :rules="{
                    required: true,
                    message: '请填写故障原因描述',
                  }"
                >
                  <el-input
                    show-word-limit
                    maxlength="255"
                    @keyup.native="
                      descTip(255, 'falutReasonDesc', 'showgzyyms')
                    "
                    type="textarea"
                    :rows="2"
                    v-model="qualitativeReviewForm.falutReasonDesc"
                    style="width: 100%"
                  >
                  </el-input>
                  <div class="el-form-item__error" v-if="showgzyyms">
                    已超过填写字数上限
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="备注:">
                  <el-input
                    type="textarea"
                    :rows="2"
                    placeholder="请输入"
                    v-model="qualitativeReviewForm.falutComment"
                    style="width: 100%"
                    show-word-limit
                    maxlength="255"
                    @keyup.native="descTip(255, 'falutComment', 'showbz')"
                  >
                  </el-input>
                  <div class="el-form-item__error" v-if="showbz">
                    已超过填写字数上限
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </template>
          <template v-else-if="list.professionalTypeName == '无线网'">
            <el-row type="flex" style="flex-wrap: wrap" :gutter="20" key="22">
              <el-col :span="8">
                <el-form-item
                  label="故障分类:"
                  prop="faultCateId"
                  :rules="{
                    required: true,
                    message: '请选择故障分类',
                  }"
                >
                  <el-select
                    v-model="qualitativeReviewForm.faultCateId"
                    placeholder="请选择"
                    style="width: 100%"
                    @change="faultCateChange"
                  >
                    <el-option
                      v-for="(item, i) in faultCateArr"
                      :key="i"
                      :label="item.dictName"
                      :value="item.dictCode"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label="故障原因:"
                  :rules="{ required: true, message: '请选择故障原因' }"
                  prop="faultReason"
                >
                  <el-select
                    v-model="qualitativeReviewForm.faultReason"
                    placeholder="请选择"
                    style="width: 100%"
                    @focus="
                      getDictData(
                        list.professionalTypeId +
                          '_' +
                          qualitativeReviewForm.faultCateId +
                          '_commonpro_faultReason',
                        'faultReasonArr'
                      )
                    "
                  >
                    <el-option
                      v-for="(item, i) in faultReasonArr"
                      :key="i"
                      :label="item.dictName"
                      :value="item.dictName"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col
                :span="8"
                v-if="
                  qualitativeReviewForm.faultCate == '传输系统' ||
                  qualitativeReviewForm.faultCate == '基站设备'
                "
              >
                <el-form-item
                  label="故障厂家:"
                  prop="vendor"
                  key="gzcj"
                  :rules="{
                    required:
                      qualitativeReviewForm.faultCate == '传输系统' ||
                      qualitativeReviewForm.faultCate == '基站设备'
                        ? true
                        : false,
                    message: '请选择故障厂家',
                  }"
                >
                  <el-select
                    v-model="qualitativeReviewForm.vendor"
                    placeholder="请选择"
                    style="width: 100%"
                    @focus="
                      getDictData(
                        list.professionalTypeId +
                          '_' +
                          qualitativeReviewForm.faultCateId +
                          '_commonpro_vendor',
                        'vendorOptions'
                      )
                    "
                  >
                    <el-option
                      v-for="(item, i) in vendorOptions"
                      :key="i"
                      :label="item.dictName"
                      :value="item.dictName"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label="设备类型:"
                  :rules="{ required: true, message: '请选择设备类型' }"
                  prop="eqpType"
                >
                  <el-select
                    v-model="qualitativeReviewForm.eqpType"
                    placeholder="请选择"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="(item, i) in eqpTypeArr"
                      :key="i"
                      :label="item.dictName"
                      :value="item.dictName"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="设备名称:">

                  <UniversalPopupWindowEquipmentOrLines :currentSearchQueryType="3" :isComProvince=true :woId="common.woId"
                                                        @changeCableEquipInputData="changeCableEquipInputData" :cableEquipInputData="qualitativeReviewForm.eqpName"
                                                        @event-select-data="eventEquipLineSelectData" :title="'设备'" />
<!--                  <el-input-->
<!--                    placeholder="请输入"-->
<!--                    v-model="qualitativeReviewForm.eqpName"-->
<!--                    style="width: 100%"-->
<!--                    maxlength="255"-->
<!--                    @keyup.native="descTip(255, 'eqpName', 'showsbmc')"-->
<!--                  >-->
<!--                    <el-button-->
<!--                      style="-->
<!--                        background: #b50b14;-->
<!--                        border-color: #b50b14;-->
<!--                        color: #fff;-->
<!--                        padding: 9px 20px;-->
<!--                        top: -0.5px;-->
<!--                        position: relative;-->
<!--                      "-->
<!--                      slot="append"-->
<!--                      @click="deviceSelect"-->
<!--                      >选择</el-button-->
<!--                    >-->
<!--                  </el-input>-->
<!--                  <div class="el-form-item__error" v-if="showsbmc">-->
<!--                    已超过填写字数上限-->
<!--                  </div>-->
                  <form
                    id="sub__device"
                    name="sub__device"
                    hidden="true"
                    method="post"
                    action="/resweb_jituan/union/resAssign.out?method=selectEquipment"
                    target="_blank"
                  >
                    <input type="hidden" name="device" id="device" />
                  </form>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label="维护主体:"
                  :rules="{ required: true, message: '请选择维护主体' }"
                  prop="maintenanceSubject"
                >
                  <el-select
                    v-model="qualitativeReviewForm.maintenanceSubject"
                    placeholder="请选择"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="(item, i) in maintenanceSubjectArr"
                      :key="i"
                      :label="item.dictName"
                      :value="item.dictName"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label="故障定责:"
                  prop="mobileFaultDutyId"
                  :rules="{ required: true, message: '请选择故障定责' }"
                >
                  <el-select
                    v-model="qualitativeReviewForm.mobileFaultDutyId"
                    placeholder="请选择"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="(item, i) in mobileFaultDutyArr"
                      :key="i"
                      :label="item.dictName"
                      :value="item.dictName"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item
                  label="故障原因描述:"
                  prop="falutReasonDesc"
                  :rules="{
                    required: true,
                    message: '请填写故障原因描述',
                  }"
                >
                  <el-input
                    show-word-limit
                    maxlength="255"
                    @keyup.native="
                      descTip(255, 'falutReasonDesc', 'showgzyyms')
                    "
                    type="textarea"
                    :rows="2"
                    v-model="qualitativeReviewForm.falutReasonDesc"
                    style="width: 100%"
                  >
                  </el-input>
                  <div class="el-form-item__error" v-if="showgzyyms">
                    已超过填写字数上限
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="备注:">
                  <el-input
                    show-word-limit
                    maxlength="255"
                    @keyup.native="descTip(255, 'falutComment', 'showbz')"
                    type="textarea"
                    :rows="2"
                    placeholder="请输入"
                    v-model="qualitativeReviewForm.falutComment"
                    style="width: 100%"
                  >
                  </el-input>
                  <div class="el-form-item__error" v-if="showbz">
                    已超过填写字数上限
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </template>
          <template
            v-else-if="
              list.professionalTypeName == '传输网' ||
              list.professionalTypeName == '集客'
            "
          >
            <el-row type="flex" style="flex-wrap: wrap" :gutter="20" key="33">
              <el-col :span="8">
                <el-form-item
                  label="故障状态:"
                  prop="faultStatus"
                  :rules="{
                    required: true,
                    message: '请选择故障状态',
                  }"
                >
                  <el-select
                    v-model="qualitativeReviewForm.faultStatus"
                    placeholder="请选择内容"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="(item, i) in faultStatusArr"
                      :key="i"
                      :label="item.dictName"
                      :value="item.dictName"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label="网络类型:"
                  prop="networkType"
                  :rules="{ required: true, message: '请选择网络类型' }"
                >
                  <el-select
                    v-model="qualitativeReviewForm.networkType"
                    placeholder="请选择"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="(item, i) in networkTypeArr"
                      :key="i"
                      :label="item.dictName"
                      :value="item.dictName"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label="故障分类:"
                  prop="faultCateId"
                  :rules="{
                    required: true,
                    message: '请选择故障分类',
                  }"
                >
                  <el-select
                    v-model="qualitativeReviewForm.faultCateId"
                    placeholder="请选择"
                    style="width: 100%"
                    @change="faultCateChange"
                  >
                    <el-option
                      v-for="(item, i) in faultCateArr"
                      :key="i"
                      :label="item.dictName"
                      :value="item.dictCode"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col
                :span="8"
                v-if="
                  qualitativeReviewForm.faultCate == '设备故障' ||
                  qualitativeReviewForm.faultCate == '网管故障' ||
                  qualitativeReviewForm.faultCate == '动力配套故障' ||
                  qualitativeReviewForm.faultCate == '其他'
                "
              >
                <el-form-item
                  label="设备类型:"
                  key="101"
                  prop="eqpTypeId"
                  :rules="{
                    required: true,
                    message: '请选择设备类型',
                  }"
                >
                  <el-select
                    v-model="qualitativeReviewForm.eqpTypeId"
                    placeholder="请选择"
                    style="width: 100%"
                    @change="eqpTypeChange"
                  >
                    <el-option
                      v-for="(item, i) in eqpTypeArr"
                      :key="i"
                      :label="item.dictName"
                      :value="item.dictCode"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label="故障原因:"
                  :rules="{ required: true, message: '请选择内容' }"
                  prop="faultReason"
                >
                  <el-select
                    v-model="qualitativeReviewForm.faultReason"
                    placeholder="请选择"
                    style="width: 100%"
                    @focus="
                      getDictData(
                        list.professionalTypeId +
                          '_' +
                          qualitativeReviewForm.faultCateId +
                          '_commonpro_faultReason',
                        'faultReasonArr'
                      )
                    "
                  >
                    <el-option
                      v-for="(item, i) in faultReasonArr"
                      :key="i"
                      :label="item.dictName"
                      :value="item.dictName"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col
                :span="8"
                v-if="qualitativeReviewForm.faultCate != '线路故障'"
              >
                <el-form-item
                  label="板卡类型:"
                  prop="boardType"
                  key="111"
                  :rules="{
                    required:
                      qualitativeReviewForm.faultCate != '线路故障'
                        ? true
                        : false,
                    message: '请选择板卡类型',
                  }"
                >
                  <el-select
                    v-model="qualitativeReviewForm.boardType"
                    placeholder="请选择"
                    style="width: 100%"
                    @focus="
                      getDictData(
                        list.professionalTypeId +
                          '_' +
                          qualitativeReviewForm.eqpTypeId +
                          '_commonpro_boardType',
                        'boardTypeArr'
                      )
                    "
                  >
                    <el-option
                      v-for="(item, i) in boardTypeArr"
                      :key="i"
                      :label="item.dictName"
                      :value="item.dictName"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col
                :span="8"
                v-if="qualitativeReviewForm.faultCate == '线路故障'"
              >
                <el-form-item label="光缆名称:" key="122" prop="deviceName">

                  <UniversalPopupWindowEquipmentOrLines :currentSearchQueryType="2" :isComProvince=true :woId="common.woId"
                                                        @changeCableEquipInputData="changeCableEquipInputData" :cableEquipInputData="qualitativeReviewForm.deviceName"
                                                        @event-select-data="eventEquipLineSelectData" :title="'光缆'" />
<!--                  <el-input-->
<!--                    v-model="qualitativeReviewForm.deviceName"-->
<!--                    style="width: 100%"-->
<!--                  >-->
<!--                    <el-button-->
<!--                      style="-->
<!--                        background: #b50b14;-->
<!--                        border-color: #b50b14;-->
<!--                        color: #fff;-->
<!--                        padding: 9px 20px;-->
<!--                        top: -0.5px;-->
<!--                        position: relative;-->
<!--                      "-->
<!--                      slot="append"-->
<!--                      @click="opticalCableSelect"-->
<!--                      >选择</el-button-->
<!--                    ></el-input-->
<!--                  >-->
                  <form
                    id="sub__fiberOpticCable"
                    name="sub__fiberOpticCable"
                    hidden="true"
                    method="post"
                    action="/resweb_jituan/union/resAssign.out?method=selectFiberSection&requestJson={}"
                    target="_blank"
                  >
                    <input
                      type="hidden"
                      name="fiberOpticCable"
                      id="fiberOpticCable"
                    />
                  </form>
                </el-form-item>
              </el-col>
              <el-col
                :span="8"
                v-if="
                  qualitativeReviewForm.faultCate == '设备故障' ||
                  qualitativeReviewForm.faultCate == '网管故障' ||
                  qualitativeReviewForm.faultCate == '动力配套故障' ||
                  qualitativeReviewForm.faultCate == '其他'
                "
              >
                <el-form-item label="设备名称:" key="102">

                  <UniversalPopupWindowEquipmentOrLines :currentSearchQueryType="3" :isComProvince=true :woId="common.woId"
                                                        @changeCableEquipInputData="changeCableEquipInputData" :cableEquipInputData="qualitativeReviewForm.eqpName"
                                                        @event-select-data="eventEquipLineSelectData" :title="'设备'" />
<!--                  <el-input-->
<!--                    placeholder="请输入"-->
<!--                    v-model="qualitativeReviewForm.eqpName"-->
<!--                    style="width: 100%"-->
<!--                    maxlength="255"-->
<!--                    @keyup.native="descTip(255, 'eqpName', 'showsbmc')"-->
<!--                  >-->
<!--                    <el-button-->
<!--                      style="-->
<!--                        background: #b50b14;-->
<!--                        border-color: #b50b14;-->
<!--                        color: #fff;-->
<!--                        padding: 9px 20px;-->
<!--                        top: -0.5px;-->
<!--                        position: relative;-->
<!--                      "-->
<!--                      slot="append"-->
<!--                      @click="deviceSelect"-->
<!--                      >选择</el-button-->
<!--                    >-->
<!--                  </el-input>-->
<!--                  <div class="el-form-item__error" v-if="showsbmc">-->
<!--                    已超过填写字数上限-->
<!--                  </div>-->
                  <form
                    id="sub__device"
                    name="sub__device"
                    hidden="true"
                    method="post"
                    action="/resweb_jituan/union/resAssign.out?method=selectEquipment"
                    target="_blank"
                  >
                    <input type="hidden" name="device" id="device" />
                  </form>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label="故障区间:"
                  :rules="{
                    required:
                      qualitativeReviewForm.faultCate == '线路故障'
                        ? true
                        : false,
                    message: '请输入故障区间',
                  }"
                  prop="faultRange"
                >
                  <el-input
                    placeholder="请输入"
                    v-model="qualitativeReviewForm.faultRange"
                    style="width: 100%"
                    maxlength="255"
                    @keyup.native="descTip(255, 'faultRange', 'showgzqj')"
                  >
                  </el-input>
                  <div class="el-form-item__error" v-if="showgzqj">
                    已超过填写字数上限
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label="维护主体:"
                  :rules="{
                    required:
                      qualitativeReviewForm.faultCate == '线路故障'
                        ? true
                        : false,
                    message: '请选择维护主体',
                  }"
                  prop="maintenanceSubject"
                >
                  <el-select
                    v-model="qualitativeReviewForm.maintenanceSubject"
                    placeholder="请选择"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="(item, i) in maintenanceSubjectArr"
                      :key="i"
                      :label="item.dictName"
                      :value="item.dictName"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="受影响系统:">
                  <el-input
                    placeholder="请输入"
                    v-model="qualitativeReviewForm.effectSystem"
                    style="width: 100%"
                    maxlength="255"
                    @keyup.native="descTip(255, 'effectSystem', 'showsyxxt')"
                  >
                  </el-input>
                  <div class="el-form-item__error" v-if="showsyxxt">
                    已超过填写字数上限
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="受影响电路:">
                  <el-input
                    placeholder="请输入"
                    v-model="qualitativeReviewForm.effectCircuit"
                    style="width: 100%"
                    maxlength="255"
                    @keyup.native="descTip(255, 'effectCircuit', 'showsyxdl')"
                  >
                  </el-input>
                  <div class="el-form-item__error" v-if="showsyxdl">
                    已超过填写字数上限
                  </div>
                </el-form-item>
              </el-col>
              <el-col
                :span="8"
                v-if="
                  qualitativeReviewForm.faultCate == '设备故障' ||
                  qualitativeReviewForm.faultCate == '网管故障' ||
                  qualitativeReviewForm.faultCate == '动力配套故障' ||
                  qualitativeReviewForm.faultCate == '其他'
                "
              >
                <el-form-item
                  label="承载业务系统:"
                  prop="bearerSystem"
                  key="103"
                  :rules="{
                    required: true,
                    message: '承载业务系统不能为空',
                  }"
                >
                  <el-input
                    placeholder="请输入"
                    v-model="qualitativeReviewForm.bearerSystem"
                    style="width: 100%"
                    maxlength="255"
                    @keyup.native="descTip(255, 'bearerSystem', 'showczywxt')"
                  >
                  </el-input>
                  <div class="el-form-item__error" v-if="showczywxt">
                    已超过填写字数上限
                  </div>
                </el-form-item>
              </el-col>
              <el-col
                :span="24"
                v-if="
                  qualitativeReviewForm.faultCate == '设备故障' ||
                  qualitativeReviewForm.faultCate == '网管故障' ||
                  qualitativeReviewForm.faultCate == '动力配套故障' ||
                  qualitativeReviewForm.faultCate == '其他'
                "
              >
                <el-form-item
                  label="故障厂家:"
                  key="104"
                  prop="vendor"
                  :rules="{
                    required: true,
                    message: '请选择故障厂家',
                  }"
                >
                  <el-select
                    v-model="qualitativeReviewForm.vendor"
                    placeholder="请选择"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="(item, i) in vendorOptions"
                      :key="i"
                      :label="item.dictName"
                      :value="item.dictName"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item
                  label="故障原因描述:"
                  prop="falutReasonDesc"
                  :rules="{
                    required: true,
                    message: '请填写故障原因描述',
                  }"
                >
                  <el-input
                    show-word-limit
                    maxlength="255"
                    @keyup.native="
                      descTip(255, 'falutReasonDesc', 'showgzyyms')
                    "
                    type="textarea"
                    :rows="2"
                    v-model="qualitativeReviewForm.falutReasonDesc"
                    style="width: 100%"
                  >
                  </el-input>
                  <div class="el-form-item__error" v-if="showgzyyms">
                    已超过填写字数上限
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="备注:">
                  <el-input
                    show-word-limit
                    maxlength="255"
                    @keyup.native="descTip(255, 'falutComment', 'showbz')"
                    type="textarea"
                    :rows="2"
                    placeholder="请输入"
                    v-model="qualitativeReviewForm.falutComment"
                    style="width: 100%"
                  >
                  </el-input>
                  <div class="el-form-item__error" v-if="showbz">
                    已超过填写字数上限
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="附件:">
                  <el-input
                    v-model="importForm.relatedFilesFileList"
                    placeholder="添加附件"
                    readonly
                    style="width: 100%"
                  >
                    <el-button
                      type="info"
                      slot="append"
                      @click="relatedFilesBrowse"
                      >+</el-button
                    >
                    <el-button
                      v-if="attachFiles.length > 0"
                      type="info"
                      slot="append"
                      @click="ngAttachmentDownloadVisible = true"
                      title="查看上传的附件"
                      >查看</el-button
                    >
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="是否路由调整:" prop="isRouterChange">
                  <el-radio-group
                    v-model="qualitativeReviewForm.isRouterChange"
                  >
                    <el-radio label="否">否</el-radio>
                    <el-radio label="是">是</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col
                :span="16"
                v-if="qualitativeReviewForm.isRouterChange == '是'"
              >
                <el-form-item
                  label="路由工单编号:"
                  key="105"
                  :rules="{
                    required:
                      qualitativeReviewForm.isRouterChange == '是'
                        ? true
                        : false,
                    message: '请输入',
                  }"
                  prop="routerChangeJobCode"
                >
                  <el-input
                    placeholder="请输入"
                    v-model="qualitativeReviewForm.routerChangeJobCode"
                    style="width: 100%"
                    maxlength="255"
                    @keyup.native="
                      descTip(255, 'routerChangeJobCode', 'showlygdbh')
                    "
                  >
                  </el-input>
                  <div class="el-form-item__error" v-if="showlygdbh">
                    已超过填写字数上限
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </template>
          <template v-else-if="list.professionalTypeName == '接入网'">
            <el-row type="flex" style="flex-wrap: wrap" :gutter="20" key="44">
              <el-col :span="8">
                <el-form-item
                  label="故障分类:"
                  prop="faultCate"
                  :rules="{
                    required: true,
                    message: '请选择故障分类',
                  }"
                >
                  <el-select
                    v-model="qualitativeReviewForm.faultCateId"
                    placeholder="请选择"
                    style="width: 100%"
                    @change="faultCateChange"
                  >
                    <el-option
                      v-for="(item, i) in faultCateArr"
                      :key="i"
                      :label="item.dictName"
                      :value="item.dictCode"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label="故障原因:"
                  :rules="{
                    required: true,
                    message: '请选择故障原因',
                  }"
                  prop="faultReason"
                >
                  <el-select
                    v-model="qualitativeReviewForm.faultReason"
                    placeholder="请选择"
                    style="width: 100%"
                    @focus="
                      getDictData(
                        list.professionalTypeId +
                          '_' +
                          qualitativeReviewForm.faultCateId +
                          '_commonpro_faultReason',
                        'faultReasonArr'
                      )
                    "
                  >
                    <el-option
                      v-for="(item, i) in faultReasonArr"
                      :key="i"
                      :label="item.dictName"
                      :value="item.dictName"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label="故障厂家:"
                  prop="vendor"
                  :rules="{
                    required: true,
                    message: '请选择故障厂家',
                  }"
                >
                  <el-select
                    v-model="qualitativeReviewForm.vendor"
                    placeholder="请选择"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="(item, i) in vendorOptions"
                      :key="i"
                      :label="item.dictName"
                      :value="item.dictName"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label="设备类型:"
                  :rules="{
                    required: true,
                    message: '请选择故障原因',
                  }"
                  prop="eqpType"
                >
                  <el-select
                    v-model="qualitativeReviewForm.eqpType"
                    placeholder="请选择"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="(item, i) in eqpTypeArr"
                      :key="i"
                      :label="item.dictName"
                      :value="item.dictName"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="设备名称:">

                  <UniversalPopupWindowEquipmentOrLines :currentSearchQueryType="3" :isComProvince=true :woId="common.woId"
                                                        @changeCableEquipInputData="changeCableEquipInputData" :cableEquipInputData="qualitativeReviewForm.eqpName"
                                                        @event-select-data="eventEquipLineSelectData" :title="'设备'" />
<!--                  <el-input-->
<!--                    placeholder="请输入"-->
<!--                    v-model="qualitativeReviewForm.eqpName"-->
<!--                    style="width: 100%"-->
<!--                    maxlength="255"-->
<!--                    @keyup.native="descTip(255, 'eqpName', 'showsbmc')"-->
<!--                  >-->
<!--                    <el-button-->
<!--                      style="-->
<!--                        background: #b50b14;-->
<!--                        border-color: #b50b14;-->
<!--                        color: #fff;-->
<!--                        padding: 9px 20px;-->
<!--                        top: -0.5px;-->
<!--                        position: relative;-->
<!--                      "-->
<!--                      slot="append"-->
<!--                      @click="deviceSelect"-->
<!--                      >选择</el-button-->
<!--                    >-->
<!--                  </el-input>-->
<!--                  <div class="el-form-item__error" v-if="showsbmc">-->
<!--                    已超过填写字数上限-->
<!--                  </div>-->
                  <form
                    id="sub__device"
                    name="sub__device"
                    hidden="true"
                    method="post"
                    action="/resweb_jituan/union/resAssign.out?method=selectEquipment"
                    target="_blank"
                  >
                    <input type="hidden" name="device" id="device" />
                  </form>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label="故障定责:"
                  prop="mobileFaultDutyId"
                  :rules="{
                    required: true,
                    message: '请选择故障定责',
                  }"
                >
                  <el-select
                    v-model="qualitativeReviewForm.mobileFaultDutyId"
                    placeholder="请选择"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="(item, i) in mobileFaultDutyArr"
                      :key="i"
                      :label="item.dictName"
                      :value="item.dictName"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item
                  label="故障原因描述:"
                  prop="falutReasonDesc"
                  :rules="{
                    required: true,
                    message: '请填写故障原因描述',
                  }"
                >
                  <el-input
                    show-word-limit
                    maxlength="255"
                    @keyup.native="
                      descTip(255, 'falutReasonDesc', 'showgzyyms')
                    "
                    type="textarea"
                    :rows="2"
                    v-model="qualitativeReviewForm.falutReasonDesc"
                    style="width: 100%"
                  >
                  </el-input>
                  <div class="el-form-item__error" v-if="showgzyyms">
                    已超过填写字数上限
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="备注:">
                  <el-input
                    show-word-limit
                    maxlength="255"
                    @keyup.native="descTip(255, 'falutComment', 'showbz')"
                    type="textarea"
                    :rows="2"
                    placeholder="请输入"
                    v-model="qualitativeReviewForm.falutComment"
                    style="width: 100%"
                  >
                  </el-input>
                  <div class="el-form-item__error" v-if="showbz">
                    已超过填写字数上限
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </template>
          <template
            v-else-if="
              list.professionalTypeName == '增值平台' ||
              list.professionalTypeName == '其他' ||
              list.professionalTypeName == 'IT监控'
            "
          >
            <el-row type="flex" style="flex-wrap: wrap" :gutter="20" key="55">
              <el-col :span="8">
                <el-form-item
                  label="故障分类:"
                  prop="faultCate"
                  :rules="{
                    required: true,
                    message: '请选择故障分类',
                  }"
                >
                  <el-select
                    v-model="qualitativeReviewForm.faultCateId"
                    placeholder="请选择"
                    style="width: 100%"
                    @change="faultCateChange"
                  >
                    <el-option
                      v-for="(item, i) in faultCateArr"
                      :key="i"
                      :label="item.dictName"
                      :value="item.dictCode"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label="故障原因:"
                  :rules="{
                    required: true,
                    message: '请选择故障原因',
                  }"
                  prop="faultReason"
                >
                  <el-select
                    v-model="qualitativeReviewForm.faultReason"
                    placeholder="请选择"
                    style="width: 100%"
                    @focus="
                      getDictData(
                        list.professionalTypeId +
                          '_' +
                          qualitativeReviewForm.faultCateId +
                          '_commonpro_faultReason',
                        'faultReasonArr'
                      )
                    "
                  >
                    <el-option
                      v-for="(item, i) in faultReasonArr"
                      :key="i"
                      :label="item.dictName"
                      :value="item.dictName"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item
                  label="故障原因描述:"
                  prop="falutReasonDesc"
                  :rules="{
                    required: true,
                    message: '请填写故障原因描述',
                  }"
                >
                  <el-input
                    show-word-limit
                    maxlength="255"
                    @keyup.native="
                      descTip(255, 'falutReasonDesc', 'showgzyyms')
                    "
                    type="textarea"
                    :rows="2"
                    v-model="qualitativeReviewForm.falutReasonDesc"
                    style="width: 100%"
                  >
                  </el-input>
                  <div class="el-form-item__error" v-if="showgzyyms">
                    已超过填写字数上限
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="备注:">
                  <el-input
                    show-word-limit
                    maxlength="255"
                    @keyup.native="descTip(255, 'falutComment', 'showbz')"
                    type="textarea"
                    :rows="2"
                    placeholder="请输入"
                    v-model="qualitativeReviewForm.falutComment"
                    style="width: 100%"
                  >
                  </el-input>
                  <div class="el-form-item__error" v-if="showbz">
                    已超过填写字数上限
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </template>
          <template v-else-if="list.professionalTypeName == '省分云'">
            <el-row type="flex" style="flex-wrap: wrap" :gutter="20" key="66">
              <el-col :span="8">
                <el-form-item label="业务名称:" prop="businessName">
                  <el-input
                    v-model="qualitativeReviewForm.businessName"
                    style="width: 100%"
                    placeholder="请输入"
                    maxlength="255"
                    @keyup.native="descTip(255, 'businessName', 'showywmc')"
                  ></el-input>
                  <div class="el-form-item__error" v-if="showywmc">
                    已超过填写字数上限
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label="紧急程度:"
                  prop="emergencyLevel"
                  :rules="{
                    required: true,
                    message: '请选择',
                  }"
                >
                  <el-select
                    v-model="qualitativeReviewForm.emergencyLevel"
                    placeholder="请选择"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="(item, i) in emergencyLevelArr"
                      :key="i"
                      :label="item.dictName"
                      :value="item.dictName"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label="故障分类:"
                  prop="faultCate"
                  :rules="{
                    required: true,
                    message: '请选择故障分类',
                  }"
                >
                  <el-select
                    v-model="qualitativeReviewForm.faultCateId"
                    placeholder="请选择"
                    style="width: 100%"
                    @change="faultCateChange"
                  >
                    <el-option
                      v-for="(item, i) in faultCateArr"
                      :key="i"
                      :label="item.dictName"
                      :value="item.dictCode"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label="故障原因:"
                  :rules="{
                    required: true,
                    message: '请选择故障原因',
                  }"
                  prop="faultReason"
                >
                  <el-select
                    v-model="qualitativeReviewForm.faultReason"
                    placeholder="请选择"
                    style="width: 100%"
                    @focus="
                      getDictData(
                        list.professionalTypeId +
                          '_' +
                          qualitativeReviewForm.faultCateId +
                          '_commonpro_faultReason',
                        'faultReasonArr'
                      )
                    "
                  >
                    <el-option
                      v-for="(item, i) in faultReasonArr"
                      :key="i"
                      :label="item.dictName"
                      :value="item.dictName"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label="设备类型:"
                  :rules="{
                    required: true,
                    message: '请选择故障原因',
                  }"
                  prop="eqpType"
                >
                  <el-select
                    v-model="qualitativeReviewForm.eqpType"
                    placeholder="请选择"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="(item, i) in eqpTypeArr"
                      :key="i"
                      :label="item.dictName"
                      :value="item.dictName"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label="是否硬件故障:"
                  prop="hardwareFlag"
                  :rules="{
                    required: true,
                    message: '请选择',
                  }"
                >
                  <el-radio-group v-model="qualitativeReviewForm.hardwareFlag">
                    <el-radio label="否">否</el-radio>
                    <el-radio label="是">是</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="设备名称:">

                  <UniversalPopupWindowEquipmentOrLines :currentSearchQueryType="3" :isComProvince=true :woId="common.woId"
                                                        @changeCableEquipInputData="changeCableEquipInputData" :cableEquipInputData="qualitativeReviewForm.eqpName"
                                                        @event-select-data="eventEquipLineSelectData" :title="'设备'" />
<!--                  <el-input-->
<!--                    placeholder="请输入"-->
<!--                    v-model="qualitativeReviewForm.eqpName"-->
<!--                    style="width: 100%"-->
<!--                    maxlength="255"-->
<!--                    @keyup.native="descTip(255, 'eqpName', 'showsbmc')"-->
<!--                  >-->
<!--                    <el-button-->
<!--                      style="-->
<!--                        background: #b50b14;-->
<!--                        border-color: #b50b14;-->
<!--                        color: #fff;-->
<!--                        padding: 9px 20px;-->
<!--                        top: -0.5px;-->
<!--                        position: relative;-->
<!--                      "-->
<!--                      slot="append"-->
<!--                      @click="deviceSelect"-->
<!--                      >选择</el-button-->
<!--                    >-->
<!--                  </el-input>-->
<!--                  <div class="el-form-item__error" v-if="showsbmc">-->
<!--                    已超过填写字数上限-->
<!--                  </div>-->
                  <form
                    id="sub__device"
                    name="sub__device"
                    hidden="true"
                    method="post"
                    action="/resweb_jituan/union/resAssign.out?method=selectEquipment"
                    target="_blank"
                  >
                    <input type="hidden" name="device" id="device" />
                  </form>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item
                  label="故障原因描述:"
                  prop="falutReasonDesc"
                  :rules="{
                    required: true,
                    message: '请填写故障原因描述',
                  }"
                >
                  <el-input
                    show-word-limit
                    maxlength="255"
                    @keyup.native="
                      descTip(255, 'falutReasonDesc', 'showgzyyms')
                    "
                    type="textarea"
                    :rows="2"
                    v-model="qualitativeReviewForm.falutReasonDesc"
                    style="width: 100%"
                  >
                  </el-input>
                  <div class="el-form-item__error" v-if="showgzyyms">
                    已超过填写字数上限
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="备注:">
                  <el-input
                    show-word-limit
                    maxlength="255"
                    @keyup.native="descTip(255, 'falutComment', 'showbz')"
                    type="textarea"
                    :rows="2"
                    placeholder="请输入"
                    v-model="qualitativeReviewForm.falutComment"
                    style="width: 100%"
                  >
                  </el-input>
                  <div class="el-form-item__error" v-if="showbz">
                    已超过填写字数上限
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </template>

          <!--            附件-->
          <template
            v-if="
              list.professionalTypeName != '传输网' &&
              list.professionalTypeName != '集客'
            "
          >
            <el-row type="flex" style="flex-wrap: wrap" :gutter="20" key="66">
              <el-col :span="8">
                <el-form-item label="附件:">
                  <el-input
                    v-model="qualitativeReviewForm.relatedFiles"
                    placeholder="添加附件"
                    readonly
                    style="width: 100%"
                  >
                    <el-button
                      type="info"
                      slot="append"
                      @click="relatedFilesBrowse"
                      >+</el-button
                    >
                    <el-button
                      v-if="attachFiles.length > 0"
                      type="info"
                      slot="append"
                      @click="ngAttachmentDownloadVisible = true"
                      title="查看上传的附件"
                      >查看</el-button
                    >
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </template>
        </template>
        <template v-if="list.solvedType == '自动恢复'">
          <el-row type="flex" style="flex-wrap: wrap" :gutter="20" key="22">
            <el-col :span="24">
              <el-form-item
                label="故障原因描述:"
                prop="falutReasonDesc"
              >
                <el-input
                  show-word-limit
                  maxlength="255"
                  @keyup.native="
                      descTip(255, 'falutReasonDesc', 'showgzyyms')
                    "
                  type="textarea"
                  :rows="2"
                  v-model="qualitativeReviewForm.falutReasonDesc"
                  style="width: 100%"
                >
                </el-input>
                <div class="el-form-item__error" v-if="showgzyyms">
                  已超过填写字数上限
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </template>
      </el-card>
      <el-card
        shadow="never"
        header="定性审核信息"
        :body-style="{ padding: '20px 8px' }"
        style="margin-top: 20px"
        class="cus-card"
      >
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item
              label="审批结果:"
              prop="auditResult"
              :rules="{
                required: true,
                message: '请选择审批结果',
              }"
            >
              <el-radio-group v-model="qualitativeReviewForm.auditResult">
                <el-radio label="同意">同意</el-radio>
                <el-radio label="拒绝">拒绝</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="是否上传故障报告:"
              :rules="{
                required: true,
                message: '请选择是否上传故障报告',
              }"
              prop="isUpdateFaultFile"
            >
              <el-radio-group v-model="qualitativeReviewForm.isUpdateFaultFile">
                <el-radio label="否">否</el-radio>
                <el-radio label="是">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="是否故障处理结束:"
              :rules="{
                required: true,
                message: '请选择是否故障处理结束',
              }"
              prop="isFaultClose"
            >
              <el-radio-group v-model="qualitativeReviewForm.isFaultClose">
                <el-radio label="否">否</el-radio>
                <el-radio label="是">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col
            :span="16"
            v-if="
              list.professionalTypeName == '传输网' ||
              list.professionalTypeName == '集客'
            "
          >
            <el-form-item
              label="处理时限:"
              :rules="{
                required:
                  list.professionalTypeName == '传输网' ||
                  list.professionalTypeName == '集客'
                    ? true
                    : false,
                message: '请选择处理时限',
              }"
            >
              <el-radio-group
                v-model="qualitativeReviewForm.resolvingTimeLimite"
                style="width: 100%"
              >
                <el-radio
                  v-for="(item, i) in processTimeLimitOptions"
                  :key="i"
                  :label="item.id"
                  >{{ item.name }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col
            :span="8"
            v-if="
              list.professionalTypeName == '传输网' ||
              list.professionalTypeName == '集客'
            "
          >
            <el-form-item label="第二故障源流水号:">
              <el-input
                style="width: 100%"
                v-model="qualitativeReviewForm.secondFaultSourceNumber"
                placeholder="请输入内容"
                maxlength="255"
                @keyup.native="
                  descTip(255, 'secondFaultSourceNumber', 'showdegzylsh')
                "
              ></el-input>
              <div class="el-form-item__error" v-if="showdegzylsh">
                已超过填写字数上限
              </div>
            </el-form-item>
          </el-col>
          <el-col
            :span="16"
            v-if="
              list.professionalTypeName == '传输网' ||
              list.professionalTypeName == '集客'
            "
          >
            <el-form-item label="业务影响范围:">
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="qualitativeReviewForm.influenceBusinessScope"
                style="width: 100%"
                maxlength="255"
                @keyup.native="
                  descTip(255, 'influenceBusinessScope', 'showywyxfw')
                "
              >
              </el-input>
              <div class="el-form-item__error" v-if="showywyxfw">
                已超过填写字数上限
              </div>
            </el-form-item>
          </el-col>
          <el-col
            :span="8"
            v-if="
              list.professionalTypeName == '传输网' ||
              list.professionalTypeName == '集客'
            "
          >
            <el-form-item label="故障所在省:">
              <el-select
                style="width: 100%"
                placeholder="请选择内容"
                v-model="qualitativeReviewForm.faultProvince"
                @change="$forceUpdate()"
                filterable
              >
                <el-option
                  v-for="(item, i) in provinceOption"
                  :key="i"
                  :label="item.name"
                  :value="item.name"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              label="审批意见:"
              :rules="{
                required: true,
                message: '请填写审批意见',
              }"
              prop="auditOpinion"
            >
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="qualitativeReviewForm.auditOpinion"
                style="width: 100%"
                show-word-limit
                maxlength="255"
                @keyup.native="descTip(255, 'auditOpinion', 'showspyj')"
              >
              </el-input>
              <div class="el-form-item__error" v-if="showspyj">
                已超过填写字数上限
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: center">
      <el-button
        type="primary"
        @click="handleSubmit('qualitativeReviewForm')"
        v-loading.fullscreen.lock="qualitativeReviewFullscreenLoading"
        >提交</el-button
      >
      <el-button @click="onReset">重 置</el-button>
    </div>

    <el-dialog
      width="420px"
      title="附件选择"
      :visible.sync="relatedFilesDialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <file-upload
        @change="changeFileData"
        @cancel="closeAttachmentDialog"
      ></file-upload>
    </el-dialog>
    <el-dialog
      width="500px"
      title="文件列表"
      :visible.sync="ngAttachmentDownloadVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <file-download
        @cancel="ngAttachmentDownloadVisible = false"
        :attachmentArr="attachFiles"
      ></file-download>
    </el-dialog>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import {
  apiActionPublic,
  apiGetProvinceDict,
  apiFileDownload,
  apiFileUpload,
  apiqueryFeedback,
} from "../api/CommonApi";
import { apiDict } from "../../../workOrder/api/CommonApi";
import FileUpload from "./FileUpload.vue";
import FileDownload from "../../../workOrder/components/FileDownload";
import UniversalPopupWindowEquipmentOrLines from '../../../workOrder/workOrderWaitDetail/components/universalPopupWindowEquipmentOrLines'

export default {
  name: "QualitativeReview",
  props: {
    common: Object,
    workItemId: [String, Number],
    index: Object,
  },
  components: { FileUpload, FileDownload, UniversalPopupWindowEquipmentOrLines },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  watch: {
    workItemId(val) {
      this.qualitativeReviewForm.workItemId = val;
    },
  },
  data() {
    return {
      list: {},
      showczywxt: false,
      showlygdbh: false,
      showywmc: false,
      showsbmc: false,
      showgzyyms: false,
      showbz: false,
      showdegzylsh: false,
      showywyxfw: false,
      showspyj: false,
      showgzqj: false,
      showsyxxt: false,
      showsyxdl: false,
      showTipTime: 5000,
      qualitativeReviewForm: {
        woId: null,
        workItemId: null,
        processInstId: null,
        feedbackFormProcessId: null,
        processDefId: null,

        processNode: null,
        //审核信息
        auditResult: "同意", //审批结果
        isUpdateFaultFile: "否", //是否上传故障报告
        isFaultClose: "是", //是否故障处理结束
        resolvingTimeLimite: "5", //处理时限
        secondFaultSourceNumber: null, //第二故障源流水号
        influenceBusinessScope: null, //业务影响范围
        faultProvince: null,
        auditOpinion: null, //审批意见
        linkId: null,
        // appendix: null,
        //
        // lastClearTime: null,
        // alarmCreateTime: null,
        // isEffectBusiness: null,
        // effectRange: null,
        relatedFiles: null,
        //定性专业信息
        faultCateId: null,
        faultCate: null,
        faultReason: null,
        vendor: null,
        //设备故障
        eqpTypeId: null,
        eqpType: null,
        eqpName: "",
        falutReasonDesc: null,
        falutComment: null,
        maintenanceSubject: null,
        mobileFaultDutyId: null,
        faultStatus: null,
        networkType: null,
        boardType: null,
        deviceName: null,
        faultRange: null,
        effectSystem: null,
        effectCircuit: null,
        bearerSystem: null,
        isRouterChange: "否", //是否路由调整  1116
        routerChangeJobCode: "", //路由调整单号
        businessName: null,
        emergencyLevel: null,
        hardwareFlag: null,
        ifPropertyLoss: "否", //是否有财产损失
        insuranceId: "", //保单号
        isDispatchAccurate: "是", //派单是否准确
      },
      //关联附件
      relatedFilesDialogVisible: false,
      importForm: {
        relatedFilesFileList: [],
      },
      attachFiles: [],
      ngAttachmentDownloadVisible: false, //附件展示列表
      vendorOptions: [],
      processTimeLimitOptions: [
        { id: "1", name: "1小时" },
        { id: "3", name: "3小时" },
        { id: "4", name: "4小时" },
        { id: "5", name: "5小时" },
      ], //处理时限
      qualitativeReviewFullscreenLoading: false,
      // fddxFileXlArr: [], //线路 附件
      // fddxFileDlArr: [], //电路 附件
      // fddxFileVisible: false,
      // faultReasonDictId: 0, //故障原因 多字典ID
      // cardTypeDictId: 0, //板卡类型 多字典ID
      provinceOption: [],
      // nowProvince: null, //当前省份

      recoveryDuration: null, //业务恢复历时
      faultDuration: null, //故障处理历时
      suspendDuration: null, //挂起历时
      faultCleanDuration: null, //故障处理净历时

      faultCateArr: [], //故障分类
      faultReasonArr: [], //故障原因
      eqpTypeArr: [], //设备类型
      maintenanceSubjectArr: [], //维护主体
      mobileFaultDutyArr: [], //故障定责
      faultStatusArr: [], //故障状态
      networkTypeArr: [], //网络类型
      boardTypeArr: [], //板卡类型
      emergencyLevelArr: [], //紧急程度
      // carrierSharingArr:[],//运营商共享情况
      // siteServiceLevelArr:[],//站址服务等级
      // ifTowerReasonArr:[],//申告工单是否铁塔原因
      // declarationFaultClassificationArr:[],//申告工单故障分类
      // ifApplyForReliefArr:[],//是否申请减免
    };
  },
  mounted() {
    // this.getVendorOptions();
    // this.getProcessTimeLimitOptions();
    // this.qualitativeReviewDetail();

    // this.resourceBackInit(this.qualitativeReviewForm);
    this.getFeedbackData();
    this.getProvinceDict();
  },

  created() {
    this.getDictData("fault_status", "faultStatusArr"); //故障状态
    this.getDictData("com_pro_network_type", "networkTypeArr"); //网络类型
    // this.getDictData("site_offline_reason", "siteOfflineReasonArr");//退服原因
    this.getDictData("com_emergency_level", "emergencyLevelArr"); //紧急程度
  },
  methods: {
    getFeedbackData() {
      let param = {
        woId: this.common.woId,
        isEdit: 1, //isEdit    0:查看 1:编辑
      };
      apiqueryFeedback(param)
        .then(res => {
          if (res.status == 0) {
            let self = this;
            let listAll = res?.data ?? [];
            self.list = listAll[self.index];
            self.dealData();
          }
        })
        .catch(err => {
          console.log(err);
        });
    },

    dealData() {
      this.getDictData(
        this.list.professionalTypeId + "_commonpro_faultCate",
        "faultCateArr"
      ); //故障分类
      this.getDictData(
        this.list.professionalTypeId + "_commonpro_vendor",
        "vendorOptions"
      ); //厂家
      this.getDictData(
        this.list.professionalTypeId + "_commonpro_eqpType",
        "eqpTypeArr"
      ); //设备类型
      this.getDictData(
        this.list.professionalTypeId + "_commonpro_faultDuty",
        "mobileFaultDutyArr"
      ); //故障定责
      this.getDictData(
        this.list.professionalTypeId + "_commonpro_maintenanceSubject",
        "maintenanceSubjectArr"
      ); //维护主体
      this.getDictData(
        this.list.professionalTypeId +
          "_" +
          this.list.faultCateId +
          "_commonpro_faultReason",
        "faultReasonArr"
      ); //
      if (this.list.faultCate == "传输" || this.list.faultCate == "无线设备") {
        this.getDictData(
          this.list.professionalTypeId +
            "_" +
            this.list.faultCateId +
            "_commonpro_vendor",
          "vendorOptions"
        ); //厂家
      }
      if (this.list.faultCate != "线路故障") {
        this.getDictData(
          this.list.professionalTypeId +
            "_" +
            this.list.eqpTypeId +
            "_commonpro_boardType",
          "boardTypeArr"
        ); //板卡类型
      }

      this.qualitativeReviewForm.workItemId = this.common.workItemId;
      this.qualitativeReviewForm.woId = this.common.woId;
      this.qualitativeReviewForm.processInstId = this.common.processInstId;
      this.qualitativeReviewForm.processDefId = this.common.processDefId;
      this.qualitativeReviewForm.processNode = this.common.processNode;

      this.qualitativeReviewForm.feedbackFormProcessId = this.list.processInstId;
      this.qualitativeReviewForm.linkId = this.list.linkId;
      this.qualitativeReviewForm.faultCate = this.list.faultCate;
      this.qualitativeReviewForm.faultCateId = this.list.faultCateId;
      this.qualitativeReviewForm.faultReason = this.list.faultReason;
      this.qualitativeReviewForm.vendor = this.list.vendor;
      this.qualitativeReviewForm.eqpTypeId = this.list.eqpTypeId;
      this.qualitativeReviewForm.eqpType = this.list.eqpType;
      this.qualitativeReviewForm.eqpName = this.list.eqpName;
      this.qualitativeReviewForm.falutReasonDesc = this.list.falutReasonDesc;
      this.qualitativeReviewForm.falutComment = this.list.falutComment;
      this.qualitativeReviewForm.maintenanceSubject = this.list.maintenanceSubject;
      this.qualitativeReviewForm.mobileFaultDutyId = this.list.mobileFaultDutyId;
      this.qualitativeReviewForm.faultStatus = this.list.faultStatus;
      this.qualitativeReviewForm.networkType = this.list.networkType;
      this.qualitativeReviewForm.boardType = this.list.boardType;
      this.qualitativeReviewForm.deviceName = this.list.deviceName;
      this.qualitativeReviewForm.faultRange = this.list.faultRange;
      this.qualitativeReviewForm.effectSystem = this.list.effectSystem;
      this.qualitativeReviewForm.effectCircuit = this.list.effectCircuit;
      this.qualitativeReviewForm.bearerSystem = this.list.bearerSystem;
      this.qualitativeReviewForm.isRouterChange = this.list.isRouterChange;
      this.qualitativeReviewForm.routerChangeJobCode = this.list.routerChangeJobCode;
      this.qualitativeReviewForm.businessName = this.list.businessName;
      this.qualitativeReviewForm.emergencyLevel = this.list.emergencyLevel;
      this.qualitativeReviewForm.hardwareFlag = this.list.hardwareFlag;
      this.qualitativeReviewForm.ifPropertyLoss = this.list.ifPropertyLoss;
      this.qualitativeReviewForm.insuranceId = this.list.insuranceId;
      this.qualitativeReviewForm.isDispatchAccurate = this.list.isDispatchAccurate;
      this.list.files.forEach(file => {
        this.attachFiles.push({
          id: file.attId,
          name: file.attOrigName,
        });
      });

      // debugger
      this.recoveryDuration = this.showTime(this.list.recoveryDuration);
      this.faultDuration = this.showTime(this.list.faultDuration);
      this.suspendDuration = this.showTime(this.list.suspendDuration);
      this.faultCleanDuration = this.showTime(this.list.faultCleanDuration);
    },

    descTip(count, name, showName) {
      if (this.qualitativeReviewForm[name].length >= count) {
        this[showName] = true;
        setTimeout(() => {
          this[showName] = false;
        }, this.showTipTime);
      } else {
        this[showName] = false;
      }
    },

    showTime(val) {
      if (val == 0) {
        return "0";
      }
      if (val == "" || null == val) {
        return "";
      }
      var time = "";
      var second = val % 60;
      var minute = parseInt(parseInt(val) / 60) % 60;
      time = second + "秒";
      if (minute >= 1) {
        time = minute + "分" + second + "秒";
      }
      var hour = parseInt(parseInt(val / 60) / 60) % 24;
      if (hour >= 1) {
        time = hour + "小时" + minute + "分" + second + "秒";
      }
      var day = parseInt(parseInt(parseInt(val / 60) / 60) / 24);
      if (day >= 1) {
        time = day + "天" + hour + "小时" + minute + "分" + second + "秒";
      }
      return time;
    },

    getProvinceDict() {
      apiGetProvinceDict()
        .then(res => {
          if (res.status == "0") {
            this.provinceOption = res?.data ?? [];
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    // 下拉查询
    getDictData(dictId, selectName) {
      this[selectName] = [];
      let param = {
        dictType: dictId,
      };
      apiDict(param)
        .then(res => {
          if (res.code == 200) {
            this[selectName] = res.data;
          }
        })
        .catch(error => {
          console.log(error);

          return false;
        });
    },

    //故障分类改变
    faultCateChange() {
      let ids = this.faultCateArr.filter(
        item => item.dictCode == this.qualitativeReviewForm.faultCateId
      );
      this.qualitativeReviewForm.faultCate = ids[0].dictName;
      // this.$message.error('faultCate'+this.backSingleForm.faultCate);
      this.qualitativeReviewForm.faultReason = "";
      if (this.list.professionalTypeName == "无线网") {
        this.qualitativeReviewForm.vendor = ""; //无线网专业特殊，和专业、故障分类三级联动
      }
      if (
        this.list.professionalTypeName == "传输网" ||
        this.list.professionalTypeName == "集客"
      ) {
        if (this.qualitativeReviewForm.faultCate == "线路故障") {
          this.qualitativeReviewForm.falutReasonDesc =
            "距   机房  km处，光缆因     （原因）中断。 处理方式：   （熔接或者倒带后）恢复";
        } else if (this.qualitativeReviewForm.faultCate == "设备故障") {
          this.qualitativeReviewForm.falutReasonDesc =
            "  机房    板卡的故障。处理方式：   （更换板卡或者倒带后）恢复";
        } else {
          this.qualitativeReviewForm.falutReasonDesc = "";
        }
      }
    },

    //设备类型改变
    eqpTypeChange() {
      let ids = this.eqpTypeArr.filter(
        item => item.dictCode == this.qualitativeReviewForm.eqpTypeId
      );
      this.qualitativeReviewForm.eqpType = ids[0].dictName;
      this.qualitativeReviewForm.boardType = "";
    },

    relatedFilesBrowse() {
      this.relatedFilesDialogVisible = true;
    },
    changeFileData(data) {
      this.qualitativeReviewForm.relatedFiles = data.fileName;
      this.importForm.relatedFilesFileList = data.attachmentFileList;
      this.relatedFilesDialogVisible = false;
    },
    closeAttachmentDialog() {
      this.relatedFilesDialogVisible = false;
    },

    // 光缆或设备输入修改返回修改
    changeCableEquipInputData(val, type) {
      if (type == '光缆') {
        this.qualitativeReviewForm.deviceName = val;
      } else if (type == '设备') {
        this.qualitativeReviewForm.eqpName = val;
      }

    },
    // 光缆或者设备选择后传回
    eventEquipLineSelectData(val, type) {
      console.log(val, "===", type);
      if (type == '光缆') {
        if (this.qualitativeReviewForm.deviceName && this.qualitativeReviewForm.deviceName.length > 0) {
          this.qualitativeReviewForm.deviceName += '、';
        }
        val.forEach((item, i) => {
          if (i == val.length - 1) {
            this.qualitativeReviewForm.deviceName = ((this.qualitativeReviewForm.deviceName && this.qualitativeReviewForm.deviceName.length > 0) ? this.qualitativeReviewForm.deviceName : "") + item["name"];
          } else {
            this.qualitativeReviewForm.deviceName =
              ((this.qualitativeReviewForm.deviceName && this.qualitativeReviewForm.deviceName.length > 0) ? this.qualitativeReviewForm.deviceName : "") + item["name"] + "、";
          }
        });
      } else if (type == '设备') {
        if (this.qualitativeReviewForm.eqpName && this.qualitativeReviewForm.eqpName.length > 0) {
          this.qualitativeReviewForm.eqpName += '、';
        }
        val.forEach((item, i) => {
          if (i == val.length - 1) {
            this.qualitativeReviewForm.eqpName = ((this.qualitativeReviewForm.eqpName && this.qualitativeReviewForm.eqpName.length > 0) ? this.qualitativeReviewForm.eqpName : "") + item["name"];
          } else {
            this.qualitativeReviewForm.eqpName =
              ((this.qualitativeReviewForm.eqpName && this.qualitativeReviewForm.eqpName.length > 0) ? this.qualitativeReviewForm.eqpName : "") + item["name"] + "、";
          }
        });
      }
    },

    // //光缆名称选择
    // opticalCableSelect() {
    //   let jsonStr =
    //     '{"userId":"302785","username":"yangxm97","callSysId":"0001","callSysName":"电子运维"}';
    //   this.$nextTick(() => {
    //     document.querySelector("#fiberOpticCable").value = jsonStr;
    //     document.querySelector("#sub__fiberOpticCable").submit();
    //   });
    // },
    // //设备名称选择
    // deviceSelect() {
    //   let jsonStr =
    //     '{"userId":"302785","username":"yangxm97","callSysId":"0001","callSysName":"电子运维"}';
    //   this.$nextTick(() => {
    //     document.querySelector("#device").value = jsonStr;
    //     document.querySelector("#sub__device").submit();
    //   });
    // },

    handleSubmit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          let self = this;
          this.qualitativeReviewFullscreenLoading = true;
          // 如果有附件，先上传附件
          let isUpload = false;
          let formData = new FormData();
          if (self.importForm.relatedFilesFileList.length > 0) {
            isUpload = true;
            for (let item of self.importForm.relatedFilesFileList) {
              formData.append("files", item.raw);
            }
          } else {
            isUpload = false;
            formData.append("files", "");
          }

          // 上传附件
          if (isUpload) {
            let uploadUrl = `/commonprovince/attach/upload?groupKey=${this.common.woId}&processId=${this.list.linkId}&processNode=${this.common.processNode}&sheetCreateTime=${this.common.sheetCreateTime}`;
            this.uploadFile(uploadUrl, formData);
          } else {
            this.submit();
          }
        } else {
          return false;
        }
      });
    },
    uploadFile(uploadUrl, formData){
      apiFileUpload(uploadUrl, formData)
        .then(res => {
          if (res.code == 200) {
            // param.linkId = this.fileLinkId;
            this.submit();
            this.importForm.relatedFilesFileList = [];
          } else {
            this.$message.error(res.msg);
            // this.importForm.relatedFilesFileList = [];
            this.qualitativeReviewFullscreenLoading = false;
            return false;
          }
        })
        .catch(error => {
          console.log(error);
          this.qualitativeReviewFullscreenLoading = false;
        });
    },

    submit() {
      // debugger
      if (
        this.list.professionalTypeName != "传输网" &&
        this.list.professionalTypeName != "集客"
      ) {
        delete this.qualitativeReviewForm.resolvingTimeLimite;
        delete this.qualitativeReviewForm.secondFaultSourceNumber;
        delete this.qualitativeReviewForm.influenceBusinessScope;
        delete this.qualitativeReviewForm.faultProvince;
      }
      this.$set(this.qualitativeReviewForm, "actionName", "定性审核");

      apiActionPublic(this.qualitativeReviewForm)
        .then(res => {
          if (res.status == "0") {
            this.$message.success(res.msg);
            this.$emit("qualitativeReviewSubmit", res.data);
          } else {
            this.$message.error(res.msg);
          }
          this.qualitativeReviewFullscreenLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error(error.msg);
          this.qualitativeReviewFullscreenLoading = false;
        });
    },

    //审批结果事件
    // changeAuditResult() {
    // if (this.qualitativeReviewForm.auditResult == "1") {
    //   this.qualitativeReviewForm.isUploadReport = "0";
    //   this.qualitativeReviewForm.isAllStored = "1";
    //   this.$set(this.qualitativeReviewForm, "auditContent", "");
    // } else if (
    //   this.qualitativeReviewForm.isUploadReport == "1" &&
    //   this.qualitativeReviewForm.auditResult == "0"
    // ) {
    //   this.$set(this.qualitativeReviewForm, "auditContent", "请上传故障报告");
    // } else if (
    //   this.qualitativeReviewForm.isUploadReport == "0" &&
    //   this.qualitativeReviewForm.auditResult == "0"
    // ) {
    //   this.$set(this.qualitativeReviewForm, "auditContent", "");
    // }
    // },
    // changeUploadReport() {
    //   if (
    //     this.qualitativeReviewForm.isUploadReport == "1" &&
    //     this.qualitativeReviewForm.auditResult == "0"
    //   ) {
    //     this.$set(this.qualitativeReviewForm, "auditContent", "请上传故障报告");
    //   } else if (
    //     this.qualitativeReviewForm.isUploadReport == "0" &&
    //     this.qualitativeReviewForm.auditResult == "0"
    //   ) {
    //     this.$set(this.qualitativeReviewForm, "auditContent", "");
    //   }
    // },
    // downloadAppendixFile(data) {
    //   let param = {
    //     attId: data.id,
    //   };
    //   apiDownloadAppendixFile(param)
    //     .then(res => {
    //       if (res.status == "0") {
    //         this.$message.success("文件下载成功");
    //       } else {
    //         this.$message.error("文件下载失败");
    //       }
    //     })
    //     .catch(error => {
    //       console.log(error);
    //       this.$message.error("文件下载失败");
    //     });
    // },

    handleDownload(attId) {
      let param = {
        attId: attId,
        watermark: "", //水印
      };
      apiFileDownload(param)
        .then(res => {})
        .catch(err => {
          console.log(err);
        });
    },

    onReset() {
      this.qualitativeReviewForm = {
        ...this.$options.data,
        woId: this.common.woId,
        workItemId: this.workItemId,
        processInstId: this.common.processInstId,
        processDefId: this.common.processDefId,
      };
      // this.qualitativeReviewDetail();
    },

    //资源系统回调方法初始化
    resourceBackInit() {
      window["__process_response_from_getDevice"] = value => {
        let form = this.qualitativeReviewForm;
        let result = JSON.parse(value);
        let objArray = JSON.parse(result?.objects);

        if (form.faultCate == "1") {
          let opticFiberArray = form.opticFiber
            ? form.opticFiber.split(",")
            : [];
          let effectSystemArray = form.effectSystem
            ? form.effectSystem.split(",")
            : [];
          let effectSystemArrayTemp = [];

          objArray.forEach(obj => {
            if (opticFiberArray.indexOf(obj.resName) < 0) {
              opticFiberArray.push(obj.resName);
            }

            effectSystemArrayTemp = JSON.parse(obj.affectedSystem);
            effectSystemArrayTemp.forEach(sys => {
              if (sys.sysName && effectSystemArray.indexOf(sys.sysName) < 0) {
                effectSystemArray.push(sys.sysName);
              }
            });
          });

          form.opticFiber = opticFiberArray.join(",");
          form.effectSystem = effectSystemArray.join(",");
          form.eqpName = "";
        } else {
          let eqpNameArray = form.eqpName ? form.eqpName.split(",") : [];

          objArray.forEach(obj => {
            if (eqpNameArray.indexOf(obj.resName) < 0) {
              eqpNameArray.push(obj.resName);
            }
          });

          form.eqpName = eqpNameArray.join(",");
          form.opticFiber = "";
          form.effectSystem = "";
        }
      };
    },
  },
};
</script>
<style lang="scss" scoped>
.descriptions {
  ::v-deep .el-descriptions-item__content {
    /*overflow: hidden;*/
    /*text-overflow: ellipsis;*/
    /*white-space: nowrap;*/

    position: relative;
    word-break: break-all;
    padding-right: 8px;
  }
}

::v-deep .el-descriptions-item__cell {
  vertical-align: top;
}

::v-deep .el-descriptions-item__label {
  margin-right: 10px;
  text-align: right !important;
  width: 120px !important;
}
.qualitative-review {
  max-height: calc(90vh - 150px);
  min-height: 400px;
  margin: 0 4px;
  padding-left: 4px;
  padding-right: 8px;
  overflow-y: auto;
  .cus-card ::v-deep .el-card__header {
    padding: 11px 24px 10px;
    font-weight: 400;
    @include themify {
      background-color: themed("$--background-color-base");
    }
  }
  .fileName_style_download {
    margin-right: 3px;
    cursor: pointer;
    vertical-align: middle;
    div {
      display: inline-block;
      max-width: 120px;
      vertical-align: top;
    }
  }
  .fileName_style {
    margin-right: 3px;
    vertical-align: middle;
    div {
      display: inline-block;
      max-width: 120px;
      vertical-align: top;
    }
  }
}
</style>
