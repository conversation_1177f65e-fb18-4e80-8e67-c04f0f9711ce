<template>
  <el-dialog
    :title="title"
    :visible="visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    width="600px"
    :before-close="closeFunc"
    top="5vh"
    :append-to-body="appendToBody"
  >
    <div style="height: 480px">
      <el-tabs type="border-card" v-model="activeName">
        <el-tab-pane v-if="showOrgsTree" label="组织树" name="orgs">
          <el-tree
            ref="orgsTree"
            show-checkbox
            node-key="id"
            :props="orgsProps"
            style="height: 405px; overflow-y: auto"
            :load="loadLazyFuncOrg"
            :default-checked-keys="orgDefaultChecked"
            lazy
            v-loading="orgsTreeLoading"
            @check="itCloudCheckLimit"
          >
          </el-tree
        ></el-tab-pane>
        <el-tab-pane v-if="showUsersTree" label="人员树" name="users">
          <el-input v-model="userFilterText" clearable>
            <el-button
              type="info"
              slot="append"
              @click="userFilter"
              icon="el-icon-search"
              title="搜索"
            ></el-button>
            <el-button
              type="info"
              slot="append"
              icon="el-icon-close"
              title="取消搜索"
              @click="closeSearch"
            ></el-button>
          </el-input>
          <el-table
            v-if="isShowFilterUserTable"
            :data="userData"
            border
            :show-header="false"
            style="
              width: 100%;
              margin-top: 5px;
              height: 400px;
              overflow-y: auto;
              max-height: 450px;
            "
            @selection-change="handleUserSelectionChange"
            v-loading="userFilterTableLoading"
          >
            <el-table-column type="selection" width="55"> </el-table-column>
            <el-table-column prop="trueName" label="姓名"></el-table-column>
            <el-table-column label="所属组织">
              <template slot-scope="scope">
                {{ scope.row.orgEntity.fullOrgName }}
              </template>
            </el-table-column>
          </el-table>
          <el-tree
            v-if="!isShowFilterUserTable"
            ref="usersTree"
            show-checkbox
            node-key="id"
            :props="usersProps"
            style="height: 405px; overflow-y: auto"
            :load="loadLazyFunc"
            :default-checked-keys="userDefaultChecked"
            lazy
            v-loading="userTreeLoading"
          >
          </el-tree>
        </el-tab-pane>
        <el-tab-pane
          v-if="showContactUserTab"
          label="常用联系人"
          name="contactUser"
        >
          <el-table
            :data="contactUserData"
            border
            style="
              width: 100%;
              margin-top: 5px;
              height: 400px;
              overflow-y: auto;
              max-height: 450px;
            "
            ref="contactUserTable"
            @selection-change="contactUserSelectionChange"
            v-loading="contactUserTableLoading"
          >
            <el-table-column type="selection" width="55"> </el-table-column>
            <el-table-column prop="trueName" label="姓名"></el-table-column>
            <el-table-column label="组织名称">
              <template slot-scope="scope">
                {{ scope.row.orgEntity.fullOrgName }}
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="onSave">确 定</el-button>
      <el-button @click="closeFunc">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import cloneDeep from "lodash/cloneDeep";
import { mapGetters } from "vuex";
import {
  apiGetUserTreeNew,
  apiGetOrgLazyTree,
  apiUserFilter,
  apiGetContactUser,
} from "../../../workOrder/api/CommonApi";

export default {
  name: "DiaOrgsUserTree",
  props: {
    title: String,
    visible: Boolean,
    appendToBody: Boolean,
    showOrgsTree: {
      type: Boolean,
      default: true,
    },
    showUsersTree: {
      type: Boolean,
      default: true,
    },
    showContactUserTab: {
      type: Boolean,
      default: false,
    },
    orgDefaultChecked: Array,
    userDefaultChecked: Array,
    professionalType: String,
  },
  data() {
    // let self = this;
    return {
      activeName: this.showOrgsTree ? "orgs" : "users",
      orgsCommonData: [],
      orgsProps: {
        children: "children",
        label: "name",
        isLeaf: "isLeaf",
      },
      usersProps: {
        children: "children",
        label: "name",
        isLeaf: "isLeaf",
      },
      userFilterText: "",
      userData: [],
      isShowFilterUserTable: false,
      multipleSelectionsUser: [],
      userFilterTableLoading: false,
      orgsTreeLoading: false,
      userTreeLoading: false,
      userInfoData: [],

      contactUserData: [],
      contactUserTableLoading: false,
      contactSelectionUser: [],
    };
  },
  computed: { ...mapGetters(["userInfo"]) },
  watch: {
    showOrgsTree(newVal) {
      if (newVal) {
        this.activeName = "orgs";
      } else {
        this.activeName = "users";
      }
    },
  },
  mounted() {
    this.userInfoData = JSON.parse(this.userInfo.attr2);
    this.getContactUserData();
  },
  methods: {
    closeFunc() {
      this.$emit("update:visible", false);
      this.$refs?.orgsTree?.setCheckedKeys?.([]);
      this.$refs?.usersTree?.setCheckedKeys?.([]);
      this.closeSearch();
      this.contactSelectionUser = [];
      this.$refs?.contactUserTable?.clearSelection();
    },
    onSave() {
      let self = this;
      let orgsChecked =
        cloneDeep(self.$refs?.orgsTree?.getCheckedNodes?.()) ?? [];
      let usersChecked =
        cloneDeep(self.$refs?.usersTree?.getCheckedNodes?.()) ?? [];
      let selectionUser = self.multipleSelectionsUser;
      let contactSelectionUser = self.contactSelectionUser;
      let conflict = false;
      for (let index = 0; index < orgsChecked.length; index++) {
        const element = orgsChecked[index];
        if (
          usersChecked.some(item => {
            const res = self.getBranchOrg(item);
            if (res.headquarters) {
              return item.orgId == element.id;
            } else {
              return res.secondId == element.id;
            }
          })
        ) {
          conflict = true;
          self.$message({
            message: `组织树已选“${element.name}”，人员树“${element.name}”下不能选择人员！`,
            type: "warning",
            showClose: true,
            duration: 6000,
          });
          break;
        }
      }
      if (conflict) {
        return false;
      }
      self.$emit("on-save", {
        orgsChecked,
        usersChecked,
        selectionUser,
        contactSelectionUser,
      });
      self.closeFunc();
    },
    getBranchOrg(user) {
      let self = this;
      const userNode = self.$refs.usersTree.getNode(user);
      let level = userNode.level;
      let parent;
      while (level > 1) {
        parent = parent?.parent ?? userNode.parent;
        level--;
      }
      if (userNode.level == 1 || /集团|大区|其他/.test(parent.data.name)) {
        return {
          headquarters: true,
        };
      } else {
        return {
          headquarters: false,
          secondId: parent.data.id,
        };
      }
    },
    loadLazyFunc(node, resolve) {
      let param = {
        level: node.level + 1,
        nodeId: node?.data?.id ?? "",
      };
      if (0 == node.level) {
        this.userTreeLoading = true;
      }
      apiGetUserTreeNew(param)
        .then(res => {
          if (res.status == 0) {
            let treeData = res?.data?.data ?? [];
            if (0 == node.level) this.userTreeLoading = false;
            return resolve(treeData);
          }
          if (0 == node.level) this.userTreeLoading = false;
        })
        .catch(e => {
          console.log("报错了，", e);
          if (0 == node.level) this.userTreeLoading = false;
          return resolve([]);
        });
    },
    loadLazyFuncOrg(node, resolve) {
      let param = {
        level: node.level + 1,
        nodeId: node?.data?.id ?? "",
      };
      if (0 == node.level) {
        this.orgsTreeLoading = true;
      }
      apiGetOrgLazyTree(param)
        .then(res => {
          if (res.status == 0) {
            let treeData = res?.data?.data ?? [];
            if (0 == node.level) this.orgsTreeLoading = false;
            return resolve(treeData);
          }
          if (0 == node.level) this.orgsTreeLoading = false;
        })
        .catch(e => {
          console.log("报错了，", e);
          if (0 == node.level) this.orgsTreeLoading = false;
          return resolve([]);
        });
    },
    userFilter() {
      if (this.userFilterText) {
        this.isShowFilterUserTable = true;
        this.userFilterTableLoading = true;
        apiUserFilter({
          userName: this.userFilterText,
          orgId: this.userInfoData.orgInfo.orgId,
        })
          .then(res => {
            if (res.code == "200") {
              this.userData = res?.data ?? [];
            }
            this.userFilterTableLoading = false;
          })
          .catch(error => {
            console.log(error);
            this.userFilterTableLoading = false;
          });
      } else {
        this.$message.warning("人员名称不能为空");
      }
    },
    handleUserSelectionChange(selection) {
      this.multipleSelectionsUser = selection;
    },
    closeSearch() {
      this.isShowFilterUserTable = false;
      this.userFilterText = "";
      this.multipleSelectionsUser = [];
    },
    contactUserSelectionChange(selection) {
      this.contactSelectionUser = selection;
    },
    getContactUserData() {
      let param = {
        userName: this.userInfoData.userName,
      };
      apiGetContactUser(param)
        .then(res => {
          if (res.code == 200) {
            this.contactUserData = res?.data ?? [];
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    //it云专业（组织树限制选一个组织）
    itCloudCheckLimit() {
      if (this.professionalType == "IT云") {
        const getList = this.$refs.orgsTree.getCheckedNodes();
        // .concat(this.$refs.orgsTree.getHalfCheckedNodes());
        if (getList.length > 1) {
          this.$message.warning("请只选择一个组织");
          this.$refs?.orgsTree?.setCheckedKeys([]);
        }
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.fileName_style {
  margin-right: 3px;
  vertical-align: middle;
  div {
    display: inline-block;
    max-width: 70px;
    vertical-align: top;
  }
}
.custom-theme-default .el-table::before {
  border-color: transparent;
}
</style>
