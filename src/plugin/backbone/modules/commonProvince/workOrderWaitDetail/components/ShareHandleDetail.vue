<template>
  <el-card shadow="always" class="card" :body-style="{ padding: '10px 24px' }">
    <div class="header clearfix">
      <span class="header-title">共建共享处理信息</span>
    </div>

    <div class="_el-table" v-if="tableData.length > 0">
      <div class="title-Cla">
        <span>处理列表</span>
        <div class="header-right" v-if="isShowAudit">
          <el-button type="primary" size="mini" @click="audit()"
          >审核</el-button>
        </div>
      </div>
      <el-table
        :data="tableData"
        border
        stripe
        v-loading="tableLoading"
      >
        <el-table-column
          type="index"
          width="60px"
          label="序号"
        ></el-table-column>
        <el-table-column
          prop="sheetNo"
          label="共享单号"
          min-width="200"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="operType"
          label="操作类别"
          min-width="150"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="operName"
          label="操作人姓名"
          width="150"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="operEmail"
          label="操作人邮箱"
          min-width="200"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="operTel"
          label="操作人手机"
          width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="operTime"
          label="操作时间"
          min-width="160"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="proCauseKind"
          label="问题原因类别"
          min-width="130"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="solution"
          label="解决方案"
          min-width="160"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="用户使用情况"
          prop="isRecover"
          min-width="160"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="故障恢复时间"
          prop="recoverTime"
          min-width="160"
          show-overflow-tooltip
        ></el-table-column>
      </el-table>
    </div>

    <div class="_el-table" v-if="tableData1.length > 0">
      <div class="title-Cla">
        故障确认列表
      </div>
      <el-table
        :data="tableData1"
        border
        stripe
        v-loading="tableLoading"
      >
        <el-table-column
          type="index"
          width="60px"
          label="序号"
        ></el-table-column>
        <el-table-column
          prop="sheetNo"
          label="共享单号"
          min-width="200"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="isChargeBack"
          label="是否退单"
          width="100"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="isRecover"
          label="故障是否恢复"
          width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="recoverTime"
          label="故障恢复时间"
          min-width="160"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="isTimeOut"
          label="申告处理是否超时"
          width="130"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="faultType"
          label="故障类别"
          width="100"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="faultCause"
          label="故障原因"
          min-width="160"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="faultSummary"
          label="故障总结"
          min-width="160"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="结单时间"
          prop="statemOfTime"
          min-width="160"
          show-overflow-tooltip
        ></el-table-column>
      </el-table>
    </div>

<!--    <el-dialog-->
<!--      title="挂起审核"-->
<!--      :visible.sync="dialogPendingReviewVisible"-->
<!--      :close-on-click-modal="false"-->
<!--      :close-on-press-escape="false"-->
<!--      @close="dialogPendingReviewClose"-->
<!--      width="450px"-->
<!--    >-->
<!--      <audit-->
<!--        :common="common"-->
<!--        :opContent="opContent"-->
<!--        actionName="挂起审核"-->
<!--        @closeDialogPendingReview="dialogPendingReviewSubmitClose"-->
<!--      ></audit>-->
<!--    </el-dialog>-->

<!--    &lt;!&ndash;  解挂审核0  &ndash;&gt;-->
<!--    <el-dialog-->
<!--      title="解挂审核"-->
<!--      :visible.sync="dialogSolutionToHangAuditVisible"-->
<!--      :close-on-click-modal="false"-->
<!--      :close-on-press-escape="false"-->
<!--      @close="dialogSolutionToHangAuditClose"-->
<!--      width="450px"-->
<!--    >-->
<!--      <audit-->
<!--        :common="common"-->
<!--        :opContent="opContent"-->
<!--        actionName="解挂审核"-->
<!--        @closeDialogSolutionToHangAudit="dialogSolutionToHangAuditSubmitClose"-->
<!--      ></audit>-->
<!--    </el-dialog>-->
  </el-card>
</template>

<script>
  import Audit from "./Audit.vue";

  import { apiGetShareInfo } from "../api/CommonApi";

export default {
  name: "ShareHandleDetail",
  components: {
    Audit
  },
  props: {
    woId: String,
  },
  data() {
    return {
      tableData: [],
      tableData1: [],
      // tableListData: {},
      tableLoading: false,
      opContent: null,
      isShowAudit:false,//是否显示审核
      // dialogPendingReviewVisible: false,
      // //解挂审核
      // dialogSolutionToHangAuditVisible: false,
    };
  },
  mounted() {
    this.getTableData();
  },
  methods: {
    getTableData() {

      this.tableLoading = true;
      let realParam = {
        woId: this.woId,
      };
      // let param = {
      //   pageIndex: this.form.pageNum,
      //   pageSize: this.form.pageSize,
      //   param1: JSON.stringify(realParam),
      // };
      apiGetShareInfo(realParam)
        .then(res => {
          if (res.status == "0") {
            // debugger
            this.tableData = res?.data?.dealOrderInfo ?? [];
            this.tableData1 = res?.data?.confirmOrderInfo ?? [];
            let flag = res?.data?.hangAuditFlag ?? '';//hangAuditFlag : CTN : 未审核， CTY:已审核
            this.isShowAudit = (flag == 'CTN') ? true : false;
            let type = res?.data?.operType ?? ''; //operType：申请挂起;申请解挂
            this.opContent = type == '申请挂起' ? 1 : 2;
          }
          this.tableLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.tableLoading = false;
        });
    },

    audit(){
      // this.opContent = 1;
      this.$emit("showAudit",this.opContent);


      // if (this.opContent == 1){
      //   this.dialogPendingReviewVisible = true;
      // }else if (this.opContent == 2){
      //   this.dialogSolutionToHangAuditVisible = true;
      // }
    },

    //挂起审核关闭--
    dialogPendingReviewClose() {
      this.dialogPendingReviewVisible = false;
    },
    //挂起审核提交--
    dialogPendingReviewSubmitClose() {
      this.dialogPendingReviewVisible = false;
      this.$emit("closeDialogPendingReview");
    },
    //解挂审核关闭--
    dialogSolutionToHangAuditClose() {
      this.dialogSolutionToHangAuditVisible = false;
    },
    //解挂审核提交--
    dialogSolutionToHangAuditSubmitClose() {
      this.dialogSolutionToHangAuditVisible = false;
      this.$emit("closeDialogSolutionToHangAudit");
    },

  },
};
</script>

<style lang="scss" scoped>
@import "../assets/common.scss";
._el-table {
  width: 100%;
  margin-bottom: 10px;

  .title-Cla{
    width: 100%;
    height: 30px;
    line-height: 30px;
    padding-left: 10px;
    background-color: #ebecec;
  }
}
::v-deep .el-descriptions-row {
  .is-bordered-label {
    width: 140px;
  }
  .tdOverflow {
    width: 260px;
    height: 50px;
    overflow-x: auto;
    // text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 50px;
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .tdOverflow::-webkit-scrollbar {
    display: none;
  }
}
</style>
