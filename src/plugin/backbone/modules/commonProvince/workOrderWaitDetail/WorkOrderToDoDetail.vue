<template>
  <head-fixed-layout class="draft-eidt-page" v-loading="pageLoading">
    <template #header>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="24" :md="12" :lg="14" class="head-title">
          <text-collapse
            :text="`【省分故障工单】${headInfo.title}`"
            :max-lines="2"
          ></text-collapse>
          <el-button
            class="modifyT"
            @click="modifyTitle"
            v-if="showModifyBtn"
          ></el-button>
        </el-col>
        <el-col :xs="24" :sm="24" :md="12" :lg="10" class="head-handle-wrap">
          <el-button-group>
            <el-button type="button" @click="onHeadHandleClick('jcxx')"
              >基础信息</el-button
            >
            <el-button
              v-if="showAlarm"
              type="button"
              @click="onHeadHandleClick('gjxq')"
              >告警详情</el-button
            >
            <el-button
              type="button"
              v-if="showRelation"
              @click="onHeadHandleClick('glzd')"
              >关联诊断</el-button
            >
            <el-button
              type="button"
              :style="getStatusStyle()"
              @click="onHeadHandleClick('fkdxq')"
              >反馈单详情</el-button
            >
            <el-dropdown
              @command="onHeadHandleClick"
              class="el-button more-dropdown"
              size="medium"
            >
              <el-button type="button">更多</el-button>
              <el-dropdown-menu slot="dropdown">
                <template v-for="(item, i) in headMoreDrops">
                  <template v-if="item.command == 'gjgx'">
                    <el-dropdown-item
                      :command="item.command"
                      :key="i"
                      v-if="showShareInfo"
                    >
                      {{ item.title }}
                    </el-dropdown-item>
                  </template>
                  <template v-else-if="item.command == 'scbg'">
                    <el-dropdown-item
                      :command="item.command"
                      :key="i"
                      v-if="showUpInfo"
                    >
                      {{ item.title }}
                    </el-dropdown-item>
                  </template>
                  <template v-else>
                    <el-dropdown-item :command="item.command" :key="i">
                      {{ item.title }}
                    </el-dropdown-item>
                  </template>
                </template>
              </el-dropdown-menu>
            </el-dropdown>
          </el-button-group>
          <br />
          <!-- 按钮动态展示 -->
          <template v-for="(item, i) in processButtonArr">
            <el-popover
              v-if="item == '现场打点'"
              :key="i"
              placement="top-start"
              title="提示"
              width="200"
              class="btnleft__group"
              trigger="hover"
              content="请到APP上操作"
            >
              <el-button size="mini" slot="reference">{{ item }}</el-button>
            </el-popover>
            <el-popover
              v-else-if="item == '返单'"
              :key="i"
              placement="bottom"
              width="310"
              popper-class="szTipCla"
              v-model="fbTipVisible"
              trigger="manual"
            >
              <div>
                <i class="el-icon-warning"></i>{{fbTipMsg}}
              </div>
              <el-button
                slot="reference"
                size="mini"
                type="primary"
                class="btnleft__group"
                :key="i"
                @click="buttonClick(item)"
                v-loading.fullscreen.lock="processFullscreenLoading"
              >{{ item }}</el-button
              >
            </el-popover>
            <template v-else>
              <!--              <template v-if="item == '转派' && turnFlag == 'Y'"></template>-->
              <template v-if="item == '告警清除申请'"></template>
              <template v-else-if="item == '人工确认清除'"></template>
              <template v-else-if="item == '定性审核'"></template>
              <template v-else-if="item == '故障报告审核'"></template>
              <!--              <template v-else-if="item == '现场打点'">-->
              <!--                <el-button-->
              <!--                  type="primary"-->
              <!--                  class="btnleft__group"-->
              <!--                  :key="i"-->
              <!--                  disabled-->
              <!--                  >{{ item }}</el-button-->
              <!--                >-->
              <!--              </template>-->
              <template v-else>
                <el-button
                  size="mini"
                  type="primary"
                  class="btnleft__group"
                  :key="i"
                  @click="buttonClick(item)"
                  v-loading.fullscreen.lock="processFullscreenLoading"
                  >{{ item }}</el-button
                >
              </template>
            </template>
          </template>
        </el-col>
      </el-row>
      <el-divider
        direction="horizontal"
        content-position="left"
        class="divider"
      ></el-divider>
      <div class="head-info2">
        <el-row :gutter="20" tag="p">
          <el-col
            :xs="24"
            :sm="24"
            :md="12"
            :lg="12"
            tag="p"
            class="head-sheetId"
          >
            工单编号: {{ headInfo.sheetId }}
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="12" tag="p">
            <el-row :gutter="20" type="flex" justify="end">
              <el-col
                :xs="24"
                :sm="12"
                :md="12"
                :lg="5"
                tag="p"
                class="head-up-down"
              >
                <div>当前处理人</div>
                <div
                  class="sheetNo_class"
                  @click="openPeerProcessor"
                  style="
                    cursor: pointer;
                    text-decoration: underline;
                    color: #b50b14;
                    user-select: unset;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    font-size: 18px;
                    line-height: 28px;
                  "
                  :title="headInfo.currentHandler"
                >
                  {{ headInfo.currentHandler }}
                </div>
              </el-col>
              <el-col
                :xs="24"
                :sm="12"
                :md="12"
                :lg="5"
                tag="p"
                class="head-up-down"
              >
                <div>工单状态</div>
                <div style="font-size: 18px; line-height: 28px">
                  {{ headInfo.sheetStatus }}
                </div>
              </el-col>
              <el-col
                :xs="24"
                :sm="12"
                :md="12"
                :lg="7"
                tag="p"
                class="head-up-down"
                v-if="headInfo.sheetStatus == '待受理'"
              >
                <div>剩余受理时间</div>
                <div
                  class="text-primary"
                  style="
                    white-space: nowrap;
                    font-size: 18px;
                    color: #c43c43;
                    line-height: 28px;
                  "
                  :title="setAcceptTimeLimit"
                >
                  {{ setAcceptTimeLimit }}
                </div>
              </el-col>
              <el-col
                :xs="24"
                :sm="12"
                :md="12"
                :lg="7"
                tag="p"
                class="head-up-down"
                v-if="
                  headInfo.sheetStatus != '待受理' &&
                  headInfo.sheetStatus != '已归档' &&
                  headInfo.sheetStatus != '作废' &&
                  headInfo.sheetStatus != '异常归档'
                "
              >
                <div>剩余处理时间</div>
                <div
                  class="text-primary"
                  style="
                    white-space: nowrap;
                    font-size: 18px;
                    color: #c43c43;
                    line-height: 28px;
                  "
                  :title="setHandleTimeLimit"
                >
                  {{ setHandleTimeLimit }}
                </div>
              </el-col>

              <el-col
                :xs="24"
                :sm="12"
                :md="12"
                :lg="6"
                tag="p"
                class="head-up-down"
              >
                <div>工单总耗时0</div>
                <div
                  class="text-primary"
                  style="
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    font-size: 18px;
                    color: #c43c43;
                    line-height: 28px;
                  "
                  :title="headInfo.totalWorkingTime"
                >
                  {{ headInfo.totalWorkingTime }}
                </div>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
        <el-row :gutter="20" tag="p">
          <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">
            建单人: {{ headInfo.buildSingleMan }}
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">
            建单部门: {{ headInfo.buildSingleDept }}
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">
            建单时间: {{ headInfo.buildSingleTime }}
          </el-col>
        </el-row>
        <el-row :gutter="20" tag="p">
          <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">
            工单来源: {{ headInfo.sourceInfo }}
          </el-col>
          <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">
            发生时间: {{ headInfo.occurrenceTime }}
          </el-col>
          <!--          <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">-->
          <!--            发生地区: {{ headInfo.faultRegion }}-->
          <!--          </el-col>-->
          <el-col :xs="24" :sm="24" :md="12" :lg="6" tag="p">
            紧急程度: {{ headInfo.emergencyDegree }}
          </el-col>
        </el-row>
      </div>
    </template>

    <base-info
      ref="jcxx"
      v-if="basicWorkOrderData"
      :basicWorkOrderData="basicWorkOrderData"
      :woId="common.woId"
      :workItemId="common.workItemId"
    />
    <!-- 告警详情 -->
    <alarm-detail
      ref="gjxq"
      :common="common"
      :clear-apply-btn-show="alarmClearApplyBtnShow"
      :manual-btn-show="manualAlarmClearBtnShow"
      v-if="showAlarm"
    />
    <!-- 关联诊断 --->
    <relation-diagnosis
      ref="glzd"
      v-if="showRelation"
      :woId="common.woId"
      :basicWorkOrderData="basicWorkOrderData"
    />
    <!-- 反馈单详情 -->
    <feedback-sheet
      ref="fkdxq"
      v-if="showFkdxq && common.woId"
      :isShowAudit="isShowQualitativeAuditButton"
      :common="common"
      :woId="common.woId"
      :basicWorkOrderData="basicWorkOrderData"
      @qualitativeReviewSubmit="qualitativeReviewSubmit"
    />
    <!--    上传故障报告信息-->
    <upload-fault-file
      ref="scbg"
      :common="common"
      :isShowReportReview="dialogUpFFAuditVisible"
      v-if="showUpInfo"
      @dialogReportAuditSubmitClose="dialogReportAuditSubmitClose"
    />
    <!--    共建共享处理信息-->
    <share-handle-detail
      ref="gjgx"
      :woId="common.woId"
      v-if="showShareInfo"
      @showAudit="showShareAudit"
    />

    <!-- 处理详情 -->
    <deal-details
      ref="clxq"
      v-if="showClxq && this.common.woId != ''"
      :woId="common.woId"
      :basicWorkOrderData="basicWorkOrderData"
    />
    <!--    流程日志-->
    <process-log
      ref="lcrz"
      v-if="showLcrz && this.common.woId != ''"
      :woId="common.woId"
      :LogsData="LogsData"
    />
    <!--    流程图-->
    <flow-chart
      ref="lct"
      v-if="showLct && this.common.woId != ''"
      :common="common"
    />

    <!--    阶段反馈-->
    <el-dialog
      title="阶段反馈"
      :visible.sync="dialogStageFeedbackVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogStageFeedbackClose"
      width="550px"
    >
      <stage-feedback
        :common="common"
        actionName="阶段反馈"
        @stageBackDialogClose="stageBackDialogCommitClose"
      ></stage-feedback>
    </el-dialog>

    <!--    返单-->
    <el-dialog
      title="返单"
      :visible.sync="dialogBackSingleVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogBackSingleClose"
      :fullscreen="false"
      width="83%"
      top="5vh"
    >
      <back-single
        ref="backSingleForm"
        :common="common"
        :timing="timing"
        @closeBackSingleDialog="dialogBackSingleSubmitClose"
      ></back-single>
    </el-dialog>

    <!--    资源勘误-->
    <el-dialog
      title="资源勘误"
      :visible.sync="dialogZYKWVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogZYKWClose"
      :fullscreen="false"
      width="84%"
      top="5vh"
    >
      <ResourceCorrigendum
        :common="common"
        @rcDialog="dialogZYKWSubmitClose"
      ></ResourceCorrigendum>
    </el-dialog>

    <!--修改主题-->
    <el-dialog
      title="修改主题"
      :visible.sync="dialogModifyTitleVisiable"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogModifyTitleClose"
      width="600px"
    >
      <el-form ref="modifyForm" :model="modifyForm" label-width="90px">
        <el-form-item
          label="工单主题:"
          prop="title"
          :rules="{
            required: true,
            message: '请填写工单主题',
          }"
        >
          <el-input
            maxlength="400"
            show-word-limit
            @keyup.native="descTipZT(400, 'title', 'showgdzt')"
            type="textarea"
            :rows="3"
            placeholder="请填写工单主题"
            v-model="modifyForm.title"
            style="width: 100%"
          >
          </el-input>
          <div class="el-form-item__error" v-if="showgdzt">
            已超过填写字数上限
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="handleModifySubmit('modifyForm')"
          v-loading.fullscreen.lock="modifySubmitLoading"
          >提 交</el-button
        >
        <el-button @click="onResetModify">重 置</el-button>
      </div>
    </el-dialog>

    <!--撤单-->
    <el-dialog
      title="撤单"
      :visible.sync="dialogRevokeVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogRevokeClose"
      width="550px"
    >
      <el-form ref="revokeForm" :model="revokeForm" label-width="90px">
        <el-form-item
          label="撤单原因:"
          prop="auditOpinion"
          :rules="{
            required: true,
            message: '请填写撤单原因',
          }"
        >
          <el-input
            type="textarea"
            :rows="2"
            placeholder="请填写撤单原因"
            v-model="revokeForm.auditOpinion"
            style="width: 300px"
            show-word-limit
            maxlength="255"
            @keyup.native="descTip(255, 'auditOpinion', 'showTip')"
          >
          </el-input>
          <div class="el-form-item__error" v-if="showTip">
            已超过填写字数上限
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="handleRevokeSubmit('revokeForm')"
          v-loading.fullscreen.lock="revokeSubmitLoading"
          >提 交</el-button
        >
        <el-button @click="onResetRevoke">重 置</el-button>
      </div>
    </el-dialog>

    <!--是否上站-->
    <el-dialog
      title="是否上站"
      :visible.sync="dialogSZVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogSZClose"
      width="420px"
    >
      <el-form ref="szForm" :model="szForm" label-width="90px">
        <el-form-item
          label="是否上站:"
          prop="isSZ"
          :rules="{
            required: true,
            message: '请选择',
          }"
        >
          <el-radio-group v-model="szForm.isSZ" @input="szChange" style="width: 260px">
            <el-radio label="1">是</el-radio>
            <el-radio label="0">否</el-radio>
          </el-radio-group>
          <div class="el-form-item__error" v-if="showSZTip">
            *上站选"是"则必须进行现场打点
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="handleSZSubmit('szForm')"
          v-loading.fullscreen.lock="szSubmitLoading"
        >提 交</el-button
        >
        <el-button @click="onResetSZ">重 置</el-button>
      </div>
    </el-dialog>

    <!--追单-->
    <el-dialog
      title="追单"
      :visible.sync="dialogAfterSingleVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogAfterSingleClose"
      width="600px"
    >
      <after-single
        :common="common"
        actionName="追单"
        @closeAfterSingleDialog="dialogAfterSingleSubmitClose"
      ></after-single>
    </el-dialog>
    <!--  挂起0 -->
    <el-dialog
      title="挂起"
      :visible.sync="dialogHangUpVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogHangUpClose"
      width="540px"
    >
      <hang-up
        :common="common"
        :opType="1"
        actionName="挂起"
        @closeDialogHangUp="dialogHangUpSubmitClose"
      ></hang-up>
    </el-dialog>
    <!--  解挂0  -->
    <el-dialog
      title="解挂"
      :visible.sync="dialogSolutionToHangVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogSolutionToHangClose"
      width="540px"
    >
      <hang-up
        :common="common"
        :opType="2"
        actionName="解挂"
        @closeDialogHangUp="dialogSolutionToHangSubmitClose"
      ></hang-up>
    </el-dialog>

    <!--  挂起审核  -->
    <el-dialog
      title="挂起审核"
      :visible.sync="dialogPendingReviewVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogPendingReviewClose"
      width="450px"
    >
      <audit
        :common="common"
        :opContent="opContent"
        actionName="挂起审核"
        @closeDialogPendingReview="dialogPendingReviewSubmitClose"
      ></audit>
    </el-dialog>

    <!--  解挂审核  -->
    <el-dialog
      title="解挂审核"
      :visible.sync="dialogSolutionToHangAuditVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogSolutionToHangAuditClose"
      width="450px"
    >
      <audit
        :common="common"
        :opContent="opContent"
        actionName="解挂审核"
        @closeDialogSolutionToHangAudit="dialogSolutionToHangAuditSubmitClose"
      ></audit>
    </el-dialog>

    <!--异常终止-->
    <el-dialog
      title="异常终止"
      :visible.sync="dialogAbendVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogAbendClose"
      width="450px"
    >
      <abend
        :common="common"
        @closeDialogAbend="dialogAbendSubmitClose"
      ></abend>
    </el-dialog>

    <!--异常终止审核-->
    <el-dialog
      title="异常终止审核"
      :visible.sync="dialogAbendAuditVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogAbendAuditClose"
      width="450px"
    >
      <abend-audit
        :common="common"
        @closeDialogAbendAudit="dialogAbendAuditSubmitClose"
      ></abend-audit>
    </el-dialog>

    <!--    转派-->
    <el-dialog
      title="转派"
      :visible.sync="dialogTurnToSendVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogTurnToSendClose"
      width="480px"
    >
      <turn-to-send
        @closeDialogTurnToSend="dialogTurnToSendSubmitClose"
        :common="common"
        :type="type"
      ></turn-to-send>
    </el-dialog>
    <!--    转办0-->
    <el-dialog
      width="480px"
      title="转办"
      :visible.sync="transferTodoDialogVisible"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      @close="dialogtransferTodoClose"
    >
      <transfer-todo
        @closeDialogTransfer="dialogTransferClose"
        :common="common"
        :type="type"
        actionName="批量转办"
      ></transfer-todo>
    </el-dialog>
    <!--    上传故障报告0-->
    <el-dialog
      title="上传故障报告"
      :visible.sync="dialogUpFFVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogUpFFClose"
      width="600px"
    >
      <upload-fault-file-handle
        :common="common"
        actionName="上传故障报告"
        @closeUpFileDialog="dialogUpFFSubmitClose"
      ></upload-fault-file-handle>
    </el-dialog>
    <!--    故障处理0-->
    <el-dialog
      title="故障处理"
      :visible.sync="dialogFaultHandleVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogFaultHandleClose"
      :fullscreen="false"
      width="83%"
      top="5vh"
    >
      <fault-handle
        :common="common"
        :order-info="basicWorkOrderData"
        @closeFaultHandleDialog="dialogFaultHandleSubmitClose"
      ></fault-handle>
    </el-dialog>

    <!--    联通确认0-->
    <el-dialog
      title="联通确认"
      :visible.sync="dialogLTConfirmVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogLTConfirmClose"
      :fullscreen="false"
      width="83%"
      top="5vh"
    >
      <unicom-confirm
        :common="common"
        :order-info="basicWorkOrderData"
        @closeLTConfirmDialog="dialogLTConfirmSubmitClose"
      ></unicom-confirm>
    </el-dialog>

    <!--处理人列表-->
    <el-dialog
      title="处理人"
      :visible.sync="dialogPeerProcessorVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="PeerProcessorClose"
      :fullscreen="false"
      width="70%"
      top="5vh"
    >
      <PeerProcessorDetail
        :common="common"
        :persons="basicWorkOrderData.operatePersonId"
      ></PeerProcessorDetail>
    </el-dialog>

    <!--    一键催办-->
    <one-key-ivr
      :dialogOneKeyIvrNotice.sync="dialogOneKeyVisible"
      :common="common"
      :sheetId="headInfo.sheetId && headInfo.sheetId"
      :sheetCreateTime="headInfo.buildSingleTime && headInfo.buildSingleTime"
    />

    <!-- 悬浮 外链接按钮 -->
    <div class="outer-link">
      <span class="num">{{ messageNum }}</span>
      <span class="icon" @click="handleOuterLink"></span>
      <span class="name" @click="handleOuterLink">chatops</span>
    </div>
    <!-- 弹出框信息 -->
    <el-dialog
      title="消息"
      :visible.sync="chatopsVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="handleCloseMessage"
      width="1000px"
    >
      <iframe
        :src="rsaEncryptUrl"
        frameborder="0"
        width="100%"
        height="500px"
        scrolling="auto"
        style="border: 1px solid #eee; margin-top: -20px"
      ></iframe>
    </el-dialog>

    <!--现场打点-->
    <!--    <el-dialog-->
    <!--      title="PC现场打点"-->
    <!--      :visible.sync="dialogLocationVisible"-->
    <!--      width="480px"-->
    <!--    >-->
    <!--      <el-form-->
    <!--        ref="locationForm"-->
    <!--        :model="locationForm"-->
    <!--        :rules="locationRules"-->
    <!--        label-width="80px"-->
    <!--      >-->
    <!--        <el-form-item label="经度">-->
    <!--          <el-input v-model="locationForm.longitude"></el-input>-->
    <!--        </el-form-item>-->
    <!--        <el-form-item label="纬度">-->
    <!--          <el-input v-model="locationForm.latitude"></el-input>-->
    <!--        </el-form-item>-->
    <!--      </el-form>-->
    <!--      <span slot="footer" class="dialog-footer">-->
    <!--        <el-button @click="dialogLocationVisible = false">取 消</el-button>-->
    <!--        <el-button type="primary" @click="submitLocationForm('locationForm')"-->
    <!--        >确 定</el-button-->
    <!--        >-->
    <!--      </span>-->
    <!--    </el-dialog>-->
  </head-fixed-layout>
</template>

<script>
import { mapGetters } from "vuex";
import moment from "moment";
import oneKeyIvr from "../../commCloud/components/CommCloudoneKeyIVR.vue";
import TransferTodo from "../../workOrder/workOrderWaitDetail/components/TransferTodo.vue";
import HeadFixedLayout from "../../workOrder/components/HeadFixedLayout.vue";
import BaseInfo from "./components/BaseInfo.vue";
import ShareHandleDetail from "./components/ShareHandleDetail.vue";
import UploadFaultFile from "./components/UploadFaultFile.vue";
import UploadFaultFileHandle from "./components/UploadFaultFileHandle.vue";
import AlarmDetail from "./components/AlarmDetail.vue";
import RelationDiagnosis from "./components/RelationDiagnosis.vue";
import FeedbackSheet from "./components/FeedbackSheet.vue";
import DealDetails from "./components/DealDetails.vue";
import ProcessLog from "./components/ProcessLog.vue";
import FlowChart from "./components/FlowChart.vue";
import StageFeedback from "./components/StageFeedback.vue";
import HangUp from "./components/HangUp.vue";
import Audit from "./components/Audit.vue";
import Abend from "./components/Abend.vue";
import AbendAudit from "./components/AbendAudit";
import AfterSingle from "./components/AfterSingle";
import TurnToSend from "./components/TurnToSend.vue";
import QualitativeAudit from "./components/QualitativeAudit.vue";
import OrderEvaluate from "./components/OrderEvaluate.vue";
import EliminateFaults from "./components/EliminateFaults.vue";
import Qualitative from "./components/Qualitative.vue";
import BackSingle from "./components/BackSingle.vue";
import FaultHandle from "./components/FaultHandle.vue";
import UnicomConfirm from "./components/UnicomConfirm.vue";
import PeerProcessorDetail from "./components/PeerProcessorDetail";
import TextCollapse from "@plugin/backbone/components/TextCollapse.vue";
import ResourceCorrigendum from "./components/ResourceCorrigendum";

import {
  apiGetShowButton,
  apiGetWorkOrderInfo,
  apiAccept,
  apiRevoke,
  apiModifyTitle,
  apiHaveRead,
  apiActionPublic,
  getCurrentTime,
  apiSetRead,
  apiGetRsaEncrypt,
  apiMessageRsaEncrypt,
  apiGetMeassgeNum,
  apiLocation,
  apiGetWoid,
  apiqueryFeedback,
  apiGetShareInfo,
  apiQueryUploadFileDetail,
  apiGetRelationDiagnosis,
  apiBackSingleInit,
  apiSyncClear,
  apiQueryAlarmDetail,
  apiGetPeerProcessorList,
} from "./api/CommonApi";

export default {
  name: "WorkOrderWaitDetail",
  components: {
    PeerProcessorDetail,
    oneKeyIvr,
    FaultHandle,
    UnicomConfirm,
    TransferTodo,
    HeadFixedLayout,
    BaseInfo,
    AlarmDetail,
    ShareHandleDetail,
    UploadFaultFileHandle,
    UploadFaultFile,
    RelationDiagnosis,
    FeedbackSheet,
    DealDetails,
    ProcessLog,
    FlowChart,
    StageFeedback,
    HangUp,
    Audit,
    Abend,
    AfterSingle,
    AbendAudit,
    TurnToSend,
    QualitativeAudit,
    OrderEvaluate,
    EliminateFaults,
    Qualitative,
    BackSingle,
    TextCollapse,
    ResourceCorrigendum,
  },
  data() {
    return {
      fbTipVisible: false,
      fbTipMsg: '',
      showgdzt: false,
      showTip: false,
      showTipTime: 5000,
      // reportId:'',//上传故障报告初始化需要，如果返回的流程id和本工单流程id一样
      // shareOrderInfo:{},//共建共享信息
      headInfo: {
        title: "",
        sheetId: "",
        currentHandler: "",
        currentLink: "",
        sheetStatus: null,
        acceptTimeLimit: "",
        processingTimeLimit: "",
        processTimeLimit: "",
        totalWorkingTime: "",
        buildSingleMan: "",
        buildSingleDept: "",
        buildSingleTime: "",
        sourceInfo: "",
        occurrenceTime: "",
        emergencyDegree: "",
      },
      headMoreDrops: [],
      processButtonArr: [],
      basicWorkOrderData: {},
      LogsData: [],
      dealData: [],
      alarmsData: [],
      messageNum: "", // chatops 未读条数
      isTitleCollapse: false,
      common: {
        woId: "", //工单ID
        workItemId: "", //工作项ID
        processInstId: null, //流程实例ID
        processDefId: null, //流程定义ID
        failureTime: "", //故障发生时间
        failureInformTime: "", //故障通知时间
        actionName: "", //操作按钮名称
        processNode: "", //环节名称
        sheetNo: "", //工单编号
        isSender: null, //是否为建单人
        agentManId: "", //主送人Id
        agentMan: "", //主送人
        copyManId: "", //抄送人Id
        copyMan: "", //抄送人
        sheetStatus: "", //工单状态
        agentDeptCode: "",
        agentDeptName: "",
        copyDeptCode: "",
        copyDeptName: "",
        professionalType: null,
        networkType: null,
        hangOver: null, //挂起历时
        faultCauseDescription: null, //故障原因描述
        networkTypeName: null, //网络类型中文名
        professionalTypeName: null, //专业类型中文名
      },
      timing: {
        hangTime: "", //挂起时间
      },
      showFkdxq: false, //反馈单详情
      showClxq: false, //处理详情
      showLcrz: false, //流程日志
      showLct: false, //流程图
      showRelation: false, //关联诊断
      showAlarm: false, //告警详情
      showUpInfo: false, //上传故障报告详情
      showShareInfo: false, //共建共享详情

      processFullscreenLoading: false,
      isShowQualitativeAuditButton: false,
      //阶段反馈
      dialogStageFeedbackVisible: false,

      //返单
      dialogBackSingleVisible: false,
      //撤单
      dialogRevokeVisible: false,
      revokeSubmitLoading: false,
      revokeForm: {
        auditOpinion: null,
      },
      //修改主题
      showModifyBtn: false,
      dialogModifyTitleVisiable: false,
      modifySubmitLoading: false,
      modifyForm: {
        title: null,
      },
      //是否上站
      showSZTip:false,
      dialogSZVisible: false,
      szSubmitLoading:false,
      szForm: {
        isSZ: null,
      },
      //资源勘误
      dialogZYKWVisible: false,
      //追单
      dialogAfterSingleVisible: false,
      //挂起
      dialogHangUpVisible: false,
      //解挂
      dialogSolutionToHangVisible: false,
      //挂起审核
      // auditTitle: null,
      opContent: null,
      dialogPendingReviewVisible: false,
      //解挂审核
      dialogSolutionToHangAuditVisible: false,
      //异常终止
      dialogAbendVisible: false,
      //异常终止审核
      dialogAbendAuditVisible: false,
      //一键催办
      dialogOneKeyVisible: false,
      //转派
      dialogTurnToSendVisible: false,
      //转办
      transferTodoDialogVisible: false,
      //上传故障报告
      dialogUpFFVisible: false,
      //故障报告审核是否显示
      dialogUpFFAuditVisible: false,
      //告警清除申请按钮是否显示
      alarmClearApplyBtnShow: false,
      //人工确认清除按钮是否显示
      manualAlarmClearBtnShow: false,
      //定性审核
      dialogQualitativeAuditVisible: false,
      // PC现场打点
      dialogLocationVisible: false,
      //类型
      type: "single",
      // 故障处理
      dialogFaultHandleVisible: false,
      //联通确认
      dialogLTConfirmVisible: false,
      //处理人
      dialogPeerProcessorVisible: false,

      route: null,
      //从哪个页面进入的，不同页面进入，显示的按钮会不同，如：待阅工单只展示已阅、传阅按钮
      fromPage: null,
      pageLoading: true,
      networkType: null,
      // turnFlag: null, // 转派了，就不显示转派按钮。'Y'不显示
      // regularFdbkFlag: null, // 操作了，阶段反馈，才能处理完成，'Y'显示
      // locationFlag: null, //现场打点完了，才显示，'Y'显示
      rsaEncrypt: "", // chatops 未读加密
      rsaEncryptUrl: "", // chatops iframe 路径
      chatopsVisible: false, // chatops 弹出框
      // locationForm: {}, // pc现场定位form
      // locationRules: {
      //   longitude: [{ require: true, message: "现场打点经度必填" }],
      //   latitude: [{ require: true, message: "现场打点纬度必填" }],
      // },
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
    ...mapGetters(["token"]),

    // mainProfessionalType() {
    //   return this.$route.query.mainProfessionalType;
    // },
    setAcceptTimeLimit() {
      let time = this.headInfo.acceptTimeLimit;
      if (time == "-") {
        return "-";
      } else if (time <= 0) {
        if (Math.floor(Math.abs(time) / 60) == 0) {
          return `已超时${Math.abs(time) % 60}分钟`;
        } else {
          return `已超时${Math.floor(Math.abs(time) / 60)}小时${
            Math.abs(time) % 60
          }分钟`;
        }
      } else if (time > 0) {
        if (Math.floor(Math.abs(time) / 60) == 0) {
          return `${time % 60}分钟`;
        } else {
          return `${Math.floor(time / 60)}小时${time % 60}分钟`;
        }
      } else {
        return "";
      }
    },
    setHandleTimeLimit() {
      let time = this.headInfo.processTimeLimit;
      if (time == "-") {
        return "-";
      } else if (time <= 0) {
        if (Math.floor(Math.abs(time) / 60) == 0) {
          return `已超时${Math.abs(time) % 60}分钟`;
        } else {
          return `已超时${Math.floor(Math.abs(time) / 60)}小时${
            Math.abs(time) % 60
          }分钟`;
        }
      } else if (time > 0) {
        if (Math.floor(Math.abs(time) / 60) == 0) {
          return `${time % 60}分钟`;
        } else {
          return `${Math.floor(time / 60)}小时${time % 60}分钟`;
        }
      } else {
        return "";
      }
    },
  },
  created() {
    this.route = this.$route;
    this.common.workItemId = this.$route.query.workItemId;
    this.common.processInstId = this.$route.query.processInstId;
    this.common.woId = this.$route.query.woId;
    this.common.processDefId = this.$route.query.processDefId;
    this.common.processNode = this.$route.query.processNode;
    this.networkType = this.$route.query.networkType;
    this.professionalType = this.$route.query.professionalType;

    //外部链接调用
    if (this.$route.query.outSystem) {
      if (this.$route.query.woId) {
        this.common.woId = this.$route.query.woId;
        this.common.processInstId = this.$route.query.processInstId;
      } else {
        let param = {
          woCode: this.$route.query.sheetNo,
        };
        apiGetWoid(param).then(res => {
          this.common.woId = res.data.woId;
          this.common.processInstId = res.data.processInstId;
        });
      }
    }
  },
  mounted() {
    // if (this.networkType == "一干") {
    //
    // } else if (this.networkType == "国际") {
    //   this.headMoreDrops = [{ command: "lct", title: "流程图" }];
    // }
    this.headMoreDrops = [
      { command: "gjgx", title: "共建共享处理信息" },
      { command: "scbg", title: "上传故障报告信息" },
      { command: "clxq", title: "处理详情" },
      { command: "lcrz", title: "流程日志" },
      { command: "lct", title: "流程图" },
    ];

    this.fromPage = this.$route.query.fromPage;

    if (this.fromPage == "待阅工单") {
      this.setReadStatus();
    }
    this.getWorkOrderInfo();
    this.getCurrentHandler();
    this.syncClearAlarm();
  },
  methods: {

    descTipZT(count, name, showName) {
      if (this.modifyForm[name].length >= count) {
        this[showName] = true;
        setTimeout(() => {
          this[showName] = false;
        }, this.showTipTime);
      } else {
        this[showName] = false;
      }
    },

    descTip(count, name, showName) {
      if (this.revokeForm[name].length >= count) {
        this[showName] = true;
        setTimeout(() => {
          this[showName] = false;
        }, this.showTipTime);
      } else {
        this[showName] = false;
      }
    },
    getShowButton() {
      return new Promise(resolve => {
        let param = {
          woId: this.common.woId,
          workItemId: this.common.workItemId,
          processInstId: this.common.processInstId,
          processNode: this.common.processNode,
          fromPage: this.fromPage,
        };
        if (sessionStorage.getItem(this.common.woId) == "受理") {
          param.actionName = "受理";
        } else {
          param.actionName = "";
        }
        apiGetShowButton(param)
          .then(res => {
            if (res.status == "0") {
              let self = this;
              self.processButtonArr = res?.data ?? [];
              self.processButtonArr.forEach(item => {
                if (item == "告警清除申请") {
                  this.alarmClearApplyBtnShow = true;
                }
                if (item == "人工确认清除") {
                  this.manualAlarmClearBtnShow = true;
                }
                if (item == "定性审核") {
                  this.isShowQualitativeAuditButton = true;
                }
                if (item == "故障报告审核") {
                  this.dialogUpFFAuditVisible = true;
                }
              });
            }
            resolve("success");
          })
          .catch(error => {
            console.log(error);
          });
      });
    },
    getWorkOrderInfo() {
      this.pageLoading = true;
      // 第三方打开时， url 中 获取 sheetNo 参数
      const url = window.location.href;
      const queryStri = url.substring(url.indexOf("?") + 1).split("&");
      let sheetNoUrl = "";
      queryStri.forEach(item => {
        if (item.indexOf("sheetNo") >= 0) {
          sheetNoUrl = decodeURI(item.split("=")[1]);
        }
      });

      let param = {
        woId: this.common.woId,
        processInstId: this.common.processInstId,
        workItemId: this.common.workItemId,
        woCode: sheetNoUrl,
      };

      apiGetWorkOrderInfo(param)
        .then(res => {
          if (res.status == "0") {
            let self = this;
            this.getFeedbackData();
            this.getShareData();
            this.getUpFileData();
            self.basicWorkOrderData = res?.data ?? {};
            // self.basicWorkOrderData = res?.data?.mainForm ?? {};
            // self.dealData = res?.data?.handleDetails ?? {};
            // self.alarmsData = res?.data?.alarms ?? {};
            self.headInfo.title = self.basicWorkOrderData.sheetTitle;
            self.modifyForm.title = self.basicWorkOrderData.sheetTitle;
            //只有工单归属福建才能修改标题
            self.showModifyBtn =
              self.basicWorkOrderData.provinceCode == 213 ? true : false;
            if (sessionStorage.hasOwnProperty(self.basicWorkOrderData.woId)) {
              this.common.actionName = sessionStorage.getItem(
                self.basicWorkOrderData.woId
              );
            }


            self.headInfo.sheetId = self.basicWorkOrderData.sheetNo;
            // self.headInfo.currentHandler =
            //   self.basicWorkOrderData.operatePerson; // 当前处理人 改成从获取处理人列表接口获取
            self.headInfo.acceptTimeLimit =
              self.basicWorkOrderData.remainAcceptTime;
            self.headInfo.processTimeLimit =
              self.basicWorkOrderData.remainHandleTime;
            self.headInfo.sheetStatus = self.basicWorkOrderData.sheetStatusName;
            self.headInfo.buildSingleMan = self.basicWorkOrderData.senderName;
            self.headInfo.buildSingleDept =
              self.basicWorkOrderData.senderDeptName;
            self.headInfo.buildSingleTime =
              self.basicWorkOrderData.sheetCreateTime;
            this.getWorkOrderSpentTime(self.basicWorkOrderData); //工单总耗时
            self.headInfo.sourceInfo = self.basicWorkOrderData.createTypeName;
            self.headInfo.occurrenceTime =
              self.basicWorkOrderData.alarmCreateTime;
            self.headInfo.emergencyDegree =
              self.basicWorkOrderData.emergencyLevelName;
            // self.headInfo.faultRegion = self.basicWorkOrderData.faultRegion;

            self.common.failureTime = self.basicWorkOrderData.alarmCreateTime;
            self.common.failureInformTime =
              self.basicWorkOrderData.sheetCreateTime;
            // self.common.processNode = self.basicWorkOrderData.processNode;
            self.common.sheetNo = self.basicWorkOrderData.sheetNo;
            self.common.isSender =
              self.basicWorkOrderData.sender == self.userInfo.userName ? 1 : 0;
            self.common.agentManId = self.basicWorkOrderData.agentManId;
            self.common.agentMan = self.basicWorkOrderData.agentMan;
            self.common.agentDeptCode = self.basicWorkOrderData.agentDeptCode;
            self.common.agentDeptName = self.basicWorkOrderData.agentDeptName;
            self.common.copyDeptCode = self.basicWorkOrderData.copyDeptCode;
            self.common.copyDeptName = self.basicWorkOrderData.copyDeptName;

            self.common.copyManId = self.basicWorkOrderData.copyManId;
            self.common.copyMan = self.basicWorkOrderData.copyMan;
            self.common.sheetStatus = self.headInfo.sheetStatus;
            self.common.sheetCreateTime =
              self.basicWorkOrderData.sheetCreateTime;
            self.timing.hangTime = self.basicWorkOrderData.suspendDuration;
            // 转化枚举值
            self.common.professionalType =
              self.basicWorkOrderData.professionalType;
            self.common.networkType = self.basicWorkOrderData.networkTypeId;
            self.common.networkTypeName = self.basicWorkOrderData.networkType;
            self.common.hangOver = self.basicWorkOrderData.suspendDuration;
            self.common.faultCauseDescription =
              self.basicWorkOrderData.ppResult;

            self.handleData();
            // if (
            //   this.headInfo.sheetStatus == "待定性审核" ||
            //   this.headInfo.sheetStatus == "已归档" ||
            //   this.headInfo.sheetStatus == "待评价"
            // ) {
            //   // this.showFkdxq = false;
            //   this.$nextTick(() => {
            //     this.showFkdxq = true;
            //   });
            // } else {
            //   this.showFkdxq = false;
            // }
          }
        })
        .catch(error => {
          console.log(error);
        })
        .finally(() => {
          this.pageLoading = false;
        });
    },

    handleData(){
      if (
        this.fromPage == "待办工单" ||
        this.fromPage == "在办工单" ||
        this.fromPage == "办结工单" ||
        this.fromPage == "工单查询"
      ) {
        this.getShowButton();
      }

      this.showClxq = false;
      this.showLcrz = false;
      this.showLct = false;
      this.showRelation = false;
      this.showAlarm = false;
      // this.showUpInfo = false;
      // this.showShareInfo = false;
      this.$nextTick(() => {
        if (this.basicWorkOrderData.createType == 2) {
          //手工派单工单来源为电子运维新建，没有关联诊断,告警详情根据接口判断
          this.getAlarmsData();
        } else {
          this.showAlarm = true;
        }
        if (this.basicWorkOrderData.professionalType == 7 || this.basicWorkOrderData.professionalType == 3){
          this.showRelation = true;
        }
        else{
          if (this.basicWorkOrderData.createType == 2) {
            //手工派单工单来源为电子运维新建，没有关联诊断,告警详情根据接口判断
            this.showRelation = false;
          } else {
            this.getRelationDiagnosis();
          }
        }

        this.showClxq = true;
        this.showLcrz = true;
        this.showLct = true;
      });
      this.getMessageNum();
    },

    //获取处当前理人
    getCurrentHandler() {
      let param = {
        pageIndex: 1,
        pageSize: 10,
        // userIds: this.persons,
        woId: this.common.woId,
      };
      let self = this;
      apiGetPeerProcessorList(param)
        .then(res => {
          if (res.status == "200") {
            self.headInfo.currentHandler = res?.data?.operatePerson ?? "";
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    //电子运维新建有的省分会生成虚拟告警列表，接口有数据就展示告警详情
    getAlarmsData() {
      let param = {
        pageIndex: 1,
        pageSize: 10,
        woId: this.common.woId,
      };
      apiQueryAlarmDetail(param)
        .then(res => {
          if (res.status == "0") {
            let total = res?.data?.total ?? 0;
            if (total != 0) {
              this.$nextTick(() => {
                this.showAlarm = true;
              });
            } else {
              this.showAlarm = false;
            }
          }
          this.tableLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.tableLoading = false;
        });
    },

    //同步告警清除接口
    syncClearAlarm() {
      // this.processFullscreenLoading = true;
      let param = {
        woId: this.common.woId,
      };
      apiSyncClear(param)
        .then(res => {
          // if (res.status == "0") {
          //   this.$message.success(res.msg);
          // } else {
          //   this.$message.error(res.msg);
          // }
          // this.processFullscreenLoading = false;
        })
        .catch(error => {
          console.log(error);
          // this.$message.error(error.msg);
          // this.processFullscreenLoading = false;
        });
    },

    //获取反馈单数据，有数据展示无数据不展示该模块--
    getFeedbackData() {
      let param = {
        woId: this.common.woId,
        isEdit: 0, //isEdit    0:查看 1:编辑
      };
      apiqueryFeedback(param)
        .then(res => {
          console.log("============反馈单");
          console.log(res);

          if (res.status == 0) {
            let data = res?.data ?? [];
            if (data.length > 0) {
              this.$nextTick(() => {
                this.showFkdxq = true;
              });
            } else {
              this.showFkdxq = false;
            }
          }
        })
        .catch(err => {
          console.log(err);
        });
    },

    //获取共建共享数据，有数据展示无数据不展示该模块--
    getShareData() {
      let realParam = {
        woId: this.common.woId,
      };
      apiGetShareInfo(realParam)
        .then(res => {
          if (res.status == "0") {
            let dealArr = res?.data?.dealOrderInfo ?? [];
            let confirmArr = res?.data?.confirmOrderInfo ?? [];
            // let tmp = res?.data ?? [];
            if (dealArr.length > 0 || confirmArr.length > 0) {
              this.$nextTick(() => {
                this.showShareInfo = true;
              });
            } else {
              this.showShareInfo = false;
            }
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    //共建共享展示审核
    showShareAudit(val) {
      this.opContent = val;
      if (val == 1) {
        this.dialogPendingReviewVisible = true;
      } else if (val == 2) {
        this.dialogSolutionToHangAuditVisible = true;
      }
    },

    //获取上传故障报告数据，有数据展示无数据不展示该模块--
    getUpFileData() {
      let param = {
        woId: this.common.woId,
      };
      apiQueryUploadFileDetail(param)
        .then(res => {
          if (res.status == "0") {
            let tmp = res?.data ?? [];
            if (tmp.length > 0) {
              this.$nextTick(() => {
                this.showUpInfo = true;
              });
            } else {
              this.showUpInfo = false;
            }
            // console.log(this.common.processInstId);
            // tmp.forEach(item => {
            //   if (item.processinstid == this.common.processInstId){
            //     this.reportId = item.linkid;
            //   }
            // });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    //查询关联诊断是否显示--
    getRelationDiagnosis() {
      let param = {
        woId: this.common.woId,
      };

      apiGetRelationDiagnosis(param)
        .then(res => {
          if (res.status == "0") {
            let self = this;
            if (res.data.length > 0) {
              let tmp = res.data[0];
              //显示
              //当 isPPShow为true 时显示关联诊断，
              // 当 isPPShow为false，isAIShow 为 true时显示上海ai
              // 都为false 则不呈现关联诊断版块；
              if (tmp.isPPShow || tmp.isRoomShow || tmp.isAIShow) {
                this.$nextTick(() => {
                  this.showRelation = true;
                });
              } else {
                self.showRelation = false;
              }
            }
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    //工单总耗时--
    getWorkOrderSpentTime(basicData) {
      if (
        basicData.sheetStatusName == "异常归档" ||
        basicData.sheetStatusName == "已归档" ||
        basicData.sheetStatusName == "作废"
      ) {
        let days = moment(
          basicData.sheetFilingTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.headInfo.buildSingleTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        this.headInfo.totalWorkingTime = this.showTime(Math.abs(days));
      } else {
        let days = moment(new Date(), "YYYY-MM-DD HH:mm:ss").diff(
          moment(this.headInfo.buildSingleTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        this.headInfo.totalWorkingTime = this.showTime(Math.abs(days));
      }
    },

    //查询是否可以返单操作
    canBack() {
      let param = {
        woId: this.common.woId,
        processInstId: this.common.processInstId,
      };
      apiBackSingleInit(param)
        .then(res => {
          if (res.status == "0") {
            // this.dialogBackSingleVisible = res?.data?.ifClearAlarm;
            this.fbTipVisible = false;

            if (!res?.data?.ifStation) {
              this.fbTipVisible = true;
              this.fbTipMsg = "请先进行【是否上站】的选择！";
              setTimeout(() => {
                this.fbTipVisible = false;
              }, 5000);
            }
            else if (!res?.data?.ifSign) {
              this.fbTipVisible = true;
              this.fbTipMsg = "上站处理必须进行【现场打点】操作！";
              setTimeout(() => {
                this.fbTipVisible = false;
              }, 5000);
            }
            else if (!res?.data?.ifClearAlarm) {
              this.$message.error(
                "主告警或追加告警还未清除不能返单！请先清除告警！"
              );
            }
            else{
              this.dialogBackSingleVisible = true;
            }
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    buttonClick(name) {
      switch (name) {
        case "受理": //已调试
          this.accept(name);
          break;
        case "阶段反馈": //界面已调试，接口调试
          this.dialogStageFeedbackVisible = true;
          break;
        case "追单": //界面已调试，接口调试
          this.dialogAfterSingleVisible = true;
          break;
        case "返单": //界面已调试，接口调试
          this.canBack();
          break;
        case "资源勘误": //界面已调试，接口调试
          this.dialogZYKWVisible = true;
          break;
        case "是否上站": //界面已调试，接口调试
          this.dialogSZVisible = true;
          break;
        case "转派": //界面已调试，接口调试
          this.dialogTurnToSendVisible = true;
          break;
        case "异常终止": //界面已调试，接口待调试
          this.dialogAbendVisible = true;
          break;
        case "撤单": //界面已调试，接口待调试
          this.dialogRevokeVisible = true;
          break;
        case "挂起审核": //界面已调试，接口待调试
          this.opContent = 1;
          this.dialogPendingReviewVisible = true;
          break;
        case "解挂审核": //界面已调试，接口待调试
          this.opContent = 2;
          this.dialogSolutionToHangAuditVisible = true;
          break;
        case "异常终止审核": //界面已调试，接口待调试
          this.dialogAbendAuditVisible = true;
          break;
        case "一键催办":
          this.dialogOneKeyVisible = true;
          break;
        case "挂起": //界面已调试，接口待调试
          // this.dialogUpFFVisible = true;
          this.dialogHangUpVisible = true;
          break;
        case "解挂": //界面已调试，接口待调试
          this.dialogSolutionToHangVisible = true;
          break;
        case "转办": //界面已调试，接口待调试
          this.transferTodoDialogVisible = true;
          break;
        case "上传故障报告":
          this.dialogUpFFVisible = true;
          break;
        case "故障处理":
          this.dialogFaultHandleVisible = true;
          break;
        case "联通确认":
          this.dialogLTConfirmVisible = true;
          break;
        case "同步清除告警":
          this.syncClearAlarm();
          break;
      }
    },
    //受理操作-已调试--
    accept(buttonName) {
      this.processFullscreenLoading = true;
      sessionStorage.setItem(this.common.woId, "受理");
      let param = {
        woId: this.common.woId,
        workItemId: this.common.workItemId,
        actionName: buttonName,
        processInstId: this.common.processInstId,
      };
      apiAccept(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success(res.msg);
            this.common.workItemId = res.data.workItemId;
            this.common.processDefId = res.data.processDefId;
            this.common.processInstId = res.data.processInstId;
            //根据新生成的workItemId再次调用显示按钮的接口
            // this.getShowButton();
            this.getWorkOrderInfo();
          } else {
            this.$message.error(res.msg);
          }
          this.processFullscreenLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error(error.msg);
          this.processFullscreenLoading = false;
        });
    },

    openPeerProcessor() {
      this.dialogPeerProcessorVisible = true;
    },

    PeerProcessorClose() {
      this.dialogPeerProcessorVisible = false;
    },

    //故障报告审核提交完成--
    //审核成功
    dialogReportAuditSubmitClose() {
      this.closeAndTurnAround();
    },
    //追单关闭--
    dialogAfterSingleClose() {
      this.dialogAfterSingleVisible = false;
    },
    //追单提交--
    dialogAfterSingleSubmitClose(val) {
      this.getShowButton().then(() => {
        this.getWorkOrderInfo();
      });
      this.dialogAfterSingleVisible = false;
      if (val == 1) {
        this.closeAndTurnAround();
      }
    },
    //返单关闭--
    dialogBackSingleClose() {
      this.dialogBackSingleVisible = false;
    },
    //返单提交--
    dialogBackSingleSubmitClose(data) {
      // this.common.workItemId = data.workItemId;
      // this.common.processInstId = data.processInstId;
      // this.common.processDefId = data.processDefId;
      // this.getShowButton().then(() => {
      //   this.getWorkOrderInfo();
      // });
      sessionStorage.removeItem(this.common.woId);
      this.dialogBackSingleVisible = false;
      this.closeAndTurnAround();
    },
    //上传故障报告关闭--
    dialogUpFFClose() {
      this.dialogUpFFVisible = false;
    },
    //上传故障报告提交--
    dialogUpFFSubmitClose(val) {
      // this.getShowButton().then(() => {
      //   this.getWorkOrderInfo();
      // });
      this.dialogUpFFVisible = false;
      if (val == 1) {
        this.closeAndTurnAround();
      }
    },
    //定性审核提交--
    qualitativeReviewSubmit(data) {
      // this.common.workItemId = data.workItemId;
      // this.common.processInstId = data.processInstId;
      // this.common.processDefId = data.processDefId;
      // this.getWorkOrderInfo();

      this.closeAndTurnAround();
    },
    //转办关闭
    dialogtransferTodoClose() {
      this.transferTodoDialogVisible = false;
    },
    //转办提交关闭--
    dialogTransferClose(val) {
      if (val == "1") {
        this.closeAndTurnAround();
        this.transferTodoDialogVisible = false;
      } else {
        this.transferTodoDialogVisible = false;
      }
    },
    //转派关闭--
    dialogTurnToSendClose() {
      this.dialogTurnToSendVisible = false;
    },
    //转派提交--
    dialogTurnToSendSubmitClose(val) {
      if (val == "1") {
        this.dialogTurnToSendVisible = false;
        sessionStorage.removeItem(this.common.woId);
        this.closeAndTurnAround();
      } else {
        this.dialogTurnToSendVisible = false;
      }
    },
    // 阶段反馈关闭--
    dialogStageFeedbackClose() {
      this.dialogStageFeedbackVisible = false;
    },
    // 阶段反馈提交--
    stageBackDialogCommitClose() {
      this.getWorkOrderInfo();
      this.dialogStageFeedbackVisible = false;
    },
    //撤单提交--
    handleRevokeSubmit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.revokeSubmitLoading = true;
          let param = {
            woId: this.common.woId,
            workItemId: this.common.workItemId,
            processInstId: this.common.processInstId,
            processNode: this.common.processNode,
            actionName: "撤单",
            sheetNo: this.common.sheetNo,
            reason: this.revokeForm.auditOpinion, //撤单原因
          };
          apiRevoke(param)
            .then(res => {
              if (res.status == "0") {
                this.$message.success(res.msg);
                this.dialogRevokeClose();
                this.closeAndTurnAround();
              } else {
                this.$message.error(res.msg);
              }
              this.revokeSubmitLoading = false;
            })
            .catch(error => {
              console.log(error);
              this.$message.error(error.msg);
              this.revokeSubmitLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    //撤单重置--
    onResetRevoke() {
      this.revokeForm = {
        ...this.$options.data,
      };
    },
    //撤单关闭--
    dialogRevokeClose() {
      this.onResetRevoke();
    },
    //资源勘误关闭--
    dialogZYKWClose() {
      this.dialogZYKWVisible = false;
    },
    //资源勘误提交--
    dialogZYKWSubmitClose() {
      this.getShowButton().then(() => {
        this.getWorkOrderInfo();
      });
      this.dialogZYKWVisible = false;
      // if (val == 1) {
      //   this.closeAndTurnAround();
      // }
    },

    //是否上站提交--
    handleSZSubmit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.szSubmitLoading = true;
          let param = {
            woId: this.common.woId,
            // workItemId: this.common.workItemId,
            processInstId: this.common.processInstId,
            // processNode: this.common.processNode,
            actionName: "是否上站",
            station: this.szForm.isSZ,
          };

          apiAccept(param)
            .then(res => {
              if (res.status == "0") {
                this.$message.success(res.msg);
                this.dialogSZClose();
                this.dialogSZVisible = false;
                //留在详情页 不跳转待办
                this.getWorkOrderInfo();
                // this.closeAndTurnAround();
              } else {
                this.$message.error(res.msg);
              }
              this.szSubmitLoading = false;
            })
            .catch(error => {
              console.log(error);
              this.$message.error(error.msg);
              this.szSubmitLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    //是否上站重置--
    onResetSZ() {
      this.szForm = {
        isSZ: null,
      };
      this.showSZTip = false;
    },
    //是否上站关闭--
    dialogSZClose() {
      this.dialogSZVisible = false;
      this.onResetSZ();
    },
    szChange(val){
      // console.log('上站变化'+val);
      if (val=='1'){
        this.showSZTip = true;
      }
      else {
        this.showSZTip = false;
      }
    },


    //修改主题提交
    handleModifySubmit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.modifySubmitLoading = true;
          let param = {
            woId: this.common.woId,
            title: this.modifyForm.title,
            // workItemId: this.common.workItemId,
            // processInstId: this.common.processInstId,
            // processNode: this.common.processNode,
            // actionName: "撤单",
            // sheetNo: this.common.sheetNo,
            // reason: this.revokeForm.auditOpinion, //撤单原因
          };
          apiModifyTitle(param)
            .then(res => {
              if (res.status == "200") {
                this.$message.success(res.msg);
                this.dialogModifyTitleClose();
                this.dialogModifyTitleVisiable = false;
                this.getWorkOrderInfo();
                // this.closeAndTurnAround();
              } else {
                this.$message.error(res.msg);
              }
              this.modifySubmitLoading = false;
            })
            .catch(error => {
              console.log(error);
              this.$message.error(error.msg);
              this.modifySubmitLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    //修改主题重置
    onResetModify() {
      this.modifyForm = {
        title: "",
      };
    },
    //修改主题关闭--
    dialogModifyTitleClose() {
      this.onResetModify();
    },
    //挂起关闭--
    dialogHangUpClose() {
      this.dialogHangUpVisible = false;
    },
    //挂起提交--
    dialogHangUpSubmitClose() {
      this.dialogHangUpVisible = false;
      // this.getWorkOrderInfo();
      this.closeAndTurnAround();
    },
    //解挂关闭--
    dialogSolutionToHangClose() {
      this.dialogSolutionToHangVisible = false;
    },
    //解挂提交--
    dialogSolutionToHangSubmitClose() {
      this.dialogSolutionToHangVisible = false;
      this.closeAndTurnAround();
    },
    //挂起审核关闭--
    dialogPendingReviewClose() {
      this.dialogPendingReviewVisible = false;
    },
    //挂起审核提交--
    dialogPendingReviewSubmitClose() {
      this.dialogPendingReviewVisible = false;
      // this.getShowButton().then(() => {
      //   this.getWorkOrderInfo();
      // });
      // this.getWorkOrderInfo();

      this.closeAndTurnAround();
    },
    //解挂审核关闭--
    dialogSolutionToHangAuditClose() {
      this.dialogSolutionToHangAuditVisible = false;
    },
    //解挂审核提交--
    dialogSolutionToHangAuditSubmitClose() {
      this.dialogSolutionToHangAuditVisible = false;
      // this.getShowButton().then(() => {
      //   this.getWorkOrderInfo();
      // });
      // this.getWorkOrderInfo();

      this.closeAndTurnAround();
    },
    //异常终止关闭--
    dialogAbendClose() {
      this.dialogAbendVisible = false;
    },
    //异常终止提交--
    dialogAbendSubmitClose() {
      sessionStorage.removeItem(this.common.woId);
      this.dialogAbendVisible = false;
      // this.getWorkOrderInfo();
      this.closeAndTurnAround();
    },
    //异常终止审核关闭--
    dialogAbendAuditClose() {
      this.dialogAbendAuditVisible = false;
    },
    //异常终止审核提交--
    dialogAbendAuditSubmitClose() {
      this.processButtonArr = [];
      this.dialogAbendAuditVisible = false;
      // this.getWorkOrderInfo();
      this.closeAndTurnAround();
    },
    //故障处理关闭--
    dialogFaultHandleClose() {
      this.dialogFaultHandleVisible = false;
    },
    //故障处理提交关闭--
    dialogFaultHandleSubmitClose() {
      this.dialogFaultHandleVisible = false;
      this.closeAndTurnAround();
    },
    //联通确认关闭--
    dialogLTConfirmClose() {
      this.dialogLTConfirmVisible = false;
    },
    //联通确认提交关闭--
    dialogLTConfirmSubmitClose() {
      this.dialogLTConfirmVisible = false;
      this.closeAndTurnAround();
    },

    // 现场打点
    // submitLocationForm(formName) {
    //   this.$refs[formName].validate(valid => {
    //     if (valid) {
    //       const params = {
    //         woId: this.common.woId,
    //         processInstId: this.common.processInstId,
    //         actionName: "现场打点",
    //         longitude: this.locationForm.longitude,
    //         latitude: this.locationForm.latitude,
    //         manageuser: this.userInfo.userName,
    //         actualSceneSign: "否",
    //       };
    //       apiLocation(params)
    //         .then(res => {
    //           this.dialogLocationVisible = false;
    //           this.$message.success({
    //             message: "PC现场打点成功！",
    //           });
    //         })
    //         .catch(error => {
    //           this.$message.error(error.msg);
    //         });
    //     } else {
    //       console.log("error submit!!");
    //       return false;
    //     }
    //   });
    // },
    //已阅
    // haveRead() {
    //   this.processFullscreenLoading = true;
    //   let param = {
    //     woId: this.common.woId,
    //   };
    //   apiHaveRead(param)
    //     .then(res => {
    //       if (res.status == "0") {
    //         this.$message.success("已阅");
    //         this.closeAndTurnAroundRead();
    //       } else {
    //         this.$message.error("已阅失败");
    //       }
    //       this.processFullscreenLoading = false;
    //     })
    //     .catch(error => {
    //       console.log(error);
    //       this.$message.error("已阅失败");
    //       this.processFullscreenLoading = false;
    //     });
    // },

    //暂时没用
    getStatusStyle() {
      // if (
      //   this.common.sheetStatus == "待定性" ||
      //   this.common.sheetStatus == "待定性审核"
      // ) {
      //   return "color:#b50b14;border-color:#e9b6b9;background-color:#f8e7e8";
      // }
    },

    //修改主题
    modifyTitle() {
      this.dialogModifyTitleVisiable = true;
    },

    onHeadHandleClick(command) {
      this.$refs[command]?.$el?.scrollIntoView?.({ behavior: "smooth" });
    },
    showTime(val) {
      var time = "";
      var second = val % 60;
      var minute = parseInt(parseInt(val) / 60) % 60;
      time = second + "秒";
      if (minute >= 1) {
        time = minute + "分" + second + "秒";
      }
      var hour = parseInt(parseInt(val / 60) / 60) % 24;
      if (hour >= 1) {
        time = hour + "小时" + minute + "分";
      }
      var day = parseInt(parseInt(parseInt(val / 60) / 60) / 24);
      if (day >= 1) {
        time = day + "天" + hour + "小时" + minute + "分";
      }
      return time;
    },
    //页面跳转
    closeAndTurnAround() {
      let self = this;
      self.$store.dispatch("tagsView/delView", self.route).then(() => {
        self.$destroy();
      });
      self.$router.push({
        name: "backbone_toDoRepairOrder",
        query: {
          globalUniqueID: sessionStorage.getItem("globalUniqueID"),
          token: this.token,
        },
      });
    },
    closeAndTurnAroundRead() {
      let self = this;
      self.$store.dispatch("tagsView/delView", self.route).then(() => {
        self.$destroy();
      });
      self.$router.push({
        name: "backbone_toReadRepairOrder",
        query: {
          globalUniqueID: sessionStorage.getItem("globalUniqueID"),
          token: this.token,
        },
      });
    },
    //待阅进来变已阅--已调试--
    setReadStatus() {
      let params = {
        woId: this.common.woId,
      };
      apiSetRead(params)
        .then(res => {
          console.log(res.msg);
        })
        .catch(error => {});
    },

    // 外连接
    handleOuterLink() {
      let params = {
        userId: this.$store.getters.userInfo.userName,
        sheetNo: this.basicWorkOrderData.sheetNo,
        title: "",
      };

      apiGetRsaEncrypt(params)
        .then(res => {
          if (res.data != "") {
            this.rsaEncrypt = res.data;
            // this.rsaEncryptUrl = "/nfm3/chatOpsWeb/talk-view?" + res.data;
            this.rsaEncryptUrl =
              "http://10.245.0.121:5412/chatOpsWeb/talk-view?appKey=EOMS&params=" +
              res.data;
            this.chatopsVisible = true;
          } else {
            this.$message.error("获取rsa明文加密结果失败");
          }
        })
        .catch(error => {
          this.$message.error(error.msg);
        });
    },
    // chatops 未读条数
    getMessageNum() {
      let groupIds = [];
      groupIds.push(this.headInfo.sheetId);
      let paramsRsa = {
        userId: this.$store.getters.userInfo.userName,
        groupIds: groupIds,
      };
      apiMessageRsaEncrypt(paramsRsa)
        .then(res => {
          if (res.data != "") {
            let paramsChatops = {
              appKey: "EOMS",
              params: res.data,
            };
            apiGetMeassgeNum(paramsChatops)
              .then(resNum => {
                if (resNum.data != "") {
                  this.messageNum = resNum.data[this.headInfo.sheetId];
                  console.log(resNum.data);
                }
              })
              .catch(error => {
                console.log(error.msg);
              });
          } else {
            this.$message.error("获取rsa明文加密结果失败");
          }
        })
        .catch(error => {
          this.$message.error(error.msg);
        });
    },

    // 消息弹出框关闭之后，未读条数清零
    handleCloseMessage() {
      this.chatopsVisible = false;
      this.messageNum = 0;
    },
  },
};
</script>
<style lang="scss">
  .szTipCla{
    background-color: #fef0f0 !important;
    color: #f56c6c !important;
    padding: 10px 17px !important;
    border: none !important;

    i{
      margin-right: 3px;
    }
    .popper__arrow::after {
      border-bottom-color: #fef0f0 !important;
    }
  }
</style>
<style lang="scss" scoped>
.draft-eidt-page {
  .head-title {
    position: relative;
    font-size: 20px;
    line-height: 28px;
    font-weight: 700;
    display: flex;

    .text-overflow {
      max-width: calc(100% - 30px) !important;
      width: auto;
    }
  }

  .modifyT {
    width: 30px;
    height: 28px;
    border: 0px !important;
    background: url("../../../assets/xiugaiziliao.png") center no-repeat;
    background-size: 70% 70%;
  }

  .head-handle-wrap {
    text-align: right;
    .more-dropdown {
      padding: 0;
      @include themify() {
        border: none;
        border-left: 1px solid themed("$--button-default-border-color");
      }
    }
    .btnleft__group {
      margin-left: 5px;
      margin-bottom: 0px;
      margin-top: 10px;
    }

  }

  .divider {
    margin: 10px 0 16px;
  }
  .head-info2 {
    p {
      margin: 0 auto 5px;
    }
  }
  .head-sheetId {
    font-size: 20px;
    line-height: 28px;
  }
  .head-up-down {
    text-align: center;
    padding-left: 0px !important;
    padding-right: 0px !important;
    margin-left: 0px !important;
    margin-right: 0px !important;
    & > div:first-child {
      line-height: 20px;
      @include themify() {
        color: themed("$--color-text-regular");
      }
    }
    & > div:last-child {
      font-weight: 400;
      font-size: 15px;
      line-height: 24px;
    }
  }
  .card-title {
    font-weight: 400;
    font-size: 18px;
    box-shadow: 0 0 5px #aaa;
    padding-left: 10px;
  }
}
::v-deep .detail-dialog .el-dialog__body {
  padding: 20px;
}
.outer-link {
  position: fixed;
  top: 55%;
  right: 15px;
  z-index: 99;
  padding: 3px 0px 0px 0px;
  border-radius: 999px;
  box-shadow: 0 0 8px #999;
  background: rgba(255, 255, 255, 0.7);
  width: 56px;
  height: 56px;

  .icon {
    display: block;
    width: 36px;
    height: 36px;
    cursor: pointer;
    background: url("../../../assets/icon_chatops.png") center no-repeat;
    background-size: 68% 60%;
    margin-left: 10px;
  }
  .num {
    position: absolute;
    top: -10px;
    right: -5px;
    padding: 2px 8px;
    color: #fff;
    background: #b50b14;
    border-radius: 999px;
  }
  .name {
    color: #b50b14;
    font-size: 11px;
    position: absolute;
    margin-top: -4px;
    font-weight: 500;
    text-align: center;
    width: 56px;
  }
}

.title-hd {
  font-size: 24px;
  line-height: 28px;
  font-weight: 700;
  overflow: hidden !important;
}

.expande {
  overflow: hidden !important;
  height: auto;
  // display: inline;
}

.close-title {
  overflow: hidden;
  height: 56px;
}

.expande-button-wrap {
  position: absolute;
  bottom: 0;
  right: -12px;
  height: 28px;
  background: white;
}

.expande-button {
  text-align: right;
  vertical-align: middle;
  line-height: 28px;
  padding-left: 3px;
}
.expande-button i {
  vertical-align: bottom;
  color: #b50b14;
}
</style>
