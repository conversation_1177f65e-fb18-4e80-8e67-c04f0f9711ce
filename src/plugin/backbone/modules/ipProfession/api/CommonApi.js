import {
  getJson,
  postJson,
  getJsonBlob,
  postFormData,
  delJson,
  postJsonBlob,
} from "@/utils/axios";
const showButtonUrl = "ipMajorFlow/workflow/op/getBtnList"; //详情页面中的按钮展示
const workOrderInfoUrl = "ipMajorFlow/workflow/queryByWoId"; //详情页面中的工单基本信息
const exportCommonOrderUrl = "ipMajorFlow/workflow/queryByWoId/excel"; //集团通用流程基本信息导出
const hangUpUrl = "ipMajorFlow/workflow/op/applyInterrupt"; //挂起/解挂 申请
const acceptUrl = "ipMajorFlow/workflow/accept"; //集团通用流程受理
const auditUrl = "ipMajorFlow/workflow/op/checkInterrupt"; //挂起申请审核
const abendUrl = "ipMajorFlow/workflow/op/applyAbnormalEnd"; //异常终止申请
const abendAuditUrl = "ipMajorFlow/workflow/op/checkAbnormalEnd"; //异常终止审核
const afterSingleUrl = "ipMajorFlow/workflow/op/addWoInfo"; //追单
const revokeUrl = "ipMajorFlow/workflow/removeProcessInst"; //撤单
const reportUpUrl = "ipMajorFlow/workflow/op/reportUp"; //上传故障报告
const getReportUpLogUrl = "/ipMajorFlow/workflow/op/getReportUpLog"; //查询故障报告上传记录
const reportAuditUrl = "/ipMajorFlow/workflow/op/reportAudit"; //故障报告审核
const reportTransferUrl = "/ipMajorFlow/workflow/op/reportTransfer"; //故障报告转派
const transferSubmitUrl = "/ipMajorFlow/workflow/op/transfer"; //转派

const dictUrl = "commonDict/enum/list"; //公共下拉接口地址
const ShowAttachDownloadLinktUrl =
  "/backbone/workflow/queryIfShowAttachDownloadLink"; //基本信息判断excel是否为空
// const acceptUrl = "backbone/workflow/accept"; //受理
const alarmDetailUrl = "ipMajorFlow/workflow/queryAlarm"; //告警详情表格
const syncClearUrl = "backbone/workflow/clearQuery"; //同步清除
const relationDiagnosisUrl = "backbone/workflow/queryRelation"; //关联诊断
const backSingleUrl = "backbone/workflow/ackDefine/feedback"; //返单
const qualitativeDetailUrl = "backbone/workflow/ackDefine/detail"; //返查定性详情(返单)
const qualitativeUrl = "backbone/workflow/ackDefine/define"; //定性提交
const qualitativeReviewUrl = "backbone/workflow/ackDefine/defineCheck"; //定性审核
const stageFeedbackUrl = "backbone/workflow/op/stageFeedback"; //阶段反馈

const haveReadUrl = "backbone/workflow/read/setReadStatus"; //已阅
const circulatedUrl = "backbone/workflow/read/addReadInfo"; //传阅
const circulatedUserTreeUrl = "backbone/tree/searchRedeployUser"; //传阅用户树数据
const circulatedOrgTreeUrl = "backbone/tree/searchRedeployOrg"; //传阅组织树数据
const faultAreaUrl = "backbone/info/faultArea"; //故障发生地区字典数据
const queryAttachmentListUrl = "backbone/info/attachmentList"; //附件组编码查询附件列表
const downloadAppendixUrl = "commonDict/attach/download"; //公共下载附件接口
const deleteFdFileUrl = "backbone/workflow/ackDefine/deleteAttach"; //删除附件接口
const manualFileUrl = "backbone/workflow/queryCircuitAttachment/excel";
const fluenceExcelUrl = "backbone/workflow/queryCutInfluenceExcel/excel"; //光缆excel
const provinceDictUrl = "backbone/info/provinceDict";

const oneKeyIvrNoticeUrl = "backbone/workflow/oneKeyIvr/oneKeyIvrNotice"; //一键IVR
const itCloudHangUpUrl = "backbone/workflow/it/cloud/applyInterrupt"; //IT云挂起/解挂 申请
const itCloudAuditUrl = "backbone/workflow/it/cloud/checkInterrupt"; //IT云挂起/解挂 审核
const itCloudAbendUrl = "backbone/workflow/it/cloud/applyInterrupt"; //IT云异常终止
const itCloudAbendAuditUrl = "backbone/workflow/it/cloud/checkInterrupt"; //IT云异常终止 审核
const commCloudHangUpUrl = "commCloud/workflow/applyInterrupt"; //通信云挂起/解挂 申请
const commCloudAuditUrl = "commCloud/workflow/checkInterrupt"; //通信云挂起/解挂 审核
const commCloudAbendUrl = "commCloud/workflow/applyInterrupt"; //通信云异常终止
const commCloudAbendAuditUrl = "commCloud/workflow/checkInterrupt"; //通信云异常终止 审核
const getEvaluationUrl = "/backbone/workflow/getEvaluation"; //是否已评价
const getUsersUrl = "backbone/workflow/oneKeyIvr/getUsers"; //一键IVR更据用户id获取用户信息

const apiDict = params => getJson(dictUrl, params);
const apiGetShowButton = params =>
  postJson(showButtonUrl, params, { timeout: 0 });
const apiGetWorkOrderInfo = params =>
  postJson(workOrderInfoUrl, params, { timeout: 0 });
const apiExportCommonOrder = params =>
  getJsonBlob(exportCommonOrderUrl, params);
const apiAccept = params => postJson(acceptUrl, params);
const apiQueryAlarmDetail = params => postJson(alarmDetailUrl, params);
const apiSyncClear = params => postJson(syncClearUrl, params);
const apiGetRelationDiagnosis = params => getJson(relationDiagnosisUrl, params);
const apiBackSingle = params => postFormData(backSingleUrl, params);
const apiQualitativeDetail = params => getJson(qualitativeDetailUrl, params);
const apiQualitative = params => postFormData(qualitativeUrl, params);
const apiQualitativeReview = params =>
  postFormData(qualitativeReviewUrl, params);
const apiStageFeedBack = params => postJson(stageFeedbackUrl, params);
const apiRevoke = params => postJson(revokeUrl, params);
const apiHangUp = params => postJson(hangUpUrl, params);
const apiAudit = params => postJson(auditUrl, params);
const apiAbend = params => postJson(abendUrl, params);
const apiAbendAudit = params => postJson(abendAuditUrl, params);
const apiAfterSingle = params => postFormData(afterSingleUrl, params);
const apiHaveRead = params => postJson(haveReadUrl, params);
const apiCirculated = params => postJson(circulatedUrl, params);
const apiCirculatedUserTree = params => getJson(circulatedUserTreeUrl, params);
const apiCirculatedOrgTree = params => getJson(circulatedOrgTreeUrl, params);
const apiGetFaultArea = params => getJson(faultAreaUrl, params);
const apiGetEvaluation = params => getJson(getEvaluationUrl, params);
const apiDownloadAppendixFile = params =>
  getJsonBlob(downloadAppendixUrl, params);
const apiQueryAttachment = params => getJson(queryAttachmentListUrl, params);
const apiDeleteFdFile = params => delJson(deleteFdFileUrl, params);
const apiDownloadManualFile = params => postJsonBlob(manualFileUrl, params);
const apiGetProvinceDict = params => getJson(provinceDictUrl, params);
const apioneKeyIvrNotice = params => postJson(oneKeyIvrNoticeUrl, params);
const apiItCloudHangUp = params => postJson(itCloudHangUpUrl, params); //挂起解挂 申请
const apiItCloudAudit = params => postJson(itCloudAuditUrl, params); //挂起解挂 审核
const apiItCloudAbend = params => postJson(itCloudAbendUrl, params);
const apiItCloudAbendAudit = params => postJson(itCloudAbendAuditUrl, params);
const apiCommCloudHangUp = params => postJson(commCloudHangUpUrl, params);
const apiCommCloudAudit = params => postJson(commCloudAuditUrl, params);
const apiCommCloudAbend = params => postJson(commCloudAbendUrl, params);
const apiCommCloudAbendAudit = params =>
  postJson(commCloudAbendAuditUrl, params);
const apiShowAttachDownloadLink = params =>
  postJson(ShowAttachDownloadLinktUrl, params);
const apifluenceExcel = params => postJsonBlob(fluenceExcelUrl, params);
const apiGetUsers = params => postJson(getUsersUrl, params);
const apiReportUp = params => postJson(reportUpUrl, params);
const apiGetReportUpLog = params => getJson(getReportUpLogUrl, params);
const apiReportAudit = params => postJson(reportAuditUrl, params);
const apiReportTransfer = params =>
  postJson(reportTransferUrl, params, { timeout: 0 });
const apiTransferSubmit = params => postJson(transferSubmitUrl, params);

export {
  apiDict,
  apiGetShowButton,
  apiGetWorkOrderInfo,
  apiExportCommonOrder,
  apiAccept,
  apiQueryAlarmDetail,
  apiSyncClear,
  apiGetRelationDiagnosis,
  apiBackSingle,
  apiQualitativeDetail,
  apiQualitative,
  apiQualitativeReview,
  apiStageFeedBack,
  apiRevoke,
  apiHangUp,
  apiAudit,
  apiAbend,
  apiAbendAudit,
  apiAfterSingle,
  apiHaveRead,
  apiCirculated,
  apiCirculatedUserTree,
  apiCirculatedOrgTree,
  apiGetFaultArea,
  apiDownloadAppendixFile,
  apiQueryAttachment,
  apiDeleteFdFile,
  apiDownloadManualFile,
  apiGetProvinceDict,
  apioneKeyIvrNotice,
  apiGetUsers,
  apiItCloudHangUp,
  apiItCloudAudit,
  apiItCloudAbend,
  apiItCloudAbendAudit,
  apiCommCloudHangUp,
  apiCommCloudAudit,
  apiCommCloudAbend,
  apiCommCloudAbendAudit,
  apifluenceExcel,
  apiShowAttachDownloadLink,
  apiGetEvaluation,
  apiReportUp,
  apiGetReportUpLog,
  apiReportAudit,
  apiReportTransfer,
  apiTransferSubmit,
};
