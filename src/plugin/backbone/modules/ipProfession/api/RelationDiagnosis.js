import { getJson } from "@/utils/axios";

const relationDiagnosisUrl = "backbone/workflow/queryRelation"; //关联诊断
const analyzeRelationDiagnosisUrl = "commonDict/analyze/getCotents"; //关联诊断（分析上海AI）
const topoUrl = "/netfm3topo/topoapi/topoinfo";

const apiTopoIsShow = params => getJson(topoUrl, params);
const apiGetRelationDiagnosis = params => getJson(relationDiagnosisUrl, params);
const apiAnalyzeRelationDiagnosis = params =>
  getJson(analyzeRelationDiagnosisUrl, params);
export { apiGetRelationDiagnosis, apiAnalyzeRelationDiagnosis, apiTopoIsShow };
