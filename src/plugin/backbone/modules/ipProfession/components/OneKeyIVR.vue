<template>
  <el-dialog
    title="一键催办"
    :visible="dialogOneKeyIvrNotice"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="dialogOneKeyIvrNoticeClose"
    width="671px"
  >
    <el-form :model="oneKeyForm" :rules="rules">
      <el-form-item label="主送人:" prop="agentPeople">
        <div style="display: flex;">
          <div class="elTagBox" v-show="oneKeyForm.agentPeople.length > 0">
            <el-tag
              style="margin-bottom 10px; "
              :key="tag"
              v-for="tag in oneKeyForm.agentPeople"
              closable
              :disable-transitions="false"
              @close="handleCloseAgen(tag)"
            >
              {{ tag }}
            </el-tag>
          </div>
          <el-button
            style="height: 30px; margin-left 20px;"
            type="info"
            icon="el-icon-user"
            @click="onOpenPeopleDialog('lordSentDetermine')"
          ></el-button>
        </div>
      </el-form-item>
      <el-form-item label="抄送人:">
        <div style="display: flex;">
          <div class="elTagBox" v-show="oneKeyForm.copyPeople.length > 0">
            <el-tag
              :key="tag"
              v-for="tag in oneKeyForm.copyPeople"
              closable
              :disable-transitions="false"
              @close="handleCloseCopy(tag)"
              style="margin-bottom 10px; "
            >
              {{ tag }}
            </el-tag>
          </div>
          <el-button
            style="height: 30px; margin-left 20px;"
            type="info"
            icon="el-icon-user"
            @click="onOpenPeopleDialog('ccDetermine')"
          ></el-button>
        </div>
      </el-form-item>
      <el-form-item label="自定义发送号码:">
        <el-tag
          :key="tag"
          v-for="tag in oneKeyForm.customPhone"
          closable
          :disable-transitions="false"
          @close="handlePhoneClose(tag)"
        >
          {{ tag }}
        </el-tag>
        <el-input
          class="input-new-tag"
          v-if="inputVisible"
          v-model="inputValue"
          ref="saveTagInput"
          size="small"
          @keyup.enter.native="handleInputConfirm"
          @blur="handleInputConfirm"
        >
        </el-input>
        <el-button v-else class="button-new-tag" size="small" @click="showInput"
          >+ 添加号码</el-button
        >
      </el-form-item>
      <el-form-item label="发送内容:" prop="sentRemind">
        <div class="onKeyForm_content">
          <div class="onKeyForm_text_top">
            <p>主送模板:</p>
            <span class="box_top_name">【姓名...】</span>
            <el-input
              class="box_top_text"
              type="text"
              v-model="oneKeyForm.sentHello"
              :title="oneKeyForm.sentHello"
            />
            <span class="box_top_name">{{ sheetId }}</span>
            <el-input
              class="sentRemind"
              type="text"
              v-model="oneKeyForm.sentRemind"
              :title="oneKeyForm.sentRemind"
            />
          </div>
          <div class="onKeyForm_text_bottom">
            <p>抄送模板:</p>
            <span class="box_top_name">【姓名...】</span>
            <el-input
              class="box_bottom_text"
              type="text"
              v-model="oneKeyForm.copyHello"
              :title="oneKeyForm.copyHello"
            />
            <span class="box_top_name">{{ sheetId }}</span>
            <el-input
              class="copyRemind"
              type="text"
              v-model="oneKeyForm.copyRemind"
              :title="oneKeyForm.copyRemind"
            />
          </div>
        </div>
      </el-form-item>
      <el-form-item class="foolter_button">
        <el-button type="primary" @click="submitOneKeyIvrNotice"
          >提交</el-button
        >
        <el-button @click="resetOneKeyForm">重置</el-button>
      </el-form-item>
    </el-form>
    <dia-orgs-user-tree
      :title="diaPeople.title"
      :visible.sync="diaPeople.visible"
      :showOrgsTree="diaPeople.showOrgsTree"
      @on-save="onSavePeople"
      :appendToBody="diaPeople.appendToBody"
    />
  </el-dialog>
</template>

<script>
import DiaOrgsUserTree from "../../workOrder/components/DiaOrgsUserTree.vue";
import { apioneKeyIvrNotice } from "../../workOrder/workOrderWaitDetail/api/CommonApi";

export default {
  components: {
    DiaOrgsUserTree,
  },
  props: {
    dialogOneKeyIvrNotice: {
      type: Boolean,
      default: false,
    },
    sheetId: {
      type: String,
      default: "",
    },
    sheetCreateTime: {
      type: String,
      default: "",
    },
    common: {
      type: Object,
    },
  },

  data() {
    return {
      //ivr对象
      inputVisible: false,
      inputValue: "",
      oneKeyForm: {
        agentPeople: [
          // "小红-上海网络研发中心6-156518601063",
          // "笑话-上海网络研发中心5-156516071063",
          // "小刚-上海网络研发中心4-156516016063",
        ],
        copyPeople: [
          // "列里-上海网络研发中心3-1565160106g",
          // "噶工资-上海网络研发中心2-15651601066",
          // "狗蛋-上海网络研发中心1-156516010663",
        ],
        customPhone: [],
        sentHello: "你好,你有一张故障工单",
        sentRemind: "请尽快处理",
        copyHello: "你好,故障工单",
        copyRemind: "请督促相关人员尽快处理",
      },
      //ivr人员数
      diaPeople: {
        title: "",
        visible: false,
        saveName: "", //方法控制
        showOrgsTree: false, //是否显示组织树
        appendToBody: true, //嵌套dialog
        saveTitleMap: {
          lordSentDetermine: "主送人",
          ccDetermine: "抄送人",
        },
      },
      rules: {
        agentPeople: [{ required: true }],
        sentRemind: [{ required: true }],
      },
    };
  },
  watch: {
    "common.agentMan": {
      handler(newV) {
        if (newV) {
          this.oneKeyForm.agentPeople = newV.split(",");
        }
      },
      deep: true,
    },
    "common.copyMan": {
      handler(newV) {
        if (newV) {
          this.oneKeyForm.copyPeople = newV.split(",");
        }
      },
      deep: true,
    },
  },
  methods: {
    //ivr提交
    submitOneKeyIvrNotice() {
      if (this.oneKeyForm.agentPeople.length == 0) {
        this.$message({
          type: "warning",
          message: "请选择主送人",
        });
        return;
      }

      let agenName = [...this.oneKeyForm.agentPeople];
      let copyName = [...this.oneKeyForm.copyPeople];
      let aN = this.arrToString(
        agenName,
        this.oneKeyForm.sentHello,
        this.oneKeyForm.sentRemind
      );
      let cN = this.arrToString(
        copyName,
        this.oneKeyForm.copyHello,
        this.oneKeyForm.copyRemind
      );

      let copyPhone = this.setcopyPhone(this.oneKeyForm.copyPeople);
      let agentPhone = this.setagenPhone(this.oneKeyForm.agentPeople);
      let param = {
        woId: this.common.woId,
        sheetCreateTime: this.sheetCreateTime,
        copy: { text: cN, phone: copyPhone },
        agent: { text: aN, phone: agentPhone },
      };
      apioneKeyIvrNotice(param).then(res => {
        if (res.status == "0") {
          this.$message({
            type: "success",
            message: "提交成功",
          });
        }
      });
    },
    //主送电话号码参数
    setagenPhone(arr) {
      let phone = [];
      arr.forEach(el => {
        let [, , p] = el.split("-");
        phone.push(p);
      });

      let newPhone = phone.join(",");
      return newPhone;
    },
    setcopyPhone(arr) {
      let phone = [];
      arr.forEach(el => {
        let [, , p] = el.split("-");
        phone.push(p);
      });

      let newPhone = phone.join(",");
      let c = this.oneKeyForm.customPhone.join(",");
      if (c) {
        return newPhone + "," + c;
      }
      return newPhone;
    },

    //发送内荣处理
    arrToString(arr, helo, remind) {
      let agenN = [];
      arr.forEach(el => {
        let [n] = el.split("-");
        agenN.push(n);
      });
      let str = agenN.join();
      let text = str + helo + this.sheetId + remind;
      let newT = text.replace(/[^a-zA-Z\u4e00-\u9fa50-9]+/g, "");
      return newT;
    },
    //ivr重置
    resetOneKeyForm() {
      this.oneKeyForm = {
        agentPeople: this.common.agentMan
          ? this.common.agentMan.split(",")
          : [],
        copyPeople: this.common.copyMan ? this.common.copyMan.split(",") : [],
        customPhone: [],
        sentHello: "你好,你有一张故障工单",
        sentRemind: "请尽快处理",
        copyHello: "你好,故障工单",
        copyRemind: "请督促相关人员尽快处理",
      };
    },
    //ivr抄送人移除
    handleCloseAgen(tag) {
      this.oneKeyForm.agentPeople.splice(
        this.oneKeyForm.agentPeople.indexOf(tag),
        1
      );
    },
    //ivr抄送人移除
    handleCloseCopy(tag) {
      this.oneKeyForm.copyPeople.splice(
        this.oneKeyForm.copyPeople.indexOf(tag),
        1
      );
    },
    //ivr自定义号码清除
    handlePhoneClose(tag) {
      this.oneKeyForm.customPhone.splice(
        this.oneKeyForm.customPhone.indexOf(tag),
        1
      );
    },
    //ivr点击显示号码输入框
    showInput() {
      this.inputVisible = true;
      this.$nextTick(_ => {
        this.$refs.saveTagInput.$refs.input.focus();
      });
    },
    //ivr输入框事件
    handleInputConfirm() {
      let inputValue = this.inputValue;
      if (inputValue.length == "11" && Number(inputValue) != "NaN") {
        if (inputValue) {
          this.oneKeyForm.customPhone.push(inputValue);
        }
        this.inputVisible = false;
        this.inputValue = "";
      } else {
        this.$message({
          type: "warning",
          message: "请输入11位的手机号",
        });
      }
    },
    //ivr人员树总开关
    onOpenPeopleDialog(key) {
      this.diaPeople.visible = true;
      this.diaPeople.saveName = key;
      this.diaPeople.title = this.diaPeople.saveTitleMap[key];
    },
    //ivr人员树确定
    onSavePeople(val) {
      this[this.diaPeople.saveName](val);
    },
    //ivr著送人
    lordSentDetermine({ usersChecked, orgsChecked, selectionUser }) {},
    //ivr抄送人
    ccDetermine({ usersChecked, orgsChecked, selectionUser }) {},
    dialogOneKeyIvrNoticeClose() {
      this.$emit("setDialogOneKeyIvrNotice", false);
      this.resetOneKeyForm();
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-dialog {
  border: 1px solid #eaeefb;
  margin-top: 6vh;
  .el-dialog__header {
    border-bottom: 1px solid #eaeefb;
  }
  .foolter_button {
    .el-form-item__content {
      float: right;
    }
  }
  .elTagBox {
    display: flex;
    flex-direction: column;
    width: 370px;
    .el-tag {
      margin-bottom: 10px;
    }
  }
  .onKeyForm_content {
    width: 628px;
    height: 173px;
    border: 1px solid rgb(133, 133, 133);
    padding: 10px;
    overflow: hidden;
    .onKeyForm_text_top,
    .onKeyForm_text_bottom {
      p {
        margin: 0;
      }
      .box_top_name {
        color: rgb(133, 133, 133);
      }
      .sentRemind {
        width: 86px;
        .el-input__inner {
          border: none;
          padding: 0;
        }
      }
      .copyRemind {
        width: 150px;
        .el-input__inner {
          border: none;
          padding: 0;
        }
      }
      .box_bottom_text {
        width: 93px;
        .el-input__inner {
          border: none;
          padding: 0;
        }
      }
      .box_top_text {
        width: 146px;
        .el-input__inner {
          border: none;
          padding: 0;
        }
      }
    }
  }
}
</style>
