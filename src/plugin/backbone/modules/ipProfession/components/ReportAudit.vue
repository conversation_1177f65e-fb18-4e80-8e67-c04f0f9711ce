<template>
  <div>
    <el-form ref="auditForm" :model="auditForm" label-width="80px">
      <el-form-item
        label="审核结果"
        prop="opResult"
        :rules="{
          required: true,
          message: '请选择审核结果',
        }"
      >
        <el-radio-group v-model="auditForm.opResult">
          <el-radio label="同意">同意</el-radio>
          <el-radio label="拒绝">拒绝</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        label="审核意见"
        prop="processSuggestion"
        :rules="{
          required: true,
          message: '请填写审核意见',
        }"
      >
        <el-input
          type="textarea"
          :rows="2"
          placeholder="请填写审核意见"
          v-model="auditForm.processSuggestion"
        >
        </el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: right">
      <el-button
        type="primary"
        @click="handleAuditSubmit('auditForm')"
        v-loading.fullscreen.lock="auditFullScreenLoading"
        >提 交</el-button
      >
      <el-button @click="onResetAudit">重 置</el-button>
    </div>
  </div>
</template>
<script>
import { apiReportAudit } from "../api/CommonApi";
export default {
  name: "ReportAudit",
  props: {
    multipleSelection: Array,
  },
  data() {
    return {
      auditForm: {
        opResult: null,
        processSuggestion: null,
      },
      auditFullScreenLoading: false,
    };
  },
  mounted() {
    // console.log(this.common.professionalTypeName);
  },
  watch: {
    opContent(val) {
      this.opContent = val;
    },
  },
  methods: {
    handleAuditSubmit(formName) {
      if (this.multipleSelection.length <= 0) {
        this.$message.warning("请选择需要审核的故障报告");
        return;
      }
      this.$refs[formName].validate(valid => {
        if (valid) {
          let formData = new FormData();
          formData.append(
            "multipleSelection",
            JSON.stringify(this.multipleSelection)
          );
          formData.append("opResult", this.auditForm.opResult);
          formData.append(
            "processSuggestion",
            this.auditForm.processSuggestion
          );
          this.auditFullScreenLoading = true;
          apiReportAudit(formData)
            .then(res => {
              if (res.status == "0") {
                this.$emit("closeDialogReportAudit");
                this.$message.success(res.msg);
              } else {
                this.$message.error(res.msg);
              }
              this.auditFullScreenLoading = false;
            })
            .catch(error => {
              console.log(error);
              this.auditFullScreenLoading = false;
              this.$message.error("审核提交失败");
            });
        } else {
          return false;
        }
      });
    },
    onResetAudit() {
      this.auditForm = {
        ...this.$options.data,
      };
    },
  },
};
</script>
