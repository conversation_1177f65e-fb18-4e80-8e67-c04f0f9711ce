<template>
  <div class="qualitative">
    <el-form
      ref="qualitativeForm"
      :inline="false"
      class="demo-form-inline"
      :model="qualitativeForm"
      label-width="130px"
      :rules="qualitativeFormRule"
    >
      <el-card
        shadow="never"
        header="故障定性信息"
        :body-style="{ padding: '20px 8px' }"
        class="cus-card"
      >
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item
              label="故障所属专业:"
              prop="professionalType"
              :rules="{
                required: true,
                message: '请选择故障所属专业',
              }"
            >
              <dict-select
                :value.sync="qualitativeForm.professionalType"
                :dictId="60001"
                style="width: 100%"
                @change="changeProfessionalType"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障发生时间:" prop="alarmCreateTime" required>
              {{ qualitativeForm.alarmCreateTime }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障通知时间:" prop="sheetCreateTime" required>
              {{ qualitativeForm.sheetCreateTime }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="业务恢复时间:" prop="busRecoverTime">
              <el-date-picker
                v-model="qualitativeForm.busRecoverTime"
                type="datetime"
                placeholder="请选择时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
                style="width: 100%"
                @change="computerBusRecoverDuration"
                :picker-options="pickerOptions"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="业务恢复历时:" required>
              {{ second2Time(qualitativeForm.busRecoverDuration) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="故障结束时间:"
              prop="faultEndTime"
              :rules="{
                required: true,
                message: '请选择内容',
              }"
            >
              <el-date-picker
                v-model="qualitativeForm.faultEndTime"
                type="datetime"
                placeholder="请选择时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
                style="width: 100%"
                @change="computerFaultTreatmentTime"
                :picker-options="pickerOptions"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障处理历时:" required>
              {{ second2Time(this.qualitativeForm.faultDuration) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="故障发生地区:"
              prop="faultRegion"
              :rules="{
                required: true,
                message: '请选择内容',
              }"
            >
              <el-select
                style="width: 100%"
                placeholder="请选择内容"
                v-model="qualitativeForm.faultRegion"
                filterable
              >
                <el-option
                  v-for="(item, i) in faultRegionOptions"
                  :key="i"
                  :label="item.name"
                  :value="item.name"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障处理部门:" prop="dept" required>
              {{ qualitativeForm.dept }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="处理人:" prop="person" required>
              {{ qualitativeForm.person }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="挂起历时:" required>
              {{ second2Time(this.qualitativeForm.suspendDuration) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障处理净历时:" required>
              {{ second2Time(this.qualitativeForm.processDuration) }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item label="是否影响业务:" prop="isEffectBusiness">
              <el-radio-group
                v-model="qualitativeForm.isEffectBusiness"
                style="width: 100%"
              >
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item
              label="影响范围:"
              v-if="qualitativeForm.isEffectBusiness == '1'"
              prop="effectRange"
            >
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="qualitativeForm.effectRange"
                style="width: 100%"
                show-word-limit
                maxlength="255"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row
          type="flex"
          style="flex-wrap: wrap"
          :gutter="20"
          v-if="
            qualitativeForm.professionalType != '25' &&
            qualitativeForm.professionalType != '12' &&
            qualitativeForm.professionalType != '13' &&
            qualitativeForm.professionalType != '33' &&
            qualitativeForm.professionalType != '5'
          "
        >
          <el-col :span="8">
            <el-form-item
              label="是否基站退服:"
              prop="isSiteOffline"
              :rules="{
                required: true,
                message: '请选择是否基站退服',
              }"
            >
              <el-radio-group
                v-model="qualitativeForm.isSiteOffline"
                style="width: 100%"
              >
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item
              label="退服原因:"
              v-if="qualitativeForm.isSiteOffline == '1'"
              prop="siteOfflineReason"
              :rules="{
                required: qualitativeForm.isSiteOffline == '1' ? true : false,
                message: '请选择退服原因',
              }"
            >
              <dict-select
                :value.sync="qualitativeForm.siteOfflineReason"
                :dictId="10016"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card
        shadow="never"
        header="故障专业信息"
        :body-style="{ padding: '20px' }"
        style="margin-top: 20px"
        class="cus-card"
      >
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item
              label="故障分类:"
              prop="faultCate"
              v-if="
                qualitativeForm.professionalType != '0' &&
                qualitativeForm.professionalType != '-1' &&
                qualitativeForm.professionalType != '25' &&
                qualitativeForm.professionalType != '30'
              "
              :rules="{
                required: true,
                message: '请选择故障分类',
              }"
            >
              <dict-select
                :value.sync="qualitativeForm.faultCate"
                :dictId="faultCateDict"
                style="width: 100%"
                placeholder="请选择内容"
                @change="changeFaultCate"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="故障原因:"
              prop="faultReason"
              v-if="
                qualitativeForm.professionalType != '0' &&
                qualitativeForm.professionalType != '-1' &&
                qualitativeForm.professionalType != '25' &&
                qualitativeForm.professionalType != '30'
              "
              :rules="{
                required: true,
                message: '请选择故障原因',
              }"
            >
              <dict-select
                :value.sync="qualitativeForm.faultReason"
                :dictId="faultReasonDict"
                style="width: 100%"
                placeholder="请选择内容"
              />
            </el-form-item>
          </el-col>
          <template
            v-if="
              qualitativeForm.professionalType == '1' ||
              qualitativeForm.professionalType == '31' ||
              qualitativeForm.professionalType == '6' ||
              qualitativeForm.professionalType == '19' ||
              qualitativeForm.professionalType == '20' ||
              qualitativeForm.professionalType == '21' ||
              qualitativeForm.professionalType == '32' ||
              qualitativeForm.professionalType == '28'
            "
          >
            <el-col :span="8">
              <el-form-item label="网元名称:" prop="neName">
                <el-input
                  v-model="qualitativeForm.neName"
                  style="width: 100%"
                  maxlength="255"
                >
                </el-input>
              </el-form-item>
            </el-col>
          </template>
          <template
            v-if="
              qualitativeForm.professionalType != '0' &&
              qualitativeForm.professionalType != '-1' &&
              qualitativeForm.professionalType != '25' &&
              qualitativeForm.professionalType != '30' &&
              qualitativeForm.professionalType != '1' &&
              qualitativeForm.professionalType != '31' &&
              qualitativeForm.professionalType != '6' &&
              qualitativeForm.professionalType != '19' &&
              qualitativeForm.professionalType != '20' &&
              qualitativeForm.professionalType != '21' &&
              qualitativeForm.professionalType != '28' &&
              qualitativeForm.professionalType != '32' &&
              qualitativeForm.professionalType != '5' &&
              qualitativeForm.professionalType != '33' &&
              qualitativeForm.professionalType != '12' &&
              qualitativeForm.professionalType != '13'
            "
          >
            <el-col :span="8">
              <el-form-item
                label="设备类型:"
                prop="eqpType"
                :rules="{
                  required: true,
                  message: '请选择设备类型',
                }"
              >
                <dict-select
                  :value.sync="qualitativeForm.eqpType"
                  :dictId="diviceTypeDict"
                  style="width: 100%"
                  placeholder="请选择内容"
                />
              </el-form-item>
            </el-col>
          </template>
          <template
            v-if="
              qualitativeForm.professionalType == '5' ||
              qualitativeForm.professionalType == '33' ||
              qualitativeForm.professionalType == '12' ||
              qualitativeForm.professionalType == '13'
            "
          >
            <el-col :span="8">
              <el-form-item label="设备类型:">
                <dict-select
                  :value.sync="qualitativeForm.eqpType"
                  :dictId="diviceTypeDict"
                  style="width: 100%"
                  placeholder="请选择内容"
                />
              </el-form-item>
            </el-col>
          </template>
          <!-- <el-col :span="8">
            <el-form-item
              label="设备类型:"
              prop="eqpType"
              v-if="
                qualitativeForm.professionalType != '0' &&
                qualitativeForm.professionalType != '-1' &&
                qualitativeForm.professionalType != '25' &&
                qualitativeForm.professionalType != '30'
              "
              :rules="{
                required: true,
                message: '请选择设备类型',
              }"
            >
              <dict-select
                :value.sync="qualitativeForm.eqpType"
                :dictId="diviceTypeDict"
                style="width: 100%"
                placeholder="请选择内容"
              />
            </el-form-item>
          </el-col> -->
        </el-row>
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col
            :span="8"
            v-if="
              qualitativeForm.professionalType != '0' &&
              qualitativeForm.professionalType != '-1' &&
              qualitativeForm.professionalType != '25' &&
              qualitativeForm.professionalType != '30' &&
              qualitativeForm.professionalType != '1' &&
              qualitativeForm.professionalType != '33' &&
              qualitativeForm.professionalType != '6' &&
              qualitativeForm.professionalType != '19' &&
              qualitativeForm.professionalType != '20' &&
              qualitativeForm.professionalType != '21' &&
              qualitativeForm.professionalType != '32' &&
              qualitativeForm.professionalType != '28'
            "
          >
            <el-form-item label="设备名称:" prop="eqpName">
              <el-input
                placeholder="请输入内容"
                v-model="qualitativeForm.eqpName"
                style="width: 100%"
                maxlength="255"
              >
                <el-button
                  style="
                    background: #b50b14;
                    border-color: #b50b14;
                    color: #fff;
                    padding: 9px 20px;
                    top: -0.5px;
                    position: relative;
                  "
                  slot="append"
                  @click="deviceSelect"
                  >选择</el-button
                >
              </el-input>
              <form
                id="sub__device"
                name="sub__device"
                hidden="true"
                method="post"
                action="/resweb_jituan/union/resAssign.out?method=selectEquipment"
                target="_blank"
              >
                <input type="hidden" name="device" id="device" />
              </form>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="qualitativeForm.professionalType == '26'">
            <el-form-item label="紧急程度:" prop="emergencyLevel">
              <el-input
                v-model="qualitativeForm.emergencyLevel"
                :disabled="true"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="qualitativeForm.professionalType == '26'">
            <el-form-item label="MEC节点名称:" prop="mecNodeName">
              <el-input
                v-model="qualitativeForm.mecNodeName"
                style="width: 100%"
                maxlength="100"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="qualitativeForm.professionalType == '26'">
            <el-form-item
              label="是否硬件故障:"
              prop="isHardFault"
              :rules="{
                required: true,
                message: '请选择是否硬件故障',
              }"
            >
              <el-radio-group
                v-model="qualitativeForm.isHardFault"
                style="width: 100%"
              >
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="相关附件:" prop="attachmentName">
              <el-tag
                class="fileName_style_download"
                closable
                v-for="(item, index) in fddxFileArr"
                :key="index"
                @close="closeAndDeleteFile(item)"
                @click="previewAppendixFile(item)"
                :title="item.name"
              >
                <div class="text-truncate">{{ item.name }}</div>
              </el-tag>
              <el-tag
                class="fileName_style"
                closable
                v-for="(item, index) in importForm.relatedFilesFileList"
                :key="index"
                @close="closeFile(item)"
                :title="item.name"
              >
                <div class="text-truncate">{{ item.name }}</div>
              </el-tag>
              <el-button size="mini" type="primary" @click="relatedFilesBrowse"
                >+上传附件</el-button
              >
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="是否派单准确:"
              v-if="qualitativeForm.professionalType == '7'"
              prop="isSentAccurately"
              :rules="{
                required: true,
                message: '请选择是否派单准确',
              }"
            >
              <dict-select
                :value.sync="qualitativeForm.isSentAccurately"
                :dictId="60006"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              label="故障厂家:"
              prop="vendor"
              v-if="
                qualitativeForm.professionalType == '2' ||
                qualitativeForm.professionalType == '4' ||
                qualitativeForm.professionalType == '7' ||
                qualitativeForm.professionalType == '10' ||
                qualitativeForm.professionalType == '11'
              "
              :rules="{
                required: true,
                message: '请选择故障厂家',
              }"
            >
              <el-radio-group v-model="qualitativeForm.vendor">
                <el-radio
                  v-for="(item, i) in vendorOptions"
                  :key="i"
                  :label="item.dictCode"
                  >{{ item.dictName }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="故障原因描述:" prop="falutReasonDesc">
              <el-input
                maxlength="500"
                show-word-limit
                type="textarea"
                :rows="2"
                v-model="qualitativeForm.falutReasonDesc"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注:" prop="falutComment">
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="qualitativeForm.falutComment"
                style="width: 100%"
                show-word-limit
                maxlength="255"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: center">
      <el-button
        type="primary"
        @click="handleSubmit('qualitativeForm')"
        v-loading.fullscreen.lock="qualitativeFullscreenLoading"
        >提交</el-button
      >
      <el-button @click="onReset">重 置</el-button>
    </div>
    <el-dialog
      width="420px"
      title="附件选择"
      :visible.sync="relatedFilesDialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <file-upload
        @change="changeFileData"
        @cancel="closeAttachmentDialog"
      ></file-upload>
    </el-dialog>

    <!-- 使用图片预览组件 -->
    <image-preview
      :visible.sync="imagePreviewVisible"
      :file-data="currentPreviewFile"
      preview-api="/commonDict/attach/download"
      :use-custom-download="true"
      @download="downloadCurrentFile"
      @close="imagePreviewVisible = false"
    ></image-preview>
  </div>
</template>
<script>
import moment from "moment";
import { mapGetters } from "vuex";
import DictSelect from "../../workOrder/components/DictSelect.vue";
import { apiQualitative, apiGetFaultArea } from "../api/BackSingle";
import { apiQualitativeDetail } from "../api/QualitativeReview";
import { apiDict, apiGetOrgInfo } from "../../workOrder/api/CommonApi";
import FileUpload from "../../workOrder/components/FileUpload.vue";
import ImagePreview from "@plugin/backbone/components/ImagePreview.vue";
import {
  apiDownloadAppendixFile,
  apiDeleteFdFile,
} from "../../workOrder/workOrderWaitDetail/api/CommonApi";
import { mixin } from "../../../../../mixins";
export default {
  name: "Qualitative",
  props: {
    common: Object,
    workItemId: [String, Number],
    isUploadReport: [String, Number],
  },
  components: { DictSelect, FileUpload, ImagePreview },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  mixins: [mixin],
  data() {
    var validBusRecoverTime = (rule, value, callback) => {
      if (this.qualitativeForm.busRecoverTime) {
        let seconds3 = moment(
          this.qualitativeForm.busRecoverTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(moment(new Date(), "YYYY-MM-DD HH:mm:ss"), "seconds");
        let seconds4 = moment(
          this.qualitativeForm.busRecoverTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.common.failureTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        if (seconds3 > 0 || seconds4 <= 0) {
          callback(
            new Error(
              "当前时间>=故障代通时间>=故障发生时间，请重新检查后选择正确时间"
            )
          );
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    var validFaultEndTime = (rule, value, callback) => {
      if (value === "" || value === null) {
        callback(new Error("请选择故障结束时间"));
      } else {
        if (this.qualitativeForm.faultEndTime) {
          let seconds3 = moment(
            this.qualitativeForm.faultEndTime,
            "YYYY-MM-DD HH:mm:ss"
          ).diff(moment(new Date(), "YYYY-MM-DD HH:mm:ss"), "seconds");
          let seconds4 = moment(
            this.qualitativeForm.faultEndTime,
            "YYYY-MM-DD HH:mm:ss"
          ).diff(
            moment(this.common.failureTime, "YYYY-MM-DD HH:mm:ss"),
            "seconds"
          );
          if (seconds3 > 0 || seconds4 <= 0) {
            callback(
              new Error(
                "当前时间>=故障结束时间>=故障发生时间，请重新检查后选择正确时间"
              )
            );
          } else {
            callback();
          }
        } else {
          callback();
        }
      }
    };
    var validAttachment = (rule, value, callback) => {
      if (this.common.isUploadReport == "1") {
        if (
          this.importForm.relatedFilesFileList.length > 0 ||
          this.fddxFileArr.length > 0
        ) {
          this.qualitativeForm.attachmentName = "xlFile";
          callback();
        } else {
          this.qualitativeForm.attachmentName = null;
          callback(new Error("请添加相关附件"));
        }
      } else {
        callback();
      }
    };
    return {
      sumBtnShow: true,
      qualitativeForm: {
        woId: null,
        workItemId: null,
        processInstId: null,
        processDefId: null,
        professionalType: null,
        alarmCreateTime: null,
        sheetCreateTime: null,
        busRecoverTime: null, //业务恢复时间
        busRecoverDuration: 0, //业务恢复历时
        faultEndTime: null,
        faultDuration: 0, //故障处理历时
        faultRegion: null,
        dept: null,
        person: null,
        suspendDuration: 0, //挂起历时 单位秒
        processDuration: 0, //故障处理净历时 单位秒
        isEffectBusiness: null,
        effectRange: null,
        //故障专业信息
        faultCate: null,
        faultReason: null,
        opticFiber: null,
        relatedFiles: null,
        effectSystem: null,
        falutReasonDesc: this.common.faultCauseDescription || "",
        falutComment: null,
        eqpType: null,
        eqpName: "",
        actionName: "",
        busName: null, //业务名称
        isHardFault: null, //是否硬件故障
        linkId: null,
        appendix: null,
        isSiteOffline: null,
        siteOfflineReason: null,
        isSentAccurately: null,
        vendor: null,
        attachmentName: null,
        emergencyLevel: null,
        mecNodeName: null,
        neName: null,
      },
      vendorOptions: [],
      faultCateDict: 60100,
      faultReasonDict: 0,
      diviceTypeDict: 0,
      areaCode: null, //区域编码
      category: null, //省份返单 OR 地市返单
      //关联附件
      relatedFilesDialogVisible: false,
      importForm: {
        relatedFilesFileList: [],
      },
      qualitativeFullscreenLoading: false,
      faultRegionOptions: [],
      fdFileXlArr: [],
      fdFileDlArr: [],
      faultReasonDictId: 0, //故障原因 多字典ID
      cardTypeDictId: 0, //板卡类型 多字典ID
      userData: null,
      timeRange: "00:00:00 - 23:59:59",
      createTimeDate: "2022-10-20",
      pickerOptions: {
        disabledDate: this.disabledDate,
        selectableRange: this.timeRange,
      },
      startTimeRange: "",
      startTimeRangeByFaultEndTime: "",

      qualitativeFormRule: {
        busRecoverTime: [{ validator: validBusRecoverTime, required: true }],
        faultEndTime: [{ validator: validFaultEndTime, required: true }],
        neName: [
          {
            validator: this.checkLength,
            max: 255,
            form: "qualitativeForm",
            messsage: "已超过填写字数上限",
          },
        ],
        falutReasonDesc: [
          {
            required: true,
            message: "请输入内容",
          },
          {
            validator: this.checkLength,
            max: 500,
            form: "qualitativeForm",
            messsage: "已超过填写字数上限",
          },
        ],
        falutComment: [
          {
            validator: this.checkLength,
            max: 255,
            form: "qualitativeForm",
            messsage: "已超过填写字数上限",
          },
        ],
        eqpName: [
          {
            validator: this.checkLength,
            max: 255,
            form: "qualitativeForm",
            messsage: "已超过填写字数上限",
          },
        ],
        mecNodeName: [
          {
            validator: this.checkLength,
            max: 100,
            form: "qualitativeForm",
            messsage: "已超过填写字数上限",
          },
        ],
        effectRange: [
          {
            required: true,
            message: "请填写影响范围",
            trigger: "blur",
          },
          {
            validator: this.checkLength,
            max: 255,
            form: "qualitativeForm",
            messsage: "已超过填写字数上限",
          },
        ],
      },
      viewsOnContentShow: false,
      faultReasonOption: [],
      fddxFileArr: [],
      // 图片预览相关
      imagePreviewVisible: false,
      currentPreviewFile: {},
    };
  },
  watch: {
    // "qualitativeForm.lastClearTime": {
    //   handler(val) {
    //     if (val) {
    //       let valDate = val.split(" ")[0];
    //       let originDate = this.common.failureTime.split(" ")[0];
    //       let now = new Date();
    //       now.setMinutes(now.getMinutes() + 1, now.getSeconds(), 0);
    //       valDate = valDate.replace(/\-/g, "/");
    //       originDate = originDate.replace(/\-/g, "/");
    //       let year = now.getFullYear();
    //       let month = now.getMonth() + 1;
    //       let date = now.getDate();
    //       let nowDate =
    //         year + "/" + this.addZero(month) + "/" + this.addZero(date);
    //       let valDateUnix = Date.parse(valDate);
    //       let originDateUnix = Date.parse(originDate);
    //       let nowDateUnix = Date.parse(nowDate);

    //       let array = this.qualitativeForm.alarmCreateTime.split(" ");
    //       let createTime = array[1];
    //       let hour = now.getHours();
    //       let minute = now.getMinutes();
    //       let second = now.getSeconds();
    //       let nowTime =
    //         this.addZero(hour) +
    //         ":" +
    //         this.addZero(minute) +
    //         ":" +
    //         this.addZero(second);
    //       if (valDateUnix == originDateUnix && originDateUnix == nowDateUnix) {
    //         this.timeRange = createTime + " - " + nowTime;
    //         this.pickerOptions.selectableRange = this.timeRange;
    //       } else if (
    //         valDateUnix == originDateUnix &&
    //         originDateUnix < nowDateUnix
    //       ) {
    //         this.timeRange = createTime + " - 23:59:59";
    //         this.pickerOptions.selectableRange = this.timeRange;
    //       } else if (
    //         valDateUnix > originDateUnix &&
    //         valDateUnix < nowDateUnix
    //       ) {
    //         this.timeRange = "00:00:00 - 23:59:59";
    //         this.pickerOptions.selectableRange = this.timeRange;
    //       } else if (
    //         valDateUnix > originDateUnix &&
    //         valDateUnix == nowDateUnix
    //       ) {
    //         this.timeRange = "00:00:00 - " + nowTime;
    //         this.pickerOptions.selectableRange = this.timeRange;
    //       }
    //     }
    //   },
    //   deep: true,
    // },
    "qualitativeForm.faultEndTime": {
      handler(val) {
        let valDate = val.split(" ")[0];
        let originDate = this.common.failureTime.split(" ")[0];
        let now = new Date();
        now.setMinutes(now.getMinutes() + 1, now.getSeconds(), 0);
        valDate = valDate.replace(/\-/g, "/");
        originDate = originDate.replace(/\-/g, "/");
        let year = now.getFullYear();
        let month = now.getMonth() + 1;
        let date = now.getDate();
        let nowDate =
          year + "/" + this.addZero(month) + "/" + this.addZero(date);
        let valDateUnix = Date.parse(valDate);
        let originDateUnix = Date.parse(originDate);
        let nowDateUnix = Date.parse(nowDate);

        let array = this.qualitativeForm.alarmCreateTime.split(" ");
        let createTime = array[1];
        let hour = now.getHours();
        let minute = now.getMinutes();
        let second = now.getSeconds();
        let nowTime =
          this.addZero(hour) +
          ":" +
          this.addZero(minute) +
          ":" +
          this.addZero(second);
        if (valDateUnix == originDateUnix && originDateUnix == nowDateUnix) {
          this.timeRange = createTime + " - " + nowTime;
          this.pickerOptions.selectableRange = this.timeRange;
        } else if (
          valDateUnix == originDateUnix &&
          originDateUnix < nowDateUnix
        ) {
          this.timeRange = createTime + " - 23:59:59";
          this.pickerOptions.selectableRange = this.timeRange;
        } else if (valDateUnix > originDateUnix && valDateUnix < nowDateUnix) {
          this.timeRange = "00:00:00 - 23:59:59";
          this.pickerOptions.selectableRange = this.timeRange;
        } else if (valDateUnix > originDateUnix && valDateUnix == nowDateUnix) {
          this.timeRange = "00:00:00 - " + nowTime;
          this.pickerOptions.selectableRange = this.timeRange;
        }
      },
      deep: true,
    },
  },
  mounted() {
    this.qualitativeForm.alarmCreateTime = this.common.failureTime;
    this.qualitativeForm.sheetCreateTime = this.common.failureInformTime;
    this.qualitativeForm.person = this.userInfo.realName;
    this.qualitativeForm.workItemId = this.workItemId;
    this.qualitativeForm.woId = this.common.woId;
    this.qualitativeForm.processInstId = this.common.processInstId;
    this.qualitativeForm.processDefId = this.common.processDefId;

    this.qualitativeDetail();
    this.getOrgInfo();
    this.userData = JSON.parse(this.userInfo.attr2);
    this.resourceBackInit(this.qualitativeForm);
    let array = this.qualitativeForm.alarmCreateTime.split(" ");
    this.createTimeDate = array[0];
  },
  methods: {
    changeProfessionalType(type) {
      if (type != "init") {
        this.qualitativeForm.faultCate = "";
        this.qualitativeForm.eqpType = "";
      }
      this.getVendorOptions(type);
      if (
        this.qualitativeForm.professionalType == "1" ||
        this.qualitativeForm.professionalType == "31" ||
        this.qualitativeForm.professionalType == "6" ||
        this.qualitativeForm.professionalType == "19" ||
        this.qualitativeForm.professionalType == "20" ||
        this.qualitativeForm.professionalType == "21" ||
        this.qualitativeForm.professionalType == "32" ||
        this.qualitativeForm.professionalType == "28"
      ) {
        this.faultCateDict = "60100";
      } else if (
        this.qualitativeForm.professionalType == "2" ||
        this.qualitativeForm.professionalType == "5" ||
        this.qualitativeForm.professionalType == "33" ||
        this.qualitativeForm.professionalType == "10" ||
        this.qualitativeForm.professionalType == "11" ||
        this.qualitativeForm.professionalType == "12" ||
        this.qualitativeForm.professionalType == "13"
      ) {
        this.faultCateDict = "60101";
      } else if (this.qualitativeForm.professionalType == "4") {
        this.faultCateDict = "60102";
      } else if (this.qualitativeForm.professionalType == "7") {
        this.faultCateDict = "60103";
      } else if (this.qualitativeForm.professionalType == "26") {
        this.faultCateDict = "60134";
      }
      if (
        this.qualitativeForm.professionalType == "1" ||
        this.qualitativeForm.professionalType == "31" ||
        this.qualitativeForm.professionalType == "32" ||
        this.qualitativeForm.professionalType == "28"
      ) {
        this.diviceTypeDict = "60127";
      } else if (this.qualitativeForm.professionalType == "6") {
        this.diviceTypeDict = "60128";
      } else if (
        this.qualitativeForm.professionalType == "2" ||
        this.qualitativeForm.professionalType == "4" ||
        this.qualitativeForm.professionalType == "5" ||
        this.qualitativeForm.professionalType == "33" ||
        this.qualitativeForm.professionalType == "10" ||
        this.qualitativeForm.professionalType == "11" ||
        this.qualitativeForm.professionalType == "12" ||
        this.qualitativeForm.professionalType == "13"
      ) {
        this.diviceTypeDict = "60129";
      } else if (this.qualitativeForm.professionalType == "7") {
        this.diviceTypeDict = "60130";
      } else if (
        this.qualitativeForm.professionalType == "20" ||
        this.qualitativeForm.professionalType == "21"
      ) {
        this.diviceTypeDict = "60131";
      } else if (this.qualitativeForm.professionalType == "19") {
        this.diviceTypeDict = "60132";
      } else if (this.qualitativeForm.professionalType == "26") {
        this.diviceTypeDict = "60136";
      }

      this.changeFaultCate(type);
    },
    getVendorOptions(type) {
      if (type != "init") {
        this.qualitativeForm.vendor = null;
      }
      let param = {
        dictTypeCode: "60124",
      };
      if (
        this.qualitativeForm.professionalType == "2" ||
        this.qualitativeForm.professionalType == "10" ||
        this.qualitativeForm.professionalType == "11"
      ) {
        this.$set(param, "dictTypeCode", "60124");
      } else if (this.qualitativeForm.professionalType == "4") {
        this.$set(param, "dictTypeCode", "60125");
      } else if (this.qualitativeForm.professionalType == "7") {
        this.$set(param, "dictTypeCode", "60126");
      }
      apiDict(param)
        .then(res => {
          if (res.code == "200") {
            this.vendorOptions = res?.data ?? [];
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    //时间对比
    disabledDate(time) {
      let now = moment(this.createTimeDate);
      let a = moment(now).subtract(1, "days");
      let today = moment(a).valueOf();
      return time.getTime() <= today || time.getTime() - 8.64e6 >= Date.now();
    },
    addZero(s) {
      return s < 10 ? "0" + s : s;
    },
    dealDisabledDate(time) {
      let self = this;
      let beginDate = moment(self.common.failureInformTime).format(
        "YYYY-MM-DD"
      );
      let endDate = moment(Date.now()).format("YYYY-MM-DD");
      let arr = this.getAllDays(beginDate, endDate);
      const timeFormat = moment(time).format("YYYY-MM-DD");
      if (arr.indexOf(timeFormat) >= 0) {
        return false;
      }
      return true;
    },
    dealDisabledDateDt(time) {
      let self = this;
      let beginDate;
      let endDate;
      if (self.qualitativeForm.faultEndTime) {
        beginDate = moment(self.common.failureInformTime).format("YYYY-MM-DD");
        endDate = moment(self.qualitativeForm.faultEndTime).format(
          "YYYY-MM-DD"
        );
      } else {
        beginDate = moment(self.common.failureInformTime).format("YYYY-MM-DD");
        endDate = moment(Date.now()).format("YYYY-MM-DD");
      }
      let arr = this.getAllDays(beginDate, endDate);
      const timeFormat = moment(time).format("YYYY-MM-DD");
      if (arr.indexOf(timeFormat) >= 0) {
        return false;
      }
      return true;
    },
    getAllDays(begin_date, end_date) {
      const errArr = [],
        resultArr = [],
        dateReg = /^[2]\d{3}-[01]\d-[0123]\d$/;

      if (
        typeof begin_date !== "string" ||
        begin_date === "" ||
        !dateReg.test(begin_date)
      ) {
        return errArr;
      }

      if (
        typeof end_date !== "string" ||
        end_date === "" ||
        !dateReg.test(end_date)
      ) {
        return errArr;
      }

      try {
        const beginTimestamp = Date.parse(new Date(begin_date)),
          endTimestamp = Date.parse(new Date(end_date));

        // 开始日期小于结束日期
        if (beginTimestamp > endTimestamp) {
          return errArr;
        }

        // 开始日期等于结束日期
        if (beginTimestamp === endTimestamp) {
          resultArr.push(begin_date);
          return resultArr;
        }

        let tempTimestamp = beginTimestamp,
          tempDate = begin_date;

        // 新增日期是否和结束日期相等， 相等跳出循环
        while (tempTimestamp !== endTimestamp) {
          resultArr.push(tempDate);

          // 增加一天
          tempDate = moment(tempTimestamp).add(1, "d").format("YYYY-MM-DD");

          // 将增加时间变为时间戳
          tempTimestamp = Date.parse(new Date(tempDate));
        }

        // 将最后一天放入数组
        resultArr.push(end_date);
        return resultArr;
      } catch (err) {
        return errArr;
      }
    },
    getOrgInfo() {
      apiGetOrgInfo()
        .then(res => {
          if (res.status == "0") {
            this.qualitativeForm.dept = res?.data?.orgInfo?.fullOrgName ?? "";
            this.qualitativeForm.areaCode = res?.data?.orgInfo?.areaCode ?? "";
            this.qualitativeForm.category = res?.data?.category ?? "";
            this.getFaultAreaOptions();
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    getFaultAreaOptions() {
      let param = {
        areaCode: this.qualitativeForm.areaCode,
        category: this.qualitativeForm.category,
      };
      apiGetFaultArea(param)
        .then(res => {
          if (res.status == "0") {
            this.faultRegionOptions = res?.data ?? [];
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    qualitativeDetail() {
      let param = {
        opType: 1,
        workItemId: this.qualitativeForm.workItemId,
        woId: this.qualitativeForm.woId,
      };
      apiQualitativeDetail(param)
        .then(res => {
          if (res.status == "0") {
            this.qualitativeForm = res?.data ?? {};
            this.qualitativeForm.emergencyLevel = this.common.emergencyLevel;
            this.changeProfessionalType("init");
            if (res.data.appendix) {
              this.fddxFileArr = JSON.parse(this.qualitativeForm.appendix);
              this.qualitativeForm.attachmentName = "xlFile";
            }
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    relatedFilesBrowse() {
      this.relatedFilesDialogVisible = true;
    },
    changeFileData(data) {
      this.qualitativeForm.relatedFiles = data.fileName;
      this.qualitativeForm.attachmentName = data.fileName;
      this.importForm.relatedFilesFileList = data.attachmentFileList;
      this.relatedFilesDialogVisible = false;
    },

    closeAttachmentDialog() {
      this.relatedFilesDialogVisible = false;
    },
    computerBusRecoverDuration() {
      if (this.qualitativeForm.busRecoverTime) {
        let days = moment(
          this.qualitativeForm.busRecoverTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.qualitativeForm.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        this.qualitativeForm.busRecoverDuration = days;
      } else {
        this.qualitativeForm.busRecoverDuration = 0;
      }
    },
    second2Time(days) {
      return this.showTimeNew(Math.abs(days));
    },
    computerFaultTreatmentTime() {
      let days = moment(
        this.qualitativeForm.faultEndTime,
        "YYYY-MM-DD HH:mm:ss"
      ).diff(
        moment(this.qualitativeForm.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
        "seconds"
      );
      this.qualitativeForm.faultDuration = days;

      //故障处理净历时  （故障结束时间-故障发生时间）-挂起历时(秒) 换算成-天-小时-分钟-秒
      if (this.qualitativeForm.suspendDuration == 0) {
        this.qualitativeForm.processDuration = this.qualitativeForm.faultDuration;
      } else {
        let seconds = moment(
          this.qualitativeForm.faultEndTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.qualitativeForm.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        this.qualitativeForm.processDuration =
          seconds - this.qualitativeForm.suspendDuration;
      }
    },
    changeFaultCate(type) {
      if (type != "init") {
        this.qualitativeForm.faultReason = "";
      }
      console.log(this.qualitativeForm.professionalType);
      if (
        (this.qualitativeForm.professionalType == "1" ||
          this.qualitativeForm.professionalType == "31" ||
          this.qualitativeForm.professionalType == "28" ||
          this.qualitativeForm.professionalType == "32" ||
          this.qualitativeForm.professionalType == "19") &&
        this.qualitativeForm.faultCate == "1"
      ) {
        this.faultReasonDict = "60104";
      } else if (
        (this.qualitativeForm.professionalType == "1" ||
          this.qualitativeForm.professionalType == "31" ||
          this.qualitativeForm.professionalType == "28" ||
          this.qualitativeForm.professionalType == "32" ||
          this.qualitativeForm.professionalType == "19") &&
        this.qualitativeForm.faultCate == "2"
      ) {
        this.faultReasonDict = "60105";
      } else if (
        (this.qualitativeForm.professionalType == "1" ||
          this.qualitativeForm.professionalType == "31" ||
          this.qualitativeForm.professionalType == "28" ||
          this.qualitativeForm.professionalType == "32" ||
          this.qualitativeForm.professionalType == "19") &&
        this.qualitativeForm.faultCate == "3"
      ) {
        this.faultReasonDict = "60106";
      } else if (
        this.qualitativeForm.professionalType == "6" &&
        this.qualitativeForm.faultCate == "1"
      ) {
        this.faultReasonDict = "60108";
      } else if (
        this.qualitativeForm.professionalType == "6" &&
        this.qualitativeForm.faultCate == "2"
      ) {
        this.faultReasonDict = "60109";
      } else if (
        this.qualitativeForm.professionalType == "6" &&
        this.qualitativeForm.faultCate == "3"
      ) {
        this.faultReasonDict = "60110";
      } else if (
        (this.qualitativeForm.professionalType == "5" ||
          this.qualitativeForm.professionalType == "33" ||
          this.qualitativeForm.professionalType == "11") &&
        this.qualitativeForm.faultCate == "1"
      ) {
        this.faultReasonDict = "60111";
      } else if (
        (this.qualitativeForm.professionalType == "2" ||
          this.qualitativeForm.professionalType == "5" ||
          this.qualitativeForm.professionalType == "33" ||
          this.qualitativeForm.professionalType == "10" ||
          this.qualitativeForm.professionalType == "11" ||
          this.qualitativeForm.professionalType == "12" ||
          this.qualitativeForm.professionalType == "13") &&
        this.qualitativeForm.faultCate == "2"
      ) {
        this.faultReasonDict = "60112";
      } else if (
        (this.qualitativeForm.professionalType == "5" ||
          this.qualitativeForm.professionalType == "33" ||
          this.qualitativeForm.professionalType == "10" ||
          this.qualitativeForm.professionalType == "11" ||
          this.qualitativeForm.professionalType == "12" ||
          this.qualitativeForm.professionalType == "13") &&
        this.qualitativeForm.faultCate == "3"
      ) {
        this.faultReasonDict = "60113";
      } else if (
        this.qualitativeForm.professionalType == "2" &&
        this.qualitativeForm.faultCate == "3"
      ) {
        this.faultReasonDict = "60114";
      } else if (
        (this.qualitativeForm.professionalType == "10" ||
          this.qualitativeForm.professionalType == "12" ||
          this.qualitativeForm.professionalType == "13") &&
        this.qualitativeForm.faultCate == "1"
      ) {
        this.faultReasonDict = "60115";
      } else if (
        this.qualitativeForm.professionalType == "2" &&
        this.qualitativeForm.faultCate == "1"
      ) {
        this.faultReasonDict = "60116";
      } else if (
        this.qualitativeForm.professionalType == "20" ||
        this.qualitativeForm.professionalType == "21"
      ) {
        this.faultReasonDict = "60117";
      } else if (
        this.qualitativeForm.professionalType == "7" &&
        this.qualitativeForm.faultCate == "1"
      ) {
        this.faultReasonDict = "60118";
      } else if (
        this.qualitativeForm.professionalType == "7" &&
        this.qualitativeForm.faultCate == "2"
      ) {
        this.faultReasonDict = "60119";
      } else if (
        this.qualitativeForm.professionalType == "7" &&
        this.qualitativeForm.faultCate == "3"
      ) {
        this.faultReasonDict = "60120";
      } else if (
        this.qualitativeForm.professionalType == "7" &&
        (this.qualitativeForm.faultCate == "4" ||
          this.qualitativeForm.faultCate == "5")
      ) {
        this.faultReasonDict = "60121";
      } else if (
        this.qualitativeForm.professionalType == "4" &&
        (this.qualitativeForm.faultCate == "1" ||
          this.qualitativeForm.faultCate == "31" ||
          this.qualitativeForm.faultCate == "3")
      ) {
        this.faultReasonDict = "60122";
      } else if (
        this.qualitativeForm.professionalType == "4" &&
        this.qualitativeForm.faultCate == "2"
      ) {
        this.faultReasonDict = "60123";
      } else if (this.qualitativeForm.professionalType == "26") {
        this.faultReasonDict = "60135";
      } else if (
        null != this.qualitativeForm.faultCate &&
        this.qualitativeForm.faultCate != ""
      ) {
        this.faultReasonDict = "60107";
      }
    },
    handleSubmit(formName) {
      this.$refs[formName].validate((valid, a) => {
        if (this.uncheck(a)) {
          this.qualitativeFullscreenLoading = true;
          this.$set(this.qualitativeForm, "actionName", "定性");
          let formData = new FormData();
          if (this.importForm.relatedFilesFileList.length > 0) {
            for (let item of this.importForm.relatedFilesFileList) {
              formData.append("files", item.raw);
            }
          } else {
            formData.append("files", "");
          }

          formData.append("jsonParam", JSON.stringify(this.qualitativeForm));
          apiQualitative(formData)
            .then(res => {
              if (res.status == "0") {
                this.$message.success("提交定性成功");
                this.onReset();
                this.$emit("qualitativeSubmit", res.data);
              } else {
                this.$message.error("提交定性失败");
              }
              this.qualitativeFullscreenLoading = false;
            })
            .catch(error => {
              console.log(error);
              this.$message.error("提交定性失败");
              this.qualitativeFullscreenLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    resetFaultCateChange() {
      this.qualitativeForm.circuitFaultReason = null;
      this.qualitativeForm.opticFiber = null;
      this.qualitativeForm.faultRange = null;
      this.qualitativeForm.maintainDept = null;
      this.qualitativeForm.isProtected = null;
      this.qualitativeForm.isProtectedValid = null;
      this.importForm.relatedFilesFileList = [];
      this.qualitativeForm.effectSystem = null;
      this.qualitativeForm.effectCircuit = null;
      this.qualitativeForm.falutReasonDesc = null;
      this.qualitativeForm.falutComment = null;
      this.qualitativeForm.eqpType = null;
      this.qualitativeForm.eqpName = null;
      this.qualitativeForm.eqpFaultReason = null;
      this.qualitativeForm.cardType = null;
      this.qualitativeForm.isPersonInRoom = null;
      this.qualitativeForm.hasBackupPart = null;
      this.qualitativeForm.supportSystem = null;
      this.qualitativeForm.vendor = null;
    },
    closeAndDeleteFile(tag) {
      this.deleteFile(tag);
    },
    deleteFile(tag) {
      // this.fddxFileArr.splice(this.fddxFileArr.indexOf(tag), 1);
      // this.qualitativeForm.appendix = JSON.stringify(this.fddxFileArr);
      let param = {
        attId: tag.id,
        linkId: this.qualitativeForm.linkId,
      };
      apiDeleteFdFile(param)
        .then(res => {
          if (res.status == "0") {
            this.fddxFileArr.splice(this.fddxFileArr.indexOf(tag), 1);
            this.qualitativeForm.appendix = JSON.stringify(this.fddxFileArr);
            if (
              this.importForm.relatedFilesFileList.length == 0 &&
              this.fddxFileArr.length == 0
            ) {
              this.qualitativeForm.attachmentName = null;
            }
            this.$message.success("附件删除成功");
          } else {
            this.$$message.error("附件删除失败");
          }
        })
        .catch(error => {
          this.$message.error("附件删除失败");
          console.log(error);
        });
    },
    closeFile(tag) {
      this.importForm.relatedFilesFileList.splice(
        this.importForm.relatedFilesFileList.indexOf(tag),
        1
      );
      if (
        this.importForm.relatedFilesFileList.length == 0 &&
        this.fddxFileArr.length == 0
      ) {
        this.qualitativeForm.attachmentName = null;
      }
    },
    // 预览附件文件
    previewAppendixFile(data) {
      // 首先检查文件名是否为图片类型
      const fileName = data.name.toLowerCase();
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'];

      // 检查文件扩展名是否为图片
      const isImage = imageExtensions.some(ext => fileName.endsWith(ext));

      if (!isImage) {
        // 如果不是图片，直接下载
        this.downloadAppendixFile(data);
        return;
      }

      // 如果是图片，保存当前文件信息并显示预览对话框
      this.currentPreviewFile = data;
      this.imagePreviewVisible = true;
    },

    // 下载当前预览的文件
    downloadCurrentFile() {
      // 确保调用正确的下载方法
      if (this.currentPreviewFile && this.currentPreviewFile.id) {
        this.downloadAppendixFile(this.currentPreviewFile);
      }
    },

    // 下载附件文件
    downloadAppendixFile(data) {
      let param = {
        attId: data.id,
      };
      apiDownloadAppendixFile(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("文件下载成功");
          } else {
            this.$message.error("文件下载失败");
          }
        })
        .catch(error => {
          console.log(error);
          this.$message.error("文件下载失败");
        });
    },

    onReset() {
      this.qualitativeForm = {
        ...this.$options.data,
        alarmCreateTime: this.common.failureTime,
        sheetCreateTime: this.common.failureInformTime,
        woId: this.common.woId,
        workItemId: this.common.workItemId,
        processInstId: this.common.processInstId,
        processDefId: this.common.processDefId,
        person: this.userInfo.realName,
        dept: this.userData.orgInfo.fullOrgName,
        emergencyLevel: this.common.emergencyLevel,
        actionName: this.common.actionName,
        professionalType: null,
        faultDuration: 0, //故障处理净历时
        suspendDuration: this.common.hangOver, //挂起历时 单位秒
        processDuration: 0, //故障处理净历时 单位秒
        effectSystem: null,
        faultReason: null,
        opticFiber: null,
        falutReasonDesc: this.common.faultCauseDescription || "",
        falutComment: null,
        eqpType: null,
        eqpName: null,
        faultEndTime: null,
        busName: null,
        isHardFault: null,
        faultCate: null,
        busRecoverTime: this.common.alarmClearTime, //业务恢复时间
        busRecoverDuration: 0,
        faultRegion: null,
        vendor: null,
        isSentAccurately: "否",
      };
      this.vendorOptions = [];
      this.faultCateDict = 60100;
      this.faultReasonDict = 0;
      this.diviceTypeDict = 0;
      this.importForm.relatedFilesFileList = [];
      this.qualitativeDetail();
    },
    showTimeNew(val) {
      if (val) {
        if (val == 0) return "0秒";
        var time = "";
        var second = val % 60;
        var minute = parseInt(parseInt(val) / 60) % 60;
        time = second + "秒";
        if (minute >= 1) {
          time = minute + "分" + second + "秒";
        }
        var hour = parseInt(parseInt(val / 60) / 60) % 24;
        if (hour >= 1) {
          time = hour + "小时" + minute + "分" + second + "秒";
        }
        var day = parseInt(parseInt(parseInt(val / 60) / 60) / 24);
        if (day >= 1) {
          time = day + "天" + hour + "小时" + minute + "分" + second + "秒";
        }
        return time;
      } else {
        return "0秒";
      }
    },
    //设备名称选择
    deviceSelect() {
      let jsonStr =
        '{"userId":"302785","username":"yangxm97","callSysId":"0001","callSysName":"电子运维"}';
      this.$nextTick(() => {
        document.querySelector("#device").value = jsonStr;
        document.querySelector("#sub__device").submit();
      });
    },
    //资源系统回调方法初始化
    resourceBackInit() {
      window["__process_response_from_getDevice"] = value => {
        let form = this.qualitativeForm;
        let result = JSON.parse(value);
        let objArray = JSON.parse(result?.objects);

        if (form.faultCate == "1") {
          let opticFiberArray = form.opticFiber
            ? form.opticFiber.split(",")
            : [];
          let effectSystemArray = form.effectSystem
            ? form.effectSystem.split(",")
            : [];
          let effectSystemArrayTemp = [];

          objArray.forEach(obj => {
            if (opticFiberArray.indexOf(obj.resName) < 0) {
              opticFiberArray.push(obj.resName);
            }

            effectSystemArrayTemp = JSON.parse(obj.affectedSystem);
            effectSystemArrayTemp.forEach(sys => {
              if (sys.sysName && effectSystemArray.indexOf(sys.sysName) < 0) {
                effectSystemArray.push(sys.sysName);
              }
            });
          });

          form.opticFiber = opticFiberArray.join(",");
          form.effectSystem = effectSystemArray.join(",");
          form.eqpName = "";
        } else {
          let eqpNameArray = form.eqpName ? form.eqpName.split(",") : [];

          objArray.forEach(obj => {
            if (eqpNameArray.indexOf(obj.resName) < 0) {
              eqpNameArray.push(obj.resName);
            }
          });

          form.eqpName = eqpNameArray.join(",");
          form.opticFiber = "";
          form.effectSystem = "";
        }
      };
    },
  },
};
</script>
<style lang="scss" scoped>
.qualitative {
  max-height: calc(90vh - 150px);
  min-height: 400px;
  margin: 0 4px;
  padding-left: 4px;
  padding-right: 8px;
  overflow-y: auto;
  .cus-card ::v-deep .el-card__header {
    padding: 11px 24px 10px;
    font-weight: 400;
    @include themify {
      background-color: themed("$--background-color-base");
    }
  }
  .fileName_style_download {
    margin-right: 3px;
    cursor: pointer;
    vertical-align: middle;
    div {
      display: inline-block;
      max-width: 120px;
      vertical-align: top;
    }
  }
  .fileName_style {
    margin-right: 3px;
    vertical-align: middle;
    div {
      display: inline-block;
      max-width: 120px;
      vertical-align: top;
    }
  }
}
</style>
