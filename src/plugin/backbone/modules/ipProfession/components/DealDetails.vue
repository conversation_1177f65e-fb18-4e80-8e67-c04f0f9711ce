<template>
  <div>
    <el-card shadow="always" class="card" :body-style="{ padding: '10px 24px' }">
      <div class="header clearfix">
        <span class="header-title">处理详情</span>
      </div>
      <div class="content">
        <el-collapse>
          <el-collapse-item v-if="uploadFaultReportInfo">
            <span class="collapse-title" slot="title">上传故障报告</span>
            <div v-for="(faultReport, key) of detailsList['uploadFaultReport']
              .uploadFaultReportInfo" :key="key" class="content__list">
              <p class="detail-p" v-html="faultReport"></p>
            </div>
          </el-collapse-item>
          <el-collapse-item v-if="cancelOrderInfo">
            <span class="collapse-title" slot="title">撤单</span>
            <div v-for="(cancel, key) of detailsList['cancelOrder']
              .cancelOrderInfo" :key="key" class="content__list">
              <p class="detail-p" v-html="cancel"></p>
            </div>
          </el-collapse-item>
          <el-collapse-item v-if="TerminationOf">
            <span class="collapse-title" slot="title">异常终止</span>
            <div v-if="abnormalEndApplicationInfo">
              <div v-for="(abnor, key) of detailsList['abnormalEnd']
                .abnormalEndApplicationInfo" :key="key" class="content__list">
                <p class="detail-p" v-html="abnor"></p>
              </div>
            </div>
          </el-collapse-item>
          <el-collapse-item v-if="determinApproveInfo">
            <span class="collapse-title" slot="title">定性审核</span>
            <div v-for="(deter, key) of detailsList['defineCheck']
              .determinApproveInfo" :key="key" class="content__list">
              <p class="detail-p" v-html="deter"></p>
            </div>
          </el-collapse-item>
          <el-collapse-item v-if="goback">
            <span class="collapse-title" slot="title">返单/定性</span>
            <div v-if="determinInfo">
              <div v-for="(deter, key) of detailsList['feedbackAndDefine']
                .determinInfo" :key="key" class="content__list">
                <p class="detail-p" v-html="deter" @click="handleBtnClick($event)"></p>
              </div>
            </div>
          </el-collapse-item>
          <el-collapse-item v-if="turnToSend">
            <span class="collapse-title" slot="title">转派/转办/重新转派</span>
            <div v-if="reassignOrderInfo">
              <div v-for="(reassign, key) of detailsList['reassign']
                .reassignOrderInfo" :key="key" class="content__list">
                <p class="detail-p" v-html="reassign"></p>
              </div>
            </div>
          </el-collapse-item>
          <el-collapse-item v-if="clearAlarmShow">
            <span class="collapse-title" slot="title">人工清除告警</span>
            <div v-for="(info, key) of detailsList['clearAlarm'].clearAlarmInfo" :key="key" class="content__list">
              <p class="detail-p" v-html="info"></p>
            </div>
          </el-collapse-item>
          <el-collapse-item v-if="hang">
            <span class="collapse-title" slot="title">挂起/解挂</span>
            <div v-if="pendingApplicationInfo">
              <div v-for="(lication, key) of detailsList['pending']
                .pendingApplicationInfo" :key="key" class="content__list">
                <template v-if="lication.str.indexOf('@') == '-1'">
                  <p class="detail-p" v-html="lication.str"></p>
                </template>
                <template v-else>
                  <p class="detail-p">
                    <span v-html="lication.str.split('@')[0]"></span>
                    <template v-if="lication.appendix">
                      <el-tag v-for="(item, index) in JSON.parse(lication.appendix)" class="fileName_style" :key="index"
                        @click="previewAppendixFile(item)" v-loading.fullscreen.lock="appendixFileLoading"
                        :title="item.name">
                        <div class="text-truncate">
                          {{ item.name }}
                        </div>
                      </el-tag>
                    </template>
                    <span v-html="lication.str.split('@')[1]"> </span>
                  </p>
                </template>
              </div>
            </div>
          </el-collapse-item>
          <el-collapse-item v-if="chase">
            <span class="collapse-title" slot="title">追单</span>
            <div v-if="senderAddedInfo">
              <div v-for="(sender, key) of detailsList['addInfo'].senderAddedInfo" :key="key" class="content__list">
                <template v-if="sender.str.indexOf('@') == '-1'">
                  <p class="detail-p" v-html="sender.str"></p>
                </template>
                <template v-else>
                  <p class="detail-p">
                    <span v-html="sender.str.split('@')[0]"></span>

                    <el-tag v-for="(item, index) in JSON.parse(sender.appendix)" class="fileName_style" :key="index"
                      @click="previewAppendixFile(item)" v-loading.fullscreen.lock="appendixFileLoading"
                      :title="item.name">
                      <div class="text-truncate">{{ item.name }}</div>
                    </el-tag>
                    <span v-html="sender.str.split('@')[1]"> </span>
                  </p>
                </template>
              </div>
            </div>
          </el-collapse-item>
          <el-collapse-item v-if="stageFeedbackInfo">
            <span class="collapse-title" slot="title">阶段反馈</span>

            <div v-for="(stage, key) of detailsList['stageFeedback']
              .stageFeedbackInfo" :key="key" class="content__list">
              <p class="detail-p" v-html="stage"></p>
            </div>
          </el-collapse-item>
          <el-collapse-item v-if="circulateInfo">
            <span class="collapse-title" slot="title">传阅</span>
            <div v-for="(cancel, key) of detailsList['passAround'].circulateInfo" :key="key" class="content__list">
              <p class="detail-p" v-html="cancel"></p>
            </div>
          </el-collapse-item>
          <el-collapse-item v-if="accept">
            <span class="collapse-title" slot="title">受理情况</span>
            <div v-if="agentAcceptInfo">
              <div v-for="(age, key) of detailsList['accept'].agentAcceptInfo" :key="key" class="content__list">
                <p class="detail-p" v-html="age"></p>
              </div>
            </div>
          </el-collapse-item>
          <el-collapse-item v-if="pendingInfo">
            <span class="collapse-title" slot="title">工单派发</span>

            <div v-for="(pend, key) of detailsList['waitingAccept'].pendingInfo" :key="key" class="content__list">
              <p class="detail-p" v-html="pend"></p>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-card>
    <el-dialog title="反馈单详情" :visible.sync="feedbackHistoryVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="dialogBackSingleClose" :fullscreen="false" width="83%" top="5vh">
      <div class="content">
        <el-descriptions title="故障定性信息" class="descriptions">
          <template>
            <el-descriptions-item label="故障所属专业">{{
              list.professionalTypeName
              }}</el-descriptions-item>
            <el-descriptions-item label="故障发生时间">{{
              list.alarmCreateTime
              }}</el-descriptions-item>
            <el-descriptions-item label="故障通知时间">
              {{ list.sheetCreateTime }}
            </el-descriptions-item>
            <el-descriptions-item label="业务恢复时间">{{
              list.busRecoverTime
              }}</el-descriptions-item>
            <el-descriptions-item label="业务恢复历时">{{
              busRecoverDuration
              }}</el-descriptions-item>
            <el-descriptions-item label="故障结束时间">
              {{ list.faultEndTime }}</el-descriptions-item>
            <el-descriptions-item label="故障处理历时">
              {{ troubleshootingDuration }}
            </el-descriptions-item>
            <el-descriptions-item label="故障发生地区">
              {{ list.faultRegion }}
            </el-descriptions-item>
            <el-descriptions-item label="故障处理部门">
              {{ list.dept }}
            </el-descriptions-item>
            <el-descriptions-item label="处理人">{{
              list.person
              }}</el-descriptions-item>
            <el-descriptions-item label="挂起历时">{{
              suspendDuration
              }}</el-descriptions-item>
            <el-descriptions-item label="故障处理净历时">
              {{ processDuration }}
            </el-descriptions-item>
            <el-descriptions-item label="是否影响业务">
              <span v-if="list.isEffectBusiness">{{
                list.isEffectBusiness
                }}</span><span v-else>否</span></el-descriptions-item>
            <el-descriptions-item label="影响范围" v-if="list.isEffectBusiness == '是'" :span="2">{{ list.effectRange }}
            </el-descriptions-item>
            <el-descriptions-item label="是否基站退服" v-if="
              list.professionalType != '25' &&
              list.professionalType != '12' &&
              list.professionalType != '13' &&
              list.professionalType != '5' &&
              list.professionalType != '33'
            ">{{ list.isSiteOffline }}</el-descriptions-item>
            <el-descriptions-item label="退服原因" v-if="list.isSiteOffline == '是'" :span="2">{{ list.siteOfflineReason }}
            </el-descriptions-item>
          </template>
        </el-descriptions>
        <el-descriptions title="故障专业信息" class="descriptions">
          <el-descriptions-item label="故障分类" v-if="
            list.professionalType != '0' &&
            list.professionalType != '-1' &&
            list.professionalType != '25' &&
            list.professionalType != '30'
          ">
            {{ list.faultCate }}
          </el-descriptions-item>
          <el-descriptions-item label="故障原因" v-if="
            list.professionalType != '0' &&
            list.professionalType != '-1' &&
            list.professionalType != '25' &&
            list.professionalType != '30'
          ">
            {{ list.faultReason }}</el-descriptions-item>
          <el-descriptions-item label="网元名称" v-if="
            list.professionalType == '1' ||
            list.professionalType == '31' ||
            list.professionalType == '6' ||
            list.professionalType == '19' ||
            list.professionalType == '20' ||
            list.professionalType == '21' ||
            list.professionalType == '32' ||
            list.professionalType == '28'
          ">
            {{ list.neName }}
          </el-descriptions-item>
          <el-descriptions-item label="设备类型" v-if="
            list.professionalType != '0' &&
            list.professionalType != '-1' &&
            list.professionalType != '25' &&
            list.professionalType != '30' &&
            list.professionalType != '1' &&
            list.professionalType != '31' &&
            list.professionalType != '6' &&
            list.professionalType != '19' &&
            list.professionalType != '20' &&
            list.professionalType != '21' &&
            list.professionalType != '32' &&
            list.professionalType != '28'
          ">
            {{ list.eqpType }}
          </el-descriptions-item>
          <el-descriptions-item label="设备名称" v-if="
            list.professionalType != '0' &&
            list.professionalType != '-1' &&
            list.professionalType != '25' &&
            list.professionalType != '30' &&
            list.professionalType != '1' &&
            list.professionalType != '31' &&
            list.professionalType != '6' &&
            list.professionalType != '19' &&
            list.professionalType != '20' &&
            list.professionalType != '21' &&
            list.professionalType != '32' &&
            list.professionalType != '28'
          ">
            {{ list.eqpName }}
          </el-descriptions-item>
          <el-descriptions-item label="MEC节点名称" v-if="list.professionalType == '26'">
            {{ list.mecNodeName }}
          </el-descriptions-item>
          <el-descriptions-item label="紧急程度" v-if="list.professionalType == '26'">
            {{ common.emergencyLevel }}
          </el-descriptions-item>
          <el-descriptions-item label="是否硬件故障" v-if="list.professionalType == '26'">
            {{ list.isHardFault }}
          </el-descriptions-item>
          <el-descriptions-item label="附件">
            <el-tag v-for="(item, index) in attachmentArr" class="fileName_style" :key="index"
              @click="previewAppendixFile(item)" v-loading.fullscreen.lock="appendixFileLoading" :title="item.name">
              <div class="text-truncate">{{ item.name }}</div>
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="是否派单准确" v-if="list.professionalType == '7'">
            {{ list.isSentAccurately }}
          </el-descriptions-item>
          <el-descriptions-item label="故障厂家" v-if="
            list.professionalType == '2' ||
            list.professionalType == '4' ||
            list.professionalType == '7' ||
            list.professionalType == '10' ||
            list.professionalType == '11'
          ">
            {{ list.vendor }}
          </el-descriptions-item>
          <el-descriptions-item label="故障原因描述" :span="6">
            {{ list.faultReasonDesc }}
          </el-descriptions-item>
          <br />
          <el-descriptions-item label="备注" :span="6">
            {{ list.faultComment }}
          </el-descriptions-item>
        </el-descriptions>
        <el-descriptions title="故障定性审核信息" v-if="list.auditResult && list.auditResult !== '无'" class="descriptions">
          <el-descriptions-item label="审批结果">
            <span v-if="list.auditResult == 0">拒绝</span><span v-else-if="list.auditResult == 1">同意</span>
          </el-descriptions-item>
          <el-descriptions-item v-if="list.auditResult == 0" label="回退环节">
            <span v-if="list.auditRejectType == 1">定性环节</span><span v-else>处理环节</span>
          </el-descriptions-item>
          <el-descriptions-item label="是否上传故障报告">
            <span v-if="list.isUploadReport == 0">否</span><span v-else>是</span>
          </el-descriptions-item>
          <el-descriptions-item label="是否故障处理结束">
            <span v-if="list.isExeOver == 0">否</span><span v-else>是</span>
          </el-descriptions-item>
          <el-descriptions-item label="审批意见">
            {{ list.auditContent }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>

    <!-- 使用图片预览组件 -->
    <image-preview :visible.sync="imagePreviewVisible" :file-data="currentPreviewFile"
      preview-api="/commonDict/attach/download" :use-custom-download="true" @download="downloadCurrentFile"
      @close="imagePreviewVisible = false"></image-preview>
  </div>
</template>

<script>
//接口出来更换
import { apiGetProcessInfo, apiGetFeedbackHistory } from "../api/DealDetails";

import { apiDownloadAppendixFile } from "../../workOrder/workOrderWaitDetail/api/CommonApi";
import ImagePreview from "@plugin/backbone/components/ImagePreview.vue";

export default {
  name: "DealDetails",
  components: {
    ImagePreview
  },
  props: {
    woId: String,
    common: Object,
  },
  data() {
    return {
      detailsList: {
        accept: {
          agentAcceptInfo: [],
        },
        waitingAccept: {
          pendingInfo: [],
        },
        addInfo: {
          senderAddedInfo: [],
        },
        stageFeedback: {
          stageFeedbackInfo: [],
        },
        pending: {
          pendingApplicationInfo: [],
        },
        feedbackAndDefine: {
          determinInfo: [],
        },
        defineCheck: {
          determinApproveInfo: [],
        },
        abnormalEnd: {
          abnormalEndApplicationInfo: [],
        },
        cancelOrder: {
          cancelOrderInfo: [],
        },
        reassign: {
          reassignOrderInfo: [],
        },
        passAround: {
          circulateInfo: [],
        },
        uploadFaultReport: {
          uploadFaultReportInfo: [],
        },
        clearAlarm: {
          clearAlarmInfo: [],
        },
      },
      list: {},
      listAll: [],
      attachmentArr: [], //附件
      feedbackHistoryVisible: false,
      busRecoverDuration: null, //业务恢复历时
      troubleshootingDuration: null, //故障处理历时
      suspendDuration: null, //挂起历时
      processDuration: null, //故障处理净历时
      appendixFileLoading: false,
      // 图片预览相关
      imagePreviewVisible: false,
      currentPreviewFile: {},
    };
  },
  mounted() {
    this.getProcessInfo();
  },
  computed: {
    accept() {
      if (this.detailsList["accept"].agentAcceptInfo.length > 0) {
        return true;
      } else {
        return false;
      }
    },
    agentAcceptInfo() {
      if (this.detailsList["accept"].agentAcceptInfo.length > 0) {
        return true;
      } else {
        return false;
      }
    },
    pendingInfo() {
      if (this.detailsList["waitingAccept"].pendingInfo.length > 0) {
        return true;
      } else {
        return false;
      }
    },
    chase() {
      if (this.detailsList["addInfo"].senderAddedInfo.length > 0) {
        return true;
      } else {
        return false;
      }
    },
    senderAddedInfo() {
      if (this.detailsList["addInfo"].senderAddedInfo.length > 0) {
        return true;
      } else {
        return false;
      }
    },
    stageFeedbackInfo() {
      if (this.detailsList["stageFeedback"].stageFeedbackInfo.length > 0) {
        return true;
      } else {
        return false;
      }
    },
    hang() {
      if (this.detailsList["pending"].pendingApplicationInfo.length > 0) {
        return true;
      } else {
        return false;
      }
    },
    pendingApplicationInfo() {
      if (this.detailsList["pending"].pendingApplicationInfo.length > 0) {
        return true;
      } else {
        return false;
      }
    },
    goback() {
      if (this.detailsList["feedbackAndDefine"].determinInfo.length > 0) {
        return true;
      } else {
        return false;
      }
    },
    determinInfo() {
      if (this.detailsList["feedbackAndDefine"].determinInfo.length > 0) {
        return true;
      } else {
        return false;
      }
    },
    determinApproveInfo() {
      if (this.detailsList["defineCheck"].determinApproveInfo.length > 0) {
        return true;
      } else {
        return false;
      }
    },
    TerminationOf() {
      if (
        this.detailsList["abnormalEnd"].abnormalEndApplicationInfo.length > 0
      ) {
        return true;
      } else {
        return false;
      }
    },
    abnormalEndApplicationInfo() {
      if (
        this.detailsList["abnormalEnd"].abnormalEndApplicationInfo.length > 0
      ) {
        return true;
      } else {
        return false;
      }
    },
    cancelOrderInfo() {
      if (this.detailsList["cancelOrder"].cancelOrderInfo.length > 0) {
        return true;
      } else {
        return false;
      }
    },
    turnToSend() {
      if (this.detailsList["reassign"].reassignOrderInfo.length > 0) {
        return true;
      } else {
        return false;
      }
    },
    reassignOrderInfo() {
      if (this.detailsList["reassign"].reassignOrderInfo.length > 0) {
        return true;
      } else {
        return false;
      }
    },
    circulateInfo() {
      if (this.detailsList["passAround"].circulateInfo.length > 0) {
        return true;
      } else {
        return false;
      }
    },
    uploadFaultReportInfo() {
      if (
        this.detailsList["uploadFaultReport"].uploadFaultReportInfo.length > 0
      ) {
        return true;
      } else {
        return false;
      }
    },
    clearAlarmShow() {
      if (this.detailsList["clearAlarm"].clearAlarmInfo.length > 0) {
        return true;
      } else {
        return false;
      }
    },
  },
  methods: {
    handleBtnClick(e) {
      if (e.target.id === "btn") {
        const linkId = e.target.dataset.linkid;
        this.getFeedbackHistory(linkId);
        this.feedbackHistoryVisible = true;
      }
    },
    getFeedbackHistory(linkId) {
      let param = {
        linkId: linkId,
      };
      apiGetFeedbackHistory(param).then(res => {
        if (res.status == 0) {
          let self = this;
          self.listAll = res?.data?.rows ?? [];
          if (self.listAll.length > 0) {
            self.list = self.listAll[0];
            self.workItemId = self.list.workItemId;
            self.isUploadReport = self.list.isUploadReport;
            self.busRecoverDuration = self.showTime(
              self.list.busRecoverDuration
            );
            self.troubleshootingDuration = self.showTime(
              self.list.faultDuration
            );
            self.suspendDuration = self.showTime(self.list.suspendDuration);
            self.processDuration = self.showTime(self.list.processDuration);
          }
          if (self.list.appendix) {
            self.attachmentArr = JSON.parse(self.list.appendix);
          }
        }
      });
    },
    showTime(val) {
      if (val == 0 || val == "" || null == val) {
        return "0秒";
      }
      var time = "";
      var second = val % 60;
      var minute = parseInt(parseInt(val) / 60) % 60;
      time = second + "秒";
      if (minute >= 1) {
        time = minute + "分" + second + "秒";
      }
      var hour = parseInt(parseInt(val / 60) / 60) % 24;
      if (hour >= 1) {
        time = hour + "小时" + minute + "分" + second + "秒";
      }
      var day = parseInt(parseInt(parseInt(val / 60) / 60) / 24);
      if (day >= 1) {
        time = day + "天" + hour + "小时" + minute + "分" + second + "秒";
      }
      return time;
    },
    dialogBackSingleClose() {
      this.feedbackHistoryVisible = false;
    },
    getProcessInfo() {
      let param = {
        woId: this.woId,
      };
      apiGetProcessInfo(param)
        .then(res => {
          if (res.status == 0) {
            this.detailsList = res?.data || {};
            console.log(this.detailsList);
          }
        })
        .catch(err => {
          console.log(err);
        });
    },
    // 预览附件文件
    previewAppendixFile(data) {
      // 首先检查文件名是否为图片类型
      const fileName = data.name.toLowerCase();
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'];

      // 检查文件扩展名是否为图片
      const isImage = imageExtensions.some(ext => fileName.endsWith(ext));

      if (!isImage) {
        // 如果不是图片，直接下载
        this.downloadAppendixFile(data);
        return;
      }

      // 如果是图片，保存当前文件信息并显示预览对话框
      this.currentPreviewFile = data;
      this.imagePreviewVisible = true;
    },

    // 下载当前预览的文件
    downloadCurrentFile() {
      this.downloadAppendixFile(this.currentPreviewFile);
    },

    // 下载附件文件
    downloadAppendixFile(data) {
      this.appendixFileLoading = true;
      let param = {
        attId: data.id,
      };
      apiDownloadAppendixFile(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("文件下载成功");
          } else {
            this.$message.error("文件下载失败");
          }
          this.appendixFileLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("文件下载失败");
          this.appendixFileLoading = false;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../../workOrder/workOrderWaitDetail/assets/common.scss";

::v-deep .el-collapse-item {
  .el-collapse-item__header {
    background-color: #fafafa;
    padding-left: 20px;
  }
}

::v-deep .content__list {
  // border-bottom: 1px #dcdcdc solid;
  padding-top: 10px;
  // padding-bottom: 5px;
  padding-left: 25px;
}

::v-deep .detail-p {
  display: block;
  padding: 0px 5px;
  margin: 0;
}
</style>
