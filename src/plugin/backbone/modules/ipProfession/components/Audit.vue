<template>
  <div>
    <el-form ref="auditForm" :model="auditForm" :rules="auditFormRules" label-width="100px">
      <el-form-item
        label="审核结果"
        prop="opResult"
        :rules="{
          required: true,
          message: '请选择审核结果',
        }"
      >
        <el-radio-group v-model="auditForm.opResult">
          <!-- style="width: 260px" -->
          <el-radio label="1">同意</el-radio>
          <el-radio label="0">拒绝</el-radio>
        </el-radio-group>
      </el-form-item>
      <!-- <el-form-item
                    label="驳回环节"
                    prop="auditRejectType"
                    :rules="{
          required: true,
          message: '请选择驳回环节',
        }"
      >
        <el-radio-group v-model="auditForm.auditRejectType" @input="changeSelect">
          <el-radio :label="'1'">定性</el-radio>
          <el-radio :label="'2'">返单</el-radio>
        </el-radio-group>
      </el-form-item> -->
      <el-form-item label="审核意见" prop="processSuggestion">
        <el-input
          type="textarea"
          :rows="2"
          placeholder="请填写审核意见"
          v-model="auditForm.processSuggestion"
          show-word-limit
          maxlength="1000"
        >
        <!-- style="width: 260px" -->
        </el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: right">
      <el-button
        type="primary"
        @click="handleAuditSubmit('auditForm')"
        v-loading.fullscreen.lock="auditFullScreenLoading"
        >提 交</el-button
      >
      <el-button @click="onResetAudit">重 置</el-button>
    </div>
  </div>
</template>
<script>
import { apiAudit } from "../api/CommonApi";
import {mixin} from "../../../../../mixins"
export default {
  name: "Audit",
  props: {
    common: Object,
    opContent: Number,
  },
  mixins: [mixin],
  data() {
    return {
      auditForm: {
        opResult: null,
        processSuggestion: null,
        auditRejectType: null,
      },
      auditFormRules: {
        processSuggestion: [
          {
            validator: this.checkLength,
            max: 1000,
            form: "auditForm",
            messsage: "已超过填写字数上限",
          },
        ],
      },
      auditFullScreenLoading: false,
    };
  },
  mounted() {
    console.log(this.common.professionalTypeName);
  },
  watch: {
    opContent(val) {
      this.opContent = val;
    },
  },
  methods: {
    changeSelect(value){
      if(value!='1'){
        if(!this.auditForm.auditRejectType){
          this.auditForm.auditRejectType = "1";
        }
      }
    },
    handleAuditSubmit(formName) {
      this.$refs[formName].validate((valid,a) => {
        if (this.uncheck(a)) {
          this.auditFullScreenLoading = true;
          if(this.auditForm.opResult=='1'){
            this.auditForm.auditRejectType = null;
          }
          let param = {
            opContent: this.opContent,
            woId: this.common.woId,
            processInstId: this.common.processInstId,
            processDefId: this.common.processDefId,
            workItemId: this.common.workItemId,
            processNode: this.common.processNode,
            opResult: this.auditForm.opResult,
            processSuggestion: this.auditForm.processSuggestion,
            // auditRejectType: this.auditForm.auditRejectType, // 不需要直接去掉
          };
          apiAudit(param)
            .then(res => {
              if (res.status == "0") {
                this.$message.success(res.msg);
                if (this.opContent == 1) {
                  this.$emit("closeDialogPendingReview");
                } else if (this.opContent == 2) {
                  this.$emit("closeDialogSolutionToHangAudit");
                }
              } else {
                this.$message.error(res.msg);
              }
              this.auditFullScreenLoading = false;
            })
            .catch(error => {
              console.log(error);
              this.auditFullScreenLoading = false;
              this.$message.error("审核提交失败");
            });
        } else {
          return false;
        }
      });
    },
    onResetAudit() {
      this.auditForm = {
        ...this.$options.data,
      };
    },
  },
};
</script>
