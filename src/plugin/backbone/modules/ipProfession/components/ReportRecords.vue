<template>
  <el-card shadow="always" class="card" :body-style="{ padding: '10px 24px' }">
    <div class="header clearfix">
      <span class="header-title">故障报告上传记录</span>
      <!-- <div class="header-right">
        <el-button type="primary" size="mini" @click="reportAudit"
          >审核</el-button
        >
      </div> -->
      <div class="header-right" v-show="isShowReportReview">
        <el-button type="primary" size="mini" @click="reportAudit"
          >故障报告审核</el-button
        >
      </div>
    </div>

    <div class="_el-table">
      <el-table
        :data="tableData"
        style="width: 100%"
        row-key="id"
        :tree-props="{ children: 'data', hasdata: 'hasdata' }"
        v-loading="tableLoading"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          width="55"
          :selectable="selectableFunc"
        >
        </el-table-column>
        <el-table-column
          prop="auditStatus"
          width="100"
          label="审核状态"
        ></el-table-column>
        <el-table-column prop="opDeptName" width="180" label="上报单位">
        </el-table-column>
        <el-table-column
          prop="opPersonName"
          width="120"
          label="上报人"
        ></el-table-column>
        <el-table-column prop="upTime" label="上报时间"> </el-table-column>
        <el-table-column prop="appendix" label="故障报告">
          <template slot-scope="scope">
            <el-tag
              v-for="(item, index) in scope.row.appendix"
              class="fileName_style"
              :key="index"
              @click="downloadAppendixFile(item)"
              v-loading.fullscreen.lock="appendixFileLoading"
              :title="item.name"
              ><div class="text-truncate">{{ item.name }}</div>
            </el-tag>
            <!-- <el-tag>{{ scope.row.opPerson.name }}</el-tag>@click="downloadAppendixFile(item)" -->
          </template>
        </el-table-column>
        <el-table-column
          prop="opContent"
          width="230"
          label="描述"
        ></el-table-column>
        <el-table-column prop="opResult" label="审核结果"></el-table-column>
        <el-table-column
          prop="processSuggestion"
          label="审核意见"
        ></el-table-column>
      </el-table>
    </div>
    <el-dialog
      title="故障报告审核"
      :visible.sync="dialogReportAuditVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="closeReportRecords"
      width="540px"
    >
      <!-- @close="qualitativeClose" -->
      <report-audit
        ref="reportAudit"
        :multipleSelection="multipleSelection"
        @closeDialogReportAudit="closeDialogReportAudit"
      >
      </report-audit>
      <!-- <qualitative
        ref="qualitative"
        :common="common"
        :workItemId="common.workItemId"
        @qualitativeSubmit="qualitativeSubmit"
      ></qualitative> -->
    </el-dialog>
  </el-card>
</template>

<script>
import { apiGetReportUpLog } from "../api/CommonApi";
import { apiDownloadAppendixFile } from "../../workOrder/workOrderWaitDetail/api/CommonApi";
import ReportAudit from "./ReportAudit.vue";
export default {
  name: "ReportRecords",
  props: {
    isShowReportReview: Boolean, //判断故障报告审核按钮
    woId: String,
  },
  components: {
    ReportAudit,
  },
  data() {
    return {
      tableData: [],
      tableLoading: false,
      dialogReportAuditVisible: false,
      appendixFileLoading: false,
      multipleSelection: [],
    };
  },
  mounted() {
    this.getLogData();
  },
  methods: {
    getLogData() {
      this.tableLoading = true;
      let param = {
        woId: this.woId,
      };
      apiGetReportUpLog(param)
        .then(res => {
          if (res.status == 0) {
            let row = res?.data ?? [];
            if (row.length > 0) {
              row.forEach(el => {
                el.appendix = JSON.parse(el.appendix);
                if (el.data.length > 0) {
                  el.data.forEach(tl => {
                    tl.appendix = JSON.parse(tl.appendix);
                  });
                }
              });
            }
            this.tableData = row;
            this.tableLoading = false;
          } else {
            this.tableLoading = false;
          }
        })
        .catch(err => {
          console.log(err);
          this.tableLoading = false;
        });
    },
    closeReportRecords() {
      this.dialogReportAuditVisible = false;
    },
    reportAudit() {
      this.dialogReportAuditVisible = true;
    },
    closeDialogReportAudit() {
      this.$emit("closeDialogReportAudit");
      this.dialogReportAuditVisible = false;
    },
    selectableFunc(row, index) {
      return row.data && row.auditStatus != "已审核";
    },
    handleSelectionChange(val) {
      // val.forEach(el => {
      //   delete el.data;
      // });
      this.multipleSelection = val;
    },
    downloadAppendixFile(data) {
      let param = {
        attId: data.id,
      };
      this.appendixFileLoading = true;
      apiDownloadAppendixFile(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("文件下载成功");
          } else {
            this.$message.error("文件下载失败");
          }
        })
        .catch(error => {
          console.log(error);
          this.$message.error("文件下载失败");
        })
        .finally(() => {
          this.appendixFileLoading = false;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../../workOrder/workOrderWaitDetail/assets/common.scss";
._el-table ::v-deep {
  .el-table__row:not(.el-table__row--level-0)
    .el-table-column--selection
    .el-checkbox.is-disabled {
    display: none;
  }
  th.el-table__cell.el-table-column--selection > .cell {
    padding-left: 14px;
  }
}
</style>
