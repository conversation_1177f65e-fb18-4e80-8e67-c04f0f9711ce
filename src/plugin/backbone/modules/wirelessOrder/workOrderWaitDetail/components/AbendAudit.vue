<template>
  <div>
    <el-form ref="abendAuditForm" :model="abendAuditForm" label-width="80px">
      <el-form-item
        label="审核结果"
        prop="opResult"
        :rules="{
          required: true,
          message: '请选择审核结果',
        }"
      >
        <el-radio-group v-model="abendAuditForm.opResult" style="width: 260px">
          <el-radio label="1">同意</el-radio>
          <el-radio label="0">拒绝</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="审核意见" prop="processSuggestion">
        <el-input
          type="textarea"
          :rows="2"
          placeholder="请填写审核意见"
          v-model="abendAuditForm.processSuggestion"
          style="width: 260px"
        >
        </el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: right">
      <el-button
        type="primary"
        @click="handleAbendAuditSubmit('abendAuditForm')"
        v-loading.fullscreen.lock="abendAuditFullScreenLoading"
        >提 交</el-button
      >
      <el-button @click="onResetAbendAudit">重 置</el-button>
    </div>
  </div>
</template>
<script>
import { apiAbendAudit, apiItCloudAbendAudit } from "../api/CommonApi";

export default {
  name: "Abend",
  props: {
    common: Object,
  },
  data() {
    return {
      abendAuditForm: {
        opResult: null,
        processSuggestion: null,
      },
      abendAuditFullScreenLoading: false,
    };
  },
  mounted() {},
  methods: {
    handleAbendAuditSubmit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.abendAuditFullScreenLoading = true;
          let param = {
            woId: this.common.woId,
            processInstId: this.common.processInstId,
            processDefId: this.common.processDefId,
            workItemId: this.common.workItemId,
            processNode: this.common.processNode,
            opResult: this.abendAuditForm.opResult,
            processSuggestion: this.abendAuditForm.processSuggestion,
          };
          if (this.common.professionalTypeName == "传输网") {
            apiAbendAudit(param)
              .then(res => {
                if (res.status == "0") {
                  this.$message.success("提交审核成功");
                  this.onResetAbendAudit();
                  this.$emit("closeDialogAbendAudit");
                } else {
                  this.$message.error("提交审核失败");
                }
                this.abendAuditFullScreenLoading = false;
              })
              .catch(error => {
                console.log(error);
                this.abendAuditFullScreenLoading = false;
                this.$message.error("提交审核失败");
              });
          } else if (this.common.professionalTypeName == "IT云设备") {
            apiItCloudAbendAudit(param)
              .then(res => {
                if (res.status == "0") {
                  this.$message.success("提交审核成功");
                  this.onResetAbendAudit();
                  this.$emit("closeDialogAbendAudit");
                } else {
                  this.$message.error("提交审核失败");
                }
                this.abendAuditFullScreenLoading = false;
              })
              .catch(error => {
                console.log(error);
                this.abendAuditFullScreenLoading = false;
                this.$message.error("提交审核失败");
              });
          }
        } else {
          return false;
        }
      });
    },
    onResetAbendAudit() {
      this.abendAuditForm = {
        ...this.$options.data,
      };
    },
  },
};
</script>
