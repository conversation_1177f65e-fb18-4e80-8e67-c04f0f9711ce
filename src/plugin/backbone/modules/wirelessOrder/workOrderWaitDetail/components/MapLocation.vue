<template>
  <div class="map-wrap">
    <div id="container"></div>
    <!-- 已打点标志 -->
    <span class="note">已打点</span>
  </div>
</template>
<script>
import loadJs from "../api/loadJs";
import { apiGetGis } from "../api/CommonApi";

export default {
  name: "Mapview",
  props: {
    woId: String,
    random: Number,
  },
  data() {
    return {
      zoom: 13,
      defaultCenterPoint: [],
    };
  },
  created() {},
  mounted() {
    loadJs("https://gis.10010.com:8219/dugis-baidu/baidumap/jsapi/api.js").then(
      () => {
        // 加载成功，进行后续操作
        // const webApiBasePath = "https://gis.10010.com:8219/dugis-baidu/";
        // const coordinateType = "bd09ll"; // 当前坐标类型，用于webAPI传参
        //const defaultCenterPoint = this.defaultCenterPoint; // 中心点经纬度
        // DuGIS权限校验
        //const apiAuthorization = "baidu-d874fd0219944973909532c3d3444432";
        this.initAMap();
      }
    );
  },
  methods: {
    initAMap() {
      //创建地图实例;
      const map = new window.BMap.Map("container", {
        resizeEnable: true,
      });

      // 先获取经纬度
      let param = {
        woId: this.woId,
      };
      apiGetGis(param)
        .then(res => {
          var m1 = new window.BMap.Point(res.data.longitude, res.data.latitude);
          // 现场打点经纬度
          var m2 = new window.BMap.Point(
            res.data.signLongitude,
            res.data.signLatitude
          );

          this.defaultCenterPoint = [
            (Number(res.data.signLongitude) + Number(res.data.longitude)) / 2,
            (Number(res.data.signLatitude) + Number(res.data.latitude)) / 2,
          ];
          //初始化地图,设置城市和地图级别。
          map.centerAndZoom(
            new BMap.Point(
              this.defaultCenterPoint[0],
              this.defaultCenterPoint[1]
            ),
            13
          );
          let pathArr = [m1, m2];
          var view = map.getViewport(pathArr); // this.pathArr 是我们知道的经纬度。可以有多个。全部以数组形式放在里面。
          //进入显示的中心位置，百分比
          map.centerAndZoom(view.center, view.zoom - 1);

          // 添加标点，图标
          var marker1 = new window.BMap.Marker(m1); // 创建标注
          var marker2 = new window.BMap.Marker(m2); // 创建标注

          map.addOverlay(marker1); // 将标注添加到地图
          map.addOverlay(marker2); // 将标注添加到地图中
          // 画线
          var polyline = new window.BMap.Polyline(
            [
              new window.BMap.Point(res.data.longitude, res.data.latitude),
              new window.BMap.Point(
                res.data.signLongitude,
                res.data.signLatitude
              ),
            ],
            { strokeColor: "#000", strokeWeight: 3, strokeStyle: "dashed" }
          );
          map.addOverlay(polyline);
          // 显示打点位置 提示
          var opts = {
            position: new window.BMap.Point(
              res.data.signLongitude,
              res.data.signLatitude
            ), // 指定文本标注所在的地理位置
            offset: new window.BMap.Size(5, 5), // 设置文本偏移量
          };
          // 创建文本标注对象
          var tip = new window.BMap.Label("打点位置", opts);
          // 自定义文本标注样式
          tip.setStyle({
            background: "#25A5F7",
            padding: "4px 10px",
            borderRadius: "3px",
            color: "#fff",
            border: 0,
            fontSize: "14px",
            boxShadow: "1px 1px 1px rgb(10 10 10 / 20%)",
          });
          map.addOverlay(tip);

          // 距离
          let pointStart = new window.BMap.Point(
            res.data.longitude,
            res.data.latitude
          );
          let pointEnd = new window.BMap.Point(
            res.data.signLongitude,
            res.data.signLatitude
          );
          let distance = map.getDistance(pointStart, pointEnd).toFixed(2);

          let distanceTxt = "";
          if (distance >= 1000) {
            distanceTxt = "两点相距" + (distance / 1000).toFixed(2) + "千米";
          } else {
            distanceTxt = "两点相距" + distance + "米";
          }

          var distancePoint = {
            position: new window.BMap.Point(
              (Number(res.data.signLongitude) + Number(res.data.longitude)) / 2,
              (Number(res.data.signLatitude) + Number(res.data.latitude)) / 2
            ),
            offset: new window.BMap.Size(18, -30), // 设置文本偏移量
          };
          // 创建文本标注对象
          var distanceTip = new window.BMap.Label(distanceTxt, distancePoint);
          // 自定义文本标注样式
          distanceTip.setStyle({
            background: "#fff",
            borderColor: "#666",
            fontSize: "16px",
          });
          map.addOverlay(distanceTip);

          var top_left_navigation = new BMap.NavigationControl(); //左上角，添加默认缩放平移控件
          map.addControl(top_left_navigation);
        })
        .catch(err => {
          console.log(err);
        });
    },
  },
  watch: {
    random: {
      handler() {
        this.initAMap();
      },
    },
  },
};
</script>
<style scoped>
.map-wrap {
  position: relative;
  margin-top: -10px;
}
.map-wrap .note {
  z-index: 999;
  position: absolute;
  bottom: 20px;
  right: 20px;
  display: block;
  width: 64px;
  height: 64px;
  line-height: 64px;
  border-radius: 999px;
  text-align: center;
  font-weight: bold;
  color: #fff;
  background: #05a605;
}
#container {
  padding: 0px;
  margin: 0px;
  width: 100%;
  height: 500px;
}
</style>
