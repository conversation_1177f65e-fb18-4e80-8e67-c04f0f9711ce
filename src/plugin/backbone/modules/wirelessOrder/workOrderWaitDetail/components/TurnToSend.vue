<template>
  <div>
    <el-form
      ref="transferForm"
      :model="transferForm"
      :rules="transferFormRules"
      label-width="90px"
    >
      <el-form-item label="转派说明:" prop="turnReason">
        <el-input
          type="textarea"
          placeholder="请填写转派说明"
          v-model="transferForm.turnReason"
          style="width: 310px"
          show-word-limit
          maxlength="1000"
        ></el-input>
      </el-form-item>
      <el-form-item label="转派对象:" prop="transfer" required>
        <el-input
          v-model="transferForm.transfer"
          placeholder="添加人员"
          style="width: 310px"
        >
          <template v-for="(tag, index) in organizeForm.transferList">
            <el-tag
              slot="prefix"
              style="margin-top: 5px"
              :key="index"
              closable
              @close="handleClose('transfer', tag)"
              v-show="index < 1"
              v-if="tag.bz == 'user'"
            >
              {{ tag.name }}
            </el-tag>
            <el-tag
              slot="prefix"
              style="margin-top: 5px"
              :key="index"
              closable
              @close="handleClose('transfer', tag)"
              v-show="index < 1"
              v-if="tag.bz == 'org'"
            >
              {{ tag.orgName }}
            </el-tag>
          </template>
          <el-popover
            slot="prefix"
            v-if="organizeForm.transferList.length >= 2"
            width="500"
            trigger="click"
          >
            <el-input
              v-model="organizeForm.transferName"
              placeholder="请输入主送人员姓名/组织名称"
            >
              <el-button
                type="info"
                slot="append"
                icon="el-icon-search"
                @click="search('transfer')"
              >
              </el-button>
              <el-button
                type="info"
                slot="append"
                icon="el-icon-close"
                @click="clear('transfer')"
              >
              </el-button>
            </el-input>
            <el-table
              ref="multipleTable"
              tooltip-effect="dark"
              @selection-change="handleSelectionChange"
              :data="organizeForm.transferListCopy"
              max-height="240"
            >
              <el-table-column width="30" type="selection"> </el-table-column>
              <el-table-column min-width="70" property="name" label="姓名">
              </el-table-column>
              <el-table-column min-width="180" property="orgName" label="组织">
              </el-table-column>
              <el-table-column
                min-width="120"
                property="mobilePhone"
                label="电话"
              >
              </el-table-column>
              <el-table-column fixed="right" label="操作" width="50">
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    size="small"
                    @click.native.prevent="handleClose('transfer', scope.row)"
                  >
                    移除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-button
              size="small"
              type="text"
              @click="toggleSelection('transfer')"
              >批量移除
            </el-button>
            <el-tag slot="reference" style="margin-top: 3px">
              +{{ organizeForm.transferList.length - 1 }}
            </el-tag>
          </el-popover>
          <el-button
            type="info"
            slot="append"
            icon="el-icon-user"
            @click="onOpenPeopleDialog('transferObjectDetermine')"
          ></el-button>
        </el-input>

        <!-- <el-input
          v-model="transferForm.transferObject"
          placeholder="添加人员"
          style="width: 310px"
          readonly
        >
          <el-button
            type="info"
            slot="append"
            icon="el-icon-user"
            @click="onOpenPeopleDialog('transferObjectDetermine')"
          ></el-button>
        </el-input> -->
      </el-form-item>
    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: right">
      <el-button
        type="primary"
        @click="transferSubmit('transferForm')"
        v-loading.fullscreen.lock="transferSubmitLoading"
        >提 交</el-button
      >
      <el-button @click="transferCancel">取 消</el-button>
    </div>

    <dia-tissue-tree
      :title="diaPeople.title"
      :visible.sync="diaPeople.visible"
      :showOrgsTree="diaPeople.showOrgsTree"
      :multiple-select-enable="diaPeople.multipleSelectEnable"
      :single-select-tip="diaPeople.singleSelectTip"
      :professionalType="common.professionalTypeName"
      @on-save="onSavePeople"
      :appendToBody="true"
      :userAttribution="userAttribution"
      :showContactOrgTab="diaPeople.showContactOrgTab"
      :showContactUserTab="diaPeople.showContactUserTab"
    />
  </div>
</template>
<script>
import { apiTransfer, getCurrentTime } from "../api/CommonApi";
import DiaTissueTree from "@/plugin/backbone/components/common/DiaTissueTree";
import {mixin} from "../../../../../../mixins"
export default {
  name: "TurnToSend",
  props: {
    common: Object,
    type: String,
    actionName: String,
  },
  components: {
    DiaTissueTree,
  },
  mixins: [mixin],
  data() {
    return {
      userAttribution: "wirelessTransfer",
      transferForm: {
        turnReason: "", //转派说明

        transfer: "", //转派
        transferUserId: "", //转派人ID
        transferOrgId: "", //转派组织ID
        transferUserName: "", //转派人名称
        transferOrgName: "", //转派组织名称

        transferManDetail: "",
      },

      organizeForm: {
        transferList: [],
        transferListCopy: [],
        transferName: "",
      },

      transferFormRules: {
        transfer: [{ required: true, message: "转派对象不能为空" }],
        turnReason: [
          {
            validator: this.checkLength,
            max: 1000,
            form: "transferForm",
            messsage: "已超过填写字数上限",
          },
        ],
      },
      //主送
      activeName: "orgTree",
      proRedepolyOrgsData: [],
      proRedeployUserData: [],
      transferObjectDialogVisible: false,
      defaultProps: {
        children: "children",
        label: "name",
        isLeaf: "isLeaf",
      },

      woId: null,
      workItemId: null,
      transferSubmitLoading: false,
      multipleSelection: null,
      diaPeople: {
        visible: false,
        title: "",
        saveName: "",
        saveTitleMap: {
          transferObjectDetermine: "选择转派人",
        },
        multipleSelectEnable: true,
        singleSelectTip: "",
        showOrgsTree: true,
        showContactOrgTab: true,
        showContactUserTab: true,
      },
    };
  },
  mounted() {
    console.log(this.common);
  },
  watch: {
    "organizeForm.transferList": {
      handler(newV) {
        if (newV.length > 0) {
          this.transferForm.transfer = "已选";
        } else {
          this.transferForm.transfer = "";
        }
      },
      deep: true,
    },
  },

  methods: {
    //转派提交
    transferSubmit(formName) {
      this.entering();
      this.$refs[formName].validate((valid,a) => {
        if (this.uncheck(a)) {
          this.transferSubmitLoading = true;
          let param = {
            woId: this.common.woId,
            processInstId: this.common.processInstId,
            workItemId: this.$route.query.workItemId,
            actionName: this.actionName,

            transferUserId: this.transferForm.transferUserId,
            transferUserName: this.transferForm.transferUserName,
            transferOrgId: this.transferForm.transferOrgId,
            transferOrgName: this.transferForm.transferOrgName,

            turnReason: this.transferForm.turnReason,
            createTime: getCurrentTime(Date.now()),
          };
          apiTransfer(param)
            .then(res => {
              if (res.status == "0") {
                this.$message.success("转派成功");
                this.$emit("closeDialogTurnToSend", "1");
              } else {
                this.$message.error("转派失败");
              }
              this.transferSubmitLoading = false;
            })
            .catch(error => {
              console.log(error);
              this.$message.error("转派失败");
              this.transferSubmitLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    //转派取消
    transferCancel() {
      this.$emit("closeDialogTurnToSend", "0");
    },

    transferObjectHandleClose() {
      this.transferObjectDialogVisible = false;
      this.resetTransferObjectChecked();
    },
    resetTransferObjectChecked() {
      this.$refs.transferObjectOrgTree.setCheckedKeys([]);
      this.$refs.transferObjectUserTree.setCheckedKeys([]);
    },

    onOpenPeopleDialog(diaSaveName) {
      this.diaPeople.title = this.diaPeople.saveTitleMap[diaSaveName];
      this.diaPeople.saveName = diaSaveName;
      this.diaPeople.showOrgsTree = true;
      this.diaPeople.visible = true;
    },

    onSavePeople(val) {
      this[this.diaPeople.saveName](val);
    },

    transferObjectDetermine({
      usersChecked,
      orgsChecked,
      selectionUser,
      contactSelectionUser,
      contactSelectionOrg,
    }) {
      if (contactSelectionUser && contactSelectionUser.length > 0) {
        contactSelectionUser.map(item => {
          let zp = this.organizeForm.transferList.findIndex(val => {
            return val.id === item.userName;
          });
          if (zp > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.transferList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (selectionUser && selectionUser.length > 0) {
        selectionUser.map(item => {
          let zp = this.organizeForm.transferList.findIndex(val => {
            return val.id === item.userName;
          });
          if (zp > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.transferList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      } else if (usersChecked && usersChecked.length > 0) {
        usersChecked.map(item => {
          let zp = this.organizeForm.transferList.findIndex(val => {
            return val.id === item.id;
          });
          if (zp > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.transferList.push({
              bz: "user",
              id: item.id,
              name: item.name,
              orgName: item.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (contactSelectionOrg && contactSelectionOrg.length > 0) {
        contactSelectionOrg.map(item => {
          let zpOrg = this.organizeForm.transferList.findIndex(val => {
            return val.id === item.orgId;
          });
          if (zpOrg > -1) {
            this.$message({
              message: "选择的组织已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.transferList.push({
              bz: "org",
              id: item.orgId + "",
              orgName: item.fullOrgName,
            });
          }
        });
      }
      if (orgsChecked && orgsChecked.length > 0) {
        orgsChecked.map(item => {
          let zpOrg = this.organizeForm.transferList.findIndex(val => {
            return val.id === item.id;
          });
          if (zpOrg > -1) {
            this.$message({
              message: "选择的组织已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.transferList.push({
              bz: "org",
              id: item.id,
              orgName: item.name,
            });
          }
        });
      }
      this.organizeForm.transferListCopy = JSON.parse(
        JSON.stringify(this.organizeForm.transferList)
      );
    },

    stitchingAlgorithmNew(orgName, userName) {
      if (orgName.length !== 0 && userName.length !== 0) {
        return orgName + "," + userName;
      } else {
        if (orgName.length !== 0) {
          return orgName;
        } else if (userName.length !== 0) {
          return userName;
        } else {
          return "";
        }
      }
    },

    //多选移除
    toggleSelection(val) {
      if (this.multipleSelection == 0) {
        this.$message({
          type: "warning",
          message: "还没有选择移除的选项，请先选择要移除的选项！",
        });
        return;
      } else {
        this.multipleSelection.forEach(row => {
          this.handleClose(val, row);
        });
      }
      this.multipleSelection;
    },
    //移除对象
    handleClose(val, tag) {
      if (val == "transfer") {
        this.organizeForm.transferList.splice(
          this.arrayIndex(this.organizeForm.transferList, tag),
          1
        );
        this.organizeForm.transferListCopy.splice(
          this.arrayIndex(this.organizeForm.transferListCopy, tag),
          1
        );
      }
    },
    //找对应对象的索引值
    arrayIndex(val1, val2) {
      for (var i = 0, len = val1.length; i < len; i++) {
        var tag = val1[i];
        if (tag.id == val2.id) {
          return i;
        }
      }
    },
    //搜索
    search(val) {
      if (val == "transfer" && this.organizeForm.transferList != null) {
        this.organizeForm.transferListCopy = [];
        this.organizeForm.transferList.forEach(row => {
          if (
            row.name == this.organizeForm.transferName ||
            row.orgName == this.organizeForm.transferName
          ) {
            this.organizeForm.transferListCopy.push(row);
          }
        });
      }
    },
    //清除搜索项
    clear(val) {
      if (val == "transfer") {
        this.organizeForm.transferName = "";
        this.organizeForm.transferListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.transferList)
        );
      }
    },

    handleSelectionChange(val) {
      this.multipleSelection = val;
    },

    entering() {
      if (
        this.organizeForm.transferList &&
        this.organizeForm.transferList.length > 0
      ) {
        this.transferForm.transfer = "已选";
        let userList = this.organizeForm.transferList.filter((item, index) => {
          return item.bz == "user";
        });
        let orgList = this.organizeForm.transferList.filter((item, index) => {
          return item.bz == "org";
        });
        if (userList && userList.length > 0) {
          let usersCheckedName = userList.map(item => {
            return item.name;
          });
          this.transferForm.transferUserName = usersCheckedName.join(",");
          let usersCheckedId = userList.map(item => {
            return item.id;
          });
          this.transferForm.transferUserId = usersCheckedId.join(",");
          const userDetailName = userList.map(item => {
            return item.name + "-" + item.orgName + "-" + item.mobilePhone;
          });
          this.transferForm.transferManDetail = userDetailName.join(",");
        }
        if (orgList && orgList.length > 0) {
          let orgsCheckedName = orgList.map(item => {
            return item.orgName;
          });
          this.transferForm.transferOrgName = orgsCheckedName.join(",");
          let orgsCheckedId = orgList.map(item => {
            return item.id;
          });
          this.transferForm.transferOrgId = orgsCheckedId.join(",");
        }
      }
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-tree {
  padding-top: 15px;
  padding-left: 10px;
  // 不可全选样式
  .el-tree-node {
    .is-leaf + .el-checkbox .el-checkbox__inner {
      display: inline-block;
    }
    .el-checkbox .el-checkbox__inner {
      display: none;
    }
  }
}
</style>
