<template>
  <el-card shadow="always" class="card" :body-style="{ padding: '10px 24px' }">
    <div class="header clearfix">
      <span class="header-title" style="margin-top: 5px; margin-right: 10px"
        >流程图</span
      >
      <div style="display: inline-block">
        <el-radio-group v-model="tabRadio" @change="handleClick" size="mini">
          <el-radio-button
            v-for="(tab, key) in tabMenu"
            :key="key"
            :label="key"
          >
            {{ tab.processName }}
          </el-radio-button>
        </el-radio-group>
      </div>
    </div>

    <div class="content">
      <iframe
        v-if="processInstId"
        :src="processUrl"
        frameborder="0"
        scrolling="no"
        width="100%"
        height="490px"
      ></iframe>
    </div>
  </el-card>
</template>
<script>
import { apiGetProcessTree } from "../../../workOrder/api/FlowChart";
export default {
  name: "FlowChart",
  props: {
    common: Object,
  },
  data() {
    return {
      tabMenu: [],
      processInstId: this.common.processInstId,
      processUrlPre:
        "http://************:8081/uflow-api/service/uflowGraph/showWFGraph?zoom=1&tenantId=eoms_netfm3",
      processUrl: "",
      activeNames: ["1"],
      tabRadio: 0,
    };
  },
  mounted() {
    this.getTabMenu();
  },
  methods: {
    //监听切换流程图
    setprocessUrl() {
      let userRealName = JSON.parse(sessionStorage.userInfo).realName;
      let username = JSON.parse(sessionStorage.userInfo).userName;
      this.processUrl =
        this.processUrlPre +
        `&processInstID=${this.processInstId}&userRealName=${userRealName}&username=${username}`;
    },
    //获取tab数据
    getTabMenu() {
      let param = {
        processInstId: this.processInstId,
        woId: this.common.woId,
      };
      let param1 = JSON.stringify(param);
      apiGetProcessTree(param1).then(res => {
        if (res.status == 0) {
          let self = this;
          self.tabMenu = res?.data ?? [];
          if (self.tabMenu.length > 0) {
            self.processInstId = self.tabMenu[0].processId;
          }
          this.processUrlPre = res.msg;
          this.setprocessUrl();
        }
      });
    },
    //tab切换流程
    handleClick(index) {
      // console.log(this.tabRadio);
      // let index = tab.index;
      this.processInstId = this.tabMenu[index].processId;
      this.setprocessUrl();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../assets/common.scss";
.custom-theme-default .el-collapse {
  border-top: none;
}
::v-deep .el-collapse-item__wrap {
  border-bottom: 0;
}
</style>
