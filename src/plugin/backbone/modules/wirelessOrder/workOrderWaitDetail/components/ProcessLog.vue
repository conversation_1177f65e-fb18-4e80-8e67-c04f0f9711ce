<template>
  <el-card shadow="always" class="card" :body-style="{ padding: '10px 24px' }">
    <div class="header clearfix">
      <span class="header-title">流程日志</span>
    </div>

    <div class="content">
      <el-table
        :data="tableData"
        style="width: 100%"
        row-key="id"
        :tree-props="{ children: 'data', hasdata: 'hasdata' }"
        v-loading="tableLoading"
      >
        <el-table-column width="400" prop="opDeptName" label="部门名称">
          <template slot-scope="scope">
            <span v-if="scope.row.processNode != '归档'">
              {{ scope.row.opDeptName }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="processNode" label="环节名称"> </el-table-column>
        <el-table-column prop="receivedTime" label="到达时间">
        </el-table-column>
        <el-table-column prop="actionName" label="处理步骤"> </el-table-column>
        <el-table-column prop="opPerson" label="处理人">
          <template slot-scope="scope">
            <span v-if="scope.row.processNode != '归档'">
              {{ scope.row.opPerson }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="completedTime" label="完成时间">
        </el-table-column>
        <el-table-column
          prop="processSuggestion"
          label="处理意见"
          class="process_suggestion_class"
        >
          <template slot-scope="scope">
            <span
              v-if="scope.row.actionName == '阶段反馈'"
              v-html="scope.row.processSuggestion"
            >
            </span>
            <span v-else>{{ scope.row.processSuggestion }}</span>
            <span
              v-if="scope.row.actionName == '阶段反馈' && scope.row.appendix"
            >
              <el-tag
                v-for="(item, index) in JSON.parse(scope.row.appendix)"
                class="fileName_style"
                :key="index"
                @click="downloadAppendixFile(item)"
                v-loading.fullscreen.lock="appendixFileLoading"
                :title="item.name"
              >
                <div class="text-truncate">{{ item.name }}</div>
              </el-tag>
            </span>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </el-card>
</template>

<script>
import { apiGetProcessLog, apiDownloadAppendixFile } from "../api/CommonApi";

export default {
  name: "ProcessLog",
  props: {
    woId: String,
  },
  data() {
    return {
      tableData: [],
      tableLoading: false,
      appendixFileLoading: false,
    };
  },
  mounted() {
    this.getLogData();
  },
  methods: {
    getLogData() {
      this.tableLoading = true;
      let param = {
        woId: this.woId,
      };
      apiGetProcessLog(param)
        .then(res => {
          if (res.status == 0) {
            let row = res?.data?.rows ?? [];
            if (row.length > 0) {
              row.forEach(el => {
                el.id = this.randomNumber();
                if (el.data.length > 0) {
                  el.data.forEach(tl => {
                    tl.id = this.randomNumber();
                  });
                }
              });
            }
            this.tableData = row;
            this.tableLoading = false;
          } else {
            this.tableLoading = false;
          }
        })
        .catch(err => {
          console.log(err);
          this.tableLoading = false;
        });
    },
    downloadAppendixFile(data) {
      this.appendixFileLoading = true;
      let param = {
        attId: data.id,
      };
      apiDownloadAppendixFile(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("文件下载成功");
          } else {
            this.$message.error("文件下载失败");
          }
          this.appendixFileLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("文件下载失败");
          this.appendixFileLoading = false;
        });
    },
    randomNumber() {
      let num = "";
      for (var i = 0; i < 4; i++) {
        num += Math.floor(Math.random() * 10);
      }
      return num;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../assets/common.scss";
</style>
