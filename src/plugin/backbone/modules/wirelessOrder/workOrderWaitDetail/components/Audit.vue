<template>
  <div>
    <el-form ref="auditForm" :model="auditForm" :rules="auditFormRules" label-width="100px">
      <el-form-item
        label="审核结果"
        prop="opResult"
        :rules="{
          required: true,
          message: '请选择审核结果',
        }"
      >
        <el-radio-group v-model="auditForm.opResult" style="width: 90%">
          <el-radio label="Y">同意</el-radio>
          <el-radio label="N">拒绝</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="审核意见" prop="processSuggestion">
        <el-input
          type="textarea"
          :rows="2"
          placeholder="请填写审核意见"
          v-model="auditForm.processSuggestion"
          style="width: 90%"
          show-word-limit
          maxlength="1000"
        >
        </el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: right">
      <el-button
        type="primary"
        @click="handleAuditSubmit('auditForm')"
        v-loading.fullscreen.lock="auditFullScreenLoading"
        >提 交</el-button
      >
      <el-button @click="onResetAudit">重 置</el-button>
    </div>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import { apiHangAuditWireless, getCurrentTime } from "../api/CommonApi";
import {mixin} from "../../../../../../mixins"
export default {
  name: "Audit",
  props: {
    common: Object,
    opContent: Number,
    actionName: String,
  },
  mixins: [mixin],
  data() {
    return {
      auditForm: {
        opResult: null,
        processSuggestion: null,
      },
      auditFormRules: {
        processSuggestion:[
          {
            validator: this.checkLength,
            max: 1000,
            form: "auditForm",
            messsage: "已超过填写字数上限",
          },
        ],
      },
      auditFullScreenLoading: false,
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  mounted() {
    console.log(this.common.professionalTypeName);
  },
  watch: {
    opContent(val) {
      this.opContent = val;
    },
  },
  methods: {
    handleAuditSubmit(formName) {
      this.$refs[formName].validate((valid,a) => {
        if (this.uncheck(a)) {
          this.auditFullScreenLoading = true;
          let param = {
            woId: this.common.woId,
            workItemId: this.common.workItemId,
            processInstId: this.common.processInstId,
            actionName: this.actionName,
            hangAuditUser: this.userInfo.userName,
            hangAuditTime: getCurrentTime(Date.now()),
            hangAuditResult: this.auditForm.opResult,
            hangAuditOpinion: this.auditForm.processSuggestion,
          };
          apiHangAuditWireless(param)
            .then(res => {
              if (res.status == "0") {
                this.$message.success("审核提交成功");
                if (this.opContent == 1) {
                  this.$emit("closeDialogPendingReview");
                } else if (this.opContent == 2) {
                  this.$emit("closeDialogSolutionToHangAudit");
                }
              } else {
                this.$message.error(res.msg);
              }
              this.auditFullScreenLoading = false;
            })
            .catch(error => {
              console.log(error);
              this.auditFullScreenLoading = false;
              this.$message.error(error.msg);
            });
        } else {
          return false;
        }
      });
    },
    onResetAudit() {
      this.auditForm = {
        ...this.$options.data,
      };
    },
  },
};
</script>
