<template>
  <div>
    <h3 style="margin-top: -5px">
      告警信息
      <el-button type="primary" @click="handleClear" style="float: right"
        >消除</el-button
      >
    </h3>
    <el-table
      ref="table"
      :data="tableData"
      border
      stripe
      v-loading="tableLoading"
    >
      <el-table-column
        type="selection"
        :selectable="selectDisableRoom"
      ></el-table-column>
      <el-table-column type="index" width="60px" label="序号"></el-table-column>
      <el-table-column label="告警地区" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          {{ scope.row.alarmProvince }}{{ scope.row.alarmRegion
          }}{{ scope.row.alarmCity }}
        </template>
      </el-table-column>
      <el-table-column
        prop="professionalType"
        label="专业"
        show-overflow-tooltip
      >
      </el-table-column>

      <el-table-column
        prop="locateNeName"
        label="告警对象"
        width="150"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="alarmTitle"
        label="标题"
        width="200"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="orgType"
        label="告警类型"
        width="100"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        label="故障发生时间"
        prop="alarmCreateTime"
        width="160"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        label="实际清除时间"
        prop="alarmClearTime"
        width="160"
        show-overflow-tooltip
      ></el-table-column>
    </el-table>
    <h3>消障确认</h3>
    <el-form
      ref="form"
      :model="form"
      label-width="110px"
      class="audit-form"
      :rules="rules"
    >
      <el-form-item label="是否确认消障:" prop="isClearConfirm" required>
        <el-radio-group v-model="form.isClearConfirm">
          <el-radio label="Y">是</el-radio>
          <el-radio label="N">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="备注:" prop="falutComment">
        <el-input
          v-model="form.falutComment"
          placeholder="请输入备注"
          style="width: 100%"
        >
        </el-input>
      </el-form-item>
    </el-form>
    <!-- 选择确认消障时间 -->
    <el-dialog
      title="选择消障时间"
      :visible.sync="dialogSelectTimeVisible"
      :close-on-click-modal="false"
      append-to-body
      width="300px"
    >
      <el-date-picker
        v-model="selectAlarmClearTime"
        type="datetime"
        placeholder="请选择时间"
        value-format="yyyy-MM-dd HH:mm:ss"
        clearable
        style="width: 100%"
        :picker-options="disabledPicker"
      />
      <div slot="footer">
        <el-button type="primary" @click="handleSelectTime">提 交</el-button>
        <el-button @click="dialogSelectTimeVisible = false">取消</el-button>
      </div>
    </el-dialog>
    <div slot="footer" style="padding: 10px 20px; text-align: right">
      <el-button
        type="primary"
        @click="handleAuditSubmit('form')"
        v-loading.fullscreen.lock="fullScreenLoading"
        >提 交</el-button
      >
      <!-- <el-button @click="onResetAudit">重 置</el-button> -->
    </div>
  </div>
</template>
<script>
import moment from "moment";
import { mapGetters } from "vuex";
import {
  apiActionPublic,
  getCurrentTime,
  apiQueryAlarmDetail,
  apiclearQuery,
} from "../api/CommonApi";
export default {
  name: "Audit",
  props: {
    common: Object,
    actionName: String,
    random: Number,
  },
  data() {
    return {
      tableLoading: false,
      tableData: [],
      form: {
        isClearConfirm: "Y",
      },
      fullScreenLoading: false,
      dialogSelectTimeVisible: false,
      selectAlarmClearTime: "",
      clearParms: {},
      disabledPicker: {
        disabledDate: time => {
          const alarmCreateTime = this.tableData[0].alarmCreateTime;
          if (alarmCreateTime) {
            return (
              time.getTime() <=
              new Date(alarmCreateTime).getTime() - 3600 * 1000 * 24
            );
          }
        },
        selectableRange: "",
      },
      rules: {
        isClearConfirm: [{ required: true, message: "请选择" }],
      },
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  mounted() {
    this.getTableData();
  },
  watch: {
    random: {
      handler() {
        this.getTableData();
      },
    },
    selectAlarmClearTime: {
      immediate: false,
      handler: function (newVal, oldVal) {
        // 故障发生时间的  时分秒。加1分钟
        let alarmCreateTimeStr = getCurrentTime(
          new Date(this.tableData[0].alarmCreateTime).getTime() + 60 * 1000
        ).slice(-8);
        this.disabledPicker.selectableRange = `${alarmCreateTimeStr} - 23:59:59`;
      },
    },
  },
  methods: {
    handleAuditSubmit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.fullScreenLoading = true;
          let param = {
            woId: this.common.woId,
            workItemId: this.common.workItemId,
            processInstId: this.common.processInstId,
            actionName: this.actionName,
            createUserEn: this.userInfo.userName,
            dept: this.userInfo.deptId,
            auditTime: getCurrentTime(Date.now()),
            isClearConfirm: this.form.isClearConfirm,
            falutComment: this.form.falutComment,
          };
          apiActionPublic(param)
            .then(res => {
              if (res.status == "0") {
                this.$message.success("消障确认成功");
                this.$emit("closeDialogEliminateFaults");
              } else {
                this.$message.error(res.msg);
              }
              this.fullScreenLoading = false;
            })
            .catch(error => {
              this.fullScreenLoading = false;
              this.$message.error(error.msg);
            });
        } else {
          return false;
        }
      });
    },
    getTableData() {
      this.tableLoading = true;
      let realParam = {
        woId: this.common.woId,
      };
      let param = {
        pageIndex: 1,
        pageSize: 1000,
        param1: JSON.stringify(realParam),
      };
      apiQueryAlarmDetail(param)
        .then(res => {
          if (res.status == "0") {
            this.tableData = res?.data?.rows ?? [];
          }
          this.tableLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.tableLoading = false;
        });
    },
    selectDisableRoom(row) {
      if (row.alarmClearTime == "" || null == row.alarmClearTime) {
        return true;
      } else {
        return false;
      }
    },
    handleSelectTime() {
      this.clearParms.alarmCreateTime = this.selectAlarmClearTime;
      apiclearQuery(this.clearParms)
        .then(res => {
          if (res.status == "0") {
            this.form.isClearConfirm = "Y";
            // this.tableData.forEach(item => {
            //   item.alarmClearTime = this.selectAlarmClearTime;
            // });
            this.getTableData();
            this.dialogSelectTimeVisible = false;
            this.$message.success("消除成功！");
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    // 批量消除
    handleClear() {
      this.clearParms.uniqueId = [];
      if (this.$refs.table.selection.length != 0) {
        this.dialogSelectTimeVisible = true;
        this.selectAlarmClearTime = moment().format("YYYY-MM-DD HH:mm:ss");
        this.$refs.table.selection.forEach(item => {
          this.clearParms.uniqueId.push(item.alarmStaId);
        });
      } else {
        this.$message.warning("选中要消除的工单");
        return false;
      }
    },
    onResetAudit() {
      this.form = {
        ...this.$options.data,
      };
    },
  },
};
</script>
<style scoped>
.custom-theme-default .el-rate {
  padding-top: 5px;
}
</style>
