<template>
  <div>
    <el-row>
      <el-col :span="24" style="text-align: center">
        <el-upload
          action="xx"
          class="upload-demo"
          :drag="true"
          ref="attachmentUpload"
          :file-list="importForm.attachmentFileList"
          name="attachmentFileList"
          :auto-upload="false"
          :on-change="handleUploadChange"
          :on-remove="upFileRemoveHandler"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">请 <em>点击上传</em></div>
          <!-- <div class="el-upload__tip" slot="tip">只能上传Excel文件</div> -->
        </el-upload>
      </el-col>
    </el-row>
    <div slot="footer" style="padding: 10px 20px; text-align: right">
      <el-button type="primary" @click="determine">确 定</el-button>
      <el-button @click="cancel">取 消</el-button>
    </div>
  </div>
</template>
<script>
export default {
  name: "FileUpload",
  data() {
    return {
      importForm: {
        attachmentFileList: [],
        fileName: "",
      },
      limit: 3,
    };
  },
  mounted() {
    console.log(this.importForm.attachmentFileList, "附件");
  },
  methods: {
    //上传附件列表改变
    handleUploadChange(file, fileList) {
      this.importForm.attachmentFileList = fileList;
    },
    //文件附件列表移除
    upFileRemoveHandler(file, fileList) {
      this.importForm.attachmentFileList = fileList;
    },
    determine() {
      let fileNameArr = this.importForm.attachmentFileList.map(item => {
        return item.name;
      });
      this.importForm.fileName = fileNameArr.join(",");
      this.$emit("change", this.importForm);
    },
    cancel() {
      this.$emit("cancel");
    },
    handleExceed() {
      this.$message.warning(`请最多上传 ${this.limit} 个文件。`);
    },
  },
};
</script>
