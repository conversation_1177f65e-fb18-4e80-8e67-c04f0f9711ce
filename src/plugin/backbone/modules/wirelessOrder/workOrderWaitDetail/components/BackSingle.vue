<template>
  <div class="back-single">
    <el-form
      ref="backSingleForm"
      :inline="false"
      class="demo-form-inline"
      :model="backSingleForm"
      label-width="130px"
      :rules="backSingleFormRule"
    >
      <el-card
        shadow="never"
        header="故障定性信息"
        :body-style="{ padding: '20px 8px' }"
        class="cus-card"
      >
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item
              label="故障所属专业:"
              prop="professionalType"
              :rules="{
                required: true,
                message: '请选择故障所属专业',
              }"
            >
              <dict-select
                :value.sync="backSingleForm.professionalType"
                :dictId="10002"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障发生时间:" prop="alarmCreateTime" required>
              {{ backSingleForm.alarmCreateTime }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障通知时间:" prop="sheetCreateTime" required>
              {{ backSingleForm.sheetCreateTime }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障代通时间:" prop="lastClearTime">
              <el-date-picker
                v-model="backSingleForm.lastClearTime"
                type="datetime"
                placeholder="请选择时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
                style="width: 100%"
                @change="computerFaultGenerationAter"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障代通历时:">
              {{ second2Time(backSingleForm.lastClearDuration) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障结束时间:" prop="faultEndTime">
              <el-date-picker
                v-model="backSingleForm.faultEndTime"
                type="datetime"
                placeholder="请选择时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
                style="width: 100%"
                @change="computerFaultTreatmentTime"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障处理历时:" required>
              {{ second2Time(this.backSingleForm.faultDuration) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="故障发生地区:"
              prop="faultRegion"
              :rules="{
                required: true,
                message: '请选择内容',
              }"
            >
              <el-select
                style="width: 100%"
                placeholder="请选择内容"
                v-model="backSingleForm.faultRegion"
                filterable
              >
                <el-option
                  v-for="(item, i) in faultRegionOptions"
                  :key="i"
                  :label="item.name"
                  :value="item.name"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障处理部门:" prop="dept" required>
              {{ backSingleForm.dept }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="处理人:" prop="person" required>
              {{ backSingleForm.person }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="挂起历时:" required>
              {{ second2Time(this.backSingleForm.suspendDuration) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障处理净历时:" required>
              {{ second2Time(this.backSingleForm.processDuration) }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item
              label="是否影响业务:"
              prop="isEffectBusiness"
              :rules="[
                {
                  required: backSingleForm.eqpType == '6' ? true : false,
                  message: '请选择是否影响业务',
                },
              ]"
            >
              <el-radio-group
                v-model="backSingleForm.isEffectBusiness"
                style="width: 100%"
                @change="faultCateChange"
              >
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item
              label="影响范围:"
              v-if="backSingleForm.isEffectBusiness == '1'"
              prop="effectRange"
              :rules="[
                {
                  required:
                    backSingleForm.isEffectBusiness == '1' ? true : false,
                  message: '请填写影响范围',
                  trigger: 'blur',
                },
              ]"
            >
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="backSingleForm.effectRange"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card
        shadow="never"
        header="故障专业信息"
        :body-style="{ padding: '20px' }"
        style="margin-top: 20px"
        class="cus-card"
      >
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item
              label="故障状态:"
              prop="faultStatus"
              :rules="{
                required: true,
                message: '请选择故障状态',
              }"
            >
              <dict-select
                :value.sync="backSingleForm.faultStatus"
                :dictId="10017"
                style="width: 100%"
                placeholder="请选择内容"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="网络类型:"
              prop="networkType"
              :rules="{
                required: true,
                message: '请选择网络类型',
              }"
            >
              <dict-select
                :value.sync="backSingleForm.networkType"
                :dictId="10018"
                style="width: 100%"
                placeholder="请选择内容"
                :notSelect="true"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="故障分类:"
              prop="faultCate"
              :rules="{
                required: true,
                message: '请选择故障分类',
              }"
            >
              <dict-select
                :value.sync="backSingleForm.faultCate"
                :dictId="10019"
                style="width: 100%"
                @change="faultCateChange('faultCate')"
                placeholder="请选择内容"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row
          type="flex"
          style="flex-wrap: wrap"
          :gutter="20"
          v-if="backSingleForm.faultCate == '1'"
          key="faultCate1"
        >
          <el-col :span="8">
            <el-form-item
              label="故障原因:"
              :rules="{
                required: true,
                message: '请选择故障原因',
              }"
              prop="circuitFaultReason"
            >
              <dict-select
                :value.sync="backSingleForm.circuitFaultReason"
                :dictId="10020"
                style="width: 100%"
                placeholder="请选择内容"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="光缆名称:"
              prop="opticFiber"
              :rules="{
                required: true,
                message: '光缆名称不能为空',
              }"
            >
              <el-input v-model="backSingleForm.opticFiber" style="width: 100%">
                <el-button
                  style="
                    background: #b50b14;
                    border-color: #b50b14;
                    color: #fff;
                    padding: 9px 20px;
                    top: -0.5px;
                    position: relative;
                  "
                  slot="append"
                  @click="opticalCableSelect"
                  >选择</el-button
                ></el-input
              >
              <form
                id="sub__fiberOpticCable"
                name="sub__fiberOpticCable"
                hidden="true"
                method="post"
                action="/resweb_jituan/union/resAssign.out?method=selectFiberSection&requestJson={}"
                target="_blank"
              >
                <input
                  type="hidden"
                  name="fiberOpticCable"
                  id="fiberOpticCable"
                />
              </form>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="故障区间:"
              prop="faultRange"
              :rules="{
                required: true,
                message: '请输入故障区间',
              }"
            >
              <el-input
                v-model="backSingleForm.faultRange"
                style="width: 100%"
                placeholder="请输入内容"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="维护主体:"
              prop="maintainDept"
              :rules="{
                required: true,
                message: '请选择维护主体',
              }"
            >
              <dict-select
                :value.sync="backSingleForm.maintainDept"
                :dictId="10021"
                style="width: 100%"
                placeholder="请输入内容"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="是否保护:"
              prop="isProtected"
              :rules="{
                required: true,
                message: '请选择是否保护',
              }"
            >
              <el-radio-group
                v-model="backSingleForm.isProtected"
                style="width: 100%"
                @change="faultCateChange"
              >
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="保护是否生效:"
              :rules="{
                required: true,
                message: '请选择保护是否生效',
              }"
              prop="isProtectedValid"
            >
              <el-radio-group v-model="backSingleForm.isProtectedValid">
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否超时:" key="isOverTimeShowXl">
              {{ backSingleForm.isOverTimeShow }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="相关附件:" key="relatedFilesXl">
              <el-tag
                class="fileName_style"
                closable
                v-for="(item, index) in importForm.relatedFilesFileList"
                :key="index"
                @close="close(item)"
                :title="item.name"
                ><div class="text-truncate">{{ item.name }}</div></el-tag
              >
              <el-button size="mini" type="primary" @click="relatedFilesBrowse"
                >+上传附件</el-button
              >
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="受影响系统:">
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="backSingleForm.effectSystem"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="受影响电路:">
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="backSingleForm.effectCircuit"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              label="故障原因描述:"
              prop="falutReasonDesc"
              :rules="{
                required: backSingleForm.faultCate == '1' ? true : false,
                message: '请填写故障原因描述',
              }"
            >
              <el-input
                type="textarea"
                :rows="2"
                v-model="backSingleForm.falutReasonDesc"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注:">
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="backSingleForm.falutComment"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row
          type="flex"
          style="flex-wrap: wrap"
          :gutter="20"
          v-if="backSingleForm.faultCate == '2'"
          key="faultCateTwo"
        >
          <el-col :span="8">
            <el-form-item
              label="设备类型:"
              prop="eqpType"
              :rules="{
                required: true,
                message: '请选择设备类型',
              }"
            >
              <dict-select
                :value.sync="backSingleForm.eqpType"
                :dictId="10046"
                style="width: 100%"
                placeholder="请选择内容"
                @change="faultCateChange('eqTypeChange')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="设备名称:">
              <el-input
                placeholder="请输入内容"
                v-model="backSingleForm.eqpName"
                style="width: 100%"
              >
                <el-button
                  style="
                    background: #b50b14;
                    border-color: #b50b14;
                    color: #fff;
                    padding: 9px 20px;
                    top: -0.5px;
                    position: relative;
                  "
                  slot="append"
                  @click="deviceSelect"
                  >选择</el-button
                >
              </el-input>
              <form
                id="sub__device"
                name="sub__device"
                hidden="true"
                method="post"
                action="/resweb_jituan/union/resAssign.out?method=selectEquipment"
                target="_blank"
              >
                <input type="hidden" name="device" id="device" />
              </form>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="故障原因:"
              :rules="{
                required: backSingleForm.faultCate == '2' ? true : false,
                message: '请选择内容',
              }"
              prop="eqpFaultReason"
            >
              <dict-select
                :value.sync="backSingleForm.eqpFaultReason"
                :dictId="faultReasonDictId"
                style="width: 100%"
                placeholder="请选择内容"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="故障区间:"
              :rules="{
                required: true,
                message: '请输入故障区间',
              }"
              prop="faultRange"
            >
              <el-input
                placeholder="请输入内容"
                v-model="backSingleForm.faultRange"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="维护主体:"
              :rules="{
                required: true,
                message: '请选择维护主体',
              }"
              prop="maintainDept"
            >
              <dict-select
                :value.sync="backSingleForm.maintainDept"
                :dictId="10021"
                style="width: 100%"
                placeholder="请选择内容"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="板卡类型:"
              prop="cardType"
              v-if="
                backSingleForm.faultCate == '2' &&
                (backSingleForm.eqpType == '1' ||
                  backSingleForm.eqpType == '2' ||
                  backSingleForm.eqpType == '3' ||
                  backSingleForm.eqpType == '4' ||
                  backSingleForm.eqpType == '5')
              "
              :rules="{
                required:
                  backSingleForm.faultCate == '2' &&
                  (backSingleForm.eqpType == '1' ||
                    backSingleForm.eqpType == '2' ||
                    backSingleForm.eqpType == '3' ||
                    backSingleForm.eqpType == '4' ||
                    backSingleForm.eqpType == '5')
                    ? true
                    : false,
                message: '请选择板卡类型',
              }"
            >
              <dict-select
                :value.sync="backSingleForm.cardType"
                :dictId="cardTypeDictId"
                style="width: 100%"
                placeholder="请选择内容"
                @change="faultCateChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="是否有人机房:"
              prop="isPersonInRoom"
              :rules="{
                required: true,
                message: '请选择是否有人机房',
              }"
            >
              <el-radio-group
                v-model="backSingleForm.isPersonInRoom"
                style="width: 100%"
                @change="faultCateChange"
              >
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="是否有备件:"
              prop="hasBackupPart"
              :rules="{
                required: true,
                message: '请选择是否有备件',
              }"
            >
              <el-radio-group
                v-model="backSingleForm.hasBackupPart"
                style="width: 100%"
                @change="faultCateChange"
              >
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="是否保护:"
              prop="isProtected"
              :rules="{
                required: true,
                message: '请选择是否保护',
              }"
            >
              <el-radio-group
                v-model="backSingleForm.isProtected"
                @change="faultCateChange"
              >
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="保护是否生效:"
              prop="isProtectedValid"
              :rules="{
                required: true,
                message: '请选择保护是否生效',
              }"
            >
              <el-radio-group
                v-model="backSingleForm.isProtectedValid"
                @change="faultCateChange"
              >
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否超时:">
              {{ backSingleForm.isOverTimeShow }}
            </el-form-item></el-col
          >
          <el-col :span="24">
            <el-form-item
              label="承载业务系统:"
              prop="supportSystem"
              :rules="{
                required: true,
                message: '承载业务系统不能为空',
              }"
            >
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="backSingleForm.supportSystem"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="受影响系统:">
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="backSingleForm.effectSystem"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="受影响电路:">
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="backSingleForm.effectCircuit"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              label="故障厂家:"
              prop="vendor"
              :rules="{
                required: true,
                message: '请选择故障厂家',
              }"
            >
              <el-radio-group v-model="backSingleForm.vendor">
                <el-radio
                  v-for="(item, i) in vendorOptions"
                  :key="i"
                  :label="item.dictCode"
                  >{{ item.dictName }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              label="故障原因描述:"
              prop="falutReasonDesc"
              :rules="{
                required: backSingleForm.faultCate == '2' ? true : false,
                message: '请填写故障原因描述',
              }"
            >
              <el-input
                type="textarea"
                :rows="2"
                v-model="backSingleForm.falutReasonDesc"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注:">
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="backSingleForm.falutComment"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="相关附件:">
              <el-tag
                class="fileName_style"
                closable
                v-for="(item, index) in importForm.relatedFilesFileList"
                :key="index"
                @close="close(item)"
                :title="item.name"
                ><div class="text-truncate">{{ item.name }}</div></el-tag
              >
              <el-button size="mini" type="primary" @click="relatedFilesBrowse"
                >+上传附件</el-button
              >
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: center">
      <el-button type="primary" @click="nextStepEvaluation()">下一步</el-button>
      <el-button @click="onReset">重 置</el-button>
    </div>

    <el-dialog
      width="420px"
      title="附件选择"
      :visible.sync="relatedFilesDialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <file-upload
        @change="changeFileData"
        @cancel="closeAttachmentDialog"
      ></file-upload>
    </el-dialog>
    <el-dialog
      width="620px"
      title="满意度评价"
      :visible.sync="evaluationDialogVisible"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      append-to-body
    >
      <el-form ref="evaluation" :model="evaluation">
        <el-form-item label="派单准确度:" label-width="90px">
          <el-rate v-model="evaluation.orderAccuracy" :colors="colors">
          </el-rate>
        </el-form-item>
        <el-form-item label="诊断准确度:" label-width="90px">
          <el-rate v-model="evaluation.diagnosticrAccuracy" :colors="colors">
          </el-rate>
        </el-form-item>
        <el-form-item
          v-if="
            evaluation.orderAccuracy <= 3 || evaluation.diagnosticrAccuracy <= 3
          "
          label="反馈问题:"
          label-width="90px"
          prop="feedbackProblemCheckList"
          :rules="[
            {
              required:
                evaluation.orderAccuracy > 3 &&
                evaluation.diagnosticrAccuracy > 3
                  ? false
                  : true,
              message: '请选择要反馈的问题',
              trigger: 'blur',
            },
          ]"
        >
          <el-checkbox-group
            v-model="evaluation.feedbackProblemCheckList"
            @change="feedbackChange"
          >
            <el-checkbox label="派单超时"></el-checkbox>
            <el-checkbox label="工单基础信息错误"></el-checkbox>
            <el-checkbox label="诊断信息不完整"></el-checkbox>
            <el-checkbox label="诊断信息错误"></el-checkbox>
            <el-checkbox label="其他"></el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item
          v-if="
            viewsOnContentShow &&
            (evaluation.orderAccuracy <= 3 ||
              evaluation.diagnosticrAccuracy <= 3)
          "
          prop="viewsOnContent"
          :rules="{
            required: true,
            message: '请填写内容',
          }"
        >
          <el-input
            type="textarea"
            :rows="3"
            placeholder="为了下次给您更好的服务，请留下宝贵意见，对您和我们都很重要"
            v-model="evaluation.viewsOnContent"
          >
          </el-input>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button
          type="primary"
          @click="handleSubmit('evaluation')"
          v-loading.fullscreen.lock="backSingleFullscreenLoading"
          >提 交</el-button
        >
        <el-button @click="evaluationDialogVisible = false">上一步</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import moment from "moment";
import { mapGetters } from "vuex";
import DictSelect from "../../../workOrder/components/DictSelect.vue";
import { apiBackSingle, apiGetFaultArea } from "../api/CommonApi";
import { apiDict, apiGetOrgInfo } from "../../../workOrder/api/CommonApi";
import FileUpload from "../../../workOrder/components/FileUpload.vue";
import { config } from "../api/TroubleshootingTime";
export default {
  name: "BackSingle",
  props: {
    common: Object,
    timing: Object,
  },
  components: { DictSelect, FileUpload },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  data() {
    var validLastClearTime = (rule, value, callback) => {
      if (
        this.backSingleForm.lastClearTime &&
        this.backSingleForm.faultEndTime
      ) {
        let seconds = moment(
          this.backSingleForm.faultEndTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.backSingleForm.lastClearTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );

        let seconds2 = moment(
          this.backSingleForm.lastClearTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.common.failureInformTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        if (seconds < 0 || seconds2 <= 0) {
          callback(
            new Error(
              "故障结束时间>=故障代通时间>故障通知时间，请重新检查后选择正确时间"
            )
          );
        } else {
          callback();
        }
      } else if (this.backSingleForm.lastClearTime) {
        let seconds3 = moment(
          this.backSingleForm.lastClearTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(moment(new Date(), "YYYY-MM-DD HH:mm:ss"), "seconds");
        let seconds4 = moment(
          this.backSingleForm.lastClearTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.common.failureInformTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        if (seconds3 > 0 || seconds4 <= 0) {
          callback(
            new Error(
              "当前时间>=故障代通时间>故障通知时间，请重新检查后选择正确时间"
            )
          );
        }
      } else {
        callback();
      }
    };
    var validFaultEndTime = (rule, value, callback) => {
      if (value === "" || value === null) {
        callback(new Error("请选择故障结束时间"));
      } else {
        if (this.backSingleForm.lastClearTime) {
          let clSeconds = moment(
            this.backSingleForm.faultEndTime,
            "YYYY-MM-DD HH:mm:ss"
          ).diff(
            moment(this.backSingleForm.lastClearTime, "YYYY-MM-DD HH:mm:ss"),
            "seconds"
          );
          if (clSeconds < 0) {
            callback(
              new Error(
                "当前时间>=故障结束时间>=故障代通时间，请重新检查后选择正确时间"
              )
            );
          }
        }
        let seconds = moment(
          this.backSingleForm.faultEndTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.common.failureInformTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );

        let seconds2 = moment(new Date(), "YYYY-MM-DD HH:mm:ss").diff(
          moment(this.backSingleForm.faultEndTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        if (seconds <= 0 || seconds2 < 0) {
          callback(
            new Error(
              "当前时间>=故障结束时间>故障通知时间，请重新检查后选择正确时间"
            )
          );
        } else {
          callback();
        }
      }
    };
    return {
      backSingleForm: {
        woId: null,
        workItemId: null,
        processInstId: null,
        processDefId: null,
        professionalType: null,
        alarmCreateTime: null,
        sheetCreateTime: null,
        lastClearTime: null,
        lastClearDuration: 0, //故障代通历时
        faultEndTime: null,
        faultDuration: 0, //故障处理历时
        faultRegion: null,
        dept: null,
        person: null,
        suspendDuration: 0, //挂起历时 单位秒
        processDuration: 0, //故障处理净历时 单位秒
        isEffectBusiness: null,
        effectRange: null,
        siteOfflineReason: null,
        faultStatus: null,
        networkType: null,
        faultCate: null,
        circuitFaultReason: null,
        opticFiber: null,
        faultRange: null,
        maintainDept: null,
        isProtected: null,
        isProtectedValid: null,
        isOverTime: null,
        isOverTimeShow: null,
        relatedFiles: null,
        effectSystem: null,
        effectCircuit: null,
        falutReasonDesc:
          this.common.faultCauseDescription ||
          "样例：距xx机房xxkm处，光缆因xxx（原因）中断。 处理方式：xxxx （熔接或者倒带后）恢复。",
        falutComment: null,
        //设备故障
        eqpType: null,
        eqpName: "",
        eqpFaultReason: null,
        cardType: null,
        isPersonInRoom: null,
        hasBackupPart: null,
        isEffectBiz: null,
        supportSystem: null,
        vendor: null,
        actionName: "",
        areaCode: null, //区域编码
        category: null, //省份返单 OR 地市返单
      },
      backSingleFullscreenLoading: false,
      //关联附件
      relatedFilesDialogVisible: false,
      importForm: {
        relatedFilesFileList: [],
      },
      evaluationDialogVisible: false,
      evaluation: {
        orderAccuracy: null,
        diagnosticrAccuracy: null,
        viewsOnContent: null,
        feedbackProblemCheckList: [],
      },
      colors: ["#99A9BF", "#F7BA2A", "#FF9900"], // 等同于 { 2: '#99A9BF', 4: { value: '#F7BA2A', excluded: true }, 5: '#FF9900' }
      vendorOptions: [],
      //当前人的部门
      currentOrgName: null,
      faultRegionOptions: [],
      userData: null,
      faultReasonDictId: 0, //故障原因 多字典ID
      cardTypeDictId: 0, //板卡类型 多字典ID
      startTimeRange: "",
      startTimeRangeByFaultEndTime: "",

      backSingleFormRule: {
        lastClearTime: [{ validator: validLastClearTime, trigger: "blur" }],
        faultEndTime: [
          { validator: validFaultEndTime, required: true, trigger: "change" },
        ],
      },
      viewsOnContentShow: false,
    };
  },
  mounted() {
    this.backSingleForm.alarmCreateTime = this.common.failureTime;
    this.backSingleForm.sheetCreateTime = this.common.failureInformTime;
    this.backSingleForm.person = this.userInfo.realName;
    this.backSingleForm.workItemId = this.common.workItemId;
    this.backSingleForm.woId = this.common.woId;
    this.backSingleForm.processInstId = this.common.processInstId;
    this.backSingleForm.processDefId = this.common.processDefId;
    this.backSingleForm.actionName = this.common.actionName;
    this.backSingleForm.professionalType = this.common.professionalType + "";
    this.backSingleForm.networkType = this.common.networkType;
    this.backSingleForm.suspendDuration = this.common.hangOver;
    this.getVendorOptions();
    this.userData = JSON.parse(this.userInfo.attr2);
    this.getOrgInfo();


    this.resourceBackInit(this.backSingleForm);
  },
  methods: {
    getOrgInfo() {
      apiGetOrgInfo()
        .then(res => {
          if (res.status == "0") {
            this.backSingleForm.dept = res?.data?.orgInfo?.fullOrgName ?? "";
            this.backSingleForm.areaCode = res?.data?.orgInfo?.areaCode ?? "";
            this.backSingleForm.category = res?.data?.category ?? "";
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    getFaultAreaOptions() {
      let param = {
        areaCode: this.backSingleForm.areaCode,
        category: this.backSingleForm.category,
      };
      apiGetFaultArea(param)
        .then(res => {
          if (res.status == "0") {
            this.faultRegionOptions = res?.data ?? [];
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    getVendorOptions() {
      let param = {
        dictTypeCode: "10047",
      };
      apiDict(param)
        .then(res => {
          if (res.code == "200") {
            this.vendorOptions = res?.data ?? [];
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    relatedFilesBrowse() {
      this.relatedFilesDialogVisible = true;
    },
    changeFileData(data) {
      this.backSingleForm.relatedFiles = data.fileName;
      this.importForm.relatedFilesFileList = data.attachmentFileList;
      this.relatedFilesDialogVisible = false;
    },
    closeAttachmentDialog() {
      this.relatedFilesDialogVisible = false;
    },
    computerFaultGenerationAter() {
      if (this.backSingleForm.lastClearTime) {
        let days = moment(
          this.backSingleForm.lastClearTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.backSingleForm.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        this.backSingleForm.lastClearDuration = days;
      } else {
        this.backSingleForm.lastClearDuration = 0;
      }
      this.faultCateChange();
    },
    second2Time(days) {
      return this.showTime(Math.abs(days));
    },
    computerFaultTreatmentTime() {
      let days = moment(
        this.backSingleForm.faultEndTime,
        "YYYY-MM-DD HH:mm:ss"
      ).diff(
        moment(this.backSingleForm.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
        "seconds"
      );
      this.backSingleForm.faultDuration = days;

      //故障处理净历时  （故障结束时间-故障发生时间）-挂起历时(秒) 换算成-天-小时-分钟-秒
      if (this.backSingleForm.suspendDuration == 0) {
        this.backSingleForm.processDuration = this.backSingleForm.faultDuration;
      } else {
        let seconds = moment(
          this.backSingleForm.faultEndTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.backSingleForm.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        this.backSingleForm.processDuration =
          seconds - this.backSingleForm.suspendDuration;
      }
      this.faultCateChange();
    },
    //挂起历时
    computerSuspendDuration() {
      if (this.timing.hangTime != "" && null != this.timing.hangTime) {
        if (
          this.timing.liftHangTime != "" &&
          null != this.timing.liftHangTime
        ) {
          let seconds = moment(
            this.timing.liftHangTime,
            "YYYY-MM-DD HH:mm:ss"
          ).diff(
            moment(this.timing.hangTime, "YYYY-MM-DD HH:mm:ss"),
            "seconds"
          );
          this.backSingleForm.suspendDuration = seconds;
        } else {
          this.backSingleForm.suspendDuration = 0;
        }
      } else {
        this.backSingleForm.suspendDuration = 0;
      }
    },
    //下一步评价
    nextStepEvaluation() {
      this.$refs.backSingleForm.validate(valid => {
        if (valid) {
          this.evaluationDialogVisible = true;
        } else {
          return false;
        }
      });
    },
    handleSubmit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          let self = this;
          this.backSingleFullscreenLoading = true;
          let formData = new FormData();
          if (self.importForm.relatedFilesFileList.length > 0) {
            for (let item of self.importForm.relatedFilesFileList) {
              formData.append("files", item.raw);
            }
          } else {
            formData.append("files", "");
          }
          // 当前剩余受理时限
          let remainAcceptTime = this?.common?.acceptTimeLimit - Math.floor((new Date()-new Date(this?.backSingleForm?.sheetCreateTime))/60000);
          if(remainAcceptTime <= 0){
            remainAcceptTime = -1;
          }
          this.backSingleForm.remainAcceptTime = remainAcceptTime;
          formData.append("jsonParam", JSON.stringify(this.backSingleForm));
          let evaluateParam = {
            woId: this.common.woId,
            sheetCreateTime: this.backSingleForm.sheetCreateTime,
            sendAccuracy: this.evaluation.orderAccuracy,
            diagnoseAccuracy: this.evaluation.diagnosticrAccuracy,
            evaluateContent: this.evaluation.viewsOnContent,
            problemClass:
              this.evaluation.feedbackProblemCheckList.length > 0
                ? this.evaluation.feedbackProblemCheckList.join(",")
                : "",
            remainAcceptTime: remainAcceptTime,
          };
          formData.append("evaluateParam", JSON.stringify(evaluateParam));
          apiBackSingle(formData)
            .then(res => {
              if (res.status == "0") {
                this.$message.success("提交返单成功");
                this.onReset();
                this.onResetEvaluation();
                this.evaluationDialogVisible = false;
                this.$emit("closeBackSingleDialog", res.data);
              } else {
                this.$message.error("提交返单失败");
              }
              this.backSingleFullscreenLoading = false;
            })
            .catch(error => {
              console.log(error);
              this.$message.error("提交返单失败");
              this.backSingleFullscreenLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    faultCateChange(type) {
      if (type == "eqTypeChange") {
        this.eqTypeChange();
      } else if (type == "faultCate") {
        this.resetFaultCateChange();
      }
      if (this.backSingleForm.faultCate == 1) {
        //线路故障
        if (this.backSingleForm.isProtected == 0) {
          //无保护
          for (let item of config.proNotProtect) {
            if (item.pro.indexOf(this.userData.provinceName) != "-1") {
              let seconds = this.dealTimeout();
              if (seconds > item.timeLimit * 60) {
                this.$set(this.backSingleForm, "isOverTimeShow", "是");
                this.$set(this.backSingleForm, "isOverTime", 1);
              } else {
                this.$set(this.backSingleForm, "isOverTimeShow", "否");
                this.$set(this.backSingleForm, "isOverTime", 0);
              }
            }
          }
        } else if (this.backSingleForm.isProtected == 1) {
          //有保护
          for (let item2 of config.proProtect) {
            if (item2.pro.indexOf(this.userData.provinceName) != "-1") {
              let seconds2 = this.dealTimeout();
              if (seconds2 > item2.timeLimit * 60) {
                this.$set(this.backSingleForm, "isOverTimeShow", "是");
                this.$set(this.backSingleForm, "isOverTime", 1);
              } else {
                this.$set(this.backSingleForm, "isOverTimeShow", "否");
                this.$set(this.backSingleForm, "isOverTime", 0);
              }
            }
          }
        }
      } else if (this.backSingleForm.faultCate == 2) {
        //设备故障
        if (
          (this.backSingleForm.eqpType == "4" ||
            this.backSingleForm.eqpType == "2" ||
            this.backSingleForm.eqpType == "5") &&
          this.backSingleForm.cardType == "3" &&
          this.backSingleForm.isProtected == "1" &&
          this.backSingleForm.isProtectedValid == "1"
        ) {
          for (let item4 of config.proEquipmentRule1) {
            if (item4.pro.indexOf(this.userData.provinceName) != "-1") {
              let seconds6 = this.dealTimeout();
              if (seconds6 > item4.timeLimit * 60) {
                this.$set(this.backSingleForm, "isOverTimeShow", "是");
                this.$set(this.backSingleForm, "isOverTime", 1);
              } else {
                this.$set(this.backSingleForm, "isOverTimeShow", "否");
                this.backSingleForm.isOverTime = 0;
              }
            }
          }
        } else if (
          this.backSingleForm.eqpType == "6" &&
          this.backSingleForm.isEffectBusiness == "0"
        ) {
          let seconds5 = this.dealTimeout();
          if (seconds5 > 720 * 60) {
            this.$set(this.backSingleForm, "isOverTimeShow", "是");
            this.$set(this.backSingleForm, "isOverTime", 1);
          } else {
            this.$set(this.backSingleForm, "isOverTimeShow", "否");
            this.backSingleForm.isOverTime = 0;
          }
        } else if (
          this.backSingleForm.isPersonInRoom == "1" &&
          this.backSingleForm.hasBackupPart == "1"
        ) {
          let seconds3 = this.dealTimeout();
          if (seconds3 > 60 * 60) {
            this.$set(this.backSingleForm, "isOverTimeShow", "是");
            this.$set(this.backSingleForm, "isOverTime", 1);
          } else {
            this.$set(this.backSingleForm, "isOverTimeShow", "否");
            this.$set(this.backSingleForm, "isOverTime", 0);
          }
        } else if (
          (this.backSingleForm.isPersonInRoom == "1" &&
            this.backSingleForm.hasBackupPart == "0") ||
          (this.backSingleForm.isPersonInRoom == "0" &&
            this.backSingleForm.hasBackupPart == "1") ||
          (this.backSingleForm.isPersonInRoom == "0" &&
            this.backSingleForm.hasBackupPart == "0")
        ) {
          for (let item3 of config.proEquipmentRule4) {
            if (item3.pro.indexOf(this.userData.provinceName) != "-1") {
              let seconds4 = this.dealTimeout();
              if (seconds4 > item3.timeLimit * 60) {
                this.$set(this.backSingleForm, "isOverTimeShow", "是");
                this.backSingleForm.isOverTime = 1;
              } else {
                this.$set(this.backSingleForm, "isOverTimeShow", "否");
                this.backSingleForm.isOverTime = 0;
              }
            }
          }
        }
      }
    },
    eqTypeChange() {
      if (this.backSingleForm.eqpType == "1") {
        //SDH
        this.faultReasonDictId = "10030";
        this.cardTypeDictId = "10037";
      } else if (this.backSingleForm.eqpType == "2") {
        //DWDM
        this.faultReasonDictId = "10031";
        this.cardTypeDictId = "10038";
      } else if (this.backSingleForm.eqpType == "3") {
        //ASON
        this.faultReasonDictId = "10032";
        this.cardTypeDictId = "10039";
      } else if (this.backSingleForm.eqpType == "4") {
        //OTN
        this.faultReasonDictId = "10033";
        this.cardTypeDictId = "10040";
      } else if (this.backSingleForm.eqpType == "5") {
        //ROADM
        this.faultReasonDictId = "10044";
        this.cardTypeDictId = "10041";
      } else if (this.backSingleForm.eqpType == "6") {
        //网管故障
        this.faultReasonDictId = "10034";
        this.backSingleForm.cardType = null;
      } else if (this.backSingleForm.eqpType == "7") {
        //配套设备
        this.faultReasonDictId = "10035";
        this.backSingleForm.cardType = null;
      } else if (this.backSingleForm.eqpType == "8") {
        //其他
        this.faultReasonDictId = "10036";
        this.backSingleForm.cardType = null;
      }
    },
    resetFaultCateChange() {
      this.backSingleForm.circuitFaultReason = null;
      this.backSingleForm.opticFiber = null;
      this.backSingleForm.faultRange = null;
      this.backSingleForm.maintainDept = null;
      this.backSingleForm.isProtected = null;
      this.backSingleForm.isProtectedValid = null;
      this.importForm.relatedFilesFileList = [];
      this.backSingleForm.effectSystem = null;
      this.backSingleForm.effectCircuit = null;
      this.backSingleForm.falutReasonDesc =
        this.common.faultCauseDescription ||
        "样例：距xx机房xxkm处，光缆因xxx（原因）中断。 处理方式：xxxx （熔接或者倒带后）恢复。";
      this.backSingleForm.falutComment = null;
      this.backSingleForm.eqpType = null;
      this.backSingleForm.eqpName = null;
      this.backSingleForm.eqpFaultReason = null;
      this.backSingleForm.cardType = null;
      this.backSingleForm.isPersonInRoom = null;
      this.backSingleForm.hasBackupPart = null;
      this.backSingleForm.supportSystem = null;
      this.backSingleForm.vendor = null;
    },
    close(tag) {
      this.importForm.relatedFilesFileList.splice(
        this.importForm.relatedFilesFileList.indexOf(tag),
        1
      );
    },
    dealTimeout() {
      //故障代通时间（无故障代通时间 用故障结束时间） - 故障开始时间
      if (this.backSingleForm.lastClearTime) {
        let seconds = moment(
          this.backSingleForm.lastClearTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.backSingleForm.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        return seconds;
      } else {
        if (this.backSingleForm.faultEndTime) {
          let seconds2 = moment(
            this.backSingleForm.faultEndTime,
            "YYYY-MM-DD HH:mm:ss"
          ).diff(
            moment(this.backSingleForm.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
            "seconds"
          );
          return seconds2;
        }
      }
    },
    onReset() {
      this.backSingleForm = {
        ...this.$options.data,
        alarmCreateTime: this.common.failureTime,
        sheetCreateTime: this.common.failureInformTime,
        woId: this.common.woId,
        workItemId: this.common.workItemId,
        processInstId: this.common.processInstId,
        processDefId: this.common.processDefId,
        person: this.userInfo.realName,
        dept: this.userData.orgInfo.fullOrgName,
        actionName: this.common.actionName,
        networkType: this.common.networkType,
        professionalType: "3",
        lastClearDuration: 0, //故障代通历时
        faultDuration: 0, //故障处理净历时
        suspendDuration: this.common.hangOver, //挂起历时 单位秒
        processDuration: 0, //故障处理净历时 单位秒
        effectSystem: null,
        circuitFaultReason: null,
        opticFiber: null,
        faultRange: null,
        maintainDept: null,
        isProtected: null,
        isProtectedValid: null,
        effectCircuit: null,
        falutReasonDesc:
          this.common.faultCauseDescription ||
          "样例：距xx机房xxkm处，光缆因xxx（原因）中断。 处理方式：xxxx （熔接或者倒带后）恢复。",
        falutComment: null,
        eqpType: null,
        eqpName: null,
        eqpFaultReason: null,
        cardType: null,
        isPersonInRoom: null,
        hasBackupPart: null,
        isEffectBiz: null,
        supportSystem: null,
        vendor: null,
        faultEndTime: null,
        lastClearTime: null,
      };
      console.log(this.backSingleForm);
    },
    onResetEvaluation() {
      this.evaluation = {
        ...this.$options.data,
      };
    },
    showTime(val) {
      if (val) {
        if (val == 0) return "0秒";
        var time = "";
        var second = val % 60;
        var minute = parseInt(parseInt(val) / 60) % 60;
        time = second + "秒";
        if (minute >= 1) {
          time = minute + "分" + second + "秒";
        }
        var hour = parseInt(parseInt(val / 60) / 60) % 24;
        if (hour >= 1) {
          time = hour + "小时" + minute + "分" + second + "秒";
        }
        var day = parseInt(parseInt(parseInt(val / 60) / 60) / 24);
        if (day >= 1) {
          time = day + "天" + hour + "小时" + minute + "分" + second + "秒";
        }

        return time;
      } else {
        return "0秒";
      }
    },
    //光缆名称选择
    opticalCableSelect() {
      let jsonStr =
        '{"userId":"302785","username":"yangxm97","callSysId":"0001","callSysName":"电子运维"}';
      this.$nextTick(() => {
        document.querySelector("#fiberOpticCable").value = jsonStr;
        document.querySelector("#sub__fiberOpticCable").submit();
      });
    },
    //设备名称选择
    deviceSelect() {
      let jsonStr =
        '{"userId":"302785","username":"yangxm97","callSysId":"0001","callSysName":"电子运维"}';
      this.$nextTick(() => {
        document.querySelector("#device").value = jsonStr;
        document.querySelector("#sub__device").submit();
      });
    },
    //资源系统回调方法初始化
    resourceBackInit() {
      window["__process_response_from_getDevice"] = value => {
        let form = this.backSingleForm;
        let result = JSON.parse(value);
        let objArray = JSON.parse(result?.objects);

        if (form.faultCate == "1") {
          let opticFiberArray = form.opticFiber
            ? form.opticFiber.split(",")
            : [];
          let effectSystemArray = form.effectSystem
            ? form.effectSystem.split(",")
            : [];
          let effectSystemArrayTemp = [];

          objArray.forEach(obj => {
            if (opticFiberArray.indexOf(obj.resName) < 0) {
              opticFiberArray.push(obj.resName);
            }

            effectSystemArrayTemp = JSON.parse(obj.affectedSystem);
            effectSystemArrayTemp.forEach(sys => {
              if (sys.sysName && effectSystemArray.indexOf(sys.sysName) < 0) {
                effectSystemArray.push(sys.sysName);
              }
            });
          });

          form.opticFiber = opticFiberArray.join(",");
          form.effectSystem = effectSystemArray.join(",");
          form.eqpName = "";
        } else {
          let eqpNameArray = form.eqpName ? form.eqpName.split(",") : [];

          objArray.forEach(obj => {
            if (eqpNameArray.indexOf(obj.resName) < 0) {
              eqpNameArray.push(obj.resName);
            }
          });

          form.eqpName = eqpNameArray.join(",");
          form.opticFiber = "";
          form.effectSystem = "";
        }
      };
    },
    feedbackChange(val) {
      if (val.length > 0) {
        if (val.join(",").indexOf("其他") != -1) {
          this.viewsOnContentShow = true;
        } else {
          this.viewsOnContentShow = false;
        }
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.back-single {
  max-height: calc(90vh - 150px);
  min-height: 400px;
  margin: 0 4px;
  padding-left: 4px;
  padding-right: 8px;
  overflow-y: auto;
  .cus-card ::v-deep .el-card__header {
    padding: 11px 24px 10px;
    font-weight: 400;
    @include themify {
      background-color: themed("$--background-color-base");
    }
  }
  .fileName_style {
    margin-right: 3px;
    vertical-align: middle;
    div {
      display: inline-block;
      max-width: 120px;
      vertical-align: top;
    }
  }
}
</style>
