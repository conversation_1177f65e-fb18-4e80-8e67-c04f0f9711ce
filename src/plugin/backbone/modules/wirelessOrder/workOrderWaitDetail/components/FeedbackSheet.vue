<template>
  <el-card shadow="always" class="card" :body-style="{ padding: '10px 24px' }">
    <div class="header clearfix">
      <div class="header-right" v-show="isShowAudit">
        <el-button type="primary" size="mini" @click="qualitativeReview"
          >定性审核
        </el-button>
      </div>
      <div class="header-tabMenu">
        <span class="header-title" style="margin-top: 5px; margin-right: 10px"
          >反馈单详情</span
        >
        <div style="display: inline-block">
          <el-radio-group v-model="radio1" @change="handleClick" size="mini">
            <el-radio-button
              v-for="(tab, key) in tabMenu"
              :key="key"
              :label="key"
            >
              {{ tab.trim() }}
            </el-radio-button>
          </el-radio-group>
        </div>
      </div>
    </div>
    <div class="content">
      <el-descriptions title="故障定性信息" class="descriptions">
        <template>
          <el-descriptions-item label="故障所属专业">{{
            list.professionalTypeName
          }}</el-descriptions-item>
          <el-descriptions-item label="故障等级">{{
            list.faultLevelName
          }}</el-descriptions-item>
          <el-descriptions-item label="故障发生时间">{{
            list.alarmCreateTime
          }}</el-descriptions-item>
          <el-descriptions-item label="故障通知时间">
            {{ list.sheetCreateTime }}
          </el-descriptions-item>
          <el-descriptions-item label="故障恢复时间">{{
            list.busRecoverTime
          }}</el-descriptions-item>
          <el-descriptions-item label="故障处理净历时">
            {{ processDuration }}
          </el-descriptions-item>

          <el-descriptions-item label="故障处理历时">
            {{ troubleshootingDuration }}
          </el-descriptions-item>
          <el-descriptions-item label="挂起历时">{{
            suspendDuration
          }}</el-descriptions-item>
          <el-descriptions-item label="故障恢复历时">{{
            busRecoverDuration
          }}</el-descriptions-item>
          <el-descriptions-item label="是否超时">
            <span v-if="list.isOverTime == 0">否</span>
            <span v-else-if="list.isOverTime == 1">是</span>
          </el-descriptions-item>
          <el-descriptions-item label="故障处理部门">
            {{ list.dept }}
          </el-descriptions-item>
          <el-descriptions-item label="处理人">{{
            list.person
          }}</el-descriptions-item>
          <el-descriptions-item label="处理人电话">{{
            list.personPhone
          }}</el-descriptions-item>
          <el-descriptions-item label="是否影响业务">
            <span v-if="list.isEffectBusiness == 0">否</span>
            <span v-else-if="list.isEffectBusiness == 1">是</span>
          </el-descriptions-item>
          <el-descriptions-item label="是否铁塔原因">
            <span v-if="list.isTowerFault == 0">否</span>
            <span v-else-if="list.isTowerFault == 1">是</span>
          </el-descriptions-item>
          <el-descriptions-item label="是否基站退服">
            <span v-if="list.isSiteOffline == 0">否</span>
            <span v-else-if="list.isSiteOffline == 1">是</span>
          </el-descriptions-item>
          <el-descriptions-item label="退服原因"
            >{{ list.siteOfflineReason }}
            <!-- v-if="list.isSiteOffline == '是'"
           :span="2" -->
          </el-descriptions-item>
          <el-descriptions-item label="故障处理方式">{{
            list.faultHandleTypeName
          }}</el-descriptions-item>
          <el-descriptions-item label="同行处理人">{{
            list.withinPerson
          }}</el-descriptions-item>
          <el-descriptions-item label="是否需要定性审核">
            <span v-if="list.needCheck == 0">否</span>
            <span v-else-if="list.needCheck == 1">是</span>
          </el-descriptions-item>
        </template>
      </el-descriptions>
      <el-descriptions title="故障专业信息" class="descriptions">
        <el-descriptions-item label="故障分类">
          {{ list.faultCateName }}
        </el-descriptions-item>
        <el-descriptions-item label="故障原因">
          {{ list.faultReasonName }}</el-descriptions-item
        >
        <el-descriptions-item label="附件">
          <el-tag
            v-for="(item, index) in attachmentArr"
            class="fileName_style"
            :key="index"
            @click="downloadAppendixFile(item)"
            v-loading.fullscreen.lock="appendixFileLoading"
            :title="item.name"
          >
            <div class="text-truncate">{{ item.name }}</div>
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="故障原因描述" :span="6">
          {{ list.faultReasonDesc }}
        </el-descriptions-item>
        <br />
        <el-descriptions-item label="备注" :span="6">
          {{ list.faultComment }}
        </el-descriptions-item>
      </el-descriptions>
      <el-descriptions
        title="故障定性审核信息"
        v-if="list.auditResult != null && list.auditResult != ''"
        class="descriptions"
      >
        <el-descriptions-item label="审批结果">
          <span v-if="list.auditResult == 'N'">拒绝</span
          ><span v-else-if="list.auditResult == 'Y'">同意</span>
        </el-descriptions-item>
        <el-descriptions-item label="审批意见">
          {{ list.auditContent }}</el-descriptions-item
        >
      </el-descriptions>
      <!-- <el-descriptions
        title="包机人评价"
        v-if="list.auditResult != null && list.auditResult != ''"
        class="descriptions"
      >
        <el-descriptions-item label="审批结果">
          <span v-if="list.auditResult == 'N'">拒绝</span
          ><span v-else-if="list.auditResult == 'Y'">同意</span>
        </el-descriptions-item>
        <el-descriptions-item label="审批意见">
          {{ list.auditContent }}</el-descriptions-item
        >
      </el-descriptions>
      <el-descriptions
        title="包机人评价"
        v-if="list.auditResult != null && list.auditResult != ''"
        class="descriptions"
      >
        <el-descriptions-item label="审批结果">
          <span v-if="list.auditResult == 0">拒绝</span
          ><span v-else-if="list.auditResult == 1">同意</span>
        </el-descriptions-item>
        <el-descriptions-item label="审批意见">
          {{ list.auditContent }}</el-descriptions-item
        >
      </el-descriptions> -->
      <el-descriptions title="返单评价" class="descriptions">
        <el-descriptions-item label="工单准确性">
          <el-rate
            v-model="list.sendAccuracy"
            :colors="colors"
            style="display: inline-block; vertical-align: text-bottom"
            disabled
          >
          </el-rate>
        </el-descriptions-item>
        <el-descriptions-item label="派单及时性">
          <el-rate
            v-model="list.sendTimely"
            :colors="colors"
            style="display: inline-block; vertical-align: text-bottom"
            disabled
          >
          </el-rate>
        </el-descriptions-item>
        <el-descriptions-item label="中台评价">
          <el-rate
            v-model="list.middleGroundRate"
            :colors="colors"
            style="display: inline-block; vertical-align: text-bottom"
            disabled
          >
          </el-rate>
        </el-descriptions-item>
        <el-descriptions-item label="诊断准确性">
          <el-rate
            v-model="list.diagnoseAccuracy"
            :colors="colors"
            style="display: inline-block; vertical-align: text-bottom"
            disabled
          >
          </el-rate>
        </el-descriptions-item>

        <el-descriptions-item label="反馈问题">
          {{ list.problemClass }}</el-descriptions-item
        >
      </el-descriptions>
    </div>
    <el-dialog
      title="定性审核"
      :visible.sync="dialogQualitativeAuditVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="qualitativeReviewClose"
      :fullscreen="false"
      width="83%"
      top="5vh"
    >
      <qualitative-audit
        ref="qualitativeAudit"
        :common="common"
        :workItemId="workItemId"
        :woId="woId"
        actionName="定性审核"
        @qualitativeReviewSubmit="qualitativeReviewSubmit"
      >
      </qualitative-audit>
    </el-dialog>
    <el-dialog
      width="420px"
      title="文件下载"
      :visible.sync="attachmentVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <file-download
        @cancel="attachmentVisible = false"
        :attachmentArr="attachmentArr"
      ></file-download>
    </el-dialog>
  </el-card>
</template>

<script>
import QualitativeAudit from "./QualitativeAudit.vue";
import FileDownload from "../../../workOrder/components/FileDownload.vue";
import { apiDownloadAppendixFile, apiqueryFeedback } from "../api/CommonApi";

export default {
  name: "FeedbackSheet",
  props: {
    isShowAudit: Boolean, //判断定性审核按钮
    common: Object,
    woId: String,
    qualitativeType: String,
  },
  components: {
    FileDownload,
    QualitativeAudit,
  },
  data() {
    return {
      tabMenu: [],
      list: {},
      listAll: [],
      colors: ["#99A9BF", "#F7BA2A", "#FF9900"],
      btnFlag: false,
      dialogQualitativeAuditVisible: false,
      //故障定性
      dialogQualitativeVisible: false,
      troubleshootingDuration: null, //故障处理历时
      suspendDuration: null, //挂起历时
      processDuration: null, //故障处理净历时
      attachmentArr: [], //附件
      attachmentVisible: false,
      busRecoverDuration: null, //业务恢复历时
      // 多个省的 定性审核区分workItemId
      workItemId: null,
      showDxsh: false,
      showDx: false,
      appendixFileLoading: false,
      isUploadReport: null,
      radio1: 0,
    };
  },
  mounted() {
    console.log(this.isShowAudit);
    if (this.woId) {
      this.getFeedbackData();
    }
  },
  methods: {
    handleClick(index) {
      this.deal(this.listAll[index]);
      // this.setBtnFlag();
    },
    deal(data) {
      this.list = data;
      this.troubleshootingDuration = this.showTime(data.faultDuration);
      this.suspendDuration = this.showTime(data.suspendDuration);
      this.processDuration = this.showTime(data.processDuration);
      this.busRecoverDuration = this.showTime(data.busRecoverDuration);
      if (data.appendix) {
        this.attachmentArr = JSON.parse(data.appendix);
      }
      this.workItemId = data.workItemId;
      this.isUploadReport = data.isUploadReport;
    },
    getFeedbackData() {
      let param = {
        woId: this.woId,
      };
      apiqueryFeedback(param)
        .then(res => {
          if (res.status == 0) {
            this.listAll = res?.data?.rows ?? [];
            if (this.listAll.length > 0) {
              this.list = this.listAll[0];
              this.workItemId = this.list.workItemId;
              this.isUploadReport = this.list.isUploadReport;
              this.troubleshootingDuration = this.showTime(
                this.list.faultDuration
              );
              this.suspendDuration = this.showTime(this.list.suspendDuration);
              this.processDuration = this.showTime(this.list.processDuration);
              this.busRecoverDuration = this.showTime(
                this.list.busRecoverDuration
              );
              if (
                this.common.sheetStatus == "待定性审核" ||
                this.common.sheetStatus == "已归档"
              ) {
                for (let i = 0; i < this.listAll.length; i++) {
                  // let tabName = `（${this.listAll[i].faultRegion}）${this.listAll[i].dept}`;
                  this.tabMenu.push(`${this.listAll[i].dept}`);
                }
              } else {
                for (let i = 0; i < this.listAll.length; i++) {
                  //let tabName = `（${this.listAll[i].faultRegion}）${this.listAll[i].dept}`;
                  this.tabMenu.push(`${this.listAll[i].dept}`);
                }
              }

              if (this.list.appendix) {
                this.attachmentArr = JSON.parse(this.list.appendix);
              }
            }
            // this.setBtnFlag();
            this.$emit("auditResultCallBack", this.list.auditResult);
          }
        })
        .catch(err => {
          console.log(err);
        });
    },
    // setBtnFlag() {
    //   if (this.qualitativeType == "定性审核") {
    //     if (this.isShowAudit == false) {
    //       this.btnFlag = false;
    //     }
    //   }
    // },
    qualitativeReviewClose() {
      this.dialogQualitativeAuditVisible = false;
      this.$refs.qualitativeAudit.onReset();
    },
    //定性审核提交
    qualitativeReviewSubmit(data) {
      this.$emit("qualitativeReviewSubmit", data);
      this.dialogQualitativeAuditVisible = false;
    },
    qualitativeReview() {
      this.showDxsh = false;
      this.$nextTick(() => {
        this.showDxsh = true;
      });
      this.dialogQualitativeAuditVisible = true;
    },
    queryAttachmentList() {
      this.attachmentVisible = true;
    },
    closeAttachmentDialog() {
      this.attachmentVisible = false;
    },
    downloadAppendixFile(data) {
      this.appendixFileLoading = true;
      let param = {
        attId: data.id,
      };
      apiDownloadAppendixFile(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("文件下载成功");
          } else {
            this.$message.error("文件下载失败");
          }
          this.appendixFileLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("文件下载失败");
          this.appendixFileLoading = false;
        });
    },
    showTime(val) {
      if (val == 0 || val == "" || null == val) {
        return "0秒";
      }
      var time = "";
      var second = val % 60;
      var minute = parseInt(parseInt(val) / 60) % 60;
      time = second + "秒";
      if (minute >= 1) {
        time = minute + "分" + second + "秒";
      }
      var hour = parseInt(parseInt(val / 60) / 60) % 24;
      if (hour >= 1) {
        time = hour + "小时" + minute + "分" + second + "秒";
      }
      var day = parseInt(parseInt(parseInt(val / 60) / 60) / 24);
      if (day >= 1) {
        time = day + "天" + hour + "小时" + minute + "分" + second + "秒";
      }
      return time;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../../../workOrder/workOrderWaitDetail/assets/common.scss";

.header-tabMenu {
  margin-top: 10px;
}
</style>
