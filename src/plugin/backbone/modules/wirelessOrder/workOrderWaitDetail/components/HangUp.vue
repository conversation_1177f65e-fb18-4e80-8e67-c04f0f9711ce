<template>
  <div class="hang-up">
    <el-form ref="hangUpForm" :model="hangUpForm" :rules="hangUpFormRules" label-width="120px">
      <!-- 挂起 -->
      <template v-if="opType == 1">
        <el-form-item :label="personName" prop="hangUserCh">
          <div style="width: 250px">{{ hangUpForm.hangUserCh }}</div>
        </el-form-item>
        <el-form-item :label="dynamicTime" prop="hangTime">
          <div style="width: 250px">
            {{ hangUpForm.hangTime }}
          </div></el-form-item
        >
        <el-form-item
          label="挂起结束时间:"
          prop="hangLimitTime"
          :rules="{
            required: true,
            message: '请选择挂起结束时间',
          }"
        >
          <el-date-picker
            v-model="hangUpForm.hangLimitTime"
            type="datetime"
            placeholder="请选择时间"
            value-format="yyyy-MM-dd HH:mm:ss"
            clearable
            style="width: 90%"
            :picker-options="pickerOptions"
          />
        </el-form-item>
        <el-form-item
          label="挂起原因:"
          prop="hangReason"
        >
          <el-input
            type="textarea"
            :rows="2"
            placeholder="请填写挂起原因"
            v-model="hangUpForm.hangReason"
            style="width: 90%"
            show-word-limit
            maxlength="1000"
          >
          </el-input>
        </el-form-item>
      </template>
      <template v-if="opType == 2">
        <p>是否确定解挂？</p>
      </template>
    </el-form>

    <div slot="footer" style="padding: 10px 20px; text-align: right">
      <el-button
        type="primary"
        @click="handleHangUpSubmit('hangUpForm')"
        v-loading.fullscreen.lock="hangUpFullScreenLoading"
        >提 交</el-button
      >
      <el-button @click="handleCancel" v-if="opType == 2">取 消</el-button>
      <el-button @click="onResetHangUp" v-if="opType == 1">重 置</el-button>
    </div>
  </div>
</template>
<script>
import moment from "moment";
import { mapGetters } from "vuex";
import { apiHangUpWireless, apiUnHangWireless } from "../api/CommonApi";
import {mixin} from "../../../../../../mixins"
export default {
  name: "HangUp",
  props: {
    common: Object,
    opType: Number,
    actionName: String,
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  mixins: [mixin],
  data() {
    return {
      personName: "",
      dynamicTime: "",
      //挂起
      hangUpForm: {
        // hangUpPerson: "",
        // hangUpTime: "",
        hangLimitTime: "",
        hangUserCh: "",
        hangTime: "",
        unHangTime: "",
      },
      hangUpFormRules: {
        hangReason: [
          {
            required: true,
            message: '请填写挂起原因',
          },
          {
            validator: this.checkLength,
            max: 1000,
            form: "hangUpForm",
            messsage: "已超过填写字数上限",
          },
        ],
      },
      pickerOptions: {
        disabledDate: this.disabledDate,
        selectableRange: this.timeRange,
      },
      hangUpFileDialogVisible: false,
      hangUpFullScreenLoading: false,
    };
  },
  created() {},
  mounted() {
    this.hangUpForm.hangUserCh = this.userInfo.realName;
    this.hangUpForm.hangTime = moment().format("YYYY-MM-DD HH:mm:ss");
    this.hangUpForm.unHangTime = moment().format("YYYY-MM-DD HH:mm:ss");
    if (this.opType == 1) {
      this.personName = "挂起人:";
      this.dynamicTime = "挂起时间:";
    } else if (this.opType == 2) {
      this.personName = "解挂人:";
      this.dynamicTime = "解挂时间:";
    }
    console.log(this.common.professionalTypeName);
  },
  watch: {
    "hangUpForm.hangLimitTime": {
      handler(val) {
        if (val == null) return;
        //所选日期
        let valDate = val.split(" ")[0];
        valDate = valDate.replace(/\-/g, "/");

        // 挂起时间
        let originDate = this.hangUpForm.hangTime.split(" ")[0];
        originDate = originDate.replace(/\-/g, "/");

        //挂起结束时间
        let now = new Date();
        now.setMonth(now.getMonth() + 1);
        now.setDate(now.getDate() + 7);
        now.setMinutes(now.getMinutes() + 1);

        let year = now.getFullYear();
        let month = now.getMonth();
        let date = now.getDate();
        let hour = now.getHours();
        let minute = now.getMinutes();
        let second = now.getSeconds();
        let nowDate =
          year + "/" + this.addZero(month) + "/" + this.addZero(date);

        let valDateUnix = Date.parse(valDate);
        let originDateUnix = Date.parse(originDate);
        let nowDateUnix = Date.parse(nowDate);

        let array = this.hangUpForm.hangTime.split(" ");
        let createTime = array[1];

        let nowTime =
          this.addZero(hour) +
          ":" +
          this.addZero(minute) +
          ":" +
          this.addZero(second);

        // 所选日期等于挂起日期
        if (valDateUnix == originDateUnix) {
          this.timeRange = createTime + " - 23:59:59";
          this.pickerOptions.selectableRange = this.timeRange;
        } else if (valDateUnix > originDateUnix && valDateUnix < nowDateUnix) {
          // 所选日期大于挂起日期小于挂起结束日期
          this.timeRange = "00:00:00 - 23:59:59";
          this.pickerOptions.selectableRange = this.timeRange;
        } else if (valDateUnix == nowDateUnix) {
          //所选日期等于挂起结束日期
          this.timeRange = "00:00:00 - " + nowTime;
          this.pickerOptions.selectableRange = this.timeRange;
        }
        console.log(this.timeRange);
      },
      deep: true,
    },
  },
  methods: {
    //时间对比
    disabledDate(time) {
      let nowDate = new Date();
      nowDate.setDate(nowDate.getDate() + 7);
      let a = moment(nowDate).subtract(8, "days");
      let today = moment(a).valueOf();
      return time.getTime() <= today || time.getTime() - 8.64e6 >= nowDate;
    },
    addZero(s) {
      return s < 10 ? "0" + s : s;
    },
    handleHangUpSubmit(formName) {
      this.$refs[formName].validate((valid,a) => {
        if (this.uncheck(a)) {
          this.hangUpFullScreenLoading = true;
          let params = {
            woId: this.common.woId,
            processInstId: this.common.processInstId,
            workItemId: this.common.workItemId,
            processNode: this.common.processNode,
            actionName: this.actionName,
            hangUserCh: this.userInfo.realName,
            hangUserEn: this.userInfo.userName,
          };
          if (this.opType == 1) {
            params.hangTime = this.hangUpForm.hangTime;
            params.hangLimitTime = this.hangUpForm.hangLimitTime;
            params.hangReason = this.hangUpForm.hangReason;
            this.hangUp(params);
          } else {
            params.unHangTime = this.hangUpForm.unHangTime;
            params.unHangType = "手动解挂";
            this.unHang(params);
          }
        }
      });
    },
    hangUp(params) {
      apiHangUpWireless(params)
        .then(res => {
          if (res.status == "0") {
            if (this.opType == 1) {
              this.$message.success("挂起申请成功");
            } else if (this.opType == 2) {
              this.$message.success("解挂申请成功");
            }
            this.onResetHangUp();
            this.$emit("closeDialogHangUp");
          } else {
            if (this.opType == 1) {
              this.$message.error(res.msg);
            } else if (this.opType == 2) {
              this.$message.error(res.msg);
            }
          }
        })
        .catch(error => {
          console.log(error);
          this.$message.error(error.msg);
        })
        .finally(() => {
          this.hangUpFullScreenLoading = false;
        });
    },
    unHang(params) {
      apiUnHangWireless(params)
        .then(res => {
          if (res.status == "0") {
            if (this.opType == 1) {
              this.$message.success("挂起申请成功");
            } else if (this.opType == 2) {
              this.$message.success("解挂申请成功");
            }
            this.onResetHangUp();
            this.$emit("closeDialogHangUp");
          } else {
            if (this.opType == 1) {
              this.$message.error(res.msg);
            } else if (this.opType == 2) {
              this.$message.error(res.msg);
            }
          }
        })
        .catch(error => {
          console.log(error);
          this.$message.error(error.msg);
        })
        .finally(() => {
          this.hangUpFullScreenLoading = false;
        });
    },
    onResetHangUp() {
      this.hangUpForm = {
        ...this.$options.data,
        hangUserCh: this.userInfo.realName,
        hangTime: moment().format("YYYY-MM-DD HH:mm:ss"),
      };
    },
    handleCancel() {
      this.$emit("closeDialogHangUp");
    },
  },
};
</script>
<style lang="scss" scoped>
.hang-up {
  .fileName_style {
    margin-right: 3px;
    vertical-align: middle;
    div {
      display: inline-block;
      max-width: 320px;
      vertical-align: top;
    }
  }
}
</style>
