<template>
  <el-card shadow="always" class="card" :body-style="{ padding: '10px 24px' }">
    <div class="header clearfix">
      <span class="header-title">处理详情</span>
    </div>
    <div class="content">
      <el-collapse>
        <el-collapse-item v-if="determinApproveInfo">
          <span class="collapse-title" slot="title">定性审核</span>
          <div
            v-for="(defineCheckInfo, key) of detailsList['defineCheck']
              .determinApproveInfo"
            :key="key"
            class="content__list"
          >
            <p class="detail-p" v-html="defineCheckInfo"></p>
          </div>
        </el-collapse-item>
        <el-collapse-item v-if="goback">
          <span class="collapse-title" slot="title">定性</span>
          <div v-if="determinInfo">
            <div
              v-for="(deter, key) of detailsList['feedbackAndDefine']
                .determinInfo"
              :key="key"
              class="content__list"
            >
              <p class="detail-p" v-html="deter"></p>
            </div>
          </div>
        </el-collapse-item>
        <el-collapse-item v-if="faultRemoving">
          <span class="collapse-title" slot="title">消障确认</span>
          <div
            v-for="(remove, key) of detailsList['faultRemoving']
              .faultRemovingInfo"
            :key="key"
            class="content__list"
          >
            <p class="detail-p" v-html="remove"></p>
          </div>
        </el-collapse-item>
        <el-collapse-item v-if="dealComplete">
          <span class="collapse-title" slot="title">处理完成</span>
          <div
            v-for="(deal, key) of detailsList['dealComplete'].dealCompleteInfo"
            :key="key"
            class="content__list"
          >
            <p class="detail-p" v-html="deal"></p>
          </div>
        </el-collapse-item>
        <el-collapse-item v-if="turnToSend">
          <span class="collapse-title" slot="title">转派</span>
          <div v-if="reassignOrderInfo">
            <div
              v-for="(reassign, key) of detailsList['reassign']
                .reassignOrderInfo"
              :key="key"
              class="content__list"
            >
              <p class="detail-p" v-html="reassign"></p>
            </div>
          </div>
        </el-collapse-item>
        <el-collapse-item v-if="hang">
          <span class="collapse-title" slot="title">挂起/解挂</span>
          <div v-if="pendingApplicationInfo">
            <div
              v-for="(lication, key) of detailsList['pending']
                .pendingApplicationInfo"
              :key="key"
              class="content__list"
            >
              <p class="detail-p" v-html="lication.str"></p>
            </div>
          </div>
        </el-collapse-item>
        <el-collapse-item v-if="stageFeedbackInfo">
          <span class="collapse-title" slot="title">阶段反馈</span>

          <div
            v-for="(stage, key) of detailsList['stageFeedback']
              .stageFeedbackInfo"
            :key="key"
            class="content__list"
          >
            <p class="detail-p" v-html="stage"></p>
          </div>
        </el-collapse-item>
        <el-collapse-item v-if="dot">
          <span class="collapse-title" slot="title">现场打点</span>
          <div
            v-for="(dot, key) of detailsList['dot'].dotInfo"
            :key="key"
            class="content__list"
          >
            <p class="detail-p" v-html="dot"></p>
            <i class="el-icon-place" @click="showMap"></i>
          </div>
        </el-collapse-item>
        <el-collapse-item v-if="accept">
          <span class="collapse-title" slot="title">受理情况</span>
          <div v-if="agentAcceptInfo">
            <div
              v-for="(acceptInfo, key) of detailsList['accept'].agentAcceptInfo"
              :key="key"
              class="content__list"
            >
              <p class="detail-p" v-html="acceptInfo"></p>
            </div>
          </div>
        </el-collapse-item>
        <el-collapse-item v-if="pendingInfo">
          <span class="collapse-title" slot="title">工单派发</span>

          <div
            v-for="(pend, key) of detailsList['waitingAccept'].pendingInfo"
            :key="key"
            class="content__list"
          >
            <p class="detail-p" v-html="pend"></p>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
    <el-dialog
      title="现场打点"
      :visible.sync="mapVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="1000px"
    >
      <map-location :woId="woId" :random="random"></map-location>
    </el-dialog>
  </el-card>
</template>

<script>
import { apiGetProcessInfo } from "../api/CommonApi";
import MapLocation from "./MapLocation.vue";

export default {
  name: "DealDetails",
  props: {
    woId: String,
  },
  components: { MapLocation },
  data() {
    return {
      detailsList: {
        accept: {
          agentAcceptInfo: [],
        },
        waitingAccept: {
          pendingInfo: [],
        },
        stageFeedback: {
          stageFeedbackInfo: [],
        },
        pending: {
          pendingApplicationInfo: [],
        },
        feedbackAndDefine: {
          determinInfo: [],
        },
        defineCheck: {
          determinApproveInfo: [],
        },
        reassign: {
          reassignOrderInfo: [],
        },
        dot: {
          dotInfo: [],
        },
        faultRemoving: {
          faultRemovingInfo: [],
        },
        dealComplete: {
          dealCompleteInfo: [],
        },
      },
      appendixFileLoading: false,
      mapVisible: false,
      random: 0,
    };
  },
  mounted() {
    this.getProcessInfo();
  },
  computed: {
    accept() {
      if (this.detailsList["accept"].agentAcceptInfo.length > 0) {
        return true;
      } else {
        return false;
      }
    },
    dot() {
      if (this.detailsList["dot"].dotInfo.length > 0) {
        return true;
      } else {
        return false;
      }
    },
    faultRemoving() {
      if (this.detailsList["faultRemoving"].faultRemovingInfo.length > 0) {
        return true;
      } else {
        return false;
      }
    },
    dealComplete() {
      if (this.detailsList["dealComplete"].dealCompleteInfo.length > 0) {
        return true;
      } else {
        return false;
      }
    },
    agentAcceptInfo() {
      if (this.detailsList["accept"].agentAcceptInfo.length > 0) {
        return true;
      } else {
        return false;
      }
    },
    pendingInfo() {
      if (this.detailsList["waitingAccept"].pendingInfo.length > 0) {
        return true;
      } else {
        return false;
      }
    },
    stageFeedbackInfo() {
      if (this.detailsList["stageFeedback"].stageFeedbackInfo.length > 0) {
        return true;
      } else {
        return false;
      }
    },
    hang() {
      if (this.detailsList["pending"].pendingApplicationInfo.length > 0) {
        return true;
      } else {
        return false;
      }
    },
    pendingApplicationInfo() {
      if (this.detailsList["pending"].pendingApplicationInfo.length > 0) {
        return true;
      } else {
        return false;
      }
    },
    goback() {
      if (this.detailsList["feedbackAndDefine"].determinInfo.length > 0) {
        return true;
      } else {
        return false;
      }
    },
    determinInfo() {
      if (this.detailsList["feedbackAndDefine"].determinInfo.length > 0) {
        return true;
      } else {
        return false;
      }
    },
    determinApproveInfo() {
      if (this.detailsList["defineCheck"].determinApproveInfo.length > 0) {
        return true;
      } else {
        return false;
      }
    },
    turnToSend() {
      if (this.detailsList["reassign"].reassignOrderInfo.length > 0) {
        return true;
      } else {
        return false;
      }
    },
    reassignOrderInfo() {
      if (this.detailsList["reassign"].reassignOrderInfo.length > 0) {
        return true;
      } else {
        return false;
      }
    },
  },

  methods: {
    getProcessInfo() {
      let param = {
        woId: this.woId,
      };
      apiGetProcessInfo(param)
        .then(res => {
          if (res.status == 0) {
            this.detailsList = res?.data || {};
          }
        })
        .catch(err => {
          console.log(err);
        });
    },
    showMap() {
      this.random = Math.random();
      this.mapVisible = true;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../assets/common.scss";
::v-deep .el-collapse-item {
  .el-collapse-item__header {
    background-color: #fafafa;
    padding-left: 20px;
  }
}
::v-deep .content__list {
  padding-top: 10px;
  padding-left: 25px;
}
::v-deep .detail-p {
  display: contents;
  padding: 0px 5px;
  margin: 0;
}

.el-icon-place {
  font-size: 22px;
  color: #b50b14;
  cursor: pointer;
}
</style>
