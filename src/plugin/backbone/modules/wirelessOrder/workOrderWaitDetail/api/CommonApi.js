import {
  getJson,
  postJson,
  getJsonBlob,
  postFormData,
  delJson,
  postJsonBlob,
} from "@/utils/axios";

const areaUrl = "commonDict/info/faultArea"; //公共下拉接口地址(地市,区县区域)
const apiDictUrl = "commonDict/enum/list"; //公用枚举
const showButtonUrl = "wireless/operation/getBtnList"; //详情页面中的按钮展示
const feedbackUrl = "/wireless/workflow/queryFeedback"; //工单详情-定性详情
const drafOrderUrl = "/wireless/operation/getOrderOne"; // 草稿回显
const processUrl = "/wireless/workflow/getProcessInfo"; //工单详情，处理详情
const workOrderInfoUrl = "/wireless/workflow/queryByWoId"; //详情页面中的工单基本信息
const exportWorkOrderUrl = "/wireless/workflow/queryByWoId/excel"; //工单基本信息导出
const alarmDetailUrl = "/wireless/workflow/queryAlarm"; //告警详情表格
//const relationDiagnosisUrl = "/wireless/workflow/queryRelation"; //关联诊断
const relationDiagnosisUrl = "backbone/workflow/queryRelation"; //关联诊断

const logUrl = "/wireless/workflow/getProcessLog"; // 工单详情- 流程日志
const acceptUrl = "/wireless/operation/accept"; //受理
const processCompleteUrl = "wireless/operation/processComplete"; //处理完成
const transferSubmitUrl = "wireless/operation/transfer"; // 转派
const actionUrlPublic = "wireless/operation/action"; // 通用操作   (无线 消障确认 待确认接口)
const stageFdbkUrl = "wireless/operation/stageFdbk"; // 阶段反馈
const getMessageNumUrl = "chatOps/chatUrl/chatOps/v1/groups/unreadMessage"; // chatops 未读条数
const rsaEncryptUrl = "wireless/operation/getRsaEncrypt"; //外连接参数请求
const messageRsaEncryptUrl = "wireless/operation/getRsaNoReadParams"; // 获取当前用户群聊未读消息数的params加密参数

const groupCodeUrl = "province/operation/getId"; //获取上传附件id
const getIdUrl = "province/operation/getId"; // 发起工单——初始化工单号 woid
const getWoidUrl = "province/operation/getOrderByCode"; // 外网调用页面时，通过工单号，查询woid
const postLocation = "province/operation/action"; // PC现场打点

const syncClearUrl = "backbone/workflow/clearQuery"; //同步清除
const backSingleUrl = "backbone/workflow/ackDefine/feedback"; //返单
const qualitativeDetailUrl = "wireless/operation/ackDefineDetail"; //返查定性详情(返单)
const qualitativeUrl = "backbone/workflow/ackDefine/define"; //定性提交
const stageFeedbackUrl = "backbone/workflow/op/stageFeedback"; //阶段反馈
const revokeUrl = "backbone/workflow/removeProcessInst"; //撤单
const hangUpUrl = "backbone/workflow/op/applyInterrupt"; //挂起/解挂 申请
const auditUrl = "province/operation/action"; //挂起申请审核
const abendUrl = "backbone/workflow/op/applyAbnormalEnd"; //异常终止申请
const abendAuditUrl = "backbone/workflow/op/checkAbnormalEnd"; //异常终止审核
const afterSingleUrl = "backbone/workflow/op/addWoInfo"; //追单
const haveReadUrl = "wireless/operation/setReadStatus"; //已阅
const circulatedUrl = "backbone/workflow/read/addReadInfo"; //传阅
const circulatedUserTreeUrl = "backbone/tree/searchRedeployUser"; //传阅用户树数据
const circulatedOrgTreeUrl = "backbone/tree/searchRedeployOrg"; //传阅组织树数据
const faultAreaUrl = "backbone/info/faultArea"; //故障发生地区字典数据
const queryAttachmentListUrl = "backbone/info/attachmentList"; //附件组编码查询附件列表
const downloadAppendixUrl = "commonDict/attach/download"; //公共下载附件接口
const deleteFdFileUrl = "backbone/workflow/ackDefine/deleteAttach"; //删除附件接口
const manualFileUrl = "backbone/workflow/queryCircuitAttachment/excel";
const fluenceExcelUrl = "backbone/workflow/queryCutInfluenceExcel/excel"; //光缆excel
const provinceDictUrl = "backbone/info/provinceDict";
const backSingleInternationUrl = "backbone/workflow/intAckDefine/intFeedback"; //国际局返单
const qualitativeInternationDetailUrl =
  "backbone/workflow/intAckDefine/intDetail"; //国际局定性详情
const qualitativeInternationUrl = "backbone/workflow/intAckDefine/intDefine"; //国际局定性
const qualitativeReviewInternationUrl =
  "backbone/workflow/intAckDefine/intDefineCheck"; //国际局定性审核
const itCloudAcceptUrl = "backbone/workflow/it/cloud/accept"; //IT云受理
const oneKeyIvrNoticeUrl = "backbone/workflow/oneKeyIvr/oneKeyIvrNotice"; //一键IVR
const itCloudHangUpUrl = "backbone/workflow/it/cloud/applyInterrupt"; //IT云挂起/解挂 申请
const itCloudAuditUrl = "backbone/workflow/it/cloud/checkInterrupt"; //IT云挂起/解挂 审核
const itCloudAbendUrl = "backbone/workflow/it/cloud/applyInterrupt"; //IT云异常终止
const itCloudAbendAuditUrl = "backbone/workflow/it/cloud/checkInterrupt"; //IT云异常终止 审核
const setReadUrl = "province/operation/setReadStatus"; // 待阅列表，打开详情页，设置待阅状态为已阅
const clearQueryUrl = "wireless/workflow/clearQuery"; // 无线网告警清除
const initQualitative = "province/operation/getFdbkInit"; // 初始化故障定性

const fileDownloadUrl = "province/attach/download"; //附件下载

const deleteDraftUrl = "backbone/workflow/op/delDraft";
const orgInfoUrl = "backbone/info/orgInfo"; //当前用户的信息地址
const getEvaluationUrl = "/backbone/workflow/getEvaluation"; //是否已评价
const wirelessQualitativeUrl = "/wireless/operation/qualitative"; //通信云返单
const qualitativeReviewUrl = "/wireless/operation/qualitativeReview"; //无线 定性审核
const hangUpWirelessUrl = "/wireless/operation/hangAppply";
const hangAuditUrl = "/wireless/operation/hangAudit";
const unHangUrl = "/wireless/operation/unHang";
const gisInfoUrl = "/wireless/operation/getGisInfo"; //现场打点经纬度-资源经纬度
const analyzeRelationDiagnosisUrl = "commonDict/analyze/getCotents"; //关联诊断（分析上海AI）
const topoUrl = "/netfm3topo/topoapi/topoinfo"; //topo图是否呈现接口

const apiStageFdbk = params => postJson(stageFdbkUrl, params); //阶段反馈
const apiProcessComplete = params => postJson(processCompleteUrl, params); // 处理完成接口
const apiArea = params => getJson(areaUrl, params);
const apiDict = params => getJson(apiDictUrl, params);
const apiGetShowButton = params =>
  postJson(showButtonUrl, params, { timeout: 0 });
const apiTransfer = params => postJson(transferSubmitUrl, params); //转派
const apiActionPublic = params => postJson(actionUrlPublic, params); // 通用操作接口
const apiGroupCode = params => getJson(groupCodeUrl, params); // 通用操作接口
const apiGetId = params => getJson(getIdUrl, params);
const apiGetWoid = params => postJson(getWoidUrl, params);
const apiDeleteDraft = params => postJson(deleteDraftUrl, params);

const apiGetWorkOrderInfo = params =>
  getJson(workOrderInfoUrl, params, { timeout: 0 });
const apiExportWorkOrder = params => getJsonBlob(exportWorkOrderUrl, params);
const apiAccept = params => postJson(acceptUrl, params);
const apiQueryAlarmDetail = params => postJson(alarmDetailUrl, params);
const apiSyncClear = params => postJson(syncClearUrl, params);
const apiGetRelationDiagnosis = params => getJson(relationDiagnosisUrl, params);
const apiBackSingle = params => postFormData(backSingleUrl, params);
const apiQualitativeDetail = params => getJson(qualitativeDetailUrl, params);
const apiQualitative = params => postFormData(qualitativeUrl, params);
const apiStageFeedBack = params => getJson(stageFeedbackUrl, params);
const apiRevoke = params => postJson(revokeUrl, params);
const apiHangUp = params => postJson(hangUpUrl, params);
const apiAudit = params => postJson(auditUrl, params);
const apiAbend = params => postJson(abendUrl, params);
const apiAbendAudit = params => postJson(abendAuditUrl, params);
const apiAfterSingle = params => postFormData(afterSingleUrl, params);
const apiHaveRead = params => postJson(haveReadUrl, params);
const apiCirculated = params => postJson(circulatedUrl, params);
const apiCirculatedUserTree = params => getJson(circulatedUserTreeUrl, params);
const apiCirculatedOrgTree = params => getJson(circulatedOrgTreeUrl, params);
const apiGetFaultArea = params => getJson(faultAreaUrl, params);
const apiDownloadAppendixFile = params =>
  getJsonBlob(downloadAppendixUrl, params);
const apiQueryAttachment = params => getJson(queryAttachmentListUrl, params);
const apiDeleteFdFile = params => delJson(deleteFdFileUrl, params);
const apiDownloadManualFile = params => postJsonBlob(manualFileUrl, params);
const apiGetProvinceDict = params => getJson(provinceDictUrl, params);
const apiBackSingleInternation = params =>
  postJson(backSingleInternationUrl, params);
const apiQualitativeInternationDetail = params =>
  getJson(qualitativeInternationDetailUrl, params);
const apiQualitativeInternation = params =>
  postJson(qualitativeInternationUrl, params);
const apiQualitativeInternationReview = params =>
  postJson(qualitativeReviewInternationUrl, params);
const apiItCloudAccept = params => postJson(itCloudAcceptUrl, params);
const apioneKeyIvrNotice = params => postJson(oneKeyIvrNoticeUrl, params);
const apiItCloudHangUp = params => postJson(itCloudHangUpUrl, params);
const apiItCloudAudit = params => postJson(itCloudAuditUrl, params);
const apiItCloudAbend = params => postJson(itCloudAbendUrl, params);
const apiItCloudAbendAudit = params => postJson(itCloudAbendAuditUrl, params);
const apifluenceExcel = params => postJsonBlob(fluenceExcelUrl, params);
const apiGetProcessInfo = params => getJson(processUrl, params);
const apiGetProcessLog = params => getJson(logUrl, params);
const apiInitOrderDraf = params => postJson(drafOrderUrl, params);
const apiSetRead = params => postJson(setReadUrl, params);
const apiclearQuery = params => postJson(clearQueryUrl, params);
const apiqueryFeedback = params => getJson(feedbackUrl, params);
const apiInitQualitative = params => postJson(initQualitative, params);
const apiGetRsaEncryptWifi = params => postJson(rsaEncryptUrl, params);
const apiMessageRsaEncrypt = params => postJson(messageRsaEncryptUrl, params);

const apiGetMeassgeNum = params => postJson(getMessageNumUrl, params);
const apiFileDownload = params => getJsonBlob(fileDownloadUrl, params);
const apiFileUpload = (url, params) => postFormData(url, params);
const apiLocation = params => postJson(postLocation, params);
const apiGetOrgInfo = params => getJson(orgInfoUrl, params);
const apiGetEvaluation = params => getJson(getEvaluationUrl, params);
const apiWirelessQualitative = params =>
  postJson(wirelessQualitativeUrl, params);
const apiQualitativeReview = params => postJson(qualitativeReviewUrl, params);
const apiHangUpWireless = params => postJson(hangUpWirelessUrl, params);
const apiHangAuditWireless = params => postJson(hangAuditUrl, params);
const apiUnHangWireless = params => postJson(unHangUrl, params);
const apiGetGis = params => getJson(gisInfoUrl, params);
const apiAnalyzeRelationDiagnosis = params =>
  getJson(analyzeRelationDiagnosisUrl, params);
const apiTopoIsShow = params => getJson(topoUrl, params);

// 时间戳 ，转化成 日期时间格式。如果是当前时间，传参 Date.now()
function getCurrentTime(timeVal) {
  const time = new Date(timeVal);
  const yy = time.getFullYear();
  const mm =
    time.getMonth() + 1 < 10
      ? "0" + (time.getMonth() + 1)
      : time.getMonth() + 1;
  const dd = time.getDate() < 10 ? "0" + time.getDate() : time.getDate();
  const hh = time.getHours() < 10 ? "0" + time.getHours() : time.getHours();
  const mf =
    time.getMinutes() < 10 ? "0" + time.getMinutes() : time.getMinutes();
  const ss =
    time.getSeconds() < 10 ? "0" + time.getSeconds() : time.getSeconds();
  const gettime = yy + "-" + mm + "-" + dd + " " + hh + ":" + mf + ":" + ss;
  return gettime;
}

export {
  apiStageFdbk,
  apiProcessComplete,
  apiDeleteDraft,
  apiArea,
  apiGetShowButton,
  apiGetWorkOrderInfo,
  apiExportWorkOrder,
  apiAccept,
  apiQueryAlarmDetail,
  apiSyncClear,
  apiGetRelationDiagnosis,
  apiBackSingle,
  apiQualitativeDetail,
  apiQualitative,
  apiQualitativeReview,
  apiStageFeedBack,
  apiRevoke,
  apiHangUp,
  apiAudit,
  apiAbend,
  apiAbendAudit,
  apiAfterSingle,
  apiHaveRead,
  apiCirculated,
  apiCirculatedUserTree,
  apiCirculatedOrgTree,
  apiGetFaultArea,
  apiDownloadAppendixFile,
  apiQueryAttachment,
  apiDeleteFdFile,
  apiDownloadManualFile,
  apiGetProvinceDict,
  apiBackSingleInternation,
  apiQualitativeInternationDetail,
  apiQualitativeInternation,
  apiQualitativeInternationReview,
  apiItCloudAccept,
  apioneKeyIvrNotice,
  apiItCloudHangUp,
  apiItCloudAudit,
  apiItCloudAbend,
  apiItCloudAbendAudit,
  apifluenceExcel,
  apiTransfer,
  apiActionPublic,
  getCurrentTime,
  apiGroupCode,
  apiGetProcessInfo,
  apiGetProcessLog,
  apiInitOrderDraf,
  apiSetRead,
  apiclearQuery,
  apiqueryFeedback,
  apiInitQualitative,
  apiGetRsaEncryptWifi,
  apiFileDownload,
  apiFileUpload,
  apiDict,
  apiGetId,
  apiGetMeassgeNum,
  apiMessageRsaEncrypt,
  apiGetWoid,
  apiGetGis,
  apiLocation,
  apiGetOrgInfo,
  apiGetEvaluation,
  apiWirelessQualitative,
  apiHangUpWireless,
  apiHangAuditWireless,
  apiUnHangWireless,
  apiAnalyzeRelationDiagnosis,
  apiTopoIsShow,
};
