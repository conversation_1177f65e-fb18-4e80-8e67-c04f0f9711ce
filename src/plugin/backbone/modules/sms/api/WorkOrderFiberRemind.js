import { postJson, postFormData } from "@/utils/axios";
const queryRuleUrl = "/backbone/workflow/faultOptNoticeRule/getOptRules"; //查询
const addRuleUrl = "/backbone/workflow/faultOptNoticeRule/addOptRule"; //添加
const deleteRUle = "/backbone/workflow/faultOptNoticeRule/delOptRule"; //删除
const queryRuleByRuleIdUrl =
  "/backbone/workflow/faultOptNoticeRule/queryByRuleId"; //编辑
const updateRuleUrl = "/backbone/workflow/faultOptNoticeRule/updateOptRule"; //编辑保存 updateRuleStatus
const updateRuleStatusUrl =
  "/backbone/workflow/faultOptNoticeRule/updateRuleStatus"; //启用 ,禁用

const apiFiberQueryRule = params => postJson(queryRuleUrl, params);
const apiFiberAddRule = params => postJson(addRuleUrl, params);
const apiFiberDeleteRule = params => postJson(deleteRUle, params);
const apiFiberQueryRuleByRuleId = params =>
  postJson(queryRuleByRuleIdUrl, params);
const apiFiberUpdateRule = params => postJson(updateRuleUrl, params);
const apiFiberUpdateRuleStatus = params =>
  postJson(updateRuleStatusUrl, params);

const apiFiberRuleFile = params =>
  postFormData("/backbone/workflow/faultOptNoticeRule/addOptFileRule", params);

export {
  apiFiberQueryRule,
  apiFiberAddRule,
  apiFiberDeleteRule,
  apiFiberQueryRuleByRuleId,
  apiFiberUpdateRule,
  apiFiberUpdateRuleStatus,
  apiFiberRuleFile,
};
