import { postJson } from "@/utils/axios.js";

export const getMsgSwitchListApi = params =>
  postJson(
    "/backbone/workflow/faultNoticeSwitchRule/queryNoticeSwitchRule",
    params
  );

export const addMsgRulesApi = params =>
  postJson(
    "/backbone/workflow/faultNoticeSwitchRule/saveNoticeSwitchRule",
    params
  );

export const mulDelApi = params =>
  postJson(
    "/backbone/workflow/faultNoticeSwitchRule/delNoticeSwitchRule",
    params
  );

export const mulEditApi = params =>
  postJson(
    "/backbone/workflow/faultNoticeSwitchRule/updateNoticeSwitchRule",
    params,
    { timeout: 0 }
  );
