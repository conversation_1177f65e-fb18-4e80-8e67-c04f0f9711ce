import { postJson, getJson, getBlob } from "@/utils/axios";
const queryRuleUrl = "/backbone/workflow/messageRule/queryRule"; //查询
const addRuleUrl = "/backbone/workflow/messageRule/addRule"; //添加
const deleteRUle = "/backbone/workflow/messageRule/deleteRules"; //删除
const queryRuleByRuleIdUrl = "/backbone/workflow/messageRule/queryRuleByRuleId"; //编辑
const updateRuleUrl = "/backbone/workflow/messageRule/updateRule"; //编辑保存 updateRuleStatus
const updateRuleStatusUrl = "/backbone/workflow/messageRule/updateRuleStatus"; //启用 ,禁用

const apiQueryRule = params => postJson(queryRuleUrl, params);
const apiAddRule = params => postJson(addRuleUrl, params);
const apiDeleteRule = params => postJson(deleteRUle, params);
const apiQueryRuleByRuleId = params => getJson(queryRuleByRuleIdUrl, params);
const apiupdateRule = params => postJson(updateRuleUrl, params);
const apiupdateRuleStatus = params => postJson(updateRuleStatusUrl, params);
// 客户业务所属传输系统模板
const apiCusTransTempDown = () =>
  getBlob("/backbone/workflow/faultOptNoticeRule/downloadOpt");
const apiDownloadAppendixFile = params =>
  getBlob("/commonDict/attach/download", params);
export {
  apiQueryRule,
  apiAddRule,
  apiDeleteRule,
  apiQueryRuleByRuleId,
  apiupdateRule,
  apiupdateRuleStatus,
  apiCusTransTempDown,
  apiDownloadAppendixFile,
};
