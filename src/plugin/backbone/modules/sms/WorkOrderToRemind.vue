<template>
  <div style="padding: 0">
    <head-content-layout class="page-wrap">
      <template #header>
        <el-row style="display: flex">
          <el-col :xs="24" :sm="12" :md="14" :lg="18" style="display: flex">
            <div v-if="tab == 'ty'">
              <div style="display: inline-block; margin-left: 5px">流程类型：</div>
              <dict-select
                :value.sync="seachData.flowType"
                :dictId="10202"
                placeholder="请选择流程类型"
                style="width: 240px"
                @change="changeFlowType"
              />
            </div>
            <div v-if="tab == 'ty'">
              <div style="display: inline-block; margin-left: 20px">专业：</div>
              <dict-select
                :value.sync="seachData.professionalType"
                :dictId="professionalDictId"
                placeholder="请选择专业"
                style="width: 270px"
                @change="changeProfessionalType"
              />
            </div>
            <div v-if="tab == 'ty'">
              <div style="display: inline-block; margin-left: 20px">省份：</div>
              <el-select
                v-model="seachData.province"
                placeholder="请选择省份"
                style="width: 280px"
                clearable
              >
                <el-option
                  v-for="(item, i) in dictData"
                  :key="i"
                  :label="item.dictName"
                  :value="item.dictCode"
                >
                </el-option>
              </el-select>
            </div>
            <div v-if="tab == 'gl'">
              <div style="display: inline-block; margin-left: 5px">专业：</div>
              <dict-select
                :value.sync="seachData.professionalType"
                :dictId="10002"
                placeholder="请选择专业"
                style="width: 270px"
              />
            </div>
            <div v-if="tab == 'gl'">
              <div style="display: inline-block; margin-left: 20px">
                客户名称：
              </div>
              <el-input
                v-model="seachData.customerName"
                style="width: 270px"
              ></el-input>
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :md="10" :lg="6" style="text-align: right">
            <div>
              <el-button type="primary" @click="seniorQuery">查询</el-button>
              <el-button @click="onResetForm">重置</el-button>
            </div>
          </el-col>
        </el-row>
        <div class="order__menuList">
          <el-button type="primary" @click="createSeup">+ 新建</el-button>
          <el-button @click="batchDelete">批量删除</el-button>
          <el-button @click="handlebatch('启用')">启用</el-button>
          <el-button @click="handlebatch('禁用')">禁用</el-button>
          <el-button
            type="primary"
            v-show="
              (mainProfessionalType == '2' || mainProfessionalType == '1,2') &&
              tab == 'gl'
            "
            @click="onDownTemp"
            >客户业务所属传输系统模板</el-button
          >
        </div>
      </template>
      <template #table>
        <el-tabs v-model="tab" @tab-click="handleClick">
          <el-tab-pane
            v-if="mainProfessionalType == '1' || mainProfessionalType == '1,2'"
            label="通用短信通知规则"
            name="ty"
            :style="{
              height: tableHeight,
            }"
          >
            <general-remind
              :professionalType="seachData.professionalType"
              :province="seachData.province"
              :flowType="seachData.flowType"
              ref="generalRemind"
            />
          </el-tab-pane>
          <el-tab-pane
            v-if="mainProfessionalType == '2' || mainProfessionalType == '1,2'"
            label="光缆短信通知规则"
            name="gl"
            :style="{
              height: tableHeight,
            }"
          >
            <fiber-remind
              :professionalType="seachData.professionalType"
              :customerName="seachData.customerName"
              ref="fiberRemind"
            />
          </el-tab-pane>
        </el-tabs>
      </template>
    </head-content-layout>
    <fiber-add
      :fiberTitle.sync="fiberTitle"
      :fiberVisible.sync="fiberVisible"
      ref="fiberAdd"
    />
    <general-add
      :generalTitle.sync="generalTitle"
      :generalVisible.sync="generalVisible"
      ref="generalAdd"
    />
  </div>
</template>
<script>
import { apiDict } from "./api/CommonApi";
import { mapGetters } from "vuex";
import DictSelect from "../workOrder/components/DictSelect.vue";
import HeadContentLayout from "../newIvr/components/HeadContentLayout.vue";
import GeneralRemind from "./components/GeneralRemind.vue";
import FiberRemind from "./components/FiberRemind.vue";
import FiberAdd from "./components/FiberAdd.vue";
import GeneralAdd from "./components/GeneralAdd.vue";

import { apiCusTransTempDown } from "./api/WorkOrderToRemind";

export default {
  name: "WorkOrderToRemind",
  components: {
    DictSelect,
    HeadContentLayout,
    GeneralRemind,
    FiberRemind,
    FiberAdd,
    GeneralAdd,
  },
  data() {
    return {
      tab: "ty",
      //查询条件
      seachData: {
        professionalType: "", //专业
        province: "", //省份
        customerName: "", //客户名称
        flowType: "",//流程类型
      },
      professionalDictId: 10002,
      provinceId: 10211,
      dictData: [],
      generalTitle: "新增",
      fiberTitle: "新增",
      generalVisible: false,
      fiberVisible: false,
      createUserId: JSON.parse(sessionStorage.userInfo).userName,
    };
  },
  watch: {
    tab: {
      handler(newV) {
        this.seachData.professionalType = "";
        this.seachData.province = "";
        this.seachData.customerName = "";
      },
      deep: true,
    },
  },
  computed: {
    ...mapGetters(["userInfo", "frameStyle"]),
    tableHeight() {
      return `calc(100vh - ${this.frameStyle.headerHeight}px - 200px)`;
    },
    mainProfessionalType() {
      return this.$route.query.type;
    },
  },
  mounted() {
    this.getDictData("10211");
  },
  methods: {
    //新建
    createSeup() {
      if (this.tab == "gl") {
        (this.fiberTitle = "新增"), (this.fiberVisible = true);
      } else {
        (this.generalTitle = "新增"), (this.generalVisible = true);
      }
    },
    changeFlowType(){
      this.seachData.professionalType = "";
      this.seachData.province = "";
      if(this.seachData.flowType == '4' || this.seachData.flowType == '8'){
        this.professionalDictId = '60012';
      } else if (this.seachData.flowType == '1'){
        this.professionalDictId = '10206';
      } else if (this.seachData.flowType == '2'){
        this.professionalDictId = '10207';
      } else if (this.seachData.flowType == '3'){
        this.professionalDictId = '10208';
      } else if (this.seachData.flowType == '8'){
        this.professionalDictId = '60012';
      } else if (this.seachData.flowType == '10'){
        this.professionalDictId = '60009';
      } else if (this.seachData.flowType == '11'){
        this.professionalDictId = '60010';
      } else if (this.seachData.flowType == '12'){
        this.professionalDictId = '60011';
      } else if (this.seachData.flowType == '7'){
        this.professionalDictId = '10209';
      }
      this.changeProfessionalType();
    },
    changeProfessionalType(){
      this.seachData.province = "";
      if(this.seachData.professionalType == '19' ||this.seachData.professionalType == '20' ||this.seachData.professionalType == '12' ||this.seachData.professionalType == '13' ||this.seachData.professionalType == '22' ||this.seachData.professionalType == '25'){
        this.getDictData("10210");
      }else if(this.seachData.professionalType == '23'){
        this.dictData = [{'dictCode': '-1','dictName': '联通系统集成有限公司'}];
      }else{
        this.getDictData("10211");
      }
    },
    getDictData(value) {
      let param = {
        dictTypeCode: value,
      };
      apiDict(param)
        .then(res => {
          if (res.code == 200) {
            this.dictData = res?.data ?? [];
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    //批量删除
    batchDelete() {
      if (this.tab == "gl") {
        this.$refs.fiberRemind.batchDelete();
      } else {
        this.$refs.generalRemind.batchDelete();
      }
    },
    //启用 禁用
    handlebatch(type) {
      if (this.tab == "gl") {
        this.$refs.fiberRemind.handlebatch(type);
      } else {
        this.$refs.generalRemind.handlebatch(type);
      }
    },
    //重置
    onResetForm() {
      this.seachData.professionalType = ""; //专业
      this.seachData.province = ""; //省份
      this.seachData.flowType = "";
      this.seachData.customerName = ""; //客户名称
    },
    //查询
    seniorQuery() {
      if (this.tab === "gl") {
        this.$refs.fiberRemind.getQueryRule("senior");
      } else {
        this.$refs.generalRemind.getQueryRule("senior");
      }
    },
    handleClick() {},
    onDownTemp() {
      apiCusTransTempDown();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../newIvr/assets/ivrCommon.scss";
.order__menuList {
  margin-top: 10px;
  padding-bottom: 10px;
}
::v-deep .el-dialog__body {
  padding: 0;
  padding-right: 50px;
  padding-left: 30px;
}
::v-deep .el-form-item__content {
  position: relative;
  .item__unit {
    position: absolute;
    display: block;
    top: 0;
    left: 106px;
    color: #dcdfe6;
  }
}
::v-deep .el-dialog__wrapper {
  .el-dialog {
    margin-top: 6vh !important;
    .el-dialog__body {
      .el-form {
        .el-form-item,
        .el-form-item--mini {
          margin: 0;
          margin-bottom: 14px;
        }
      }
    }
  }
}
</style>
