<template>
  <head-content-layout>
    <template #header>
      <el-row>
        <el-form
          :model="queryForm"
          ref="queryForm"
          :inline="true"
          label-width="85px"
        >
          <el-col :xs="24" :sm="24" :md="24" :lg="20">
            <el-form-item label="流程名称：">
              <dict-select
                :value.sync="queryForm.processName"
                :dictId="60137"
                :dict-list.sync="dicts.processName"
                placeholder="请选择流程名称"
                style="width: 180px"
              />
            </el-form-item>
            <el-form-item label="专业：">
              <dict-select
                :value.sync="queryForm.professionalType"
                :dictId="10002"
                :dict-list.sync="dicts.professionalType"
                placeholder="请选择专业"
                style="width: 180px"
              />
            </el-form-item>
            <el-form-item label="省份：">
              <dict-select
                :value.sync="queryForm.province"
                :dictId="10073"
                :dict-list.sync="dicts.province"
                placeholder="请选择省份"
                style="width: 180px"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="24" :md="4" :lg="4" style="text-align: right">
            <el-form-item>
              <el-button type="primary" @click="onQuery">查询</el-button>
              <el-button @click="onReset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>
    </template>
    <template #contentHeader>
      <el-button type="primary" icon="el-icon-plus" @click="onAdd"
        >新建</el-button
      >
      <el-button type="default" @click="onMultipleDel">删除</el-button>
      <el-button type="default" @click="onMultipleSave">保存</el-button>
    </template>
    <template #table>
      <el-table
        :data="table.data"
        stripe
        height="100%"
        @selection-change="onSelectionChange"
      >
        <el-table-column type="selection"></el-table-column>
        <el-table-column prop="processName" label="流程名称">
          <template v-slot="{ row, column }">{{
            code2Label(row[column.property], column.property)
          }}</template>
        </el-table-column>
        <el-table-column prop="professionalType" label="专业">
          <template v-slot="{ row, column }">{{
            code2Label(row[column.property], column.property)
          }}</template>
        </el-table-column>
        <el-table-column prop="province" label="省份">
          <template v-slot="{ row, column }">{{
            code2Label(row[column.property], column.property)
          }}</template>
        </el-table-column>
        <el-table-column prop="sms" label="短信发送">
          <template v-slot="{ row, column }">
            <el-switch
              v-model="row[column.property]"
              active-color="#169bd5"
              :active-value="1"
              :inactive-value="0"
            >
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column prop="ivr" label="IVR发送">
          <template v-slot="{ row, column }">
            <el-switch
              v-model="row[column.property]"
              active-color="#169bd5"
              :active-value="1"
              :inactive-value="0"
            >
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column prop="mail" label="邮件发送">
          <template v-slot="{ row, column }">
            <el-switch
              v-model="row[column.property]"
              active-color="#169bd5"
              :active-value="1"
              :inactive-value="0"
            >
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column prop="dingTalk" label="钉钉发送">
          <template v-slot="{ row, column }">
            <el-switch
              v-model="row[column.property]"
              active-color="#169bd5"
              :active-value="1"
              :inactive-value="0"
            >
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column prop="app" label="APP发送">
          <template v-slot="{ row, column }">
            <el-switch
              v-model="row[column.property]"
              active-color="#169bd5"
              :active-value="1"
              :inactive-value="0"
            >
            </el-switch>
          </template>
        </el-table-column>
      </el-table>
    </template>
    <template #pagination>
      <pagination
        ref="pagination"
        :total="table.total"
        :page.sync="queryForm.pageIndex"
        :limit.sync="queryForm.pageSize"
        @change="queryData"
      />
    </template>

    <template #dialog>
      <el-dialog
        title="新增"
        :visible.sync="dialog.visible"
        :close-on-click-modal="false"
        width="450px"
        @close="onDialogClose"
      >
        <el-form
          ref="dialogForm"
          :model="dialog.form"
          :rules="dialog.rules"
          :inline="false"
          label-width="85px"
        >
          <el-form-item label="流程名称" prop="processName">
            <el-select
              v-model="dialog.form.processName"
              filterable
              @change="onProcessNameChange"
              style="width: 100%"
            >
              <el-option
                v-for="item in dicts.processName"
                :key="item.dictId"
                :label="item.dictName"
                :value="item.dictCode"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="专业" prop="professionalType">
            <dict-select
              :value.sync="dialog.form.professionalType"
              :dictId="dialog.proTypeDictId"
              multiple
              placeholder="请选择专业"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="省份" prop="province">
            <!-- <div v-if="dialog.form.province.includes('120')">集团</div> -->
            <el-select
              v-if="dialog.oper"
              v-model="dialog.form.province"
              filterable
              multiple="true"
              style="width: 100%"
            >
              <el-option
                v-for="item in dicts.province"
                :key="item.dictId"
                :label="item.dictName"
                :value="item.dictCode"
              >
              </el-option>
            </el-select>
            <el-select
              v-else
              v-model="dialog.form.province"
              filterable
              multiple="true"
              style="width: 100%"
            >
              <el-option
                v-for="item in dicts.province.filter(
                  item => item.dictCode != '120'
                )"
                :key="item.dictId"
                :label="item.dictName"
                :value="item.dictCode"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>

        <span slot="footer">
          <el-button @click="dialog.visible = false">取消</el-button>
          <el-button type="primary" @click="onSave">确定</el-button>
        </span>
      </el-dialog>
    </template>
  </head-content-layout>
</template>

<script>
import HeadContentLayout from "@plugin/backbone/modules/workOrder/components/HeadContentLayout.vue";
import DictSelect from "@plugin/backbone/modules/workOrder/components/DictSelect.vue";
import Pagination from "@plugin/backbone/modules/workOrder/components/Pagination.vue";

import {
  getMsgSwitchListApi,
  addMsgRulesApi,
  mulDelApi,
  mulEditApi,
} from "./api/msgSwitch.js";

export default {
  name: "MsgSwitch",
  components: {
    HeadContentLayout,
    DictSelect,
    Pagination,
  },
  data() {
    return {
      queryForm: {
        processName: "",
        professionalType: "",
        province: "",
        pageIndex: 1,
        pageSize: 10,
      },
      table: {
        data: [],
        total: 0,
        mulSelection: [],
      },
      dicts: {
        processName: [],
        professionalType: [],
        province: [],
      },
      dialog: {
        visible: false,
        form: {
          processName: "",
          professionalType: [],
          province: [],
        },
        rules: {
          processName: [{ required: true, message: "流程名称不能为空" }],
          professionalType: [{ required: true, message: "专业不能为空" }],
          province: [{ required: true, message: "省份不能为空" }],
        },
        oper: true,
        proTypeDictId: 10205,
        proTypeDictIds: Object.freeze({
          1: 10206,
          2: 10207,
          3: 10208,
          4: 10205,
          5: 10205,
          7: 10209,
          8: 60012,
          9: 10209,
          10: 60009,
          11: 60010,
          12: 60011,
        }),
      },
    };
  },
  mounted() {
    this.onQuery();
  },
  methods: {
    onQuery() {
      this.queryForm.pageIndex = 1;
      this.queryData();
    },
    onReset() {
      this.queryForm.processName = "";
      this.queryForm.professionalType = "";
      this.queryForm.province = "";
    },
    queryData() {
      getMsgSwitchListApi(this.queryForm).then(res => {
        this.table.data = res?.data?.records || [];
        this.table.total = res.data?.total || 0;
      });
    },
    code2Label(code, property) {
      return this.dicts[property].find(item => item.dictCode == code)?.dictName;
    },
    onSelectionChange(selection) {
      this.table.mulSelection = selection;
    },
    onAdd() {
      this.dialog.visible = true;
    },
    onProcessNameChange(command) {
      this.dialog.proTypeDictId = this.dialog.proTypeDictIds[command] || 10002;
      if (["1", "2", "3", "8","10","11","12"].includes(`${command}`)) {
        this.dialog.oper = true;
        // this.dialog.form.province = ["120"];
      } else {
        this.dialog.oper = false;
        // this.dialog.form.province = [];
      }
    },
    onSave() {
      this.$refs.dialogForm?.validate(valid => {
        if (valid) {
          const dialogFormData = this.dialog.form;
          addMsgRulesApi({
            processName: dialogFormData.processName,
            professionalTypes: dialogFormData.professionalType.join(),
            provinces: dialogFormData.province.join(),
          })
            .then(res => {
              if (res.status == 0) {
                this.$message({
                  message: "规则添加成功",
                  type: "success",
                });
                this.queryData();
                this.dialog.visible = false;
              } else {
                this.$alert(res.msg || res.message || "规则添加失败");
              }
            })
            .catch(res => {
              this.$alert(res.msg || res.message || "规则添加失败");
            });
        }
      });
    },
    onDialogClose() {
      this.dialog.form = {
        processName: "8",
        professionalType: [],
        province: ["120"],
      };
      this.$refs.dialogForm.clearValidate();
    },
    onMultipleDel() {
      if (this.table.mulSelection.length == 0) {
        this.$alert("请选择至少一条数据");
        return;
      }
      this.$confirm("确定删除所选的规则吗？", {
        type: "warning",
      })
        .then(() => {
          mulDelApi(this.table.mulSelection.map(item => item.ruleId))
            .then(res => {
              if (res.status == 0) {
                this.$message({
                  message: "规则删除成功",
                  type: "success",
                });
                this.onQuery();
              } else {
                this.$alert(res.msg || res.message || "规则删除失败");
              }
            })
            .catch(res => {
              this.$alert(res.msg || res.message || "规则删除失败");
            });
        })
        .catch(() => {});
    },
    onMultipleSave() {
      this.$confirm("是否保存当前页的规则？", {
        type: "warning",
      })
        .then(() => {
          mulEditApi(
            this.table.data.map(item => ({
              ruleId: item.ruleId,
              processName: item.processName,
              province: item.province,
              provinces: null,
              professionalType: item.professionalType,
              professionalTypes: null,
              sms: item.sms,
              ivr: item.ivr,
              mail: item.mail,
              app: item.app,
              dingTalk: item.dingTalk,
            }))
          )
            .then(res => {
              if (res.status == 0) {
                this.$message({
                  message: "规则编辑成功",
                  type: "success",
                });
                this.queryData();
              } else {
                this.$alert(res.msg || res.message || "规则编辑失败");
              }
            })
            .catch(res => {
              this.$alert(res.msg || res.message || "规则编辑失败");
            });
        })
        .catch(() => {});
    },
  },
};
</script>
