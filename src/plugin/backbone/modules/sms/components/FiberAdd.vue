<template>
  <el-dialog
    :title="fiberTitle"
    :visible.sync="fiberVisible"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="850px"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="180px">
      <el-form-item label="规则制定人:" prop="createUserName">
        <el-input
          disabled
          v-model="form.createUserName"
          maxlength="25"
        ></el-input>
      </el-form-item>
      <el-form-item label="制订人所属组织:" prop="createUserDept">
        <el-input
          disabled
          v-model="form.createUserDept"
          maxlength="25"
        ></el-input>
      </el-form-item>

      <el-form-item label="省份:" prop="provinceName">
        <dict-select
          :notSelect="fiberTitle == '编辑' ? true : false"
          :value.sync="form.provinceName"
          :dictId="10064"
          placeholder="请选择省份"
          style="width: 240px"
        />
      </el-form-item>

      <el-form-item label="专业:" prop="professionalType">
        <dict-select
          :notSelect="true"
          :value.sync="form.professionalType"
          :dictId="10002"
          placeholder="请选择专业"
          style="width: 240px"
        />
      </el-form-item>

      <el-form-item label="描述:" prop="ruleComment">
        <el-input
          v-model="form.ruleComment"
          type="textarea"
          maxlength="255"
          show-word-limit
        ></el-input>
      </el-form-item>

      <el-form-item label="工单来源:" prop="createType">
        <el-checkbox-group v-model="form.createType">
          <div style="display: flex">
            <el-checkbox label="电子运维新建" name="type"></el-checkbox>
            <el-checkbox label="智能监控自动" name="type"></el-checkbox>
          </div>
          <div style="display: flex">
            <el-checkbox label="智能监控手动" name="type"></el-checkbox>
            <el-checkbox label="电信共建共享" name="type"></el-checkbox>
          </div>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="客户名称:" prop="customerName">
        <el-input
          v-model="form.customerName"
          :disabled="fiberTitle != '新增'"
        ></el-input>
      </el-form-item>
      <el-form-item label="客户业务所属传输系统:" prop="busTransSystem">
        <div class="bus-trans-sys">
          <div class="left">
            <el-input
              v-if="!formOther.isFile"
              v-model="form.busTransSystem"
              type="textarea"
              rows="2"
              placeholder="请输入光缆系统名称，多个光缆系统以英文逗号分隔"
            ></el-input>
            <template v-else>
              <el-link
                v-if="!formOther.isLocalFile"
                class="file-name text-truncate"
                :title="formOther.fileName"
                :underline="false"
                style="color: #409eff"
                @click="onDownBusTransSystem"
              >
                {{ formOther.fileName }}
              </el-link>
              <div
                v-else
                class="file-name text-truncate"
                :title="formOther.fileName"
              >
                {{ formOther.fileName }}
              </div>
              <el-button type="text" @click="onClearFile"
                ><em class="el-icon-delete cursor-pointer"></em
              ></el-button>
            </template>
          </div>
          <el-button type="primary" @click="onFileSelect">附件上传</el-button>
        </div>
      </el-form-item>
      <el-form-item label="通知方式:" required>
        <div>
          <el-button type="text" size="small" @click="addRows()"
            >新增</el-button
          >
          <div v-show="validMsgFirst" class="text-danger">
            {{ validMsgFirst }}
          </div>
          <el-table
            :data="form.sendObject"
            style="width: 750px"
            max-height="196px"
            class="send-table"
          >
            <el-table-column
              label="姓名"
              width="95"
              prop="name"
              header-align="center"
            >
              <template v-slot="{ row, $index }">
                <el-form-item
                  :prop="`sendObject.${$index}.name`"
                  :rules="rules.name"
                  :show-message="false"
                >
                  <el-input v-model="row.name"></el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column min-width="40" prop="isSendSms">
              <template v-slot:header>
                <el-checkbox
                  :indeterminate="isSmsIndeterminate"
                  v-model="checkAllSms"
                  @change="changeSmsCheckAll(checkAllSms)"
                ></el-checkbox>
              </template>
              <template v-slot="{ row, $index }">
                <el-form-item
                  :prop="`sendObject.${$index}.isSendSms`"
                  :rules="rules.isSendSms"
                  :show-message="false"
                >
                  <el-checkbox
                    v-model="row.isSendSms"
                    @change="changeSmsCheckbox(row.isSendSms)"
                  ></el-checkbox>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              label="手机号"
              width="145"
              prop="mobile"
              header-align="center"
            >
              <template v-slot="{ row, $index }">
                <el-form-item
                  :prop="`sendObject.${$index}.mobile`"
                  :rules="rules.mobile"
                  :show-message="false"
                >
                  <el-input
                    v-model="row.mobile"
                    @change="changeMobile(row.mobile)"
                  ></el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column min-width="40" prop="isSendEmail">
              <template v-slot:header>
                <el-checkbox
                  :indeterminate="isEmailIndeterminate"
                  v-model="checkAllEmail"
                  @change="changeEmailCheckAll(checkAllEmail)"
                ></el-checkbox>
              </template>
              <template v-slot="{ row, $index }">
                <el-form-item
                  :prop="`sendObject.${$index}.isSendEmail`"
                  :rules="rules.isSendEmail"
                  :show-message="false"
                >
                  <el-checkbox
                    v-model="row.isSendEmail"
                    @change="changeEmailCheckbox(row.isSendEmail)"
                  ></el-checkbox>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column
              label="邮箱"
              width="220"
              prop="email"
              header-align="center"
            >
              <template v-slot="{ row, $index }">
                <el-form-item
                  :prop="`sendObject.${$index}.email`"
                  :rules="rules.email"
                  :show-message="false"
                >
                  <el-input
                    v-model="row.email"
                    @change="changeEmail(row.email)"
                  ></el-input>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column min-width="40" fixed="right">
              <template slot-scope="scope">
                <el-form-item>
                  <el-button @click="deleteRows(scope)" type="text" size="small"
                    >删除
                  </el-button>
                </el-form-item>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="_save"> 确 定 </el-button>
      <el-button @click="_cancel">取 消</el-button>
    </span>

    <el-dialog
      title="附件选择"
      :visible.sync="formOther.fileDiaVisible"
      width="30%"
      :close-on-click-modal="false"
      append-to-body
    >
      <el-upload
        class="upload-bus-trans-sys"
        drag
        action=""
        :file-list="formOther.fileList"
        :auto-upload="false"
        :on-change="onFileChange"
        :on-remove="onFileChange"
        accept=".xls,.xlsx"
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip" slot="tip">
          <el-link type="primary" style="color: #409eff" @click="onDownTemp"
            >客户业务所属传输系统模板</el-link
          >
        </div>
      </el-upload>
      <span slot="footer">
        <el-button @click="formOther.fileDiaVisible = false">取消</el-button>
        <el-button type="primary" @click="onFileSelected">确定</el-button>
      </span>
    </el-dialog>
  </el-dialog>
</template>

<script>
import isObject from "lodash/isObject";

import DictSelect from "../../workOrder/components/DictSelect.vue";

import {
  apiCusTransTempDown,
  apiDownloadAppendixFile,
} from "../api/WorkOrderToRemind.js";
import { apiFiberRuleFile } from "../api/WorkOrderFiberRemind";

import Bus from "../../workOrder/bus";

export default {
  name: "RemindAdd",
  components: {
    DictSelect,
  },
  props: {
    fiberVisible: {
      type: Boolean,
      default: false,
    },
    fiberTitle: {
      type: String,
      default: "新增",
    },
    // required: true,
  },
  data() {
    let self = this;
    const checkTransSys = (rule, value = "", callback) => {
      if (self.formOther.isFile) {
        if (self.formOther.isLocalFile && self.formOther.upFiles.length == 0) {
          return callback(new Error("请选择一个附件"));
        }
        if (!self.formOther.isLocalFile && self.formOther.fileName == "") {
          return callback(new Error("请选择一个附件或者填写业务所属传输系统"));
        }
      } else {
        if (value == "") {
          return callback(new Error("请填写业务所属传输系统"));
        }
      }
      callback();
    };
    const checkName = (rule, value = "", callback) => {
      const rowIndex = rule.fullField.split(".")[1];
      if (value == "") {
        self.validMsg.splice(0, 1, `请输入第${Number(rowIndex) + 1}行姓名`);
        return callback(new Error("请输入姓名"));
      }
      self.validMsg.splice(0, 1, "");
      callback();
    };
    const checkIsSend = (rule, value = false, callback) => {
      const rowIndex = rule.fullField.split(".")[1];
      const isSendEmail = self.form.sendObject[rowIndex].isSendEmail;
      const isSendSms = self.form.sendObject[rowIndex].isSendSms;
      if (!isSendEmail && !isSendSms) {
        self.$refs?.form?.validateField?.(`sendObject.${rowIndex}.mobile`);
        self.$refs?.form?.validateField?.(`sendObject.${rowIndex}.email`);
        self.validMsg.splice(
          1,
          1,
          `第${Number(rowIndex) + 1}行手机号和邮箱至少要选择一项`
        );
        return callback(new Error("手机号和邮箱至少要选择一项"));
      }
      self.$refs?.form?.validateField?.(`sendObject.${rowIndex}.mobile`);
      self.$refs?.form?.validateField?.(`sendObject.${rowIndex}.email`);
      self.validMsg.splice(1, 1, "");
      callback();
    };
    const checkMobile = (rule, value = "", callback) => {
      const rowIndex = rule.fullField.split(".")[1];
      const isSendSms = self.form.sendObject[rowIndex].isSendSms;
      if (isSendSms) {
        if (value == "") {
          self.validMsg.splice(2, 1, `请输入第${Number(rowIndex) + 1}行手机号`);
          return callback(new Error("手机号格式不正确"));
        }
      }
      if (value != "" && !/^1[3456789]{1}\d{9}$/.test(value)) {
        self.validMsg.splice(
          2,
          1,
          `第${Number(rowIndex) + 1}行手机号格式不正确`
        );
        return callback(new Error("手机号格式不正确"));
      }
      self.validMsg.splice(2, 1, "");
      callback();
    };
    const checkEmail = (rule, value = "", callback) => {
      const rowIndex = rule.fullField.split(".")[1];
      const isSendEmail = self.form.sendObject[rowIndex].isSendEmail;
      if (isSendEmail) {
        if (value == "") {
          self.validMsg.splice(3, 1, `请输入第${Number(rowIndex) + 1}行邮箱`);
          return callback(new Error("邮箱格式不正确"));
        }
      }
      if (
        value != "" &&
        !/^[A-Za-z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/.test(value)
      ) {
        self.validMsg.splice(3, 1, `第${Number(rowIndex) + 1}行邮箱格式不正确`);
        return callback(new Error("邮箱格式不正确"));
      }
      self.validMsg.splice(3, 1, "");
      callback();
    };
    return {
      // tableLoading: false,
      // tableData: [],
      form: {
        createUserName: "", //规则指定人
        createUserDept: "",
        provinceName: "全国",
        professionalType: "3",
        professionalTypeCode: 3,
        ruleComment: "",
        createType: [
          "电子运维新建",
          "智能监控自动",
          "智能监控手动",
          "电信共建共享",
        ],
        ruleStatus: 0,
        ruleId: "",
        customerName: "",
        busTransSystem: "",
        pageNum: 1,
        pageSize: 10,
        total: 0,
        sendObject: [
          {
            name: "",
            mobile: "",
            isSendSms: false,
            email: "",
            isSendEmail: false,
          },
        ],
      },
      formOther: {
        original: "",
        isFile: false,
        isLocalFile: false,
        attId: "",
        fileName: "",
        upFiles: [],
        fileList: [],
        fileDiaVisible: false,
      },
      typeList: [
        "智能监控自动",
        "智能监控手动",
        "电子运维新建",
        "电信共建共享",
      ],
      multipleSelection: [],

      createUserId: JSON.parse(sessionStorage.userInfo).userName,

      rules: {
        provinceName: [
          { required: true, message: "请选择省份", trigger: "change" },
        ],
        professionalType: [
          { required: true, message: "请选择专业", trigger: "change" },
        ],
        createType: [
          {
            required: true,
            type: "array",
            message: "请选择工单来源",
            trigger: "change",
          },
        ],
        busTransSystem: [
          {
            required: true,
            message: "请填写业务所属传输系统",
            trigger: "blur",
            validator: checkTransSys,
          },
        ],
        customerName: [
          {
            required: true,
            message: "请填写客户名称",
          },
        ],
        sendObject: [
          {
            required: true,
          },
        ],
        name: [{ validator: checkName }],
        isSendSms: [{ type: "boolean", validator: checkIsSend }],
        isSendEmail: [{ type: "boolean", validator: checkIsSend }],
        mobile: [{ validator: checkMobile }],
        email: [{ validator: checkEmail }],
      },
      validMsg: [],
      options: [{ value: "中国联通总部", label: "中国联通总部" }],
      isSmsIndeterminate: false,
      isEmailIndeterminate: false,
      checkAllSms: false,
      checkAllEmail: false,
      smsCheckboxCount: 0,
      emailCheckboxCount: 0,
      userData: null,
    };
  },
  computed: {
    validMsgFirst() {
      return this.validMsg.filter(item => item != "")[0];
    },
  },
  mounted() {
    this.form.createUserName = JSON.parse(sessionStorage.userInfo).realName;
    this.userData = JSON.parse(JSON.parse(sessionStorage.userInfo).attr2);
    this.form.createUserDept = this.userData.orgInfo.fullOrgName.split("-")[0];
    if (this.form.createUserDept == null || this.form.createUserDept == "") {
      this.form.createUserDept = "中国联通总部";
    }
    Bus.$on("fiber", rows => {
      this.clickEditor(rows);
    });
  },

  methods: {
    changeEmail(val) {
      if (val != null && val != "") {
        var reg = /^[A-Za-z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
        if (!reg.test(val)) {
          this.$message({
            message: "您输入的邮箱格式有误，请检查您的邮箱格式",
            type: "warning",
          });
        }
      }
    },
    changeMobile(val) {
      if (val != null && val != "") {
        var reg = /^1[3456789]{1}\d{9}$/;
        if (!reg.test(val)) {
          this.$message({
            message: "您输入的手机号格式有误，请检查您的手机号格式",
            type: "warning",
          });
        }
      }
    },
    changeSmsCheckbox(val) {
      if (val) {
        this.smsCheckboxCount += 1;
        if (this.smsCheckboxCount > 0) {
          this.isSmsIndeterminate = true;
          if (this.smsCheckboxCount == this.form.sendObject.length) {
            this.checkAllSms = true;
            this.isSmsIndeterminate = false;
          }
        }
      } else {
        this.smsCheckboxCount += -1;
        if (this.smsCheckboxCount < this.form.sendObject.length) {
          if (this.smsCheckboxCount == 0) {
            this.isSmsIndeterminate = false;
            this.checkAllSms = false;
          } else {
            this.isSmsIndeterminate = true;
            this.checkAllSms = false;
          }
        }
      }
    },
    changeEmailCheckbox(val) {
      if (val) {
        this.emailCheckboxCount += 1;
        if (this.emailCheckboxCount > 0) {
          this.isEmailIndeterminate = true;
          if (this.emailCheckboxCount == this.form.sendObject.length) {
            this.checkAllEmail = true;
            this.isEmailIndeterminate = false;
          }
        }
      } else {
        this.emailCheckboxCount += -1;
        if (this.emailCheckboxCount < this.form.sendObject.length) {
          if (this.emailCheckboxCount == 0) {
            this.isEmailIndeterminate = false;
            this.checkAllEmail = false;
          } else {
            this.isEmailIndeterminate = true;
            this.checkAllEmail = false;
          }
        }
      }
    },
    changeSmsCheckAll(val) {
      if (val) {
        for (let i = 0; i < this.form.sendObject.length; i++) {
          // this.form.sendObject[i].isSendSms = true;
          this.$set(this.form.sendObject[i], "isSendSms", true);
        }
        this.isSmsIndeterminate = false;
        this.smsCheckboxCount = this.form.sendObject.length;
      } else {
        for (let i = 0; i < this.form.sendObject.length; i++) {
          // this.form.sendObject[i].isSendSms = false;
          this.$set(this.form.sendObject[i], "isSendSms", false);
        }
        this.isSmsIndeterminate = false;
        this.smsCheckboxCount = 0;
      }
      this.$set(this, "checkAllSms", val);
    },
    changeEmailCheckAll(val) {
      if (val) {
        for (let i = 0; i < this.form.sendObject.length; i++) {
          // this.form.sendObject[i].isSendEmail = true;
          this.$set(this.form.sendObject[i], "isSendEmail", true);
        }
        this.isEmailIndeterminate = false;
        this.emailCheckboxCount = this.form.sendObject.length;
      } else {
        for (let i = 0; i < this.form.sendObject.length; i++) {
          // this.form.sendObject[i].isSendEmail = false;
          this.$set(this.form.sendObject[i], "isSendEmail", false);
        }
        this.isEmailIndeterminate = false;
        this.emailCheckboxCount = 0;
      }
      this.$set(this, "checkAllEmail", val);
    },
    //添加行
    addRows() {
      const newObj = {
        name: "",
        isSendSms: false,
        mobile: "",
        isSendEmail: false,
        email: "",
      };
      this.form.sendObject.splice(this.form.sendObject.length, 0, newObj);
    },
    //删除行
    deleteRows(scope) {
      this.form.sendObject.splice(scope.$index, 1);
      // this.$refs["form"].clearValidate();
      if (this.form.sendObject.length == 0) this.validMsg = [];
    },
    //点击编辑
    clickEditor(rows) {
      if (rows.length > 0) {
        const arrData = rows;
        const {
          createUserName,
          provinceName,
          professionalType,
          professionalTypeCode,
          ruleComment,
          createType,
          busTransSystem,
          customerName,
          sendObject,
          ruleId,
          ruleStatus,
          createUserDept,
        } = arrData[0];
        this.form.ruleId = ruleId;
        this.form.createUserDept = createUserDept;
        this.form.createUserName = createUserName; //规则制定人
        this.form.provinceName = provinceName; //省份
        this.form.professionalType = professionalType; //专业
        this.form.professionalTypeCode = professionalTypeCode;
        this.form.ruleComment = ruleComment; //描述
        this.form.ruleStatus = ruleStatus;
        const l = [];
        var createTypeArr = createType.split(",");
        createTypeArr.forEach(el => {
          if (el == "0") {
            l.push("智能监控自动");
          }
          if (el == "1") {
            l.push("智能监控手动");
          }
          if (el == "2") {
            l.push("电子运维新建");
          }
          if (el == "3") {
            l.push("电信共建共享");
          }
        });
        this.form.createType = l; //来源
        this.formOther.original = busTransSystem;
        try {
          const busTransSystemObj = JSON.parse(busTransSystem) ?? {};
          if (isObject(busTransSystemObj)) {
            this.formOther.isFile = true;
            this.formOther.fileName = busTransSystemObj[0].name;
            this.formOther.attId = busTransSystemObj[0].id;
            this.formOther.isLocalFile = false;
          } else {
            this.form.busTransSystem = busTransSystem;
            this.formOther.isFile = false;
            this.formOther.isLocalFile = false;
          }
        } catch (error) {
          this.form.busTransSystem = busTransSystem;
          this.formOther.isFile = false;
          this.formOther.isLocalFile = false;
        }
        this.form.customerName = customerName;
        this.form.sendObject = JSON.parse(sendObject);
        this.smsCheckboxCount = 0;
        this.emailCheckboxCount = 0;
        for (var i = 0; i < this.form.sendObject.length; i++) {
          if (this.form.sendObject[i].isSendSms) {
            this.smsCheckboxCount += 1;
          }
          if (this.form.sendObject[i].isSendEmail) {
            this.emailCheckboxCount += 1;
          }
        }
        if (this.smsCheckboxCount > 0) {
          if (this.smsCheckboxCount == this.form.sendObject.length) {
            this.isSmsIndeterminate = false;
            this.checkAllSms = true;
          } else {
            this.isSmsIndeterminate = true;
            this.checkAllSms = false;
          }
        }
        if (this.emailCheckboxCount > 0) {
          if (this.emailCheckboxCount == this.form.sendObject.length) {
            this.isEmailIndeterminate = false;
            this.checkAllEmail = true;
          } else {
            this.isEmailIndeterminate = true;
            this.checkAllEmail = false;
          }
        }
      }
      this.$emit("update:fiberTitle", "编辑");
      this.$emit("update:fiberVisible", true);
    },
    //dialog确定
    _save() {
      let self = this;
      self.$refs.form.validate(valid => {
        if (valid) {
          if (
            self.form.createType.length == 0 ||
            self.form.sendObject.length == 0
          ) {
            self.$message({
              message: "您有未填写的必填项,新增失败",
              type: "warning",
            });
            return;
          }
          let l = [];
          console.log(self.form.createType);
          self.form.createType.forEach(el => {
            if (el == "智能监控自动") {
              l.push(0);
            }
            if (el == "智能监控手动") {
              l.push(1);
            }
            if (el == "电子运维新建") {
              l.push(2);
            }
            if (el == "电信共建共享") {
              l.push(3);
            }
          });
          l.sort(function (a, b) {
            return a - b;
          });
          var objArr = self.form.sendObject;
          var reg = /^[A-Za-z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
          var regPhone = /^1[3456789]{1}\d{9}$/;
          for (var i = 0; i < objArr.length; i++) {
            if (objArr[i].name == null || objArr[i].name == "") {
              this.$message({
                message: "通知方式中，姓名不能为空，请检查您输入的数据",
                type: "warning",
              });
              return;
            }
            if (
              (objArr[i].mobile == null || objArr[i].mobile == "") &&
              (objArr[i].email == null || objArr[i].email == "")
            ) {
              this.$message({
                message: "手机号和邮箱不能同时为空，请检查您输入的数据",
                type: "warning",
              });
              return;
            }
            if (
              objArr[i].isSendSms == false &&
              objArr[i].isSendEmail == false
            ) {
              this.$message({
                message: "手机号和邮箱至少要选择一项，请检查您输入的数据",
                type: "warning",
              });
              return;
            }
            if (
              (objArr[i].isSendSms == true &&
                (objArr[i].mobile == null || objArr[i].mobile == "")) ||
              (objArr[i].isSendEmail == true &&
                (objArr[i].email == null || objArr[i].email == ""))
            ) {
              this.$message({
                message:
                  "选择手机号/邮箱之后，对应项不能为空，请检查您输入的数据",
                type: "warning",
              });
              return;
            }
            if (objArr[i].email != null && objArr[i].email != "") {
              if (!reg.test(objArr[i].email)) {
                this.$message({
                  message: "您输入的邮箱格式有误，请检查您的邮箱格式",
                  type: "warning",
                });
                return;
              }
            }
            if (objArr[i].mobile != null && objArr[i].mobile != "") {
              if (!regPhone.test(objArr[i].mobile)) {
                this.$message({
                  message: "您输入的手机号格式有误，请检查您的手机号格式",
                  type: "warning",
                });
                return;
              }
            }
          }
          const formData = new FormData();
          let param = {
            createUserId: self.createUserId,
            createUserName: self.form.createUserName,
            createUserDept: self.form.createUserDept,
            provinceName: self.form.provinceName,
            professionalType:
              self.fiberTitle == "新增"
                ? self.form.professionalType
                : self.form.professionalTypeCode,
            // professionalTypeCode: self.form.professionalType,
            ruleComment: self.form.ruleComment,
            createType: l.toString(),
            ruleStatus: self.form.ruleStatus,
            ruleId: self.form.ruleId,
            sendObject: JSON.stringify(objArr),
            customerName: self.form.customerName,
            busTransSystem: self.form.busTransSystem,
          };
          if (self.formOther.isFile) {
            if (self.formOther.isLocalFile) {
              formData.append("fileParam", self.formOther.upFiles[0].raw);
            } else {
              param.busTransSystem = self.formOther.original;
            }
          }
          formData.append("jsonParam", JSON.stringify(param));
          // return;
          apiFiberRuleFile(formData)
            .then(res => {
              if (res.status == "0") {
                if (self.fiberTitle == "新增") {
                  self.$emit("update:fiberVisible", false);
                  self.$message({
                    message: "添加成功",
                    type: "success",
                  });
                  self.$refs["form"].resetFields();
                  self.form.ruleId = "";
                  self.form.sendObject = [
                    {
                      name: "",
                      mobile: "",
                      isSendSms: false,
                      email: "",
                      isSendEmail: false,
                    },
                  ];
                  self.isSmsIndeterminate = false;
                  self.isEmailIndeterminate = false;
                  self.checkAllSms = false;
                  self.checkAllEmail = false;
                  self.smsCheckboxCount = 0;
                  self.emailCheckboxCount = 0;
                  Bus.$emit("getFiberList");
                } else {
                  self.$emit("update:fiberVisible", false);
                  self.$message({
                    message: "修改成功",
                    type: "success",
                  });
                  self.$refs["form"].resetFields();
                  Bus.$emit("getFiberList");
                }
              } else if (res.status == 200) {
                self.$alert("同一客户不可存在多条规则！", {
                  type: "error",
                });
              } else {
                self.$alert(res.msg, {
                  type: "error",
                });
              }
            })
            .catch(error => {
              self.$alert(error.msg || error.message || "保存失败", {
                type: "error",
              });
            });
        }
      });
    },
    //dialog取消
    _cancel() {
      this.$emit("update:fiberVisible", false);
      this.$refs["form"].resetFields();
    },
    //点击叉号
    handleClose() {
      this.$emit("update:fiberVisible", false);
      this.$refs["form"].resetFields();
      this.form = {
        createUserName: this.form.createUserName,
        createUserDept: this.form.createUserDept,
        provinceName: "全国",
        professionalType: "3",
        professionalTypeCode: 3,
        ruleComment: "",
        createType: [
          "电子运维新建",
          "智能监控自动",
          "智能监控手动",
          "电信共建共享",
        ],
        ruleStatus: 0,
        ruleId: "",
        customerName: "",
        busTransSystem: "",
        pageNum: 1,
        pageSize: 10,
        total: 0,
        sendObject: [
          {
            name: "",
            mobile: "",
            isSendSms: false,
            email: "",
            isSendEmail: false,
          },
        ],
      };
      this.isSmsIndeterminate = false;
      this.isEmailIndeterminate = false;
      this.checkAllSms = false;
      this.checkAllEmail = false;
      this.smsCheckboxCount = 0;
      this.emailCheckboxCount = 0;

      this.formOther = {
        original: "",
        isFile: false,
        isLocalFile: false,
        attId: "",
        fileName: "",
        upFiles: [],
        fileList: [],
        fileDiaVisible: false,
      };
      this.validMsg = [];
    },

    indexMethod(index) {
      const currentPage = this.form.pageNum;
      const pageSize = this.form.pageSize;
      return (currentPage - 1) * pageSize + (index + 1);
    },
    onClearFile() {
      this.formOther.isFile = false;
      this.formOther.isLocalFile = false;
      this.formOther.fileList = [];
      this.formOther.upFiles = [];
    },
    onFileSelect() {
      this.formOther.fileDiaVisible = true;
      this.formOther.fileList = [];
    },
    onDownTemp() {
      apiCusTransTempDown();
    },
    onFileChange(file, fileList) {
      this.formOther.fileList = fileList;
    },
    onFileSelected() {
      if (this.formOther.fileList.length != 1) {
        this.$alert("请选择一个文件，且只能选择一个文件", "提示", {
          confirmButtonText: "确定",
        });
        return;
      } else {
        let filename = this.formOther.fileList[0].name;
        let arr = filename.split(".");
        let filex = arr[arr.length - 1];
        if (!".xls.xlsx".includes(filex)) {
          this.$alert("文件类型不符合!", "", {
            confirmButtonText: "确定",
          });
          return;
        }
      }
      this.formOther.upFiles = this.formOther.fileList;
      this.formOther.isFile = true;
      this.formOther.isLocalFile = true;
      this.formOther.fileName = this.formOther.fileList[0]?.name;
      this.formOther.fileDiaVisible = false;
      this.$refs.form.clearValidate("busTransSystem");
    },
    onDownBusTransSystem() {
      const loading = this.$loading({
        lock: true,
      });
      apiDownloadAppendixFile({ attId: this.formOther.attId }).finally(() => {
        loading.close();
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../../newIvr/assets/ivrCommon.scss";
.bus-trans-sys {
  // display: inline-block;
  // width: calc(100% - 100px);
  display: flex;
  align-items: center;

  .left {
    flex-grow: 1;
    margin-right: 10px;
  }

  .file-name {
    display: inline-block;
    max-width: calc(100% - 24px);
    vertical-align: bottom;
    margin-right: 5px;
  }
}
.send-table ::v-deep .el-form-item {
  margin-bottom: 0px !important;
}
</style>

<style>
.upload-bus-trans-sys .el-upload {
  width: 100%;
}
.upload-bus-trans-sys .el-upload-dragger {
  width: 100%;
}
</style>
