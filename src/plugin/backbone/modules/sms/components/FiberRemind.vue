<template>
  <div style="height: 93%">
    <el-table
      ref="table"
      :data="tableData"
      :border="false"
      stripe
      @selection-change="onSelectionChange"
      :header-cell-style="{ textAlign: 'center' }"
      :cell-style="{ textAlign: 'center' }"
      height="100%"
      v-loading="tableLoading"
    >
      <el-table-column type="selection" min-width="50" :index="indexMethod" />
      <el-table-column prop="createUserName" label="规则制定人" width="100" />
      <el-table-column
        prop="createUserDept"
        label="制定人所属组织"
        width="150"
      />
      <el-table-column prop="provinceName" label="省份" width="100" />
      <el-table-column prop="refreshTime" label="规则制定时间" width="180" />
      <el-table-column prop="professionalType" label="专业" width="150" />
      <el-table-column prop="ruleComment" label="描述" width="200" />
      <el-table-column prop="createType" label="工单来源" width="200">
        <template slot-scope="scope">
          <div class="createType">
            <span v-for="(item, key) of scope.row.createType" :key="key">
              {{ handlecreateType(item) }}
            </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="ruleStatus"
        label="规则状态"
        width="100"
        align="center"
      >
        <template slot-scope="scope">
          <el-tag v-if="scope.row.ruleStatus == 1" type="success" effect="dark">
            启用
          </el-tag>
          <el-tag v-if="scope.row.ruleStatus == 0" type="info" effect="dark">
            禁用
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="customerName" label="客户名称" width="150" />
      <el-table-column
        prop="busTransSystem"
        label="客户业务所属传输系统"
        width="400"
      >
        <template v-slot="{ row }">
          <span v-if="!row.isFile">{{ row.busTransSystem }}</span>
          <el-link
            v-else
            :underline="false"
            style="color: #409eff"
            @click="onDownBusTransSystem(row.busTransSystem.id)"
            >{{ row.busTransSystem.name }}</el-link
          >
        </template>
      </el-table-column>
      <el-table-column prop="sendObject" label="通知方式" min-width="360">
        <template slot-scope="scope">
          <div class="createType">
            <div v-for="(item, key) of scope.row.sendObject" :key="key">
              {{ handleSendObject(item) }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="100" fixed="right">
        <template slot-scope="scope">
          <el-button
            @click="clickEditor(scope.row)"
            type="text"
            size="small"
            :disabled="scope.row.createUserId == createUserId ? false : true"
          >
            编辑
          </el-button>
          <el-button
            type="text"
            size="small"
            slot="reference"
            style="margin-left: 10px"
            @click="clickDelete(scope.row)"
            :disabled="scope.row.createUserId == createUserId ? false : true"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      ref="pagination"
      :total="form.total"
      :page.sync="form.pageNum"
      :limit.sync="form.pageSize"
      @change="seniorQuery"
    />
  </div>
</template>
<script>
import isObject from "lodash/isObject";

import Pagination from "./Pagination.vue";
import {
  apiFiberQueryRule,
  // apiFiberAddRule,
  apiFiberDeleteRule,
  apiFiberQueryRuleByRuleId,
  // apiFiberUpdateRule,
  apiFiberUpdateRuleStatus,
} from "../api/WorkOrderFiberRemind";
import { apiDownloadAppendixFile } from "../api/WorkOrderToRemind";
import Bus from "../../workOrder/bus";
export default {
  components: {
    Pagination,
  },
  props: {
    professionalType: String,
    customerName: {
      type: String,
      default: "",
    },
    // required: true,
  },
  data() {
    return {
      tableLoading: false,
      form: {
        createUserName: "", //规则指定人
        createUserDept: "",
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      typeList: [
        "智能监控自动",
        "智能监控手动",
        "电子运维新建",
        "电信共建共享",
      ],
      createUserId: JSON.parse(sessionStorage.userInfo).userName,
      tableData: [],
      sclectList: [],
    };
  },
  mounted() {
    this.form.createUserName = JSON.parse(sessionStorage.userInfo).realName;
    this.userData = JSON.parse(JSON.parse(sessionStorage.userInfo).attr2);
    this.form.createUserDept = this.userData.orgInfo.fullOrgName.split("-")[0];
    if (this.form.createUserDept == null || this.form.createUserDept == "") {
      this.form.createUserDept = "中国联通总部";
    }
    this.getQueryRule();
    Bus.$on("getFiberList", () => {
      this.getQueryRule();
    });
  },

  methods: {
    //工单来源
    handlecreateType(item) {
      return this.typeList[item];
    },
    //通知方式
    handleSendObject(item) {
      if (item.isSendSms == true && item.isSendEmail == true) {
        return item.name + " ( " + item.mobile + "，" + item.email + " )";
      } else if (item.isSendSms == true && item.isSendEmail == false) {
        return item.name + " ( " + item.mobile + " )";
      } else if (item.isSendSms == false && item.isSendEmail == true) {
        return item.name + " ( " + item.email + " )";
      } else {
        return "";
      }
    },
    //查询数据
    getQueryRule(type = "") {
      this.tableLoading = true;
      let seniorParam = {
        professionalType: this.professionalType,
        customerName: this.customerName,
      };
      let defaultParam = {
        professionalType: "",
        customerName: "",
      };
      let param = {
        pageIndex: this.form.pageNum,
        pageSize: this.form.pageSize,
        professionalType:
          type == "senior"
            ? seniorParam.professionalType
            : defaultParam.professionalType,
        customerName:
          type == "senior"
            ? seniorParam.customerName
            : defaultParam.customerName,
      };
      apiFiberQueryRule(param)
        .then(res => {
          if (res.status == "0") {
            let d = res?.data?.rows ?? [];
            d.forEach(el => {
              el.sendObject = JSON.parse(el.sendObject);
              try {
                const busTransSystem = JSON.parse(el.busTransSystem);
                if (isObject(busTransSystem)) {
                  el.isFile = true;
                  el.busTransSystem = busTransSystem[0];
                }
              } catch (error) {
                el.isFile = false;
              }
            });
            this.tableData = d;
            this.form.total = res?.data?.totalElements ?? 0;
          }
          this.tableLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.tableLoading = false;
        });
    },
    //批量删除
    batchDelete() {
      if (this.multipleSelection.length == 0) {
        this.$message({
          message: "请先选择规则",
          type: "warning",
        });
        return;
      }
      this.$confirm("你确定要删除吗?", "删除", {
        distinguishCancelAndClose: true,
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      }).then(() => {
        this.deleApiDeleteRule(this.multipleSelection);
      });
    },

    deleApiDeleteRule(params) {
      let param = {
        ruleId: params.join(),
      };
      apiFiberDeleteRule(param).then(res => {
        if (res.status == "0") {
          this.getQueryRule();
          this.$message({
            message: "删除成功",
            type: "success",
          });
        }
      });
    },
    //启用 禁用
    handlebatch(type) {
      if (this.multipleSelection.length == 0) {
        this.$message({
          message: "请先选择规则",
          type: "warning",
        });
        return;
      }
      let param = {
        ruleIds: this.multipleSelection,
        ruleStatus: type == "启用" ? "1" : "0",
      };
      let formData = new FormData();
      formData.append("jsonParam", JSON.stringify(param));
      apiFiberUpdateRuleStatus(formData).then(res => {
        if (res.status == "0") {
          this.getQueryRule();
          this.$message({
            message: type == "启用" ? "已启动" : "已禁用",
            type: "success",
          });
        }
      });
    },
    //查询
    seniorQuery() {
      this.getQueryRule("senior");
    },
    //多选
    onSelectionChange(val) {
      let s = [];
      if (val.length > 0) {
        val.forEach(item => {
          s.push(item.ruleId);
        });
      }
      this.multipleSelection = s;
    },
    //点击编辑
    clickEditor(scope) {
      let param = {
        ruleId: scope.ruleId,
      };
      apiFiberQueryRuleByRuleId(param).then(res => {
        if (res.status == "0") {
          let rowData = res.data.rows || {};
          Bus.$emit("fiber", rowData);
        }
      });
    },
    //点击删除
    clickDelete(scope) {
      this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let l = [];
          l[0] = scope.ruleId;
          this.deleApiDeleteRule(l);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    onDownBusTransSystem(attId) {
      const loading = this.$loading({
        lock: true,
      });
      apiDownloadAppendixFile({ attId }).finally(() => {
        loading.close();
      });
    },
    indexMethod(index) {
      const currentPage = this.form.pageNum;
      const pageSize = this.form.pageSize;
      return (currentPage - 1) * pageSize + (index + 1);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../../newIvr/assets/ivrCommon.scss";
</style>
