<template>
  <div style="height: 93%">
    <el-table
      ref="treetable"
      :data="tableData"
      :border="false"
      stripe
      @selection-change="onSelectionChange"
      :header-cell-style="{ textAlign: 'center' }"
      :cell-style="{ textAlign: 'center' }"
      height="100%"
      v-loading="tableLoading"
    >
      <el-table-column
        type="selection"
        min-width="50"
        :index="indexMethod"
        :selectable="selectDisableRoom"
      />
      <el-table-column prop="createUserName" label="规则制定人" width="100" />
      <el-table-column prop="createUserDept" label="制定人所属组织" width="150">
      </el-table-column>
      <el-table-column prop="ruleType" label="规则类型" width="100" />
      <el-table-column prop="flowType" label="流程类型" width="100" />
      <el-table-column
        prop="province"
        label="省份"
        width="100"
        show-overflow-tooltip
      />
      <el-table-column prop="professionalType" label="专业" width="150" />
      <el-table-column prop="ruleComment" label="描述" width="200" />
      <el-table-column prop="createType" label="工单来源" width="200">
        <template slot-scope="scope">
          <div class="createType">
            <span v-for="(item, key) of scope.row.createType" :key="key">
              {{ handlecreateType(item) }},
            </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="ruleStatus"
        label="规则状态"
        width="100"
        align="center"
      >
        <template slot-scope="scope">
          <el-tag v-if="scope.row.ruleStatus == 1" type="success" effect="dark">
            启用
          </el-tag>
          <el-tag v-if="scope.row.ruleStatus == 0" type="info" effect="dark">
            禁用
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="isSendAccOvertimeSoonMsg"
        label="是否发送受理即将超时短信"
        width="150"
      >
        <template slot-scope="scope">
          <span v-if="scope.row.isSendAccOvertimeSoonMsg == 0">否</span>
          <span v-else-if="scope.row.isSendAccOvertimeSoonMsg == 1">是</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="isSendRetOvertimeSoonMsg"
        label="是否发送返单即将超时短信"
        width="150"
      >
        <template slot-scope="scope">
          <span v-if="scope.row.isSendRetOvertimeSoonMsg == 0">否</span>
          <span v-else-if="scope.row.isSendRetOvertimeSoonMsg == 1">是</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="firstAccOvertimeTime"
        label="受理即将超时时间(第一次/分钟)"
        width="150"
      />
      <el-table-column
        prop="secondAccOvertimeTime"
        label="受理即将超时时间（第二次）"
        width="150"
      />
      <el-table-column
        prop="thirdAccOvertimeTime"
        label="受理即将超时时间（第三次）"
        width="150"
      />
      <el-table-column
        prop="firstRetOvertimeTime"
        label="返单即将超时时间（第一次）"
        width="150"
      />
      <el-table-column
        prop="secondRetOvertimeTime"
        label="返单即将超时时间（第二次）"
        width="150"
      />
      <el-table-column
        prop="thirdRetOvertimeTime"
        label="返单即将超时时间（第三次）"
        width="150"
      />
      <el-table-column
        prop="isSendAccOvertimeMsg"
        label="是否发送受理超时短信"
        width="150"
      >
        <template slot-scope="scope">
          <span v-if="scope.row.isSendAccOvertimeMsg == 0">否</span>
          <span v-else-if="scope.row.isSendAccOvertimeMsg == 1">是</span>
        </template>
      </el-table-column>
      <!-- <el-table-column
        prop="isSendRetOvertimeMsg"
        label="是否发送返单超时短信"
        width="150"
      >
        <template slot-scope="scope">
          <span v-if="scope.row.isSendRetOvertimeMsg == 0">否</span>
          <span v-else-if="scope.row.isSendRetOvertimeMsg == 1">是</span>
        </template>
      </el-table-column> -->

      <!-- 新增 是否发送返单即将超时短信 -->
      <el-table-column
        prop="isSendAckOvertimeMsg"
        label="是否发送阶段反馈即将超时短信"
        width="150"
      >
        <template slot-scope="scope">
          <span v-if="scope.row.isSendAckOvertimeMsg == 0">否</span>
          <span v-else-if="scope.row.isSendAckOvertimeMsg == 1">是</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" width="150" fixed="right">
        <template slot-scope="scope">
          <el-button
            @click="clickEditor(scope.row)"
            type="text"
            size="small"
            :disabled="scope.row.createUserId == createUserId ? false : true"
          >
          <!--  -->
            编辑
          </el-button>
          <!-- <el-popconfirm
          title="确定删除这条规则么?"
                @confirm="clickDelete(scope.row)"
              > -->
          <el-button
            @click="clickDelete(scope.row)"
            type="text"
            size="small"
            slot="reference"
            style="margin-left: 10px"
            :disabled="scope.row.createUserId == createUserId ? false : true"
          >
            删除
          </el-button>
          <!-- </el-popconfirm> -->
        </template>
      </el-table-column>
    </el-table>
    <pagination
      ref="pagination"
      :total="form.total"
      :page.sync="form.pageNum"
      :limit.sync="form.pageSize"
      @change="seniorQuery"
    />
  </div>
</template>

<script>
import Pagination from "./Pagination.vue";
import {
  apiQueryRule,
  apiAddRule,
  apiDeleteRule,
  apiQueryRuleByRuleId,
  apiupdateRule,
  apiupdateRuleStatus,
} from "../api/WorkOrderToRemind";
import GeneralAdd from "./GeneralAdd.vue";
import Bus from "../../workOrder/bus";
export default {
  components: {
    Pagination,
    GeneralAdd,
  },
  props: {
    professionalType: String,
    province: {
      type: String,
      default: "",
    },
    flowType: String,
    required: true,
  },
  data() {
    return {
      tableLoading: false,
      form: {
        createUserName: "", //规则指定人
        createUserDept: "",
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      typeList: [
        "智能监控自动",
        "智能监控手动",
        "电子运维新建",
        "电信共建共享",
      ],
      createUserId: JSON.parse(sessionStorage.userInfo).userName,
      tableData: [],
      sclectList: [],
    };
  },
  mounted() {
    // this.form.createUserName = JSON.parse(sessionStorage.userInfo).realName;
    // this.form.createUserDept = "中国联通总部";
    this.getQueryRule();
    Bus.$on("getGeneralList", () => {
      this.getQueryRule();
    });
  },

  methods: {
    //工单来源
    handlecreateType(item) {
      return this.typeList[item];
    },
    //查询数据
    getQueryRule(type = "") {
      this.tableLoading = true;
      let seniorParam = {
        professionalType: this.professionalType,
        province: this.province,
        flowType: this.flowType,
      };
      let defaultParam = {
        professionalType: "",
        province: "",
        flowType: "",
      };
      let param = {
        pageIndex: this.form.pageNum,
        pageSize: this.form.pageSize,
        param1:
          type == "senior"
            ? JSON.stringify(seniorParam)
            : JSON.stringify(defaultParam),
      };
      apiQueryRule(param)
        .then(res => {
          if (res.status == "0") {
            this.tableData = res?.data?.rows ?? [];
            this.form.total = res?.data?.totalElements ?? 0;
            this.$nextTick(() => this.$refs.treetable.doLayout());
            console.log(res, "===========工单提醒");
          }
          this.tableLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.tableLoading = false;
        });
    },
    //批量删除
    batchDelete() {
      if (this.multipleSelection.length == 0) {
        this.$message({
          message: "请先选择规则",
          type: "warning",
        });
        return;
      }
      this.$confirm("你确定要删除吗?", "删除", {
        distinguishCancelAndClose: true,
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      }).then(() => {
        this.deleApiDeleteRule(this.multipleSelection);
      });
    },

    deleApiDeleteRule(params) {
      let param = {
        ruleIds: params,
      };
      let param1 = JSON.stringify(param);
      apiDeleteRule(param1).then(res => {
        if (res.status == "0") {
          this.getQueryRule();
          this.$message({
            message: "删除成功",
            type: "success",
          });
        }
      });
    },
    //启用 禁用
    handlebatch(type) {
      if (this.multipleSelection.length == 0) {
        this.$message({
          message: "请先选择规则",
          type: "warning",
        });
        return;
      }
      let seniorParam = {
        ruleIds: this.multipleSelection,
        ruleStatus: type == "启用" ? 1 : 0,
      };
      let param = {
        param1: JSON.stringify(seniorParam),
      };
      apiupdateRuleStatus(param).then(res => {
        if (res.status == "0") {
          this.getQueryRule();
          this.$message({
            message: type == "启用" ? "已启动" : "已禁用",
            type: "success",
          });
        }
      });
    },
    //查询
    seniorQuery() {
      this.getQueryRule("senior");
    },
    selectDisableRoom(row) {
      if (row.createUserId == this.createUserId) {
        return true;
      } else {
        return false;
      }
    },
    //多选
    onSelectionChange(val) {
      let s = [];
      if (val.length > 0) {
        val.forEach(item => {
          s.push(item.ruleId);
        });
      }
      this.multipleSelection = s;
    },
    //点击编辑
    clickEditor(scope) {
      // let ruleId = scope.ruleId;
      // this.$refs.generalAdd.clickEditor(ruleId);
      let param = {
        ruleId: scope.ruleId,
      };
      apiQueryRuleByRuleId(param).then(res => {
        if (res.status == "0") {
          let rowData = res.data.rows || {};
          Bus.$emit("general", rowData);
        }
      });
    },
    //点击删除
    clickDelete(scope) {
      this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let l = [];
          l[0] = scope.ruleId;
          this.deleApiDeleteRule(l);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },

    indexMethod(index) {
      const currentPage = this.form.pageNum;
      const pageSize = this.form.pageSize;
      return (currentPage - 1) * pageSize + (index + 1);
    },
  },
};
</script>
<style lang="scss" scoped>
@import "../../newIvr/assets/ivrCommon.scss";
</style>
