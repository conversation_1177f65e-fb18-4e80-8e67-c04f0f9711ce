<template>
  <el-dialog :title="generalTitle" :visible="generalVisible" :close-on-click-modal="false" :before-close="handleClose"
    width="500px">
    <el-form ref="form" :model="form" :rules="rules" label-width="180px">
      <el-form-item label="规则制定人:" prop="createUserName">
        <el-input :disabled="true" v-model="form.createUserName" maxlength="25"></el-input>
      </el-form-item>
      <el-form-item label="制订人所属组织:" prop="createUserDept">
        <el-input disabled v-model="form.createUserDept" maxlength="25"></el-input>
      </el-form-item>
      <el-form-item label="规则类型" prop="ruleType">
        <el-input disabled v-model="form.ruleType" maxlength="25"></el-input>
      </el-form-item>
      <!-- <el-form-item
        label="流程类型"
        prop="flowType"
        :rules="{
          required: true,
          message: '流程类型不可以为空！',
        }"
      >
        <dict-select
          :value.sync="form.flowType"
          :dictId="form.ruleType == '集团规则' ? 10203 : 10204"
          placeholder="请选择流程类型"
          style="width: 240px"
          @change="changeFlowType"
        />
      </el-form-item> -->
      <el-form-item label="流程类型" prop="flowType" :rules="{
        required: true,
        message: '流程类型不可以为空！',
      }">
        <dict-select :value.sync="form.flowType" :dictId="form.ruleType == '集团规则' || form.ruleType == '大区规则'
            ? 10203
            : 10204
          " placeholder="请选择流程类型" style="width: 240px" @change="changeFlowType" />
      </el-form-item>

      <el-form-item label="专业:" prop="professionalType" :rules="{
        required: true,
        message: '专业不可以为空！',
      }">
        <dict-select :value.sync="form.professionalType" :dictId="professionalDictId" placeholder="请选择专业"
          style="width: 240px" @change="changeProfessionalType" />
      </el-form-item>
      <template v-if="form.createUserDept == '中国联通总部'">
        <el-form-item label="省份:" prop="provinces" :rules="{
          required: true,
          message: '省份不可以为空',
        }">
          <el-select v-model="form.provinces" placeholder="请选择省份" multiple collapse-tags style="width: 240px">
            <el-option v-for="(item, i) in dictData" :key="i" :label="item.dictName" :value="item.dictCode">
            </el-option>
          </el-select>
        </el-form-item>
      </template>
      <template v-else>
        <el-form-item label="省份:" prop="province" :rules="{
          required: true,
          message: '省份不可以为空',
        }">
          <dict-select :notSelect="true" :value.sync="form.province" :dictId="form.ruleType == '大区规则' ? 10210 : 10211"
            placeholder="请选择省份" style="width: 240px" />
        </el-form-item>
      </template>
      <el-form-item label="描述:" prop="ruleComment">
        <el-input v-model="form.ruleComment" maxlength="25"></el-input>
      </el-form-item>

      <el-form-item label="工单来源:" prop="createType">
        <el-checkbox-group v-model="form.createType">
          <div style="display: flex">
            <el-checkbox label="电子运维新建" name="type"></el-checkbox>
            <el-checkbox label="智能监控自动" name="type"></el-checkbox>
          </div>
          <div style="display: flex">
            <el-checkbox label="智能监控手动" name="type"></el-checkbox>
            <el-checkbox label="电信共建共享" name="type"></el-checkbox>
          </div>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="是否发送受理即将超时短信:" prop="isSendAccOvertimeSoonMsg">
        <el-radio-group @change="isSendAccOvertimeSoonMsgClcik" v-model="form.isSendAccOvertimeSoonMsg">
          <el-radio label="是"></el-radio>
          <el-radio label="否"></el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="受理即将超时(第一次):" prop="firstAccOvertimeTime">
        <el-input v-model="form.firstAccOvertimeTime"></el-input>
        <span class="item__unit">分钟</span>
      </el-form-item>
      <el-form-item label="受理即将超时时间(第二次):" prop="secondAccOvertimeTime">
        <el-input v-model="form.secondAccOvertimeTime"></el-input>
        <span class="item__unit">分钟</span>
      </el-form-item>
      <el-form-item label="受理即将超时时间(第三次):" prop="thirdAccOvertimeTime">
        <el-input v-model="form.thirdAccOvertimeTime"></el-input>
        <span class="item__unit">分钟</span>
      </el-form-item>
      <el-form-item label="是否发送返单即将超时短信:" prop="isSendRetOvertimeSoonMsg">
        <el-radio-group :disabled="(form.professionalType == 3 ||
            form.professionalType == 7 ||
            form.professionalTypeCode == 3 ||
            form.professionalTypeCode == 7) &&
          (form.flowType == 1 || form.flowType == 9)
          " @change="isSendRetOvertimeSoonMsgClick" v-model="form.isSendRetOvertimeSoonMsg">
          <el-radio label="是"></el-radio>
          <el-radio label="否"></el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="返单即将超时时间(第一次):" prop="firstRetOvertimeTime">
        <el-input :disabled="(form.professionalType == 3 ||
            form.professionalType == 7 ||
            form.professionalTypeCode == 3 ||
            form.professionalTypeCode == 7) &&
          (form.flowType == 1 || form.flowType == 9)
          " v-model="form.firstRetOvertimeTime"></el-input>
        <span class="item__unit">分钟</span>
      </el-form-item>
      <el-form-item label="返单即将超时时间(第二次):" prop="secondRetOvertimeTime">
        <el-input :disabled="(form.professionalType == 3 ||
            form.professionalType == 7 ||
            form.professionalTypeCode == 3 ||
            form.professionalTypeCode == 7) &&
          (form.flowType == 1 || form.flowType == 9)
          " v-model="form.secondRetOvertimeTime"></el-input>
        <span class="item__unit">分钟</span>
      </el-form-item>
      <el-form-item label="返单即将超时时间(第三次):" prop="thirdRetOvertimeTime">
        <el-input :disabled="(form.professionalType == 3 ||
            form.professionalType == 7 ||
            form.professionalTypeCode == 3 ||
            form.professionalTypeCode == 7) &&
          (form.flowType == 1 || form.flowType == 9)
          " v-model="form.thirdRetOvertimeTime"></el-input>
        <span class="item__unit">分钟</span>
      </el-form-item>
      <el-form-item label="是否发送受理超时短信:" prop="isSendAccOvertimeMsg">
        <el-radio-group @change="isSendAccOvertimeMsgClick" v-model="form.isSendAccOvertimeMsg">
          <el-radio label="是"></el-radio>
          <el-radio label="否"></el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="是否发送返单超时短信:" prop="isSendRetOvertimeMsg">
        <el-radio-group :disabled="(form.professionalType == 3 ||
            form.professionalType == 7 ||
            form.professionalTypeCode == 3 ||
            form.professionalTypeCode == 7) &&
          (form.flowType == 1 || form.flowType == 9)
          " @change="isSendRetOvertimeMsgClick" v-model="form.isSendRetOvertimeMsg">
          <el-radio label="是"></el-radio>
          <el-radio label="否"></el-radio>
        </el-radio-group>
      </el-form-item>
      <!-- 新增加 是否发送阶段反馈即将超时短信 -->

      <!-- label="是否发送阶段反馈即将超时短信:" -->
      <el-form-item prop="isSendAckOvertimeMsg">
        <template #label>
          <!-- content="短信开启后，根据阶段反馈考核要求，计算每阶段考核时间点。若在该阶段考核时间点到达前10分钟仍未进行阶段反馈，则会给工单当前处理人发送即将超时提醒短信。" -->
          <el-tooltip class="custom-tooltip" effect="dark" placement="top">
            <div slot="content" style="width: 23rem">
              短信开启后，根据阶段反馈考核要求，计算每阶段考核时间点。若在该阶段考核时间点到达前10分钟仍未进行阶段反馈，则会给工单当前处理人发送即将超时提醒短信。
            </div>
            <i style="
                margin-right: 0.3rem;
                cursor: pointer;
                display: inline-block;
              " class="el-icon-question"></i>
          </el-tooltip>
          <span>是否发送阶段反馈即将超时短信：</span>
        </template>
        <el-radio-group :disabled="false" @change="isSendStageFeedbackOvertimeMsgClick"
          v-model="form.isSendAckOvertimeMsg">
          <el-radio label="是"></el-radio>
          <el-radio label="否"></el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="_save"> 确 定 </el-button>
      <el-button @click="_cancel">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { apiDict } from "../api/CommonApi";
import DictSelect from "../../workOrder/components/DictSelect.vue";
import HeadContentLayout from "../../newIvr/components/HeadContentLayout.vue";
import Pagination from "./Pagination.vue";
import Bus from "../../workOrder/bus";
import {
  apiQueryRule,
  apiAddRule,
  apiupdateRule,
} from "../api/WorkOrderToRemind";
export default {
  name: "GeneralAdd",
  components: {
    DictSelect,
    HeadContentLayout,
    Pagination,
  },
  props: {
    generalVisible: {
      type: Boolean,
      default: false,
    },
    generalTitle: {
      type: String,
      default: "新增",
    },
    required: true,
  },
  data() {
    const checkAge = (rule, value, callback) => {
      const regex = new RegExp("^-?[0-9]\\d*$");
      if (value == "") {
        callback();
      } else if (!regex.test(value)) {
        callback(new Error("请输入数字"));
      } else {
        callback();
      }
    };
    const checkRetAge = (rule, value, callback) => {
      const regex = new RegExp("^-?[0-9]\\d*$");
      if (value == "") {
        callback();
      } else if (!regex.test(value)) {
        callback(new Error("请输入数字"));
      } else {
        callback();
      }
    };
    return {
      CprofessionalType: "", //专业
      province: "", //省份
      tableLoading: false,
      tableData: [],
      form: {
        createUserName: "", //规则指定人
        createUserDept: "",
        province: "",
        provinces: [],
        professionalType: "",
        professionalTypeCode: "",
        ruleComment: "",
        createType: [],
        ruleStatus: "",
        ruleId: "",
        isSendAccOvertimeSoonMsg: "否",
        isSendRetOvertimeSoonMsg: "否",
        firstAccOvertimeTime: "",
        secondAccOvertimeTime: "",
        thirdAccOvertimeTime: "",
        firstRetOvertimeTime: "",
        secondRetOvertimeTime: "",
        thirdRetOvertimeTime: "",
        isSendAccOvertimeMsg: "否",
        isSendRetOvertimeMsg: "否",
        isSendAckOvertimeMsg: "否",
        pageNum: 1,
        pageSize: 10,
        total: 0,
        flowType: "",
        ruleType: "",
      },
      typeList: [
        "智能监控自动",
        "智能监控手动",
        "电子运维新建",
        "电信共建共享",
      ],
      multipleSelection: [],
      userInfo: {},
      userInfoAttr2: {},
      dictData: [],
      professionalDictId: "10002",
      createUserId: JSON.parse(sessionStorage.userInfo).userName,

      rules: {
        province: [
          { required: true, message: "请选择省份", trigger: "change" },
        ],
        professionalType: [
          { required: true, message: "请选择专业", trigger: "change" },
        ],
        createType: [
          { required: true, message: "请选择工单来源", trigger: "change" },
        ],
        firstAccOvertimeTime: [{ validator: checkAge, trigger: "blur" }],
        secondAccOvertimeTime: [{ validator: checkAge, trigger: "blur" }],
        thirdAccOvertimeTime: [{ validator: checkAge, trigger: "blur" }],
        firstRetOvertimeTime: [{ validator: checkRetAge, trigger: "blur" }],
        secondRetOvertimeTime: [{ validator: checkRetAge, trigger: "blur" }],
        thirdRetOvertimeTime: [{ validator: checkRetAge, trigger: "blur" }],
      },
      options: [{ value: "中国联通总部", label: "中国联通总部" }],
    };
  },
  mounted() {
    if (sessionStorage.userInfo != null) {
      this.userInfo = JSON.parse(sessionStorage.userInfo);
      this.form.createUserName = this.userInfo.realName;

      if (this.userInfo.attr2 != null) {
        this.userInfoAttr2 = JSON.parse(this.userInfo.attr2);
        // this.locationData = JSON.parse(this.userData.attr2);
        if (this.userInfoAttr2.dqInfo != null) {
          if (this.userInfoAttr2.dqInfo.orgType === "DAQU") {
            this.form.ruleType = "大区规则";
            this.form.province = this.userInfoAttr2.dqInfo.orgCode + "";
            this.form.createUserDept = this.userInfoAttr2.dqInfo.orgName;
          }
        } else {
          if (this.userInfoAttr2.category === "UNI") {
            this.form.ruleType = "集团规则";
            this.form.createUserDept =
              this.userInfoAttr2.orgInfo.fullOrgName.split("-")[0];
          } else {
            this.form.ruleType = "省分规则";
            this.form.province = this.userInfoAttr2.orgInfo.proCode + "";
            this.form.createUserDept = this.userInfoAttr2.orgInfo.orgName;
          }
        }

        // if(this.form.createUserDept == "中国联通总部"){
        //   this.form.ruleType = "集团规则";
        // }else{
        //   this.form.ruleType = "省分规则";
        //   this.form.province = this.locationData.orgInfo.proCode + "";
        // }
      } else {
        console.log("缺少用户信息");
      }
      // this.form.createUserDept = this.userInfoAttr2.orgInfo.fullOrgName.split("-")[0];
      // if(this.form.createUserDept == "中国联通总部"){
      //   this.form.ruleType = "集团规则";
      // }else{
      //   this.form.ruleType = "省分规则";
      //   this.form.province = this.userInfoAttr2.orgInfo.proCode + "";
      // }
    }
    Bus.$on("general", rows => {
      this.clickEditor(rows);
    });
  },

  methods: {
    //工单来源
    handlecreateType(item) {
      return this.typeList[item];
    },
    getDictData(value) {
      let param = {
        dictTypeCode: value,
      };
      apiDict(param)
        .then(res => {
          if (res.code == 200) {
            this.dictData = res?.data ?? [];
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    changeFlowType(type) {
      if (type != "edit") {
        this.form.professionalType = "";
        this.form.provinces = [];
      }
      if (this.form.flowType == "4") {
        this.professionalDictId = "10205";
      } else if (this.form.flowType == "1") {
        this.professionalDictId = "10206";
      } else if (this.form.flowType == "2") {
        this.professionalDictId = "10207";
      } else if (this.form.flowType == "3") {
        this.professionalDictId = "10208";
      } else if (this.form.flowType == "7" || this.form.flowType == "9") {
        this.professionalDictId = "10209";
      } else if (this.form.flowType == "8") {
        this.professionalDictId = "60012";
      } else if (this.form.flowType == "10") {
        this.professionalDictId = "60009";
      } else if (this.form.flowType == "11") {
        this.professionalDictId = "60010";
      } else if (this.form.flowType == "12") {
        this.professionalDictId = "60011";
      } else if (this.form.flowType == "") {
        this.professionalDictId = "10002";
      }
    },
    changeProfessionalType(type) {
      if (type != "edit") {
        this.form.provinces = [];
      }
      if (
        this.form.createUserDept == "中国联通总部" &&
        (this.form.professionalType == "19" ||
          this.form.professionalType == "20" ||
          this.form.professionalType == "12" ||
          this.form.professionalType == "13" ||
          this.form.professionalType == "22" ||
          this.form.professionalType == "25")
      ) {
        this.getDictData("10210");
      } else if (
        this.form.createUserDept == "中国联通总部" &&
        this.form.professionalType == "23"
      ) {
        this.dictData = [{ dictCode: "-1", dictName: "联通系统集成有限公司" }];
      } else if (this.form.createUserDept == "中国联通总部") {
        this.getDictData("10211");
      }
    },
    //查询数据
    getQueryRule(type = "") {
      this.tableLoading = true;
      let seniorParam = {
        professionalType: this.CprofessionalType,
        province: this.province,
      };
      let defaultParam = {
        professionalType: "",
        province: "",
      };
      let param = {
        pageIndex: this.form.pageNum,
        pageSize: this.form.pageSize,
        param1:
          type == "senior"
            ? JSON.stringify(seniorParam)
            : JSON.stringify(defaultParam),
      };
      apiQueryRule(param)
        .then(res => {
          if (res.status == "0") {
            this.tableData = res?.data?.rows ?? [];
            this.form.total = res?.data?.totalElements ?? 0;
            console.log(res, "===========工单提醒");
          }
          this.tableLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.tableLoading = false;
        });
    },

    //点击编辑
    clickEditor(rows) {
      if (rows.length > 0) {
        const arrData = rows;
        const {
          createUserName,
          province,
          provinces,
          ruleType,
          flowType,
          professionalType,
          professionalTypeCode,
          ruleComment,
          createType,
          isSendAccOvertimeSoonMsg,
          firstAccOvertimeTime,
          secondAccOvertimeTime,
          thirdAccOvertimeTime,
          isSendAccOvertimeMsg,
          isSendRetOvertimeSoonMsg,
          firstRetOvertimeTime,
          secondRetOvertimeTime,
          thirdRetOvertimeTime,
          isSendRetOvertimeMsg,
          isSendAckOvertimeMsg,
          ruleId,
          createUserDept,
        } = arrData[0];
        console.log(isSendAckOvertimeMsg);
        const l = [];
        createType.forEach(el => {
          if (el == "0") {
            l.push("智能监控自动");
          }
          if (el == "1") {
            l.push("智能监控手动");
          }
          if (el == "2") {
            l.push("电子运维新建");
          }
          if (el == "3") {
            l.push("电信共建共享");
          }
        });
        this.form.ruleId = ruleId;
        this.form.createUserDept = createUserDept;
        this.form.createUserName = createUserName; //规则制定人
        this.form.province = province; //省份
        this.form.provinces = provinces;
        this.form.flowType = flowType;
        this.form.ruleType = ruleType;
        this.form.professionalType = professionalType + ""; //专业s
        this.form.professionalTypeCode = professionalTypeCode;
        this.form.ruleComment = ruleComment; //描述
        this.form.createType = l; //来源
        this.form.isSendAccOvertimeSoonMsg =
          isSendAccOvertimeSoonMsg == 0 ? "否" : "是";
        this.form.firstAccOvertimeTime =
          firstAccOvertimeTime == null ? "" : firstAccOvertimeTime;
        this.form.secondAccOvertimeTime =
          secondAccOvertimeTime == null ? "" : secondAccOvertimeTime;
        this.form.thirdAccOvertimeTime =
          thirdAccOvertimeTime == null ? "" : thirdAccOvertimeTime;
        this.form.isSendAccOvertimeMsg =
          isSendAccOvertimeMsg == 0 ? "否" : "是";
        this.form.isSendRetOvertimeSoonMsg =
          isSendRetOvertimeSoonMsg == 0 ? "否" : "是";
        this.form.firstRetOvertimeTime =
          firstRetOvertimeTime == null ? "" : firstRetOvertimeTime;
        this.form.secondRetOvertimeTime =
          secondRetOvertimeTime == null ? "" : secondRetOvertimeTime;
        this.form.thirdRetOvertimeTime =
          thirdRetOvertimeTime == null ? "" : thirdRetOvertimeTime;
        this.form.isSendRetOvertimeMsg =
          isSendRetOvertimeMsg == 0 ? "否" : "是";
        this.form.isSendAckOvertimeMsg =
          isSendAckOvertimeMsg == '0' ? "否" : "是";
      }
      this.changeFlowType("edit");
      this.changeProfessionalType("edit");
      this.$emit("update:generalTitle", "编辑");
      this.$emit("update:generalVisible", true);
    },
    //dialog确定
    _save() {
      this.$refs.form.validate(valid => {
        if (valid) {
          if (
            (this.form.province == "" && this.form.provinces.length == 0) ||
            this.form.professionalType == "" ||
            this.form.createType.length == 0 ||
            this.form.flowType == ""
          ) {
            this.$message({
              message: "选择流程类型 专业 省份 工单来源 才能新增",
              type: "warning",
            });
            return;
          }
          if (
            this.form.isSendAccOvertimeSoonMsg == "是" &&
            this.form.firstAccOvertimeTime == ""
          ) {
            this.$message({
              message: "当选择发送短信必须设置时间",
              type: "warning",
            });
            return;
          }
          if (
            this.form.isSendRetOvertimeSoonMsg == "是" &&
            this.form.firstRetOvertimeTime == ""
          ) {
            this.$message({
              message: "当选择发送短信必须设置时间",
              type: "warning",
            });
            return;
          }
          let l = [];
          this.form.createType.forEach(el => {
            if (el == "智能监控自动") {
              l.push(0);
            }
            if (el == "智能监控手动") {
              l.push(1);
            }
            if (el == "电子运维新建") {
              l.push(2);
            }
            if (el == "电信共建共享") {
              l.push(3);
            }
            // if (el == "电子运维新建") {
            //   l.push(3);
            // }
            // if (el == "智能监控自动") {
            //   l.push(0);
            // }
            // if (el == "智能监控手动") {
            //   l.push(1);
            // }
            // if (el == "电信共建共享") {
            //   l.push(4);
            // }
          });
          if (this.form.provinces.length == 0) {
            this.form.provinces.push(this.form.province);
          }
          let seniorParam = {
            createUserNameId: this.createUserId,
            createUserName: this.form.createUserName,
            createUserDept: this.form.createUserDept,
            province: this.form.province,
            provinces: this.form.provinces,
            flowType: this.form.flowType,
            ruleType: this.form.ruleType,
            professionalType: this.form.professionalType,
            ruleComment: this.form.ruleComment,
            createType: l,
            isSendAccOvertimeSoonMsg:
              this.form.isSendAccOvertimeSoonMsg == "否" ? 0 : 1,
            isSendRetOvertimeSoonMsg:
              this.form.isSendRetOvertimeSoonMsg == "否" ? 0 : 1,
            firstAccOvertimeTime: this.form.firstAccOvertimeTime,
            secondAccOvertimeTime: this.form.secondAccOvertimeTime,
            thirdAccOvertimeTime: this.form.thirdAccOvertimeTime,
            firstRetOvertimeTime: this.form.firstRetOvertimeTime,
            secondRetOvertimeTime: this.form.secondRetOvertimeTime,
            thirdRetOvertimeTime: this.form.thirdRetOvertimeTime,
            isSendAccOvertimeMsg:
              this.form.isSendAccOvertimeMsg == "否" ? 0 : 1,
            isSendRetOvertimeMsg:
              this.form.isSendRetOvertimeMsg == "否" ? 0 : 1,
            isSendAckOvertimeMsg:
              this.form.isSendAckOvertimeMsg == "否" ? 0 : 1,
            ruleId: this.form.ruleId,
          };
          console.log(seniorParam.isSendAckOvertimeMsg)
          let param = {
            param1: JSON.stringify(seniorParam),
          };
          if (this.generalTitle == "新增") {
            apiAddRule(param)
              .then(res => {
                if (res.status == "0") {
                  this.$emit("update:generalVisible", false);
                  this.getQueryRule();
                  this.$message({
                    message: "添加成功",
                    type: "success",
                  });
                  this.$refs["form"].resetFields();
                  Bus.$emit("getGeneralList");
                } else if (res.status == "200") {
                  this.$message.warning("此规则已存在");
                } else {
                  this.$message.error("添加失败");
                }
              })
              .catch(error => {
                if (error.status == "400") {
                  this.$message.error(error.msg);
                } else {
                  this.$message.error("添加失败");
                }
              });
          } else {
            apiupdateRule(param).then(res => {
              if (res.status == "0") {
                this.$emit("update:generalVisible", false);
                this.getQueryRule();
                this.$message({
                  message: "修改成功",
                  type: "success",
                });
                this.$refs["form"].resetFields();
                Bus.$emit("getGeneralList");
              } else if (res.status == "200") {
                this.$message.warning("此规则已存在");
              }
            });
          }
        } else {
          return false;
        }
      });
    },
    //dialog取消
    _cancel() {
      this.$emit("update:generalVisible", false);
      this.$refs["form"].resetFields();
    },
    //点击叉号
    handleClose() {
      this.$emit("update:generalVisible", false);
      this.$refs["form"].resetFields();
    },
    //是否发送短信
    isSendAccOvertimeSoonMsgClcik(label) {
      this.form.isSendAccOvertimeSoonMsg = label;
    },
    isSendRetOvertimeSoonMsgClick(label) {
      this.form.isSendRetOvertimeSoonMsg = label;
    },
    isSendAccOvertimeMsgClick(label) {
      this.form.isSendAccOvertimeMsg = label;
    },
    isSendRetOvertimeMsgClick(label) {
      this.form.isSendRetOvertimeMsg = label;
    },
    isSendStageFeedbackOvertimeMsgClick(label) {
      this.form.isSendAckOvertimeMsg = label;
    },
    indexMethod(index) {
      const currentPage = this.form.pageNum;
      const pageSize = this.form.pageSize;
      return (currentPage - 1) * pageSize + (index + 1);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../../newIvr/assets/ivrCommon.scss";

.order__menuList {
  margin-top: 10px;
  padding-bottom: 10px;
}

::v-deep .el-dialog__body {
  padding: 0;
  padding-right: 50px;
  padding-left: 30px;
}

::v-deep .el-form-item__content {
  position: relative;

  .item__unit {
    position: absolute;
    display: block;
    top: 0;
    left: 106px;
    color: #dcdfe6;
  }
}

::v-deep .el-dialog__wrapper {
  .el-dialog {
    margin-top: 6vh !important;

    .el-dialog__body {
      .el-form {

        .el-form-item,
        .el-form-item--mini {
          margin: 0;
          margin-bottom: 14px;
        }
      }
    }
  }
}
</style>
