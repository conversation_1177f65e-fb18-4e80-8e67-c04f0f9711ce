import { getJson, postJson } from "@/utils/axios";

const applyItCloudManualClearurl =
  "backbone/workflow/it/cloud/applyItCloudManualClear"; //告警详情清除申请
const checkItCloudManualClearurl =
  "backbone/workflow/it/cloud/checkItCloudManualClear"; //告警详情清除申请

const apiApplyItCloudManualClear = params =>
  postJson(applyItCloudManualClearurl, params);

const apicheckItCloudManualClear = params =>
  postJson(checkItCloudManualClearurl, params);

export { apiApplyItCloudManualClear, apicheckItCloudManualClear };
