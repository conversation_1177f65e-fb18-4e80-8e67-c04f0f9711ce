import { getJson, postFormData } from "@/utils/axios";
const faultAreaUrl = "backbone/info/faultArea"; //故障发生地区字典数据
const getEvaluationUrl = "/backbone/workflow/getEvaluation"; //是否已评价

const itCloudBackSingleUrl =
  "backbone/workflow/itcloudAckDefine/itcloudFeedback"; //IT云返单

const apiGetFaultArea = params => getJson(faultAreaUrl, params);
const apiItCloudBackSingle = params =>
  postFormData(itCloudBackSingleUrl, params);
const apiGetEvaluation = params => getJson(getEvaluationUrl, params);

export { apiGetFaultArea, apiItCloudBackSingle, apiGetEvaluation };
