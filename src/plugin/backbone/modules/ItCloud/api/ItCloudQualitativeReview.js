import { getJson, postFormData } from "@/utils/axios";

const qualitativeDetailUrl = "backbone/workflow/itcloudAckDefine/itcloudDetail"; //IT云 定性审核初始化数据
const qualitativeReviewUrl =
  "backbone/workflow/itcloudAckDefine/itcloudDefineCheck"; //IT云 定性审核

const apiQualitativeDetail = params => getJson(qualitativeDetailUrl, params);
const apiQualitativeReview = params =>
  postFormData(qualitativeReviewUrl, params);

export { apiQualitativeDetail, apiQualitativeReview };
