<template>
  <el-card shadow="always" class="card" :body-style="{ padding: '10px 24px' }">
    <div class="header clearfix">
      <div class="header-tabMenu">
        <span class="header-title" style="margin-top: 5px; margin-right: 10px">反馈单详情</span>
        <div style="display: inline-block">
          <el-radio-group v-model="radio1" @change="handleClick" size="mini">
            <el-radio-button v-for="(tab, key) in tabMenu" :key="key" :label="key">
              {{ tab.trim() }}
            </el-radio-button>
          </el-radio-group>
        </div>
      </div>
    </div>
    <div class="content">
      <el-descriptions title="故障定性信息" class="descriptions">
        <template>
          <el-descriptions-item label="故障所属专业">{{
            list.professionalType
            }}</el-descriptions-item>
          <el-descriptions-item label="故障发生时间">{{
            list.alarmCreateTime
            }}</el-descriptions-item>
          <el-descriptions-item label="故障通知时间">
            {{ list.sheetCreateTime }}
          </el-descriptions-item>
          <el-descriptions-item label="业务恢复时间">{{
            list.busRecoverTime
            }}</el-descriptions-item>
          <el-descriptions-item label="业务恢复历时">{{
            busRecoverDuration
            }}</el-descriptions-item>
          <el-descriptions-item label="故障结束时间">
            {{ list.faultEndTime }}</el-descriptions-item>
          <el-descriptions-item label="故障处理历时">
            {{ troubleshootingDuration }}
          </el-descriptions-item>
          <el-descriptions-item label="故障发生地区">
            {{ list.faultRegion }}
          </el-descriptions-item>
          <el-descriptions-item label="故障处理部门">
            {{ list.dept }}
          </el-descriptions-item>
          <el-descriptions-item label="处理人">{{
            list.person
            }}</el-descriptions-item>
          <el-descriptions-item label="挂起历时">{{
            suspendDuration
            }}</el-descriptions-item>
          <el-descriptions-item label="故障处理净历时">
            {{ processDuration }}
          </el-descriptions-item>
          <el-descriptions-item label="是否影响业务">{{
            list.isEffectBusiness
            }}</el-descriptions-item>
          <el-descriptions-item label="影响范围" v-if="list.isEffectBusiness == '是'" :span="2">{{ list.effectRange }}
          </el-descriptions-item>
        </template>
      </el-descriptions>
      <el-descriptions title="故障专业信息" class="descriptions">
        <el-descriptions-item label="故障分类">
          {{ list.faultCate }}
        </el-descriptions-item>
        <el-descriptions-item label="故障原因">
          {{ list.faultReason }}</el-descriptions-item>
        <el-descriptions-item label="设备类型">
          {{ list.eqpType }}
        </el-descriptions-item>
        <el-descriptions-item label="设备名称">
          {{ list.eqpName }}
        </el-descriptions-item>
        <el-descriptions-item label="附件">
          <el-tag v-for="(item, index) in attachmentArr" class="fileName_style" :key="index"
            @click="previewAppendixFile(item)" v-loading.fullscreen.lock="appendixFileLoading" :title="item.name">
            <div class="text-truncate">{{ item.name }}</div>
          </el-tag>
          <span v-if="attachmentArr.length <= 0">无</span>
        </el-descriptions-item>
        <el-descriptions-item label="是否硬件故障">
          {{ list.isHardFault == 0 ? "否" : "是" }}
        </el-descriptions-item>
        <el-descriptions-item label="故障原因描述" :span="6">
          {{ list.faultReasonDesc }}
        </el-descriptions-item>
        <br />
        <el-descriptions-item label="备注" :span="6">
          {{ list.faultComment }}
        </el-descriptions-item>
      </el-descriptions>
      <el-descriptions title="故障定性审核信息" v-if="list.auditResult != null && list.auditResult != ''" class="descriptions">
        <el-descriptions-item label="审批结果">
          <span v-if="list.auditResult == 0">拒绝</span><span v-else-if="list.auditResult == 1">同意</span>
        </el-descriptions-item>
        <el-descriptions-item label="是否上传故障报告">
          {{ list.isUploadReport }}</el-descriptions-item>
        <el-descriptions-item label="是否故障处理结束">
          {{ list.isExeOver }}</el-descriptions-item>
        <el-descriptions-item label="审批意见">
          {{ list.auditContent }}</el-descriptions-item>
      </el-descriptions>
    </div>
    <el-dialog title="定性审核" :visible.sync="dialogQualitativeReviewVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="qualitativeReviewClose" :fullscreen="false" width="83%" top="5vh">
      <qualitative-review ref="qualitativeReview" :common="common" :workItemId="workItemId" v-if="showDxsh"
        @qualitativeReviewSubmit="qualitativeReviewSubmit"></qualitative-review>
    </el-dialog>

    <el-dialog width="420px" title="文件下载" :visible.sync="attachmentVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" append-to-body>
      <file-download @cancel="attachmentVisible = false" :attachmentArr="attachmentArr"></file-download>
    </el-dialog>

    <!-- 使用图片预览组件 -->
    <image-preview :visible.sync="imagePreviewVisible" :file-data="currentPreviewFile"
      preview-api="/commonDict/attach/download" :use-custom-download="true" @download="downloadCurrentFile"
      @close="imagePreviewVisible = false"></image-preview>
  </el-card>
</template>

<script>
import QualitativeReview from "./ItCloudQualitativeReview.vue";
import FileDownload from "../../workOrder/components/FileDownload.vue";
import ImagePreview from "@plugin/backbone/components/ImagePreview.vue";
import { apiDownloadAppendixFile } from "../../workOrder/workOrderWaitDetail/api/CommonApi";
import { apiqueryItcloudFeedback } from "../api/ItCoudFeedbackSheet";

export default {
  name: "FeedbackSheet",
  props: {
    common: Object,
    woId: String,
  },
  components: {
    QualitativeReview,
    FileDownload,
    ImagePreview,
  },
  data() {
    return {
      tabMenu: [],
      list: {},
      listAll: [],
      btnFlag: false,
      dialogQualitativeReviewVisible: false,
      //故障定性
      troubleshootingDuration: null, //故障处理历时
      suspendDuration: null, //挂起历时
      processDuration: null, //故障处理净历时
      attachmentArr: [], //附件
      attachmentVisible: false,
      busRecoverDuration: null, //业务恢复历时
      // 多个省的 定性审核区分workItemId
      workItemId: null,
      showDxsh: false,
      appendixFileLoading: false,
      isUploadReport: null,
      radio1: 0,
      // 图片预览相关
      imagePreviewVisible: false,
      currentPreviewFile: {},
    };
  },
  mounted() {
    if (this.woId) {
      this.getFeedbackData();
    }
  },
  methods: {
    handleClick(index) {
      this.deal(this.listAll[index]);
    },
    deal(data) {
      this.list = data;
      this.troubleshootingDuration = this.showTime(data.faultDuration);
      this.suspendDuration = this.showTime(data.suspendDuration);
      this.processDuration = this.showTime(data.processDuration);
      this.busRecoverDuration = this.showTime(data.busRecoverDuration);
      if (data.appendix) {
        this.attachmentArr = JSON.parse(data.appendix);
      }
      this.workItemId = data.workItemId;
      this.isUploadReport = data.isUploadReport;
    },
    getFeedbackData() {
      let param = {
        woId: this.woId,
      };
      apiqueryItcloudFeedback(param)
        .then(res => {
          if (res.status == 0) {
            this.listAll = res?.data?.rows ?? [];
            if (this.listAll.length > 0) {
              this.list = this.listAll[0];
              this.workItemId = this.list.workItemId;
              this.isUploadReport = this.list.isUploadReport;
              this.troubleshootingDuration = this.showTime(
                this.list.faultDuration
              );
              this.suspendDuration = this.showTime(this.list.suspendDuration);
              this.processDuration = this.showTime(this.list.processDuration);
              this.busRecoverDuration = this.showTime(
                this.list.busRecoverDuration
              );
              if (
                this.common.sheetStatus == "待定性审核" ||
                this.common.sheetStatus == "已归档"
              ) {
                for (let i = 0; i < this.listAll.length; i++) {
                  // `${this.listAll[i].personProName}`
                  let tabName = `（${this.listAll[i].faultRegion}）${this.listAll[i].dept}`;
                  this.tabMenu.push(tabName);
                }
              } else {
                for (let i = 0; i < this.listAll.length; i++) {
                  let tabName = `（${this.listAll[i].faultRegion}）${this.listAll[i].dept}`;
                  this.tabMenu.push(tabName);
                }
              }

              if (this.list.appendix) {
                this.attachmentArr = JSON.parse(this.list.appendix);
              }
            }
          }
        })
        .catch(err => {
          console.log(err);
        });
    },
    qualitativeReview() {
      this.showDxsh = false;
      this.$nextTick(() => {
        this.showDxsh = true;
      });
      this.dialogQualitativeReviewVisible = true;
    },
    qualitativeReviewClose() {
      this.dialogQualitativeReviewVisible = false;
    },
    qualitativeReviewSubmit(data) {
      this.$emit("qualitativeReviewSubmit", data);
      this.dialogQualitativeReviewVisible = false;
    },
    // 预览附件文件
    previewAppendixFile(data) {
      // 首先检查文件名是否为图片类型
      const fileName = data.name.toLowerCase();
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'];

      // 检查文件扩展名是否为图片
      const isImage = imageExtensions.some(ext => fileName.endsWith(ext));

      if (!isImage) {
        // 如果不是图片，直接下载
        this.downloadAppendixFile(data);
        return;
      }

      // 如果是图片，保存当前文件信息并显示预览对话框
      this.currentPreviewFile = data;
      this.imagePreviewVisible = true;
    },

    // 下载当前预览的文件
    downloadCurrentFile() {
      this.downloadAppendixFile(this.currentPreviewFile);
    },

    // 下载附件文件
    downloadAppendixFile(data) {
      this.appendixFileLoading = true;
      let param = {
        attId: data.id,
      };
      apiDownloadAppendixFile(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("文件下载成功");
          } else {
            this.$message.error("文件下载失败");
          }
          this.appendixFileLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("文件下载失败");
          this.appendixFileLoading = false;
        });
    },
    showTime(val) {
      if (val == 0 || val == "" || null == val) {
        return "0秒";
      }

      var time = "";
      var second = val % 60;
      var minute = parseInt(parseInt(val) / 60) % 60;
      time = second + "秒";
      if (minute >= 1) {
        time = minute + "分" + second + "秒";
      }
      var hour = parseInt(parseInt(val / 60) / 60) % 24;
      if (hour >= 1) {
        time = hour + "小时" + minute + "分" + second + "秒";
      }
      var day = parseInt(parseInt(parseInt(val / 60) / 60) / 24);
      if (day >= 1) {
        time = day + "天" + hour + "小时" + minute + "分" + second + "秒";
      }
      return time;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../../workOrder/workOrderWaitDetail/assets/common.scss";

.header-tabMenu {
  margin-top: 10px;
}
</style>
