<template>
  <head-content-layout class="work-order-temps">
    <template #header>
      <el-form :model="queryForm" ref="queryForm" :inline="true">
        <el-form-item label="专业">
          <dict-select
            :value.sync="queryForm.professionalType"
            :dictId="10002"
            placeholder="请选择专业"
            :dict-list.sync="dicts.professionalType"
          />
        </el-form-item>
        <el-form-item label="工单类别">
          <dict-select
            :value.sync="queryForm.networkTypeTop"
            :dictId="60133"
            placeholder="请选择工单类别"
            :dict-list.sync="dicts.networkTypeTop"
          />
        </el-form-item>
        <el-form-item label="网络类型">
          <dict-select
            :value.sync="queryForm.networkType"
            :dictId="10018"
            placeholder="请选择网络类型"
            :dict-list.sync="dicts.networkType"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
    </template>

    <template #contentHeader>
      <div style="text-align: right">
        <el-button type="primary" @click="onAdd">新增</el-button>
        <el-button type="primary" @click="onMultDel">删除</el-button>
      </div>
    </template>

    <template #table>
      <el-table
        :data="table.data"
        stripe
        height="100%"
        v-loading="table.loading"
        element-loading-text="努力加载中，请耐心等待"
        @selection-change="selection => (table.multSelection = selection)"
      >
        <el-table-column
          type="selection"
          width="50"
          label-class-name="select-all"
        />
        <el-table-column prop="templateName" label="模板名称">
        </el-table-column>
        <el-table-column prop="networkTypeTop" label="工单类别" width="120">
          <template v-slot="{ row, column }">{{
            val2Lable(row[column.property], column.property)
          }}</template>
        </el-table-column>
        <el-table-column prop="provinceName" label="省份" width="120">
        </el-table-column>
        <el-table-column prop="professionalType" label="专业" width="120">
          <template v-slot="{ row, column }">{{
            val2Lable(row[column.property], column.property)
          }}</template>
        </el-table-column>
        <el-table-column
          prop="networkType"
          label="网络类型"
          width="120"
        ></el-table-column>
        <el-table-column prop="sheetTitle" label="工单主题"></el-table-column>
        <el-table-column
          prop="faultPhenomenon"
          label="故障现象"
        ></el-table-column>
        <el-table-column label="操作" width="120">
          <template v-slot="{ row }">
            <el-link
              type="primary"
              style="margin-right: 10px"
              @click="onEdit(row)"
              >修改</el-link
            >
            <el-link type="primary" @click="onDel(row)">删除</el-link>
          </template>
        </el-table-column>
      </el-table>
    </template>

    <template #pagination>
      <pagination
        ref="pagination"
        :total="table.total"
        :page.sync="queryForm.pageIndex"
        :limit.sync="queryForm.pageSize"
        @change="getList"
        v-loading="table.loading"
        element-loading-spinner=" "
      />
    </template>

    <template #dialog>
      <el-dialog
        :title="dialog.isAdd ? '新增' : '编辑'"
        :visible.sync="dialog.visible"
        :close-on-click-modal="false"
        width="600px"
        @closed="onDiaClosed"
      >
        <el-form
          :model="dialog.form"
          ref="diaForm"
          :rules="dialog.rules"
          label-width="140px"
          :inline="false"
        >
          <el-form-item label="模板名称" prop="templateName">
            <el-input
              v-model="dialog.form.templateName"
              :disabled="!dialog.isAdd"
            ></el-input>
          </el-form-item>
          <el-form-item label="工单类别" prop="networkTypeTop">
            <el-select
              v-model="dialog.form.networkTypeTop"
              placeholder=""
              clearable
              filterable
              style="width: 100%"
              :disabled="!dialog.isAdd"
              @change="onNetTypeTopChange"
            >
              <el-option
                v-for="item in dicts.networkTypeTop"
                :key="item.dictCode"
                :label="item.dictName"
                :value="Number(item.dictCode)"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="省份" prop="provinceName">
            <dict-select
              :value.sync="dialog.form.provinceName"
              :dictId="10064"
              placeholder="请选择省份"
              style="width: 100%"
              :notSelect="!dialog.isAdd || proDisabled"
            />
          </el-form-item>
          <el-form-item label="专业" prop="professionalType">
            <el-select
              v-model="dialog.form.professionalType"
              placeholder=""
              clearable
              filterable
              style="width: 100%"
              :disabled="!dialog.isAdd || specialDisabled"
            >
              <el-option
                v-for="item in dicts.professionalType"
                :key="item.dictCode"
                :label="item.dictName"
                :value="Number(item.dictCode)"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="网络类型" prop="networkType">
            <el-select
              v-model="dialog.form.networkType"
              placeholder=""
              clearable
              filterable
              style="width: 100%"
              :disabled="!dialog.isAdd"
            >
              <el-option
                v-for="item in dicts.networkType"
                :key="item.dictCode"
                :label="item.dictName"
                :value="item.dictName"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="工单主题" prop="sheetTitle">
            <el-input v-model="dialog.form.sheetTitle"></el-input>
          </el-form-item>
          <el-form-item label="故障现象" prop="faultPhenomenon">
            <el-input
              type="textarea"
              v-model="dialog.form.faultPhenomenon"
            ></el-input>
          </el-form-item>
        </el-form>

        <span slot="footer">
          <el-button @click="dialog.visible = false">取消</el-button>
          <el-button type="primary" @click="onSave">确定</el-button>
        </span>
      </el-dialog>
    </template>
  </head-content-layout>
</template>

<script>
import HeadContentLayout from "./components/HeadContentLayout.vue";
import DictSelect from "./components/DictSelect.vue";
import Pagination from "./components/Pagination.vue";

import * as workOrderTempApi from "./api/WorkOrderTemp.js";

export default {
  name: "WorkOrderTemps",
  components: {
    HeadContentLayout,
    DictSelect,
    Pagination,
  },
  data() {
    return {
      queryForm: {
        networkTypeTop: null,
        professionalType: null,
        networkType: "",
        keyWord: "",
        pageIndex: 1,
        pageSize: 10,
      },
      dicts: {
        networkTypeTop: [],
        professionalType: [],
        networkType: [],
        multSelection: [],
      },
      table: {
        data: [],
        total: 0,
        loading: false,
      },
      dialog: {
        visible: false,
        isAdd: false,
        form: {
          templateName: "",
          networkTypeTop: null,
          provinceName: "全国",
          professionalType: null,
          networkType: "",
          sheetTitle: "",
          faultPhenomenon: "",
        },
        rules: {
          templateName: [{ required: true, message: "请填写模板名称" }],
          networkTypeTop: [{ required: true, message: "请选择工单类别" }],
          provinceName: [{ required: true, message: "请选择省份" }],
          professionalType: [{ required: true, message: "请选择专业" }],
          sheetTitle: [{ required: true, message: "请填写工单主题" }],
          faultPhenomenon: [{ required: true, message: "请填写故障现象" }],
        },
      },
    };
  },
  computed: {
    pageCount() {
      return Math.max(1, Math.ceil(this.table.total / this.queryForm.pageSize));
    },
    proDisabled() {
      return (
        this.dialog.form.networkTypeTop ==
        this.dicts.networkTypeTop.find(item => item.dictName == "集团工单")
          ?.dictCode
      );
    },
    specialDisabled() {
      return (
        this.dialog.form.networkTypeTop ==
        this.dicts.networkTypeTop.find(item => item.dictName == "高铁督办单")
          ?.dictCode
      );
    },
  },
  mounted() {
    this.onSearch();
  },
  methods: {
    onSearch() {
      this.queryForm.pageIndex = 1;
      this.getList();
    },
    getList() {
      this.table.loading = true;
      workOrderTempApi
        .tempQueryApi({
          ...this.queryForm,
          networkType: this.dicts.networkType.find(
            item => item.dictCode == this.queryForm.networkType
          )?.dictName,
        })
        .then(res => {
          this.table.data = res.data.records;
          this.table.total = res.data.total;
        })
        .finally(() => {
          this.table.loading = false;
          this.table.multSelection = [];
        });
    },
    val2Lable(val, dictList) {
      return this.dicts[dictList].find(item => item.dictCode == val)?.dictName;
    },
    onAdd() {
      this.dialog.visible = true;
      this.dialog.isAdd = true;
    },
    onEdit(row) {
      this.dialog.visible = true;
      this.dialog.isAdd = false;
      this.dialog.form = Object.assign(this.dialog.form, { ...row });
      this.$refs.diaForm?.clearValidate?.();
    },
    onNetTypeTopChange() {
      if (this.proDisabled) {
        this.dialog.form.provinceName = "全国";
      }
      if (this.specialDisabled) {
        this.dialog.form.professionalType = Number(
          this.dicts.professionalType.find(item => item.dictName == "无线网")
            ?.dictCode
        );
      }
    },
    onSave() {
      this.$refs.diaForm.validate(valid => {
        if (valid) {
          workOrderTempApi
            .tempSaveApi(this.dialog.form)
            .then(res => {
              if (res.status == 0) {
                this.$message({
                  message: "模板保存成功",
                  type: "success",
                  showClose: true,
                  duration: 3000,
                });
                this.dialog.visible = false;
                this.getList();
              } else {
                this.$alert(res.msg, {
                  type: "error",
                });
              }
            })
            .catch(res => {
              this.$alert(res.msg || res.message || "模板保存失败", {
                type: "error",
              });
            });
        }
      });
    },
    onDiaClosed() {
      this.dialog.form = {
        templateName: "",
        networkTypeTop: null,
        provinceName: "全国",
        professionalType: null,
        networkType: "",
        sheetTitle: "",
        faultPhenomenon: "",
      };
      this.$refs.diaForm?.resetFields?.();
    },
    tempDel(linkIds) {
      this.$confirm("确认删除派单模板吗？", "", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "error",
      })
        .then(() => {
          workOrderTempApi
            .tempDelApi(linkIds)
            .then(res => {
              if (res.status == 0) {
                this.$message({
                  message: "模板删除成功",
                  type: "success",
                  showClose: true,
                  duration: 3000,
                });
                this.table.total = this.total - 1;
                if (this.queryForm.pageIndex > this.pageCount) {
                  this.queryForm.pageIndex = this.pageCount;
                }
                this.getList();
              } else {
                this.$alert(`模板删除失败！${res.msg || ""}`, {
                  type: "error",
                });
              }
            })
            .catch(res => {
              this.$alert(`模板删除失败！${res.msg || res.message || ""}`, {
                type: "error",
              });
            });
        })
        .catch(() => {});
    },
    onDel(row) {
      this.tempDel([row.linkId]);
    },
    onMultDel() {
      if (this.table.multSelection.length == 0) {
        this.$alert("请选择模板", {
          type: "warning",
        });
        return false;
      }
      this.tempDel(this.table.multSelection.map(item => item.linkId));
    },
  },
};
</script>

<style lang="scss" scoped>
.work-order-temps {
  ::v-deep .select-all {
    .el-checkbox__inner {
      display: none;
      position: relative;
    }
    .cell::before {
      position: absolute;
      content: "\9009\62e9";
      left: 7px;
    }
  }
}
</style>
