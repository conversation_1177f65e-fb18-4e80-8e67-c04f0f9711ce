<template>
  <head-content-layout class="page-wrap">
    <template #header>
      <el-form
        label-width="150px"
        :inline="false"
        class="demo-form-inline"
        label-position="right"
      >
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item label-width="0px">
              <el-input
                v-model.trim="form.keyword"
                placeholder="请输入工单主题或工单编号关键字"
                style="width: 100%"
                clearable
              >
                <el-button
                  slot="append"
                  type="primary"
                  style="
                    color: #fff;
                    padding: 9px 20px;
                    top: -0.6px;
                    position: relative;
                  "
                  class="search-input-button"
                  @click="onSearch"
                  >查询</el-button
                >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="3">
            <el-form-item label-width="0px">
              <el-button
                type="text"
                style="color: #606266"
                v-if="!showMoreSearch"
                @click="onSwitchMoreSearch"
                >高级查询
                <i class="el-icon-caret-bottom" style="color: #b50b14"></i
              ></el-button>
              <el-button
                type="text"
                style="color: #606266"
                v-else
                @click="onSwitchMoreSearch"
                >高级查询<i
                  class="el-icon-caret-top"
                  style="color: #b50b14"
                ></i>
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
        <!--        高级查询-->
        <el-row
          v-show="showMoreSearch"
          type="flex"
          style="flex-wrap: wrap"
          :gutter="20"
        >
          <el-col :span="8">
            <el-form-item label="工单编号：">
              <el-input
                v-model.trim="form.sheetId"
                placeholder="请输入内容"
                clearable
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="工单主题：">
              <el-input
                v-model.trim="form.title"
                placeholder="请选择内容"
                clearable
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <template v-if="form.type != 'gt' && form.type != 'sfty'">
            <el-col :span="8">
              <el-form-item label="工单来源：">
                <dict-select
                  :value.sync="form.source"
                  :dictId="10003"
                  placeholder="请选择内容"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </template>
          <template v-if="form.type == 'sfty'">
            <el-col :span="8">
              <el-form-item label="工单来源：">
                <dict-select
                  :value.sync="form.source"
                  :dictId="10003"
                  placeholder="请选择内容"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </template>
          <template v-if="form.type == 'gt'">
            <el-col :span="8">
              <el-form-item label="工单状态：">
                <dict-select
                  :value.sync="form.status"
                  placeholder="请选择内容"
                  :dictId="statusDictId"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </template>
          <el-col :span="16">
            <el-form-item label="建单时间：">
              <el-date-picker
                v-model="form.createTimeRange"
                type="datetimerange"
                range-separator="-"
                :start-placeholder="startTime"
                :end-placeholder="endTime"
                value-format="yyyy-MM-dd HH:mm:ss"
                :default-time="['00:00:00', '23:59:59']"
                style="width: 100%"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="建单人：">
              <!-- <el-input v-model="form.createUser" style="width: 100%" readonly>
                <el-button
                  type="info"
                  slot="append"
                  icon="el-icon-user"
                  @click="onOpenPeopleDialog('builderDetermine')"
                ></el-button>
              </el-input> -->
              <input-tag
                v-model="form.createUser"
                :readonly="true"
                @select="onOpenPeopleDialog('builderDetermine')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="主送：">
              <!-- <el-input v-model="form.agentPeople" style="width: 100%" readonly>
                <el-button
                  type="info"
                  slot="append"
                  icon="el-icon-user"
                  @click="onOpenPeopleDialog('agentDetermine')"
                ></el-button>
              </el-input> -->
              <input-tag
                v-model="form.agentPeople"
                :readonly="true"
                @select="onOpenPeopleDialog('agentDetermine')"
              />
            </el-form-item>
          </el-col>
          <template
            v-if="
              form.type != 'gt' &&
              form.type != 'sfty' &&
              form.type != 'commonFlow' &&
              form.type != 'core' &&
              form.type != 'ip' &&
              form.type != 'pt' &&
              form.type != 'province' &&
              form.type != 'supervise'
            "
          >
            <el-col :span="8" key="gzzy">
              <el-form-item label="故障专业：">
                <dict-select
                  :value.sync="form.specialty"
                  :dictId="10002"
                  placeholder="请选择内容"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </template>
          <template v-if="form.type == 'wxw'">
            <el-col :span="8" key="fgcj">
              <el-form-item label="覆盖场景">
                <dict-select
                  :value.sync="form.coverScene"
                  :dictId="81002"
                  placeholder="请选择内容"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </template>
          <template v-if="form.type == 'sfty' || form.type == 'province'">
            <el-col :span="8" key="sfty_gzzy">
              <el-form-item label="故障专业：">
                <dict-select
                  :value.sync="form.professionalType"
                  :dictId="810002"
                  placeholder="请选择内容"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="form.professionalType == 7">
              <el-form-item label="覆盖场景：">
                <dict-select
                  :value.sync="form.coverScene"
                  :dictId="81002"
                  placeholder="请选择内容"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </template>
          <template v-if="form.type == 'gt'">
            <el-col :span="8">
              <el-form-item label="故障类型：">
                <dict-select
                  :value.sync="form.faultType"
                  :dictId="10201"
                  placeholder="请选择内容"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </template>
          <template
            v-if="
              form.type == 'commonFlow' ||
              form.type == 'core' ||
              form.type == 'ip' ||
              form.type == 'pt'
            "
          >
            <el-col :span="8" key="jtty_gzzy">
              <el-form-item label="故障专业：">
                <dict-select
                  :value.sync="form.professionalType"
                  :dictId="proTypeDictId"
                  placeholder="请选择内容"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8" key="jtty_jblx">
              <el-form-item label="经办类型：">
                <dict-select
                  :value.sync="form.handleType"
                  :dictId="10401"
                  placeholder="请选择内容"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </template>
           <!-- v-if="form.type == 'ggw'" -->
            <!-- v-if="form.type == 'ggw'" -->
           <template v-if="form.type == 'ggw'">
            <el-col :span="8" key="ggw_jblx">
              <el-form-item label="经办类型：">
                <dict-select
                  :value.sync="form.handleType"
                  :dictId="10401"
                  placeholder="请选择内容"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8" key="ggw_wllx">
              <el-form-item label="网络类型：">
                <dict-select
                  :value.sync="form.networkType"
                  :dictId="10018"
                  placeholder="请选择内容"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :offset="0">
              <el-form-item label="告警类别:" >
                <dict-select
                  :value.sync="form.orgType"
                  :dictId="10050"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </template>
          <template v-if="form.type == 'ITy'">
            <el-col :span="8" key="ITy_jblx">
              <el-form-item label="经办类型：">
                <dict-select
                  :value.sync="form.handleType"
                  :dictId="10401"
                  placeholder="请选择内容"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </template>
          <template v-if="form.type == 'txy'">
            <el-col :span="8" key="txy_jblx">
              <el-form-item label="经办类型：">
                <dict-select
                  :value.sync="form.handleType"
                  :dictId="10401"
                  placeholder="请选择内容"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </template>
          <template v-if="form.type == 'wxw'">
            <el-col :span="8" key="wxw_jblx">
              <el-form-item label="经办类型：">
                <dict-select
                  :value.sync="form.handleType"
                  :dictId="10401"
                  placeholder="请选择内容"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </template>
          <template v-if="form.type == 'supervise'">
            <el-col :span="8" key="supervise_sszy">
              <el-form-item label="所属专业：">
                <dict-select
                  :value.sync="form.professionalType"
                  :dictId="811038"
                  placeholder="请选择内容"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </template>
          <el-col :span="8">
            <el-form-item label="紧急程度：">
              <dict-select
                :value.sync="form.urgencyDegree"
                :dictId="emergencyDictId"
                placeholder="请选择内容"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>

          <template v-if="form.type != 'gt'">
            <el-col :span="8">
              <el-form-item label="工单状态：">
                <dict-select
                  :value.sync="form.status"
                  placeholder="请选择内容"
                  :dictId="statusDictId"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </template>
          <!-- v-if="form.type == ' province'" -->
          <template
            v-if="
              (userData.category == 'CITY' || userData.category == 'PRO') &&
              form.type != 'gt'
            "
          >
            <el-col :span="8">
              <el-form-item label="归属地市：">
                <el-select
                  style="width: 100%"
                  v-model="form.faultRegion"
                  filterable
                  clearable
                >
                  <!-- :disabled="userData.category == 'CITY'" -->
                  <el-option
                    v-for="(item, i) in faultRegionOptions"
                    :key="i"
                    :label="item.name"
                    :value="item.name"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </template>
          <template v-else-if="form.type != 'gt'">
            <el-col :span="8">
              <el-form-item :label="getProvinceLabel(form.type)">
                <el-select
                  style="width: 100%"
                  v-model="form.province"
                  filterable
                  clearable
                  :disabled="userData.category == 'PRO'"
                >
                  <el-option
                    v-for="(item, i) in provinceOptions"
                    :key="i"
                    :label="item.name"
                    :value="item.name"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </template>
          <template v-if="form.type == 'province'">
            <el-col :span="8" key="pro_jblx">
              <el-form-item label="经办类型：">
                <dict-select
                  :value.sync="form.handleType"
                  :dictId="10401"
                  placeholder="请选择内容"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </template>
          <template v-if="form.type == 'gt'">
            <el-col :span="8">
              <el-form-item label="省分：">
                <el-select
                  style="width: 100%"
                  v-model="form.province"
                  filterable
                  clearable
                  @change="gtProvinceChange"
                  :disabled="userData.category != 'UNI'"
                >
                  <el-option
                    v-for="(item, i) in provinceOptions"
                    :key="i"
                    :label="item.name"
                    :value="item.name"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="城市：">
                <el-select
                  style="width: 100%"
                  v-model="form.regionName"
                  filterable
                  clearable
                >
                  <el-option
                    v-for="(item, i) in faultRegionOptions"
                    :key="i"
                    :label="userData.category == 'CITY' ? item : item.name"
                    :value="userData.category == 'CITY' ? item : item.name"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8" key="gt_jblx">
              <el-form-item label="经办类型：">
                <dict-select
                  :value.sync="form.handleType"
                  :dictId="10401"
                  placeholder="请选择内容"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </template>
          <template v-if="form.type == 'sfty'">
            <el-col :span="8">
              <el-form-item label="预估处理时限：">
                <dict-select
                  :value.sync="form.processTimeLimit"
                  :dictId="10400"
                  placeholder="请选择内容"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8" key="sfty_jblx">
              <el-form-item label="经办类型：">
                <dict-select
                  :value.sync="form.handleType"
                  :dictId="10401"
                  placeholder="请选择内容"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="告警地市：">
                <el-select
                  style="width: 100%"
                  v-model="form.alarmRegion"
                  filterable
                  multiple
                  collapse-tags
                  clearable
                  placeholder="请选择"
                  @change="alarmRegionChange"
                >
                  <el-option
                    v-for="(item, i) in alarmRegionOptions"
                    :key="i"
                    :label="item"
                    :value="item"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="告警区县：">
                <el-select
                  style="width: 100%"
                  v-model="form.alarmCity"
                  filterable
                  collapse-tags
                  multiple
                  clearable
                  :disabled="form.alarmRegion.length == 0"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="(item, i) in alarmCityOptions"
                    :key="i"
                    :label="item"
                    :value="item"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </template>

          <template
            v-if="
              form.type == 'ggw' ||
              form.type == 'ITy' ||
              form.type == 'txy' ||
              form.type == 'core' ||
              form.type == 'ip' ||
              form.type == 'pt' ||
              form.type == 'commonFlow' ||
              form.type == 'supervise'
            "
          >
            <el-col :span="8">
              <el-form-item label="是否备份中心管辖：">
                <el-select
                  v-model="form.backCenterControlled"
                  placeholder="请选择"
                  clearable
                  style="width: 100%"
                >
                  <el-option
                    label="是"
                    value="1"
                  ></el-option>
                  <el-option
                    label="否"
                    value="0"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </template>
          <el-col :span="8">
            <el-checkbox
              v-model="form.noNullify"
              style="margin-top: 6px; margin-left: 36px"
              >不包含作废</el-checkbox
            >
          </el-col>
          <el-col :span="24" style="text-align: right; padding-right: 10px">
            <el-form-item label="" label-width="0">
              <el-button type="primary" @click="seniorQuery">查询</el-button>
              <el-button type="primary" @click="onResetForm">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </template>

    <template #contentHeader>
      <el-row
        ref="contentHeader"
        :gutter="20"
        class="content-header"
        v-loading="tableLoading"
        element-loading-spinner=" "
      >
        <el-col :xs="24" :sm="12" :md="14" :lg="20">
          <filter-total
            v-loading="tabMenuLoading"
            element-loading-text="数据加载中..."
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.8)"
            :value.sync="form.type"
            :filters="filterList"
            :totals="filterTotals"
            @onConHeadFilterChange="onConHeadFilterChange"
          ></filter-total>
        </el-col>
        <el-col :xs="24" :sm="24" :md="10" :lg="4" style="text-align: right">
          <div>
            <el-button type="primary" :loading="btnLoading" @click="onExport"
              >导出</el-button
            >
            <el-button type="primary" @click="getTableData('senior')"
              >刷新</el-button
            >
          </div>
        </el-col>
      </el-row>
    </template>

    <template #table>
      <el-table
        ref="table"
        :data="tableData"
        :border="false"
        stripe
        @selection-change="onSelectionChange"
        height="100%"
        v-loading="tableLoading"
      >
        <el-table-column key="1" label="工单类别" width="120px">
          <template slot-scope="scope">
            <el-tag :class="getSheetTypeClass(scope.row)">
              <span v-if="scope.row.networkTypeTop == 1">省分</span>
              <span v-if="scope.row.networkTypeTop == 2">省分</span>
              <span v-if="scope.row.networkTypeTop == 4">高铁</span>
              <span v-if="scope.row.networkTypeTop == 5">集团</span>
              <span v-if="scope.row.networkTypeTop == 7">核心网</span>
              <span v-if="scope.row.networkTypeTop == 8">IP专业</span>
              <span v-if="scope.row.networkTypeTop == 9">平台</span>
              <span
                v-else-if="
                  scope.row.networkTypeTop == 0 &&
                  scope.row.professionalType == '传输网'
                "
                >骨干传输</span
              ><span
                v-else-if="
                  scope.row.networkTypeTop == 0 &&
                  scope.row.professionalType == 'IT云设备'
                "
                >IT云</span
              ><span
                v-else-if="
                  scope.row.networkTypeTop == 0 &&
                  scope.row.professionalType == '通信云'
                "
                >通信云</span
              >
              <span v-else-if="scope.row.networkTypeTop == 6">无线网</span>
              <span v-else-if="scope.row.networkTypeTop == 10">GNOC督办</span>
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="sheetNo" width="320px">
          <template slot="header" slot-scope="scope">
            <span>工单编号</span>
            <!-- v-if="
                form.type == 'province' ||
                form.type == 'sfty' ||
                form.type == 'gt'
              " -->
            <el-popover
              title="标签筛选"
              v-model="popoverVisible"
              placement="right"
              width="300"
              trigger="click"
              :ref="'sheetNo_' + scope.$index"
            >
              <el-tabs type="border-card" v-model="activeName">
                <el-tab-pane label="标签" name="tags">
                  <el-tree
                    ref="tagTree"
                    :data="treeData"
                    show-checkbox
                    node-key="label"
                    :props="defaultProps"
                  >
                  </el-tree>
                </el-tab-pane>
              </el-tabs>
              <base-icon
                slot="reference"
                icon-class="icon-filter"
                style="
                  width: 20px;
                  height: 30px;
                  vertical-align: middle;
                  cursor: pointer;
                "
              ></base-icon>
              <div style="float: right; margin-top: 10px">
                <el-button type="primary" @click="filterTree()"
                  >确 定</el-button
                >
                <el-button @click="closePopOver()">重 置</el-button>
              </div>
            </el-popover>
          </template>
          <template slot-scope="scope">
            <div :style="getSheetNoStyle(scope.row)">
              <el-button
                type="text"
                class="sheetNo_style"
                @click="toDoDetail(scope.row)"
                ><span style="font-size: 14px">{{
                  scope.row.sheetNo
                }}</span></el-button
              >
              <span v-if="scope.row.suspendStatus == 1" class="hang__order"
                >挂</span
              >
            </div>
            <div
              v-if="scope.row.workOrderTag.length > 0"
              style="position: absolute; bottom: 10px"
            >
              <div class="tag-container">
                <el-link
                  v-show="scope.row.workOrderTag.length > 3"
                  :key="'linkLeft_' + scope.$index"
                  @click="scrollLeft('scrollWrapper_' + scope.$index)"
                  style="padding: 2px 3px"
                  plain
                  icon="el-icon-caret-left"
                  :underline="false"
                ></el-link>

                <div
                  :ref="'scrollWrapper_' + scope.$index"
                  class="scroll-wrapper"
                >
                  <div class="tags">
                    <el-tag
                      v-for="item in scope.row.workOrderTag"
                      :color="item.color"
                      style="color: #fff"
                    >
                      {{ item.label }}
                    </el-tag>
                  </div>
                </div>
                <el-link
                  v-show="scope.row.workOrderTag.length > 3"
                  :key="'linkRight_' + scope.$index"
                  @click="scrollRight('scrollWrapper_' + scope.$index)"
                  style="padding: 2px 3px"
                  plain
                  icon="el-icon-caret-right"
                  :underline="false"
                ></el-link>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          key="3"
          prop="sheetTitle"
          label="工单主题"
          min-width="320px"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <el-tooltip :content="row.sheetTitle" placement="top">
              <p class="descStyle">{{ row.sheetTitle }}</p>
            </el-tooltip>
          </template>
        </el-table-column>
        <template v-if="form.type == 'gt'">
          <el-table-column
            key="4"
            prop="emergencyLevel"
            label="紧急程度"
            width="120px"
          ></el-table-column>
          <el-table-column
            key="5"
            prop="senderName"
            label="建单人"
            width="200px"
          ></el-table-column>
        </template>
        <template v-if="form.type == 'ggw'">
          <el-table-column
            prop="senderName"
            label="建单人"
            min-width="165px"
          ></el-table-column>
          <el-table-column
            prop="createType"
            label="工单来源"
            width="120px"
          ></el-table-column>
        </template>
        <template
          v-if="
            form.type == 'commonFlow' ||
            form.type == 'core' ||
            form.type == 'ip' ||
            form.type == 'pt'
          "
        >
          <el-table-column key="23" prop="emergencyLevel" label="紧急程度">
          </el-table-column>
          <el-table-column
            key="24"
            prop="senderName"
            label="建单人"
            min-width="150px"
          ></el-table-column>
        </template>
        <template
          v-if="
            form.type == 'ggw' ||
            form.type == 'ITy' ||
            form.type == 'txy' ||
            form.type == 'core' ||
            form.type == 'ip' ||
            form.type == 'pt' ||
            form.type == 'commonFlow' ||
            form.type == 'supervise'
          "
        >
          <el-table-column
            key="backCenterControlled"
            prop="backCenterControlled"
            label="是否备份中心管辖"
            width="150px"
          >
            <template #default="{ row }">
              <div style="text-align: center;width: 100px">
                {{ row.backCenterControlled == '1' ? '是' : (row.backCenterControlled == '0' ? '否' : '否') }}
              </div>
            </template>
          </el-table-column>
        </template>

        <el-table-column
          key="6"
          prop="sheetCreateTime"
          label="建单时间"
          width="200px"
        ></el-table-column>
        <template v-if="form.type == 'supervise'">
          <el-table-column
            key="supervise_emergencyLevel"
            prop="emergencyLevel"
            label="紧急程度"
            width="100px"
          ></el-table-column>
          <el-table-column
            key="supervise_createType"
            prop="createType"
            label="工单来源"
            min-width="120px"
          ></el-table-column>
          <el-table-column
            key="supervise_professional"
            prop="professionalType"
            label="所属专业"
            min-width="120px"
          ></el-table-column>
          <el-table-column
            key="supervise_province"
            prop="wProvinceName"
            label="归属省份"
            min-width="120px"
          ></el-table-column>
          <el-table-column
            key="supervise_senderName"
            prop="senderName"
            label="建单人"
            width="160px"
          ></el-table-column>
        </template>
        <template v-if="form.type == 'sfty'">
          <el-table-column
            key="29"
            prop="cityName"
            label="告警地市"
            width="120px"
          ></el-table-column>
          <el-table-column
            key="30"
            prop="regionName"
            label="告警区县"
            min-width="120px"
          ></el-table-column>
        </template>
        <template
          v-if="
            form.type == 'commonFlow' ||
            form.type == 'core' ||
            form.type == 'ip' ||
            form.type == 'pt'
          "
        >
          <el-table-column
            key="25"
            prop="processTimeLimit"
            label="预估处理时限"
            min-width="120px"
          ></el-table-column>
          <el-table-column
            prop="createType"
            label="工单来源"
            width="120px"
          ></el-table-column>
        </template>
        <template
          v-if="
            form.type != 'gt' &&
            form.type != 'sfty' &&
            form.type != 'province' &&
            form.type != 'ggw' &&
            form.type != 'commonFlow' &&
            form.type != 'core' &&
            form.type != 'ip' &&
            form.type != 'pt' &&
            form.type != 'supervise'
          "
        >
          <el-table-column
            key="7"
            prop="acceptTime"
            label="受理时间"
            width="240px"
          >
            <template
              #default="{ row }"
              v-if="form.type != 'province' && form.type != 'ggw'"
            >
              <el-tooltip :content="row.acceptTime" placement="top">
                <p class="acceptStyle">{{ row.acceptTime }}</p>
              </el-tooltip>
            </template>
          </el-table-column>
        </template>

        <template v-if="form.type == 'province'">
          <el-table-column
            key="8"
            prop="city"
            label="归属地市"
          ></el-table-column>
          <el-table-column key="9" prop="emergencyLevel" label="紧急程度">
          </el-table-column>
          <el-table-column
            key="10"
            prop="senderName"
            label="建单人"
            min-width="120px"
          ></el-table-column>
        </template>
        <template v-if="form.type != 'gt' && form.type != 'supervise'">
          <el-table-column
            key="11"
            prop="professionalType"
            label="故障专业"
            width="140px"
          ></el-table-column>
        </template>
        <el-table-column
          key="12"
          prop="agentMan"
          label="主送人"
          min-width="180px"
        >
          <template #default="{ row }">
            <el-tooltip :content="row.agentMan" placement="top">
              <p class="descStyle">{{ row.agentMan }}</p>
            </el-tooltip>
          </template>
        </el-table-column>
        <template v-if="form.type == 'gt'">
          <el-table-column
            key="13"
            prop="provinceName"
            label="省份"
            width="100px"
          ></el-table-column>
          <el-table-column
            key="14"
            prop="cityName"
            label="城市"
            width="100px"
          ></el-table-column>
          <el-table-column
            key="15"
            prop="hsrLineName"
            label="高铁线路名称"
            width="150px"
          ></el-table-column>
          <el-table-column
            key="16"
            prop="bsName"
            label="小区/基站名称"
            width="200px"
          ></el-table-column>
        </template>
        <template v-if="form.type == 'sfty'">
          <el-table-column
            key="17"
            prop="emergencyLevel"
            label="紧急程度"
            width="100px"
          ></el-table-column>
          <el-table-column
            key="18"
            prop="createType"
            label="工单来源"
            min-width="120px"
          ></el-table-column>
          <el-table-column
            key="19"
            prop="senderName"
            label="建单人"
            width="200px"
          ></el-table-column>
        </template>
        <template
          v-if="
            form.type == 'wxw' || form.type == 'sfty' || form.type == 'province'
          "
        >
          <el-table-column
            key="20"
            prop="coverScene"
            min-width="120px"
            label="覆盖场景"
          >
            <template #default="{ row }">
              <el-tooltip :content="row.coverScene" placement="top">
                <p class="descStyle">{{ row.coverScene }}</p>
              </el-tooltip>
            </template>
          </el-table-column>
        </template>
        <el-table-column
          key="40"
          prop="sheetStatus"
          label="工单状态"
          width="100px"
        ></el-table-column>
        <template v-if="form.type == 'gt'">
          <el-table-column
            key="21"
            prop="faultType"
            label="故障类型"
            width="150px"
          ></el-table-column>
          <el-table-column
            key="22"
            prop="neTypeGt"
            label="网络类型"
            width="150px"
          ></el-table-column>
        </template>
      </el-table>
    </template>
    <template #pagination>
      <pagination
        ref="pagination"
        :total="form.total"
        :page.sync="form.pageNum"
        :limit.sync="form.pageSize"
        @change="getTableData('senior')"
        v-loading="tableLoading"
        element-loading-spinner=" "
      />
    </template>
    <template #dialog>
      <work-order-export
        ref="dataExport"
        :visible.sync="dataExportDialogVisible"
        :order-type="form.type"
        :province="provinceName"
        :areaType="areaType"
      ></work-order-export>
      <dia-orgs-user-tree
        :title="diaPeople.title"
        :visible.sync="diaPeople.visible"
        :showOrgsTree="diaPeople.showOrgsTree"
        @on-save="onSavePeople"
      />
    </template>
  </head-content-layout>
</template>

<script>
import { mapGetters } from "vuex";
import HeadContentLayout from "./components/HeadContentLayout.vue";
import Pagination from "./components/Pagination.vue";
import DictSelect from "./components/DictSelect.vue";
import InputTag from "../../../backbone/components/InputTag.vue";
import { apiQueryAll, apicountAllGrp } from "./api/WorkOrderList";
import FilterTotal from "./components/FilterTotal.vue";
import WorkOrderExport from "./components/WorkOrderExport.vue";
import DiaOrgsUserTree from "./components/DiaOrgsUserTree.vue";
import {
  apiUserInfoByUserNames,
  apiGetWoListProOrCity,
  apiGetRegionInfoSfty,
  apiListTreeTag,
} from "./api/CommonApi";
// import { apiGetFaultArea } from "./workOrderWaitDetail/api/CommonApi";
import moment from "moment";
export default {
  name: "WorkOrderList",
  components: {
    HeadContentLayout,
    Pagination,
    DictSelect,
    InputTag,
    FilterTotal,
    WorkOrderExport,
    DiaOrgsUserTree,
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  data() {
    return {
      filterTreeId: "85023",
      activeName: "tags",
      tabScroll: "0px", // 移动的距离
      showButton: false, // 标签左右两侧箭头是否显示
      // swiperScrollWidth: 0, // 盒子的宽度
      // swiperScrollContentWidth: 0,
      popoverVisible: false,
      // showPopover: false,
      treeData: [],
      defaultProps: {
        children: "children",
        label: "label",
      },
      scrollLeftDisabled: false,
      scrollRightDisabled: false,

      // 查询参数
      btnLoading: false,
      form: {
        woPriority: [],
        mkAlarmClear: [],
        coverScene: "",
        keyword: "",
        sheetId: "",
        title: "",
        source: "",
        createTimeRange: [],
        createUser: [],
        specialty: "",
        urgencyDegree: "",
        processing: "",
        status: "",
        level: "",
        type: "",
        pageNum: 1,
        pageSize: 10,
        total: 0,
        agentPeople: [],
        agentUserId: null,
        agentOrgId: null,
        networkTypeTop: 0,
        networkType: "", //骨干网网络类型筛选
        province: null,
        region: null,
        faultRegion: null,
        regionName: "",
        faultType: "",
        professionalType: "", //省分通用故障专业
        processTimeLimit: "", //省分通用预估处理时限
        handleType: "", //省分通用经办类型,新增高铁和通用一
        noNullify: true, //不包含作废工单
        alarmRegion: [], //省分通用告警地市：
        alarmCity: [], //省分通用告警区县：
        orgType: "",
        backCenterControlled: "", //是否备份中心管辖
      },
      emergencyDictId: "10001",
      statusDictId: "30009",
      proTypeDictId: 60001,
      provinceRangeTime: [], //省份建单时间
      startTime: moment().subtract(30, "days").format("YYYY-MM-DD 00:00:00"),
      endTime: moment().format("YYYY-MM-DD 23:59:59"),
      showMoreSearch: false,
      moreSearchColAttrs: Object.freeze({
        xs: 24,
        sm: 12,
        md: 12,
        lg: 8,
        offset: 0,
      }),
      filterList: [],
      tabMenuLoading: false,
      filterTotals: {
        ggw: 0,
        fiveGc: 0,
        ywpt: 0,
        gt: 0,
        yzy: 0,
        ITy: 0,
        txy: 0,
        commonFlow: 0,
        core: 0,
        ip: 0,
        pt: 0,
        province: 0,
        wxw: 0,
        supervise: 0,
      },
      tableLoading: false,
      tableData: [],
      multipleSelections: [],
      dataExportDialogVisible: false,
      diaPeople: {
        visible: false,
        title: "",
        saveName: "",
        saveTitleMap: {
          builderDetermine: "建单人选择",
          agentDetermine: "主送选择",
        },
        showOrgsTree: true,
        showOrgsTreeMap: {
          builderDetermine: false,
        },
      },
      alarmRegionOptions: [], //告警地市数据组
      alarmCityOptions: [], //告警区县数据组
      createUserIdArr: [],
      faultRegionOptions: [],
      userData: null,
      provinceOptions: [],
      regionOptions: [],
      areaCode: null,
      provinceName: null,
      areaType: "",
      acceptTime:
        "江苏张三：2022-12-27 00:00:00<br>上海李四：2022-12-27 00:00:00",
      parentAreaCode: "",
    };
  },
  created() {
    this.userData = JSON.parse(this.userInfo.attr2);
  },
  mounted() {
    this.onSearch();
    // this.getAlarmCityOptionsForSfty();

    this.getAlarmCityOptions();

    this.getAreaInfo().then(() => {
      this.getProvinceOptions();
      this.getFaultAreaOptions();
    });
    this.getFilterTreeData();
    this.getAlarmTypeOption();
  },
  methods: {
    // 得到告警类别下拉
    getAlarmTypeOption() {
      let param = {
        dictTypeCode: "10050",
      };
      apiDict(param)
        .then(res => {
          if (res.code == 200) {
            this.orgType = res?.data ?? [];
            this.orgType.reverse();
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    //查询标签筛选树
    getFilterTreeData() {
      let param = {
        dictTypeCode: this.filterTreeId,
      };
      apiListTreeTag(param)
        .then(res => {
          if (res.code == 200) {
            this.treeData = res?.data ?? [];
          }
        })
        .catch(error => {
          console.log(error);
        })
        .finally(() => {
          this.$emit("update:dict-list", this.dictData);
        });
    },
    // 标签向左切换
    arrowBack() {
      let tabBoxWidth = this.$refs.tagBox.clientWidth; //盒子宽度
      let offsetLeft = Math.abs(this.$refs.scrollWrapper.offsetLeft); //移动距离
      if (offsetLeft > tabBoxWidth) {
        //移动距离大于父盒子宽度，向前移动一整个父盒子宽度
        this.tabScroll = offsetLeft + tabBoxWidth + "px";
      } else {
        this.tabScroll = "0px"; // 否则移动到开始位置
      }
    },
    // 标签向右切换
    arrowForward() {
      let tabBoxWidth = this.$refs.tagBox.clientWidth; //盒子宽度
      let scrollWidth = this.$refs.scrollWrapper.scrollWidth; //内容宽度
      // 必须要在循环的父级添加 定位样式, offsetLeft 获取元素相对带有定位父元素左边框的偏移
      let offsetLeft = Math.abs(this.$refs.scrollWrapper.offsetLeft); //移动距离
      let diffWidth = scrollWidth - tabBoxWidth; //计算内容宽度与盒子宽度的差值
      if (diffWidth - offsetLeft > tabBoxWidth) {
        //判断差值减去移动距离是否大于盒子宽度 大于则滚动已移动距离+盒子宽度
        this.tabScroll = -(offsetLeft + tabBoxWidth) + "px";
      } else {
        this.tabScroll = -diffWidth + "px"; //小于则移动差值距离
      }
    },
    checkButtonStatus() {
      if (!this.$refs.scrollWrapper) return;
      // 盒子的宽度
      let containerSize = this.$refs.tagBox.clientWidth;
      // 内容的宽度
      let navSize = this.$refs.scrollWrapper.scrollWidth;
      if (containerSize > navSize || containerSize == navSize) {
        this.showButton = false;
      } else {
        this.showButton = true;
      }
    },
    getSheetNoStyle(row) {
      if (row.workOrderTag.length > 0) {
        return "display: inline-block;margin-bottom:25px";
      }
    },
    filterTree() {
      let self = this;
      let checked = self.$refs?.tagTree?.getCheckedNodes(true) ?? [];
      // let checkedNew = checked.map((obj, index) => {
      //   return obj.id;
      // });

      console.log("checked----", checked);
      this.form.woPriority = [];
      this.form.mkAlarmClear = [];

      checked.forEach(item => {
        if (this.form[item.name] == null) {
          this.form[item.name] = [];
        }
        this.form[item.name].push(item.id);
      });

      // console.log("form", this.form);
      this.popoverVisible = false;
      this.getTableData("senior");
    },
    closePopOver() {
      // 将选中设置为空
      this.$nextTick(function () {
        this.$refs.tagTree.setCheckedKeys([]);
      });
    },
    scrollLeft(wrapper) {
      this.$refs[wrapper].scrollBy({ left: -100, behavior: "smooth" });
      // this.getScrollLeftDisabled(wrapper);
      // this.getScrollRightDisabled(wrapper);
    },
    scrollRight(wrapper) {
      this.$refs[wrapper].scrollBy({ left: 100, behavior: "smooth" });
      // this.getScrollLeftDisabled(wrapper);
      // this.getScrollRightDisabled(wrapper);
    },
    checkScrollButtons() {
      const scrollWrapper = this.$refs.scrollWrapper;
      this.scrollLeftDisabled = scrollWrapper.scrollLeft <= 0;
      this.scrollRightDisabled =
        scrollWrapper.scrollWidth -
          scrollWrapper.clientWidth -
          scrollWrapper.scrollLeft <=
        0;
    },
    getScrollLeftDisabled(wrapper) {
      console.log("wrapper", wrapper);
      this.$nextTick(() => {
        const scrollWrapper = this.$refs[wrapper];
        console.log("scrollLeft", scrollWrapper.scrollLeft <= 0);
        return scrollWrapper.scrollLeft <= 0;
      });
    },
    getScrollRightDisabled(wrapper) {
      this.$nextTick(() => {
        const scrollWrapper = this.$refs[wrapper];
        console.log(
          "scrollright",
          scrollWrapper.scrollWidth -
            scrollWrapper.clientWidth -
            scrollWrapper.scrollLeft <=
            0
        );
        return (
          scrollWrapper.scrollWidth -
            scrollWrapper.clientWidth -
            scrollWrapper.scrollLeft <=
          0
        );
      });
    },

    //告警获取地市
    getAlarmCityOptions() {
      // this.multipleSelections = [];
      let param = {
        cityName: "",
      };
      apiGetRegionInfoSfty(param)
        .then(res => {
          if (res.status == "0") {
            this.alarmRegionOptions = res?.data ?? [];
            // if (this.userData.category == "CITY") {
            //   this.form.alarmRegion = [];
            //   this.form.alarmRegion.push(this.alarmRegionOptions[0]);
            //   this.getAlarmAreaOptions();
            // }
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    //告警获取区县
    getAlarmAreaOptions() {
      let param = {
        cityName: this.form.alarmRegion.join(","),
      };
      apiGetRegionInfoSfty(param)
        .then(res => {
          if (res.status == "0") {
            this.alarmCityOptions = res?.data ?? [];
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    //选完地市联动区县数据
    alarmRegionChange() {
      console.log(this.form.alarmRegion);

      if (this.form.alarmRegion.length == 0) {
        this.form.alarmCity = [];
        this.alarmCityOptions = [];
        return;
      }
      this.getAlarmAreaOptions();
    },

    //获取省份数据
    getProvinceOptions() {
      if (this.userData.category == "PRO") {
        this.provinceOptions.push({
          id: "1",
          code: null,
          name: this.provinceName,
          decr: null,
        });
        this.form.province = this.provinceName;
      } else {
        let param = {
          areaCode: "",
          type: "",
        };
        if (this.userData.orgInfo.fullOrgName.indexOf("联通国际公司") != -1) {
          param.type = "international";
        } else {
          param.type = "province";
        }
        apiGetWoListProOrCity(param)
          .then(res => {
            if (res.status == "0") {
              this.provinceOptions = res?.data ?? [];
              if (
                this.userData.orgInfo.fullOrgName.indexOf("联通国际公司") != -1
              ) {
                this.form.province = "联通国际公司";
              }
            }
          })
          .catch(error => {
            console.log(error);
          });
      }
    },
    //高铁选完省份联动城市数据
    gtProvinceChange() {
      if (this.userData.category == "CITY") {
        let param = {
          cityName: "",
        };
        apiGetRegionInfoSfty(param)
          .then(res => {
            if (res.status == "0") {
              this.faultRegionOptions = res?.data ?? [];
            }
          })
          .catch(error => {
            console.log(error);
          });
      } else {
        if (this.form.province.length == 0) {
          return;
        }
        this.form.regionName = [];
        this.faultRegionOptions = [];
        let ids = this.provinceOptions.filter(
          item => item.name == this.form.province
        );
        let idCode = ids[0].id;

        let param = {
          areaCode: idCode,
          type: "region",
        };
        apiGetWoListProOrCity(param)
          .then(res => {
            if (res.status == "0") {
              this.faultRegionOptions = res?.data ?? [];
            }
          })
          .catch(error => {
            console.log(error);
          });
      }
    },
    professionalSum(param) {
      if (this.filterList.length == 0) this.tabMenuLoading = true;
      apicountAllGrp(param)
        .then(res => {
          if (res.status == "0") {
            let _this = this;
            let v = res?.data?.rows ?? [];
            let filList = [];
            for (let i = 0; i < v.length; i++) {
              filList.push({
                label: v[i].professionalType,
                value: this.getLabelValue(v[i].professionalType),
                professionalType: v[i].professionalTypeCode,
                networkTypeTop: v[i].networkTypeTop,
              });
            }
            this.filterList = filList;
            this.tabMenuLoading = false;
            for (let i = 0; i < v.length; i++) {
              filList.map(item => {
                if (
                  item.professionalType == v[i].professionalTypeCode &&
                  item.networkTypeTop == v[i].networkTypeTop
                ) {
                  _this.filterTotals[item.value] = v[i].sum;
                }
              });
            }
          } else {
            this.tabMenuLoading = false;
          }
        })
        .catch(err => {
          console.log(err);
          this.this.tabMenuLoading = false;
        });
    },
    getLabelValue(val) {
      switch (val) {
        case "骨干传输":
          return "ggw";
        case "IT云设备":
          return "ITy";
        case "通信云":
          return "txy";
        case "省分":
          return "province";
        case "高铁督办":
          return "gt";
        case "省分通用":
          return "sfty";
        case "集团通用":
          return "commonFlow";
        case "核心网":
          return "core";
        case "IP专业":
          return "ip";
        case "平台":
          return "pt";
        case "无线网":
          return "wxw";
        case "GNOC督办":
          return "supervise";
      }
    },
    setType(newVal) {
      if (newVal == "传输网") {
        this.form.type = "ggw";
      } else if (newVal == "IT云设备") {
        this.form.type = "ITy";
      } else if (newVal == "通信云") {
        this.form.type = "txy";
      } else if (newVal == "省分") {
        this.form.type = "province";
      } else if (newVal == "集团通用") {
        this.form.type = "commonFlow";
      } else if (newVal == "核心网") {
        this.form.type = "core";
      } else if (newVal == "IP专业") {
        this.form.type = "ip";
      } else if (newVal == "平台") {
        this.form.type = "pt";
      } else if (newVal == "无线网") {
        this.form.type = "wxw";
      } else if (newVal == "集团督办") {
        this.form.type = "supervise";
      } else {
        this.form.type = "";
      }
    },
    getType() {
      let type = "";
      if (this.form.specialty == "传输网") {
        type = "3";
      } else if (this.form.specialty == "IT云设备") {
        type = "23";
      } else if (this.form.specialty == "通信云") {
        type = "22";
      } else if (this.form.specialty == "高铁督办") {
        type = "";
      } else if (this.form.specialty == "省分通用") {
        type = "";
      } else if (this.form.specialty == "集团通用") {
        type = "";
      } else if (this.form.specialty == "核心网") {
        type = "";
      } else if (this.form.specialty == "IP专业") {
        type = "";
      } else if (this.form.specialty == "平台") {
        type = "";
      } else if (this.form.specialty == "无线网") {
        type = "7";
      } else if (this.form.specialty == "集团督办") {
        type = "";
      } else {
        type = this.form.specialty;
      }
      return type;
    },
    onSwitchMoreSearch() {
      this.showMoreSearch = !this.showMoreSearch;
    },
    onSearch() {
      this.form.pageNum = 1;
      this.form.type = "";
      // if (this.form.type != "gt") {
      //   this.setType(this.form.specialty);
      // }
      this.getTableData("simple");
    },
    //获取区域信息
    getAreaInfo() {
      return new Promise(resolve => {
        let param = {
          userName: this.userData.userName,
        };
        apiUserInfoByUserNames(param)
          .then(res => {
            if (res.status == "0") {
              this.areaCode = res?.data?.data?.[0]?.areaCode ?? "";
              this.provinceName = res?.data?.data?.[0].areaName ?? "";
              this.areaType = res?.data?.data?.[0].areaType ?? "";
              this.parentAreaCode = res?.data?.data?.[0].parentAreaCode ?? "";
              resolve("success");
            }
          })
          .catch(error => {
            console.log(error);
          });
      });
    },
    getFaultAreaOptions() {
      if (this.userData.category == "PRO" || this.userData.category == "CITY") {
        let areaCode = this.areaCode;
        if (this.userData.category == "CITY") {
          areaCode = this.parentAreaCode;
        }
        let param = {
          areaCode: areaCode,
          type: "region",
        };
        apiGetWoListProOrCity(param)
          .then(res => {
            if (res.status == "0") {
              this.faultRegionOptions = res?.data ?? [];
            }
          })
          .catch(error => {
            console.log(error);
          });
      }
      // else if (this.userData.category == "CITY") {
      //   let param = {
      //     areaCode: this.areaCode,
      //   };
      //   apiGetRegionInfo(param)
      //     .then(res => {
      //       if (res.status == "0") {
      //         this.faultRegionOptions = res?.data ?? [];
      //         this.form.faultRegion = this.faultRegionOptions[0].name;
      //       }
      //     })
      //     .catch(error => {
      //       console.log(error);
      //     });
      // }
    },
    seniorQuery() {
      this.form.pageNum = 1;

      if (
        this.form.type == "ggw" ||
        this.form.type == "ITy" ||
        this.form.type == "txy"
      ) {
        this.setType(this.form.specialty);
      }
      this.getTableData("senior");
    },
    onResetForm() {
      this.createUserIdArr = [];
      this.form = {
        // woPriority:[],
        // mkAlarmClear:[],
        coverScene: "",
        keyword: this.form.keyword,
        sheetId: "",
        title: "",
        source: "",
        createTimeRange: [],
        createUser: [],
        specialty: "",
        urgencyDegree: "",
        processing: "",
        status: "",
        level: "",
        type: "",
        pageNum: 1,
        pageSize: 10,
        total: 0,
        agentPeople: [],
        agentUserId: null,
        agentOrgId: null,
        networkTypeTop: 0,
        province: null,
        region: null,
        faultRegion: null,
        regionName: "",
        faultType: "",
        professionalType: "", //省分通用故障专业
        processTimeLimit: "", //省分通用预估处理时限
        handleType: "", //省分通用经办类型
        noNullify: true, //不包含作废工单
        alarmRegion: [], //省分通用告警地市：
        alarmCity: [], //省分通用告警区县：
        backCenterControlled: "", //是否备份中心管辖
      };
    },
    onHeadFilterChange(val = "") {
      this.form.type = val;
    },

    onExport() {
      let excelParam = {};
      const createUserIdArr = this.form.createUser.map(item => item.value);
      if (this.form.type === "gt") {
        excelParam = {
          pageIndex: this.form.pageNum,
          pageSize: this.form.pageSize,
          param1: {
            sheetNo: this.form.sheetId,
            sheetTitle: this.form.title,
            // sheetStatus: this.form.source, //工单状态
            startTime: this?.form?.createTimeRange?.[0] ?? "",
            endTime: this?.form?.createTimeRange?.[1] ?? "",
            senders: createUserIdArr,
            professionalType: this.getType(),
            emergencyLevel: this.form.urgencyDegree,
            processNodeId: this.form.processing,
            sheetStatus: this.form.status,
            sheetLevel: this.form.level,
            faultRegion: this.form?.faultRegion ?? "",
            province: this.form.province,
            networkTypeTop: 4,
            handleType: this.form.handleType,
            faultType: this.form.faultType,
            agentMen: this.form.agentPeople
              .filter(item => !item.isOrgs)
              .map(item => item.value)
              .join(),
            agentDept: this.form.agentPeople
              .filter(item => item.isOrgs)
              .map(item => item.value)
              .join(),
          },
        };
      } else if (this.form.type === "sfty") {
        excelParam = {
          pageIndex: this.form.pageNum,
          pageSize: this.form.pageSize,
          param1: {
            sheetNo: this.form.sheetId,
            sheetTitle: this.form.title,
            createType: this.form.source, //工单来源
            startTime: this?.form?.createTimeRange?.[0] ?? this.startTime,
            endTime: this?.form?.createTimeRange?.[1] ?? this.endTime,
            senders: createUserIdArr,
            professionalType: this.form.professionalType,
            emergencyLevel: this.form.urgencyDegree,
            processNodeId: this.form.processing,
            sheetStatus: this.form.status,
            sheetLevel: this.form.level,
            agentMen: this.form.agentPeople
              .filter(item => !item.isOrgs)
              .map(item => item.value)
              .join(),
            agentDept: this.form.agentPeople
              .filter(item => item.isOrgs)
              .map(item => item.value)
              .join(),
            province: this.form.province,
            networkTypeTop: 2,
            processTimeLimit: this.form.processTimeLimit,
            handleType: this.form.handleType,
            alarmCity: this.form.alarmRegion.join(","),
            alarmRegion: this.form.alarmCity.join(","),
            regionName: this.form?.faultRegion ?? "",
          },
        };
      } else {
        excelParam = {
          pageIndex: this.form.pageNum,
          pageSize: this.form.pageSize,
          param1: {
            sheetTitleOrNoKeyword:
              this.form.type == "" ? this.form.keyword : "",
            sheetNo: this.form.sheetId,
            sheetTitle: this.form.title,
            createType: this.form.source, //工单来源
            startTime: this?.form?.createTimeRange?.[0] ?? "",
            endTime: this?.form?.createTimeRange?.[1] ?? "",
            senders: createUserIdArr,
            professionalType: this.getType() || this.form.professionalType,
            emergencyLevel: this.form.urgencyDegree,
            processNodeId: this.form.processing,
            sheetStatus: this.form.status,
            sheetLevel: this.form.level,
            networkType: this.form.networkType, //骨干网增加网络类型筛选条件
            // faultRegion: this.form?.faultRegion ?? "",
            regionName: this.form?.faultRegion ?? "",
            province: this.form.province,
            networkTypeTop: "",
            noNullify: this.form.noNullify ? 0 : 1,
            agentMen: this.form.agentPeople
              .filter(item => !item.isOrgs)
              .map(item => item.value)
              .join(),
            agentDept: this.form.agentPeople
              .filter(item => item.isOrgs)
              .map(item => item.value)
              .join(),
          },
        };
        if (this.form.type == "province") {
          excelParam.param1.handleType = this.form.handleType;
          excelParam.param1.networkTypeTop = "1";
        } else if (
          this.form.type == "ggw" ||
          this.form.type == "ITy" ||
          this.form.type == "txy"
        ) {
          excelParam.param1.networkTypeTop = "0";
        } else if (this.form.type == "commonFlow") {
          excelParam.param1.networkTypeTop = "5";
        } else if (this.form.type == "core") {
          excelParam.param1.networkTypeTop = "7";
        } else if (this.form.type == "ip") {
          excelParam.param1.networkTypeTop = "8";
        } else if (this.form.type == "pt") {
          excelParam.param1.networkTypeTop = "9";
        } else if (this.form.type == "supervise") {
          excelParam.param1.networkTypeTop = "10";
        }
      }

      console.log(excelParam);
      this.$refs.dataExport.initData(excelParam);
      this.dataExportDialogVisible = true;
    },
    onConHeadFilterChange(val = {}) {
      this.form.handleType = "";
      this.form.networkType = "";
      let { value, professionalType: type, networkTypeTop } = val;

      if (type == "3" && networkTypeTop == "0") {
        this.form.specialty = "传输网";
        this.statusDictId = "30010";
        this.filterTreeId = "85013";
      } else if (type == "23" && networkTypeTop == "0") {
        this.form.specialty = "IT云设备";
        this.statusDictId = "30012";
        this.filterTreeId = "85013";
      } else if (type == "22" && networkTypeTop == "0") {
        this.form.specialty = "通信云";
        this.statusDictId = "30024";
        this.filterTreeId = "85013";
      } else if (networkTypeTop == "1") {
        //省分
        this.form.specialty = "";
        this.statusDictId = "30011";
        this.filterTreeId = "85003";
      } else if (networkTypeTop == "4") {
        this.form.specialty = "高铁督办";
        this.statusDictId = "30017";
        this.emergencyDictId = "10200";
        this.filterTreeId = "85003";
      } else if (networkTypeTop == "2") {
        this.form.specialty = "省分通用";
        this.statusDictId = "30020";
        this.emergencyDictId = "10300";
        this.filterTreeId = "85003";
      } else if (networkTypeTop == "5") {
        this.form.specialty = "集团通用";
        this.statusDictId = "60007";
        this.proTypeDictId = 60012;
        this.form.professionalType = "";
        this.filterTreeId = "85013";
      } else if (networkTypeTop == "7") {
        this.form.specialty = "核心网";
        this.statusDictId = "60007";
        this.proTypeDictId = 60009;
        this.form.professionalType = "";
        this.filterTreeId = "85013";
      } else if (networkTypeTop == "8") {
        this.form.specialty = "IP专业";
        this.statusDictId = "60007";
        this.proTypeDictId = 60010;
        this.form.professionalType = "";
        this.filterTreeId = "85013";
      } else if (networkTypeTop == "9") {
        this.form.specialty = "平台";
        this.statusDictId = "60007";
        this.proTypeDictId = 60011;
        this.form.professionalType = "";
        this.filterTreeId = "85013";
      } else if (networkTypeTop == "6") {
        this.form.specialty = "无线网";
        this.statusDictId = "70015";
        this.filterTreeId = "85003";
      } else if (networkTypeTop == "10") {
        this.form.specialty = "集团督办";
        this.statusDictId = "811051";
        this.filterTreeId = "85013";
      } else {
        this.form.specialty = "";
        this.statusDictId = "30009";
        this.filterTreeId = "85023";
      }
      if (value === undefined) {
        value = "";
      }

      if (this.userData.category == "CITY" && networkTypeTop == "4") {
        this.form.province = this.userData.provinceName;
        this.gtProvinceChange();
      }
      if (this.userData.category == "UNI") {
        this.form.province = "";
        this.form.regionName = "";
      }
      this.form.type = value; //控制tab
      this.form.pageNum = 1;
      this.form.faultRegion = "";
      this.form.woPriority = [];
      this.form.mkAlarmClear = [];
      this.getFilterTreeData();
      this.getTableData("senior");
    },
    getTableData(type) {
      if (type == "simple") {
        this.onResetForm();
      }
      this.tableLoading = true;
      let seniorParam = {
        sheetNo: this.form.sheetId,
        sheetTitle: this.form.title,
        createType: this.form.source, //工单来源
        startTime: this?.form?.createTimeRange?.[0] ?? this.startTime,
        endTime: this?.form?.createTimeRange?.[1] ?? this.endTime,
        senders: this.form.createUser.map(item => item.value),
        professionalType: this.getType(),
        emergencyLevel: this.form.urgencyDegree,
        processNodeId: this.form.processing,
        sheetStatus: this.form.status,
        sheetLevel: this.form.level,
        agentMen: this.form.agentPeople
          .filter(item => !item.isOrgs)
          .map(item => item.value)
          .join(),
        agentDept: this.form.agentPeople
          .filter(item => item.isOrgs)
          .map(item => item.value)
          .join(),
        province: this.form.province,
        noNullify: this.form.noNullify ? 0 : 1,
        regionName: this.form.faultRegion,
        networkType: this.form.networkType,
        orgType: this.form.orgType
      };
      let simpleParam = {
        sheetTitleOrNoKeyword: this.form.keyword,
        startTime: this.startTime,
        endTime: this.endTime,
        noNullify: this.form.noNullify ? 0 : 1,
      };
      if (this.form.type === "province") {
        this.$set(seniorParam, "networkTypeTop", 1);
        this.$set(seniorParam, "handleType", this.form.handleType);
        this.$set(seniorParam, "professionalType", this.form.professionalType);

        if (this.form.professionalType == 7) {
          this.$set(seniorParam, "coverScene", this.form.coverScene);
        }
      } else if (this.form.type === "gt") {
        this.$set(seniorParam, "networkTypeTop", 4);
        this.$set(seniorParam, "regionName", this.form.regionName);
        this.$set(seniorParam, "faultType", this.form.faultType);
        this.$set(seniorParam, "handleType", this.form.handleType);
      } else if (this.form.type === "sfty") {
        this.$set(seniorParam, "networkTypeTop", 2);
        this.$set(seniorParam, "professionalType", this.form.professionalType);
        this.$set(seniorParam, "processTimeLimit", this.form.processTimeLimit);
        this.$set(seniorParam, "handleType", this.form.handleType);
        this.$set(seniorParam, "alarmCity", this.form.alarmRegion.join(","));
        this.$set(seniorParam, "alarmRegion", this.form.alarmCity.join(","));

        if (this.form.professionalType == 7) {
          this.$set(seniorParam, "coverScene", this.form.coverScene);
        }
      } else if (
        this.form.type == "ggw" ||
        this.form.type == "ITy" ||
        this.form.type == "txy"
      ) {
        this.$set(seniorParam, "networkTypeTop", 0);
        this.$set(seniorParam, "handleType", this.form.handleType);
      } else if (this.form.type == "commonFlow") {
        this.$set(seniorParam, "networkTypeTop", 5);
        this.$set(seniorParam, "professionalType", this.form.professionalType);
        this.$set(seniorParam, "handleType", this.form.handleType);
      } else if (this.form.type == "core") {
        this.$set(seniorParam, "networkTypeTop", 7);
        this.$set(seniorParam, "professionalType", this.form.professionalType);
        this.$set(seniorParam, "handleType", this.form.handleType);
      } else if (this.form.type == "ip") {
        this.$set(seniorParam, "networkTypeTop", 8);
        this.$set(seniorParam, "professionalType", this.form.professionalType);
        this.$set(seniorParam, "handleType", this.form.handleType);
      } else if (this.form.type == "pt") {
        this.$set(seniorParam, "networkTypeTop", 9);
        this.$set(seniorParam, "professionalType", this.form.professionalType);
        this.$set(seniorParam, "handleType", this.form.handleType);
      } else if (this.form.type == "wxw") {
        this.$set(seniorParam, "networkTypeTop", 6);
        this.$set(seniorParam, "professionalType", 7);
        this.$set(seniorParam, "handleType", this.form.handleType);
        this.$set(seniorParam, "coverScene", this.form.coverScene);
      } else if (this.form.type == "supervise") {
        this.$set(seniorParam, "networkTypeTop", 10);
        this.$set(seniorParam, "professionalType", this.form.professionalType);
      }
      // 添加是否备份中心管控字段
      if (
        this.form.type == "ggw" ||
        this.form.type == "ITy" ||
        this.form.type == "txy" ||
        this.form.type == "core" ||
        this.form.type == "ip" ||
        this.form.type == "pt" ||
        this.form.type == "commonFlow" ||
        this.form.type == "supervise"
      ) {
        // 只有在下拉框选择了值时才将该字段传递给接口
        if (this.form.backCenterControlled) {
          this.$set(seniorParam, "backCenterControlled", this.form.backCenterControlled);
        }
      }

      this.$set(seniorParam, "woPriority", this.form.woPriority?.join(","));
      this.$set(seniorParam, "mkAlarmClear", this.form.mkAlarmClear?.join(","));
      let param = {
        pageIndex: this.form.pageNum,
        pageSize: this.form.pageSize,
        param1:
          type == "senior"
            ? JSON.stringify(seniorParam)
            : JSON.stringify(simpleParam),
      };
      if(this.form.type == "ggw"){
        this.form.orgType
      }
      apiQueryAll(param)
        .then(res => {
          if (res.status == "0") {
            this.tableData = res?.data?.rows ?? [];
            this.form.total = res?.data?.totalElements ?? 0;

            this.professionalSum(param);
          }
          this.tableLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.tableLoading = false;
        });
    },

    onSelectionChange(selection) {
      this.multipleSelections = selection;
    },
    indexMethod(index) {
      const currentPage = this.form.pageNum;
      const pageSize = this.form.pageSize;
      return (currentPage - 1) * pageSize + (index + 1);
    },
    toDoDetail(data) {
      if (data.networkTypeTop == 1) {
        this.JumpDetails_Province(data);
      } else if (data.networkTypeTop == 4) {
        this.JumpDetails_GT(data);
      } else if (data.networkTypeTop == 2) {
        this.JumpDetails_SFTY(data);
      } else if (data.networkTypeTop == 5) {
        let title = "集团通用";
        this.JumpDetails_Common(data, title);
      } else if (data.networkTypeTop == 7) {
        let title = "核心网";
        this.JumpDetails_Common(data, title);
      } else if (data.networkTypeTop == 8) {
        let title = "IP专业";
        this.JumpDetails_Common(data, title);
      } else if (data.networkTypeTop == 9) {
        let title = "平台";
        this.JumpDetails_Common(data, title);
      } else if (data.networkTypeTop == 10) {
        let title = "GNOC督办单";
        this.JumpDetails_Supervise(data, title);
      } else if (data.networkTypeTop == 0) {
        switch (data.professionalType) {
          case "传输网":
            this.JumpDetails_GGW(data);
            break;
          case "IT云设备":
            this.JumpDetails_ITY(data);
            break;
          case "通信云":
            this.JumpDetails_TXY(data);
            break;
        }
      } else if (data.networkTypeTop == 6) {
        this.JumpDetails_Wireless(data);
      }
    },
    JumpDetails_Supervise(data, title) {
      this.$router.push({
        name: "supervise_orderDetail",
        query: {
          workItemId: data.workItemId,
          processInstId: data.processInstId,
          woId: data.woId,
          processDefId: data.processDefId,
          processNode: data.sheetStatus,
          fromPage: "工单查询",
          frameTabTitle: title,
          networkType: data.networkType,
          professionalType: data.professionalType,
        },
      });
    },
    JumpDetails_TXY(data) {
      this.$router.push({
        name: "commCloud_woDetail",
        query: {
          workItemId: data.workItemId,
          processInstId: data.processInstId,
          woId: data.woId,
          processDefId: data.processDefId,
          fromPage: "工单查询",
          networkType: data.networkType,
        },
      });
    },
    JumpDetails_ITY(data) {
      this.$router.push({
        name: "itCloud_woDetail",
        query: {
          workItemId: data.workItemId,
          processInstId: data.processInstId,
          woId: data.woId,
          processDefId: data.processDefId,
          fromPage: "工单查询",
          networkType: data.networkType,
        },
      });
    },
    JumpDetails_GGW(data) {
      this.$router.push({
        name: "backbone_toDoRepairOrderDetail",
        query: {
          workItemId: data.workItemId,
          processInstId: data.processInstId,
          woId: data.woId,
          processDefId: data.processDefId,
          fromPage: "工单查询",
          networkType: data.networkType,
          networkTypeTop: data.networkTypeTop,
          professionalType: data.professionalType,
        },
      });
    },
    JumpDetails_Province(data) {
      this.$router.push({
        name: "provinceOrder_detail",
        query: {
          workItemId: data.workItemId,
          processInstId: data.processInstId,
          woId: data.woId,
          processDefId: data.processDefId,
          processNode: data.sheetStatus,
          fromPage: "工单查询",
          networkType: data.networkType,
        },
      });
    },

    JumpDetails_GT(data) {
      this.$router.push({
        name: "gtOrder_detail",
        query: {
          workItemId: data.workItemId,
          processInstId: data.processInstId,
          woId: data.woId,
          processDefId: data.processDefId,
          processNode: data.sheetStatus,
          fromPage: "工单查询",
          networkType: data.networkType,
        },
      });
    },

    JumpDetails_Common(data, title) {
      this.$router.push({
        name: data.networkTypeTop==8?"ipProfessionOrder_Detail":"common_orderDetail",
        query: {
          workItemId: data.workItemId,
          processInstId: data.processInstId,
          woId: data.woId,
          processDefId: data.processDefId,
          processNode: data.sheetStatus,
          fromPage: "工单查询",
          frameTabTitle: title + "故障工单",
          networkType: data.networkType,
        },
      });
    },
    JumpDetails_SFTY(data) {
      this.$router.push({
        name: "commonProvinceOrder_detail",
        query: {
          workItemId: data.workItemId,
          processInstId: data.processInstId,
          woId: data.woId,
          processDefId: data.processDefId,
          processNode: data.sheetStatus,
          fromPage: "工单查询",
          networkType: data.networkType,
        },
      });
    },
    JumpDetails_Wireless(data) {
      this.$router.push({
        name: "wireless_orderDetail",
        query: {
          workItemId: data.workItemId,
          processInstId: data.processInstId,
          woId: data.woId,
          processDefId: data.processDefId,
          fromPage: "工单查询",
          networkType: data.networkType,
          networkTypeTop: data.networkTypeTop,
          professionalType: data.professionalType,
        },
      });
    },
    onOpenPeopleDialog(diaSaveName) {
      this.diaPeople.title = this.diaPeople.saveTitleMap[diaSaveName];
      this.diaPeople.showOrgsTree =
        this.diaPeople.showOrgsTreeMap[diaSaveName] ?? true;
      this.diaPeople.saveName = diaSaveName;
      this.diaPeople.visible = true;
    },
    onSavePeople(val) {
      this[this.diaPeople.saveName](val);
    },
    builderDetermine({ usersChecked, orgsChecked, selectionUser }) {
      let createUserArr = [];
      if (selectionUser && selectionUser.length > 0) {
        createUserArr = selectionUser.map(item => ({
          value: item.userName,
          label: item.trueName,
          data: item,
        }));
      } else {
        createUserArr = usersChecked.map(item => ({
          value: item.id,
          label: item.name,
          data: item,
        }));
      }
      this.form.createUser = createUserArr;
    },
    agentDetermine({ usersChecked, orgsChecked, selectionUser }) {
      let agentUser = [];
      if (selectionUser && selectionUser.length > 0) {
        agentUser = selectionUser.map(item => ({
          value: item.userName,
          label: item.trueName,
          data: item,
          isOrgs: false,
        }));
      } else {
        agentUser = usersChecked.map(item => ({
          value: item.id,
          label: item.name,
          data: item,
          isOrgs: false,
        }));
      }
      this.form.agentPeople = agentUser.concat(
        orgsChecked.map(item => ({
          value: item.id,
          label: item.name,
          data: item,
          isOrgs: true,
        }))
      );
    },
    getSheetTypeClass(data) {
      if (data.networkTypeTop == 0 && data.professionalType == "传输网") {
        return "ggw_sheetType";
      } else if (
        data.networkTypeTop == 0 &&
        data.professionalType == "IT云设备"
      ) {
        return "itCloud_sheetType";
      } else if (
        data.networkTypeTop == 0 &&
        data.professionalType == "通信云"
      ) {
        return "commCloud_sheetType";
      } else if (data.networkTypeTop == 1) {
        return "sf_sheetType";
      } else if (data.networkTypeTop == 5) {
        return "commonFlow_sheetType";
      } else if (data.networkTypeTop == 7) {
        return "core_sheetType";
      } else if (data.networkTypeTop == 8) {
        return "ip_sheetType";
      } else if (data.networkTypeTop == 9) {
        return "pt_sheetType";
      } else if (data.networkTypeTop == 4) {
        return "gt_sheetType";
      } else if (data.networkTypeTop == 2) {
        return "sfty_sheetType";
      } else if (data.networkTypeTop == 6) {
        return "wxw_sheetType";
      } else if (data.networkTypeTop == 10) {
        return "supervise_sheetType";
      }
    },
    stitchingAlgorithm(orgName, userName) {
      if (orgName.length !== 0 && userName.length !== 0) {
        return orgName + "," + userName;
      } else {
        if (orgName.length !== 0) {
          return orgName;
        } else if (userName.length !== 0) {
          return userName;
        } else {
          return "";
        }
      }
    },
    getProvinceLabel(type) {
      if (type == "supervise") {
        return "归属省份：";
      } else {
        return "省分：";
      }
    },
  },
};
</script>
<style lang="scss" scoped>
@import "./workOrderWaitDetail/assets/common.scss";
.filter-total {
  ::v-deep .el-loading-mask .el-loading-spinner {
    text-align: left;
    line-height: 32px;
    margin-left: 150px;
  }
  ::v-deep .el-loading-text {
    display: inline-block;
    margin-left: 10px;
  }
}
.descStyle {
  display: -webkit-box;
  overflow: hidden;
  white-space: normal !important;
  text-overflow: ellipsis;
  word-wrap: break-word;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.acceptStyle {
  display: -webkit-box;
  overflow: hidden;
  white-space: normal !important;
  text-overflow: ellipsis;
  word-wrap: break-word;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

// 新样式
.tag-container {
  display: flex;
  align-items: center;
}
</style>
