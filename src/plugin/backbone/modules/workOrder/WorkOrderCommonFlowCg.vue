<template>
  <head-fixed-layout class="draft-eidt-page">
    <template #header>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="15" :md="18" class="head-title"
          ><span v-if="networkTypeTop == '5'">集团通用故障工单草稿</span>
          <span v-if="networkTypeTop == '7'">核心网故障工单草稿</span>
          <span v-if="networkTypeTop == '8'">IP专业故障工单草稿</span>
          <span v-if="networkTypeTop == '9'">平台故障工单草稿</span>
        </el-col>
        <el-col :xs="24" :sm="9" :md="6" class="head-handle-wrap">
          <el-button
            type="primary"
            @click="onSheetSave"
            v-loading.fullscreen.lock="sheetCgSaveLoading"
            >保存</el-button
          >
          <el-button
            type="primary"
            @click="onSheetDel"
            v-loading.fullscreen.lock="sheetCgDelLoading"
            >删除</el-button
          >
          <el-button
            type="primary"
            @click="onSheetSubmit"
            v-loading.fullscreen.lock="sheetCgCommitLoading"
            >提交</el-button
          >
        </el-col>
      </el-row>
    </template>

    <el-form
      :model="sheetForm"
      ref="sheetForm"
      :rules="sheetFormRules"
      label-width="130px"
      label-position="right"
    >
      <el-card shadow="always" :body-style="{ padding: '10px' }">
        <div slot="header" class="card-title">
          <span>工单基本信息</span>
        </div>
        <el-row :gutter="10" type="flex" style="flex-wrap: wrap">
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="建单人:" prop="createUser" required>
              <el-input
                v-model="sheetForm.createUser"
                placeholder="请输入建单人"
                readonly
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="建单部门:" prop="dept" required>
              <el-input
                v-model="sheetForm.dept"
                placeholder="请输入建单部门"
                readonly
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="建单时间:" prop="createTime" required>
              <el-date-picker
                v-model="sheetForm.createTime"
                type="datetime"
                placeholder="请选择建单时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                readonly
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="14" :md="16" :offset="0">
            <el-form-item label="工单主题:" prop="title" required>
              <el-input
                v-model="sheetForm.title"
                placeholder="使用拼接规则手动填写"
                clearable
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="10" :md="8" :offset="0">
            <el-form-item label="工单来源:" prop="source" required>
              <dict-select
                :value.sync="sheetForm.source"
                :dictId="10003"
                placeholder="请选择内容"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="发生时间:" prop="happenTime">
              <el-date-picker
                v-model="sheetForm.happenTime"
                type="datetime"
                placeholder="请选择时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="紧急程度:" prop="urgent" required>
              <el-radio-group v-model="sheetForm.urgent">
                <el-radio
                  v-for="(item, i) in urgentOption"
                  :key="i"
                  :label="item.dictCode"
                  >{{ item.dictName }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="10" :md="8" :offset="0">
            <el-form-item label="工单优先级:" prop="faultLevel" required>
              <dict-select
                :value.sync="sheetForm.faultLevel"
                :dictId="81005"
                placeholder="请选择"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item
              label="受理时限(分钟):"
              prop="acceptTimeLimit"
              required
            >
              <el-input
                v-model="sheetForm.acceptTimeLimit"
                placeholder="请输入受理时限"
                clearable
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="14" :md="16" :offset="0">
            <el-form-item
              label="预估处理时限:"
              prop="estimatedProTimeLimit"
              required
            >
              <el-radio-group v-model="sheetForm.estimatedProTimeLimit">
                <el-radio
                  v-for="(item, i) in estimatedProTimeLimitOption"
                  :key="i"
                  :label="item.id"
                  >{{ item.name }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <template v-if="networkTypeTop == '5'">
            <el-col :xs="24" :sm="8" :md="8" :offset="0">
              <el-form-item label="所属专业:" prop="special" required>
                <dict-select
                  :value.sync="sheetForm.special"
                  :dictId="60012"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </template>
          <template v-if="networkTypeTop == '7'">
            <el-col :xs="24" :sm="8" :md="8" :offset="0">
              <el-form-item label="所属专业:" prop="special" required>
                <dict-select
                  :value.sync="sheetForm.special"
                  :dictId="60009"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </template>
          <template v-if="networkTypeTop == '8'">
            <el-col :xs="24" :sm="8" :md="8" :offset="0">
              <el-form-item label="所属专业:" prop="special" required>
                <dict-select
                  :value.sync="sheetForm.special"
                  :dictId="60010"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </template>
          <template v-if="networkTypeTop == '9'">
            <el-col :xs="24" :sm="8" :md="8" :offset="0">
              <el-form-item label="所属专业:" prop="special" required>
                <dict-select
                  :value.sync="sheetForm.special"
                  :dictId="60011"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </template>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="业务中断:" prop="businessDown" required>
              <dict-select
                :value.sync="sheetForm.businessDown"
                :dictId="60138"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="网络类型:" prop="netType">
              <dict-select
                :value.sync="sheetForm.netType"
                :dictId="this.networkTypeDictId"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="告警类别:" prop="alarmType">
              <dict-select
                :value.sync="sheetForm.alarmType"
                :dictId="60002"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <template
            v-if="
              sheetForm.special == 1 ||
              sheetForm.special == 31 ||
              sheetForm.special == 6 ||
              sheetForm.special == 20 ||
              sheetForm.special == 21 ||
              sheetForm.special == 28 ||
              sheetForm.special == 32 ||
              sheetForm.special == 19
            "
          >
            <el-col :xs="24" :sm="8" :md="8" :offset="0">
              <el-form-item label="网元类型:" prop="neType" key="neType">
                <dict-select
                  :value.sync="sheetForm.neType"
                  :dictId="60004"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col
              :xs="24"
              :sm="8"
              :md="8"
              :offset="0"
              v-show="isOpticalCableShow"
            >
              <el-form-item label="网元名称:" prop="neName" key="neName">
                <el-input
                  v-model="sheetForm.neName"
                  placeholder="请输入内容"
                  style="width: 100%"
                >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="14" :md="16" :offset="0">
              <el-form-item
                label="工作内容:"
                prop="jobComment"
                key="jobComment"
              >
                <el-input
                  v-model="sheetForm.jobComment"
                  type="textarea"
                  :rows="1"
                  clearable
                  style="width: 100%"
                >
                </el-input>
              </el-form-item>
            </el-col>
          </template>
          <template v-else-if="sheetForm.special == 26">
            <el-col :xs="24" :sm="8" :md="8" :offset="0">
              <el-form-item label="网元类型:" prop="neType" key="neType">
                <dict-select
                  :value.sync="sheetForm.neType"
                  :dictId="60004"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :offset="0">
              <el-form-item
                label="云池类型:"
                prop="cloudPoolType"
                key="cloudPoolType"
              >
                <el-input
                  v-model="sheetForm.cloudPoolType"
                  placeholder="请输入内容"
                  style="width: 100%"
                  disabled
                >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="14" :md="8" :offset="0">
              <el-form-item
                label="MEC节点名称:"
                prop="mecNodeName"
                key="mecNodeName"
              >
                <el-input
                  v-model="sheetForm.mecNodeName"
                  placeholder="请输入内容"
                  style="width: 100%"
                >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="14" :md="8" :offset="0">
              <el-form-item
                label="工作内容:"
                prop="jobComment"
                key="jobComment"
              >
                <el-input
                  v-model="sheetForm.jobComment"
                  type="textarea"
                  :rows="1"
                  clearable
                  style="width: 100%"
                >
                </el-input>
              </el-form-item>
            </el-col>
          </template>
          <template v-else>
            <el-col :xs="24" :sm="14" :md="8" :offset="0">
              <el-form-item
                label="工作内容:"
                prop="jobComment"
                key="jobComment"
              >
                <el-input
                  v-model="sheetForm.jobComment"
                  type="textarea"
                  :rows="1"
                  clearable
                  style="width: 100%"
                >
                </el-input>
              </el-form-item>
            </el-col>
          </template>
          <el-col :xs="24" :sm="16" :md="24" :offset="0">
            <el-form-item
              label="故障现象:"
              prop="phenomenon"
              required
              key="phenomenon"
            >
              <el-input
                v-model="sheetForm.phenomenon"
                placeholder="请输入内容"
                type="textarea"
                :rows="2"
                clearable
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="影响业务列表:" prop="incidenceFile">
              <el-input
                v-model="sheetForm.incidenceFile"
                placeholder="添加附件"
                readonly
                style="width: 100%"
              >
                <el-button type="info" slot="append" @click="incidenceBrowse"
                  >+</el-button
                >
                <el-button
                  v-if="ngIncidenceFileArr.length > 0"
                  type="info"
                  slot="append"
                  @click="ngIncidenceBrowse"
                  title="查看拟稿上传的影响业务列表"
                  >查看</el-button
                >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="16" :md="16" :offset="0">
            <el-form-item label="影响范围:" prop="incidence">
              <el-input
                v-model="sheetForm.incidence"
                placeholder="请输入内容"
                clearable
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24" :offset="0">
            <el-form-item label="备注:" prop="remarks">
              <el-input
                v-model="sheetForm.remarks"
                placeholder="请输入内容"
                clearable
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card
        shadow="always"
        :body-style="{ padding: '10px' }"
        style="margin-top: 10px"
      >
        <div slot="header" class="card-title">
          <span>工单派送信息</span>
        </div>
        <el-row :gutter="10" type="flex" style="flex-wrap: wrap">
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="附件:" prop="attachmentFile">
              <el-input
                v-model="sheetForm.attachmentFile"
                placeholder="添加附件"
                readonly
                style="width: 100%"
              >
                <el-button type="info" slot="append" @click="attachmentBrowse"
                  >+</el-button
                >
                <el-button
                  v-if="ngAttachmentArr.length > 0"
                  type="info"
                  slot="append"
                  @click="ngAttachmentBrowse"
                  title="查看拟稿上传的附件"
                  >查看</el-button
                >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="建单人主送:" prop="builderZs" required>
              <el-input v-model="sheetForm.builderZs" placeholder="添加人员">
                <template v-for="(tag, index) in organizeForm.builderZsList">
                  <el-tag
                    slot="prefix"
                    style="margin-top: 5px"
                    :key="index"
                    closable
                    @close="handleClose('builderZs', tag)"
                    v-show="index < 1"
                    v-if="tag.bz == 'user'"
                  >
                    {{ tag.name }}
                  </el-tag>
                  <el-tag
                    slot="prefix"
                    style="margin-top: 5px"
                    :key="index"
                    closable
                    @close="handleClose('builderZs', tag)"
                    v-show="index < 1"
                    v-if="tag.bz == 'org'"
                  >
                    {{ tag.orgName }}
                  </el-tag>
                </template>
                <el-popover
                  slot="prefix"
                  v-if="organizeForm.builderZsList.length >= 2"
                  width="500"
                  trigger="click"
                >
                  <el-input
                    v-model="organizeForm.builderZsName"
                    placeholder="请输入主送人员姓名/组织名称"
                  >
                    <el-button
                      type="info"
                      slot="append"
                      icon="el-icon-search"
                      @click="search('builderZs')"
                    >
                    </el-button>
                    <el-button
                      type="info"
                      slot="append"
                      icon="el-icon-close"
                      @click="clear('builderZs')"
                    >
                    </el-button>
                  </el-input>
                  <el-table
                    ref="multipleTable"
                    tooltip-effect="dark"
                    @selection-change="handleSelectionChange"
                    :data="organizeForm.builderZsListCopy"
                    max-height="240"
                  >
                    <el-table-column width="30" type="selection">
                    </el-table-column>
                    <el-table-column
                      min-width="70"
                      property="name"
                      label="姓名"
                    >
                    </el-table-column>
                    <el-table-column
                      min-width="180"
                      property="orgName"
                      label="组织"
                    >
                    </el-table-column>
                    <el-table-column
                      min-width="120"
                      property="mobilePhone"
                      label="电话"
                    >
                    </el-table-column>
                    <el-table-column fixed="right" label="操作" width="50">
                      <template slot-scope="scope">
                        <el-button
                          type="text"
                          size="small"
                          @click.native.prevent="
                            handleClose('builderZs', scope.row)
                          "
                        >
                          移除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <el-button
                    size="small"
                    type="text"
                    @click="toggleSelection('builderZs')"
                    >批量移除
                  </el-button>
                  <el-tag slot="reference" style="margin-top: 3px">
                    +{{ organizeForm.builderZsList.length - 1 }}
                  </el-tag>
                </el-popover>
                <el-button
                  type="info"
                  slot="append"
                  icon="el-icon-user"
                  @click="onOpenPeopleDialog('lordSentDetermine')"
                ></el-button>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="建单人抄送:" prop="builderCs">
              <el-input v-model="sheetForm.builderCs" placeholder="添加人员">
                <template v-for="(tag, index) in organizeForm.builderCsList">
                  <el-tag
                    slot="prefix"
                    style="margin-top: 5px"
                    :key="index"
                    closable
                    @close="handleClose('builderCs', tag)"
                    v-show="index < 1"
                    v-if="tag.bz == 'user'"
                  >
                    {{ tag.name }}
                  </el-tag>
                  <el-tag
                    slot="prefix"
                    style="margin-top: 5px"
                    :key="index"
                    closable
                    @close="handleClose('builderCs', tag)"
                    v-show="index < 1"
                    v-if="tag.bz == 'org'"
                  >
                    {{ tag.orgName }}
                  </el-tag>
                </template>
                <el-popover
                  slot="prefix"
                  v-if="organizeForm.builderCsList.length >= 2"
                  width="500"
                  trigger="click"
                >
                  <el-input
                    v-model="organizeForm.builderCsName"
                    placeholder="请输入抄送人员姓名/组织名称"
                  >
                    <el-button
                      type="info"
                      slot="append"
                      icon="el-icon-search"
                      @click="search('builderCs')"
                    >
                    </el-button>
                    <el-button
                      type="info"
                      slot="append"
                      icon="el-icon-close"
                      @click="clear('builderCs')"
                    >
                    </el-button>
                  </el-input>
                  <el-table
                    ref="multipleTable"
                    tooltip-effect="dark"
                    @selection-change="handleSelectionChange"
                    :data="organizeForm.builderCsListCopy"
                    max-height="240"
                  >
                    <el-table-column width="30" type="selection">
                    </el-table-column>
                    <el-table-column
                      min-width="70"
                      property="name"
                      label="姓名"
                    >
                    </el-table-column>
                    <el-table-column
                      min-width="180"
                      property="orgName"
                      label="组织"
                    >
                    </el-table-column>
                    <el-table-column
                      min-width="120"
                      property="mobilePhone"
                      label="电话"
                    >
                    </el-table-column>
                    <el-table-column fixed="right" label="操作" width="50">
                      <template slot-scope="scope">
                        <el-button
                          type="text"
                          size="small"
                          @click.native.prevent="
                            handleClose('builderCs', scope.row)
                          "
                        >
                          移除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <el-button
                    size="small"
                    type="text"
                    @click="toggleSelection('builderCs')"
                    >批量移除
                  </el-button>
                  <el-tag slot="reference" style="margin-top: 5px">
                    +{{ organizeForm.builderCsList.length - 1 }}
                  </el-tag>
                </el-popover>
                <el-button
                  type="info"
                  slot="append"
                  icon="el-icon-user"
                  @click="onOpenPeopleDialog('ccDetermine')"
                >
                </el-button>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="派单模式:" prop="seizeOrders" required>
              <el-radio-group v-model="sheetForm.seizeOrders">
                <el-radio :label="1">抢单受理</el-radio>
                <el-radio :label="0">均可受理</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="是否通知他人:" prop="sms">
              <el-radio-group v-model="sheetForm.sms">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <template v-if="sheetForm.sms">
            <el-col :xs="24" :sm="8" :md="8" :offset="0">
              <el-form-item
                label="接收人:"
                prop="recipient"
                :rules="[
                  {
                    required: sheetForm.sms == '1' ? true : false,
                    message: '请选择',
                  },
                ]"
              >
                <el-input v-model="sheetForm.recipient" placeholder="添加人员">
                  <el-tag
                    slot="prefix"
                    style="margin-top: 5px"
                    v-for="(tag, index) in organizeForm.recipientList"
                    :key="index"
                    closable
                    @close="handleClose('recipient', tag)"
                    v-show="index < 1"
                  >
                    {{ tag.name }}
                  </el-tag>

                  <el-popover
                    slot="prefix"
                    v-if="organizeForm.recipientList.length >= 2"
                    width="500"
                    trigger="click"
                  >
                    <el-input
                      v-model="organizeForm.recipientName"
                      placeholder="请输入接收人员姓名/组织名称"
                    >
                      <el-button
                        type="info"
                        slot="append"
                        icon="el-icon-search"
                        @click="search('recipient')"
                      >
                      </el-button>
                      <el-button
                        type="info"
                        slot="append"
                        icon="el-icon-close"
                        @click="clear('recipient')"
                      >
                      </el-button>
                    </el-input>
                    <el-table
                      ref="multipleTable"
                      tooltip-effect="dark"
                      @selection-change="handleSelectionChange"
                      :data="organizeForm.recipientListCopy"
                      max-height="240"
                    >
                      <el-table-column width="30" type="selection">
                      </el-table-column>
                      <el-table-column
                        min-width="70"
                        property="name"
                        label="姓名"
                      >
                      </el-table-column>
                      <el-table-column
                        min-width="180"
                        property="orgName"
                        label="组织"
                      >
                      </el-table-column>
                      <el-table-column
                        min-width="120"
                        property="mobilePhone"
                        label="电话"
                      >
                      </el-table-column>
                      <el-table-column fixed="right" label="操作" width="50">
                        <template slot-scope="scope">
                          <el-button
                            type="text"
                            size="small"
                            @click.native.prevent="
                              handleClose('recipient', scope.row)
                            "
                          >
                            移除
                          </el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                    <el-button
                      size="small"
                      type="text"
                      @click="toggleSelection('recipient')"
                      >批量移除
                    </el-button>
                    <el-tag slot="reference" style="margin-top: 5px">
                      +{{ organizeForm.recipientList.length - 1 }}
                    </el-tag>
                  </el-popover>
                  <el-button
                    type="info"
                    slot="append"
                    icon="el-icon-user"
                    @click="onOpenPeopleDialog('recipientDetermine')"
                  ></el-button>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :offset="0">
              <el-form-item
                label="发送内容:"
                prop="sendContent"
                :rules="[
                  {
                    required: sheetForm.sms == '1' ? true : false,
                    message: '请输入内容',
                    trigger: 'blur',
                  },
                ]"
              >
                <el-input
                  v-model="sheetForm.sendContent"
                  placeholder="请输入内容"
                  type="textarea"
                  :rows="4"
                  clearable
                  style="width: 100%"
                ></el-input>
              </el-form-item>
            </el-col>
          </template>
          <template v-if="sheetForm.special == 7">
            <el-col :xs="24" :sm="8" :md="8" :offset="0">
              <el-form-item label="是否共建共享:" prop="isShare">
                <el-radio-group v-model="sheetForm.isShare">
                  <el-radio :label="1">是</el-radio>
                  <el-radio :label="0">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </template>
        </el-row>
      </el-card>
      <el-card
        shadow="always"
        :body-style="{ padding: '10px' }"
        style="margin-top: 10px"
        v-if="sheetForm.isShare"
      >
        <div slot="header" class="card-title">
          <span>共建共享信息</span>
        </div>
        <el-row :gutter="10" type="flex" style="flex-wrap: wrap">
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="站址信息:" prop="createUser">
              <el-input
                placeholder="请输入站址信息"
                readonly
                style="width: 100%"
              >
              </el-input>
              <!-- <el-input
                v-model="sheetForm.createUser"
                placeholder="请输入建单人"
                readonly
                style="width: 100%"
              ></el-input> -->
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="站址名称:" prop="createUser">
              <el-input
                placeholder="请输入站址名称"
                readonly
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="站址经纬度:" prop="createUser">
              <el-input
                placeholder="请输入站址经纬度"
                readonly
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="站址类别:">
              <dict-select
                :value.sync="sheetForm.alarmType"
                :dictId="60002"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="基站ID:" prop="createUser">
              <el-input placeholder="请输入基站ID" readonly style="width: 100%">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="基站名称:" prop="createUser">
              <el-input
                placeholder="请输入基站名称"
                readonly
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="所属区域:" prop="createUser">
              <el-input
                placeholder="请输入所属区域"
                readonly
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="所属场景:">
              <dict-select
                :value.sync="sheetForm.alarmType"
                :dictId="60002"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="基站级别:" prop="createUser">
              <dict-select
                :value.sync="sheetForm.alarmType"
                :dictId="60002"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="基站类型:">
              <dict-select
                :value.sync="sheetForm.alarmType"
                :dictId="60002"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="告警ID和名称:" prop="createUser">
              <el-input
                placeholder="请输入告警ID和名称"
                readonly
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="告警类别:" prop="createUser">
              <el-input
                placeholder="请输入告警类别"
                readonly
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :offset="0">
            <el-form-item label="告警详情:" prop="createUser">
              <el-input
                placeholder="请输入告警详情"
                type="textarea"
                :rows="2"
                clearable
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="告警级别:" prop="createUser">
              <dict-select
                :value.sync="sheetForm.alarmType"
                :dictId="60002"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="告警发生时间:" prop="createTime" required>
              <el-date-picker
                v-model="sheetForm.createTime"
                type="datetime"
                placeholder="请选择告警发生时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                readonly
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="故障原因:" prop="createUser">
              <dict-select
                :value.sync="sheetForm.alarmType"
                :dictId="60002"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="交互方式:" prop="createUser">
              <dict-select
                :value.sync="sheetForm.alarmType"
                :dictId="60002"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
    </el-form>
    <dia-tissue-tree
      v-if="isDiaOrgsUserTree"
      :title="diaPeople.title"
      :visible.sync="diaPeople.visible"
      :showOrgsTree="diaPeople.showOrgsTree"
      :showContactUserTab="diaPeople.showContactUserTab"
      :showContactOrgTab="diaPeople.showContactOrgTab"
      @on-save="onSavePeople"
    />
    <!-- <dia-orgs-user-tree
      :title="diaPeople.title"
      :visible.sync="diaPeople.visible"
      :showOrgsTree="diaPeople.showOrgsTree"
      @on-save="onSavePeople"
    /> -->
    <el-dialog
      width="420px"
      title="附件选择"
      :visible.sync="attachmentDialogVisible"
      :close-on-click-modal="false"
      :destroy-on-close="true"
    >
      <file-upload
        @change="changeFileData"
        @cancel="closeAttachmentDialog"
      ></file-upload>
    </el-dialog>
    <el-dialog
      width="420px"
      title="影响业务列表附件选择"
      :visible.sync="incidenceDialogVisible"
      :close-on-click-modal="false"
      :destroy-on-close="true"
    >
      <file-upload
        @change="changeIncidenceFileData"
        @cancel="closeIncidenceDialog"
      ></file-upload>
    </el-dialog>
    <el-dialog
      width="500px"
      title="文件列表"
      :visible.sync="ngIncidenceFileDownloadVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <file-download
        @cancel="ngIncidenceFileDownloadVisible = false"
        :attachmentArr="ngIncidenceFileArr"
      ></file-download>
    </el-dialog>
    <el-dialog
      width="500px"
      title="文件列表"
      :visible.sync="ngAttachmentDownloadVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <file-download
        @cancel="ngAttachmentDownloadVisible = false"
        :attachmentArr="ngAttachmentArr"
      ></file-download>
    </el-dialog>
  </head-fixed-layout>
</template>

<script>
import { mapGetters } from "vuex";
import moment from "moment";

import HeadFixedLayout from "./components/HeadFixedLayout.vue";
import DictSelect from "./components/DictSelect.vue";
import FileUpload from "./components/FileUpload.vue";
import FileDownload from "./components/FileDownload.vue";
import DiaTissueTree from "@/plugin/backbone/components/common/DiaTissueTree";

import { apiDict } from "./api/CommonApi";
import { apiDeleteDraft } from "./api/WorkOrderMyDraft";
import {
  apiQueryCommonFlowWorkOrder,
  apiBuildCommonFlowSingle,
  apiSaveToCommonFlowDraft,
} from "./api/WorkOrderDraftEdit";
export default {
  name: "WorkOrderDraftEdit",
  components: {
    HeadFixedLayout,
    DictSelect,
    FileUpload,
    FileDownload,
    DiaTissueTree,
  },
  data() {
    var validHappenTime = (rule, value, callback) => {
      if (value === "" || value === null) {
        callback(new Error("请选择发生时间"));
      } else {
        let seconds = moment(
          this.sheetForm.createTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.sheetForm.happenTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        if (seconds < 0) {
          callback(new Error("“发生时间”不能晚于“建单时间”，请重新选择"));
        } else {
          callback();
        }
      }
    };
    return {
      networkTypeDictId: "",
      sheetForm: {
        createUser: "", //建单人
        sender: null, //建单人的登录名
        dept: "", //建单部门
        deptId: null, //建单部门Id
        createTime: "", //建单时间
        title: "", //工单主题
        source: "2", //工单来源
        happenTime: "", //发生时间
        urgent: "0", //紧急程度
        acceptTimeLimit: "30", //受理时限
        estimatedProTimeLimit: null, //预估处理时限
        special: null, //所属专业
        netType: "", //网络类型
        alarmType: "", //告警类别
        neType: "", //网元类型
        neName: "", //网元名称
        jobComment: "", //工作内容
        phenomenon: "", //故障现象
        incidenceFile: "", //影响业务列表
        incidence: "", //影响范围
        remarks: "", //备注
        // opticalCable: "",
        attachmentFile: "", //附件
        fileVirtual: "",
        builderZs: "",
        builderZsUserId: "",
        builderZsOrgId: "",
        builderZsUserName: "",
        builderZsOrgName: "",
        builderCs: "",
        builderCsUserId: "",
        builderCsOrgId: "",
        builderCsUserName: "",
        builderCsOrgName: "",
        sms: 0,
        seizeOrders: 0, //建单模式
        businessDown: "", //业务中断
        recipient: "",
        recipientUserName: "",
        recipientOrgName: "",
        recipientUserId: "",
        recipientOrgId: "",
        sendContent:
          "1、光缆：**， ××××光缆在××省××（网元）与××（网元）之间中断，影响××××波分系统，承载的SDH系统保护倒换，已经派单给省公司，并电话通知处理，现在该省正在利用备用纤芯倒接;(该段落OLP正常切换;)(分公司正在协调处理;)2、设备：××， ××××系统中断，初步怀疑是××网元××板卡问题，承载的业务目前不受影响，已经派单给省公司，并电话通知处理，现在分公司正在利用备用波道倒接；(该系统承载的SDH系统正常倒换，业务不受影响；)(该系统承载的SDH系统倒换不正常，已经影响业务；)(影响的业务有（无）××重保电路。)",
        isShare: 0, //是否共建共享
        agentManDetail: null,
        copyManDetail: null,
        recipientDetailUserName: null,
        cloudPoolType: "MEC",
        mecNodeName: "",
        faultLevel: "",
      },
      organizeForm: {
        builderZsList: [],
        builderZsListCopy: [],
        builderZsName: "",
        builderCsList: [],
        builderCsListCopy: [],
        builderCsName: "",
        recipientList: [],
        recipientListCopy: [],
        recipientName: "",
      },
      sheetFormRules: {
        createUser: [{ required: true, message: "请输入建单人" }],
        dept: [{ required: true, message: "请输入建单部门" }],
        createTime: [{ required: true, message: "请选择建单时间" }],
        title: [{ required: true, message: "工单主题不能为空" }],
        source: [{ required: true, message: "请选择工单来源" }],
        happenTime: [{ validator: validHappenTime, required: true }],
        urgent: [{ required: true, message: "请选择紧急程度" }],
        acceptTimeLimit: [{ required: true, message: "请输入受理时限" }],
        special: [{ required: true, message: "请选择所属专业" }],
        businessDown: [{ required: true, message: "请选择业务中断" }],
        phenomenon: [{ required: true, message: "请输入故障现象" }],
        builderZs: [{ required: true, message: "建单主送人不能为空" }],
        estimatedProTimeLimit: [
          { required: true, message: "请选择预估处理时限" },
        ],
        faultLevel: [{ required: true, message: "请选择工单优先级" }],
      },
      diaPeople: {
        visible: false,
        title: "",
        saveName: "",
        saveTitleMap: {
          lordSentDetermine: "建单人主送",
          ccDetermine: "建单人抄送",
          recipientDetermine: "接收人选择",
        },
        showOrgsTree: true,
        showOrgsTreeMap: {
          recipientDetermine: false,
          lordSentDetermine: true,
          ccDetermine: true,
        },
        showContactUserTab: false,
        showContactOrgTab: false,
        showContactUserTabMap: {
          lordSentDetermine: true,
          ccDetermine: true,
          recipientDetermine: true,
        },
        showContactOrgTabMap: {
          lordSentDetermine: true,
          ccDetermine: true,
        },
      },
      attachmentDialogVisible: false,
      isDiaOrgsUserTree: false,
      importForm: {
        //附件
        attachmentFileList: [],
        //影响业务列表附件
        incidenceFileList: [],
      },
      urgentOption: [],
      opticFiber: [],
      allInfos: [],
      sheetCommitLoading: false,
      sheetSaveLoading: false,
      incidenceDialogVisible: false,
      isOpticalCableShow: true,
      isOpticalCableIntShow: false,
      orgInfoData: {}, //组织信息数据缓存

      cgContentLoading: false,
      sheetCgSaveLoading: false,
      sheetCgDelLoading: false,
      sheetCgCommitLoading: false,
      ngIncidenceFileDownloadVisible: false,
      ngIncidenceFileArr: [],
      ngAttachmentDownloadVisible: false,
      ngAttachmentArr: [],

      route: null,
      //预估处理时限
      estimatedProTimeLimitOption: [
        { id: "1", name: "1小时" },
        { id: "3", name: "3小时" },
        { id: "4", name: "4小时" },
        { id: "5", name: "5小时" },
        { id: "24", name: "24小时" },
        { id: "48", name: "48小时" },
      ],
      sheetNo: null,
      woId: "",
      professionalType: "",
      networkTypeTop: "",
    };
  },

  computed: {
    ...mapGetters(["userInfo"]),
  },
  watch: {
    "organizeForm.builderZsList": {
      handler(newV) {
        if (newV.length > 0) {
          this.sheetForm.builderZs = "已选";
        } else {
          this.sheetForm.builderZs = "";
        }
      },
      deep: true,
    },
    "organizeForm.recipientList": {
      handler(newV) {
        if (newV.length > 0) {
          this.sheetForm.recipient = "已选";
        } else {
          this.sheetForm.recipient = "";
        }
      },
      deep: true,
    },
    "sheetForm.special": {
      handler(newV) {
        this.sheetForm.netType = "";
        if (newV == "20") {
          this.networkTypeDictId = "60200";
        } else if (newV == "19") {
          this.networkTypeDictId = "60201";
        } else if (newV == "1" || newV == "31") {
          this.networkTypeDictId = "60202";
        } else if (newV == "21") {
          this.networkTypeDictId = "60203";
        } else if (newV == "6") {
          this.networkTypeDictId = "60204";
        } else if (newV == "28" || newV == "32") {
          this.networkTypeDictId = "60205";
        } else {
          this.networkTypeDictId = "60003";
        }
      },
      deep: true,
    },
  },
  created() {
    this.woId = this.$route.query.woId;
    this.sheetNo = this.$route.query.sheetNo;
    this.query();
  },
  mounted() {
    this.sheetForm.createTime = moment(Date.now()).format(
      "YYYY-MM-DD HH:mm:ss"
    );

    this.getUrgentOption();
  },
  updated() {},
  methods: {
    getUrgentOption() {
      let param = {
        dictTypeCode: "10001",
      };
      apiDict(param)
        .then(res => {
          if (res.code == 200) {
            this.urgentOption = res?.data ?? [];
            this.urgentOption.reverse();
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    query() {
      this.cgContentLoading = true;
      let param = {
        woId: this.woId,
        sheetNo: this.sheetNo,
      };
      apiQueryCommonFlowWorkOrder(param)
        .then(res => {
          if (res.status == "0") {
            let self = this;
            let echoData = res?.data ?? {};

            this.sheetNo = echoData.sheetNo; //工单编号
            this.woId = echoData.woId; //工单ID
            this.professionalType = echoData.professionalType; //专业
            this.networkTypeTop = echoData.networkTypeTop;
            this.sheetForm.processInstId = echoData.processInstId; //流程实例id

            this.sheetForm.sender = echoData.sender; //建单人
            this.sheetForm.dept = echoData.senderDeptName; //建单部门
            this.sheetForm.createUser = echoData.senderName; //建单人中文名
            this.sheetForm.deptId = echoData.senderDept; //建单部门ID
            this.sheetForm.createTime = echoData.sheetCreateTime; //建单时间
            this.sheetForm.title = echoData.sheetTitle; //工单主题
            this.sheetForm.source = String(echoData.createType); //工单来源
            this.sheetForm.happenTime = echoData.alarmCreateTime; //发生时间
            this.sheetForm.urgent = String(echoData.emergencyLevel); //紧急程度
            this.sheetForm.acceptTimeLimit = echoData.acceptTimeLimit; //受理时限
            if (
              echoData.processTimeLimit != null &&
              echoData.processTimeLimit != ""
            ) {
              this.sheetForm.estimatedProTimeLimit = String(
                echoData.processTimeLimit
              ); //处理时限
            }
            if (
              echoData.professionalType != null &&
              echoData.professionalType != ""
            ) {
              this.sheetForm.special = String(echoData.professionalType); //所属专业
            }
            if (echoData.businessDown != null && echoData.businessDown != "") {
              this.sheetForm.businessDown = String(echoData.businessDown); //业务中断
            }
            if (echoData.networkType != null && echoData.networkType != "") {
              this.sheetForm.netType = String(echoData.networkType);
            }
            if (echoData.orgType != null && echoData.orgType != "") {
              this.sheetForm.alarmType = String(echoData.orgType); //告警类别
            }
            if (echoData.neType != null && echoData.neType != "") {
              this.sheetForm.neType = String(echoData.neType); //网元类型
            }
            this.sheetForm.neName = echoData.neName; //网元名称
            this.sheetForm.jobComment = echoData.workContent; //工作内容
            this.sheetForm.phenomenon = echoData.faultPhenomenon; //故障现象
            this.sheetForm.incidence = echoData.effectRange; //影响范围
            this.sheetForm.remarks = echoData.falutComment; //备注

            this.sheetForm.agentManDetail = echoData.agentManDetail;
            this.sheetForm.builderZsUserId = echoData.agentManId; //主送人ID
            this.sheetForm.builderZsOrgId = echoData.agentDeptCode; //主送组织ID
            this.sheetForm.builderZsUserName = echoData.agentMan; //主送人
            this.sheetForm.builderZsOrgName = echoData.agentDeptName; //主送组织

            this.sheetForm.copyManDetail = echoData.copyManDetail;
            this.sheetForm.builderCsUserId = echoData.copyManId; //抄送人ID
            this.sheetForm.builderCsOrgId = echoData.copyDeptCode; //抄送组织ID
            this.sheetForm.builderCsUserName = echoData.copyMan; //抄送人
            this.sheetForm.builderCsOrgName = echoData.copyDeptName; //抄送组织ID
            this.sheetForm.mecNodeName = echoData.mecNodeName;
            this.sheetForm.seizeOrders = echoData.seizeOrders; //建单模式

            this.sheetForm.recipientDetailUserName =
              echoData.recipientDetailUserName;
            this.sheetForm.recipientUserName = echoData.smsToUsername; //短信接收人
            this.sheetForm.recipientUserId = echoData.smsToUserid; //短信接收人ID

            this.sheetForm.sendContent =
              echoData?.sendContent ?? this.sheetForm.sendContent; //短信内容
            this.sheetForm.sms = echoData.isSendSms; //是否短信通知

            if (echoData.appendixFileUrl) {
              self.ngIncidenceFileArr = JSON.parse(echoData.appendixFileUrl);
            }
            if (echoData.appendix) {
              self.ngAttachmentArr = JSON.parse(echoData.appendix);
            }
            this.sheetForm.faultLevel = echoData.faultLevel;
            this.processing();
          }
          this.cgContentLoading = false;
        })
        .catch(error => {
          this.cgContentLoading = false;
          console.log(error);
        });
    },
    processing() {
      //主送人数据处理
      if (
        this.sheetForm.builderZsUserId != null &&
        this.sheetForm.builderZsUserId != "" &&
        this.sheetForm.agentManDetail != null &&
        this.sheetForm.agentManDetail != ""
      ) {
        let usersCheckedName = [];
        let usersCheckedOrgName = [];
        let usersCheckedMobilePhone = [];
        let usersCheckedId = this.sheetForm.builderZsUserId.split(",");
        let usersCheckedManDetail = this.sheetForm.agentManDetail.split(",");
        usersCheckedManDetail.forEach(element => {
          let userCheckedManDetail = element.split("-");
          usersCheckedName.push(userCheckedManDetail[0]);
          usersCheckedOrgName.push(userCheckedManDetail[1]);
          usersCheckedMobilePhone.push(userCheckedManDetail[2]);
        });
        for (var i = 0; i < usersCheckedId.length; i++) {
          this.organizeForm.builderZsList.push({
            bz: "user",
            id: usersCheckedId[i],
            name: usersCheckedName[i],
            orgName: usersCheckedOrgName[i],
            mobilePhone: usersCheckedMobilePhone[i],
          });
        }
        this.organizeForm.builderZsListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.builderZsList)
        );
      }
      //主送组织数据处理
      if (
        this.sheetForm.builderZsOrgId != null &&
        this.sheetForm.builderZsOrgId != "" &&
        this.sheetForm.builderZsOrgName != null &&
        this.sheetForm.builderZsOrgName != ""
      ) {
        let orgsCheckedName = this.sheetForm.builderZsOrgName.split(",");
        let orgsCheckedId = this.sheetForm.builderZsOrgId.split(",");
        for (var j = 0; j < orgsCheckedId.length; j++) {
          this.organizeForm.builderZsList.push({
            bz: "org",
            id: orgsCheckedId[j],
            orgName: orgsCheckedName[j],
          });
        }
        this.organizeForm.builderZsListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.builderZsList)
        );
      }
      //抄送人数据处理
      if (
        this.sheetForm.builderCsUserId != null &&
        this.sheetForm.builderCsUserId != "" &&
        this.sheetForm.copyManDetail != null &&
        this.sheetForm.copyManDetail != ""
      ) {
        let usersCheckedName = [];
        let usersCheckedOrgName = [];
        let usersCheckedMobilePhone = [];
        let usersCheckedId = this.sheetForm.builderCsUserId.split(",");
        let usersCheckedManDetail = this.sheetForm.copyManDetail.split(",");
        usersCheckedManDetail.forEach(element => {
          let userCheckedManDetail = element.split("-");
          usersCheckedName.push(userCheckedManDetail[0]);
          usersCheckedOrgName.push(userCheckedManDetail[1]);
          usersCheckedMobilePhone.push(userCheckedManDetail[2]);
        });
        for (var k = 0; k < usersCheckedId.length; k++) {
          this.organizeForm.builderCsList.push({
            bz: "user",
            id: usersCheckedId[k],
            name: usersCheckedName[k],
            orgName: usersCheckedOrgName[k],
            mobilePhone: usersCheckedMobilePhone[k],
          });
        }
        this.organizeForm.builderCsListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.builderCsList)
        );
      }
      //抄送组织数据处理
      if (
        this.sheetForm.builderCsOrgId != null &&
        this.sheetForm.builderCsOrgId != "" &&
        this.sheetForm.builderCsOrgName != null &&
        this.sheetForm.builderCsOrgName != ""
      ) {
        let orgsCheckedName = this.sheetForm.builderCsOrgName.split(",");
        let orgsCheckedId = this.sheetForm.builderCsOrgId.split(",");
        for (var l = 0; l < orgsCheckedId.length; l++) {
          this.organizeForm.builderCsList.push({
            bz: "org",
            id: orgsCheckedId[l],
            orgName: orgsCheckedName[l],
          });
        }
        this.organizeForm.builderCsListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.builderCsList)
        );
      }
      //接收人数据处理
      if (
        this.sheetForm.recipientUserId != null &&
        this.sheetForm.recipientUserId != "" &&
        this.sheetForm.recipientDetailUserName != null &&
        this.sheetForm.recipientDetailUserName != ""
      ) {
        let usersCheckedName = [];
        let usersCheckedOrgName = [];
        let usersCheckedMobilePhone = [];
        let usersCheckedId = this.sheetForm.recipientUserId.split(",");
        let usersCheckedManDetail = this.sheetForm.recipientDetailUserName.split(
          ","
        );
        usersCheckedManDetail.forEach(element => {
          let userCheckedManDetail = element.split("-");
          usersCheckedName.push(userCheckedManDetail[0]);
          usersCheckedOrgName.push(userCheckedManDetail[1]);
          usersCheckedMobilePhone.push(userCheckedManDetail[2]);
        });
        for (var a = 0; a < usersCheckedId.length; a++) {
          this.organizeForm.recipientList.push({
            bz: "user",
            id: usersCheckedId[a],
            name: usersCheckedName[a],
            orgName: usersCheckedOrgName[a],
            mobilePhone: usersCheckedMobilePhone[a],
          });
        }
        this.organizeForm.recipientListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.recipientList)
        );
      }
    },
    onSheetSave() {
      this.entering();
      this.$confirm("是否保存？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // this.$refs.sheetForm.validate(valid => {
          //   if (valid) {
          if (this.sheetForm.title != "" && this.sheetForm.title != null) {
            let formData = new FormData();
            if (this.importForm.attachmentFileList.length > 0) {
              for (let item of this.importForm.attachmentFileList) {
                formData.append("orderFiles", item.raw);
              }
            }
            if (this.importForm.incidenceFileList.length > 0) {
              for (let item of this.importForm.incidenceFileList) {
                formData.append("affectFiles", item.raw);
              }
            }
            this.sheetCgSaveLoading = true;
            let self = this;
            let param = {
              sheetInfo: {
                woId: self.woId,
                networkTypeTop: 5, //集团通用流程
                sender: this.sheetForm.sender, //建单人
                senderDeptName: this.sheetForm.dept, //建单部门
                senderName: this.sheetForm.createUser, //建单人中文名
                senderDept: this.sheetForm.deptId, //建单部门ID
                sheetCreateTime: this.sheetForm.createTime, //建单时间
                sheetTitle: this.sheetForm.title, //工单主题
                createType: this.sheetForm.source, //工单来源
                alarmCreateTime: this.sheetForm.happenTime, //发生时间
                emergencyLevel: this.sheetForm.urgent, //紧急程度
                acceptTimeLimit: this.sheetForm.acceptTimeLimit, //受理时限
                processTimeLimit: this.sheetForm.estimatedProTimeLimit, //处理时限
                professionalType: this.sheetForm.special, //所属专业
                businessDown: this.sheetForm.businessDown, //业务中断
                networkType: self.sheetForm.netType, //== "25100507" ? "一干" : "国际", //网络类型, //网络类型
                // opticFiberCableSegment: this.sheetForm.opticalCable, //光缆名称
                mecNodeName: this.sheetForm.mecNodeName,
                cloudPoolType:
                  this.sheetForm.special == "26"
                    ? this.sheetForm.cloudPoolType
                    : "", //云池类型
                orgType: this.sheetForm.alarmType, //告警类别
                neType: this.sheetForm.neType, //网元类型
                neName: this.sheetForm.neName, //网元名称
                workContent: this.sheetForm.jobComment, //工作内容
                faultPhenomenon: this.sheetForm.phenomenon, //故障现象
                effectRange: this.sheetForm.incidence, //影响范围
                falutComment: this.sheetForm.remarks, //备注
                agentDeptCode: this.sheetForm.builderZsOrgId, //主送是组织 传入组织ID
                agentManId: this.sheetForm.builderZsUserId, //主送是人员 传入人员ID
                agentMan: this.sheetForm.builderZsUserName, //主送人员 中文名称
                agentDeptName: this.sheetForm.builderZsOrgName, //主送部门 中文名称
                sheetStatus: 1, //1:保存到草稿 2:提交到待办
                seizeOrders: this.sheetForm.seizeOrders, //建单模式
                copyDeptCode: this.sheetForm.builderCsOrgId, //抄送是组织 传入组织ID
                copyManId: this.sheetForm.builderCsUserId, //抄送是用户 传入用户ID
                copyMan: this.sheetForm.builderCsUserName, //抄送人员中文名称
                copyDeptName: this.sheetForm.builderCsOrgName, //抄送部门中文名称
                // noticeMan: this.sheetForm.notifierUserName,
                // noticeManId: this.sheetForm.notifierUserId,
                // noticeDeptCode: this.sheetForm.notifierOrgId,
                // noticeDeptName: this.sheetForm.notifierOrgName,
                isSendSms: this.sheetForm.sms, //是否短信通知
                smsToUserid: this.sheetForm.recipientUserId,
                smsToUsername: this.sheetForm.recipientUserName,
                smsToDeptCode: this.sheetForm.recipientOrgId,
                smsToDeptName: this.sheetForm.recipientOrgName,
                sendContent:
                  this.sheetForm.sms == "0" ? null : this.sheetForm.sendContent,
                agentManDetail: this.sheetForm.agentManDetail,
                copyManDetail: this.sheetForm.copyManDetail,
                recipientDetailUserName: self.sheetForm.recipientDetailUserName,
                isShare:
                  this.sheetForm.special == "7" ? this.sheetForm.isShare : null,
                faultLevel: this.sheetForm.faultLevel,
              },
            };
            formData.append("jsonParam", JSON.stringify(param));
            //更新草稿
            apiSaveToCommonFlowDraft(formData)
              .then(res => {
                if (res.status == "0") {
                  this.$message.success("保存成功");
                  this.closeAndTurnAround("backbone_myDraft");
                } else {
                  this.$message.error("保存失败");
                }
                this.sheetCgSaveLoading = false;
              })
              .catch(error => {
                console.log(error);
                this.$message.error("保存失败");
                this.sheetCgSaveLoading = false;
              });
          } else {
            this.$message.warning("请填写主题后在做保存！");
            return false;
          }
          // });
        })
        .catch(error => {
          console.log(error);
        });
    },
    onSheetDel() {
      this.$confirm("是否删除工单草稿？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "error",
      }).then(() => {
        this.sheetCgDelLoading = true;
        let param = {
          woIds: this.woId.split(","),
        };
        apiDeleteDraft(param)
          .then(res => {
            if (res.status == "0") {
              this.$message.success("删除成功");
              this.closeAndTurnAround("backbone_myDraft");
            } else {
              this.$message.error("删除失败");
            }
            this.sheetCgDelLoading = false;
          })
          .catch(error => {
            console.log(error);
            this.sheetCgDelLoading = false;
          });
      });
    },
    onSheetSubmit() {
      this.entering();
      this.$confirm("是否提交工单？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$refs.sheetForm.validate(valid => {
            if (valid) {
              let formData = new FormData();
              if (this.importForm.attachmentFileList.length > 0) {
                for (let item of this.importForm.attachmentFileList) {
                  formData.append("orderFiles", item.raw);
                }
              }
              if (this.importForm.incidenceFileList.length > 0) {
                for (let item of this.importForm.incidenceFileList) {
                  formData.append("affectFiles", item.raw);
                }
              }
              this.sheetCgCommitLoading = true;
              let self = this;
              let param = {
                sheetInfo: {
                  woId: self.woId,
                  networkTypeTop: 5, //集团通用流程
                  sender: this.sheetForm.sender, //建单人
                  senderDeptName: this.sheetForm.dept, //建单部门
                  senderName: this.sheetForm.createUser, //建单人中文名
                  senderDept: this.sheetForm.deptId, //建单部门ID
                  sheetCreateTime: this.sheetForm.createTime, //建单时间
                  sheetTitle: this.sheetForm.title, //工单主题
                  createType: this.sheetForm.source, //工单来源
                  alarmCreateTime: this.sheetForm.happenTime, //发生时间
                  emergencyLevel: this.sheetForm.urgent, //紧急程度
                  acceptTimeLimit: this.sheetForm.acceptTimeLimit, //受理时限
                  processTimeLimit: this.sheetForm.estimatedProTimeLimit, //处理时限
                  professionalType: this.sheetForm.special, //所属专业
                  businessDown: this.sheetForm.businessDown, //业务中断
                  networkType: self.sheetForm.netType, //== "25100507" ? "一干" : "国际", //网络类型, //网络类型
                  // opticFiberCableSegment: this.sheetForm.opticalCable, //光缆名称
                  mecNodeName: this.sheetForm.mecNodeName,
                  cloudPoolType:
                    this.sheetForm.special == "26"
                      ? this.sheetForm.cloudPoolType
                      : "", //云池类型
                  orgType: this.sheetForm.alarmType, //告警类别
                  neType: this.sheetForm.neType, //网元类型
                  neName: this.sheetForm.neName, //网元名称
                  workContent: this.sheetForm.jobComment, //工作内容
                  faultPhenomenon: this.sheetForm.phenomenon, //故障现象
                  effectRange: this.sheetForm.incidence, //影响范围
                  falutComment: this.sheetForm.remarks, //备注
                  agentDeptCode: this.sheetForm.builderZsOrgId, //主送是组织 传入组织ID
                  agentManId: this.sheetForm.builderZsUserId, //主送是人员 传入人员ID
                  agentMan: this.sheetForm.builderZsUserName, //主送人员 中文名称
                  agentDeptName: this.sheetForm.builderZsOrgName, //主送部门 中文名称
                  sheetStatus: 2, //1:保存到草稿 2:提交到待办
                  seizeOrders: this.sheetForm.seizeOrders, //建单模式
                  copyDeptCode: this.sheetForm.builderCsOrgId, //抄送是组织 传入组织ID
                  copyManId: this.sheetForm.builderCsUserId, //抄送是用户 传入用户ID
                  copyMan: this.sheetForm.builderCsUserName, //抄送人员中文名称
                  copyDeptName: this.sheetForm.builderCsOrgName, //抄送部门中文名称
                  isSendSms: this.sheetForm.sms, //是否短信通知
                  smsToUserid: this.sheetForm.recipientUserId,
                  smsToUsername: this.sheetForm.recipientUserName,
                  smsToDeptCode: this.sheetForm.recipientOrgId,
                  smsToDeptName: this.sheetForm.recipientOrgName,
                  sendContent:
                    this.sheetForm.sms == "0"
                      ? null
                      : this.sheetForm.sendContent,
                  agentManDetail: this.sheetForm.agentManDetail,
                  copyManDetail: this.sheetForm.copyManDetail,
                  recipientDetailUserName:
                    self.sheetForm.recipientDetailUserName,
                  isShare:
                    this.sheetForm.special == "7"
                      ? this.sheetForm.isShare
                      : null,
                  faultLevel: this.sheetForm.faultLevel,
                },
              };
              formData.append("jsonParam", JSON.stringify(param));
              apiBuildCommonFlowSingle(formData)
                .then(res => {
                  if (res.status == "0") {
                    this.$message.success("工单提交成功");
                    this.resetSheetForm();
                    this.closeAndTurnAround(
                      "backbone_haveToDoRepairOrder_inOffice"
                    );
                  } else {
                    this.$message.error("工单提交失败");
                  }
                  this.sheetCgCommitLoading = false;
                })
                .catch(error => {
                  console.log(error);
                  this.$message.error("工单提交失败");
                  this.sheetCgCommitLoading = false;
                });
            } else {
              return false;
            }
          });
        })
        .catch(() => {});
    },
    resetSheetForm() {
      this.sheetForm = {
        ...this.$options.data,
      };
      this.importForm.attachmentFileList = [];
    },
    onOpenPeopleDialog(diaSaveName) {
      this.isDiaOrgsUserTree = false;
      this.diaPeople.title = this.diaPeople.saveTitleMap[diaSaveName];
      this.diaPeople.saveName = diaSaveName;
      this.diaPeople.showOrgsTree =
        this.diaPeople.showOrgsTreeMap[diaSaveName] ?? true;
      this.diaPeople.showContactUserTab =
        this.diaPeople.showContactUserTabMap[diaSaveName] ?? false;
      this.diaPeople.showContactOrgTab =
        this.diaPeople.showContactOrgTabMap[diaSaveName] ?? false;
      this.diaPeople.visible = true;
      this.$nextTick(() => {
        this.isDiaOrgsUserTree = true;
      });
    },
    onSavePeople(val) {
      this[this.diaPeople.saveName](val);
    },
    //建单人主送确定
    lordSentDetermine({
      usersChecked,
      orgsChecked,
      selectionUser,
      contactSelectionUser,
      contactSelectionOrg,
    }) {
      if (contactSelectionUser && contactSelectionUser.length > 0) {
        contactSelectionUser.map(item => {
          let zs = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.userName;
          });
          if (zs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderZsList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (selectionUser && selectionUser.length > 0) {
        selectionUser.map(item => {
          let zs = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.userName;
          });
          if (zs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderZsList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (usersChecked && usersChecked.length > 0) {
        usersChecked.map(item => {
          let zs = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.id;
          });
          if (zs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderZsList.push({
              bz: "user",
              id: item.id,
              name: item.name,
              orgName: item.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (contactSelectionOrg && contactSelectionOrg.length > 0) {
        contactSelectionOrg.map(item => {
          let zsOrg = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.orgId;
          });
          if (zsOrg > -1) {
            this.$message({
              message: "选择的组织已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderZsList.push({
              bz: "org",
              id: item.orgId + "",
              orgName: item.fullOrgName,
            });
          }
        });
      }
      if (orgsChecked && orgsChecked.length > 0) {
        orgsChecked.map(item => {
          let zsOrg = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.id;
          });
          let zsOrg2 = this.organizeForm.builderZsList.findIndex(val => {
            return val.bz === "org";
          });
          let flag = false;
          if (zsOrg > -1) {
            this.$message({
              message: "选择的组织已存在",
              type: "warning",
            });
            flag = true;
          }
          if (zsOrg2 > -1 && this.mainProfessionalType == "IT云") {
            this.$message({
              message: "it云只能选择一个组织",
              type: "warning",
            });
            flag = true;
          }
          if (!flag) {
            this.organizeForm.builderZsList.push({
              bz: "org",
              id: item.id,
              orgName: item.name,
            });
          }
        });
      }

      this.organizeForm.builderZsListCopy = JSON.parse(
        JSON.stringify(this.organizeForm.builderZsList)
      );
    },
    //抄送人确定
    ccDetermine({
      usersChecked,
      orgsChecked,
      selectionUser,
      contactSelectionUser,
      contactSelectionOrg,
    }) {
      if (contactSelectionUser && contactSelectionUser.length > 0) {
        contactSelectionUser.map(item => {
          let cs = this.organizeForm.builderCsList.findIndex(val => {
            return val.id === item.userName;
          });
          if (cs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderCsList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (selectionUser && selectionUser.length > 0) {
        selectionUser.map(item => {
          let cs = this.organizeForm.builderCsList.findIndex(val => {
            return val.id === item.userName;
          });
          if (cs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderCsList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (usersChecked && usersChecked.length > 0) {
        usersChecked.map(item => {
          let cs = this.organizeForm.builderCsList.findIndex(val => {
            return val.id === item.id;
          });
          if (cs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderCsList.push({
              bz: "user",
              id: item.id,
              name: item.name,
              orgName: item.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (contactSelectionOrg && contactSelectionOrg.length > 0) {
        contactSelectionOrg.map(item => {
          let csOrg = this.organizeForm.builderCsList.findIndex(val => {
            return val.id === item.orgId;
          });
          if (csOrg > -1) {
            this.$message({
              message: "选择的组织已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderCsList.push({
              bz: "org",
              id: item.orgId + "",
              orgName: item.fullOrgName,
            });
          }
        });
      }
      if (orgsChecked && orgsChecked.length > 0) {
        orgsChecked.map(item => {
          let csOrg = this.organizeForm.builderCsList.findIndex(val => {
            return val.bz === "org";
          });
          let csOrg2 = this.organizeForm.builderCsList.findIndex(val => {
            return val.id === item.id;
          });
          let flag = false;
          if (csOrg > -1 && this.mainProfessionalType == "IT云") {
            this.$message({
              message: "it云只能选择一个组织",
              type: "warning",
            });
            flag = true;
          }
          if (csOrg2 > -1) {
            this.$message({
              message: "选择的组织已存在",
              type: "warning",
            });
            flag = true;
          }

          if (!flag) {
            this.organizeForm.builderCsList.push({
              bz: "org",
              id: item.id,
              orgName: item.name,
            });
          }
        });
      }
      this.organizeForm.builderCsListCopy = JSON.parse(
        JSON.stringify(this.organizeForm.builderCsList)
      );
    },
    //接收人确定
    recipientDetermine({
      usersChecked,
      orgsChecked,
      selectionUser,
      contactSelectionUser,
    }) {
      if (contactSelectionUser && contactSelectionUser.length > 0) {
        contactSelectionUser.map(item => {
          let rec = this.organizeForm.recipientList.findIndex(val => {
            return val.id === item.userName;
          });
          if (rec > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.recipientList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (selectionUser && selectionUser.length > 0) {
        selectionUser.map(item => {
          let rec = this.organizeForm.recipientList.findIndex(val => {
            return val.id === item.userName;
          });
          if (rec > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.recipientList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      } else if (usersChecked && usersChecked.length > 0) {
        usersChecked.map(item => {
          let rec = this.organizeForm.recipientList.findIndex(val => {
            return val.id === item.id;
          });
          if (rec > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.recipientList.push({
              bz: "user",
              id: item.id,
              name: item.name,
              orgName: item.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      this.organizeForm.recipientListCopy = JSON.parse(
        JSON.stringify(this.organizeForm.recipientList)
      );
    },
    // recipientDetermine({
    //   usersChecked,
    //   orgsChecked,
    //   selectionUser,
    //   contactSelectionUser,
    // }) {
    //   if (contactSelectionUser && contactSelectionUser.length > 0) {
    //     contactSelectionUser.map(item => {
    //       this.organizeForm.recipientList.push({
    //         bz: "user",
    //         id: item.userName,
    //         name: item.trueName,
    //         orgName: item.orgEntity.orgName,
    //         mobilePhone: item.mobilePhone,
    //       });
    //     });
    //   }
    //   if (selectionUser && selectionUser.length > 0) {
    //     selectionUser.map(item => {
    //       this.organizeForm.recipientList.push({
    //         bz: "user",
    //         id: item.userName,
    //         name: item.trueName,
    //         orgName: item.orgEntity.orgName,
    //         mobilePhone: item.mobilePhone,
    //       });
    //     });
    //   } else if (usersChecked && usersChecked.length > 0) {
    //     usersChecked.map(item => {
    //       this.organizeForm.recipientList.push({
    //         bz: "user",
    //         id: item.id,
    //         name: item.name,
    //         orgName: item.orgName,
    //         mobilePhone: item.mobilePhone,
    //       });
    //     });
    //   }
    //   this.organizeForm.recipientListCopy = JSON.parse(
    //     JSON.stringify(this.organizeForm.recipientList)
    //   );
    // },
    opticalCableSelect() {},
    //附件选择
    attachmentBrowse() {
      this.attachmentDialogVisible = true;
    },
    changeFileData(data) {
      this.sheetForm.attachmentFile = data.fileName;
      this.importForm.attachmentFileList = data.attachmentFileList;
      this.attachmentDialogVisible = false;
    },
    closeAttachmentDialog() {
      this.attachmentDialogVisible = false;
    },
    //影响业务列表选择
    incidenceBrowse() {
      this.incidenceDialogVisible = true;
    },
    changeIncidenceFileData(data) {
      this.sheetForm.incidenceFile = data.fileName;
      this.importForm.incidenceFileList = data.attachmentFileList;
      this.incidenceDialogVisible = false;
    },
    closeIncidenceDialog() {
      this.incidenceDialogVisible = false;
    },
    ngIncidenceBrowse() {
      this.ngIncidenceFileDownloadVisible = true;
    },
    ngAttachmentBrowse() {
      this.ngAttachmentDownloadVisible = true;
    },
    stitchingAlgorithm(orgName, userName) {
      if (null == orgName) orgName = "";
      if (null == userName) userName = "";
      if (orgName.length !== 0 && userName.length !== 0) {
        return orgName + "," + userName;
      } else {
        if (orgName.length !== 0) {
          return orgName;
        } else if (userName.length !== 0) {
          return userName;
        } else {
          return "";
        }
      }
    },
    closeAndTurnAround(pageName) {
      let self = this;
      self.$store.dispatch("tagsView/delView", self.route).then(() => {
        self.$destroy();
      });
      self.$router.push({
        name: pageName,
      });
    },
    //多选移除
    toggleSelection(val) {
      if (this.multipleSelection == 0) {
        this.$message({
          type: "warning",
          message: "还没有选择移除的选项，请先选择要移除的选项！",
        });
        return;
      } else {
        this.multipleSelection.forEach(row => {
          this.handleClose(val, row);
        });
      }
      this.multipleSelection;
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    //移除对象
    handleClose(val, tag) {
      if (val == "builderZs") {
        this.organizeForm.builderZsList.splice(
          this.arrayIndex(this.organizeForm.builderZsList, tag),
          1
        );
        this.organizeForm.builderZsListCopy.splice(
          this.arrayIndex(this.organizeForm.builderZsListCopy, tag),
          1
        );
      }
      if (val == "builderCs") {
        this.organizeForm.builderCsList.splice(
          this.arrayIndex(this.organizeForm.builderCsList, tag),
          1
        );
        this.organizeForm.builderCsListCopy.splice(
          this.arrayIndex(this.organizeForm.builderCsListCopy, tag),
          1
        );
      }
      if (val == "notifier") {
        this.organizeForm.notifierList.splice(
          this.arrayIndex(this.organizeForm.notifierList, tag),
          1
        );
        this.organizeForm.notifierListCopy.splice(
          this.arrayIndex(this.organizeForm.notifierListCopy, tag),
          1
        );
      }
      if (val == "recipient") {
        this.organizeForm.recipientList.splice(
          this.arrayIndex(this.organizeForm.recipientList, tag),
          1
        );
        this.organizeForm.recipientListCopy.splice(
          this.arrayIndex(this.organizeForm.recipientListCopy, tag),
          1
        );
      }
    },
    //找对应对象的索引值
    arrayIndex(val1, val2) {
      for (var i = 0, len = val1.length; i < len; i++) {
        var tag = val1[i];
        if (tag.id == val2.id) {
          return i;
        }
      }
    },
    //搜索
    search(val) {
      if (val == "builderZs" && this.organizeForm.builderZsList != null) {
        this.organizeForm.builderZsListCopy = [];
        this.organizeForm.builderZsList.forEach(row => {
          if (
            row.name == this.organizeForm.builderZsName ||
            row.orgName == this.organizeForm.builderZsName
          ) {
            this.organizeForm.builderZsListCopy.push(row);
          }
        });
      }
      if (val == "builderCs" && this.organizeForm.builderCsList != null) {
        this.organizeForm.builderCsListCopy = [];
        this.organizeForm.builderCsList.forEach(row => {
          if (
            row.name == this.organizeForm.builderCsName ||
            row.orgName == this.organizeForm.builderCsName
          ) {
            this.organizeForm.builderCsListCopy.push(row);
          }
        });
      }
      if (val == "notifier" && this.organizeForm.notifierList != null) {
        this.organizeForm.notifierListCopy = [];
        this.organizeForm.notifierList.forEach(row => {
          if (
            row.name == this.organizeForm.notifierName ||
            row.orgName == this.organizeForm.notifierName
          ) {
            this.organizeForm.notifierListCopy.push(row);
          }
        });
      }
      if (val == "recipient" && this.organizeForm.recipientList != null) {
        this.organizeForm.recipientListCopy = [];
        this.organizeForm.recipientList.forEach(row => {
          if (
            row.name == this.organizeForm.recipientName ||
            row.orgName == this.organizeForm.recipientName
          ) {
            this.organizeForm.recipientListCopy.push(row);
          }
        });
      }
    },
    //清除搜索项
    clear(val) {
      if (val == "builderZs") {
        this.organizeForm.builderZsName = "";
        this.organizeForm.builderZsListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.builderZsList)
        );
      }
      if (val == "builderCs") {
        this.organizeForm.builderCsName = "";
        this.organizeForm.builderCsListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.builderCsList)
        );
      }
      if (val == "notifier") {
        this.organizeForm.notifierName = "";
        this.organizeForm.notifierListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.notifierList)
        );
      }
      if (val == "recipient") {
        this.organizeForm.recipientName = "";
        this.organizeForm.recipientListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.recipientList)
        );
      }
    },
    entering() {
      if (
        this.organizeForm.builderZsList &&
        this.organizeForm.builderZsList.length > 0
      ) {
        let userList = this.organizeForm.builderZsList.filter((item, index) => {
          return item.bz == "user";
        });
        let orgList = this.organizeForm.builderZsList.filter((item, index) => {
          return item.bz == "org";
        });
        if (userList && userList.length > 0) {
          let usersCheckedName = userList.map(item => {
            return item.name;
          });
          this.sheetForm.builderZsUserName = usersCheckedName.join(",");
          let usersCheckedId = userList.map(item => {
            return item.id;
          });
          this.sheetForm.builderZsUserId = usersCheckedId.join(",");
          const userDetailName = userList.map(item => {
            return item.name + "-" + item.orgName + "-" + item.mobilePhone;
          });
          this.sheetForm.agentManDetail = userDetailName.join(",");
        }
        if (orgList && orgList.length > 0) {
          let orgsCheckedName = orgList.map(item => {
            return item.orgName;
          });
          this.sheetForm.builderZsOrgName = orgsCheckedName.join(",");
          let orgsCheckedId = orgList.map(item => {
            return item.id;
          });
          this.sheetForm.builderZsOrgId = orgsCheckedId.join(",");
        }
      } else {
        this.sheetForm.builderZsUserName = "";
        this.sheetForm.builderZsUserId = "";
        this.sheetForm.agentManDetail = "";
        this.sheetForm.builderZsOrgName = "";
        this.sheetForm.builderZsOrgId = "";
      }
      if (
        this.organizeForm.builderCsList &&
        this.organizeForm.builderCsList.length > 0
      ) {
        let userList = this.organizeForm.builderCsList.filter((item, index) => {
          return item.bz == "user";
        });
        let orgList = this.organizeForm.builderCsList.filter((item, index) => {
          return item.bz == "org";
        });
        if (userList && userList.length > 0) {
          let usersCheckedName = userList.map(item => {
            return item.name;
          });
          this.sheetForm.builderCsUserName = usersCheckedName.join(",");
          let usersCheckedId = userList.map(item => {
            return item.id;
          });
          this.sheetForm.builderCsUserId = usersCheckedId.join(",");
          const userDetailName = userList.map(item => {
            return item.name + "-" + item.orgName + "-" + item.mobilePhone;
          });
          this.sheetForm.copyManDetail = userDetailName.join(",");
        }
        if (orgList && orgList.length > 0) {
          let orgsCheckedName = orgList.map(item => {
            return item.orgName;
          });
          this.sheetForm.builderCsOrgName = orgsCheckedName.join(",");
          let orgsCheckedId = orgList.map(item => {
            return item.id;
          });
          this.sheetForm.builderCsOrgId = orgsCheckedId.join(",");
        }
      } else {
        this.sheetForm.builderCsUserName = "";
        this.sheetForm.builderCsUserId = "";
        this.sheetForm.copyManDetail = "";
        this.sheetForm.builderCsOrgName = "";
        this.sheetForm.builderCsOrgId = "";
      }
      if (
        this.organizeForm.notifierList &&
        this.organizeForm.notifierList.length > 0
      ) {
        let userList = this.organizeForm.notifierList.filter((item, index) => {
          return item.bz == "user";
        });
        let orgList = this.organizeForm.notifierList.filter((item, index) => {
          return item.bz == "org";
        });
        if (userList && userList.length > 0) {
          let usersCheckedName = userList.map(item => {
            return item.name;
          });
          this.sheetForm.notifierUserName = usersCheckedName.join(",");
          let usersCheckedId = userList.map(item => {
            return item.id;
          });
          this.sheetForm.notifierUserId = usersCheckedId.join(",");
          const userDetailName = userList.map(item => {
            return item.name + "-" + item.orgName + "-" + item.mobilePhone;
          });
          this.sheetForm.notifierUserDetail = userDetailName.join(",");
        }
        if (orgList && orgList.length > 0) {
          let orgsCheckedName = orgList.map(item => {
            return item.orgName;
          });
          this.sheetForm.notifierOrgName = orgsCheckedName.join(",");
          let orgsCheckedId = orgList.map(item => {
            return item.id;
          });
          this.sheetForm.notifierUserId = orgsCheckedId.join(",");
        }
      }
      if (
        this.organizeForm.recipientList &&
        this.organizeForm.recipientList.length > 0
      ) {
        let userList = this.organizeForm.recipientList.filter((item, index) => {
          return item.bz == "user";
        });
        if (userList && userList.length > 0) {
          let usersCheckedName = userList.map(item => {
            return item.name;
          });
          this.sheetForm.recipientUserName = usersCheckedName.join(",");
          let usersCheckedId = userList.map(item => {
            return item.id;
          });
          this.sheetForm.recipientUserId = usersCheckedId.join(",");
          const userDetailName = userList.map(item => {
            return item.name + "-" + item.orgName + "-" + item.mobilePhone;
          });
          this.sheetForm.recipientDetailUserName = userDetailName.join(",");
        }
      } else {
        this.sheetForm.recipientUserName = "";
        this.sheetForm.recipientUserId = "";
        this.sheetForm.recipientDetailUserName = "";
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.draft-eidt-page {
  .head-title {
    font-size: 24px;
    line-height: 28px;
  }
  .head-handle-wrap {
    text-align: right;
  }
  .card-title {
    font-size: 16px;
    letter-spacing: 0;
    padding-left: 10px;
  }

  ::v-deep .el-tree {
    padding-top: 15px;
    padding-left: 10px;
    // 不可全选样式
    .el-tree-node {
      .is-leaf + .el-checkbox .el-checkbox__inner {
        display: inline-block;
      }
      .el-checkbox .el-checkbox__inner {
        display: none;
      }
    }
  }
}
</style>
