<template>
  <head-content-layout class="page-wrap">
    <template #header>
      <el-form
        label-width="90px"
        :inline="true"
        class="demo-form-inline"
        label-position="right"
      >
        <el-row>
          <el-form-item label="" label-width="0">
            <el-input
              v-model.trim="form.keyword"
              placeholder="请输入工单主题或工单编号关键字"
              style="width: 470px"
              clearable
            >
              <el-button
                type="primary"
                slot="append"
                style="
                  color: #fff;
                  padding: 9px 20px;
                  top: -0.6px;
                  position: relative;
                "
                class="search-input-button"
                @click="onSearch"
                >查询</el-button
              >
            </el-input>
          </el-form-item>
        </el-row>
      </el-form>
    </template>

    <template #contentHeader>
      <el-row
        ref="contentHeader"
        :gutter="20"
        class="content-header"
        v-loading="tableLoading"
        element-loading-spinner=" "
      >
        <el-col :xs="24" :sm="12" :md="14" :lg="18">
          <filter-total
            v-loading="tabMenuLoading"
            element-loading-text="数据加载中..."
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.8)"
            :value.sync="form.type"
            :filters="filterList"
            :totals="filterTotals"
            @onConHeadFilterChange="onConHeadFilterChange"
          ></filter-total>
        </el-col>
        <el-col :xs="24" :sm="24" :md="10" :lg="6" style="text-align: right">
          <div>
            <el-button type="primary" @click="onBatchDel">删除</el-button>
            <el-button type="primary" @click="getTableData">刷新</el-button>
          </div>
        </el-col>
      </el-row>
    </template>

    <template #table>
      <el-table
        ref="table"
        :data="tableData"
        :border="false"
        stripe
        @selection-change="onSelectionChange"
        height="100%"
        v-loading="tableLoading"
      >
        <el-table-column type="selection" min-width="50" :index="indexMethod" />
        <el-table-column key="1" label="工单类别" width="120px">
          <template slot-scope="scope">
            <el-tag :class="getSheetTypeClass(scope.row)">
              <span v-if="scope.row.networkTypeTop == 1">省分</span>
              <span v-if="scope.row.networkTypeTop == 2">省分</span>
              <span v-if="scope.row.networkTypeTop == 5">集团</span>
              <span v-if="scope.row.networkTypeTop == 7">核心网</span>
              <span v-if="scope.row.networkTypeTop == 8">IP专业</span>
              <span v-if="scope.row.networkTypeTop == 9">平台</span>
              <span
                v-else-if="
                  scope.row.networkTypeTop == 0 &&
                  scope.row.professionalType == '传输网'
                "
                >骨干传输</span
              ><span
                v-else-if="
                  scope.row.networkTypeTop == 0 &&
                  scope.row.professionalType == 'IT云设备'
                "
                >IT云</span
              ><span
                v-else-if="
                  scope.row.networkTypeTop == 0 &&
                  scope.row.professionalType == '通信云'
                "
                >通信云</span
              >
              <span v-else-if="scope.row.networkTypeTop == 6">无线网</span>
              <span v-else-if="scope.row.networkTypeTop == 10">GNOC督办</span>
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          key="2"
          prop="sheetNo"
          class="sheetNo_style"
          label="工单编号"
          width="320px"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              class="sheetNo_style"
              @click="toDraftDetail(scope.row)"
              >{{ scope.row.sheetNo }}</el-button
            >
          </template></el-table-column
        >
        <el-table-column
          key="3"
          prop="sheetTitle"
          label="工单主题"
          min-width="280px"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <el-tooltip :content="row.sheetTitle" placement="top">
              <p class="descStyle">{{ row.sheetTitle }}</p>
            </el-tooltip>
          </template>
        </el-table-column>
        <template v-if="form.type == 'province' || form.type == 'wxw'">
          <el-table-column
            key="4"
            prop="city"
            label="所属地市"
            width="120px"
          ></el-table-column>
          <el-table-column
            key="5"
            prop="createType"
            label="工单来源"
            width="120px"
          ></el-table-column>
          <el-table-column
            key="6"
            prop="emergencyLevel"
            label="紧急程度"
            width="120px"
          ></el-table-column>
        </template>
        <template v-else-if="form.type == 'sfty'">
          <el-table-column
            key="7"
            prop="sheetCreateTime"
            label="创建时间"
            width="160px"
          ></el-table-column>
          <el-table-column
            key="8"
            prop="professionalType"
            label="故障专业"
            width="120px"
          ></el-table-column>
          <el-table-column
            key="9"
            prop="emergencyLevel"
            label="紧急程度"
            width="120px"
          ></el-table-column>
          <el-table-column
            key="10"
            prop="createType"
            label="工单来源"
            width="120px"
          ></el-table-column>
          <el-table-column
            key="11"
            prop="senderName"
            label="建单人"
            min-width="120px"
          >
          </el-table-column>
        </template>
        <template v-else-if="form.type == 'supervise'">
          <el-table-column
            key="supervise_createType"
            prop="createType"
            label="工单来源"
            width="120px"
          ></el-table-column>
          <el-table-column
            key="supervise_emergencyLevel"
            prop="emergencyLevel"
            label="紧急程度"
            width="120px"
          ></el-table-column>
        </template>
        <template v-else-if="form.type != 'wxw' && form.type != 'supervise'">
          <template v-if="form.type == 'commonFlow'">
            <el-table-column key="15" prop="emergencyLevel" label="紧急程度">
            </el-table-column>
            <el-table-column
              key="16"
              prop="createType"
              label="工单来源"
              min-width="120px"
            >
            </el-table-column>
            <el-table-column
              key="17"
              prop="senderName"
              label="建单人"
              min-width="120px"
            ></el-table-column>
          </template>
          <el-table-column
            key="12"
            prop="sheetCreateTime"
            label="建单时间"
            width="160px"
          ></el-table-column>
          <el-table-column
            key="13"
            prop="professionalType"
            label="专业"
            width="120px"
          ></el-table-column>
        </template>
        <el-table-column
          key="14"
          prop="processNode"
          label="处理环节"
          width="120px"
        ></el-table-column>
      </el-table>
    </template>
    <template #pagination>
      <pagination
        ref="pagination"
        :total="form.total"
        :page.sync="form.pageNum"
        :limit.sync="form.pageSize"
        @change="getTableData"
        v-loading="tableLoading"
        element-loading-spinner=" "
      />
    </template>
  </head-content-layout>
</template>

<script>
import HeadContentLayout from "./components/HeadContentLayout.vue";
import Pagination from "./components/Pagination.vue";
import FilterTotal from "./components/FilterTotal.vue";
import {
  apiMyDraftList,
  apiDeleteDraft,
  apicountDraftGrp,
} from "./api/WorkOrderMyDraft";
export default {
  name: "WorkOrderDraftList",
  components: {
    HeadContentLayout,
    Pagination,
    FilterTotal,
  },
  data() {
    return {
      // 查询参数
      form: {
        keyword: "",
        pageNum: 1,
        pageSize: 10,
        total: 0,
        type: "",
        value: "",
        networkTypeTop: 0,
      },
      moreSearchColAttrs: Object.freeze({
        xs: 24,
        sm: 12,
        md: 12,
        lg: 8,
        offset: 0,
      }),
      filterList: [],
      filterTotals: {
        ggw: 0,
        ITy: 0,
        txy: 0,
        fiveGc: 0,
        ywpt: 0,
        gt: 0,
        yzy: 0,
        province: 0,
        wxw: 0,
        supervise: 0,
      },
      tableLoading: false,
      tabMenuLoading: false,
      tableData: [],
      multipleSelections: [],
    };
  },
  activated() {
    this.onSearch();
  },
  methods: {
    onSearch() {
      this.form.pageNum = 1;
      this.getTableData();
    },
    professionalSum(param) {
      if (this.filterList.length == 0) this.tabMenuLoading = true;
      apicountDraftGrp(param)
        .then(res => {
          if (res.status == "0") {
            let _this = this;
            let v = res?.data?.rows ?? [];
            let filList = [];
            for (let i = 0; i < v.length; i++) {
              filList.push({
                label: v[i].professionalType,
                value: this.getLabelValue(v[i].professionalType),
                professionalType: v[i].professionalTypeCode,
                networkTypeTop: v[i].networkTypeTop,
              });
            }
            this.filterList = filList;
            this.tabMenuLoading = false;
            for (let i = 0; i < v.length; i++) {
              filList.map(item => {
                if (
                  item.professionalType == v[i].professionalTypeCode &&
                  item.networkTypeTop == v[i].networkTypeTop
                ) {
                  _this.filterTotals[item.value] = v[i].sum;
                }
              });
            }
          } else {
            this.tabMenuLoading = false;
          }
        })
        .catch(err => {
          console.log(err);
          this.this.tabMenuLoading = false;
        });
    },
    getLabelValue(val) {
      switch (val) {
        case "骨干传输":
          return "ggw";
        case "IT云设备":
          return "ITy";
        case "通信云":
          return "txy";
        case "省分":
          return "province";
        case "省分通用":
          return "sfty";
        case "集团通用":
          return "commonFlow";
        case "核心网":
          return "core";
        case "IP专业":
          return "ip";
        case "平台":
          return "pt";
        case "无线网":
          return "wxw";
        case "GNOC督办":
          return "supervise";
      }
    },
    onBatchDel() {
      if (this.multipleSelections.length > 0) {
        this.$confirm("是否删除工单草稿？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            let selectedWoId = this.multipleSelections.map(item => {
              return item.woId;
            });
            let param = {
              woIds: selectedWoId,
            };
            apiDeleteDraft(param)
              .then(res => {
                if (res.status == "0") {
                  console.log(res);
                  this.$message.success("删除成功");
                  this.onSearch();
                } else {
                  this.$message.error("删除失败");
                }
              })
              .catch(error => {
                console.log(error);
                this.$message.error("删除失败");
              });
          })
          .catch(error => {
            console.log(error);
          });
      } else {
        this.$message.warning("请先选择要删除的工单草稿");
      }
    },
    onConHeadFilterChange(val = {}) {
      let { value, professionalType: type } = val;
      this.form.type = value;
      this.form.value = type;
      this.form.pageNum = 1;
      console.log(this.form);
      this.getTableData();
    },

    getTableData(type) {
      this.tableLoading = true;
      let simpleParam = {
        sheetTitleOrNoKeyword: this.form.keyword,
        professionalType: this.form.value,
      };
      if (this.form.type === "province") {
        delete simpleParam.professionalType;
        this.$set(simpleParam, "networkTypeTop", 1);
      } else if (this.form.type === "sfty") {
        delete simpleParam.professionalType;
        this.$set(simpleParam, "networkTypeTop", 2);
      } else if (
        this.form.type == "ggw" ||
        this.form.type == "ITy" ||
        this.form.type == "txy"
      ) {
        this.$set(simpleParam, "networkTypeTop", 0);
      } else if (this.form.type == "commonFlow") {
        this.$set(simpleParam, "networkTypeTop", 5);
      } else if (this.form.type == "core") {
        this.$set(simpleParam, "networkTypeTop", 7);
      } else if (this.form.type == "ip") {
        this.$set(simpleParam, "networkTypeTop", 8);
      } else if (this.form.type == "pt") {
        this.$set(simpleParam, "networkTypeTop", 9);
      } else if (this.form.type == "wxw") {
        this.$set(simpleParam, "networkTypeTop", 6);
      } else if (this.form.type == "supervise") {
        this.$set(simpleParam, "networkTypeTop", 10);
      }
      let param = {
        pageIndex: this.form.pageNum,
        pageSize: this.form.pageSize,
        param1: JSON.stringify(simpleParam),
      };
      apiMyDraftList(param)
        .then(res => {
          if (res.status == "0") {
            this.tableData = res?.data?.rows ?? [];
            this.form.total = res?.data?.totalElements ?? 0;
            this.filterTotals[this.form.type] = res?.data?.totalElements ?? 0;
            this.professionalSum(param);
          }
          this.tableLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.tableLoading = false;
        });
    },
    onSelectionChange(selection) {
      this.multipleSelections = selection;
    },
    indexMethod(index) {
      const currentPage = this.form.pageNum;
      const pageSize = this.form.pageSize;
      return (currentPage - 1) * pageSize + (index + 1);
    },
    toDraftDetail(data) {
      let url = "";
      let frameTabTitle = "";
      if (data.networkTypeTop == 0) {
        if (data.professionalType == "通信云") {
          url = "commCloud_repairOrderCg";
          frameTabTitle = "通信云故障工单草稿";
        } else if (data.professionalType == "IT云设备") {
          url = "itCloud_repairOrderCg";
          frameTabTitle = "IT云设备故障工单草稿";
        } else {
          url = "backbone_repairOrderCg";
          frameTabTitle = "骨干传输故障工单草稿";
        }
      } else if (data.networkTypeTop == 5) {
        url = "commonFlow_repairOrderCg";
        frameTabTitle = "集团通用故障工单草稿";
      } else if (data.networkTypeTop == 7) {
        url = "commonFlow_repairOrderCg";
        frameTabTitle = "核心网故障工单草稿";
      } else if (data.networkTypeTop == 8) {
        url = "ipProfession_repairOrderCg";
        frameTabTitle = "IP专业故障工单草稿";
      } else if (data.networkTypeTop == 9) {
        url = "commonFlow_repairOrderCg";
        frameTabTitle = "平台故障工单草稿";
      } else if (data.networkTypeTop == 1) {
        url = "backbone_initiateSheet_provinceOrder";
        frameTabTitle = "省分工单";
      } else if (data.networkTypeTop == 2) {
        url =
          "backbone_initiateSheet_backbone_initiateSheet_commonProvinceOrder";
        frameTabTitle = "省分通用工单";
      } else if (data.networkTypeTop == 6) {
        url = "backbone_initiateSheet_wirelessOrder";
        frameTabTitle = "无线网故障工单草稿";
      } else if (data.networkTypeTop == 10) {
        url = "backbone_initiateSheet_supervise";
        frameTabTitle = "GNOC督办单草稿";
      }
      this.$router.push({
        name: url,
        query: {
          frameTabTitle: frameTabTitle,
          woId: data.woId,
          orderType: "caogao",
          sheetNo: data.sheetNo,
          professionalType: data.professionalType,
        },
      });
    },
    getSheetTypeClass(data) {
      if (data.networkTypeTop == 0 && data.professionalType == "传输网") {
        return "ggw_sheetType";
      } else if (
        data.networkTypeTop == 0 &&
        data.professionalType == "IT云设备"
      ) {
        return "itCloud_sheetType";
      } else if (
        data.networkTypeTop == 0 &&
        data.professionalType == "通信云"
      ) {
        return "commCloud_sheetType";
      } else if (data.networkTypeTop == 6) {
        return "wxw_sheetType";
      } else if (data.networkTypeTop == 5) {
        return "commonFlow_sheetType";
      } else if (data.networkTypeTop == 7) {
        return "core_sheetType";
      } else if (data.networkTypeTop == 8) {
        return "ip_sheetType";
      } else if (data.networkTypeTop == 9) {
        return "pt_sheetType";
      } else if (data.networkTypeTop == 1) {
        return "sf_sheetType";
      } else if (data.networkTypeTop == 2) {
        return "sfty_sheetType";
      } else if (data.networkTypeTop == 10) {
        return "supervise_sheetType";
      }
    },
  },
};
</script>
<style lang="scss" scoped>
@import "./workOrderWaitDetail/assets/common.scss";
.filter-total {
  ::v-deep .el-loading-mask .el-loading-spinner {
    text-align: left;
    line-height: 32px;
    margin-left: 150px;
  }
  ::v-deep .el-loading-text {
    display: inline-block;
    margin-left: 10px;
  }
}
.descStyle {
  display: -webkit-box;
  overflow: hidden;
  white-space: normal !important;
  text-overflow: ellipsis;
  word-wrap: break-word;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
</style>
