<template>
  <head-fixed-layout class="draft-eidt-page">
    <template #header>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="15" :md="18" class="head-title"
          ><span>{{ mainProfessionalType }}故障工单拟稿</span></el-col
        >
        <el-col :xs="24" :sm="9" :md="6" class="head-handle-wrap">
          <el-button
            type="primary"
            @click="onSheetSave"
            v-loading.fullscreen.lock="sheetSaveLoading"
            >保存</el-button
          >
          <el-button
            type="primary"
            @click="onSheetSubmit"
            v-loading.fullscreen.lock="sheetCommitLoading"
            >提交</el-button
          >
        </el-col>
      </el-row>
    </template>

    <el-form
      :model="sheetForm"
      ref="sheetForm"
      :rules="sheetFormRules"
      label-width="130px"
      label-position="right"
    >
      <el-card shadow="always" :body-style="{ padding: '10px' }">
        <div slot="header" class="card-title">
          <span>工单基本信息</span>
        </div>
        <el-row :gutter="10" type="flex" style="flex-wrap: wrap">
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="建单人:" prop="createUser" required>
              <el-input
                v-model="sheetForm.createUser"
                placeholder="请输入建单人"
                readonly
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="建单部门:" prop="dept" required>
              <el-input
                v-model="sheetForm.dept"
                placeholder="请输入建单部门"
                readonly
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="建单时间:" prop="createTime" required>
              <el-date-picker
                v-model="sheetForm.createTime"
                type="datetime"
                placeholder="请选择建单时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                readonly
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="14" :md="16" :offset="0">
            <el-form-item label="工单主题:" prop="title" required>
              <el-input
                v-model="sheetForm.title"
                placeholder="使用拼接规则手动填写"
                clearable
                style="width: 100%"
                maxlength="400"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="10" :md="8" :offset="0">
            <el-form-item label="工单来源:" prop="source" required>
              <dict-select
                :value.sync="sheetForm.source"
                :dictId="10003"
                placeholder="请选择内容"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="发生时间:" prop="happenTime">
              <el-date-picker
                v-model="sheetForm.happenTime"
                type="datetime"
                placeholder="请选择时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="紧急程度:" prop="urgent" required>
              <el-radio-group v-model="sheetForm.urgent">
                <el-radio
                  v-for="(item, i) in urgentOption"
                  :key="i"
                  :label="item.dictCode"
                  >{{ item.dictName }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
          <!-- <el-col :xs="24" :sm="10" :md="8" :offset="0">
            <el-form-item label="工单优先级:" prop="faultLevel" required>
              <dict-select
                :value.sync="sheetForm.faultLevel"
                :dictId="81005"
                placeholder="请选择"
                style="width: 100%"
              />
            </el-form-item>
          </el-col> -->
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item
              label="受理时限(分钟):"
              prop="acceptTimeLimit"
              required
            >
              <el-input
                v-model="sheetForm.acceptTimeLimit"
                placeholder="请输入受理时限"
                clearable
                style="width: 100%"
                maxlength="11"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="14" :md="16" :offset="0">
            <el-form-item
              label="预估处理时限:"
              prop="estimatedProTimeLimit"
              required
            >
              <el-radio-group v-model="sheetForm.estimatedProTimeLimit">
                <el-radio
                  v-for="(item, i) in estimatedProTimeLimitOption"
                  :key="i"
                  :label="item.id"
                  >{{ item.name }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <template v-if="mainProfessionalType == '集团通用'">
            <el-col :xs="24" :sm="8" :md="8" :offset="0">
              <el-form-item label="所属专业:" prop="special" required>
                <dict-select
                  :value.sync="sheetForm.special"
                  :dictId="60012"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </template>
          <template v-if="mainProfessionalType == '核心网'">
            <el-col :xs="24" :sm="8" :md="8" :offset="0">
              <el-form-item label="所属专业:" prop="special" required>
                <dict-select
                  :value.sync="sheetForm.special"
                  :dictId="60009"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </template>
          <template v-if="mainProfessionalType == 'IP专业'">
            <el-col :xs="24" :sm="8" :md="8" :offset="0">
              <el-form-item label="所属专业:" prop="special" required>
                <dict-select
                  :value.sync="sheetForm.special"
                  :dictId="60010"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </template>
          <template v-if="mainProfessionalType == '平台'">
            <el-col :xs="24" :sm="8" :md="8" :offset="0">
              <el-form-item label="所属专业:" prop="special" required>
                <dict-select
                  :value.sync="sheetForm.special"
                  :dictId="60011"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </template>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="业务中断:" prop="businessDown" required>
              <dict-select
                :value.sync="sheetForm.businessDown"
                :dictId="60138"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="网络类型:" prop="netType">
              <dict-select
                :value.sync="sheetForm.netType"
                :dictId="this.networkTypeDictId"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="告警类别:" prop="alarmType">
              <dict-select
                :value.sync="sheetForm.alarmType"
                :dictId="60002"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <template
            v-if="
              sheetForm.special == 1 ||
              sheetForm.special == 31 ||
              sheetForm.special == 6 ||
              sheetForm.special == 20 ||
              sheetForm.special == 21 ||
              sheetForm.special == 28 ||
              sheetForm.special == 32 ||
              sheetForm.special == 19
            "
          >
            <el-col :xs="24" :sm="8" :md="8" :offset="0">
              <el-form-item label="网元类型:" prop="neType" key="neType">
                <!-- <dict-select
                  :value.sync="sheetForm.neType"
                  :dictId="60004"
                  style="width: 100%"
                /> -->
                <el-input
                  v-model="sheetForm.neType"
                  placeholder="请输入网元类型"
                  style="width: 100%"
                  maxlength="20"
                >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col
              :xs="24"
              :sm="8"
              :md="8"
              :offset="0"
              v-show="isOpticalCableShow"
            >
              <el-form-item label="网元名称:" prop="neName" key="neName">
                <el-input
                  v-model="sheetForm.neName"
                  placeholder="请输入内容"
                  style="width: 100%"
                  maxlength="255"
                >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="16" :md="24" :offset="0">
              <el-form-item
                label="工作内容:"
                prop="jobComment"
                key="jobComment"
              >
                <el-input
                  v-model="sheetForm.jobComment"
                  clearable
                  style="width: 100%"
                  maxlength="3000"
                  show-word-limit
                >
                </el-input>
              </el-form-item>
            </el-col>
          </template>
          <template v-else-if="sheetForm.special == 26">
            <el-col :xs="24" :sm="8" :md="8" :offset="0">
              <el-form-item label="网元类型:" prop="neType" key="neType">
                <dict-select
                  :value.sync="sheetForm.neType"
                  :dictId="60004"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :offset="0">
              <el-form-item
                label="云池类型:"
                prop="cloudPoolType"
                key="cloudPoolType"
              >
                <el-input
                  v-model="sheetForm.cloudPoolType"
                  placeholder="请输入内容"
                  style="width: 100%"
                  disabled
                >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="14" :md="8" :offset="0">
              <el-form-item
                label="MEC节点名称:"
                prop="mecNodeName"
                key="mecNodeName"
              >
                <el-input
                  v-model="sheetForm.mecNodeName"
                  placeholder="请输入内容"
                  style="width: 100%"
                  maxlength="100"
                >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="16" :md="24" :offset="0">
              <el-form-item
                label="工作内容:"
                prop="jobComment"
                key="jobComment"
              >
                <el-input
                  v-model="sheetForm.jobComment"
                  clearable
                  maxlength="3000"
                  show-word-limit
                  style="width: 100%"
                >
                </el-input>
              </el-form-item>
            </el-col>
          </template>
          <template v-else>
            <el-col :xs="24" :sm="16" :md="24" :offset="0">
              <el-form-item
                label="工作内容:"
                prop="jobComment"
                key="jobComment"
              >
                <el-input
                  v-model="sheetForm.jobComment"
                  clearable
                  maxlength="3000"
                  show-word-limit
                  style="width: 100%"
                >
                </el-input>
              </el-form-item>
            </el-col>
          </template>
          <el-col :xs="24" :sm="16" :md="24" :offset="0">
            <el-form-item
              label="故障现象:"
              prop="phenomenon"
              required
              key="phenomenon"
            >
              <el-input
                v-model="sheetForm.phenomenon"
                placeholder="请输入内容"
                type="textarea"
                :rows="2"
                clearable
                style="width: 100%"
                maxlength="3000"
                show-word-limit
              >
              </el-input>

              <!--
              20需求变更
              maxlength="500"
              show-word-limit -->
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="影响业务列表:" prop="incidenceFile">
              <el-input
                v-model="sheetForm.incidenceFile"
                placeholder="添加附件"
                readonly
                style="width: 100%"
              >
                <el-button type="info" slot="append" @click="incidenceBrowse"
                  >+</el-button
                >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="16" :md="16" :offset="0">
            <el-form-item label="影响范围:" prop="incidence">
              <el-input
                v-model="sheetForm.incidence"
                placeholder="请输入内容"
                clearable
                style="width: 100%"
                maxlength="1000"
                show-word-limit
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24" :offset="0">
            <el-form-item label="备注:" prop="remarks">
              <el-input
                v-model="sheetForm.remarks"
                placeholder="请输入内容"
                clearable
                maxlength="255"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card
        shadow="always"
        :body-style="{ padding: '10px' }"
        style="margin-top: 10px"
      >
        <div slot="header" class="card-title">
          <span>工单派送信息</span>
        </div>
        <el-row :gutter="10" type="flex" style="flex-wrap: wrap">
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="附件:" prop="attachmentFile">
              <el-input
                v-model="sheetForm.attachmentFile"
                placeholder="添加附件"
                readonly
                style="width: 100%"
              >
                <el-button type="info" slot="append" @click="attachmentBrowse"
                  >+</el-button
                >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="建单人主送:" prop="builderZs" required>
              <el-input v-model="sheetForm.builderZs" placeholder="添加人员">
                <template v-for="(tag, index) in organizeForm.builderZsList">
                  <el-tag
                    slot="prefix"
                    style="margin-top: 5px"
                    :key="index"
                    closable="true"
                    @close="handleClose('builderZs', tag)"
                    v-show="index < 1"
                    v-if="tag.bz == 'user'"
                  >
                    {{ tag.name }}
                  </el-tag>
                  <el-tag
                    slot="prefix"
                    style="margin-top: 5px"
                    :key="index"
                    closable="true"
                    @close="handleClose('builderZs', tag)"
                    v-show="index < 1"
                    v-if="tag.bz == 'org'"
                  >
                    {{ tag.orgName }}
                  </el-tag>
                </template>
                <el-popover
                  slot="prefix"
                  v-if="organizeForm.builderZsList.length >= 2"
                  width="500"
                  trigger="click"
                >
                  <el-input
                    v-model="organizeForm.builderZsName"
                    placeholder="请输入主送人员姓名/组织名称"
                  >
                    <el-button
                      type="info"
                      slot="append"
                      icon="el-icon-search"
                      @click="search('builderZs')"
                    >
                    </el-button>
                    <el-button
                      type="info"
                      slot="append"
                      icon="el-icon-close"
                      @click="clear('builderZs')"
                    >
                    </el-button>
                  </el-input>
                  <el-table
                    ref="multipleTable"
                    tooltip-effect="dark"
                    @selection-change="handleSelectionChange"
                    :data="organizeForm.builderZsListCopy"
                    max-height="240"
                  >
                    <el-table-column width="30" type="selection">
                    </el-table-column>
                    <el-table-column
                      min-width="70"
                      property="name"
                      label="姓名"
                    >
                    </el-table-column>
                    <el-table-column
                      min-width="180"
                      property="orgName"
                      label="组织"
                    >
                    </el-table-column>
                    <el-table-column
                      min-width="120"
                      property="mobilePhone"
                      label="电话"
                    >
                    </el-table-column>
                    <el-table-column fixed="right" label="操作" width="50">
                      <template slot-scope="scope">
                        <el-button
                          type="text"
                          size="small"
                          @click.native.prevent="
                            handleClose('builderZs', scope.row)
                          "
                        >
                          移除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <el-button
                    size="small"
                    type="text"
                    @click="toggleSelection('builderZs')"
                    >批量移除
                  </el-button>
                  <el-tag slot="reference" style="margin-top: 3px">
                    +{{ organizeForm.builderZsList.length - 1 }}
                  </el-tag>
                </el-popover>
                <el-button
                  type="info"
                  slot="append"
                  icon="el-icon-user"
                  @click="onOpenPeopleDialog('lordSentDetermine')"
                ></el-button>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="建单人抄送:" prop="builderCs">
              <el-input v-model="sheetForm.builderCs" placeholder="添加人员">
                <template v-for="(tag, index) in organizeForm.builderCsList">
                  <el-tag
                    slot="prefix"
                    style="margin-top: 5px"
                    :key="index"
                    closable="true"
                    @close="handleClose('builderCs', tag)"
                    v-show="index < 1"
                    v-if="tag.bz == 'user'"
                  >
                    {{ tag.name }}
                  </el-tag>
                  <el-tag
                    slot="prefix"
                    style="margin-top: 5px"
                    :key="index"
                    closable="true"
                    @close="handleClose('builderCs', tag)"
                    v-show="index < 1"
                    v-if="tag.bz == 'org'"
                  >
                    {{ tag.orgName }}
                  </el-tag>
                </template>
                <el-popover
                  slot="prefix"
                  v-if="organizeForm.builderCsList.length >= 2"
                  width="500"
                  trigger="click"
                >
                  <el-input
                    v-model="organizeForm.builderCsName"
                    placeholder="请输入抄送人员姓名/组织名称"
                  >
                    <el-button
                      type="info"
                      slot="append"
                      icon="el-icon-search"
                      @click="search('builderCs')"
                    >
                    </el-button>
                    <el-button
                      type="info"
                      slot="append"
                      icon="el-icon-close"
                      @click="clear('builderCs')"
                    >
                    </el-button>
                  </el-input>
                  <el-table
                    ref="multipleTable"
                    tooltip-effect="dark"
                    @selection-change="handleSelectionChange"
                    :data="organizeForm.builderCsListCopy"
                    max-height="240"
                  >
                    <el-table-column width="30" type="selection">
                    </el-table-column>
                    <el-table-column
                      min-width="70"
                      property="name"
                      label="姓名"
                    >
                    </el-table-column>
                    <el-table-column
                      min-width="180"
                      property="orgName"
                      label="组织"
                    >
                    </el-table-column>
                    <el-table-column
                      min-width="120"
                      property="mobilePhone"
                      label="电话"
                    >
                    </el-table-column>
                    <el-table-column fixed="right" label="操作" width="50">
                      <template slot-scope="scope">
                        <el-button
                          type="text"
                          size="small"
                          @click.native.prevent="
                            handleClose('builderCs', scope.row)
                          "
                        >
                          移除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <el-button
                    size="small"
                    type="text"
                    @click="toggleSelection('builderCs')"
                    >批量移除
                  </el-button>
                  <el-tag slot="reference" style="margin-top: 5px">
                    +{{ organizeForm.builderCsList.length - 1 }}
                  </el-tag>
                </el-popover>
                <el-button
                  type="info"
                  slot="append"
                  icon="el-icon-user"
                  @click="onOpenPeopleDialog('ccDetermine')"
                >
                </el-button>
              </el-input>
            </el-form-item>
          </el-col>
          <template v-if="sheetForm.special == 7">
            <el-col :xs="24" :sm="8" :md="8" :offset="0">
              <el-form-item label="是否共建共享:" prop="isShare">
                <el-radio-group v-model="sheetForm.isShare">
                  <el-radio :label="1">是</el-radio>
                  <el-radio :label="0">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </template>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="派单模式:" prop="seizeOrders" required>
              <el-radio-group v-model="sheetForm.seizeOrders">
                <el-radio :label="1">抢单受理</el-radio>
                <el-radio :label="0">均可受理</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="是否通知他人:" prop="sms">
              <el-radio-group v-model="sheetForm.sms">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <template v-if="sheetForm.sms">
            <el-col :xs="24" :sm="8" :md="8" :offset="0">
              <el-form-item
                label="接收人:"
                prop="recipient"
                :rules="[
                  {
                    required: sheetForm.sms == '1' ? true : false,
                    message: '请选择',
                  },
                ]"
              >
                <el-input v-model="sheetForm.recipient" placeholder="添加人员">
                  <el-tag
                    slot="prefix"
                    style="margin-top: 5px"
                    v-for="(tag, index) in organizeForm.recipientList"
                    :key="index"
                    closable="true"
                    @close="handleClose('recipient', tag)"
                    v-show="index < 1"
                  >
                    {{ tag.name }}
                  </el-tag>

                  <el-popover
                    slot="prefix"
                    v-if="organizeForm.recipientList.length >= 2"
                    width="500"
                    trigger="click"
                  >
                    <el-input
                      v-model="organizeForm.recipientName"
                      placeholder="请输入接收人员姓名/组织名称"
                    >
                      <el-button
                        type="info"
                        slot="append"
                        icon="el-icon-search"
                        @click="search('recipient')"
                      >
                      </el-button>
                      <el-button
                        type="info"
                        slot="append"
                        icon="el-icon-close"
                        @click="clear('recipient')"
                      >
                      </el-button>
                    </el-input>
                    <el-table
                      ref="multipleTable"
                      tooltip-effect="dark"
                      @selection-change="handleSelectionChange"
                      :data="organizeForm.recipientListCopy"
                      max-height="240"
                    >
                      <el-table-column width="30" type="selection">
                      </el-table-column>
                      <el-table-column
                        min-width="70"
                        property="name"
                        label="姓名"
                      >
                      </el-table-column>
                      <el-table-column
                        min-width="180"
                        property="orgName"
                        label="组织"
                      >
                      </el-table-column>
                      <el-table-column
                        min-width="120"
                        property="mobilePhone"
                        label="电话"
                      >
                      </el-table-column>
                      <el-table-column fixed="right" label="操作" width="50">
                        <template slot-scope="scope">
                          <el-button
                            type="text"
                            size="small"
                            @click.native.prevent="
                              handleClose('recipient', scope.row)
                            "
                          >
                            移除
                          </el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                    <el-button
                      size="small"
                      type="text"
                      @click="toggleSelection('recipient')"
                      >批量移除
                    </el-button>
                    <el-tag slot="reference" style="margin-top: 5px">
                      +{{ organizeForm.recipientList.length - 1 }}
                    </el-tag>
                  </el-popover>
                  <el-button
                    type="info"
                    slot="append"
                    icon="el-icon-user"
                    @click="onOpenPeopleDialog('recipientDetermine')"
                  ></el-button>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :offset="0">
              <el-form-item label="发送内容:" prop="sendContent">
                <!-- :rules="[
                  {
                    required: sheetForm.sms == '1' ? true : false,
                    message: '请输入内容',
                    trigger: 'blur',
                  },
                ]" -->
                <el-input
                  v-model="sheetForm.sendContent"
                  placeholder="请输入内容"
                  type="textarea"
                  :rows="4"
                  clearable
                  style="width: 100%"
                  maxlength="1000"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-col>
          </template>
        </el-row>
      </el-card>
      <el-card
        shadow="always"
        :body-style="{ padding: '10px' }"
        style="margin-top: 10px"
        v-if="sheetForm.isShare"
      >
        <div slot="header" class="card-title">
          <span>共建共享信息</span>
        </div>
        <el-row :gutter="10" type="flex" style="flex-wrap: wrap">
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="站址信息:" prop="createUser">
              <el-input
                placeholder="请输入站址信息"
                readonly
                style="width: 100%"
              >
              </el-input>
              <!-- <el-input
                v-model="sheetForm.createUser"
                placeholder="请输入建单人"
                readonly
                style="width: 100%"
              ></el-input> -->
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="站址名称:" prop="createUser">
              <el-input
                placeholder="请输入站址名称"
                readonly
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="站址经纬度:" prop="createUser">
              <el-input
                placeholder="请输入站址经纬度"
                readonly
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="站址类别:">
              <dict-select
                :value.sync="sheetForm.alarmType"
                :dictId="60002"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="基站ID:" prop="createUser">
              <el-input placeholder="请输入基站ID" readonly style="width: 100%">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="基站名称:" prop="createUser">
              <el-input
                placeholder="请输入基站名称"
                readonly
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="所属区域:" prop="createUser">
              <el-input
                placeholder="请输入所属区域"
                readonly
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="所属场景:">
              <dict-select
                :value.sync="sheetForm.alarmType"
                :dictId="60002"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="基站级别:" prop="createUser">
              <dict-select
                :value.sync="sheetForm.alarmType"
                :dictId="60002"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="基站类型:">
              <dict-select
                :value.sync="sheetForm.alarmType"
                :dictId="60002"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="告警ID和名称:" prop="createUser">
              <el-input
                placeholder="请输入告警ID和名称"
                readonly
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="告警类别:" prop="createUser">
              <el-input
                placeholder="请输入告警类别"
                readonly
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :offset="0">
            <el-form-item label="告警详情:" prop="createUser">
              <el-input
                placeholder="请输入告警详情"
                type="textarea"
                :rows="2"
                clearable
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="告警级别:" prop="createUser">
              <dict-select
                :value.sync="sheetForm.alarmType"
                :dictId="60002"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="告警发生时间:" prop="createTime" required>
              <el-date-picker
                v-model="sheetForm.createTime"
                type="datetime"
                placeholder="请选择告警发生时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                readonly
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="故障原因:" prop="createUser">
              <dict-select
                :value.sync="sheetForm.alarmType"
                :dictId="60002"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="交互方式:" prop="createUser">
              <dict-select
                :value.sync="sheetForm.alarmType"
                :dictId="60002"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
    </el-form>
    <dia-tissue-tree
      v-if="isDiaOrgsUserTree"
      :title="diaPeople.title"
      :visible.sync="diaPeople.visible"
      :showOrgsTree="diaPeople.showOrgsTree"
      :showContactUserTab="diaPeople.showContactUserTab"
      :showContactOrgTab="diaPeople.showContactOrgTab"
      :professionalType="mainProfessionalType"
      @on-save="onSavePeople"
    />
    <el-dialog
      width="420px"
      title="附件选择"
      :visible.sync="attachmentDialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <file-upload
        @change="changeFileData"
        @cancel="closeAttachmentDialog"
      ></file-upload>
    </el-dialog>
    <el-dialog
      width="420px"
      title="影响业务列表附件选择"
      :visible.sync="incidenceDialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <file-upload
        @change="changeIncidenceFileData"
        @cancel="closeIncidenceDialog"
      ></file-upload>
    </el-dialog>
  </head-fixed-layout>
</template>

<script>
import { mapGetters } from "vuex";
import moment from "moment";
import HeadFixedLayout from "./components/HeadFixedLayout.vue";
import DictSelect from "./components/DictSelect.vue";
import FileUpload from "./components/FileUpload.vue";
import DiaTissueTree from "@/plugin/backbone/components/common/DiaTissueTree";

import { apiDict, apiGetOrgInfo } from "./api/CommonApi";
import {
  apiBuildCommonFlowSingle,
  apiSaveToCommonFlowDraft,
} from "./api/WorkOrderDraftEdit";
import { mixin } from "../../../../mixins";
export default {
  name: "WorkOrderDraftEdit",
  components: {
    HeadFixedLayout,
    DictSelect,
    FileUpload,
    DiaTissueTree,
  },
  mixins: [mixin],
  data() {
    var validHappenTime = (rule, value, callback) => {
      if (value === "" || value === null) {
        callback(new Error("请选择发生时间"));
      } else {
        let seconds = moment(
          this.sheetForm.createTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.sheetForm.happenTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        if (seconds < 0) {
          callback(new Error("“发生时间”不能晚于“建单时间”，请重新选择"));
        } else {
          callback();
        }
      }
    };
    return {
      networkTypeDictId: "",
      sheetForm: {
        createUser: "", //建单人
        sender: null, //建单人的登录名
        dept: "", //建单部门
        deptId: null, //建单部门Id
        createTime: "", //建单时间
        title: "", //工单主题
        source: "2", //工单来源
        happenTime: "", //发生时间
        urgent: "0", //紧急程度
        acceptTimeLimit: "30", //受理时限
        estimatedProTimeLimit: null, //预估处理时限
        special: null, //所属专业
        netType: "", //网络类型
        alarmType: "", //告警类别
        neType: "", //网元类型
        neName: "", //网元名称
        jobComment: "", //工作内容
        phenomenon: "", //故障现象
        incidenceFile: "", //影响业务列表
        incidence: "", //影响范围
        remarks: "", //备注
        // opticalCable: "",
        attachmentFile: "", //附件
        fileVirtual: "",
        builderZs: "",
        builderZsUserId: "",
        builderZsOrgId: "",
        builderZsUserName: "",
        builderZsOrgName: "",
        builderCs: "",
        builderCsUserId: "",
        builderCsOrgId: "",
        builderCsUserName: "",
        builderCsOrgName: "",
        sms: 0,
        seizeOrders: 0, //建单模式
        businessDown: "", //业务中断
        recipient: "",
        recipientUserName: "",
        recipientOrgName: "",
        recipientUserId: "",
        recipientOrgId: "",
        sendContent:
          "1、光缆：**， ××××光缆在××省××（网元）与××（网元）之间中断，影响××××波分系统，承载的SDH系统保护倒换，已经派单给省公司，并电话通知处理，现在该省正在利用备用纤芯倒接;(该段落OLP正常切换;)(分公司正在协调处理;)2、设备：××， ××××系统中断，初步怀疑是××网元××板卡问题，承载的业务目前不受影响，已经派单给省公司，并电话通知处理，现在分公司正在利用备用波道倒接；(该系统承载的SDH系统正常倒换，业务不受影响；)(该系统承载的SDH系统倒换不正常，已经影响业务；)(影响的业务有（无）××重保电路。)",
        isShare: 0, //是否共建共享
        agentManDetail: null,
        copyManDetail: null,
        recipientDetailUserName: null,
        cloudPoolType: "MEC",
        mecNodeName: "",
        faultLevel: "2",
      },
      organizeForm: {
        builderZsList: [],
        builderZsListCopy: [],
        builderZsName: "",
        builderCsList: [],
        builderCsListCopy: [],
        builderCsName: "",
        recipientList: [],
        recipientListCopy: [],
        recipientName: "",
      },
      sheetFormRules: {
        createUser: [{ required: true, message: "请输入建单人" }],
        dept: [{ required: true, message: "请输入建单部门" }],
        createTime: [{ required: true, message: "请选择建单时间" }],
        title: [
          { required: true, message: "工单主题不能为空" },
          // { min: 1, max: 5, message: "已超过填写字数上限" },
          {
            validator: this.checkLength,
            max: 400,
            form: "sheetForm",
            messsage: "已超过填写字数上限",
            required: true,
          },
        ],
        source: [{ required: true, message: "请选择工单来源" }],
        happenTime: [{ validator: validHappenTime, required: true }],
        urgent: [{ required: true, message: "请选择紧急程度" }],
        acceptTimeLimit: [
          { required: true, message: "请输入受理时限" },
          {
            validator: this.checkLength,
            max: 11,
            form: "sheetForm",
            messsage: "已超过填写字数上限",
            required: true,
          },
        ],
        neType: [
          {
            validator: this.checkLength,
            max: 20,
            form: "sheetForm",
            messsage: "已超过填写字数上限",
          },
        ],
        neName: [
          {
            validator: this.checkLength,
            max: 255,
            form: "sheetForm",
            messsage: "已超过填写字数上限",
          },
        ],
        jobComment: [
          {
            validator: this.checkLength,
            max: 3000,
            form: "sheetForm",
            messsage: "已超过填写字数上限",
          },
        ],
        special: [{ required: true, message: "请选择所属专业" }],
        businessDown: [{ required: true, message: "请选择业务中断" }],
        // netType: [{ required: true, message: "请选择网络类型" }],
        phenomenon: [
          { required: true, message: "请输入故障现象" },
          {
            validator: this.checkLength,
            max: 3000,
            form: "sheetForm",
            messsage: "已超过填写字数上限",
          },
        ],
        incidence: [
          {
            validator: this.checkLength,
            max: 1000,
            form: "sheetForm",
            messsage: "已超过填写字数上限",
          },
        ],
        remarks: [
          {
            validator: this.checkLength,
            max: 255,
            form: "sheetForm",
            messsage: "已超过填写字数上限",
          },
        ],
        sendContent: [
          {
            required: true,
            message: "请输入发送内容",
          },
          {
            validator: this.checkLength,
            max: 1000,
            form: "sheetForm",
            messsage: "已超过填写字数上限",
          },
        ],
        builderZs: [{ required: true, message: "建单主送人不能为空" }],
        estimatedProTimeLimit: [
          { required: true, message: "请选择预估处理时限" },
        ],
        mecNodeName: [
          {
            validator: this.checkLength,
            max: 100,
            form: "sheetForm",
            messsage: "已超过填写字数上限",
          },
        ],
        // faultLevel: [{ required: true, message: "请选择工单优先级" }],
      },
      diaPeople: {
        visible: false,
        title: "",
        saveName: "",
        saveTitleMap: {
          lordSentDetermine: "建单人主送",
          ccDetermine: "建单人抄送",
          recipientDetermine: "接收人选择",
        },
        showOrgsTree: true,
        showOrgsTreeMap: {
          recipientDetermine: false,
          lordSentDetermine: true,
          ccDetermine: true,
        },
        showContactUserTab: false,
        showContactOrgTab: false,
        showContactUserTabMap: {
          lordSentDetermine: true,
          ccDetermine: true,
          recipientDetermine: true,
        },
        showContactOrgTabMap: {
          lordSentDetermine: true,
          ccDetermine: true,
        },
      },
      attachmentDialogVisible: false,
      isDiaOrgsUserTree: false,
      importForm: {
        //附件
        attachmentFileList: [],
        //影响业务列表附件
        incidenceFileList: [],
      },
      urgentOption: [],
      opticFiber: [],
      allInfos: [],
      sheetCommitLoading: false,
      sheetSaveLoading: false,
      incidenceDialogVisible: false,
      isOpticalCableShow: true,
      isOpticalCableIntShow: false,
      orgInfoData: {}, //组织信息数据缓存
      route: null,
      userInfoData: {},
      //预估处理时限
      estimatedProTimeLimitOption: [
        { id: "1", name: "1小时" },
        { id: "3", name: "3小时" },
        { id: "4", name: "4小时" },
        { id: "5", name: "5小时" },
        { id: "24", name: "24小时" },
        { id: "48", name: "48小时" },
      ],
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
    ...mapGetters(["token"]),
    mainProfessionalType() {
      return this.$route.query.mainProfessionalType;
    },
  },
  watch: {
    "organizeForm.builderZsList": {
      handler(newV) {
        if (newV.length > 0) {
          this.sheetForm.builderZs = "已选";
        } else {
          this.sheetForm.builderZs = "";
        }
      },
      deep: true,
    },
    "organizeForm.recipientList": {
      handler(newV) {
        if (newV.length > 0) {
          this.sheetForm.recipient = "已选";
        } else {
          this.sheetForm.recipient = "";
        }
      },
      deep: true,
    },
    "sheetForm.special": {
      handler(newV) {
        this.sheetForm.netType = "";
        if (newV == "20") {
          this.networkTypeDictId = "60200";
        } else if (newV == "19") {
          this.networkTypeDictId = "60201";
        } else if (newV == "1" || newV == "31") {
          this.networkTypeDictId = "60202";
        } else if (newV == "21") {
          this.networkTypeDictId = "60203";
        } else if (newV == "6") {
          this.networkTypeDictId = "60204";
        } else if (newV == "28" || newV == "32") {
          this.networkTypeDictId = "60205";
        } else {
          this.networkTypeDictId = "60003";
        }
      },
      deep: true,
    },
  },
  created() {
    this.route = this.$route;
    this.getUrgentOption();
    this.getOpticFiber();
    this.setSpecial(this.$route.query.mainProfessionalType);
  },
  mounted() {
    this.userInfoData = JSON.parse(this.userInfo.attr2);
    this.sheetForm.createTime = moment(Date.now()).format(
      "YYYY-MM-DD HH:mm:ss"
    );
    this.sheetForm.happenTime = moment(Date.now()).format(
      "YYYY-MM-DD HH:mm:ss"
    );
    this.getOrgInfo();

    window["__process_response_from_getDevice"] = value => {
      let objStr = "";
      let result = JSON.parse(value);
      let objArray = JSON.parse(result?.system);
      objArray.forEach(obj => {
        objStr += obj.sysName + ",";
      });
      if (objStr) {
        objStr = objStr.substr(0, objStr.length - 1);
      }

      this.sheetForm.title += this.sheetForm.title ? "," + objStr : objStr;
    };
  },
  methods: {
    setSpecial(mainProfessionalType) {
      switch (mainProfessionalType) {
        case "核心网":
          this.sheetForm.special = "31";
          break;
        case "IP专业":
          this.sheetForm.special = "12";
          break;
        case "平台":
          this.sheetForm.special = "25";
          break;
        case "集团通用":
          this.sheetForm.special = "30";
          break;
      }
    },
    getOrgInfo() {
      apiGetOrgInfo()
        .then(res => {
          if (res.status == "0") {
            this.orgInfoData = res?.data ?? {};
            this.sheetForm.createUser = res?.data?.trueName ?? "";
            this.sheetForm.dept = res?.data?.orgInfo?.orgName ?? "";
            this.sheetForm.deptId = res?.data?.orgInfo?.orgId ?? "";
            this.sheetForm.sender = res?.data?.userName ?? "";
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    getOpticFiber() {
      let param = {
        dictTypeCode: "10108",
      };
      apiDict(param)
        .then(res => {
          if (res.code == 200) {
            this.opticFiber = res?.data ?? [];
            this.opticFiber.reverse();
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    querySearch(queryString, callback) {
      this.allInfos = this.opticFiber.map(function (obj) {
        return {
          value: obj.dictName,
          id: obj.dictCode,
        };
      });
      var opticFiber = this.allInfos;
      var results = queryString
        ? opticFiber.filter(this.createFilter(queryString))
        : opticFiber;
      callback(results);
    },
    createFilter(queryString) {
      return opticFiber => {
        return opticFiber.value.toLowerCase().match(queryString.toLowerCase());
      };
    },
    getUrgentOption() {
      let param = {
        dictTypeCode: "10001",
      };
      apiDict(param)
        .then(res => {
          if (res.code == 200) {
            this.urgentOption = res?.data ?? [];
            this.urgentOption.reverse();
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    onSheetSave() {
      this.entering();
      this.$confirm("是否保存到草稿？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // this.$refs.sheetForm.validate(valid => {
          // if (valid) {
          if (this.sheetForm.title != "" && this.sheetForm.title != null) {
            let formData = new FormData();
            if (this.importForm.attachmentFileList.length > 0) {
              for (let item of this.importForm.attachmentFileList) {
                formData.append("orderFiles", item.raw);
              }
            }
            if (this.importForm.incidenceFileList.length > 0) {
              for (let item of this.importForm.incidenceFileList) {
                formData.append("affectFiles", item.raw);
              }
            }
            this.sheetSaveLoading = true;
            let self = this;
            let param = {
              sheetInfo: {
                networkTypeTop: 5, //集团通用流程
                sender: this.sheetForm.sender, //建单人
                senderDeptName: this.sheetForm.dept, //建单部门
                senderName: this.sheetForm.createUser, //建单人中文名
                senderDept: this.sheetForm.deptId, //建单部门ID
                sheetCreateTime: this.sheetForm.createTime, //建单时间
                sheetTitle: this.sheetForm.title, //工单主题
                createType: this.sheetForm.source, //工单来源
                alarmCreateTime: this.sheetForm.happenTime, //发生时间
                emergencyLevel: this.sheetForm.urgent, //紧急程度
                acceptTimeLimit: this.sheetForm.acceptTimeLimit, //受理时限
                processTimeLimit: this.sheetForm.estimatedProTimeLimit, //处理时限
                professionalType: this.sheetForm.special, //所属专业
                businessDown: this.sheetForm.businessDown, //业务中断
                networkType: self.sheetForm.netType, //== "25100507" ? "一干" : "国际", //网络类型, //网络类型
                // opticFiberCableSegment: this.sheetForm.opticalCable, //光缆名称
                mecNodeName: this.sheetForm.mecNodeName,
                cloudPoolType:
                  this.sheetForm.special == "26"
                    ? this.sheetForm.cloudPoolType
                    : "",
                orgType: this.sheetForm.alarmType, //告警类别
                neType: this.sheetForm.neType, //网元类型
                neName: this.sheetForm.neName, //网元名称
                workContent: this.sheetForm.jobComment, //工作内容
                faultPhenomenon: this.sheetForm.phenomenon, //故障现象
                effectRange: this.sheetForm.incidence, //影响范围
                falutComment: this.sheetForm.remarks, //备注
                agentDeptCode: this.sheetForm.builderZsOrgId, //主送是组织 传入组织ID
                agentManId: this.sheetForm.builderZsUserId, //主送是人员 传入人员ID
                agentMan: this.sheetForm.builderZsUserName, //主送人员 中文名称
                agentDeptName: this.sheetForm.builderZsOrgName, //主送部门 中文名称
                sheetStatus: 1, //1:保存到草稿 2:提交到待办
                seizeOrders: this.sheetForm.seizeOrders, //建单模式
                copyDeptCode: this.sheetForm.builderCsOrgId, //抄送是组织 传入组织ID
                copyManId: this.sheetForm.builderCsUserId, //抄送是用户 传入用户ID
                copyMan: this.sheetForm.builderCsUserName, //抄送人员中文名称
                copyDeptName: this.sheetForm.builderCsOrgName, //抄送部门中文名称
                // noticeMan: this.sheetForm.notifierUserName,
                // noticeManId: this.sheetForm.notifierUserId,
                // noticeDeptCode: this.sheetForm.notifierOrgId,
                // noticeDeptName: this.sheetForm.notifierOrgName,
                isSendSms: this.sheetForm.sms, //是否短信通知
                smsToUserid: this.sheetForm.recipientUserId,
                smsToUsername: this.sheetForm.recipientUserName,
                smsToDeptCode: this.sheetForm.recipientOrgId,
                smsToDeptName: this.sheetForm.recipientOrgName,
                sendContent:
                  this.sheetForm.sms == "0" ? null : this.sheetForm.sendContent,
                agentManDetail: this.sheetForm.agentManDetail,
                copyManDetail: this.sheetForm.copyManDetail,
                recipientDetailUserName: self.sheetForm.recipientDetailUserName,
                isShare:
                  this.sheetForm.special == "7" ? this.sheetForm.isShare : null,
                faultLevel: self.sheetForm.faultLevel,
              },
            };
            formData.append("jsonParam", JSON.stringify(param));
            //保存到草稿
            apiSaveToCommonFlowDraft(formData)
              .then(res => {
                if (res.status == "0") {
                  this.$message.success("保存成功");
                  this.closeAndTurnAroundMyDraft();
                } else {
                  this.$message.error("保存失败");
                }
                this.sheetSaveLoading = false;
              })
              .catch(error => {
                console.log(error);
                this.$message.error("保存失败");
                this.sheetSaveLoading = false;
              });
          } else {
            this.$message.warning("请填写主题后在做保存！");
            return false;
          }
          // });
        })
        .catch(() => {});
    },
    onSheetSubmit() {
      this.entering();
      this.$confirm("是否提交工单？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        closeOnClickModal: false,
        type: "warning",
      }).then(() => {
        this.$refs.sheetForm.validate((valid, a) => {
          if (this.uncheck(a)) {
            this.sheetCommitLoading = true;
            let formData = new FormData();

            if (this.importForm.attachmentFileList.length > 0) {
              for (let item of this.importForm.attachmentFileList) {
                formData.append("orderFiles", item.raw);
              }
            }
            if (this.importForm.incidenceFileList.length > 0) {
              for (let item of this.importForm.incidenceFileList) {
                formData.append("affectFiles", item.raw);
              }
            }
            let self = this;
            let param = {
              sheetInfo: {
                networkTypeTop: 5, //集团通用流程
                sender: this.sheetForm.sender, //建单人
                senderDeptName: this.sheetForm.dept, //建单部门
                senderName: this.sheetForm.createUser, //建单人中文名
                senderDept: this.sheetForm.deptId, //建单部门ID
                sheetCreateTime: this.sheetForm.createTime, //建单时间
                sheetTitle: this.sheetForm.title, //工单主题
                createType: this.sheetForm.source, //工单来源
                alarmCreateTime: this.sheetForm.happenTime, //发生时间
                emergencyLevel: this.sheetForm.urgent, //紧急程度
                acceptTimeLimit: this.sheetForm.acceptTimeLimit, //受理时限
                processTimeLimit: this.sheetForm.estimatedProTimeLimit, //处理时限
                professionalType: this.sheetForm.special, //所属专业
                businessDown: this.sheetForm.businessDown, //业务中断
                networkType: self.sheetForm.netType, //== "25100507" ? "一干" : "国际", //网络类型, //网络类型
                // opticFiberCableSegment: this.sheetForm.opticalCable, //光缆名称
                mecNodeName: this.sheetForm.mecNodeName,
                cloudPoolType:
                  this.sheetForm.special == "26"
                    ? this.sheetForm.cloudPoolType
                    : "",
                orgType: this.sheetForm.alarmType, //告警类别
                neType: this.sheetForm.neType, //网元类型
                neName: this.sheetForm.neName, //网元名称
                workContent: this.sheetForm.jobComment, //工作内容
                faultPhenomenon: this.sheetForm.phenomenon, //故障现象
                effectRange: this.sheetForm.incidence, //影响范围
                falutComment: this.sheetForm.remarks, //备注
                agentDeptCode: this.sheetForm.builderZsOrgId, //主送是组织 传入组织ID
                agentManId: this.sheetForm.builderZsUserId, //主送是人员 传入人员ID
                agentMan: this.sheetForm.builderZsUserName, //主送人员 中文名称
                agentDeptName: this.sheetForm.builderZsOrgName, //主送部门 中文名称
                sheetStatus: 2, //1:保存到草稿 2:提交到待办
                seizeOrders: this.sheetForm.seizeOrders, //建单模式
                copyDeptCode: this.sheetForm.builderCsOrgId, //抄送是组织 传入组织ID
                copyManId: this.sheetForm.builderCsUserId, //抄送是用户 传入用户ID
                copyMan: this.sheetForm.builderCsUserName, //抄送人员中文名称
                copyDeptName: this.sheetForm.builderCsOrgName, //抄送部门中文名称
                isSendSms: this.sheetForm.sms, //是否短信通知
                smsToUserid: this.sheetForm.recipientUserId,
                smsToUsername: this.sheetForm.recipientUserName,
                smsToDeptCode: this.sheetForm.recipientOrgId,
                smsToDeptName: this.sheetForm.recipientOrgName,
                sendContent:
                  this.sheetForm.sms == "0" ? null : this.sheetForm.sendContent,
                agentManDetail: this.sheetForm.agentManDetail,
                copyManDetail: this.sheetForm.copyManDetail,
                recipientDetailUserName: self.sheetForm.recipientDetailUserName,
                isShare:
                  this.sheetForm.special == "7" ? this.sheetForm.isShare : null,
                faultLevel: self.sheetForm.faultLevel,
              },
            };
            formData.append("jsonParam", JSON.stringify(param));
            apiBuildCommonFlowSingle(formData)
              .then(res => {
                if (res.status == "0") {
                  this.$message.success("工单提交成功");
                  this.closeAndTurnAroundTo();
                } else {
                  if (res.msg.indexOf(":")) {
                    var n = res.msg.split(":");
                    this.$message.error("工单提交失败，原因：" + n[1]);
                  } else {
                    this.$message.error("工单提交失败");
                  }
                  this.closeAndTurnAroundRead();
                }
                this.sheetCommitLoading = false;
              })
              .catch(error => {
                console.log(error);
                if (error.msg.indexOf(":")) {
                  var n = error.msg.split(":");
                  this.$message.error("工单提交失败，原因：" + n[1]);
                } else {
                  this.$message.error("工单提交失败");
                }
                this.closeAndTurnAroundRead();
                this.sheetCommitLoading = false;
              });
          } else {
            return false;
          }
        });
      });
    },
    closeAndTurnAroundRead() {
      this.$router.replace({
        name: "redirect",
        params: {
          skipType: "query",
          view: this.$route,
          isCurrentRoute: true,
        },
      });
    },
    onOpenPeopleDialog(diaSaveName) {
      this.isDiaOrgsUserTree = false;
      this.diaPeople.title = this.diaPeople.saveTitleMap[diaSaveName];
      this.diaPeople.saveName = diaSaveName;
      this.diaPeople.showOrgsTree =
        this.diaPeople.showOrgsTreeMap[diaSaveName] ?? true;
      this.diaPeople.showContactUserTab =
        this.diaPeople.showContactUserTabMap[diaSaveName] ?? false;
      this.diaPeople.showContactOrgTab =
        this.diaPeople.showContactOrgTabMap[diaSaveName] ?? false;
      this.diaPeople.visible = true;
      this.$nextTick(() => {
        this.isDiaOrgsUserTree = true;
      });
    },
    onSavePeople(val) {
      this[this.diaPeople.saveName](val);
    },
    resetForm() {
      this.$refs["sheetForm"].resetFields();
      this.organizeForm.builderZsList = [];
      this.organizeForm.builderZsListCopy = [];
      this.organizeForm.builderZsName = "";
      this.organizeForm.builderCsList = [];
      this.organizeForm.builderCsListCopy = [];
      this.organizeForm.builderCsName = "";
      this.organizeForm.recipientList = [];
      this.organizeForm.recipientListCopy = [];
      this.organizeForm.recipientName = "";
    },
    //建单人主送确定
    lordSentDetermine({
      usersChecked,
      orgsChecked,
      selectionUser,
      contactSelectionUser,
      contactSelectionOrg,
    }) {
      if (contactSelectionUser && contactSelectionUser.length > 0) {
        contactSelectionUser.map(item => {
          let zs = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.userName;
          });
          if (zs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderZsList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (selectionUser && selectionUser.length > 0) {
        selectionUser.map(item => {
          let zs = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.userName;
          });
          if (zs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderZsList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (usersChecked && usersChecked.length > 0) {
        usersChecked.map(item => {
          let zs = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.id;
          });
          if (zs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderZsList.push({
              bz: "user",
              id: item.id,
              name: item.name,
              orgName: item.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (contactSelectionOrg && contactSelectionOrg.length > 0) {
        contactSelectionOrg.map(item => {
          let zsOrg = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.orgId;
          });
          if (zsOrg > -1) {
            this.$message({
              message: "选择的组织已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderZsList.push({
              bz: "org",
              id: item.orgId + "",
              orgName: item.fullOrgName,
            });
          }
        });
      }
      if (orgsChecked && orgsChecked.length > 0) {
        orgsChecked.map(item => {
          let zsOrg = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.id;
          });
          let zsOrg2 = this.organizeForm.builderZsList.findIndex(val => {
            return val.bz === "org";
          });
          let flag = false;
          if (zsOrg > -1) {
            this.$message({
              message: "选择的组织已存在",
              type: "warning",
            });
            flag = true;
          }
          if (zsOrg2 > -1 && this.mainProfessionalType == "IT云") {
            this.$message({
              message: "it云只能选择一个组织",
              type: "warning",
            });
            flag = true;
          }
          if (!flag) {
            this.organizeForm.builderZsList.push({
              bz: "org",
              id: item.id,
              orgName: item.name,
            });
          }
        });
      }

      this.organizeForm.builderZsListCopy = JSON.parse(
        JSON.stringify(this.organizeForm.builderZsList)
      );
    },
    //抄送人确定
    ccDetermine({
      usersChecked,
      orgsChecked,
      selectionUser,
      contactSelectionUser,
      contactSelectionOrg,
    }) {
      if (contactSelectionUser && contactSelectionUser.length > 0) {
        contactSelectionUser.map(item => {
          let cs = this.organizeForm.builderCsList.findIndex(val => {
            return val.id === item.userName;
          });
          if (cs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderCsList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (selectionUser && selectionUser.length > 0) {
        selectionUser.map(item => {
          let cs = this.organizeForm.builderCsList.findIndex(val => {
            return val.id === item.userName;
          });
          if (cs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderCsList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (usersChecked && usersChecked.length > 0) {
        usersChecked.map(item => {
          let cs = this.organizeForm.builderCsList.findIndex(val => {
            return val.id === item.id;
          });
          if (cs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderCsList.push({
              bz: "user",
              id: item.id,
              name: item.name,
              orgName: item.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (contactSelectionOrg && contactSelectionOrg.length > 0) {
        contactSelectionOrg.map(item => {
          let csOrg = this.organizeForm.builderCsList.findIndex(val => {
            return val.id === item.orgId;
          });
          if (csOrg > -1) {
            this.$message({
              message: "选择的组织已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderCsList.push({
              bz: "org",
              id: item.orgId + "",
              orgName: item.fullOrgName,
            });
          }
        });
      }
      if (orgsChecked && orgsChecked.length > 0) {
        orgsChecked.map(item => {
          let csOrg = this.organizeForm.builderCsList.findIndex(val => {
            return val.bz === "org";
          });
          let csOrg2 = this.organizeForm.builderCsList.findIndex(val => {
            return val.id === item.id;
          });
          let flag = false;
          if (csOrg > -1 && this.mainProfessionalType == "IT云") {
            this.$message({
              message: "it云只能选择一个组织",
              type: "warning",
            });
            flag = true;
          }
          if (csOrg2 > -1) {
            this.$message({
              message: "选择的组织已存在",
              type: "warning",
            });
            flag = true;
          }

          if (!flag) {
            this.organizeForm.builderCsList.push({
              bz: "org",
              id: item.id,
              orgName: item.name,
            });
          }
        });
      }
      this.organizeForm.builderCsListCopy = JSON.parse(
        JSON.stringify(this.organizeForm.builderCsList)
      );
    },
    //接收人确定
    recipientDetermine({
      usersChecked,
      orgsChecked,
      selectionUser,
      contactSelectionUser,
    }) {
      if (contactSelectionUser && contactSelectionUser.length > 0) {
        contactSelectionUser.map(item => {
          let rec = this.organizeForm.recipientList.findIndex(val => {
            return val.id === item.userName;
          });
          if (rec > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.recipientList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (selectionUser && selectionUser.length > 0) {
        selectionUser.map(item => {
          let rec = this.organizeForm.recipientList.findIndex(val => {
            return val.id === item.userName;
          });
          if (rec > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.recipientList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      } else if (usersChecked && usersChecked.length > 0) {
        usersChecked.map(item => {
          let rec = this.organizeForm.recipientList.findIndex(val => {
            return val.id === item.id;
          });
          if (rec > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.recipientList.push({
              bz: "user",
              id: item.id,
              name: item.name,
              orgName: item.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      this.organizeForm.recipientListCopy = JSON.parse(
        JSON.stringify(this.organizeForm.recipientList)
      );
    },
    //附件选择
    attachmentBrowse() {
      this.attachmentDialogVisible = true;
    },
    changeFileData(data) {
      this.sheetForm.attachmentFile = data.fileName;
      this.importForm.attachmentFileList = data.attachmentFileList;
      this.attachmentDialogVisible = false;
    },
    closeAttachmentDialog() {
      this.attachmentDialogVisible = false;
    },
    //影响业务列表选择
    incidenceBrowse() {
      this.incidenceDialogVisible = true;
    },
    changeIncidenceFileData(data) {
      this.sheetForm.incidenceFile = data.fileName;
      this.importForm.incidenceFileList = data.attachmentFileList;
      this.incidenceDialogVisible = false;
    },
    closeIncidenceDialog() {
      this.incidenceDialogVisible = false;
    },
    stitchingAlgorithm(orgName, userName) {
      if (null == orgName) orgName = "";
      if (null == userName) userName = "";
      if (orgName.length !== 0 && userName.length !== 0) {
        return orgName + "," + userName;
      } else {
        if (orgName.length !== 0) {
          return orgName;
        } else if (userName.length !== 0) {
          return userName;
        } else {
          return "";
        }
      }
    },
    filterLordSentOrgNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    closeAndTurnAroundMyDraft() {
      let self = this;
      self.$store.dispatch("tagsView/delView", self.route).then(() => {
        self.$destroy();
      });
      self.$router.push({
        name: "backbone_myDraft",
      });
    },
    closeAndTurnAroundTo() {
      let self = this;
      self.$store.dispatch("tagsView/delView", self.route).then(() => {
        self.$destroy();
      });
      self.$router.push({
        name: "backbone_toDoRepairOrder",
        query: {
          globalUniqueID: sessionStorage.getItem("globalUniqueID"),
          token: this.token,
        },
      });
    },
    //多选移除
    toggleSelection(val) {
      if (this.multipleSelection == 0) {
        this.$message({
          type: "warning",
          message: "还没有选择移除的选项，请先选择要移除的选项！",
        });
        return;
      } else {
        this.multipleSelection.forEach(row => {
          this.handleClose(val, row);
        });
      }
      this.multipleSelection;
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    //移除对象
    handleClose(val, tag) {
      if (val == "builderZs") {
        this.organizeForm.builderZsList.splice(
          this.arrayIndex(this.organizeForm.builderZsList, tag),
          1
        );
        this.organizeForm.builderZsListCopy.splice(
          this.arrayIndex(this.organizeForm.builderZsListCopy, tag),
          1
        );
      }
      if (val == "builderCs") {
        this.organizeForm.builderCsList.splice(
          this.arrayIndex(this.organizeForm.builderCsList, tag),
          1
        );
        this.organizeForm.builderCsListCopy.splice(
          this.arrayIndex(this.organizeForm.builderCsListCopy, tag),
          1
        );
      }
      if (val == "recipient") {
        this.organizeForm.recipientList.splice(
          this.arrayIndex(this.organizeForm.recipientList, tag),
          1
        );
        this.organizeForm.recipientListCopy.splice(
          this.arrayIndex(this.organizeForm.recipientListCopy, tag),
          1
        );
      }
    },
    //找对应对象的索引值
    arrayIndex(val1, val2) {
      for (var i = 0, len = val1.length; i < len; i++) {
        var tag = val1[i];
        if (tag.id == val2.id) {
          return i;
        }
      }
    },
    //搜索
    search(val) {
      if (val == "builderZs" && this.organizeForm.builderZsList != null) {
        this.organizeForm.builderZsListCopy = [];
        this.organizeForm.builderZsList.forEach(row => {
          if (
            row.name == this.organizeForm.builderZsName ||
            row.orgName == this.organizeForm.builderZsName
          ) {
            this.organizeForm.builderZsListCopy.push(row);
          }
        });
      }
      if (val == "builderCs" && this.organizeForm.builderCsList != null) {
        this.organizeForm.builderCsListCopy = [];
        this.organizeForm.builderCsList.forEach(row => {
          if (
            row.name == this.organizeForm.builderCsName ||
            row.orgName == this.organizeForm.builderCsName
          ) {
            this.organizeForm.builderCsListCopy.push(row);
          }
        });
      }
      if (val == "recipient" && this.organizeForm.recipientList != null) {
        this.organizeForm.recipientListCopy = [];
        this.organizeForm.recipientList.forEach(row => {
          if (
            row.name == this.organizeForm.recipientName ||
            row.orgName == this.organizeForm.recipientName
          ) {
            this.organizeForm.recipientListCopy.push(row);
          }
        });
      }
    },
    //清除搜索项
    clear(val) {
      if (val == "builderZs") {
        this.organizeForm.builderZsName = "";
        this.organizeForm.builderZsListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.builderZsList)
        );
      }
      if (val == "builderCs") {
        this.organizeForm.builderCsName = "";
        this.organizeForm.builderCsListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.builderCsList)
        );
      }
      if (val == "recipient") {
        this.organizeForm.recipientName = "";
        this.organizeForm.recipientListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.recipientList)
        );
      }
    },
    entering() {
      if (
        this.organizeForm.builderZsList &&
        this.organizeForm.builderZsList.length > 0
      ) {
        let userList = this.organizeForm.builderZsList.filter((item, index) => {
          return item.bz == "user";
        });
        let orgList = this.organizeForm.builderZsList.filter((item, index) => {
          return item.bz == "org";
        });
        if (userList && userList.length > 0) {
          let usersCheckedName = userList.map(item => {
            return item.name;
          });
          this.sheetForm.builderZsUserName = usersCheckedName.join(",");
          let usersCheckedId = userList.map(item => {
            return item.id;
          });
          this.sheetForm.builderZsUserId = usersCheckedId.join(",");
          const userDetailName = userList.map(item => {
            return item.name + "-" + item.orgName + "-" + item.mobilePhone;
          });
          this.sheetForm.agentManDetail = userDetailName.join(",");
        }
        if (orgList && orgList.length > 0) {
          let orgsCheckedName = orgList.map(item => {
            return item.orgName;
          });
          this.sheetForm.builderZsOrgName = orgsCheckedName.join(",");
          let orgsCheckedId = orgList.map(item => {
            return item.id;
          });
          this.sheetForm.builderZsOrgId = orgsCheckedId.join(",");
        }
      }
      if (
        this.organizeForm.builderCsList &&
        this.organizeForm.builderCsList.length > 0
      ) {
        let userList = this.organizeForm.builderCsList.filter((item, index) => {
          return item.bz == "user";
        });
        let orgList = this.organizeForm.builderCsList.filter((item, index) => {
          return item.bz == "org";
        });
        if (userList && userList.length > 0) {
          let usersCheckedName = userList.map(item => {
            return item.name;
          });
          this.sheetForm.builderCsUserName = usersCheckedName.join(",");
          let usersCheckedId = userList.map(item => {
            return item.id;
          });
          this.sheetForm.builderCsUserId = usersCheckedId.join(",");
          const userDetailName = userList.map(item => {
            return item.name + "-" + item.orgName + "-" + item.mobilePhone;
          });
          this.sheetForm.copyManDetail = userDetailName.join(",");
        }
        if (orgList && orgList.length > 0) {
          let orgsCheckedName = orgList.map(item => {
            return item.orgName;
          });
          this.sheetForm.builderCsOrgName = orgsCheckedName.join(",");
          let orgsCheckedId = orgList.map(item => {
            return item.id;
          });
          this.sheetForm.builderCsOrgId = orgsCheckedId.join(",");
        }
      }
      if (
        this.organizeForm.recipientList &&
        this.organizeForm.recipientList.length > 0
      ) {
        let userList = this.organizeForm.recipientList.filter((item, index) => {
          return item.bz == "user";
        });
        if (userList && userList.length > 0) {
          let usersCheckedName = userList.map(item => {
            return item.name;
          });
          this.sheetForm.recipientUserName = usersCheckedName.join(",");
          let usersCheckedId = userList.map(item => {
            return item.id;
          });
          this.sheetForm.recipientUserId = usersCheckedId.join(",");
          const userDetailName = userList.map(item => {
            return item.name + "-" + item.orgName + "-" + item.mobilePhone;
          });
          this.sheetForm.recipientDetailUserName = userDetailName.join(",");
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.draft-eidt-page {
  .head-title {
    font-size: 24px;
    line-height: 28px;
  }
  .head-handle-wrap {
    text-align: right;
  }
  .card-title {
    font-size: 15px;
    letter-spacing: 0;
    padding-left: 10px;
  }

  ::v-deep .el-tree {
    padding-top: 15px;
    padding-left: 10px;
    // 不可全选样式
    .el-tree-node {
      .is-leaf + .el-checkbox .el-checkbox__inner {
        display: inline-block;
      }
      .el-checkbox .el-checkbox__inner {
        display: none;
      }
    }
  }

  .select_style {
    background: #b50b14;
    border-radius: 2px;
    border-color: #b50b14;
  }
}
</style>
