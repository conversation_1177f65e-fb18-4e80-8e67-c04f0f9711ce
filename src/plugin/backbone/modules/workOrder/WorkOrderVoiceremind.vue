<template>
  <div style="padding: 0">
    <head-content-layout class="page-wrap">
      <template #header>
        <el-row style="display: flex">
          <el-col :xs="24" :sm="12" :md="14" :lg="18" style="display: flex">
            <div>
              <div style="display: inline-block; margin-left: 5px">专业：</div>
              <dict-select
                :value.sync="seachData.professionalType"
                :dictId="10002"
                placeholder="请选择专业"
                style="width: 270px"
              />
            </div>
            <div>
              <div style="display: inline-block; margin-left: 20px">省份：</div>
              <dict-select
                :value.sync="seachData.province"
                :dictId="10053"
                placeholder="请选择省份"
                style="width: 240px"
              />
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :md="10" :lg="6" style="text-align: right">
            <div>
              <el-button type="primary" @click="seniorQuery">查询</el-button>
              <el-button @click="onResetForm">重置</el-button>
            </div>
          </el-col>
        </el-row>
        <div class="order__menuList">
          <el-button type="primary" @click="createSeup">+ 新建</el-button>
          <el-button @click="batchDelete">批量删除</el-button>
          <el-button @click="handlebatch('启用')">启用</el-button>
          <el-button @click="handlebatch('禁用')">禁用</el-button>
        </div>
      </template>
      <template #table>
        <remind-table
          :professionalType="seachData.professionalType"
          :province="seachData.province"
          ref="remindTable"
        />
      </template>
    </head-content-layout>
    <remind-add :title.sync="title" :visible.sync="visible" />
  </div>
</template>

<script>
import HeadContentLayout from "./components/HeadContentLayout.vue";
import DictSelect from "./components/DictSelect.vue";
import RemindTable from "./components/RemindTable.vue";
import RemindAdd from "./components/RemindAdd.vue";
export default {
  name: "WorkOrderVoiceremind",
  components: {
    HeadContentLayout,
    DictSelect,
    RemindTable,
    RemindAdd,
  },
  data() {
    return {
      //查询条件
      seachData: {
        professionalType: "", //专业
        province: "全国", //省份
      },
      //新增,编辑
      visible: false,
      title: "新增",
    };
  },
  methods: {
    //查询
    seniorQuery() {
      this.$refs.remindTable.getTablelListData();
    },
    //重置
    onResetForm() {
      this.seachData = {
        professionalType: "", //专业
        province: "", //省份
      };
    },
    //新增
    createSeup() {
      this.visible = true;
      this.title = "新增";
    },
    //批量删除
    batchDelete() {
      this.$refs.remindTable.batchDelete();
    },
    //启用禁用
    handlebatch(type) {
      if (type == "启用") {
        this.$refs.remindTable.gettRemindSatus(1);
      } else {
        this.$refs.remindTable.gettRemindSatus(0);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "./workOrderWaitDetail/assets/common.scss";
.order__menuList {
  margin-top: 10px;
  padding-bottom: 10px;
}
::v-deep .el-dialog__body {
  padding: 0;
  padding-right: 50px;
  padding-left: 30px;
}
::v-deep .el-form-item__content {
  position: relative;
  .item__unit {
    position: absolute;
    display: block;
    top: 0;
    left: 106px;
    color: #dcdfe6;
  }
}
::v-deep .el-dialog__wrapper {
  .el-dialog {
    margin-top: 6vh !important;
    .el-dialog__body {
      .el-form {
        .el-form-item,
        .el-form-item--mini {
          margin: 0;
          margin-bottom: 14px;
        }
      }
    }
  }
}
</style>
