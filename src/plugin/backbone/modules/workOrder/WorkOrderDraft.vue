<template>
  <div></div>
</template>

<script>
import { apiDict, apiInitOrderDraf } from "./api/CommonApi";

export default {
  name: "WorkOrderDraft",
  data() {
    return {
      routeData: {
        networkTypeTop: null, // 0集团 1省分
        professionalType: null, //专业
        sheetNo: "",
        orderType: "caogao",
      },
      professionalTypeMap: null,
      professionalType: null, //将智能监控的专业转换为EOMS的专业ID
      networkTypeTopSupervise: null,
    };
  },
  mounted() {
    this.routeData.networkTypeTop = this.$route.query.networkTypeTop;
    this.routeData.professionalType = this.$route.query.professionalType;
    this.routeData.sheetNo = this.$route.query.sheetNo;

    this.getProfessionalTypeMap();
  },
  methods: {
    getProfessionalTypeMap() {
      let _this = this;
      _this.professionalTypeMap = sessionStorage.getItem("ProfessionalTypeMap");
      if (_this.professionalTypeMap) {
        _this.professionalTypeMap = JSON.parse(_this.professionalTypeMap);
        _this.toDraftDetail();
      } else {
        let param = {
          dictTypeCode: "10002",
        };
        apiDict(param)
          .then(res => {
            if (res.code == 200 && res?.data && res?.data.length > 0) {
              _this.professionalTypeMap = {};
              res.data.forEach(function (item) {
                _this.professionalTypeMap[item.dictCodeOther] = item.dictCode;
              });

              _this.toDraftDetail();
              sessionStorage.setItem(
                "ProfessionalTypeMap",
                JSON.stringify(_this.professionalTypeMap)
              );
            }
          })
          .catch(error => {
            console.log(error);
          });
      }
    },
    toDraftDetail() {
      this.professionalType = this.professionalTypeMap[
        this.routeData.professionalType
      ];
      let toPage = "";
      if (this.routeData.networkTypeTop != 0) {
        if (this.routeData.networkTypeTop == 1) {
          toPage = "backbone_initiateSheet_provinceOrder";
        } else if (this.routeData.networkTypeTop == 2) {
          toPage =
            "backbone_initiateSheet_backbone_initiateSheet_commonProvinceOrder";
        } else if (this.routeData.networkTypeTop == 10) {
          toPage = "backbone_initiateSheet_supervise";
        }
      } else {
        if (this.professionalType == 3) {
          //骨干网
          toPage = "backbone_repairOrderCg";
        } else if (this.professionalType == 23) {
          //IT云设备
          toPage = "itCloud_repairOrderCg";
        } else if (this.professionalType == 22) {
          //通信云设备
          toPage = "commCloud_repairOrderCg";
        } else {
          //集团通用
          toPage = "commonFlow_repairOrderCg";
        }
      }
      this.routerPage(toPage);
    },
    routerPage(toPage) {
      this.$router.push({
        name: toPage,
        query: {
          orderType: this.routeData.orderType,
          showLayout: false,
          sheetNo: this.routeData.sheetNo,
        },
      });
    },
    getOrderDrafData() {
      let param = {
        sheetNo: this.routeData.sheetNo,
      };
      return new Promise(resolve => {
        apiInitOrderDraf(param).then(res => {
          if (res.status == 0) {
            this.networkTypeTopSupervise = res?.data?.networkTypeTop ?? null;
          }
          resolve("success");
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
