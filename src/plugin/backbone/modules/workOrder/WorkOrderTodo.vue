<template>
  <head-content-layout class="page-wrap">
    <!--    搜索-->
    <template #header>
      <el-form
        label-width="110px"
        :inline="false"
        class="demo-form-inline"
        label-position="right"
      >
        <template
          v-if="
            form.type != 'province' &&
            form.type != 'gt' &&
            form.type != 'sfty' &&
            form.type != 'commonFlow' &&
            form.type != 'core' &&
            form.type != 'ip' &&
            form.type != 'pt' &&
            form.type != 'ITy' &&
            form.type != 'txy' &&
            form.type != 'supervise' &&
            form.type != 'wireless' &&
            form.type != 'ggw'
          "
        >
          <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
            <el-col :span="5">
              <el-form-item label-width="0px">
                <el-input
                  v-model.trim="form.keyword"
                  placeholder="请输入工单主题或工单编号关键字"
                  style="width: 100%"
                  clearable
                >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="处理环节：">
                <dict-select
                  :value.sync="form.processing"
                  placeholder="请选择步骤"
                  style="width: 100%"
                  :dictId="dictId"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="紧急程度：">
                <dict-select
                  :value.sync="form.urgencyDegree"
                  :dictId="10001"
                  placeholder="请选择内容"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="到单时间：">
                <el-date-picker
                  v-model="form.arriveTimeRange"
                  type="datetimerange"
                  range-separator="-"
                  :start-placeholder="startTime"
                  :end-placeholder="endTime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :default-time="['00:00:00', '23:59:59']"
                  style="width: 100%"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col v-if="!showDefaultMoreSearch" :span="4">
              <el-form-item label-width="0px">
                <el-button type="primary" @click="seniorQuery">筛选</el-button>
                <el-button
                  type="text"
                  style="color: #606266"
                  @click="onDefaultSwitchMoreSearch"
                  >更多
                  <i class="el-icon-caret-bottom" style="color: #b50b14"></i
                ></el-button>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row
            v-show="showDefaultMoreSearch"
            type="flex"
            style="flex-wrap: wrap"
            :gutter="20"
          >
            <el-col :span="5">
              <el-form-item label="建单时间：">
                <el-date-picker
                  v-model="form.createTimeRange"
                  type="datetimerange"
                  range-separator="-"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :default-time="['00:00:00', '23:59:59']"
                  style="width: 100%"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item>
                <span slot="label" style="display: inline-block">
                  <el-tooltip
                    popper-class="my-popper"
                    effect="dark"
                    content="超时情况包括受理超时和处理超时，且排序优先级受理超时>处理超时"
                    placement="top"
                  >
                    <em class="el-icon-question" style="color: #b50b14"></em>
                  </el-tooltip>
                  排序规则：
                </span>
                <!-- <dict-select
                  :value.sync="form.sortingRules"
                  placeholder="请选择内容"
                  style="width: 100%"
                  :dictId="83001"
                /> -->
                <el-select
                  v-model="form.sortingRules"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="(item, i) in sortRulesData"
                    :key="i"
                    :label="item.dictName"
                    :value="item.dictCode"
                  >
                    <span style="float: left">{{ item.dictName }}</span>
                    <el-button
                      v-if="defaultSortIndex[selectedLabel] == item.dictCode"
                      class="defaultBtnCommon"
                      style="color: #b50b14"
                    >
                      默认
                    </el-button>
                    <el-button
                      v-else
                      @click="setDefaultSortIndex(item.dictCode)"
                      class="defaultBtnCommon"
                      style="color: #828282; text-decoration: underline"
                    >
                      设为默认
                    </el-button>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="10"></el-col>
            <el-col :span="3">
              <el-button type="primary" @click="seniorQuery">筛选</el-button>
              <el-button
                type="text"
                style="color: #606266"
                @click="onDefaultSwitchMoreSearch"
                >收起<i class="el-icon-caret-top" style="color: #b50b14"></i>
              </el-button>
            </el-col>
          </el-row>
        </template>
        <template v-if="form.type == 'ggw'">
          <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
            <el-col :span="5">
              <el-form-item label-width="0px">
                <el-input
                  v-model.trim="form.keyword"
                  placeholder="请输入工单主题或工单编号关键字"
                  style="width: 100%"
                  clearable
                >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="处理环节：">
                <dict-select
                  :value.sync="form.processing"
                  placeholder="请选择步骤"
                  style="width: 100%"
                  :dictId="dictId"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="紧急程度：">
                <dict-select
                  :value.sync="form.urgencyDegree"
                  :dictId="10001"
                  placeholder="请选择内容"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="到单时间：">
                <el-date-picker
                  v-model="form.arriveTimeRange"
                  type="datetimerange"
                  range-separator="-"
                  :start-placeholder="startTime"
                  :end-placeholder="endTime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :default-time="['00:00:00', '23:59:59']"
                  style="width: 100%"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col v-if="!showDefaultMoreSearch" :span="4">
              <el-form-item label-width="0px">
                <el-button type="primary" @click="seniorQuery">筛选</el-button>
                <el-button
                  type="text"
                  style="color: #606266"
                  @click="onDefaultSwitchMoreSearch"
                  >更多
                  <i class="el-icon-caret-bottom" style="color: #b50b14"></i
                ></el-button>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row
            v-show="showDefaultMoreSearch"
            type="flex"
            style="flex-wrap: wrap"
            :gutter="20"
          >
            <el-col :span="5">
              <el-form-item label="建单时间：">
                <el-date-picker
                  v-model="form.createTimeRange"
                  type="datetimerange"
                  range-separator="-"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :default-time="['00:00:00', '23:59:59']"
                  style="width: 100%"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item>
                <span slot="label" style="display: inline-block">
                  <el-tooltip
                    popper-class="my-popper"
                    effect="dark"
                    content="超时情况包括受理超时和处理超时，且排序优先级受理超时>处理超时"
                    placement="top"
                  >
                    <em class="el-icon-question" style="color: #b50b14"></em>
                  </el-tooltip>
                  排序规则：
                </span>
                <!-- <dict-select
                  :value.sync="form.sortingRules"
                  placeholder="请选择内容"
                  style="width: 100%"
                  :dictId="83001"
                /> -->
                <el-select
                  v-model="form.sortingRules"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="(item, i) in sortWorkOrderPriorityRulesData"
                    :key="i"
                    :label="item.dictName"
                    :value="item.dictCode"
                  >
                    <span style="float: left">{{ item.dictName }}</span>
                    <el-button
                      v-if="defaultSortIndex[selectedLabel] == item.dictCode"
                      class="defaultBtnCommon"
                      style="color: #b50b14"
                    >
                      默认
                    </el-button>
                    <el-button
                      v-else
                      @click="setDefaultSortIndex(item.dictCode)"
                      class="defaultBtnCommon"
                      style="color: #828282; text-decoration: underline"
                    >
                      设为默认
                    </el-button>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="10"></el-col>
            <el-col :span="3">
              <el-button type="primary" @click="seniorQuery">筛选</el-button>
              <el-button
                type="text"
                style="color: #606266"
                @click="onDefaultSwitchMoreSearch"
                >收起<i class="el-icon-caret-top" style="color: #b50b14"></i>
              </el-button>
            </el-col>
          </el-row>
        </template>
        <template v-if="form.type == 'wireless'">
          <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
            <el-col :span="5">
              <el-form-item label-width="0px">
                <el-input
                  v-model.trim="form.keyword"
                  placeholder="请输入工单主题或工单编号关键字"
                  style="width: 100%"
                  clearable
                >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="处理环节：">
                <dict-select
                  :value.sync="form.processing"
                  placeholder="请选择步骤"
                  style="width: 100%"
                  :dictId="dictId"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="覆盖场景：">
                <dict-select
                  :value.sync="form.coverScene"
                  placeholder="请选择覆盖场景"
                  style="width: 100%"
                  :dictId="81002"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="紧急程度：">
                <dict-select
                  :value.sync="form.urgencyDegree"
                  :dictId="10001"
                  placeholder="请选择内容"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>

            <el-col v-if="!showWirelessMoreSearch" :span="4">
              <el-form-item label-width="0px">
                <el-button type="primary" @click="seniorQuery">筛选</el-button>
                <el-button
                  type="text"
                  style="color: #606266"
                  @click="onWirelessSwitchMoreSearch"
                  >更多
                  <i class="el-icon-caret-bottom" style="color: #b50b14"></i
                ></el-button>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row
            v-show="showWirelessMoreSearch"
            type="flex"
            style="flex-wrap: wrap"
            :gutter="20"
          >
            <el-col :span="5">
              <el-form-item label="到单时间：">
                <el-date-picker
                  v-model="form.arriveTimeRange"
                  type="datetimerange"
                  range-separator="-"
                  :start-placeholder="startTime"
                  :end-placeholder="endTime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :default-time="['00:00:00', '23:59:59']"
                  style="width: 100%"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="建单时间：">
                <el-date-picker
                  v-model="form.createTimeRange"
                  type="datetimerange"
                  range-separator="-"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :default-time="['00:00:00', '23:59:59']"
                  style="width: 100%"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item>
                <span slot="label" style="display: inline-block">
                  <el-tooltip
                    popper-class="my-popper"
                    effect="dark"
                    content="超时情况包括受理超时和处理超时，且排序优先级受理超时>处理超时"
                    placement="top"
                  >
                    <em class="el-icon-question" style="color: #b50b14"></em>
                  </el-tooltip>
                  排序规则：
                </span>
                <!-- <dict-select
                  :value.sync="form.sortingRules"
                  placeholder="请选择内容"
                  style="width: 100%"
                  :dictId="83001"
                /> -->
                <el-select
                  v-model="form.sortingRules"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="(item, i) in sortRulesData"
                    :key="i"
                    :label="item.dictName"
                    :value="item.dictCode"
                  >
                    <span style="float: left">{{ item.dictName }}</span>
                    <el-button
                      v-if="defaultSortIndex[selectedLabel] == item.dictCode"
                      class="defaultBtnCommon"
                      style="color: #b50b14"
                    >
                      默认
                    </el-button>
                    <el-button
                      v-else
                      @click="setDefaultSortIndex(item.dictCode)"
                      class="defaultBtnCommon"
                      style="color: #828282; text-decoration: underline"
                    >
                      设为默认
                    </el-button>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="5"> </el-col>
            <el-col :span="3">
              <el-button type="primary" @click="seniorQuery">筛选</el-button>
              <el-button
                type="text"
                style="color: #606266"
                @click="onWirelessSwitchMoreSearch"
                >收起<i class="el-icon-caret-top" style="color: #b50b14"></i>
              </el-button>
            </el-col>
          </el-row>
        </template>
        <template v-if="form.type == 'province'">
          <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
            <el-col :span="5">
              <el-form-item label-width="0px">
                <el-input
                  v-model.trim="form.keyword"
                  placeholder="请输入工单主题或工单编号关键字"
                  style="width: 100%"
                  clearable
                >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="处理环节：">
                <dict-select
                  :value.sync="form.processing"
                  placeholder="请选择步骤"
                  style="width: 100%"
                  :dictId="dictId"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="紧急程度：">
                <dict-select
                  :value.sync="form.urgencyDegree"
                  :dictId="10001"
                  placeholder="请选择内容"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="到单时间：">
                <el-date-picker
                  v-model="form.arriveTimeRange"
                  type="datetimerange"
                  range-separator="-"
                  :start-placeholder="startTime"
                  :end-placeholder="endTime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :default-time="['00:00:00', '23:59:59']"
                  style="width: 100%"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col v-if="!provinceShowMoreSearch" :span="3">
              <el-form-item label-width="0px">
                <el-button type="primary" @click="seniorQuery">筛选</el-button>
                <el-button
                  type="text"
                  style="color: #606266"
                  @click="onProvinceSwitchMoreSearch"
                  >更多
                  <i class="el-icon-caret-bottom" style="color: #b50b14"></i
                ></el-button>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row
            v-show="provinceShowMoreSearch"
            type="flex"
            style="flex-wrap: wrap"
            :gutter="20"
          >
            <el-col :span="5">
              <el-form-item label="建单时间：">
                <el-date-picker
                  v-model="form.createTimeRange"
                  type="datetimerange"
                  range-separator="-"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :default-time="['00:00:00', '23:59:59']"
                  style="width: 100%"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="覆盖场景：">
                <dict-select
                  :value.sync="form.coverScene"
                  :dictId="81002"
                  placeholder="请选择内容"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item>
                <span slot="label" style="display: inline-block">
                  <el-tooltip
                    popper-class="my-popper"
                    effect="dark"
                    content="超时情况包括受理超时和处理超时，且排序优先级受理超时>处理超时"
                    placement="top"
                  >
                    <em class="el-icon-question" style="color: #b50b14"></em>
                  </el-tooltip>
                  排序规则：
                </span>
                <el-select
                  v-model="form.sortingRules"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="(item, i) in sortRulesData"
                    :key="i"
                    :label="item.dictName"
                    :value="item.dictCode"
                  >
                    <span style="float: left">{{ item.dictName }}</span>
                    <el-button
                      v-if="defaultSortIndex[selectedLabel] == item.dictCode"
                      class="defaultBtnCommon"
                      style="color: #b50b14"
                    >
                      默认
                    </el-button>
                    <el-button
                      v-else
                      @click="setDefaultSortIndex(item.dictCode)"
                      class="defaultBtnCommon"
                      style="color: #828282; text-decoration: underline"
                    >
                      设为默认
                    </el-button>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="5"> </el-col>
            <el-col :span="3">
              <el-button type="primary" @click="seniorQuery">筛选</el-button>
              <el-button
                type="text"
                style="color: #606266"
                @click="onProvinceSwitchMoreSearch"
                >收起<i class="el-icon-caret-top" style="color: #b50b14"></i>
              </el-button>
            </el-col>
          </el-row>
        </template>
        <template v-if="form.type == 'gt'">
          <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
            <el-col :span="5">
              <el-form-item label-width="0px">
                <el-input
                  v-model.trim="form.keyword"
                  placeholder="请输入工单主题或工单编号关键字"
                  style="width: 100%"
                  clearable
                >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="处理环节：">
                <dict-select
                  :value.sync="form.processing"
                  placeholder="请选择步骤"
                  style="width: 100%"
                  :dictId="dictId"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="到单时间：">
                <el-date-picker
                  v-model="form.arriveTimeRange"
                  type="datetimerange"
                  range-separator="-"
                  :start-placeholder="startTime"
                  :end-placeholder="endTime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :default-time="['00:00:00', '23:59:59']"
                  style="width: 100%"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="要求完成时间：">
                <el-date-picker
                  v-model="form.finishTimeRange"
                  type="datetimerange"
                  range-separator="-"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :default-time="['00:00:00', '23:59:59']"
                  style="width: 100%"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col v-if="!gtShowMoreSearch" :span="3">
              <el-form-item label-width="0px">
                <el-button type="primary" @click="seniorQuery">筛选</el-button>
                <el-button
                  type="text"
                  style="color: #606266"
                  @click="onGtSwitchMoreSearch"
                  >更多
                  <i class="el-icon-caret-bottom" style="color: #b50b14"></i
                ></el-button>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row
            v-show="gtShowMoreSearch"
            type="flex"
            style="flex-wrap: wrap"
            :gutter="20"
          >
            <el-col :span="5">
              <el-form-item>
                <span slot="label" style="display: inline-block">
                  <el-tooltip
                    popper-class="my-popper"
                    effect="dark"
                    content="超时情况包括受理超时和处理超时，且排序优先级受理超时>处理超时"
                    placement="top"
                  >
                    <em class="el-icon-question" style="color: #b50b14"></em>
                  </el-tooltip>
                  排序规则：
                </span>
                <el-select
                  v-model="form.sortingRules"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="(item, i) in sortRulesData"
                    :key="i"
                    :label="item.dictName"
                    :value="item.dictCode"
                  >
                    <span style="float: left">{{ item.dictName }}</span>
                    <el-button
                      v-if="defaultSortIndex[selectedLabel] == item.dictCode"
                      class="defaultBtnCommon"
                      style="color: #b50b14"
                    >
                      默认
                    </el-button>
                    <el-button
                      v-else
                      @click="setDefaultSortIndex(item.dictCode)"
                      class="defaultBtnCommon"
                      style="color: #828282; text-decoration: underline"
                    >
                      设为默认
                    </el-button>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="5"> </el-col>
            <el-col :span="5"> </el-col>
            <el-col :span="5"> </el-col>
            <el-col :span="3">
              <el-button type="primary" @click="seniorQuery">筛选</el-button>
              <el-button
                type="text"
                style="color: #606266"
                @click="onGtSwitchMoreSearch"
                >收起<i class="el-icon-caret-top" style="color: #b50b14"></i>
              </el-button>
            </el-col>
          </el-row>
        </template>
        <template v-if="form.type == 'sfty'">
          <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
            <el-col :span="5">
              <el-form-item label-width="0px">
                <el-input
                  v-model.trim="form.keyword"
                  placeholder="请输入工单主题或工单编号关键字"
                  style="width: 100%"
                  clearable
                >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="处理环节：">
                <dict-select
                  :value.sync="form.processing"
                  placeholder="请选择步骤"
                  style="width: 100%"
                  :dictId="30022"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="到单时间：">
                <el-date-picker
                  v-model="form.arriveTimeRange"
                  type="datetimerange"
                  range-separator="-"
                  :start-placeholder="startTime"
                  :end-placeholder="endTime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :default-time="['00:00:00', '23:59:59']"
                  style="width: 100%"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-checkbox class="dfdCla" v-model="form.isAlarmClear"
                >待返单工单</el-checkbox
              >
            </el-col>
            <el-col v-if="!sftyShowMoreSearch" :span="3">
              <el-form-item label-width="0px">
                <el-button type="primary" @click="seniorQuery">筛选</el-button>
                <el-button
                  type="text"
                  style="color: #606266"
                  @click="onSftySwitchMoreSearch"
                  >更多
                  <i class="el-icon-caret-bottom" style="color: #b50b14"></i
                ></el-button>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row
            v-show="sftyShowMoreSearch"
            type="flex"
            style="flex-wrap: wrap"
            :gutter="20"
          >
            <el-col :span="5">
              <el-form-item label="建单时间：">
                <el-date-picker
                  v-model="form.createTimeRange"
                  type="datetimerange"
                  range-separator="-"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :default-time="['00:00:00', '23:59:59']"
                  style="width: 100%"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="紧急程度：">
                <dict-select
                  :value.sync="form.urgencyDegree"
                  :dictId="10300"
                  placeholder="请选择内容"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="故障专业：">
                <dict-select
                  :value.sync="form.professionalType"
                  :dictId="810002"
                  placeholder="请选择内容"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5" v-if="form.professionalType == 7">
              <el-form-item label="覆盖场景：">
                <dict-select
                  :value.sync="form.coverScene"
                  :dictId="81002"
                  placeholder="请选择内容"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="工单来源：">
                <dict-select
                  :value.sync="form.source"
                  placeholder="请选择内容"
                  style="width: 100%"
                  :dictId="10003"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item>
                <span slot="label" style="display: inline-block">
                  <el-tooltip
                    popper-class="my-popper"
                    effect="dark"
                    content="超时情况包括受理超时和处理超时，且排序优先级受理超时>处理超时"
                    placement="top"
                  >
                    <em class="el-icon-question" style="color: #b50b14"></em>
                  </el-tooltip>
                  排序规则：
                </span>
                <el-select
                  v-model="form.sortingRules"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="(item, i) in sortRulesData"
                    :key="i"
                    :label="item.dictName"
                    :value="item.dictCode"
                  >
                    <span style="float: left">{{ item.dictName }}</span>
                    <el-button
                      v-if="defaultSortIndex[selectedLabel] == item.dictCode"
                      class="defaultBtnCommon"
                      style="color: #b50b14"
                    >
                      默认
                    </el-button>
                    <el-button
                      v-else
                      @click="setDefaultSortIndex(item.dictCode)"
                      class="defaultBtnCommon"
                      style="color: #828282; text-decoration: underline"
                    >
                      设为默认
                    </el-button>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="15" v-if="form.professionalType != 7"> </el-col>
            <el-col :span="10" v-if="form.professionalType == 7"> </el-col>
            <el-col :span="3">
              <el-button type="primary" @click="seniorQuery">筛选</el-button>
              <el-button
                type="text"
                style="color: #606266"
                @click="onSftySwitchMoreSearch"
                >收起<i class="el-icon-caret-top" style="color: #b50b14"></i>
              </el-button>
            </el-col>
          </el-row>
        </template>
        <template
          v-if="
            form.type == 'commonFlow' ||
            form.type == 'core' ||
            form.type == 'ip' ||
            form.type == 'pt'
          "
        >
          <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
            <el-col :span="5">
              <el-form-item label-width="0px">
                <el-input
                  v-model.trim="form.keyword"
                  placeholder="请输入工单主题或工单编号关键字"
                  style="width: 100%"
                  clearable
                >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="处理环节：">
                <dict-select
                  :value.sync="form.processing"
                  placeholder="请选择步骤"
                  style="width: 100%"
                  :dictId="dictId"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="紧急程度：">
                <dict-select
                  :value.sync="form.urgencyDegree"
                  :dictId="10001"
                  placeholder="请选择内容"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="到单时间：">
                <el-date-picker
                  v-model="form.arriveTimeRange"
                  type="datetimerange"
                  range-separator="-"
                  :start-placeholder="startTime"
                  :end-placeholder="endTime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :default-time="['00:00:00', '23:59:59']"
                  style="width: 100%"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="5">
              <el-form-item label="建单时间：">
                <el-date-picker
                  v-model="form.createTimeRange"
                  type="datetimerange"
                  range-separator="-"
                  :start-placeholder="startTime"
                  :end-placeholder="endTime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :default-time="['00:00:00', '23:59:59']"
                  style="width: 100%"
                >
                </el-date-picker>
              </el-form-item>
            </el-col> -->
            <el-col v-if="!showMoreSearch" :span="3">
              <el-form-item label-width="0px">
                <el-button type="primary" @click="seniorQuery">筛选</el-button>
                <el-button
                  type="text"
                  style="color: #606266"
                  v-if="!showMoreSearch"
                  @click="onSwitchMoreSearch"
                  >更多
                  <i class="el-icon-caret-bottom" style="color: #b50b14"></i
                ></el-button>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row
            v-show="showMoreSearch"
            type="flex"
            style="flex-wrap: wrap"
            :gutter="20"
          >
            <el-col :span="5">
              <el-form-item label="建单时间：">
                <el-date-picker
                  v-model="form.createTimeRange"
                  type="datetimerange"
                  range-separator="-"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :default-time="['00:00:00', '23:59:59']"
                  style="width: 100%"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="故障专业：">
                <dict-select
                  :value.sync="form.professionalType"
                  :dictId="proTypeDictId"
                  placeholder="请选择内容"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="工单来源：">
                <dict-select
                  :value.sync="form.source"
                  placeholder="请选择内容"
                  style="width: 100%"
                  :dictId="10003"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="建单人：">
                <input-tag
                  v-model="form.createUser"
                  :readonly="true"
                  @select="onOpenPeopleDialog('builderDetermine')"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row
            v-show="showMoreSearch"
            type="flex"
            style="flex-wrap: wrap"
            :gutter="20"
          >
            <el-col :span="5">
              <el-form-item label-width="28px">
                <el-checkbox class="dfdCla" v-model="form.isAlarmClear"
                  >待返单工单</el-checkbox
                >
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item>
                <span slot="label" style="display: inline-block">
                  <el-tooltip
                    popper-class="my-popper"
                    effect="dark"
                    content="超时情况包括受理超时和处理超时，且排序优先级受理超时>处理超时"
                    placement="top"
                  >
                    <em class="el-icon-question" style="color: #b50b14"></em>
                  </el-tooltip>
                  排序规则：
                </span>
                <!-- <dict-select
                  :value.sync="form.sortingRules"
                  placeholder="请选择内容"
                  style="width: 100%"
                  :dictId="83001"
                /> -->
                <el-select
                  v-model="form.sortingRules"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="(item, i) in sortWorkOrderPriorityRulesData"
                    :key="i"
                    :label="item.dictName"
                    :value="item.dictCode"
                  >
                    <span style="float: left">{{ item.dictName }}</span>
                    <el-button
                      v-if="defaultSortIndex[selectedLabel] == item.dictCode"
                      class="defaultBtnCommon"
                      style="color: #b50b14"
                    >
                      默认
                    </el-button>
                    <el-button
                      v-else
                      @click="setDefaultSortIndex(item.dictCode)"
                      class="defaultBtnCommon"
                      style="color: #828282; text-decoration: underline"
                    >
                      设为默认
                    </el-button>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="10"> </el-col>
            <el-col :span="3">
              <el-button type="primary" @click="seniorQuery">筛选</el-button>
              <el-button
                type="text"
                style="color: #606266"
                @click="onSwitchMoreSearch"
                >收起<i class="el-icon-caret-top" style="color: #b50b14"></i>
              </el-button>
            </el-col>
          </el-row>
        </template>
        <template v-if="form.type == 'ITy' || form.type == 'txy'">
          <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
            <el-col :span="5">
              <el-form-item label-width="0px">
                <el-input
                  v-model.trim="form.keyword"
                  placeholder="请输入工单主题或工单编号关键字"
                  style="width: 100%"
                  clearable
                >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="处理环节：">
                <dict-select
                  :value.sync="form.processing"
                  placeholder="请选择步骤"
                  style="width: 100%"
                  :dictId="dictId"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="到单时间：">
                <el-date-picker
                  v-model="form.arriveTimeRange"
                  type="datetimerange"
                  range-separator="-"
                  :start-placeholder="startTime"
                  :end-placeholder="endTime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :default-time="['00:00:00', '23:59:59']"
                  style="width: 100%"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="5">
              <el-form-item label="建单时间：">
                <el-date-picker
                  v-model="form.createTimeRange"
                  type="datetimerange"
                  range-separator="-"
                  :start-placeholder="startTime"
                  :end-placeholder="endTime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :default-time="['00:00:00', '23:59:59']"
                  style="width: 100%"
                >
                </el-date-picker>
              </el-form-item>
            </el-col> -->
            <el-col :span="5">
              <el-form-item label-width="28px">
                <el-checkbox class="dfdCla" v-model="form.isAlarmClear"
                  >待返单工单</el-checkbox
                >
              </el-form-item>
            </el-col>

            <el-col v-if="!cloudShowMoreSearch" :span="3">
              <el-form-item label-width="0px">
                <el-button type="primary" @click="seniorQuery">筛选</el-button>
                <el-button
                  type="text"
                  style="color: #606266"
                  v-if="!cloudShowMoreSearch"
                  @click="onCloudSwitchMoreSearch"
                  >更多
                  <i class="el-icon-caret-bottom" style="color: #b50b14"></i
                ></el-button>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row
            v-show="cloudShowMoreSearch"
            type="flex"
            style="flex-wrap: wrap"
            :gutter="20"
          >
            <el-col :span="5">
              <el-form-item label="建单时间：">
                <el-date-picker
                  v-model="form.createTimeRange"
                  type="datetimerange"
                  range-separator="-"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :default-time="['00:00:00', '23:59:59']"
                  style="width: 100%"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="紧急程度：">
                <dict-select
                  :value.sync="form.urgencyDegree"
                  :dictId="10001"
                  placeholder="请选择内容"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item>
                <span slot="label" style="display: inline-block">
                  <el-tooltip
                    popper-class="my-popper"
                    effect="dark"
                    content="超时情况包括受理超时和处理超时，且排序优先级受理超时>处理超时"
                    placement="top"
                  >
                    <em class="el-icon-question" style="color: #b50b14"></em>
                  </el-tooltip>
                  排序规则：
                </span>
                <!-- <dict-select
                  :value.sync="form.sortingRules"
                  placeholder="请选择内容"
                  style="width: 100%"
                  :dictId="83001"
                /> -->
                <el-select
                  v-model="form.sortingRules"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="(item, i) in sortWorkOrderPriorityRulesData"
                    :key="i"
                    :label="item.dictName"
                    :value="item.dictCode"
                  >
                    <span style="float: left">{{ item.dictName }}</span>
                    <el-button
                      v-if="defaultSortIndex[selectedLabel] == item.dictCode"
                      class="defaultBtnCommon"
                      style="color: #b50b14"
                    >
                      默认
                    </el-button>
                    <el-button
                      v-else
                      @click="setDefaultSortIndex(item.dictCode)"
                      class="defaultBtnCommon"
                      style="color: #828282; text-decoration: underline"
                    >
                      设为默认
                    </el-button>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="5"> </el-col>
            <!-- <el-col :span="5"> </el-col> -->
            <el-col :span="3">
              <el-button type="primary" @click="seniorQuery">筛选</el-button>
              <el-button
                type="text"
                style="color: #606266"
                @click="onCloudSwitchMoreSearch"
                >收起<i class="el-icon-caret-top" style="color: #b50b14"></i>
              </el-button>
            </el-col>
          </el-row>
        </template>
        <template v-if="form.type == 'supervise'">
          <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
            <el-col :span="5">
              <el-form-item label-width="0px">
                <el-input
                  v-model.trim="form.keyword"
                  placeholder="请输入工单主题或工单编号关键字"
                  style="width: 100%"
                  clearable
                >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="处理环节：">
                <dict-select
                  :value.sync="form.processing"
                  placeholder="请选择步骤"
                  style="width: 100%"
                  :dictId="dictId"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="所属专业：">
                <dict-select
                  :value.sync="form.professionalType"
                  :dictId="811038"
                  placeholder="请选择内容"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>

            <el-col :span="5">
              <el-form-item label="到单时间：">
                <el-date-picker
                  v-model="form.arriveTimeRange"
                  type="datetimerange"
                  range-separator="-"
                  :start-placeholder="startTime"
                  :end-placeholder="endTime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :default-time="['00:00:00', '23:59:59']"
                  style="width: 100%"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col v-if="!showDefaultMoreSearch" :span="4">
              <el-form-item label-width="0px">
                <el-button type="primary" @click="seniorQuery">筛选</el-button>
                <el-button
                  type="text"
                  style="color: #606266"
                  @click="onDefaultSwitchMoreSearch"
                  >更多
                  <i class="el-icon-caret-bottom" style="color: #b50b14"></i
                ></el-button>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row
            v-show="showDefaultMoreSearch"
            type="flex"
            style="flex-wrap: wrap"
            :gutter="20"
            ><el-col :span="5">
              <el-form-item label="建单时间：">
                <el-date-picker
                  v-model="form.createTimeRange"
                  type="datetimerange"
                  range-separator="-"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :default-time="['00:00:00', '23:59:59']"
                  style="width: 100%"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="紧急程度：">
                <dict-select
                  :value.sync="form.urgencyDegree"
                  :dictId="10001"
                  placeholder="请选择内容"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item>
                <span slot="label" style="display: inline-block">
                  <el-tooltip
                    popper-class="my-popper"
                    effect="dark"
                    content="超时情况包括受理超时和处理超时，且排序优先级受理超时>处理超时"
                    placement="top"
                  >
                    <em class="el-icon-question" style="color: #b50b14"></em>
                  </el-tooltip>
                  排序规则：
                </span>
                <!-- <dict-select
                  :value.sync="form.sortingRules"
                  placeholder="请选择内容"
                  style="width: 100%"
                  :dictId="83001"
                /> -->
                <el-select
                  v-model="form.sortingRules"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="(item, i) in sortWorkOrderPriorityRulesData"
                    :key="i"
                    :label="item.dictName"
                    :value="item.dictCode"
                  >
                    <span style="float: left">{{ item.dictName }}</span>
                    <el-button
                      v-if="defaultSortIndex[selectedLabel] == item.dictCode"
                      class="defaultBtnCommon"
                      style="color: #b50b14"
                    >
                      默认
                    </el-button>
                    <el-button
                      v-else
                      @click="setDefaultSortIndex(item.dictCode)"
                      class="defaultBtnCommon"
                      style="color: #828282; text-decoration: underline"
                    >
                      设为默认
                    </el-button>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="5"> </el-col>
            <el-col :span="3">
              <el-button type="primary" @click="seniorQuery">筛选</el-button>
              <el-button
                type="text"
                style="color: #606266"
                @click="onDefaultSwitchMoreSearch"
                >收起<i class="el-icon-caret-top" style="color: #b50b14"></i>
              </el-button>
            </el-col>
          </el-row>
        </template>
      </el-form>
    </template>
    <!--   tab -->
    <template #contentHeader>
      <el-row
        ref="contentHeader"
        :gutter="20"
        class="content-header"
        v-loading="tableLoading"
        element-loading-spinner=" "
      >
        <el-col :xs="16" :sm="16" :md="16" :lg="18" style="padding-right: 0px">
          <filter-total
            v-loading="tabMenuLoading"
            element-loading-text="数据加载中..."
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.8)"
            :value.sync="form.type"
            :filters="filterList"
            :totals="filterTotals"
            @onConHeadFilterChange="onConHeadFilterChange"
          ></filter-total>
        </el-col>
        <el-col
          :xs="8"
          :sm="8"
          :md="8"
          :lg="6"
          style="text-align: right; padding-left: 0px"
        >
          <div>
            <el-button
              v-if="provincesBtnShow"
              type="primary"
              @click="onSelectAll"
              >全选</el-button
            >
            <el-button
              v-if="provincesBtnShow"
              type="primary"
              @click="onBatchTransfer"
              >批量转派</el-button
            >
            <el-button
              v-if="toAcceptShow"
              type="primary"
              @click="onBatchAccept"
              v-loading.fullscreen.lock="acceptFullscreenLoading"
              >批量受理</el-button
            >
            <!--骨干网批量操作按钮-->
            <el-button
              v-if="backboneBtnShow"
              type="primary"
              @click="onSelectAllJtty"
              >全选</el-button
            >
            <el-button
              v-if="backboneBtnShow"
              type="primary"
              @click="onBatchAcceptByBackbone"
              v-loading.fullscreen.lock="acceptFullscreenLoading"
              >批量受理</el-button
            >
            <el-button
              v-if="backboneBtnShow"
              type="primary"
              @click="onBatchTransfer"
              >批量转派</el-button
            >
            <!--无线网批量操作按钮-->
            <el-button
              v-if="wirelessBtnShow"
              type="primary"
              @click="onSelectAllJtty"
              >全选</el-button
            >
            <el-button
              v-if="wirelessBtnShow"
              type="primary"
              @click="onBatchAcceptByWireless"
              v-loading.fullscreen.lock="acceptFullscreenLoading"
              >批量受理</el-button
            >
            <!--集团通用批量操作按钮-->
            <el-button
              v-if="jttyBtnShow"
              type="primary"
              @click="onSelectAllJtty"
              >全选</el-button
            >
            <el-button
              v-if="jttyBtnShow"
              type="primary"
              @click="onBatchAcceptByJtty"
              v-loading.fullscreen.lock="acceptFullscreenLoading"
              >批量受理</el-button
            >
            <el-button
              v-if="jttyBtnShow"
              type="primary"
              @click="onBatchZhuanbanByJtty"
              >批量转办</el-button
            >
            <!--集团督办批量操作按钮-->
            <!-- <el-button
              v-if="superviseBtnShow"
              type="primary"
              @click="onSelectAllJtty"
              >全选</el-button
            >
            <el-button
              v-if="superviseBtnShow"
              type="primary"
              @click="onBatchAcceptBySupervise"
              v-loading.fullscreen.lock="acceptFullscreenLoading"
              >批量受理</el-button
            > -->
            <el-button
              v-if="comTransShow"
              type="primary"
              @click="onSelectAllZhuanban"
              >全选</el-button
            >
            <el-button
              v-if="comTransShow"
              type="primary"
              @click="onBatchAcceptSFTY"
              v-loading.fullscreen.lock="acceptFullscreenLoading"
              >批量受理</el-button
            >
            <el-button
              v-if="comTransShow"
              type="primary"
              @click="onBatchZhuanban"
              >批量转办</el-button
            >
            <el-button
              v-if="batchAlarmClearBtnShow"
              type="primary"
              @click="onBatchAlarmClear"
              >批量告警清除</el-button
            >
            <el-button type="primary" @click="getTableData('senior')"
              >刷新</el-button
            >
          </div>
        </el-col>
      </el-row>
    </template>

    <!--    列表-->
    <template #table>
      <el-table
        ref="table"
        :data="tableData"
        stripe
        @selection-change="onSelectionChange"
        height="100%"
        v-loading="tableLoading"
        element-loading-text="努力加载中，请耐心等待"
      >
        <el-table-column
          type="selection"
          width="50"
          :index="indexMethod"
          :selectable="selectDisableRoom"
          label-class-name="select-all"
        />
        <el-table-column key="1" label="工单类别" width="120px">
          <template slot-scope="scope">
            <el-tag :class="getSheetTypeClass(scope.row)">
              <span v-if="scope.row.networkTypeTop == 1">省分</span>
              <span v-if="scope.row.networkTypeTop == 4">高铁</span>
              <span v-if="scope.row.networkTypeTop == 2">省分</span>
              <span v-if="scope.row.networkTypeTop == 5">集团</span>
              <span v-if="scope.row.networkTypeTop == 7">核心网</span>
              <span v-if="scope.row.networkTypeTop == 8">IP专业</span>
              <span v-if="scope.row.networkTypeTop == 9">平台</span>
              <span
                v-else-if="
                  scope.row.networkTypeTop == 0 &&
                  scope.row.professionalType == '传输网'
                "
                >骨干传输</span
              >
              <span
                v-else-if="
                  scope.row.networkTypeTop == 0 &&
                  scope.row.professionalType == 'IT云设备'
                "
                >IT云</span
              >
              <span
                v-else-if="
                  scope.row.networkTypeTop == 0 &&
                  scope.row.professionalType == '通信云'
                "
                >通信云</span
              >
              <span v-else-if="scope.row.networkTypeTop == 6">无线网</span>
              <span v-else-if="scope.row.networkTypeTop == 10">GNOC督办</span>
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column width="320px">
          <template slot="header" slot-scope="scope">
            <span>工单编号</span>
            <!-- v-if="
                form.type == 'province' ||
                form.type == 'sfty' ||
                form.type == 'gt'
              " -->
            <el-popover
              title="标签筛选"
              v-model="popoverVisible"
              placement="right"
              width="300"
              trigger="click"
              :ref="'sheetNo_' + scope.$index"
            >
              <el-tabs type="border-card" v-model="activeName">
                <el-tab-pane label="标签" name="tags">
                  <el-tree
                    ref="tagTree"
                    :data="treeData"
                    show-checkbox
                    node-key="label"
                    :props="defaultProps"
                  >
                  </el-tree>
                </el-tab-pane>
              </el-tabs>
              <base-icon
                slot="reference"
                icon-class="icon-filter"
                style="
                  width: 20px;
                  height: 30px;
                  vertical-align: middle;
                  cursor: pointer;
                "
              ></base-icon>
              <div style="float: right; margin-top: 10px">
                <el-button type="primary" @click="filterTree()"
                  >确 定</el-button
                >
                <el-button @click="closePopOver()">重 置</el-button>
              </div>
            </el-popover>
          </template>
          <template slot-scope="scope">
            <div :style="getSheetNoStyle(scope.row)">
              <el-button
                type="text"
                class="sheetNo_style"
                @click="toDoDetail(scope.row)"
                ><span style="font-size: 14px">{{
                  scope.row.sheetNo
                }}</span></el-button
              >
              <span v-if="scope.row.suspendStatus == 1" class="hang__order"
                >挂</span
              >
            </div>
            <div
              v-if="scope.row.workOrderTag.length > 0"
              style="position: absolute; bottom: 10px"
            >
              <div class="tag-container">
                <el-link
                  v-show="scope.row.workOrderTag.length > 3"
                  :key="'linkLeft_' + scope.$index"
                  @click="scrollLeft('scrollWrapper_' + scope.$index)"
                  style="padding: 2px 3px"
                  plain
                  icon="el-icon-caret-left"
                  :underline="false"
                ></el-link>

                <div
                  :ref="'scrollWrapper_' + scope.$index"
                  class="scroll-wrapper"
                >
                  <div class="tags">
                    <el-tag
                      v-for="item in scope.row.workOrderTag"
                      :color="item.color"
                      style="color: #fff"
                    >
                      {{ item.label }}
                    </el-tag>
                  </div>
                </div>
                <el-link
                  v-show="scope.row.workOrderTag.length > 3"
                  :key="'linkRight_' + scope.$index"
                  @click="scrollRight('scrollWrapper_' + scope.$index)"
                  style="padding: 2px 3px"
                  plain
                  icon="el-icon-caret-right"
                  :underline="false"
                ></el-link>
              </div>
              <!-- <div class="tags-list" style="width: 192px" ref="totalLists">

                <el-link
                  @click="arrowBack"
                  v-if="showButton"
                  style="padding: 2px 3px"
                  plain
                  icon="el-icon-caret-left"
                  :underline="false"
                ></el-link>
                <div class="tag-style" ref="tagBox">
                  <div
                    class="scrollWrapper"
                    ref="scrollWrapper"
                    id="nav"
                    :style="{ marginLeft: tabScroll }"
                  >
                    <el-tag
                      v-for="item in scope.row.workOrderTag"
                      :color="item.color"
                      style="color: #fff"
                    >
                      {{ item.label }}
                    </el-tag>
                  </div>
                </div>

                <el-link
                  @click="arrowForward"
                  v-if="showButton"
                  style="padding: 2px 3px"
                  plain
                  icon="el-icon-caret-left"
                  :underline="false"
                ></el-link>
              </div> -->
            </div>
          </template>
        </el-table-column>
        <el-table-column
          key="3"
          prop="sheetTitle"
          label="工单主题"
          min-width="320px"
        >
          <template #default="{ row }">
            <el-tooltip :content="row.sheetTitle" placement="top">
              <p class="descStyle">{{ row.sheetTitle }}</p>
            </el-tooltip>
          </template>
        </el-table-column>
        <template
          v-if="
            form.type == 'commonFlow' ||
            form.type == 'core' ||
            form.type == 'ip' ||
            form.type == 'pt' ||
            form.type == 'supervise'
          "
        >
          <el-table-column key="4" prop="emergencyLevel" label="紧急程度">
          </el-table-column>
          <el-table-column
            key="5"
            prop="createType"
            label="工单来源"
            min-width="120px"
          >
          </el-table-column>
          <el-table-column
            key="6"
            prop="senderName"
            label="建单人"
            min-width="120px"
          ></el-table-column>
        </template>

        <template v-if="form.type != 'gt'">
          <el-table-column
            key="7"
            prop="arrivalTime"
            width="200px"
            label="到单时间"
          ></el-table-column>
          <el-table-column
            key="8"
            prop="sheetCreateTime"
            width="200px"
            label="建单时间"
          ></el-table-column>
          <el-table-column
            key="9"
            prop="professionalType"
            width="120px"
            :label="getProfessionalTitle()"
          ></el-table-column>
        </template>
        <template v-if="form.type == 'province' || form.type == 'wireless'">
          <el-table-column
            key="10"
            prop="city"
            min-width="120px"
            label="归属地市"
          ></el-table-column>
        </template>
        <template
          v-if="
            form.type == 'province' ||
            form.type == 'sfty' ||
            form.type == 'wireless'
          "
        >
          <el-table-column
            key="11"
            prop="emergencyLevel"
            min-width="120px"
            label="紧急程度"
          >
          </el-table-column>
          <el-table-column
            key="12"
            prop="createType"
            :label="labelName"
            min-width="120px"
          >
          </el-table-column>
          <el-table-column
            key="13"
            prop="senderName"
            label="建单人"
            width="180px"
          >
          </el-table-column>
        </template>
        <template v-if="form.type != 'sfty'">
          <el-table-column
            key="14"
            prop="agentMan"
            min-width="180px"
            label="主送人"
          >
            <template #default="{ row }">
              <el-tooltip :content="row.agentMan" placement="top">
                <p class="descStyle">{{ row.agentMan }}</p>
              </el-tooltip>
            </template>
          </el-table-column>
        </template>
        <template
          v-if="
            form.type == 'sfty' ||
            form.type == 'province' ||
            form.type == 'wireless'
          "
        >
          <el-table-column
            key="15"
            prop="coverScene"
            min-width="120px"
            label="覆盖场景"
          >
            <template #default="{ row }">
              <el-tooltip :content="row.coverScene" placement="top">
                <p class="descStyle">{{ row.coverScene }}</p>
              </el-tooltip>
            </template>
          </el-table-column>
        </template>
        <el-table-column
          key="16"
          prop="processNode"
          width="100px"
          label="处理环节"
        ></el-table-column>
        <template v-if="form.type == 'gt'">
          <el-table-column
            key="17"
            prop="senderName"
            label="客户名称"
            width="200px"
          ></el-table-column>
          <el-table-column
            key="18"
            prop="provinceName"
            label="发起省"
            width="100px"
          ></el-table-column>
          <el-table-column
            key="19"
            prop="regionName"
            label="地市"
            width="100px"
          ></el-table-column>
          <el-table-column
            key="20"
            prop="sheetStatus"
            label="工单状态"
            width="100px"
          ></el-table-column>
          <el-table-column
            key="21"
            prop="arrivalTime"
            label="到单时间"
            width="200px"
          ></el-table-column>
          <el-table-column
            key="22"
            prop="needCompletionTime"
            label="要求完成时间"
            width="200px"
          ></el-table-column>
        </template>
      </el-table>
    </template>

    <template #pagination>
      <pagination
        ref="pagination"
        :total="form.total"
        :page.sync="form.pageNum"
        :limit.sync="form.pageSize"
        :fromPage="form.type == 'gt' ? '' : '待办'"
        @change="getTableData('senior')"
        v-loading="tableLoading"
        element-loading-spinner=" "
      />
    </template>
    <template #dialog>
      <el-dialog
        width="480px"
        title="转派"
        :visible.sync="transferDialogVisible"
        :close-on-click-modal="false"
        :destroy-on-close="true"
      >
        <turn-to-send
          @closeDialogTurnToSend="dialogTurnToSendClose"
          :common="common"
          :type="type"
          actionName="转派"
        ></turn-to-send>
      </el-dialog>

      <el-dialog
        width="480px"
        title="转办"
        :visible.sync="transferTodoDialogVisible"
        :close-on-click-modal="false"
        :destroy-on-close="true"
      >
        <transfer-todo
          @closeDialogTransfer="dialogTransferClose"
          :common="common"
          :type="type"
          :userAttribution="userAttribution"
          :interfaceProfession="interfaceProfession"
          actionName="批量转办"
        ></transfer-todo>
      </el-dialog>
      <dia-orgs-user-tree
        :title="diaPeople.title"
        :visible.sync="diaPeople.visible"
        :showOrgsTree="diaPeople.showOrgsTree"
        @on-save="onSavePeople"
      />
      <!-- 悬浮 外链接按钮 -->
      <div class="outer-link" v-if="isChatops">
        <span class="icon" @click="handleOuterLink"></span>
        <span class="name" @click="handleOuterLink">chatops</span>
      </div>
      <!-- 弹出框信息 -->
      <el-dialog
        title="消息"
        :visible.sync="chatopsVisible"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        width="1000px"
      >
        <iframe
          :src="rsaEncryptUrl"
          frameborder="0"
          width="100%"
          height="500px"
          scrolling="auto"
          style="border: 1px solid #eee; margin-top: -20px"
        ></iframe>
      </el-dialog>
    </template>
  </head-content-layout>
</template>

<script>
import HeadContentLayout from "./components/HeadContentLayout.vue";
import Pagination from "./components/Pagination.vue";
import DictSelect from "./components/DictSelect.vue";
import FilterTotal from "./components/FilterTotal.vue";
import TurnToSend from "./workOrderWaitDetail/components/TurnToSend.vue";
import TransferTodo from "./workOrderWaitDetail/components/TransferTodo.vue";
import InputTag from "@/plugin/backbone/components/InputTag.vue";
import {
  apiGetTodoList,
  apiqueryToDoCountsByUser,
} from "./api/WorkOrderTodo.js";
import {
  apiDict,
  apiGetOrgInfo,
  apiListTreeTag,
  apiTodoSorting,
  apiTodoSortSave,
} from "./api/CommonApi";
import {
  apiGetFaultArea,
  apiItCloudAccept,
  apiCommCloudAccept,
  apiBatchClearAlarm,
  apiGroupGeneralAccept,
  apiAccept,
  apiWirelessAccept,
} from "./workOrderWaitDetail/api/CommonApi";
import { apiTransferTodoSubmit } from "./api/WorkOrderTodo";
import { apiGetRsaEncrypt } from "../provinceOrder/workOrderWaitDetail/api/CommonApi";
import { apiGetRsaEncryptWifi } from "../wirelessOrder/workOrderWaitDetail/api/CommonApi";
import { mapGetters } from "vuex";
import DiaOrgsUserTree from "./components/DiaOrgsUserTree.vue";
import moment from "moment";

export default {
  name: "WorkOrderTodo",
  components: {
    HeadContentLayout,
    Pagination,
    DictSelect,
    TurnToSend,
    TransferTodo,
    FilterTotal,
    DiaOrgsUserTree,
    InputTag,
  },
  data() {
    return {
      filterTreeId: "85021",
      activeName: "tags",
      tabScroll: "0px", // 移动的距离
      showButton: false, // 标签左右两侧箭头是否显示
      // swiperScrollWidth: 0, // 盒子的宽度
      // swiperScrollContentWidth: 0,
      popoverVisible: false,
      // showPopover: false,
      treeData: [],
      defaultProps: {
        children: "children",
        label: "label",
      },
      scrollLeftDisabled: false,
      scrollRightDisabled: false,
      sortRulesData: [],
      sortWorkOrderPriorityRulesData: [],
      defaultSortIndex: {}, //各专业默认排序
      selectedLabel: "全部",

      // 查询参数
      form: {
        woPriority: [],
        mkAlarmClear: [],
        overtime: [],
        coverScene: "",
        keyword: "",
        sheetId: "",
        title: "",
        source: "",
        createTimeRange: [],
        arriveTimeRange: [],
        finishTimeRange: [],
        createUser: [],
        specialty: "",
        urgencyDegree: "",
        processing: "",
        professionalType: "",
        // bigProfessional: "",
        status: "",
        level: "",
        type: "",
        isAlarmClear: false,
        networkTypeTop: 0,
        pageNum: 1,
        pageSize: 10,
        total: 0,
        fgfw: "",
        sortingRules: "",
      },
      labelName: "",
      dictId: 30007,
      proTypeDictId: 60001,
      toAcceptShow: false,
      provincesBtnShow: false,
      comTransShow: false,
      faultRegionOptions: [], //地区
      provinceRangeTime: [], //省份建单时间
      startTime: moment().subtract(30, "days").format("YYYY-MM-DD 00:00:00"),
      endTime: moment().format("YYYY-MM-DD 23:59:59"),
      showMoreSearch: false,
      provinceShowMoreSearch: false,
      gtShowMoreSearch: false,
      sftyShowMoreSearch: false,
      cloudShowMoreSearch: false,
      filterList: [],
      filterTotals: {
        ggw: 0,
        fiveGc: 0,
        ywpt: 0,
        gt: 0,
        yzy: 0,
        ITy: 0,
        txy: 0,
        province: 0,
        commonFlow: 0,
        core: 0, //新增核心网
        ip: 0, //新增IP专业
        pt: 0, //新增平台
        wireless: 0,
        supervise: 0, //新增集团督办单
      },
      tableLoading: false,
      tabMenuLoading: false,
      tableData: [],
      multipleSelections: [],
      transferDialogVisible: false,
      transferTodoDialogVisible: false, //批量转办
      common: {},
      //转派类型
      type: "batch",
      diaPeople: {
        visible: false,
        title: "",
        saveName: "",
        saveTitleMap: {
          builderDetermine: "建单人选择",
        },
        showOrgsTree: false,
        showOrgsTreeMap: {
          builderDetermine: false,
        },
      },
      createUserIdArr: [],
      acceptFullscreenLoading: false,
      chatopsVisible: false,
      rsaEncryptUrl: "", // chatops iframe 路径
      isChatops: false,
      batchAlarmClearBtnShow: false,
      jttyBtnShow: false, //集团通用
      backboneBtnShow: false, //骨干网
      wirelessBtnShow: false, //无线网
      superviseBtnShow: false, //督办单
      userAttribution: null,
      interfaceProfession: null,
      showDefaultMoreSearch: false,
      showWirelessMoreSearch: false,
      fgfwDictId: "",
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  mounted() {
    if (this.$route.query.globalUniqueID) {
      sessionStorage.setItem(
        "globalUniqueID",
        this.$route.query.globalUniqueID
      );
    }
    this.getSortData(83001);
    this.getWorkOrderPrioritySortData(83002);
    // this.getWorkOrderPrioritySortData(83002);
    this.getDefaultSortIndexData();
    this.getFilterTreeData();
  },
  activated() {
    this.onSimpleSearch();
    this.getOrgInfo();
  },
  methods: {
    //获取各专业默认排序
    getDefaultSortIndexData() {
      // let param = {
      //   dictTypeCode: dictId,
      // };
      apiTodoSorting({})
        .then(res => {
          if (res.status == 0) {
            this.defaultSortIndex = res?.data ?? {};
            if (this.selectedLabel == "全部") {
              this.form.sortingRules = this.defaultSortIndex[
                this.selectedLabel
              ];
            }
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    //设为默认排序规则
    setDefaultSortIndex(sortCode) {
      event.stopPropagation();
      let param = {
        tabName: this.selectedLabel,
        rule: sortCode,
      };
      apiTodoSortSave(param)
        .then(res => {
          if (res.status == 0) {
            this.defaultSortIndex[this.selectedLabel] = sortCode;
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    //获取排序规则枚举
    getSortData(dictId) {
      let param = {
        dictTypeCode: dictId,
      };
      apiDict(param)
        .then(res => {
          if (res.code == 200) {
            this.sortRulesData = res?.data ?? [];
            // this.selectedLabel = val.label;
            if (this.selectedLabel == "全部") {
              // console.log(this.selectedLabel);
              // this.form.sortingRules = this.defaultSortIndex[this.selectedLabel];
              this.onConHeadFilterChange();
            }
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    //获取存在工单优先级的排序规则枚举
    getWorkOrderPrioritySortData(dictId) {
      let param = {
        dictTypeCode: dictId,
      };
      apiDict(param)
        .then(res => {
          if (res.code == 200) {
            this.sortWorkOrderPriorityRulesData = res?.data ?? [];
            // this.selectedLabel = val.label;
            if (this.selectedLabel == "全部") {
              // console.log(this.selectedLabel);
              // this.form.sortingRules = this.defaultSortIndex[this.selectedLabel];
              this.onConHeadFilterChange();
            }
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    //查询标签筛选树
    getFilterTreeData() {
      let param = {
        dictTypeCode: this.filterTreeId,
      };
      apiListTreeTag(param)
        .then(res => {
          if (res.code == 200) {
            this.treeData = res?.data ?? [];
          }
        })
        .catch(error => {
          console.log(error);
        })
        .finally(() => {
          this.$emit("update:dict-list", this.dictData);
        });
    },
    // 标签向左切换
    arrowBack() {
      let tabBoxWidth = this.$refs.tagBox.clientWidth; //盒子宽度
      let offsetLeft = Math.abs(this.$refs.scrollWrapper.offsetLeft); //移动距离
      if (offsetLeft > tabBoxWidth) {
        //移动距离大于父盒子宽度，向前移动一整个父盒子宽度
        this.tabScroll = offsetLeft + tabBoxWidth + "px";
      } else {
        this.tabScroll = "0px"; // 否则移动到开始位置
      }
    },
    // 标签向右切换
    arrowForward() {
      let tabBoxWidth = this.$refs.tagBox.clientWidth; //盒子宽度
      let scrollWidth = this.$refs.scrollWrapper.scrollWidth; //内容宽度
      // 必须要在循环的父级添加 定位样式, offsetLeft 获取元素相对带有定位父元素左边框的偏移
      let offsetLeft = Math.abs(this.$refs.scrollWrapper.offsetLeft); //移动距离
      let diffWidth = scrollWidth - tabBoxWidth; //计算内容宽度与盒子宽度的差值
      if (diffWidth - offsetLeft > tabBoxWidth) {
        //判断差值减去移动距离是否大于盒子宽度 大于则滚动已移动距离+盒子宽度
        this.tabScroll = -(offsetLeft + tabBoxWidth) + "px";
      } else {
        this.tabScroll = -diffWidth + "px"; //小于则移动差值距离
      }
    },
    checkButtonStatus() {
      if (!this.$refs.scrollWrapper) return;
      // 盒子的宽度
      let containerSize = this.$refs.tagBox.clientWidth;
      // 内容的宽度
      let navSize = this.$refs.scrollWrapper.scrollWidth;
      if (containerSize > navSize || containerSize == navSize) {
        this.showButton = false;
      } else {
        this.showButton = true;
      }
    },
    getSheetNoStyle(row) {
      if (row.workOrderTag.length > 0) {
        return "display: inline-block;margin-bottom:25px";
      }
    },
    filterTree() {
      let self = this;
      let checked = self.$refs?.tagTree?.getCheckedNodes(true) ?? [];
      console.log("checked----", checked);

      this.form.woPriority = [];
      this.form.mkAlarmClear = [];
      this.form.overtime = [];
      checked.forEach(item => {
        if (this.form[item.name] == null) {
          this.form[item.name] = [];
        }
        this.form[item.name].push(item.id);
      });
      this.popoverVisible = false;
      this.getTableData("senior");
    },
    closePopOver() {
      // 将选中设置为空
      this.$nextTick(function () {
        this.$refs.tagTree.setCheckedKeys([]);
      });
    },
    scrollLeft(wrapper) {
      this.$refs[wrapper].scrollBy({ left: -100, behavior: "smooth" });
      // this.getScrollLeftDisabled(wrapper);
      // this.getScrollRightDisabled(wrapper);
    },
    scrollRight(wrapper) {
      this.$refs[wrapper].scrollBy({ left: 100, behavior: "smooth" });
      // this.getScrollLeftDisabled(wrapper);
      // this.getScrollRightDisabled(wrapper);
    },
    checkScrollButtons() {
      const scrollWrapper = this.$refs.scrollWrapper;
      this.scrollLeftDisabled = scrollWrapper.scrollLeft <= 0;
      this.scrollRightDisabled =
        scrollWrapper.scrollWidth -
          scrollWrapper.clientWidth -
          scrollWrapper.scrollLeft <=
        0;
    },
    getScrollLeftDisabled(wrapper) {
      console.log("wrapper", wrapper);
      this.$nextTick(() => {
        const scrollWrapper = this.$refs[wrapper];
        console.log("scrollLeft", scrollWrapper.scrollLeft <= 0);
        return scrollWrapper.scrollLeft <= 0;
      });
    },
    getScrollRightDisabled(wrapper) {
      this.$nextTick(() => {
        const scrollWrapper = this.$refs[wrapper];
        console.log(
          "scrollright",
          scrollWrapper.scrollWidth -
            scrollWrapper.clientWidth -
            scrollWrapper.scrollLeft <=
            0
        );
        return (
          scrollWrapper.scrollWidth -
            scrollWrapper.clientWidth -
            scrollWrapper.scrollLeft <=
          0
        );
      });
    },

    //获取tab
    professionalSum(param) {
      if (this.filterList.length == 0) this.tabMenuLoading = true;
      apiqueryToDoCountsByUser(param)
        .then(res => {
          if (res.status == "0") {
            let _this = this;
            let v = res?.data?.rows ?? [];
            let filList = [];
            for (let i = 0; i < v.length; i++) {
              filList.push({
                label: v[i].professionalType,
                value: this.getLabelValue(v[i].professionalType),
                professionalType: v[i].professionalTypeCode,
                networkTypeTop: v[i].networkTypeTop,
              });
            }
            this.filterList = filList;
            this.tabMenuLoading = false;
            for (let i = 0; i < v.length; i++) {
              filList.map(item => {
                if (
                  item.professionalType == v[i].professionalTypeCode &&
                  item.networkTypeTop == v[i].networkTypeTop
                ) {
                  _this.filterTotals[item.value] = v[i].sum;
                }
              });
            }
          } else {
            this.tabMenuLoading = false;
          }
        })
        .catch(err => {
          console.log(err);
          this.this.tabMenuLoading = false;
        });
    },

    getLabelValue(val) {
      switch (val) {
        case "骨干传输":
          return "ggw";
        case "IT云设备":
          return "ITy";
        case "通信云":
          return "txy";
        case "省分":
          return "province";
        case "高铁督办":
          return "gt";
        case "省分通用":
          return "sfty";
        case "集团通用":
          return "commonFlow";
        case "核心网":
          return "core";
        case "IP专业":
          return "ip";
        case "平台":
          return "pt";
        case "无线网":
          return "wireless";
        case "GNOC督办":
          return "supervise";
      }
    },
    setType(newVal) {
      if (newVal == "传输网") {
        this.form.type = "ggw";
      } else if (newVal == "IT云设备") {
        this.form.type = "ITy";
      } else if (newVal == "通信云") {
        this.form.type = "txy";
      } else if (newVal == "省分") {
        this.form.type = "province";
      } else if (newVal == "集团通用") {
        this.form.type = "commonFlow";
      } else if (newVal == "核心网") {
        this.form.type = "core";
      } else if (newVal == "IP专业") {
        this.form.type = "ip";
      } else if (newVal == "平台") {
        this.form.type = "pt";
      } else if (newVal == "无线网") {
        this.form.type = "wireless";
      } else if (newVal == "集团督办") {
        this.form.type = "supervise";
      } else {
        this.form.type = "";
      }
    },
    getType() {
      let type = "";
      if (this.form.specialty == "传输网") {
        type = "3";
      } else if (this.form.specialty == "IT云设备") {
        type = "23";
      } else if (this.form.specialty == "通信云") {
        type = "22";
      } else if (this.form.specialty == "省分") {
        type = "1";
      } else if (this.form.specialty == "高铁督办") {
        type = "";
      } else if (this.form.specialty == "省分通用") {
        type = "";
      } else if (this.form.specialty == "核心网") {
        type = "";
      } else if (this.form.specialty == "IP专业") {
        type = "";
      } else if (this.form.specialty == "平台") {
        type = "";
      } else if (this.form.specialty == "无线网") {
        type = "7";
      } else {
        type = this.form.specialty;
      }
      return type;
    },
    onSwitchMoreSearch() {
      this.showMoreSearch = !this.showMoreSearch;
      this.form.source = "";
      this.form.professionalType = "";
      this.createUserIdArr = [];
      this.form.createUser = [];
    },
    onWirelessSwitchMoreSearch() {
      this.showWirelessMoreSearch = !this.showWirelessMoreSearch;
    },
    onDefaultSwitchMoreSearch() {
      this.showDefaultMoreSearch = !this.showDefaultMoreSearch;
      this.form.source = "";
      this.form.professionalType = "";
      this.createUserIdArr = [];
      this.form.createUser = [];
    },
    onProvinceSwitchMoreSearch() {
      this.provinceShowMoreSearch = !this.provinceShowMoreSearch;
    },
    onGtSwitchMoreSearch() {
      this.gtShowMoreSearch = !this.gtShowMoreSearch;
    },
    onSftySwitchMoreSearch() {
      this.sftyShowMoreSearch = !this.sftyShowMoreSearch;
    },
    onCloudSwitchMoreSearch() {
      this.cloudShowMoreSearch = !this.cloudShowMoreSearch;
    },
    //搜索
    onSimpleSearch() {
      this.form.pageNum = 1;
      this.form.type = this.form.type || "";
      this.getTableData(this.tableDataType || "simple");
    },
    // 获取地区
    getOrgInfo() {
      apiGetOrgInfo()
        .then(res => {
          if (res.status == "0") {
            this.form.areaCode = res?.data?.orgInfo?.areaCode ?? "";
            this.form.category = res?.data?.category ?? "";
            this.getFaultAreaOptions();
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    getFaultAreaOptions() {
      let param = {
        areaCode: this.form.areaCode,
        category: this.form.category,
      };
      apiGetFaultArea(param)
        .then(res => {
          if (res.status == "0") {
            this.faultRegionOptions = res?.data ?? [];
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    //查询
    seniorQuery() {
      this.form.pageNum = 1;
      if (
        this.form.type == "ggw" ||
        this.form.type == "ITy" ||
        this.form.type == "txy"
      ) {
        this.setType(this.form.specialty);
      }
      this.getTableData("senior");
    },
    onResetForm() {
      this.form = {
        ...this.$options.data,
        pageNum: this.form.pageNum,
        total: this.form.total,
      };
    },
    onConHeadFilterChange(
      val = {
        label: "全部",
      }
    ) {
      this.selectedLabel = val.label;
      this.form.sortingRules = this.defaultSortIndex[this.selectedLabel];
      let { value, professionalType: type, networkTypeTop } = val;
      if (type == "3" && networkTypeTop == "0") {
        this.form.specialty = "传输网";
        this.toAcceptShow = false;
        this.provincesBtnShow = false;
        this.comTransShow = false;
        this.isChatops = false;
        this.batchAlarmClearBtnShow = false;
        this.jttyBtnShow = false;
        this.backboneBtnShow = true;
        this.wirelessBtnShow = false;
        this.superviseBtnShow = false;
        this.dictId = 30004;
        this.filterTreeId = "85011";
      } else if (type == "23" && networkTypeTop == "0") {
        this.form.specialty = "IT云设备";
        this.toAcceptShow = true;
        this.provincesBtnShow = true;
        this.comTransShow = false;
        this.isChatops = false;
        this.batchAlarmClearBtnShow = false;
        this.jttyBtnShow = false;
        this.backboneBtnShow = false;
        this.wirelessBtnShow = false;
        this.superviseBtnShow = false;
        this.dictId = 30005;
        this.filterTreeId = "85011";
      } else if (type == "22" && networkTypeTop == "0") {
        this.form.specialty = "通信云";
        this.toAcceptShow = true;
        this.provincesBtnShow = true;
        this.isChatops = false;
        this.comTransShow = false;
        this.batchAlarmClearBtnShow = false;
        this.jttyBtnShow = false;
        this.backboneBtnShow = false;
        this.wirelessBtnShow = false;
        this.superviseBtnShow = false;
        this.dictId = 30008;
        this.filterTreeId = "85011";
      } else if (networkTypeTop == "6") {
        this.form.specialty = "无线网";
        this.toAcceptShow = false;
        this.provincesBtnShow = false;
        this.comTransShow = false;
        this.isChatops = true;
        this.batchAlarmClearBtnShow = true;
        this.jttyBtnShow = false;
        this.backboneBtnShow = false;
        this.wirelessBtnShow = true;
        this.superviseBtnShow = false;
        this.dictId = 70003;
        this.labelName = "工单来源";
        this.filterTreeId = "85003";
      } else if (networkTypeTop == "4") {
        this.form.specialty = "高铁督办";
        this.toAcceptShow = false;
        this.provincesBtnShow = false;
        this.comTransShow = false;
        this.batchAlarmClearBtnShow = false;
        this.isChatops = false;
        this.jttyBtnShow = false;
        this.backboneBtnShow = false;
        this.wirelessBtnShow = false;
        this.superviseBtnShow = false;
        this.dictId = 30018;
        this.filterTreeId = "85001";
      } else if (networkTypeTop == "2") {
        this.form.specialty = "省分通用";
        this.toAcceptShow = false;
        this.provincesBtnShow = false;
        this.comTransShow = true;
        this.batchAlarmClearBtnShow = false;
        this.isChatops = true;
        this.jttyBtnShow = false;
        this.backboneBtnShow = false;
        this.wirelessBtnShow = false;
        this.superviseBtnShow = false;
        this.dictId = 30022;
        this.labelName = "工单来源";
        this.filterTreeId = "85002";
      } else if (networkTypeTop == "1") {
        this.filterTreeId = "85001";
        this.toAcceptShow = false;
        this.provincesBtnShow = false;
        this.comTransShow = false;
        this.isChatops = true;
        this.batchAlarmClearBtnShow = false;
        this.jttyBtnShow = false;
        this.backboneBtnShow = false;
        this.wirelessBtnShow = false;
        this.superviseBtnShow = false;
        this.dictId = 30006;
        this.labelName = "工单来源";
      } else if (networkTypeTop == "5") {
        this.form.specialty = "集团通用";
        this.jttyBtnShow = true;
        this.toAcceptShow = false;
        this.provincesBtnShow = false;
        this.isChatops = false;
        this.batchAlarmClearBtnShow = false;
        this.backboneBtnShow = false;
        this.wirelessBtnShow = false;
        this.superviseBtnShow = false;
        this.dictId = 30004;
        this.proTypeDictId = 60012;
        this.form.professionalType = "";
        this.filterTreeId = "85011";
      } else if (networkTypeTop == "7") {
        this.form.specialty = "核心网";
        this.jttyBtnShow = true;
        this.toAcceptShow = false;
        this.provincesBtnShow = false;
        this.isChatops = false;
        this.batchAlarmClearBtnShow = false;
        this.backboneBtnShow = false;
        this.wirelessBtnShow = false;
        this.superviseBtnShow = false;
        this.dictId = 30004;
        this.proTypeDictId = 60009;
        this.form.professionalType = "";
        this.filterTreeId = "85011";
      } else if (networkTypeTop == "8") {
        this.form.specialty = "IP专业";
        this.jttyBtnShow = true;
        this.toAcceptShow = false;
        this.provincesBtnShow = false;
        this.isChatops = false;
        this.batchAlarmClearBtnShow = false;
        this.backboneBtnShow = false;
        this.wirelessBtnShow = false;
        this.superviseBtnShow = false;
        this.dictId = 30004;
        this.proTypeDictId = 60010;
        this.form.professionalType = "";
        this.filterTreeId = "85011";
      } else if (networkTypeTop == "9") {
        this.form.specialty = "平台";
        this.jttyBtnShow = true;
        this.toAcceptShow = false;
        this.provincesBtnShow = false;
        this.isChatops = false;
        this.batchAlarmClearBtnShow = false;
        this.backboneBtnShow = false;
        this.wirelessBtnShow = false;
        this.superviseBtnShow = false;
        this.dictId = 30004;
        this.proTypeDictId = 60011;
        this.form.professionalType = "";
        this.filterTreeId = "85011";
      } else if (networkTypeTop == "10") {
        this.form.specialty = "集团督办";
        this.jttyBtnShow = false;
        this.toAcceptShow = false;
        this.provincesBtnShow = false;
        this.isChatops = false;
        this.batchAlarmClearBtnShow = false;
        this.backboneBtnShow = false;
        this.wirelessBtnShow = false;
        this.superviseBtnShow = true;
        this.dictId = 811040;
        this.filterTreeId = "85011";
      } else {
        this.form.specialty = "";
        this.toAcceptShow = false;
        this.provincesBtnShow = false;
        this.comTransShow = false;
        this.isChatops = false;
        this.batchAlarmClearBtnShow = false;
        this.jttyBtnShow = false;
        this.backboneBtnShow = false;
        this.wirelessBtnShow = false;
        this.superviseBtnShow = false;
        this.dictId = 30007;
        this.filterTreeId = "85021";
      }
      this.form.createTimeRange = [];
      this.form.type = value; //控制tab
      this.form.pageNum = 1;
      this.form.woPriority = [];
      this.form.mkAlarmClear = [];
      this.form.overtime = [];
      this.getFilterTreeData();
      this.getTableData("senior");
    },
    //全选
    onSelectAll() {
      if (this.$refs.table.selection.length == 0) {
        let userArea = JSON.parse(this.userInfo.attr2);
        let category = userArea.category;
        let ableSelect = [];
        if (category != "CITY") {
          this.tableData.forEach(row => {
            if (row.processNode == "受理") {
              ableSelect.push(row);
            }
          });
        }
        ableSelect.forEach(row => {
          this.$refs.table?.toggleRowSelection(row, true);
        });
      } else {
        this.$refs.table.clearSelection();
      }
    },
    //批量转派
    onBatchTransfer() {
      if (this.multipleSelections.length > 0) {
        const flag = this.multipleSelections.every(
          item =>
            item.professionalType ===
              this.multipleSelections[0].professionalType &&
            item.processNode === "处理"
        );
        if (flag) {
          this.transferDialogVisible = true;
          this.common = {
            multipleSelections: this.multipleSelections,
          };
        } else {
          this.$message.warning(
            "只能对处于“处理”环节且归属同一工单类别的工单进行“批量转派”"
          );
        }
      } else {
        this.$message.warning("请先选择工单");
      }
    },
    //转派关闭
    dialogTurnToSendClose(val) {
      if (val == "1") {
        this.getTableData("senior");
        this.transferDialogVisible = false;
      } else {
        this.transferDialogVisible = false;
      }
    },
    //批量受理
    onBatchAccept() {
      if (this.multipleSelections.length > 0) {
        console.log(this.multipleSelections);
        const notstandard = this.multipleSelections.some(
          item =>
            item.processNode != "受理" ||
            (item.professionalType != "IT云设备" &&
              item.professionalType != "通信云")
        );
        if (notstandard) {
          this.$message.warning(
            "只能对处于“受理”环节的“IT云/通信云”工单进行“批量受理”"
          );
        } else {
          const woIds = this.multipleSelections
            .map(item => {
              return item.woId;
            })
            .join(",");
          const workItemIds = this.multipleSelections
            .map(item => {
              return item.workItemId;
            })
            .join(",");
          let param = {
            woId: woIds,
            workItemId: workItemIds,
            actionName: "受理",
          };
          this.acceptFullscreenLoading = true;
          if (this.form.specialty == "IT云设备") {
            apiItCloudAccept(param)
              .then(res => {
                if (res.status == "0") {
                  this.$message.success("批量受理成功");
                  this.getTableData("senior");
                } else {
                  this.$message.error("批量受理失败");
                }
                this.acceptFullscreenLoading = false;
              })
              .catch(error => {
                console.log(error);
                this.$message.error("批量受理失败");
                this.acceptFullscreenLoading = false;
              });
          } else if (this.form.specialty == "通信云") {
            apiCommCloudAccept(param)
              .then(res => {
                if (res.status == "0") {
                  this.$message.success("批量受理成功");
                  this.getTableData("senior");
                } else {
                  this.$message.error("批量受理失败");
                }
                this.acceptFullscreenLoading = false;
              })
              .catch(error => {
                console.log(error);
                this.$message.error("批量受理失败");
                this.acceptFullscreenLoading = false;
              });
          } else {
            //集团通用批量处理
            apiGroupGeneralAccept(param)
              .then(res => {
                if (res.status == "0") {
                  this.$message.success("批量受理成功");
                  this.getTableData("senior");
                } else {
                  this.$message.error("批量受理失败");
                }
                this.acceptFullscreenLoading = false;
              })
              .catch(error => {
                console.log(error);
                this.$message.error("批量受理失败");
                this.acceptFullscreenLoading = false;
              });
          }
        }
      } else {
        this.$message.warning("请先选择工单");
      }
    },
    //集团通用全选
    onSelectAllJtty() {
      if (this.$refs.table.selection.length == 0) {
        let jttySelect = [];
        this.tableData.forEach(row => {
          jttySelect.push(row);
        });
        jttySelect.forEach(row => {
          this.$refs.table?.toggleRowSelection(row, true);
        });
      } else {
        this.$refs.table.clearSelection();
      }
    },
    //集团通用批量受理
    onBatchAcceptByJtty() {
      if (this.multipleSelections.length > 0) {
        const isCallInterface = this.multipleSelections.some(
          item => item.processNode == "受理"
        );
        if (isCallInterface) {
          const woIds = this.multipleSelections
            .map(item => {
              return item.woId;
            })
            .join(",");
          const workItemIds = this.multipleSelections
            .map(item => {
              return item.workItemId;
            })
            .join(",");
          let param = {
            woId: woIds,
            workItemId: workItemIds,
            actionName: "受理",
          };
          this.acceptFullscreenLoading = true;
          apiGroupGeneralAccept(param)
            .then(res => {
              if (res.status == "0") {
                this.$message.success("批量受理成功");
                this.getTableData("senior");
              } else if (res.status == "400") {
                this.getTableData("senior");
                let resultData = res?.data ?? [];
                this.messageBybatchAccpetError(resultData, 1);
              } else if (res.status == "401") {
                this.getTableData("senior");
                let resultData = res?.data ?? [];
                this.messageBybatchAccpetError(resultData, 1, 0);
              } else {
                this.$message.error(res.msg);
              }
            })
            .catch(error => {
              console.log(error);
              if (error.status == "400") {
                this.getTableData("senior");
                this.messageBybatchAccpetError(error.data, 1);
              } else if (error.status == "401") {
                this.getTableData("senior");
                this.messageBybatchAccpetError(error.data, 1, 0);
              } else {
                this.$message.error(error.msg);
              }
            })
            .finally(() => {
              this.acceptFullscreenLoading = false;
            });
        } else {
          let errorWorkOrderData = this.multipleSelections;
          this.messageBybatchAccpetError(errorWorkOrderData, 0);
        }
      } else {
        this.$message.warning("请先选择工单");
      }
    },
    //骨干网批量受理
    onBatchAcceptByBackbone() {
      if (this.multipleSelections.length > 0) {
        const isCallInterface = this.multipleSelections.some(
          item => item.processNode == "受理"
        );
        console.log(isCallInterface);
        if (isCallInterface) {
          const woIds = this.multipleSelections
            .map(item => {
              return item.woId;
            })
            .join(",");
          const workItemIds = this.multipleSelections
            .map(item => {
              return item.workItemId;
            })
            .join(",");
          let param = {
            woId: woIds,
            workItemId: workItemIds,
            actionName: "受理",
          };
          this.acceptFullscreenLoading = true;
          apiAccept(param)
            .then(res => {
              if (res.status == "0") {
                this.$message.success("批量受理成功");
                this.getTableData("senior");
              } else if (res.status == "400") {
                this.getTableData("senior");
                let resultData = res?.data ?? [];
                this.messageBybatchAccpetError(resultData, 1);
              } else if (res.status == "401") {
                this.getTableData("senior");
                let resultData = res?.data ?? [];
                this.messageBybatchAccpetError(resultData, 1, 0);
              } else {
                this.$message.error(res.msg);
              }
            })
            .catch(error => {
              console.log(error);
              if (error.status == "400") {
                this.getTableData("senior");
                this.messageBybatchAccpetError(error.data, 1);
              } else if (error.status == "401") {
                this.getTableData("senior");
                this.messageBybatchAccpetError(error.data, 1, 0);
              } else {
                this.$message.error(error.msg);
              }
            })
            .finally(() => {
              this.acceptFullscreenLoading = false;
            });
        } else {
          let errorWorkOrderData = this.multipleSelections;
          this.messageBybatchAccpetError(errorWorkOrderData, 0);
        }
      } else {
        this.$message.warning("请先选择工单");
      }
    },
    //无线网批量受理
    onBatchAcceptByWireless() {
      if (this.multipleSelections.length > 0) {
        const isCallInterface = this.multipleSelections.some(
          item => item.processNode == "受理"
        );
        console.log(isCallInterface);
        if (isCallInterface) {
          const woIds = this.multipleSelections
            .map(item => {
              return item.woId;
            })
            .join(",");
          const workItemIds = this.multipleSelections
            .map(item => {
              return item.workItemId;
            })
            .join(",");
          const processInstIds = this.multipleSelections
            .map(item => {
              return item.processInstId;
            })
            .join(",");
          let param = {
            woId: woIds,
            workItemId: workItemIds,
            processInstId: processInstIds,
            actionName: "受理",
          };
          this.acceptFullscreenLoading = true;
          apiWirelessAccept(param)
            .then(res => {
              if (res.status == "0") {
                this.$message.success("批量受理成功");
                this.getTableData("senior");
              } else if (res.status == "400") {
                this.getTableData("senior");
                let resultData = res?.data ?? [];
                this.messageBybatchAccpetError(resultData, 1);
              } else if (res.status == "401") {
                this.getTableData("senior");
                let resultData = res?.data ?? [];
                this.messageBybatchAccpetError(resultData, 1, 0);
              } else {
                this.$message.error(res.msg);
              }
            })
            .catch(error => {
              console.log(error);
              if (error.status == "400") {
                this.getTableData("senior");
                this.messageBybatchAccpetError(error.data, 1);
              } else if (error.status == "401") {
                this.getTableData("senior");
                this.messageBybatchAccpetError(error.data, 1, 0);
              } else {
                this.$message.error(error.msg);
              }
            })
            .finally(() => {
              this.acceptFullscreenLoading = false;
            });
        } else {
          let errorWorkOrderData = this.multipleSelections;
          this.messageBybatchAccpetError(errorWorkOrderData, 0);
        }
      } else {
        this.$message.warning("请先选择工单");
      }
    },
    //集团通用批量转办
    onBatchZhuanbanByJtty() {
      if (this.multipleSelections.length > 0) {
        const flag = this.multipleSelections.every(
          item => item.processNode !== "受理"
        );
        if (flag) {
          this.transferTodoDialogVisible = true;
          //如果用户是集团或大区  userAttribution 是Group, 其它的用户则用 cpZhuanban
          let userArea = JSON.parse(this.userInfo.attr2);
          if (userArea.category == "UNI" || userArea.dqInfo != null) {
            this.userAttribution = "Group";
          } else {
            this.userAttribution = "cpZhuanban";
          }
          this.interfaceProfession = "JTTY";
          this.common = {
            multipleSelections: this.multipleSelections,
          };
        } else {
          this.$message.warning(
            "处在“受理”环节的工单无法批量转办，请重新选择。"
          );
        }
      } else {
        this.$message.warning("请先选择工单");
      }
    },
    //省分通用全选
    onSelectAllZhuanban() {
      if (this.$refs.table.selection.length == 0) {
        let ableSelect = [];
        this.tableData.forEach(row => {
          ableSelect.push(row);
        });
        ableSelect.forEach(row => {
          this.$refs.table?.toggleRowSelection(row, true);
        });
      } else {
        this.$refs.table.clearSelection();
      }
    },

    //省分通用批量转办
    onBatchZhuanban() {
      if (this.multipleSelections.length > 0) {
        const flag = this.multipleSelections.every(
          item => item.processNode !== "受理"
        );
        if (flag) {
          this.transferTodoDialogVisible = true;
          this.userAttribution = "cpZhuanban";
          this.common = {
            multipleSelections: this.multipleSelections,
          };
          this.interfaceProfession = "commonProvince";
        } else {
          this.$message.warning(
            "处在“受理”环节的工单无法批量转办，请重新选择。"
          );
        }
      } else {
        this.$message.warning("请先选择工单");
      }
    },
    //省分通用批量受理
    onBatchAcceptSFTY() {
      debugger;
      if (this.multipleSelections.length > 0) {
        const notstandard = this.multipleSelections.some(
          item => item.processNode == "受理"
        );
        console.log(notstandard);

        if (notstandard) {
          var numbers = [];
          this.multipleSelections.forEach(item => {
            if (item.processNode != "受理") {
              numbers.push(item);
            }
          });

          var numberStr = "";
          let length = numbers.length > 5 ? 5 : numbers.length;

          for (let i = 0; i < length; i++) {
            var item = numbers[i];
            if (i == 0) {
              numberStr = item.sheetNo;
            } else {
              numberStr = numberStr + ", <br/> " + item.sheetNo;
            }
          }

          if (numbers.length > 5) {
            numberStr = numberStr + "... <br/> " + "受理失败。";
          } else {
            numberStr = numberStr + " <br/> " + "受理失败。";
          }

          var msg =
            "只能对待受理的工单进行“批量受理”。" + " <br/> " + numberStr;
          this.$message({
            dangerouslyUseHTMLString: true,
            message: msg,
            type: "warning",
          });
        }

        var shouli = [];
        this.multipleSelections.forEach(item => {
          if (item.processNode == "受理") {
            shouli.push(item);
          }
        });

        if (shouli.length == 0) {
          this.$message.warning("不在受理环节不支持批量受理");
          return false;
        }

        const woIds = shouli
          .map(item => {
            return item.woId;
          })
          .join(",");

        var sheetInfos = [];
        shouli.forEach(item => {
          let info = {
            woId: item.woId,
            workItemId: item.workItemId,
            actionName: "受理",
            processInstId: item.processInstId,
          };
          sheetInfos.push(info);
        });

        let param = {
          woId: woIds,
          sheetInfo: sheetInfos,
          actionName: "批量受理",
        };
        this.acceptFullscreenLoading = true;
        //调用接口
        apiTransferTodoSubmit(param)
          .then(res => {
            if (res.status == "200") {
              this.$message.success("批量受理成功");
              this.getTableData("senior");
            } else if (res.status == "400") {
              this.getTableData("senior");
              var numbers = res.data;
              var numberStr = "";
              let length = numbers.length > 5 ? 5 : numbers.length;

              for (let i = 0; i < length; i++) {
                var item = numbers[i];
                if (i == 0) {
                  numberStr = item;
                } else {
                  numberStr = numberStr + ", <br/> " + item;
                }
              }

              if (numbers.length > 5) {
                numberStr = numberStr + "... <br/> " + "受理失败。";
              } else {
                numberStr = numberStr + " <br/> " + "受理失败。";
              }

              this.$message({
                dangerouslyUseHTMLString: true,
                message: numberStr,
                type: "warning",
              });
            } else {
              this.$message.error(res.msg);
            }
            this.acceptFullscreenLoading = false;
          })
          .catch(error => {
            console.log(error);
            if (error.status == "400") {
              this.getTableData("senior");
              var numbers = error.data;
              var numberStr = "";
              let length = numbers.length > 5 ? 5 : numbers.length;

              for (let i = 0; i < length; i++) {
                var item = numbers[i];
                if (i == 0) {
                  numberStr = item;
                } else {
                  numberStr = numberStr + ", <br/> " + item;
                }
              }

              if (numbers.length > 5) {
                numberStr = numberStr + "... <br/> " + "受理失败。";
              } else {
                numberStr = numberStr + " <br/> " + "受理失败。";
              }

              this.$message({
                dangerouslyUseHTMLString: true,
                message: numberStr,
                type: "warning",
              });
            } else {
              this.$message.error(error.msg);
            }
            this.acceptFullscreenLoading = false;
          });
        // }
      } else {
        this.$message.warning("请先选择工单");
      }
    },
    onBatchAlarmClear() {
      if (this.multipleSelections.length > 0) {
        const woIds = this.multipleSelections.map(item => {
          return item.woId;
        });
        console.log(woIds);

        apiBatchClearAlarm({ woIds: woIds })
          .then(res => {
            if (res.status == "0") {
              this.$message.success("告警清除成功");
            }
          })
          .catch(error => {
            console.log(error);
            this.$message.error("告警清除失败");
          });
      } else {
        this.$message.warning("请先选择工单");
      }
    },
    //转办关闭
    dialogTransferClose(val) {
      if (val == "1") {
        this.getTableData("senior");
        this.$refs.table.clearSelection();
        this.transferTodoDialogVisible = false;
      } else {
        this.transferTodoDialogVisible = false;
      }
    },

    getTableData(type) {
      this.tableDataType = type;
      this.tableLoading = true;
      let seniorParam = {
        sheetNo: this.form.sheetId,
        sheetTitle: this.form.title,
        // createType: this.form.source, //工单来源
        //senders: this.createUserIdArr,
        professionalType: this.getType(),
        emergencyLevel: this.form.urgencyDegree,
        startTime: this?.form?.createTimeRange?.[0],
        endTime: this?.form?.createTimeRange?.[1],
        processNodeId: this.form.processing,
        sheetStatus: this.form.status,
        sheetLevel: this.form.level,
        sheetTitleOrNoKeyword: this.form.keyword,
        arrivalStartTime: this.form.arriveTimeRange?.[0] ?? this.startTime,
        arrivalEndTime: this.form.arriveTimeRange?.[1] ?? this.endTime,
      };
      let simpleParam = {
        sheetTitleOrNoKeyword: this.form.keyword,
        // startTime: this.startTime,
        // endTime: this.endTime,
        arrivalStartTime: this.startTime,
        arrivalEndTime: this.endTime,
      };

      if (this.form.type === "province") {
        this.$set(
          seniorParam,
          "startTime",
          this?.form?.createTimeRange?.[0] ?? ""
        );
        this.$set(
          seniorParam,
          "endTime",
          this?.form?.createTimeRange?.[1] ?? ""
        );
        this.$set(
          seniorParam,
          "arrivalStartTime",
          this?.form?.arriveTimeRange?.[0] ?? this.startTime
        );
        this.$set(
          seniorParam,
          "arrivalEndTime",
          this?.form?.arriveTimeRange?.[1] ?? this.endTime
        );

        this.$set(seniorParam, "sortingRules", this.form.sortingRules);
        this.$set(seniorParam, "coverScene", this.form.coverScene);
        this.$set(seniorParam, "networkTypeTop", 1);
        delete seniorParam.professionalType; //20230218 by jk
      } else if (this.form.type === "gt") {
        delete seniorParam.startTime;
        delete seniorParam.endTime;
        delete seniorParam.emergencyLevel;
        this.$set(seniorParam, "sortingRules", this.form.sortingRules);
        this.$set(seniorParam, "networkTypeTop", 4);
        this.$set(
          seniorParam,
          "arrivalStartTime",
          this?.form?.arriveTimeRange?.[0] ?? this.startTime
        );
        this.$set(
          seniorParam,
          "arrivalEndTime",
          this?.form?.arriveTimeRange?.[1] ?? this.endTime
        );
        this.$set(
          seniorParam,
          "needComplStartTime",
          this?.form?.finishTimeRange?.[0] ?? ""
        );
        this.$set(
          seniorParam,
          "needComplEndTime",
          this?.form?.finishTimeRange?.[1] ?? ""
        );
      } else if (this.form.type === "sfty") {
        this.$set(
          seniorParam,
          "startTime",
          this?.form?.createTimeRange?.[0] ?? ""
        );
        this.$set(
          seniorParam,
          "endTime",
          this?.form?.createTimeRange?.[1] ?? ""
        );
        this.$set(
          seniorParam,
          "arrivalStartTime",
          this?.form?.arriveTimeRange?.[0] ?? this.startTime
        );
        this.$set(
          seniorParam,
          "arrivalEndTime",
          this?.form?.arriveTimeRange?.[1] ?? this.endTime
        );

        this.$set(seniorParam, "sortingRules", this.form.sortingRules);
        this.$set(seniorParam, "networkTypeTop", 2);
        this.$set(seniorParam, "createType", this.form.source); //工单来源
        this.$set(seniorParam, "professionalType", this.form.professionalType); //故障专业
        this.$set(seniorParam, "isAlarmClear", this.form.isAlarmClear ? 1 : 0); //待返单工单
        if (this.form.professionalType == 7) {
          this.$set(seniorParam, "coverScene", this.form.coverScene);
        }
      } else if (this.form.type == "ggw") {
        this.$set(seniorParam, "networkTypeTop", 0);
        this.$set(seniorParam, "sortingRules", this.form.sortingRules);
      } else if (this.form.type == "ITy" || this.form.type == "txy") {
        this.$set(seniorParam, "networkTypeTop", 0);
        this.$set(seniorParam, "isAlarmClear", this.form.isAlarmClear ? 1 : 0); //待返单工单
        this.$set(seniorParam, "sortingRules", this.form.sortingRules);
      } else if (this.form.type == "commonFlow") {
        this.$set(seniorParam, "networkTypeTop", 5);
        this.$set(seniorParam, "createType", this.form.source);
        this.$set(seniorParam, "professionalType", this.form.professionalType); //故障专业
        this.$set(
          seniorParam,
          "senders",
          this.form.createUser.map(item => item.value)
        );
        this.$set(seniorParam, "isAlarmClear", this.form.isAlarmClear ? 1 : 0); //待返单工单
        this.$set(seniorParam, "sortingRules", this.form.sortingRules);
      } else if (this.form.type == "core") {
        this.$set(seniorParam, "networkTypeTop", 7);
        this.$set(seniorParam, "createType", this.form.source);
        this.$set(seniorParam, "professionalType", this.form.professionalType); //故障专业
        this.$set(seniorParam, "sortingRules", this.form.sortingRules);
        this.$set(
          seniorParam,
          "senders",
          this.form.createUser.map(item => item.value)
        );
        this.$set(seniorParam, "isAlarmClear", this.form.isAlarmClear ? 1 : 0); //待返单工单
      } else if (this.form.type == "ip") {
        this.$set(seniorParam, "networkTypeTop", 8);
        this.$set(seniorParam, "createType", this.form.source);
        this.$set(seniorParam, "professionalType", this.form.professionalType); //故障专业
        this.$set(seniorParam, "sortingRules", this.form.sortingRules);
        this.$set(
          seniorParam,
          "senders",
          this.form.createUser.map(item => item.value)
        );
        this.$set(seniorParam, "isAlarmClear", this.form.isAlarmClear ? 1 : 0); //待返单工单
      } else if (this.form.type == "pt") {
        this.$set(seniorParam, "networkTypeTop", 9);
        this.$set(seniorParam, "createType", this.form.source);
        this.$set(seniorParam, "professionalType", this.form.professionalType); //故障专业
        this.$set(seniorParam, "sortingRules", this.form.sortingRules);
        this.$set(
          seniorParam,
          "senders",
          this.form.createUser.map(item => item.value)
        );
        this.$set(seniorParam, "isAlarmClear", this.form.isAlarmClear ? 1 : 0); //待返单工单
      } else if (this.form.type == "wireless") {
        this.$set(seniorParam, "networkTypeTop", 6);
        this.$set(seniorParam, "professionalType", 7);
        this.$set(seniorParam, "coverScene", this.form.coverScene);
        this.$set(seniorParam, "sortingRules", this.form.sortingRules);
      } else if (this.form.type == "supervise") {
        this.$set(seniorParam, "networkTypeTop", 10);
        this.$set(seniorParam, "professionalType", this.form.professionalType);
        this.$set(seniorParam, "sortingRules", this.form.sortingRules);
        // this.$set(seniorParam, "bigProfessional", this.form.bigProfessional);
      } else {
        this.$set(seniorParam, "sortingRules", this.form.sortingRules);
      }
      this.$set(seniorParam, "woPriority", this.form.woPriority?.join(","));
      this.$set(seniorParam, "mkAlarmClear", this.form.mkAlarmClear?.join(","));
      this.$set(seniorParam, "overtime", this.form.overtime?.join(","));
      let param = {
        pageIndex: this.form.pageNum,
        pageSize: this.form.pageSize,
        param1:
          type == "senior"
            ? JSON.stringify(seniorParam)
            : JSON.stringify(simpleParam),
      };
      apiGetTodoList(param)
        .then(res => {
          if (res.status == "0") {
            this.tableData = res?.data?.rows ?? [];
            this.form.total = res?.data?.totalElements ?? 0;
            this.filterTotals[this.form.type] = res?.data?.totalElements ?? 0;
            this.professionalSum(param);
          }
          this.tableLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.tableLoading = false;
        });
    },

    onSelectionChange(selection) {
      this.multipleSelections = selection;
    },
    indexMethod(index) {
      const currentPage = this.form.pageNum;
      const pageSize = this.form.pageSize;
      return (currentPage - 1) * pageSize + (index + 1);
    },
    //跳转详情
    toDoDetail(data) {
      if (data.networkTypeTop == 1) {
        this.JumpDetails_Province(data);
      } else if (data.networkTypeTop == 4) {
        this.JumpDetails_GT(data);
      } else if (data.networkTypeTop == 2) {
        this.JumpDetails_SFTY(data);
      } else if (data.networkTypeTop == 0) {
        switch (data.professionalType) {
          case "传输网":
            this.JumpDetails_GGW(data);
            break;
          case "IT云设备":
            this.JumpDetails_ITY(data);
            break;
          case "通信云":
            this.JumpDetails_TXY(data);
            break;
        }
      } else if (data.networkTypeTop == 5) {
        let title = "集团通用";
        this.JumpDetails_Common(data, title);
      } else if (data.networkTypeTop == 6) {
        this.JumpDetails_Wireless(data);
      } else if (data.networkTypeTop == 7) {
        let title = "核心网";
        this.JumpDetails_Common(data, title);
      } else if (data.networkTypeTop == 8) {
        let title = "IP专业";
        //this.JumpDetails_Common(data, title);
        this.JumpDetails_IpProfessionnal(data, title);
      } else if (data.networkTypeTop == 9) {
        let title = "平台";
        this.JumpDetails_Common(data, title);
      } else if (data.networkTypeTop == 10) {
        let title = "GNOC督办单";
        this.JumpDetails_Supervise(data, title);
      }
    },

    JumpDetails_TXY(data) {
      this.$router.push({
        name: "commCloud_woDetail",
        query: {
          workItemId: data.workItemId,
          processInstId: data.processInstId,
          woId: data.woId,
          processDefId: data.processDefId,
          fromPage: "待办工单",
          networkType: data.networkType,
        },
      });
    },
    JumpDetails_ITY(data) {
      this.$router.push({
        name: "itCloud_woDetail",
        query: {
          workItemId: data.workItemId,
          processInstId: data.processInstId,
          woId: data.woId,
          processDefId: data.processDefId,
          fromPage: "待办工单",
          networkType: data.networkType,
        },
      });
    },
    JumpDetails_Wireless(data) {
      this.$router.push({
        name: "wireless_orderDetail",
        query: {
          workItemId: data.workItemId,
          processInstId: data.processInstId,
          woId: data.woId,
          processDefId: data.processDefId,
          fromPage: "待办工单",
          processNode: data.processNode,
          networkType: data.networkType,
          professionalType: data.professionalType,
        },
      });
    },
    JumpDetails_GGW(data) {
      this.$router.push({
        name: "backbone_toDoRepairOrderDetail",
        query: {
          workItemId: data.workItemId,
          processInstId: data.processInstId,
          woId: data.woId,
          processDefId: data.processDefId,
          fromPage: "待办工单",
          networkType: data.networkType,
          networkTypeTop: data.networkTypeTop,
          professionalType: data.professionalType,
        },
      });
    },
    JumpDetails_Province(data) {
      this.$router.push({
        name: "provinceOrder_detail",
        query: {
          workItemId: data.workItemId,
          processInstId: data.processInstId,
          woId: data.woId,
          processDefId: data.processDefId,
          processNode: data.processNode,
          fromPage: "待办工单",
          networkType: data.networkType,
          professionalType: data.professionalType,
        },
      });
    },
    JumpDetails_GT(data) {
      this.$router.push({
        name: "gtOrder_detail",
        query: {
          workItemId: data.workItemId,
          processInstId: data.processInstId,
          woId: data.woId,
          processDefId: data.processDefId,
          processNode: data.processNode,
          fromPage: "待办工单",
          networkType: data.networkType,
          professionalType: data.professionalType,
        },
      });
    },

    JumpDetails_Common(data, title) {
      this.$router.push({
        name: "common_orderDetail",
        query: {
          workItemId: data.workItemId,
          processInstId: data.processInstId,
          woId: data.woId,
          processDefId: data.processDefId,
          processNode: data.processNode,
          fromPage: "待办工单",
          frameTabTitle: title + "故障工单",
          networkType: data.networkType,
          professionalType: data.professionalType,
        },
      });
    },
    JumpDetails_Supervise(data, title) {
      this.$router.push({
        name: "supervise_orderDetail",
        query: {
          workItemId: data.workItemId,
          processInstId: data.processInstId,
          woId: data.woId,
          processDefId: data.processDefId,
          processNode: data.processNode,
          fromPage: "待办工单",
          frameTabTitle: title,
          networkType: data.networkType,
          professionalType: data.professionalType,
        },
      });
    },

    JumpDetails_IpProfessionnal(data, title) {
      this.$router.push({
        name: "ipProfessionOrder_Detail",
        query: {
          workItemId: data.workItemId,
          processInstId: data.processInstId,
          woId: data.woId,
          processDefId: data.processDefId,
          processNode: data.processNode,
          fromPage: "待办工单",
          frameTabTitle: title + "故障工单",
          networkType: data.networkType,
          professionalType: data.professionalType,
        },
      });
    },
    JumpDetails_SFTY(data) {
      this.$router.push({
        name: "commonProvinceOrder_detail",
        query: {
          workItemId: data.workItemId,
          processInstId: data.processInstId,
          woId: data.woId,
          processDefId: data.processDefId,
          processNode: data.processNode,
          fromPage: "待办工单",
          networkType: data.networkType,
          professionalType: data.professionalType,
        },
      });
    },

    //列表项是否可选
    selectDisableRoom(row, index) {
      if (
        this.form.type == "sfty" ||
        this.form.type == "wireless" ||
        this.form.type == "commonFlow" ||
        this.form.type == "core" ||
        this.form.type == "ip" ||
        this.form.type == "pt"
      ) {
        return true;
      } else {
        let userArea = JSON.parse(this.userInfo.attr2);
        let category = userArea.category;
        if (
          (row.processNode == "处理" || row.processNode == "受理") &&
          category != "CITY" &&
          row.networkTypeTop != 1
        ) {
          return true;
        } else {
          return false;
        }
      }
    },
    onOpenPeopleDialog(diaSaveName) {
      this.diaPeople.title = this.diaPeople.saveTitleMap[diaSaveName];
      this.diaPeople.saveName = diaSaveName;
      this.diaPeople.visible = true;
    },
    onSavePeople(val) {
      this[this.diaPeople.saveName](val);
    },
    builderDetermine({ usersChecked, orgsChecked, selectionUser }) {
      let createUser = [];
      if (selectionUser && selectionUser.length > 0) {
        createUser = selectionUser.map(item => ({
          value: item.userName,
          label: item.trueName,
          data: item,
        }));
      } else {
        createUser = usersChecked.map(item => ({
          value: item.id,
          label: item.name,
          data: item,
        }));
      }
      this.form.createUser = createUser;
    },
    getSheetTypeClass(data) {
      if (data.networkTypeTop == 0 && data.professionalType == "传输网") {
        return "ggw_sheetType";
      } else if (
        data.networkTypeTop == 0 &&
        data.professionalType == "IT云设备"
      ) {
        return "itCloud_sheetType";
      } else if (
        data.networkTypeTop == 0 &&
        data.professionalType == "通信云"
      ) {
        return "commCloud_sheetType";
      } else if (data.networkTypeTop == 6) {
        return "wxw_sheetType";
      } else if (data.networkTypeTop == 1) {
        return "sf_sheetType";
      } else if (data.networkTypeTop == 5) {
        return "commonFlow_sheetType";
      } else if (data.networkTypeTop == 7) {
        return "core_sheetType";
      } else if (data.networkTypeTop == 8) {
        return "ip_sheetType";
      } else if (data.networkTypeTop == 9) {
        return "pt_sheetType";
      } else if (data.networkTypeTop == 4) {
        return "gt_sheetType";
      } else if (data.networkTypeTop == 2) {
        return "sfty_sheetType";
      } else if (data.networkTypeTop == 10) {
        return "supervise_sheetType";
      }
    },
    // 外连接
    handleOuterLink() {
      let params = {
        userId: this.$store.getters.userInfo.userName,
        type: "list",
      };

      if (this.form.specialty == "无线网") {
        apiGetRsaEncryptWifi(params)
          .then(res => {
            if (res.data != "") {
              this.rsaEncrypt = res.data;
              this.rsaEncryptUrl =
                "http://************:5412/chatOpsWeb/talk-view-list?appKey=EOMS&params=" +
                res.data;
              this.chatopsVisible = true;
            } else {
              this.$message.error("获取rsa明文加密结果失败");
            }
          })
          .catch(error => {
            this.$message.error(error.msg);
          });
      } else {
        apiGetRsaEncrypt(params)
          .then(res => {
            if (res.data != "") {
              this.rsaEncrypt = res.data;
              this.rsaEncryptUrl =
                "http://************:5412/chatOpsWeb/talk-view-list?appKey=EOMS&params=" +
                res.data;
              this.chatopsVisible = true;
            } else {
              this.$message.error("获取rsa明文加密结果失败");
            }
          })
          .catch(error => {
            this.$message.error(error.msg);
          });
      }
    },
    //批量受理，错误提示信息通用，isCallInterface 是否调用接口：0否1是 isAllAccept 是否提交的全为受理工单 0:否 1:是
    messageBybatchAccpetError(data, isCallInterface, isAllAccept) {
      let numberStr = "";
      let length = data.length > 5 ? 5 : data.length;

      for (let i = 0; i < length; i++) {
        var item = data[i];
        if (isCallInterface == 0) {
          //未调用接口
          if (i == 0) {
            numberStr = item.sheetNo;
          } else {
            numberStr = numberStr + ", <br/> " + item.sheetNo;
          }
        } else if (isCallInterface == 1) {
          //调用了接口
          if (i == 0) {
            numberStr = item;
          } else {
            numberStr = numberStr + ", <br/> " + item;
          }
        }
      }
      if (data.length > 5) {
        numberStr = numberStr + "... <br/> " + "受理失败。";
      } else {
        numberStr = numberStr + " <br/> " + "受理失败。";
      }
      let msg = numberStr;
      if (isCallInterface == 0) {
        msg = "只能对待受理的工单进行“批量受理”。" + " <br/> " + numberStr;
      }
      if (isCallInterface == 1 && isAllAccept) {
        msg = "只能对待受理的工单进行“批量受理”。" + " <br/> " + numberStr;
      }
      this.$message({
        dangerouslyUseHTMLString: true,
        message: msg,
        type: "warning",
      });
    },
    getProfessionalTitle() {
      if (this.form.type == "supervise") {
        return "所属专业";
      } else {
        return "故障专业";
      }
    },
  },
};
</script>
<style lang="scss" scoped>
@import "./workOrderWaitDetail/assets/common.scss";

.filter-total {
  ::v-deep .el-loading-mask .el-loading-spinner {
    text-align: left;
    line-height: 32px;
    margin-left: 150px;
  }

  ::v-deep .el-loading-text {
    display: inline-block;
    margin-left: 10px;
  }
}

.el-table ::v-deep.select-all .el-checkbox__inner {
  display: none;
  position: relative;
}

.el-table ::v-deep.select-all .cell::before {
  position: absolute;
  content: "\9009\62e9";
  left: 7px;
}

.outer-link {
  position: fixed;
  top: 70%;
  right: 15px;
  z-index: 99;
  padding: 3px 0px 0px 0px;
  border-radius: 999px;
  box-shadow: 0 0 8px #999;
  background: rgba(255, 255, 255, 0.7);
  width: 56px;
  height: 56px;

  .icon {
    display: block;
    width: 36px;
    height: 36px;
    cursor: pointer;
    background: url("../../assets/icon_chatops.png") center no-repeat;
    background-size: 68% 60%;
    margin-left: 10px;
  }

  .name {
    color: #b50b14;
    font-size: 11px;
    position: absolute;
    margin-top: -4px;
    font-weight: 500;
    text-align: center;
    width: 56px;
  }
}

.descStyle {
  display: -webkit-box;
  overflow: hidden;
  white-space: normal !important;
  text-overflow: ellipsis;
  word-wrap: break-word;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.dfdCla {
  height: 32px;
  display: flex;
  align-items: center;
}

// 新样式
.tag-container {
  display: flex;
  align-items: center;
}

.defaultBtnCommon {
  float: right;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 80px;
  border: none;
  font-size: 12px;
  padding-right: 0px;
  background-color: transparent;
}

.defaultBtnCommon:hover {
  border: none;
  background-color: transparent;
}
</style>
