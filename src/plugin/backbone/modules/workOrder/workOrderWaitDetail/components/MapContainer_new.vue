<template>
  <div id="container"></div>
  <!-- <el-dialog
      title="故障/预警信息"
      :visible.sync="dialogFaultVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogFaultClose"
      width="33%"
      top="5vh"
    >
      <div style="width: 100%; height: 340px">
        <div>
          <div
            style="
              width: 10px;
              height: 10px;
              background-color: #409eff;
              display: inline-block;
            "
          ></div>
          <div style="display: inline-block; margin-left: 5px">资源关联</div>
        </div>
        <div
          class="contentL"
          style="
            display: flex;
            justify-content: space-between;
            flex-direction: row;
            flex-wrap: wrap;
            width: 300px;
            height: 52px;
          "
        >
          <div
            class="content-item item1"
            style="
              width: 80px;
              height: 50px;
              color: #fff;
              background-color: #a9a9a9;
            "
          >
            <div style="text-align: center">设备端口</div>
            <div style="text-align: center">
              {{ faultInfo.relSource.portCount }}</div>
          </div>
          <div
            class="content-item item2"
            style="
              width: 80px;
              height: 50px;
              color: #fff;
              background-color: #a9a9a9;
            "
          >
            <div style="text-align: center">光缆</div>
            <div style="text-align: center">
               {{ faultInfo.relSource.optCount }}
            </div>
          </div>
          <div
            class="content-item item3"
            style="
              width: 80px;
              height: 50px;
              color: #fff;
              background-color: #a9a9a9;
            "
          >
            <div style="text-align: center">系统</div>
            <div style="text-align: center">
              {{ faultInfo.relSource.sysCount }}
            </div>
          </div>
        </div>
        <div
          style="
            width: 10px;
            height: 10px;
            background-color: #409eff;
            display: inline-block;
          "
        ></div>
        <div style="display: inline-block; margin-left: 5px">资源属性</div>
        <div style="display: table; width: 100%">
          <div style="display: table-row">
            <div
              style="
                display: table-cell;
                border: 1px solid #f4f6f7;
                padding: 3px;
                background-color: #f9fafb;
                width: 30%;
              "
            >
              故障段落
            </div>
            <div
              style="
                display: table-cell;
                border: 1px solid #f4f6f7;
                padding: 3px;
              "
            >
              {{ faultInfo.optSectName }}
            </div>
          </div>
          <div style="display: table-row">
            <div
              style="
                display: table-cell;
                border: 1px solid #f4f6f7;
                padding: 3px;
                background-color: #f9fafb;
                width: 30%;
              "
            >
              故障点定位
            </div>
            <div
              style="
                display: table-cell;
                border: 1px solid #f4f6f7;
                padding: 3px;
                overflow: hidden;
                text-overflow: ellipsis;
              "
            >
              {{ faultInfo.supportSectName }}
            </div>
          </div>
          <div style="display: table-row">
            <div
              style="
                display: table-cell;
                border: 1px solid #f4f6f7;
                padding: 3px;
                background-color: #f9fafb;
                width: 30%;
              "
            >
              经度
            </div>
            <div
              style="
                display: table-cell;
                border: 1px solid #f4f6f7;
                padding: 3px;
              "
            >
              {{ faultInfo.wgs84X }}
            </div>
          </div>
          <div style="display: table-row">
            <div
              style="
                display: table-cell;
                border: 1px solid #f4f6f7;
                padding: 3px;
                background-color: #f9fafb;
                width: 30%;
              "
            >
              纬度
            </div>
            <div
              style="
                display: table-cell;
                border: 1px solid #f4f6f7;
                padding: 3px;
              "
            >
              {{ faultInfo.wgs84Y }}
            </div>
          </div>
          <div style="display: table-row">
            <div
              style="
                display: table-cell;
                border: 1px solid #f4f6f7;
                padding: 3px;
                background-color: #f9fafb;
                width: 30%;
              "
            >
              故障原因
            </div>
            <div
              style="
                display: table-cell;
                border: 1px solid #f4f6f7;
                padding: 3px;
              "
            >
              {{ faultInfo.faultReason }}
            </div>
          </div>
          <div style="display: table-row">
            <div
              style="
                display: table-cell;
                border: 1px solid #f4f6f7;
                padding: 3px;
                background-color: #f9fafb;
                width: 30%;
              "
            >
              告警发生时间
            </div>
            <div
              style="
                display: table-cell;
                border: 1px solid #f4f6f7;
                padding: 3px;
              "
            >
              {{ faultInfo.faultTime }}
            </div>
          </div>
        </div>
      </div>
    </el-dialog> -->
</template>
<script>
import loadJs from "../api/loadJs";
import * as zrender from "zrender";
import manholePng from "../assets/img/manhole.png";
import jifangPng from "../assets/img/jifang.png";
import polePng from "../assets/img/pole.png";

export default {
  name: "MapContainer",
  props: {
    woId: String,
    otdrData: Object,
  },
  data() {
    return {
      faultInfo: null,
      defaultCenterPoint: [],
      map: null,

      zr: null,
      curPoint: null,
      canvasWidth: 0,
      canvasHeight: 0,
      pointArr: [],
      setViewport: null,

      faultInfoWgs84X: null,
      faultInfoWgs84Y: null,
      zoomLevel: 18,
      dialogFaultVisible: false,
    };
  },
  created() {},
  mounted() {
    this.faultInfo = this.otdrData.faultInfo;
    loadJs("https://gis.10010.com:8219/dugis-baidu/baidumap/jsapi/api.js").then(
      () => {
        // 加载成功，进行后续操作
        const webApiBasePath = "https://gis.10010.com:8219/dugis-baidu/";
        const coordinateType = "bd09ll"; // 当前坐标类型，用于webAPI传参
        const defaultCenterPoint = this.defaultCenterPoint; // 中心点经纬度
        //DuGIS权限校验
        const apiAuthorization = "baidu-d874fd0219944973909532c3d3444432";

        this.initAMap();
      }
    );
  },
  methods: {
    /**
     * 响应marker的事件
     */
    onClickMarkerImage(event) {
      console.log("onClickMarkerImage", event);
    },

    /**
     * 添加一个marker点封装函数
     * <p> 图片image对象
     *
     * @param point 经纬度位置信息BMap.Point对象
     * @param markData 业务数据
     */
    addMarkerImage(point, markData, imgTypeName) {
      let pixel = this.map.pointToPixel(point);
      // console.log("pixel", pixel);
      let circle = new zrender.Image({
        style: {
          image:
            imgTypeName == "人井"
              ? manholePng
              : imgTypeName == "机房"
              ? jifangPng
              : imgTypeName == "电杆"
              ? polePng
              : "",
          x: pixel.x - 15,
          y: pixel.y - 16,
          width: imgTypeName == "电杆" ? 22 : 30,
          height: imgTypeName == "电杆" ? 22 : 30,
        },
        data: markData,
      });
      //点击事件
      circle.on("click", e => {
        this.onClickMarkerImage(e);
      });
      this.zr.add(circle);
    },

    /**
     * 绘制多边形
     *
     * @param points
     * @param polygonData
     */
    addPolygon(points, polygonData, isFault) {
      const polygonShape = {
        points: points,
        smooth: false,
      };
      let polygonStyle = {};
      if (isFault) {
        polygonStyle = {
          //  fill: "lightblue",
          stroke: "red",
          lineWidth: 5,
        };
      } else {
        polygonStyle = {
          fill: "lightblue",
          stroke: "#0081FF",
          lineWidth: 4,
        };
      }

      var polygon = new zrender.Polygon({
        shape: polygonShape,
        style: polygonStyle,
      });
      if (isFault) {
        polygon.on("click", e => {
          //this.dialogFaultVisible = true;
          const faultInfo = this.otdrData.faultInfo;
          let faultSection = "故障段落";
          // 这里是HTML内容
          let content =
            '<div  style="width: 100%;height: 340px;overflow-y:scroll;margin-top:5px;font-size:12px"><div><div style=" width: 10px;height: 10px; background-color: #409EFF;display:inline-block;"></div><div style="display:inline-block;margin-left:5px;font-weight:bold;font-size:13px">资源关联</div></div>' +
            '<div class="contentL" style = "display: flex;justify-content: space-between;flex-direction: row;flex-wrap: wrap;width: 340px;height: 52px;" > <div class="content-item item1" style="width: 100px;height: 48px;color: #fff;background-color: #a9a9a9;"><div style="text-align: center">设备端口</div><div style="text-align: center;font-weight:bold">' +
            faultInfo.relSource.portCount +
            "</div>" +
            "</div>" +
            '<div class="content-item item2" style="width: 100px;height: 48px;color: #fff;background-color: #a9a9a9;"><div style="text-align: center">光缆</div> <div style="text-align: center;font-weight:bold">' +
            faultInfo.relSource.optCount +
            "</div></div>" +
            ' <div class="content-item item3" style="width: 100px;height: 48px;color: #fff;background-color: #a9a9a9;"><div style="text-align: center">系统</div> <div style="text-align: center;font-weight:bold">' +
            faultInfo.relSource.sysCount +
            "</div></div></div>" +
            '<div style=" width: 10px;height: 10px; background-color: #409EFF;display:inline-block;"></div><div style="display:inline-block;margin-left:5px;font-weight:bold;margin-top:5px;font-size:13px">资源属性</div>' +
            '<div style="display: table;width: 95%;">' +
            '<div style="display: table-row;">' +
            '<div style="display: table-cell;border: 1px solid #f4f6f7; padding: 3px;background-color: #f9fafb;width: 30%;">' +
            faultSection +
            "</div>" +
            '<div style="display: table-cell;border: 1px solid #f4f6f7; padding: 3px;">' +
            faultInfo.optSectName +
            "</div>" +
            "</div>" +
            '<div style="display: table-row;">' +
            '<div style="display: table-cell;border: 1px solid #f4f6f7; padding: 3px;background-color: #f9fafb;width: 30%;">故障点定位' +
            "</div>" +
            '<div style="display: table-cell;border: 1px solid #f4f6f7; padding: 3px;overflow:hidden;text-overflow:ellipsis">' +
            faultInfo.supportSectName +
            "</div>" +
            "</div>" +
            '<div style="display: table-row;">' +
            '<div style="display: table-cell;border: 1px solid #f4f6f7; padding: 3px;background-color: #f9fafb;width: 30%;">经度' +
            "</div>" +
            '<div style="display: table-cell;border: 1px solid #f4f6f7; padding: 3px;">' +
            faultInfo.wgs84X +
            "</div>" +
            "</div>" +
            '<div style="display: table-row;">' +
            '<div style="display: table-cell;border: 1px solid #f4f6f7; padding: 3px;background-color: #f9fafb;width: 30%;">纬度' +
            "</div>" +
            '<div style="display: table-cell;border: 1px solid #f4f6f7; padding: 3px;">' +
            faultInfo.wgs84Y +
            "</div>" +
            "</div>" +
            '<div style="display: table-row;">' +
            '<div style="display: table-cell;border: 1px solid #f4f6f7; padding: 3px;background-color: #f9fafb;width: 30%;">故障原因' +
            "</div>" +
            '<div style="display: table-cell;border: 1px solid #f4f6f7; padding: 3px;">' +
            faultInfo.faultReason +
            "</div>" +
            "</div>" +
            '<div style="display: table-row;">' +
            '<div style="display: table-cell;border: 1px solid #f4f6f7; padding: 3px;background-color: #f9fafb;width: 30%;">告警发生时间' +
            "</div>" +
            '<div style="display: table-cell;border: 1px solid #f4f6f7; padding: 3px;">' +
            faultInfo.faultTime +
            "</div>" +
            "</div>" +
            " </div>";
          const opts = {
            width: 370, // 信息窗口宽度
            height: 350, // 信息窗口高度
            title: "故障/预警信息", // 信息窗口标题
          };
          const infoWindow = new window.BMap.InfoWindow(content, opts);
          this.map.openInfoWindow(
            infoWindow,
            new window.BMap.Point(
              parseFloat(this.faultInfoWgs84X),
              parseFloat(this.faultInfoWgs84Y)
            )
          );
        });
      }
      this.zr.add(polygon);
    },

    // 这里修改为创建markerImage
    onCanvasUpdate() {
      console.log(this.map.getZoom());
      this.zr.resize();
      const faultInfo = this.otdrData.faultInfo;
      const lineList = this.otdrData.lineList;

      const pointList = [];
      // const polylinePoints = [];
      lineList.forEach(item => {
        const points = [];
        points.push({
          lng: item.beginPointInfo.holeWgs84X,
          lat: item.beginPointInfo.holeWgs84Y,
        });
        points.push({
          lng: item.endPointInfo.holeWgs84X,
          lat: item.endPointInfo.holeWgs84Y,
        });

        //画线
        let pixelPoints = [];
        for (let point of points) {
          let { x, y } = this.map.pointToPixel(point);
          pixelPoints.push([x, y]);
        }
        if (faultInfo.supportSectName != item.supportSectName) {
          this.addPolygon(pixelPoints);
        } else {
          //画故障线
          this.addPolygon(pixelPoints, null, true);
        }
        //画点
        if (item.beginPointInfo.typeName) {
          let point = new window.BMap.Point(
            parseFloat(item.beginPointInfo.holeWgs84X),
            parseFloat(item.beginPointInfo.holeWgs84Y)
          );
          pointList.push(point);
          if (this.map.getZoom() > 13) {
            this.addMarkerImage(point, item, item.beginPointInfo.typeName);
          } else {
            if (item.beginPointInfo.typeName == "机房") {
              this.addMarkerImage(point, item, item.beginPointInfo.typeName);
            }
          }
        }
        if (item.endPointInfo.typeName) {
          let point = new window.BMap.Point(
            parseFloat(item.endPointInfo.holeWgs84X),
            parseFloat(item.endPointInfo.holeWgs84Y)
          );
          pointList.push(point);
          if (this.map.getZoom() > 13) {
            this.addMarkerImage(point, item, item.endPointInfo.typeName);
          } else {
            if (item.endPointInfo.typeName == "机房") {
              this.addMarkerImage(point, item, item.endPointInfo.typeName);
            }
          }
        }
      });
      //画故障点
      let faultPoint = new window.BMap.Point(
        parseFloat(faultInfo.wgs84X),
        parseFloat(faultInfo.wgs84Y)
      );
      this.addMarkerImage(faultPoint, null, "电杆");

      // 注意这里的代码需要控制触发一次，因为会触发canvas的update函数
      if (!this.setViewport) {
        // 根据点的位置自动设置地图的视口
        this.map.setViewport(pointList);
        this.setViewport = true;
        const point = new window.BMap.Point(faultInfo.wgs84X, faultInfo.wgs84Y);
        //初始化地图，设置中心点坐标和地图级别
        this.map.centerAndZoom(point, 18);
      }
    },

    initAMap() {
      //创建地图实例;
      let map = (this.map = new window.BMap.Map("container", {
        resizeEnable: true,
      }));

      let that = this;
      const canvasLayer = new window.BMap.CanvasLayer({
        update: function () {
          if (!that.zr) {
            that.zr = zrender.init(this.canvas); //初始化zrender
            that.canvasWidth = that.zr.getWidth();
            that.canvasHeight = that.zr.getHeight();
            that.onCanvasUpdate();
          } else {
            that.zr.clear();
            that.onCanvasUpdate();
          }
        },
      });
      map.addOverlay(canvasLayer);
      const faultInfo = this.otdrData.faultInfo;
      // this.map.setMapType(window.BMAP_NORMAL_MAP);
      // 创建点坐标
      // const point = new window.BMap.Point(116.404, 39.915);
      this.faultInfoWgs84X = parseFloat(faultInfo.wgs84X);
      this.faultInfoWgs84Y = parseFloat(faultInfo.wgs84Y);
      const point = new window.BMap.Point(
        parseFloat(faultInfo.wgs84X),
        parseFloat(faultInfo.wgs84Y)
      );
      //初始化地图，设置中心点坐标和地图级别
      this.map.centerAndZoom(point, 15);
      // 开启鼠标滚轮缩放
      this.map.enableScrollWheelZoom(true);
      var top_left_navigation = new window.BMap.NavigationControl(); //左上角，添加默认缩放平移控件
      this.map.addControl(top_left_navigation);
      this.map.setMaxZoom(18);
    },

    dialogFaultClose() {
      this.dialogFaultVisible = false;
    },
  },
};
</script>
<style lang="less" scoped>
// .map-wrap {
//   position: relative;
//   margin-top: 5px;
// }

// .map-wrap .note {
//   z-index: 999;
//   position: absolute;
//   bottom: 20px;
//   right: 20px;
//   display: block;
//   width: 64px;
//   height: 64px;
//   line-height: 64px;
//   border-radius: 999px;
//   text-align: center;
//   font-weight: bold;
//   color: #fff;
//   background: #05a605;
// }

#container {
  padding: 0px;
  margin: 0px;
  width: 100%;
  height: 100%;
  min-height: 500px;
  overflow: hidden;
}
</style>
