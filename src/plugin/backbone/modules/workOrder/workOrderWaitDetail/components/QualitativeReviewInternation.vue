<template>
  <div class="qualitative-review">
    <el-form
      ref="qualitativeReviewForm"
      :inline="false"
      class="demo-form-inline"
      :model="qualitativeReviewForm"
      label-width="140px"
      :rules="qualitativeReviewFormRules"
    >
      <el-card
        shadow="never"
        header="故障定性信息"
        :body-style="{ padding: '20px 8px' }"
        class="cus-card"
      >
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item
              label="故障所属专业:"
              prop="professionalType"
              :rules="{
                required: true,
                message: '请选择故障所属专业',
              }"
            >
              <dict-select
                :value.sync="qualitativeReviewForm.professionalType"
                :dictId="10002"
                placeholder=""
                style="width: 100%"
                :notSelect="true"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障发生时间:" prop="alarmCreateTime" required>
              {{ qualitativeReviewForm.alarmCreateTime }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障通知时间:" prop="sheetCreateTime" required>
              {{ qualitativeReviewForm.sheetCreateTime }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障得知时间:" prop="faultKnowTime" required>
              {{ qualitativeReviewForm.faultKnowTime }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障代通时间:" prop="lastClearTime">
              <el-date-picker
                v-model="qualitativeReviewForm.lastClearTime"
                type="datetime"
                placeholder="请选择故障代通时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
                disabled
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障代通历时:">
              {{ second2Time(qualitativeReviewForm.lastClearDuration) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障结束时间:" prop="faultEndTime">
              <el-date-picker
                v-model="qualitativeReviewForm.faultEndTime"
                type="datetime"
                placeholder="请选择故障结束时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
                disabled
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障处理历时:" required>
              {{ second2Time(this.qualitativeReviewForm.faultDuration) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="故障发生国家:"
              prop="faultHappenCountry"
              :rules="{
                required: true,
                message: '请输入内容',
              }"
            >
              <el-input
                v-model="qualitativeReviewForm.faultHappenCountry"
                disabled
                placeholder="请输入内容"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障处理部门:" required>
              {{ qualitativeReviewForm.dept }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="处理人:" prop="person" required>
              {{ qualitativeReviewForm.person }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="挂起历时:" required>
              {{ second2Time(this.qualitativeReviewForm.suspendDuration) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障处理净历时:" required>
              {{ second2Time(this.qualitativeReviewForm.processDuration) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="是否影响业务:"
              prop="isEffectBusiness"
              :rules="{
                required: true,
                message: '请选择内容',
              }"
            >
              <el-radio-group
                v-model="qualitativeReviewForm.isEffectBusiness"
                disabled
              >
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item
              label="影响范围:"
              v-if="qualitativeReviewForm.isEffectBusiness == '1'"
            >
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                disabled
                v-model="qualitativeReviewForm.effectRange"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card
        shadow="never"
        header="故障专业信息"
        :body-style="{ padding: '20px 8px' }"
        class="cus-card"
      >
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item
              label="故障状态:"
              prop="faultStatus"
              :rules="{
                required: true,
                message: '请选择故障状态',
              }"
            >
              <dict-select
                :value.sync="qualitativeReviewForm.faultStatus"
                :dictId="10017"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="网络类型:"
              prop="networkType"
              :rules="{
                required: true,
                message: '请选择网络类型',
              }"
            >
              <dict-select
                :value.sync="qualitativeReviewForm.networkType"
                :dictId="10063"
                style="width: 100%"
                :notSelect="true"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="故障分类:"
              prop="faultCate"
              :rules="{
                required: true,
                message: '请选择故障分类',
              }"
            >
              <dict-select
                :value.sync="qualitativeReviewForm.faultCate"
                :dictId="10055"
                style="width: 100%"
                @change="faultCateChange"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row
          type="flex"
          style="flex-wrap: wrap"
          :gutter="20"
          v-if="qualitativeReviewForm.faultCate == '1'"
          key="faultReasonCate"
        >
          <el-col :span="8">
            <el-form-item
              label="故障原因分类:"
              :rules="{
                required: true,
                message: '请输入内容',
              }"
              prop="faultReasonCate"
            >
              <el-input
                v-model="qualitativeReviewForm.faultReasonCate"
                style="width: 100%"
                placeholder="请输入内容"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="所在海缆:"
              prop="seaFiberName"
              :rules="{
                required: true,
                message: '请输入内容',
              }"
            >
              <el-input
                v-model="qualitativeReviewForm.seaFiberName"
                placeholder="请输入内容"
                style="width: 100%"
              >
                <el-button
                  style="
                    background: #b50b14;
                    border-color: #b50b14;
                    color: #fff;
                    padding: 9px 20px;
                    top: -0.8px;
                    position: relative;
                  "
                  slot="append"
                  @click="selectSeaFiberName"
                >
                  选择
                </el-button>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="海缆段:"
              prop="seaFiberSeg"
              :rules="{
                required: true,
                message: '请输入内容',
              }"
            >
              <el-input
                v-model="qualitativeReviewForm.seaFiberSeg"
                style="width: 100%"
                placeholder="请输入内容"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="运营商名称:"
              prop="operatorName"
              :rules="{
                required: true,
                message: '请输入内容',
              }"
            >
              <el-input
                v-model="qualitativeReviewForm.operatorName"
                style="width: 100%"
                placeholder="请输入内容"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="海缆类型:"
              prop="seaFiberType"
              :rules="{
                required: true,
                message: '请选择内容',
              }"
            >
              <dict-select
                :value.sync="qualitativeReviewForm.seaFiberType"
                :dictId="10061"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="故障海域:"
              prop="seaFiberFaultArea"
              :rules="{
                required: true,
                message: '请选择内容',
              }"
            >
              <dict-select
                :value.sync="qualitativeReviewForm.seaFiberFaultArea"
                :dictId="10062"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="是否完成修复:"
              prop="isRepairCompleted"
              :rules="{
                required: true,
                message: '请选择内容',
              }"
            >
              <el-radio-group
                v-model="qualitativeReviewForm.isRepairCompleted"
                style="width: 100%"
              >
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="关联海缆站点段:"
              prop="connectedSeaFiberStationSection"
              :rules="{
                required: true,
                message: '请输入内容',
              }"
            >
              <el-input
                v-model="qualitativeReviewForm.connectedSeaFiberStationSection"
                style="width: 100%"
                placeholder="请输入内容"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否超时:" prop="isOverTimeShow">
              {{ qualitativeReviewForm.isOverTimeShow }}
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item
              label="故障原因描述:"
              prop="falutReasonDesc"
              :rules="{
                required: qualitativeReviewForm.faultCate == '1' ? true : false,
                message: '请填写故障原因描述',
              }"
            >
              <el-input
                maxlength="500"
                show-word-limit
                type="textarea"
                :rows="2"
                :autosize="{ minRows: 3, maxRows: 6 }"
                v-model="qualitativeReviewForm.falutReasonDesc"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              v-if="isRouteAdjustShow"
              label="是否路由调整:"
              prop="isRouteAdjust"
            >
              <el-radio-group
                v-model="qualitativeReviewForm.isRouteAdjust"
                style="width: 100%"
                disabled
              >
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              v-if="qualitativeReviewForm.isRouteAdjust == 1 && isRouteNoShow"
              label="路由调整单号:"
            >
              <el-link
                :underline="false"
                @click="toRoutePage"
                type="primary"
                style="color: #409eff"
                >{{ qualitativeReviewForm.routeNo }}</el-link
              >
            </el-form-item>
          </el-col>
        </el-row>
        <el-row
          type="flex"
          style="flex-wrap: wrap"
          :gutter="20"
          v-if="qualitativeReviewForm.faultCate == '2'"
          key="faultReasonCate"
        >
          <el-col :span="8">
            <el-form-item
              label="故障原因分类:"
              :rules="{
                required: true,
                message: '请输入内容',
              }"
              prop="faultReasonCate"
            >
              <el-input
                v-model="qualitativeReviewForm.faultReasonCate"
                style="width: 100%"
                placeholder="请输入内容"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="所在陆缆:"
              prop="landFiberName"
              :rules="{
                required: true,
                message: '请输入内容',
              }"
            >
              <el-input
                v-model="qualitativeReviewForm.landFiberName"
                placeholder="请输入内容"
                style="width: 100%"
              >
                <!-- <el-button
                  style=" background: #b50b14;
                    border-color: #b50b14;
                    color: #fff;
                    padding: 9px 20px;
                    top: -0.8px;
                    position: relative;
                  "
                  slot="append"
                  @click="selectLandFiberName"
                >
                  选择
                </el-button> -->
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="陆缆段:"
              prop="landFiberSeg"
              :rules="{
                required: true,
                message: '请输入内容',
              }"
            >
              <el-input
                v-model="qualitativeReviewForm.landFiberSeg"
                style="width: 100%"
                placeholder="请输入内容"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="供应商:"
              prop="provider"
              :rules="{
                required: true,
                message: '请输入内容',
              }"
            >
              <el-input
                v-model="qualitativeReviewForm.provider"
                style="width: 100%"
                placeholder="请输入内容"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="是否完成修复:"
              prop="isRepairCompleted"
              :rules="{
                required: true,
                message: '请选择内容',
              }"
            >
              <el-radio-group
                v-model="qualitativeReviewForm.isRepairCompleted"
                style="width: 100%"
              >
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否超时:" prop="isOverTimeShow">
              {{ qualitativeReviewForm.isOverTimeShow }}
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item
              label="故障原因描述:"
              prop="falutReasonDesc"
              :rules="{
                required: qualitativeReviewForm.faultCate == '1' ? true : false,
                message: '请填写故障原因描述',
              }"
            >
              <el-input
                maxlength="500"
                show-word-limit
                type="textarea"
                :rows="2"
                :autosize="{ minRows: 3, maxRows: 6 }"
                v-model="qualitativeReviewForm.falutReasonDesc"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              v-if="isRouteAdjustShow"
              label="是否路由调整:"
              prop="isRouteAdjust"
            >
              <el-radio-group
                v-model="qualitativeReviewForm.isRouteAdjust"
                style="width: 100%"
                disabled
              >
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              v-if="qualitativeReviewForm.isRouteAdjust == 1 && isRouteNoShow"
              label="路由调整单号:"
            >
              <el-link
                :underline="false"
                @click="toRoutePage"
                type="primary"
                style="color: #409eff"
                >{{ qualitativeReviewForm.routeNo }}</el-link
              >
            </el-form-item>
          </el-col>
        </el-row>
        <el-row
          type="flex"
          style="flex-wrap: wrap"
          :gutter="20"
          v-if="qualitativeReviewForm.faultCate == '3'"
          key="faultCateTwo"
        >
          <el-col :span="8">
            <el-form-item
              label="故障原因分类:"
              prop="faultReasonCate"
              :rules="{
                required: true,
                message: '请输入内容',
              }"
            >
              <el-input
                v-model="qualitativeReviewForm.faultReasonCate"
                style="width: 100%"
                placeholder="请输入内容"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="故障国家:"
              prop="faultCountry"
              :rules="{
                required: true,
                message: '请输入内容',
              }"
            >
              <el-input
                placeholder="请输入内容"
                v-model="qualitativeReviewForm.faultCountry"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="运营商名称:"
              :rules="{
                required: qualitativeReviewForm.faultCate == '2' ? true : false,
                message: '请输入内容',
              }"
              prop="operatorName"
            >
              <el-input
                placeholder="请输入内容"
                v-model="qualitativeReviewForm.operatorName"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否超时:" prop="isOverTimeShow">
              {{ qualitativeReviewForm.isOverTimeShow }}
            </el-form-item></el-col
          >
          <el-col :span="24">
            <el-form-item
              label="故障原因描述:"
              prop="falutReasonDesc"
              :rules="{
                required: true,
                message: '请填写故障原因描述',
              }"
            >
              <el-input
                maxlength="500"
                show-word-limit
                type="textarea"
                :rows="2"
                :autosize="{ minRows: 3, maxRows: 6 }"
                v-model="qualitativeReviewForm.falutReasonDesc"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              v-if="isRouteAdjustShow"
              label="是否路由调整:"
              prop="isRouteAdjust"
            >
              <el-radio-group
                v-model="qualitativeReviewForm.isRouteAdjust"
                style="width: 100%"
                disabled
              >
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              v-if="qualitativeReviewForm.isRouteAdjust == 1 && isRouteNoShow"
              label="路由调整单号:"
            >
              <el-link
                :underline="false"
                @click="toRoutePage"
                type="primary"
                style="color: #409eff"
                >{{ qualitativeReviewForm.routeNo }}</el-link
              >
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card
        shadow="never"
        header="定性审核信息"
        :body-style="{ padding: '20px 8px' }"
        style="margin-top: 20px"
        class="cus-card"
      >
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item
              label="审批结果:"
              prop="auditResult"
              :rules="{
                required: true,
                message: '请选择审批结果',
              }"
            >
              <el-radio-group
                v-model="qualitativeReviewForm.auditResult"
                @change="changeAuditResult()"
              >
                <el-radio label="1">同意</el-radio>
                <el-radio label="0">拒绝</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="是否上传故障报告:"
              :rules="{
                required: true,
                message: '请选择是否上传故障报告',
              }"
              prop="isUploadReport"
            >
              <el-radio-group
                v-model="qualitativeReviewForm.isUploadReport"
                @change="changeUploadReport"
                :disabled="qualitativeReviewForm.auditResult == '1'"
              >
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="是否全选入库:"
              :rules="{
                required: true,
                message: '请选择是否全选入库',
              }"
              prop="isAllStored"
            >
              <el-radio-group
                v-model="qualitativeReviewForm.isAllStored"
                :disabled="
                  qualitativeReviewForm.auditResult == '1' ||
                  qualitativeReviewForm.auditResult == '0'
                "
              >
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="处理时限:">
              <el-radio-group
                v-model="qualitativeReviewForm.dealTimeLimit"
                style="width: 100%"
              >
                <el-radio
                  v-for="(item, i) in processTimeLimitOptions"
                  :key="i"
                  :label="item.dictCode"
                  >{{ item.dictName }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="第二故障源流水号:" prop="secondFalutSource">
              <el-input
                style="width: 100%"
                v-model="qualitativeReviewForm.secondFalutSource"
                placeholder="请输入内容"
                maxlength="255"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="业务影响范围:" prop="busEffectRange">
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="qualitativeReviewForm.busEffectRange"
                style="width: 100%"
                show-word-limit
                maxlength="255"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障所在省:">
              <el-input
                style="width: 100%"
                placeholder="请选择内容"
                v-model="qualitativeReviewForm.faultProvince"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="审批意见:" prop="auditContent">
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model.trim="qualitativeReviewForm.auditContent"
                style="width: 100%"
                show-word-limit
                maxlength="255"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: center">
      <el-button
        type="primary"
        @click="handleSubmit('qualitativeReviewForm')"
        v-loading.fullscreen.lock="qualitativeReviewFullscreenLoading"
        >提交</el-button
      >
      <el-button @click="onReset">重 置</el-button>
    </div>
    <el-dialog
      width="620px"
      title="海缆名称"
      :visible.sync="seaDialogVisible"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      append-to-body
      top="5vh"
    >
      <div>
        <el-checkbox-group v-model="seaCheckBox" size="small">
          <el-checkbox
            v-for="(item, index) in seaCheckBoxArr"
            :key="index"
            :label="item.dictName"
            border
            style="width: 140px; margin-top: 10px; margin-left: 0px"
          ></el-checkbox>
        </el-checkbox-group>
      </div>
      <span slot="footer">
        <el-button type="primary" @click="seaCheckBoxDetermine"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <el-dialog
      width="620px"
      title="陆缆名称"
      :visible.sync="landDialogVisible"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      append-to-body
      top="5vh"
    >
      <div>
        <el-checkbox-group v-model="landCheckBox" size="small">
          <el-checkbox
            v-for="(item, index) in landCheckBoxArr"
            :key="index"
            :label="item.dictName"
            border
            style="width: 140px; margin-top: 10px; margin-left: 0px"
          ></el-checkbox>
        </el-checkbox-group>
      </div>
      <span slot="footer">
        <el-button type="primary" @click="landCheckBoxDetermine"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import DictSelect from "../../components/DictSelect.vue";
import {
  apiQualitativeInternationDetail,
  apiQualitativeInternationReview,
  apiGetProvinceDict,
} from "../api/CommonApi";
import { apiDict } from "../../api/CommonApi";
import { mixin } from "../../../../../../mixins";
export default {
  name: "QualitativeReviewInternation",
  props: {
    common: Object,
    workItemId: [String, Number],
  },
  components: { DictSelect },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  mixins: [mixin],
  data() {
    return {
      qualitativeReviewForm: {
        woId: null,
        workItemId: null,
        processInstId: null,
        processDefId: null,
        professionalType: null,
        alarmCreateTime: null,
        sheetCreateTime: null,
        faultKnowTime: null,
        lastClearTime: null,
        lastClearDuration: 0, //故障代通历时(单位秒)
        faultEndTime: null,
        faultDuration: 0, //故障处理历时(单位秒)
        faultHappenCountry: null,
        dept: null,
        person: null,
        suspendDuration: 0, //挂起历时
        processDuration: null, //故障处理净历时
        isEffectBusiness: null,
        effectRange: null,
        //海陆缆故障
        faultStatus: null,
        networkType: null,
        faultCate: null,
        faultReasonCate: null,
        seaFiberName: null,
        seaFiberSeg: null,
        landFiberName: null,
        landFiberSeg: null,
        operatorName: null,
        seaFiberType: null,
        seaFiberFaultArea: null,
        isOverTimeShow: "否",
        falutReasonDesc: null,
        //设备故障
        faultCountry: null,
        isRepairCompleted: null,
        connectedSeaFiberStationSection: "",
        isEffectBusiness: null,
        effectRange: null,
        isRouteAdjust: null,
        routeNo: null,
        routeProcessInstId: null,
        //审核信息
        auditResult: null,
        isUploadReport: "0",
        isAllStored: null,
        dealTimeLimit: null,
        secondFalutSource: null,
        busEffectRange: null, //业务影响范围
        auditContent: null,
        faultProvince: null,
      },
      qualitativeReviewFormRules: {
        secondFalutSource: [
          {
            validator: this.checkLength,
            max: 255,
            form: "qualitativeReviewForm",
            messsage: "已超过填写字数上限",
          },
        ],
        busEffectRange: [
          {
            validator: this.checkLength,
            max: 255,
            form: "qualitativeReviewForm",
            messsage: "已超过填写字数上限",
          },
        ],
        auditContent: [
          {
            required: true,
            message: "请填写审批意见",
          },
          {
            validator: this.checkLength,
            max: 255,
            form: "qualitativeReviewForm",
            messsage: "已超过填写字数上限",
          },
        ],
      },
      backSingleFullscreenLoading: false,
      processTimeLimitOptions: [], //处理时限
      qualitativeReviewFullscreenLoading: false,
      provinceOption: [],
      //海陆缆名称
      seaCheckBox: [],
      seaCheckBoxArr: [],
      landCheckBox: [],
      landCheckBoxArr: [],
      seaDialogVisible: false,
      landDialogVisible: false,
      isRouteNoShow: true,
      isRouteAdjustShow: true,
    };
  },
  mounted() {
    this.qualitativeReviewForm.workItemId = this.workItemId;
    this.qualitativeReviewForm.woId = this.common.woId;
    this.qualitativeReviewForm.processInstId = this.common.processInstId;
    this.qualitativeReviewForm.processDefId = this.common.processDefId;
    this.getProcessTimeLimitOptions();
    this.qualitativeReviewDetail();

    this.getProvinceDict();
    this.getSeaOptions();
    //this.getLandOptions();
  },
  methods: {
    //时间对比
    disabledDate(time) {
      let now = moment(this.createTimeDate);
      let a = moment(now).subtract(1, "days");
      let today = moment(a).valueOf();
      return time.getTime() <= today || time.getTime() - 8.64e6 >= Date.now();
    },
    addZero(s) {
      return s < 10 ? "0" + s : s;
    },
    getSeaOptions() {
      let param = {
        dictTypeCode: "10057",
      };
      apiDict(param)
        .then(res => {
          if (res.code == "200") {
            this.seaCheckBoxArr = res?.data ?? [];
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    getLandOptions() {
      let param = {
        dictTypeCode: "10057",
      };
      apiDict(param)
        .then(res => {
          if (res.code == "200") {
            this.landCheckBoxArr = res?.data ?? [];
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    getProvinceDict() {
      apiGetProvinceDict()
        .then(res => {
          if (res.status == "0") {
            this.provinceOption = res?.data ?? [];
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    getProcessTimeLimitOptions() {
      let param = {
        dictTypeCode: "10051",
      };
      apiDict(param)
        .then(res => {
          if (res.code == "200") {
            this.processTimeLimitOptions = res?.data ?? [];
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    qualitativeReviewDetail() {
      let param = {
        opType: 2,
        workItemId: this.qualitativeReviewForm.workItemId,
        woId: this.qualitativeReviewForm.woId,
      };
      apiQualitativeInternationDetail(param)
        .then(res => {
          if (res.status == "0") {
            this.qualitativeReviewForm = res?.data?.rows?.[0] ?? {};
            this.$set(this.qualitativeReviewForm, "isAllStored", "1");
            this.$set(this.qualitativeReviewForm, "isUploadReport", "0");
            this.$set(
              this.qualitativeReviewForm,
              "faultProvince",
              "联通国际公司"
            );
            this.qualitativeReviewForm.isOverTimeShow = "否";
            if (this.qualitativeReviewForm.seaFiberName) {
              this.seaCheckBox = this.qualitativeReviewForm.seaFiberName.split(
                ","
              );
            }
            if (this.qualitativeReviewForm.landFiberName) {
              this.landCheckBox = this.qualitativeReviewForm.landFiberName.split(
                ","
              );
            }
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    handleSubmit(formName) {
      this.$refs[formName].validate((valid, a) => {
        if (this.uncheck(a)) {
          this.qualitativeReviewFullscreenLoading = true;
          this.$set(this.qualitativeReviewForm, "actionName", "定性审核");
          if (this.qualitativeReviewForm.isEffectBusiness == "0") {
            this.qualitativeReviewForm.effectRange = "";
          }
          apiQualitativeInternationReview({
            param1: JSON.stringify(this.qualitativeReviewForm),
          })
            .then(res => {
              if (res.status == "0") {
                this.$message.success("故障定性审核完成");
                this.$emit("qualitativeReviewInternationSubmit", res.data);
              } else {
                this.$message.error("故障定性审核失败");
              }
              this.qualitativeReviewFullscreenLoading = false;
            })
            .catch(error => {
              console.log(error);
              this.$message.error("故障定性审核失败");
              this.qualitativeReviewFullscreenLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    faultCateChange() {
      this.resetFaultCateChange();
    },
    resetFaultCateChange() {
      this.qualitativeReviewForm.faultReasonCate = null;
      this.qualitativeReviewForm.seaFiberName = null;
      this.qualitativeReviewForm.seaFiberSeg = null;
      this.qualitativeReviewForm.landFiberName = null;
      this.qualitativeReviewForm.landFiberSeg = null;
      this.qualitativeReviewForm.operatorName = null;
      this.qualitativeReviewForm.seaFiberType = null;
      this.qualitativeReviewForm.seaFiberFaultArea = null;
      this.qualitativeReviewForm.faultCountry = null;
      this.qualitativeReviewForm.provider = null;
      this.qualitativeReviewForm.isRepairCompleted = null;
      this.qualitativeReviewForm.connectedSeaFiberStationSection = null;
    },
    //审批结果事件
    changeAuditResult() {
      if (this.qualitativeReviewForm.auditResult == "1") {
        this.qualitativeReviewForm.isUploadReport = "0";
        this.qualitativeReviewForm.isAllStored = "1";
        this.$set(
          this.qualitativeReviewForm,
          "auditContent",
          "确认故障已恢复，工单返单合规，同意结单"
        );
      } else if (this.qualitativeReviewForm.auditResult == "0") {
        this.qualitativeReviewForm.isAllStored = "0";
        this.$set(this.qualitativeReviewForm, "auditContent", "");
      } else if (
        this.qualitativeReviewForm.isUploadReport == "1" &&
        this.qualitativeReviewForm.auditResult == "0"
      ) {
        this.$set(this.qualitativeReviewForm, "auditContent", "请上传故障报告");
      } else if (
        this.qualitativeReviewForm.isUploadReport == "0" &&
        this.qualitativeReviewForm.auditResult == "0"
      ) {
        this.$set(this.qualitativeReviewForm, "auditContent", "");
      }
    },
    changeUploadReport() {
      if (
        this.qualitativeReviewForm.isUploadReport == "1" &&
        this.qualitativeReviewForm.auditResult == "0"
      ) {
        this.$set(this.qualitativeReviewForm, "auditContent", "请上传故障报告");
      } else if (
        this.qualitativeReviewForm.isUploadReport == "0" &&
        this.qualitativeReviewForm.auditResult == "0"
      ) {
        this.$set(this.qualitativeReviewForm, "auditContent", "");
      }
    },
    selectSeaFiberName() {
      this.seaDialogVisible = true;
    },
    selectLandFiberName() {
      this.landDialogVisible = true;
    },
    seaCheckBoxDetermine() {
      this.$set(
        this.qualitativeReviewForm,
        "seaFiberName",
        this.seaCheckBox.join(",")
      );
      this.seaDialogVisible = false;
    },
    landCheckBoxDetermine() {
      this.$set(
        this.qualitativeReviewForm,
        "landFiberName",
        this.landCheckBox.join(",")
      );
      this.landDialogVisible = false;
    },
    second2Time(days) {
      return this.showTime(Math.abs(days));
    },
    showTime(val) {
      if (val) {
        if (val == 0) return "0秒";
        var time = "";
        var second = val % 60;
        var minute = parseInt(parseInt(val) / 60) % 60;
        time = second + "秒";
        if (minute >= 1) {
          time = minute + "分" + second + "秒";
        }
        var hour = parseInt(parseInt(val / 60) / 60) % 24;
        if (hour >= 1) {
          time = hour + "小时" + minute + "分" + second + "秒";
        }
        var day = parseInt(parseInt(parseInt(val / 60) / 60) / 24);
        if (day >= 1) {
          time = day + "天" + hour + "小时" + minute + "分" + second + "秒";
        }

        return time;
      } else {
        return "0秒";
      }
    },
    onReset() {
      // this.qualitativeReviewForm = {
      //   ...this.$options.data,
      //   woId: this.common.woId,
      //   workItemId: this.workItemId,
      //   processInstId: this.common.processInstId,
      //   processDefId: this.common.processDefId,
      //   faultProvince: "联通国际公司",
      // };
      // this.qualitativeReviewDetail();
      this.qualitativeReviewForm.faultStatus = null;
      this.qualitativeReviewForm.faultCate = null;
      this.qualitativeReviewForm.faultReasonCate = null;
      this.qualitativeReviewForm.faultCountry = null;
      this.qualitativeReviewForm.operatorName = null;
      this.qualitativeReviewForm.isEffectBusiness = null;
      this.qualitativeReviewForm.effectRange = null;
      this.qualitativeReviewForm.falutReasonDesc = null;
      this.qualitativeReviewForm.seaFiberName = null;
      this.qualitativeReviewForm.seaFiberSeg = null;
      this.qualitativeReviewForm.landFiberName = null;
      this.qualitativeReviewForm.landFiberSeg = null;
      this.qualitativeReviewForm.seaFiberType = null;
      this.qualitativeReviewForm.seaFiberFaultArea = null;
      this.qualitativeReviewForm.connectedSeaFiberStationSection = null;
      this.$set(this.qualitativeReviewForm, "auditResult", null);
      this.$set(this.qualitativeReviewForm, "isUploadReport", "0");
      this.$set(this.qualitativeReviewForm, "isAllStored", "1");
      this.$set(this.qualitativeReviewForm, "dealTimeLimit", null);
      this.$set(this.qualitativeReviewForm, "secondFalutSource", null);
      this.$set(this.qualitativeReviewForm, "busEffectRange", null);
      this.$set(this.qualitativeReviewForm, "faultProvince", "联通国际公司");
      this.$set(this.qualitativeReviewForm, "auditContent", null);
    },
  },
};
</script>
<style lang="scss" scoped>
.qualitative-review {
  max-height: calc(90vh - 150px);
  min-height: 400px;
  margin: 0 4px;
  padding-left: 4px;
  padding-right: 8px;
  overflow-y: auto;
  .cus-card ::v-deep .el-card__header {
    padding: 11px 24px 10px;
    font-weight: 400;
    @include themify {
      background-color: themed("$--background-color-base");
    }
  }
  .fileName_style_download {
    margin-right: 3px;
    cursor: pointer;
    vertical-align: middle;
    div {
      display: inline-block;
      max-width: 120px;
      vertical-align: top;
    }
  }
  .fileName_style {
    margin-right: 3px;
    vertical-align: middle;
    div {
      display: inline-block;
      max-width: 120px;
      vertical-align: top;
    }
  }
}
</style>
