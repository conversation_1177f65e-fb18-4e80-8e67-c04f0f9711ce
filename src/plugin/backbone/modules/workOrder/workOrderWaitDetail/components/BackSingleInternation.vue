<template>
  <div class="back-single-internation">
    <el-form ref="backSingleForm" :inline="false" class="demo-form-inline" :model="backSingleForm" label-width="130px"
      :rules="backSingleFormRule">
      <el-card shadow="never" header="故障定性信息" :body-style="{ padding: '20px 8px' }" class="cus-card">
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item label="故障所属专业:" prop="professionalType" :rules="{
              required: true,
              message: '请选择故障所属专业',
            }">
              <dict-select :value.sync="backSingleForm.professionalType" :dictId="10002" style="width: 100%"
                :woId="common.woId" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障发生时间:" prop="alarmCreateTime" required>
              {{ backSingleForm.alarmCreateTime }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障通知时间:" prop="sheetCreateTime" required>
              {{ backSingleForm.sheetCreateTime }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障得知时间:" prop="faultKnowTime" :rules="{
              required: true,
              message: '请选择内容',
            }">
              <el-date-picker v-model="backSingleForm.faultKnowTime" type="datetime" placeholder="请选择时间"
                value-format="yyyy-MM-dd HH:mm:ss" clearable style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障代通时间:" prop="lastClearTime" :rules="{
              required: true,
              message: '请选择内容',
            }">
              <el-date-picker v-model="backSingleForm.lastClearTime" type="datetime" placeholder="请选择时间"
                value-format="yyyy-MM-dd HH:mm:ss" clearable style="width: 100%" @change="computerFaultGenerationAter"
                :picker-options="pickerOptions" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障代通历时:">
              {{ second2Time(backSingleForm.lastClearDuration) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障结束时间:" prop="faultEndTime">
              <el-date-picker v-model="backSingleForm.faultEndTime" type="datetime" placeholder="请选择时间"
                value-format="yyyy-MM-dd HH:mm:ss" clearable style="width: 100%" @change="computerFaultTreatmentTime"
                :picker-options="pickerOptions" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障处理历时:" required>
              {{ second2Time(this.backSingleForm.faultDuration) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障发生国家:" prop="faultHappenCountry" :rules="{
              required: true,
              message: '请输入内容',
            }">
              <el-input v-model.trim="backSingleForm.faultHappenCountry" placeholder="请输入内容"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障处理部门:" prop="dept" required>
              {{ backSingleForm.dept }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="处理人:" prop="person" required>
              {{ backSingleForm.person }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="挂起历时:" required>
              {{ second2Time(this.backSingleForm.suspendDuration) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障处理净历时:" required>
              {{ second2Time(this.backSingleForm.processDuration) }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item label="是否影响业务:" prop="isEffectBusiness" :rules="{
              required: true,
              message: '请选择内容',
            }">
              <el-radio-group v-model="backSingleForm.isEffectBusiness" style="width: 100%">
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="影响范围:" v-if="backSingleForm.isEffectBusiness == '1'" prop="effectRange">
              <el-input type="textarea" :rows="2" placeholder="请输入内容" v-model="backSingleForm.effectRange"
                style="width: 100%" show-word-limit maxlength="255">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card shadow="never" header="故障专业信息" :body-style="{ padding: '20px' }" style="margin-top: 20px"
        class="cus-card">
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item label="故障状态:" prop="faultStatus" :rules="{
              required: true,
              message: '请选择故障状态',
            }">
              <dict-select :value.sync="backSingleForm.faultStatus" :dictId="10017" style="width: 100%"
                placeholder="请选择内容" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="网络类型:" prop="networkType" :rules="{
              required: true,
              message: '请选择网络类型',
            }">
              <dict-select :value.sync="backSingleForm.networkType" :dictId="10063" style="width: 100%"
                placeholder="请选择内容" :notSelect="true" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障分类:" prop="faultCate" :rules="{
              required: true,
              message: '请选择故障分类',
            }">
              <dict-select :value.sync="backSingleForm.faultCate" :dictId="10055" style="width: 100%"
                @change="faultCateChange" :woId="common.woId" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20" v-if="backSingleForm.faultCate == '1'"
          key="faultReasonCate">
          <el-col :span="8">
            <el-form-item label="故障原因分类:" prop="faultReasonCate">
              <el-input v-model.trim="backSingleForm.faultReasonCate" style="width: 100%" placeholder="请输入内容"
                maxlength="100"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所在海缆:" prop="seaFiberName">
              <el-input v-model.trim="backSingleForm.seaFiberName" placeholder="请输入内容" style="width: 100%"
                maxlength="255">
                <el-button style="
                    background: #b50b14;
                    border-color: #b50b14;
                    color: #fff;
                    padding: 9px 20px;
                    top: -0.8px;
                    position: relative;
                  " slot="append" @click="selectSeaFiberName">
                  选择
                </el-button>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="海缆段:" prop="seaFiberSeg">
              <el-input v-model.trim="backSingleForm.seaFiberSeg" style="width: 100%" placeholder="请输入内容"
                maxlength="255"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="运营商名称:" prop="operatorName">
              <el-input v-model.trim="backSingleForm.operatorName" style="width: 100%" placeholder="请输入内容"
                maxlength="255"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="海缆类型:" prop="seaFiberType" :rules="{
              required: true,
              message: '请选择内容',
            }">
              <dict-select :value.sync="backSingleForm.seaFiberType" :dictId="10061" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障海域:" prop="seaFiberFaultArea" :rules="{
              required: true,
              message: '请选择内容',
            }">
              <dict-select :value.sync="backSingleForm.seaFiberFaultArea" :dictId="10062" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否完成修复:" prop="isRepairCompleted" :rules="{
              required: true,
              message: '请选择内容',
            }">
              <el-radio-group v-model="backSingleForm.isRepairCompleted" style="width: 100%">
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="关联海缆站点段:" prop="connectedSeaFiberStationSection">
              <el-input v-model.trim="backSingleForm.connectedSeaFiberStationSection" style="width: 100%"
                placeholder="请输入内容" maxlength="255"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否超时:">
              {{ backSingleForm.isOverTimeShow }}
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="故障原因描述:" prop="falutReasonDesc">
              <el-input maxlength="500" show-word-limit type="textarea" :autosize="{ minRows: 3, maxRows: 6 }" :rows="2"
                v-model.trim="backSingleForm.falutReasonDesc" style="width: 100%">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item v-if="isRouteAdjustShow" label="是否路由调整:" prop="isRouteAdjust">
              <el-radio-group v-model="backSingleForm.isRouteAdjust" @change="isRouteChange">
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item v-if="backSingleForm.isRouteAdjust == 1" label="路由调整工单编号:">
              <el-link :underline="false" @click="toRoutePage" type="primary" style="color: #409eff">
                {{ backSingleForm.routeNo }}</el-link>
            </el-form-item>
            <!-- <form
              id="sub__route"
              name="sub__route"
              hidden="true"
              method="get"
              target="_blank"
            >
              <input type="hidden" name="route" id="route" />
            </form> -->
          </el-col>
        </el-row>
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20" v-if="backSingleForm.faultCate == '2'"
          key="faultReasonCate">
          <el-col :span="8">
            <el-form-item label="故障原因分类:" prop="faultReasonCate">
              <el-input v-model.trim="backSingleForm.faultReasonCate" style="width: 100%" placeholder="请输入内容"
                maxlength="100"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所在陆缆:" prop="landFiberName">
              <el-input v-model.trim="backSingleForm.landFiberName" placeholder="请输入内容" style="width: 100%"
                maxlength="255">
                <!-- <el-button
                  style="
                    background: #b50b14;
                    border-color: #b50b14;
                    color: #fff;
                    padding: 9px 20px;
                    top: -0.8px;
                    position: relative;
                  "
                  slot="append"
                  @click="selectLandFiberName"
                >
                  选择
                </el-button> -->
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="陆缆段:" prop="landFiberSeg">
              <el-input v-model.trim="backSingleForm.landFiberSeg" style="width: 100%" placeholder="请输入内容"
                maxlength="255"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="供应商:" prop="provider">
              <el-input v-model.trim="backSingleForm.provider" style="width: 100%" placeholder="请输入内容"
                maxlength="100"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否完成修复:" prop="isRepairCompleted" :rules="{
              required: true,
              message: '请选择内容',
            }">
              <el-radio-group v-model="backSingleForm.isRepairCompleted" style="width: 100%">
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否超时:">
              {{ backSingleForm.isOverTimeShow }}
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="故障原因描述:" prop="falutReasonDesc">
              <el-input maxlength="500" show-word-limit type="textarea" :rows="2" :autosize="{ minRows: 3, maxRows: 6 }"
                v-model.trim="backSingleForm.falutReasonDesc" style="width: 100%">
              </el-input>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item v-if="isRouteAdjustShow" label="是否路由调整:" prop="isRouteAdjust">
              <el-radio-group v-model="backSingleForm.isRouteAdjust" @change="isRouteChange">
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item v-if="backSingleForm.isRouteAdjust == 1" label="路由调整工单编号:">
              <el-link target="_blank" :underline="false" type="primary" style="color: #409eff">
                {{ backSingleForm.routeNo }}</el-link>
            </el-form-item>
            <!-- <form
              id="sub__route"
              name="sub__route"
              hidden="true"
              method="get"
              target="_blank"
            >
              <input type="hidden" name="route" id="route" />
            </form> -->
          </el-col>
        </el-row>
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20" v-if="backSingleForm.faultCate == '3'"
          key="faultCateTwo">
          <el-col :span="8">
            <el-form-item label="故障原因分类:" prop="faultReasonCate">
              <el-input v-model.trim="backSingleForm.faultReasonCate" style="width: 100%" placeholder="请输入内容"
                maxlength="100"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障国家:" prop="faultCountry">
              <el-input placeholder="请输入内容" v-model.trim="backSingleForm.faultCountry" style="width: 100%"
                maxlength="100">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="运营商名称:" prop="operatorName">
              <el-input placeholder="请输入内容" v-model.trim="backSingleForm.operatorName" style="width: 100%"
                maxlength="255">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否超时:">
              {{ backSingleForm.isOverTimeShow }}
            </el-form-item></el-col>
          <el-col :span="24">
            <el-form-item label="故障原因描述:" prop="falutReasonDesc">
              <el-input maxlength="500" show-word-limit type="textarea" :rows="2" :autosize="{ minRows: 3, maxRows: 6 }"
                v-model.trim="backSingleForm.falutReasonDesc" style="width: 100%">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item v-if="isRouteAdjustShow" label="是否路由调整:" prop="isRouteAdjust">
              <el-radio-group v-model="backSingleForm.isRouteAdjust" @change="isRouteChange">
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item v-if="backSingleForm.isRouteAdjust == 1" label="路由调整工单编号:">
              <el-link target="_blank" :underline="false" @click="toRoutePage" type="primary" style="color: #409eff">
                {{ backSingleForm.routeNo }}</el-link>
            </el-form-item>
            <!-- <form
              id="sub__route"
              name="sub__route"
              hidden="true"
              method="get"
              target="_blank"
            >
              <input type="hidden" name="route" id="route" />
            </form> -->
          </el-col>
        </el-row>
      </el-card>
    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: center">
      <el-button type="primary" @click="handleSubmit('backSingleForm')"
        v-loading.fullscreen.lock="backSingleFullscreenLoading">提 交</el-button>
      <!-- <el-button type="primary" @click="nextStepEvaluation()">下一步</el-button> -->
      <el-button @click="onReset">重 置</el-button>
    </div>

    <el-dialog width="620px" title="满意度评价" :visible.sync="evaluationDialogVisible" :close-on-click-modal="false"
      :destroy-on-close="true" append-to-body>
      <el-form ref="evaluation" :model="evaluation">
        <el-form-item label="派单准确度:" label-width="90px">
          <el-rate v-model="evaluation.orderAccuracy" :colors="colors"
            style="display: inline-block; vertical-align: text-bottom">
          </el-rate>
        </el-form-item>
        <el-form-item label="诊断准确度:" label-width="90px">
          <el-rate v-model="evaluation.diagnosticrAccuracy" :colors="colors"
            style="display: inline-block; vertical-align: text-bottom">
          </el-rate>
        </el-form-item>
        <el-form-item v-if="
          evaluation.orderAccuracy <= 3 || evaluation.diagnosticrAccuracy <= 3
        " label="反馈问题:" label-width="90px" prop="feedbackProblemCheckList" :rules="[
            {
              required:
                evaluation.orderAccuracy > 3 &&
                  evaluation.diagnosticrAccuracy > 3
                  ? false
                  : true,
              message: '请选择要反馈的问题',
              trigger: 'blur',
            },
          ]">
          <el-checkbox-group v-model="evaluation.feedbackProblemCheckList" @change="feedbackChange">
            <el-checkbox label="派单超时"></el-checkbox>
            <el-checkbox label="工单基础信息错误"></el-checkbox>
            <el-checkbox label="诊断信息不完整"></el-checkbox>
            <el-checkbox label="诊断信息错误"></el-checkbox>
            <el-checkbox label="其他"></el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item v-if="
          viewsOnContentShow &&
          (evaluation.orderAccuracy <= 3 ||
            evaluation.diagnosticrAccuracy <= 3)
        " prop="viewsOnContent">
          <el-input type="textarea" :rows="3" placeholder="为了下次给您更好的服务，请留下宝贵意见，对您和我们都很重要"
            v-model="evaluation.viewsOnContent">
          </el-input>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button type="primary" @click="handleSubmit('evaluation')"
          v-loading.fullscreen.lock="backSingleFullscreenLoading">提 交</el-button>
        <el-button @click="evaluationDialogVisible = false">上一步</el-button>
      </span>
    </el-dialog>
    <el-dialog width="620px" title="海缆名称" :visible.sync="seaDialogVisible" :close-on-click-modal="false"
      :destroy-on-close="true" append-to-body top="5vh">
      <div>
        <el-checkbox-group v-model="seaCheckBox" size="small">
          <el-checkbox v-for="(item, index) in seaCheckBoxArr" :key="index" :label="item.dictName" border
            style="width: 140px; margin-top: 10px; margin-left: 0px"></el-checkbox>
        </el-checkbox-group>
      </div>
      <span slot="footer">
        <el-button type="primary" @click="seaCheckBoxDetermine">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog width="620px" title="陆缆名称" :visible.sync="landDialogVisible" :close-on-click-modal="false"
      :destroy-on-close="true" append-to-body top="5vh">
      <div>
        <el-checkbox-group v-model="landCheckBox" size="small">
          <el-checkbox v-for="(item, index) in landCheckBoxArr" :key="index" :label="item.dictName" border
            style="width: 140px; margin-top: 10px; margin-left: 0px"></el-checkbox>
        </el-checkbox-group>
      </div>
      <span slot="footer">
        <el-button type="primary" @click="landCheckBoxDetermine">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import moment from "moment";
import { mapGetters } from "vuex";
import DictSelect from "../../components/DictSelect.vue";
import { apiBackSingleInternation } from "../api/CommonApi";
import { apiDict, apiGetOrgInfo } from "../../api/CommonApi";
import { mixin } from "../../../../../../mixins";
export default {
  name: "BackSingleInternation",
  props: {
    common: Object,
    timing: Object,
  },
  components: { DictSelect },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  mixins: [mixin],
  data() {
    var validLastClearTime = (rule, value, callback) => {
      if (this.backSingleForm.lastClearTime) {
        let seconds3 = moment(
          this.backSingleForm.lastClearTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(moment(new Date(), "YYYY-MM-DD HH:mm:ss"), "seconds");
        let seconds4 = moment(
          this.backSingleForm.lastClearTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.common.failureTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        if (seconds3 > 0 || seconds4 <= 0) {
          callback(
            new Error(
              "当前时间>=故障代通时间>=故障发生时间，请重新检查后选择正确时间"
            )
          );
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    var validFaultEndTime = (rule, value, callback) => {
      if (value === "" || value === null) {
        callback(new Error("请选择故障结束时间"));
      } else {
        if (this.backSingleForm.faultEndTime) {
          // let seconds3 = moment(
          //   this.backSingleForm.faultEndTime,
          //   "YYYY-MM-DD HH:mm:ss"
          // ).diff(moment(new Date(), "YYYY-MM-DD HH:mm:ss"), "seconds");
          // let seconds4 = moment(
          //   this.backSingleForm.faultEndTime,
          //   "YYYY-MM-DD HH:mm:ss"
          // ).diff(
          //   moment(this.common.failureTime, "YYYY-MM-DD HH:mm:ss"),
          //   "seconds"
          // );
          // if (seconds3 > 0 || seconds4 <= 0) {
          //   callback(
          //     new Error(
          //       "当前时间>=故障结束时间>=故障发生时间，请重新检查后选择正确时间"
          //     )
          //   );
          // } else {
          //   callback();
          // }
          let secondsProcessingDuration = moment(
            this.backSingleForm.faultEndTime,
            "YYYY-MM-DD HH:mm:ss"
          ).diff(
            moment(this.common.failureTime, "YYYY-MM-DD HH:mm:ss"),
            "seconds"
          );
          let secondsProcessingDurationJing =
            secondsProcessingDuration - this.backSingleForm.suspendDuration;
          if (secondsProcessingDuration <= 0) {
            callback(new Error("故障处理历时<=0，请重新检查后选择正确时间！"));
          } else if (secondsProcessingDurationJing <= 0) {
            callback(
              new Error("故障处理净历时<=0，请重新检查后选择正确时间！")
            );
          } else {
            callback();
          }
        } else {
          callback();
        }
      }
    };
    return {
      backSingleForm: {
        woId: null,
        workItemId: null,
        processInstId: null,
        processDefId: null,
        professionalType: null,
        alarmCreateTime: null,
        sheetCreateTime: null,
        lastClearTime: null,
        faultKnowTime: null, //故障得知时间
        lastClearDuration: 0, //故障代通历时
        faultEndTime: null,
        faultDuration: 0, //故障处理历时
        faultHappenCountry: null,
        dept: null,
        person: null,
        suspendDuration: 0, //挂起历时 单位秒
        processDuration: 0, //故障处理净历时 单位秒
        isEffectBusiness: null,
        effectRange: null,
        //海陆缆故障
        faultStatus: null,
        networkType: null,
        faultCate: null,
        faultReasonCate: null,
        seaFiberName: null,
        seaFiberSeg: null,
        landFiberName: null,
        landFiberSeg: null,
        operatorName: null,
        seaFiberType: null,
        seaFiberFaultArea: null,
        isOverTime: 0,
        isOverTimeShow: "否",
        falutReasonDesc:
          this.common.faultCauseDescription ||
          "样例：距xx机房xxkm处，光缆因xxx（原因）中断。 处理方式：xxxx （熔接或者倒带后）恢复。",
        //设备故障
        faultCountry: null,
        actionName: "",
        isRepairCompleted: null,
        connectedSeaFiberStationSection: "",
        provider: "",
        remainAcceptTime: "",
        isRouteAdjust: "0", //是否路由调整  1116//is_route_adjust
        routeProcessInstId: null, //路由调整流程实例Id
        routeNo: "", //路由调整单号//routeOrderNumber
        routeOrderTitle: null, //路由调整主题
      },
      backSingleFullscreenLoading: false,

      evaluationDialogVisible: false,
      evaluation: {
        orderAccuracy: 5,
        diagnosticrAccuracy: 5,
        viewsOnContent: null,
        feedbackProblemCheckList: [],
      },
      colors: ["#99A9BF", "#F7BA2A", "#FF9900"], // 等同于 { 2: '#99A9BF', 4: { value: '#F7BA2A', excluded: true }, 5: '#FF9900' }
      //当前人的部门
      userData: null,
      backSingleFormRule: {
        lastClearTime: [{ validator: validLastClearTime, trigger: "blur" }],
        faultEndTime: [{ validator: validFaultEndTime, required: true }],
        faultReasonCate: [
          {
            required: true,
            message: "请输入内容",
          },
          {
            validator: this.checkLength,
            max: 100,
            form: "backSingleForm",
            messsage: "已超过填写字数上限",
          },
        ],
        seaFiberName: [
          {
            required: true,
            message: "请输入内容",
          },
          {
            validator: this.checkLength,
            max: 255,
            form: "backSingleForm",
            messsage: "已超过填写字数上限",
          },
        ],
        seaFiberSeg: [
          {
            required: true,
            message: "请输入内容",
          },
          {
            validator: this.checkLength,
            max: 255,
            form: "backSingleForm",
            messsage: "已超过填写字数上限",
          },
        ],
        operatorName: [
          {
            required: true,
            message: "请输入内容",
          },
          {
            validator: this.checkLength,
            max: 255,
            form: "backSingleForm",
            messsage: "已超过填写字数上限",
          },
        ],
        connectedSeaFiberStationSection: [
          {
            required: true,
            message: "请输入内容",
          },
          {
            validator: this.checkLength,
            max: 255,
            form: "backSingleForm",
            messsage: "已超过填写字数上限",
          },
        ],
        landFiberName: [
          {
            required: true,
            message: "请输入内容",
          },
          {
            validator: this.checkLength,
            max: 255,
            form: "backSingleForm",
            messsage: "已超过填写字数上限",
          },
        ],
        landFiberSeg: [
          {
            required: true,
            message: "请输入内容",
          },
          {
            validator: this.checkLength,
            max: 255,
            form: "backSingleForm",
            messsage: "已超过填写字数上限",
          },
        ],
        provider: [
          {
            required: true,
            message: "请输入内容",
          },
          {
            validator: this.checkLength,
            max: 100,
            form: "backSingleForm",
            messsage: "已超过填写字数上限",
          },
        ],
        faultCountry: [
          {
            required: true,
            message: "请输入内容",
          },
          {
            validator: this.checkLength,
            max: 100,
            form: "backSingleForm",
            messsage: "已超过填写字数上限",
          },
        ],
        effectRange: [
          {
            required: true,
            message: "请填写影响范围",
            trigger: "blur",
          },
          {
            validator: this.checkLength,
            max: 255,
            form: "backSingleForm",
            messsage: "已超过填写字数上限",
          },
        ],
        falutReasonDesc: [
          {
            required: true,
            message: "请填写故障原因描述",
          },
          {
            validator: this.checkLength,
            max: 500,
            form: "backSingleForm",
            messsage: "已超过填写字数上限",
          },
        ],
      },
      viewsOnContentShow: false,
      seaCheckBox: [],
      seaCheckBoxArr: [],
      landCheckBox: [],
      landCheckBoxArr: [],
      seaDialogVisible: false,
      landDialogVisible: false,

      timeRange: "00:00:00 - 23:59:59",
      createTimeDate: "2022-10-20",
      pickerOptions: {
        disabledDate: this.disabledDate,
        selectableRange: this.timeRange,
      },
      isRouteNoShow: true,
      isRouteAdjustShow: true,
    };
  },
  watch: {
    "backSingleForm.lastClearTime": {
      handler(val) {
        let valDate = val.split(" ")[0];
        let originDate = this.common.failureTime.split(" ")[0];
        let now = new Date();
        now.setMinutes(now.getMinutes() + 1, now.getSeconds(), 0);
        valDate = valDate.replace(/\-/g, "/");
        originDate = originDate.replace(/\-/g, "/");
        let year = now.getFullYear();
        let month = now.getMonth() + 1;
        let date = now.getDate();
        let nowDate =
          year + "/" + this.addZero(month) + "/" + this.addZero(date);
        let valDateUnix = Date.parse(valDate);
        let originDateUnix = Date.parse(originDate);
        let nowDateUnix = Date.parse(nowDate);

        let array = this.backSingleForm.alarmCreateTime.split(" ");
        let createTime = array[1];
        let hour = now.getHours();
        let minute = now.getMinutes();
        let second = now.getSeconds();
        let nowTime =
          this.addZero(hour) +
          ":" +
          this.addZero(minute) +
          ":" +
          this.addZero(second);
        if (valDateUnix == originDateUnix && originDateUnix == nowDateUnix) {
          this.timeRange = createTime + " - " + nowTime;
          this.pickerOptions.selectableRange = this.timeRange;
        } else if (
          valDateUnix == originDateUnix &&
          originDateUnix < nowDateUnix
        ) {
          this.timeRange = createTime + " - 23:59:59";
          this.pickerOptions.selectableRange = this.timeRange;
        } else if (valDateUnix > originDateUnix && valDateUnix < nowDateUnix) {
          this.timeRange = "00:00:00 - 23:59:59";
          this.pickerOptions.selectableRange = this.timeRange;
        } else if (valDateUnix > originDateUnix && valDateUnix == nowDateUnix) {
          this.timeRange = "00:00:00 - " + nowTime;
          this.pickerOptions.selectableRange = this.timeRange;
        }
        console.log(this.timeRange);
      },
      deep: true,
    },
    "backSingleForm.faultEndTime": {
      handler(val) {
        let valDate = val.split(" ")[0];
        let originDate = this.common.failureTime.split(" ")[0];
        let now = new Date();
        now.setMinutes(now.getMinutes() + 1, now.getSeconds(), 0);
        valDate = valDate.replace(/\-/g, "/");
        originDate = originDate.replace(/\-/g, "/");
        let year = now.getFullYear();
        let month = now.getMonth() + 1;
        let date = now.getDate();
        let nowDate =
          year + "/" + this.addZero(month) + "/" + this.addZero(date);
        let valDateUnix = Date.parse(valDate);
        let originDateUnix = Date.parse(originDate);
        let nowDateUnix = Date.parse(nowDate);

        let array = this.backSingleForm.alarmCreateTime.split(" ");
        let createTime = array[1];
        let hour = now.getHours();
        let minute = now.getMinutes();
        let second = now.getSeconds();
        let nowTime =
          this.addZero(hour) +
          ":" +
          this.addZero(minute) +
          ":" +
          this.addZero(second);
        if (valDateUnix == originDateUnix && originDateUnix == nowDateUnix) {
          this.timeRange = createTime + " - " + nowTime;
          this.pickerOptions.selectableRange = this.timeRange;
        } else if (
          valDateUnix == originDateUnix &&
          originDateUnix < nowDateUnix
        ) {
          this.timeRange = createTime + " - 23:59:59";
          this.pickerOptions.selectableRange = this.timeRange;
        } else if (valDateUnix > originDateUnix && valDateUnix < nowDateUnix) {
          this.timeRange = "00:00:00 - 23:59:59";
          this.pickerOptions.selectableRange = this.timeRange;
        } else if (valDateUnix > originDateUnix && valDateUnix == nowDateUnix) {
          this.timeRange = "00:00:00 - " + nowTime;
          this.pickerOptions.selectableRange = this.timeRange;
        }
      },
      deep: true,
    },
  },
  mounted() {
    this.backSingleForm.alarmCreateTime = this.common.failureTime;
    this.backSingleForm.sheetCreateTime = this.common.failureInformTime;
    this.backSingleForm.person = this.userInfo.realName;
    this.backSingleForm.workItemId = this.common.workItemId;
    this.backSingleForm.woId = this.common.woId;
    this.backSingleForm.processInstId = this.common.processInstId;
    this.backSingleForm.processDefId = this.common.processDefId;
    this.backSingleForm.actionName = this.common.actionName;
    this.backSingleForm.professionalType = this.common.professionalType + "";
    this.backSingleForm.networkType =
      this.common.networkType == "国际" ? "1" : "2";
    this.backSingleForm.suspendDuration = this.common.hangOver;
    this.backSingleForm.remainAcceptTime = this.common.remainAcceptTime;
    this.userData = JSON.parse(this.userInfo.attr2);
    this.backSingleForm.dept = this.userData.orgInfo.fullOrgName;
    this.getSeaOptions();
    let array = this.backSingleForm.alarmCreateTime.split(" ");
    this.createTimeDate = array[0];
  },
  methods: {
    //时间对比
    disabledDate(time) {
      let now = moment(this.createTimeDate);
      let a = moment(now).subtract(1, "days");
      let today = moment(a).valueOf();
      return time.getTime() <= today || time.getTime() - 8.64e6 >= Date.now();
    },
    addZero(s) {
      return s < 10 ? "0" + s : s;
    },
    getSeaOptions() {
      let param = {
        dictTypeCode: "10057",
      };
      apiDict(param)
        .then(res => {
          if (res.code == "200") {
            this.seaCheckBoxArr = res?.data ?? [];
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    getLandOptions() {
      let param = {
        dictTypeCode: "10057",
      };
      apiDict(param)
        .then(res => {
          if (res.code == "200") {
            this.landCheckBoxArr = res?.data ?? [];
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    getOrgInfo() {
      apiGetOrgInfo()
        .then(res => {
          if (res.status == "0") {
            this.backSingleForm.dept = res?.data?.orgInfo?.fullOrgName ?? "";
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    computerFaultGenerationAter() {
      if (this.backSingleForm.lastClearTime) {
        let days = moment(
          this.backSingleForm.lastClearTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.backSingleForm.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        this.backSingleForm.lastClearDuration = days;
      } else {
        this.backSingleForm.lastClearDuration = 0;
      }
    },
    second2Time(days) {
      // return this.showTime(Math.abs(days));
      return this.showTime(days);
    },
    computerFaultTreatmentTime() {
      let days = moment(
        this.backSingleForm.faultEndTime,
        "YYYY-MM-DD HH:mm:ss"
      ).diff(
        moment(this.backSingleForm.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
        "seconds"
      );
      this.backSingleForm.faultDuration = days;

      //故障处理净历时  （故障结束时间-故障发生时间）-挂起历时(秒) 换算成-天-小时-分钟-秒
      if (this.backSingleForm.suspendDuration == 0) {
        this.backSingleForm.processDuration = this.backSingleForm.faultDuration;
      } else {
        let seconds = moment(
          this.backSingleForm.faultEndTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.backSingleForm.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        this.backSingleForm.processDuration =
          seconds - this.backSingleForm.suspendDuration;
      }
    },
    //挂起历时
    computerSuspendDuration() {
      if (this.timing.hangTime != "" && null != this.timing.hangTime) {
        if (
          this.timing.liftHangTime != "" &&
          null != this.timing.liftHangTime
        ) {
          let seconds = moment(
            this.timing.liftHangTime,
            "YYYY-MM-DD HH:mm:ss"
          ).diff(
            moment(this.timing.hangTime, "YYYY-MM-DD HH:mm:ss"),
            "seconds"
          );
          this.backSingleForm.suspendDuration = seconds;
        } else {
          this.backSingleForm.suspendDuration = 0;
        }
      } else {
        this.backSingleForm.suspendDuration = 0;
      }
    },
    // //下一步评价
    // nextStepEvaluation() {
    //   this.$refs.backSingleForm.validate(valid => {
    //     if (valid) {
    //       this.evaluationDialogVisible = true;
    //     } else {
    //       return false;
    //     }
    //   });
    // },
    handleSubmit(formName) {
      this.$refs[formName].validate((valid, a) => {
        if (this.uncheck(a)) {
          let self = this;
          this.backSingleFullscreenLoading = true;
          // let formData = new FormData();
          // formData.append("param1", JSON.stringify(self.backSingleForm));
          // let evaluateParam = {
          //   woId: this.common.woId,
          //   sheetCreateTime: this.backSingleForm.sheetCreateTime,
          //   sendAccuracy: this.evaluation.orderAccuracy,
          //   diagnoseAccuracy: this.evaluation.diagnosticrAccuracy,
          //   evaluateContent: this.evaluation.viewsOnContent,
          //   problemClass:
          //     this.evaluation.feedbackProblemCheckList.length > 0
          //       ? this.evaluation.feedbackProblemCheckList.join(",")
          //       : "",
          // };
          // formData.append("evaluateParam", JSON.stringify(evaluateParam));
          // 当前剩余受理时限
          let remainAcceptTime = this.common.acceptTimeLimit - Math.floor((new Date() - new Date(this.backSingleForm.sheetCreateTime)) / 60000);
          if (remainAcceptTime <= 0) {
            remainAcceptTime = -1;
          }
          self.backSingleForm.remainAcceptTime = remainAcceptTime;
          apiBackSingleInternation({
            param1: JSON.stringify(self.backSingleForm),
          })
            .then(res => {
              if (res.status == "0") {
                this.$message.success("提交返单成功");
                //   this.onReset();
                this.$emit("closeBackSingleDialog", res.data);
              } else {
                this.$message.error("提交返单失败");
              }
              this.backSingleFullscreenLoading = false;
            })
            .catch(error => {
              console.log(error);
              this.$message.error("提交返单失败");
              this.backSingleFullscreenLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    faultCateChange() {
      this.resetFaultCateChange();
    },
    resetFaultCateChange() {
      this.backSingleForm.faultReasonCate = null;
      this.backSingleForm.seaFiberName = null;
      this.backSingleForm.seaFiberSeg = null;
      this.backSingleForm.landFiberName = null;
      this.backSingleForm.landFiberSeg = null;
      this.backSingleForm.operatorName = null;
      this.backSingleForm.seaFiberType = null;
      this.backSingleForm.seaFiberFaultArea = null;
      this.backSingleForm.falutReasonDesc =
        this.common.faultCauseDescription ||
        "样例：距xx机房xxkm处，光缆因xxx（原因）中断。 处理方式：xxxx （熔接或者倒带后）恢复。";
      this.backSingleForm.faultCountry = null;
      this.backSingleForm.provider = null;
      this.backSingleForm.isRepairCompleted = null;
      this.backSingleForm.connectedSeaFiberStationSection = null;
    },
    onReset() {
      this.backSingleForm = {
        ...this.$options.data,
        alarmCreateTime: this.common.failureTime,
        sheetCreateTime: this.common.failureInformTime,
        woId: this.common.woId,
        workItemId: this.common.workItemId,
        processInstId: this.common.processInstId,
        processDefId: this.common.processDefId,
        person: this.userInfo.realName,
        dept: this.userData.orgInfo.fullOrgName,
        professionalType: "3",
        lastClearDuration: 0, //故障代通历时
        faultDuration: 0, //故障处理净历时
        suspendDuration: this.common.hangOver, //挂起历时 单位秒
        processDuration: 0, //故障处理净历时 单位秒
        networkType: this.common.networkType == "国际" ? "1" : "2",
        isOverTime: 0,
        isOverTimeShow: "否",
        falutReasonDesc:
          this.common.faultCauseDescription ||
          "样例：距xx机房xxkm处，光缆因xxx（原因）中断。 处理方式：xxxx （熔接或者倒带后）恢复。",
        actionName: this.common.actionName,
        faultReasonCate: null,
        seaFiberName: null,
        seaFiberSeg: null,
        landFiberName: null,
        landFiberSeg: null,
        operatorName: null,
        seaFiberType: null,
        seaFiberFaultArea: null,
        faultCountry: null,
        faultEndTime: null,
        lastClearTime: null,
        provider: null,
        isRepairCompleted: null,
        connectedSeaFiberStationSection: null,
      };
    },
    onResetEvaluation() {
      this.evaluation = {
        ...this.$options.data,
      };
    },
    showTime(val) {
      let isPositiveNumber = true;
      let valStr = val + "";
      if (valStr.indexOf("-") != -1) {
        //负数
        val = Math.abs(val);
        isPositiveNumber = false;
      }
      if (val) {
        if (val == 0) return "0秒";
        var time = "";
        var second = val % 60;
        var minute = parseInt(parseInt(val) / 60) % 60;
        time = second + "秒";
        if (minute >= 1) {
          time = minute + "分" + second + "秒";
        }
        var hour = parseInt(parseInt(val / 60) / 60) % 24;
        if (hour >= 1) {
          time = hour + "小时" + minute + "分" + second + "秒";
        }
        var day = parseInt(parseInt(parseInt(val / 60) / 60) / 24);
        if (day >= 1) {
          time = day + "天" + hour + "小时" + minute + "分" + second + "秒";
        }
        if (!isPositiveNumber) {
          time = "-" + time;
        }
        return time;
      } else {
        return "0秒";
      }
    },
    feedbackChange(val) {
      if (val.length > 0) {
        if (val.join(",").indexOf("其他") != -1) {
          this.viewsOnContentShow = true;
        } else {
          this.viewsOnContentShow = false;
        }
      }
    },
    selectSeaFiberName() {
      this.seaDialogVisible = true;
    },
    selectLandFiberName() {
      this.landDialogVisible = true;
    },
    seaCheckBoxDetermine() {
      this.$set(
        this.backSingleForm,
        "seaFiberName",
        this.seaCheckBox.join(",")
      );
      this.seaDialogVisible = false;
    },
    landCheckBoxDetermine() {
      this.$set(
        this.backSingleForm,
        "landFiberName",
        this.landCheckBox.join(",")
      );
      this.landDialogVisible = false;
    },
    isRouteChange() {
      if (this.backSingleForm.isRouteAdjust == "1") {
        this.isRouteTrue();
      } else {
        this.backSingleForm.isRouteAdjust = "0";
        this.backSingleForm.routeNo = "";
      }
    },
    //路由调整跳转拟稿
    isRouteTrue() {
      let routeURI = window.location.protocol + "//" + window.location.host;
      // let routeURI = "http://************:8088"; //测试环境地址
      // let routeURI = "http://************"; //生产环境地址
      routeURI += "/EOM_LIFE_ADJUST/#/transfer/draftform";
      routeURI +=
        "?hideHeader=1&hideTagsNav=1&actonShow=true&routeSource=网络故障3.0";
      routeURI +=
        "&influenceCategory=国际&otherSystemNo=" +
        this.common.sheetNo +
        "&otherSystemId=" +
        this.common.woId +
        "&globalUniqueID=" +
        sessionStorage.getItem("globalUniqueID");
      // let param = {
      //   routeSource: "网络故障3.0",
      //   influenceCategory: "国际",
      //   otherSystemNo: this.common.sheetNo,
      //   otherSystemId: this.common.woId,
      //   globalUniqueID: sessionStorage.getItem("globalUniqueID"),
      // };
      // this.$nextTick(() => {
      //   document.querySelector("#sub__route").action = routeURI;
      //   document.querySelector("#route").val = JSON.stringify(param);
      //   document.querySelector("#sub__route").submit();
      // });
      window.open(routeURI);
    },
    //路由跳转回调方法
    setParentObjectData() {
      window["_setParentObjectData"] = value => {
        let form = this.backSingleForm;
        if (value.success) {
          //"success": true,
          // "jobTitle": "中国联通总部-中国联通总部-传输",
          // "processInstID": "1182565",
          // "jobCode": "CSLYTZAPP-20221019-0000"
          form.routeProcessInstId = value.processInstID;
          form.routeNo = value.jobCode;
          form.routeOrderTitle = value.jobTitle;
        }
      };
    },
    toRoutePage() {
      let url = window.location.protocol + "//" + window.location.host;
      // let url = "http://************:8088"; //测试环境地址
      // let url = "http://************"; //生产环境地址
      if (this.backSingleForm.routeProcessInstId != null) {
        url +=
          "/ibpm-ui/#/contentTo?content=Y&needi=Y&no=detail&hideHeader=1&reqFrom=handle&processInstId=" +
          this.backSingleForm.routeProcessInstId +
          "&jobCode=" +
          this.backSingleForm.routeNo +
          "&globalUniqueID=" +
          sessionStorage.getItem("globalUniqueID");
      } else {
        url +=
          "/EOM_LIFE_ADJUST/#/transfer/draftform?hideHeader=1&hideTagsNav=1&actonShow=false&jobcode=" +
          this.backSingleForm.routeNo;
      }
      window.open(url);
    },
  },
};
</script>
<style lang="scss" scoped>
.back-single-internation {
  max-height: calc(90vh - 150px);
  min-height: 400px;
  margin: 0 4px;
  padding-left: 4px;
  padding-right: 8px;
  overflow-y: auto;

  .cus-card ::v-deep .el-card__header {
    padding: 11px 24px 10px;
    font-weight: 400;

    @include themify {
      background-color: themed("$--background-color-base");
    }
  }

  .fileName_style {
    margin-right: 3px;
    vertical-align: middle;

    div {
      display: inline-block;
      max-width: 120px;
      vertical-align: top;
    }
  }
}
</style>
