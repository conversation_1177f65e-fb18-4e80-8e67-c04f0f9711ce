<template>
  <el-card
    shadow="always"
    :body-style="{ padding: '10px 24px' }"
    class="business-impact-details card"
  >
    <div
      class="header clearfix"
      :style="{ marginBottom: showDetail ? '' : '0px' }"
    >
      <span class="header-title" :class="{ fold: !showDetail }"
        >影响业务详情
        <el-radio-group v-show="showDetail" v-model="radio" size="mini">
          <el-radio-button label="1">影响业务</el-radio-button>
          <el-radio-button label="2">影响系统</el-radio-button>
        </el-radio-group>
      </span>
      <div class="header-right">
        <el-input
          v-show="showDetail"
          v-if="radio == '1'"
          v-model="business.form.keyWord"
          placeholder="请输入电路编号"
          clearable
          class="search-input"
          @keyup.enter.native="onQuery(true)"
        >
          <!-- <el-button
            slot="append"
            icon="el-icon-search"
            @click="onQuery"
          ></el-button> -->
        </el-input>
        <el-input
          v-show="showDetail"
          v-else
          v-model="trans.form.keyWord"
          placeholder="请输入SDH或WDM系统名称"
          clearable
          class="search-input"
          @keyup.enter.native="onQuery(true)"
        >
          <!-- <el-button
            slot="append"
            icon="el-icon-search"
            @click="onQuery"
          ></el-button> -->
        </el-input>
        <el-button
          v-show="showDetail"
          type="primary"
          size="mini"
          @click="onQuery(true)"
          >刷新</el-button
        >
        <el-button
          v-show="showDetail"
          type="primary"
          size="mini"
          @click="onExport"
          >导出</el-button
        >
        <el-button
          type="text"
          size="mini"
          :icon="!showDetail ? 'el-icon-arrow-right' : 'el-icon-arrow-down'"
          style="font-weight: 700"
          @click="showDetail = !showDetail"
        >
          <!-- {{ showDetail ? "收起" : "展开" }} -->
        </el-button>
      </div>
    </div>
    <div v-show="showDetail" class="">
      <div v-show="radio == 1">
        <el-table
          :data="circuitType != '电路故障' ? business.data : currentPageData"
          border
          stripe
          max-height="240px"
        >
          <el-table-column
            type="index"
            label="序号"
            width="50"
            :index="
              index =>
                (business.form.pageIndex - 1) * business.form.pageSize +
                index +
                1
            "
          ></el-table-column>
          <el-table-column
            label="电路编号"
            prop="circuitId"
            min-width="160"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            label="客户名称"
            prop="clientName"
            min-width="140"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            label="电路速率"
            prop="brandwidth"
            :width="circuitType != '电路故障'?120:200"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            label="A端省份"
            prop="aprovince"
            :width="circuitType != '电路故障'?120:200"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            label="Z端省份"
            prop="zprovince"
            :width="circuitType != '电路故障'?120:200"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            label="电路状态"
            prop="circuitStatus"
            :width="circuitType != '电路故障'?120:200"
            show-overflow-tooltip
          ></el-table-column>

          <!-- isCircuitType -->
          <template v-if="isCircuitType">
            <el-table-column
              label="SDH传输段"
              prop="sdhSegment"
              min-width="140"
              show-overflow-tooltip
            ></el-table-column>
            <el-table-column
              label="WDM传输段"
              prop="wdmSegment"
              min-width="160"
              show-overflow-tooltip
            ></el-table-column>
            <el-table-column
              label="传输系统"
              prop="transSystem"
              min-width="160"
              show-overflow-tooltip
            ></el-table-column>
            <el-table-column
              label="是否冲突"
              prop="isConflict"
              width="120"
              show-overflow-tooltip
            >
              <template v-slot="{ row }">{{
                ["", "是", "否"][row.isConflict]
              }}</template>
            </el-table-column>
            <el-table-column
              label="是否重保"
              prop="effectInsurance"
              width="120"
              show-overflow-tooltip
            >
              <template v-slot="{ row }">{{
                ["", "是", "否"][row.effectInsurance]
              }}</template>
            </el-table-column>
          </template>
        </el-table>
        <pagination
          v-if="circuitType != '电路故障'"
          :total="business.total"
          :page.sync="business.form.pageIndex"
          :limit.sync="business.form.pageSize"
          layout="->, total, sizes, prev, pager, next"
          @change="getBusinessList"
        />
        <div
          v-else
          class="pagePagination"
          style="display: flex; justify-content: end"
        >
          <el-pagination
            @size-change="handleItemsPerPageChange"
            background
            layout="total, sizes, prev, pager, next"
            :total="totalItems"
            :page-size="itemsPerPage"
            :page-sizes="[5, 10, 20]"
            @current-change="handlePageChange"
            :current-page="currentPage"
          >
          </el-pagination>
        </div>
      </div>
      <div v-show="radio != 1">
        <el-table :data="trans.data" border stripe max-height="240px">
          <el-table-column
            type="index"
            label="序号"
            width="50"
            :index="
              index =>
                (trans.form.pageIndex - 1) * trans.form.pageSize + index + 1
            "
          ></el-table-column>
          <el-table-column
            label="主题"
            prop="sheetTitle"
            min-width="160"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            label="SDH系统名称"
            prop="sdhSysName"
            min-width="130"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            label="SDH传输段名称"
            prop="sdhSegName"
            min-width="160"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            label="WDM系统名称"
            prop="wdmSysName"
            min-width="130"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            label="WDM传输段名称"
            prop="wdmSegName"
            min-width="160"
            show-overflow-tooltip
          ></el-table-column>
        </el-table>
        <pagination
          :total="trans.total"
          :page.sync="trans.form.pageIndex"
          :limit.sync="trans.form.pageSize"
          layout="->, total, sizes, prev, pager, next"
          @change="getTransList"
        />
      </div>
    </div>
  </el-card>
</template>

<script>
import Pagination from "../../components/Pagination.vue";

import { apiDownloadManualFile, apifluenceExcel } from "../api/CommonApi";
import {
  getBusinessAffectListApi,
  getTransAffectListApi,
} from "../api/businessImpactDetails.js";

export default {
  name: "BusinessImpactDetails",
  components: {
    Pagination,
  },
  props: {
    woId: String,
    circuitType: String,
    newCircuitData: Array,
  },
  data() {
    return {
      //  分页新增
      items: [], // 所有数据项
      currentPage: 1, // 当前页码
      itemsPerPage: 5, // 每页显示的数据条数

      showDetail: true,
      radio: "1",
      business: {
        form: {
          keyWord: "",
          pageIndex: 1,
          pageSize: 5,
        },
        total: 0,
        data: [],
      },
      trans: {
        form: {
          keyWord: "",
          pageIndex: 1,
          pageSize: 5,
        },
        total: 0,
        data: [],
      },
      // 新增
      isCircuitType: true,
    };
  },
  mounted() {
    // if (this.circuitType == "电路故障") {
    //   Promise.all([this.getBusinessList()]).then(() => {
    //     if (this.business.data.length == 0 && this.trans.data.length == 0) {
    //       this.showDetail = false;
    //     } else {
    //       this.radio = this.business.data.length > 0 ? "1" : "2";
    //       this.showDetail = true;
    //     }
    //   });
    //   return;
    // }
    Promise.all([this.getBusinessList(), this.getTransList()]).then(() => {
      if (this.business.data.length == 0 && this.trans.data.length == 0) {
        this.showDetail = false;
      } else {
        this.radio = this.business.data.length > 0 ? "1" : "2";
        this.showDetail = true;
      }
    });
  },
  created() {
    // console.log(this.circuitType);
  },
  computed: {
    // 分页新增
    totalItems() {
      return this.newCircuitData.length; // 总数据条数
    },
    currentPageData() {
      const start = (this.currentPage - 1) * this.itemsPerPage;
      const end = start + this.itemsPerPage;
      return this.newCircuitData.slice(start, end); // 当前页的数据
    },
  },
  watch: {
    circuitType: {
      handler(res) {
        // console.log(this.newCircuitData);
        if (res == "电路故障") {
          this.isCircuitType = false;
          this.business.data = this.newCircuitData;
          this.business.total = this.newCircuitData.length;
          this.totalItems = this.newCircuitData.length;
        }
      },
      immediate: true,
    },
    newCircuitData: {
      handler(res) {
        console.log("=====", res);
        if (this.circuitType == "电路故障") {
          this.business.data = this.newCircuitData;
          this.business.total = this.newCircuitData.length;
          this.totalItems = this.newCircuitData.length;
          if (this.business.data.length == 0) {
            this.showDetail = false;
          } else {
            this.radio = this.business.data.length > 0 ? "1" : "2";
            this.showDetail = true;
          }
        }
      },
      immediate: true,
    },
  },
  methods: {
    handlePageChange(newPage) {
      this.currentPage = newPage; // 更新当前页码
    },
    handleItemsPerPageChange(value) {
      this.currentPage = 1; // 重置到第一页
      this.itemsPerPage = value; // 更新每页显示的数据条数
    },

    onQuery(resetPageIndex) {
      if (this.radio == 1) {
        if (resetPageIndex) this.business.form.pageIndex = 1;
        this.getBusinessList();
      } else {
        if (resetPageIndex) this.trans.form.pageIndex = 1;
        this.getTransList();
      }
    },
    getBusinessList() {
      return new Promise(resolve => {
        getBusinessAffectListApi({
          ...this.business.form,
          woId: this.woId,
        })
          .then(res => {
            // if(this.circuitType == '电路故障'){
            //   return
            // }
            this.business.data = res.data.records;
            this.business.total = res.data.total;
            console.log("aaaaaa");
            if ((this.circuitType == "电路故障")) {
              this.$emit("requestCircuitData", true);
            }
          })
          .catch(() => {
            this.business.data = [];
            this.business.total = 0;
          })
          .finally(() => resolve());
      });
    },
    getTransList() {
      return new Promise(resolve => {
        getTransAffectListApi({
          ...this.trans.form,
          woId: this.woId,
        })
          .then(res => {
            this.trans.data = res.data.records;
            this.trans.total = res.data.total;
            // console.log("aaaaaa")
            if ((this.circuitType == "电路故障")) {
              this.$emit("requestCircuitData", true);
            }
          })
          .catch(() => {
            this.trans.data = [];
            this.trans.total = 0;
          })
          .finally(() => resolve());
      });
    },
    onExport() {
      const loading = this.$loading();
      const api = this.radio == 1 ? apiDownloadManualFile : apifluenceExcel;
      const param = { woId: this.woId };
      api({
        param1: JSON.stringify(param),
      })
        .then(res => {
          if (res.status != "0") {
            this.$message.error("文件下载失败");
          } else {
            this.$message.success("文件下载成功");
          }
        })
        .catch(res => {
          this.$message.error("文件下载失败");
        })
        .finally(() => loading.close());
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../assets/common.scss";
.business-impact-details {
  .search-input {
    width: inherit;
    margin-right: 10px;
  }
  .header-title {
    display: inline-block;
    margin-top: 2px;
    &.fold {
      margin-top: 6px;
    }
  }
}
</style>
