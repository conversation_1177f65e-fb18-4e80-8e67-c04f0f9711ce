<template>
  <div class="map-wrap">
    <div id="container"></div>
  </div>
</template>
<script>
//import loadJs from "../api/loadJs";
// import * as zrender from "zrender";
export default {
  name: "Mapview",
  props: {
    otdrData: Object,
  },
  watch: {
    otdrData() {
      this.initAMap();
      this.setPoints();
    },
  },
  data() {
    return {
      zoom: 13,
      defaultCenterPoint: [],
      map: null,

      zr: null,
      curPoint: null,
      canvasWidth: 0,
      canvasHeight: 0,
      pointArr: [],
    };
  },
  created() {},
  mounted() {
    // loadJs("../api/baiduApi").then(() => {
    //   // 加载成功，进行后续操作
    //   // const webApiBasePath = "https://gis.10010.com:8219/dugis-baidu/";
    //   // const coordinateType = "bd09ll"; // 当前坐标类型，用于webAPI传参
    //   // const defaultCenterPoint = this.defaultCenterPoint; // 中心点经纬度
    //   // //DuGIS权限校验
    //   // const apiAuthorization = "baidu-d874fd0219944973909532c3d3444432";
    //   console.log("aaaa");
    // });
    this.initAMap();
    this.setPoints();
  },
  methods: {
    initAMap() {
      //创建地图实例;
      //this.map = new window.BMap.Map("container", { render: "canvas" });
      this.map = new window.BMap.Map("container", {
        resizeEnable: true,
      });

      // 开启鼠标滚轮缩放
      this.map.enableScrollWheelZoom(true);
      var top_left_navigation = new window.BMap.NavigationControl(); //左上角，添加默认缩放平移控件
      this.map.addControl(top_left_navigation);
    },

    setPoints() {
      //获取故障信息
      const faultInfo = this.otdrData.faultInfo;

      const point = new window.BMap.Point(faultInfo.wgs84X, faultInfo.wgs84Y);
      //初始化地图，设置中心点坐标和地图级别
      this.map.centerAndZoom(point, 18);
      // 创建人井图标
      var manhole = new window.BMap.Icon(
        require("../assets/img/manhole.png"),
        new window.BMap.Size(52, 30)
      );
      var jifang = new window.BMap.Icon(
        require("../assets/img/jifang.png"),
        new window.BMap.Size(52, 50)
      );
      let faultLineStart = null;
      let faultLineEnd = null;
      const lineList = this.otdrData.lineList;
      var markers = [];
      lineList.forEach(item => {
        const points = [];
        points.push({
          lng: item.beginPointInfo.holeWgs84X,
          lat: item.beginPointInfo.holeWgs84Y,
        });
        points.push({
          lng: item.endPointInfo.holeWgs84X,
          lat: item.endPointInfo.holeWgs84Y,
        });

        if (item.beginPointInfo.typeName == "人井") {
          const marker = new window.BMap.Marker(
            new window.BMap.Point(
              item.beginPointInfo.holeWgs84X,
              item.beginPointInfo.holeWgs84Y
            ),
            {
              icon: manhole,
            }
          ); // 创建标注
          markers.push(marker);
          this.map.addOverlay(marker);
        } else if (item.beginPointInfo.typeName == "机房") {
          const marker = new window.BMap.Marker(
            new window.BMap.Point(
              item.beginPointInfo.holeWgs84X,
              item.beginPointInfo.holeWgs84Y
            ),
            {
              icon: jifang,
            }
          ); // 创建标注
          markers.push(marker);
          this.map.addOverlay(marker);
        }
        if (item.endPointInfo.typeName == "人井") {
          const marker = new window.BMap.Marker(
            new window.BMap.Point(
              item.endPointInfo.holeWgs84X,
              item.endPointInfo.holeWgs84Y
            ),
            {
              icon: manhole,
            }
          ); // 创建标注
          this.map.addOverlay(marker);
          markers.push(marker);
        } else if (item.endPointInfo.typeName == "机房") {
          const marker = new window.BMap.Marker(
            new window.BMap.Point(
              item.endPointInfo.holeWgs84X,
              item.endPointInfo.holeWgs84Y
            ),
            {
              icon: jifang,
            }
          ); // 创建标注
          this.map.addOverlay(marker);
          markers.push(marker);
        }
        if (faultInfo.supportSectName == item.supportSectName) {
          console.log("故障----------------段落存在");
          faultLineStart = new window.BMap.Point(
            item.beginPointInfo.holeWgs84X,
            item.beginPointInfo.holeWgs84Y
          );
          faultLineEnd = new window.BMap.Point(
            item.endPointInfo.holeWgs84X,
            item.endPointInfo.holeWgs84Y
          );
        }
        const polyline = new window.BMap.Polyline(
          [
            new window.BMap.Point(
              item.beginPointInfo.holeWgs84X,
              item.beginPointInfo.holeWgs84Y
            ),
            new window.BMap.Point(
              item.endPointInfo.holeWgs84X,
              item.endPointInfo.holeWgs84Y
            ),
          ],
          {
            strokeColor: "blue",
            strokeWeight: 1,
            strokeOpacity: 0.6,
          }
        );
        // 添加overlay;
        this.map.addOverlay(polyline);
      });
      // var markerClusterer = new window.BMapLib.MarkerClusterer(this.map, {
      //   markers: markers,
      //   gridSize: 60,
      //   maxZoom: 18,
      //   minClusterSize: 2,
      // });
      // markerClusterer.addMarkers(markers);

      //var view = this.map.getViewport(points);
      //进入显示的中心位置，百分比
      //this.map.centerAndZoom(view.center, 10);

      // 画线
      // 创建polyline实例

      //故障段落
      const polylineFault = new window.BMap.Polyline(
        [faultLineStart, faultLineEnd],
        { strokeColor: "#c43c43", strokeWeight: 6, strokeOpacity: 1 }
      );
      this.map.addOverlay(polylineFault);

      //添加信息窗口
      const opts = {
        width: 390, // 信息窗口宽度
        height: 320, // 信息窗口高度
        title: "故障/预警信息", // 信息窗口标题
      };
      let faultSection = "故障段落";
      // 这里是HTML内容
      let content =
        '<div  style="width: 100%;height: 290px;overflow-y:scroll"><div><div style=" width: 10px;height: 10px; background-color: #409EFF;display:inline-block;"></div><div style="display:inline-block;margin-left:5px">资源关联</div></div> <div class="contentL" style="display: flex;justify-content: space-between;flex-direction: row;flex-wrap: wrap;width: 300px;height: 46px;"> <div class="content-item item1" style="width: 80px;height: 45px;color: #fff;background-color: #a9a9a9;"><div style="text-align: center">设备端口</div><div style="text-align: center">' +
        faultInfo.relSource.portCount +
        "</div>" +
        "</div>" +
        '<div class="content-item item2" style="width: 80px;height: 45px;color: #fff;background-color: #a9a9a9;"><div style="text-align: center">光缆</div> <div style="text-align: center">' +
        faultInfo.relSource.optCount +
        "</div></div>" +
        ' <div class="content-item item3" style="width: 80px;height: 45px;color: #fff;background-color: #a9a9a9;"><div style="text-align: center">系统</div> <div style="text-align: center">' +
        faultInfo.relSource.sysCount +
        "</div></div></div>" +
        '<div style=" width: 10px;height: 10px; background-color: #409EFF;display:inline-block;"></div><div style="display:inline-block;margin-left:5px">资源属性</div>' +
        '<div style="display: table;width: 100%;">' +
        '<div style="display: table-row;">' +
        '<div style="display: table-cell;border: 1px solid #f4f6f7; padding: 3px;background-color: #f9fafb;width: 30%;">' +
        faultSection +
        "</div>" +
        '<div style="display: table-cell;border: 1px solid #f4f6f7; padding: 3px;">' +
        faultInfo.optSectName +
        "</div>" +
        "</div>" +
        '<div style="display: table-row;">' +
        '<div style="display: table-cell;border: 1px solid #f4f6f7; padding: 3px;background-color: #f9fafb;width: 30%;">故障点定位' +
        "</div>" +
        '<div style="display: table-cell;border: 1px solid #f4f6f7; padding: 3px;overflow:hidden;text-overflow:ellipsis">' +
        faultInfo.supportSectName +
        "</div>" +
        "</div>" +
        '<div style="display: table-row;">' +
        '<div style="display: table-cell;border: 1px solid #f4f6f7; padding: 3px;background-color: #f9fafb;width: 30%;">经度' +
        "</div>" +
        '<div style="display: table-cell;border: 1px solid #f4f6f7; padding: 3px;">' +
        faultInfo.wgs84X +
        "</div>" +
        "</div>" +
        '<div style="display: table-row;">' +
        '<div style="display: table-cell;border: 1px solid #f4f6f7; padding: 3px;background-color: #f9fafb;width: 30%;">纬度' +
        "</div>" +
        '<div style="display: table-cell;border: 1px solid #f4f6f7; padding: 3px;">' +
        faultInfo.wgs84Y +
        "</div>" +
        "</div>" +
        '<div style="display: table-row;">' +
        '<div style="display: table-cell;border: 1px solid #f4f6f7; padding: 3px;background-color: #f9fafb;width: 30%;">故障原因' +
        "</div>" +
        '<div style="display: table-cell;border: 1px solid #f4f6f7; padding: 3px;">' +
        faultInfo.faultReason +
        "</div>" +
        "</div>" +
        '<div style="display: table-row;">' +
        '<div style="display: table-cell;border: 1px solid #f4f6f7; padding: 3px;background-color: #f9fafb;width: 30%;">告警发生时间' +
        "</div>" +
        '<div style="display: table-cell;border: 1px solid #f4f6f7; padding: 3px;">' +
        faultInfo.faultTime +
        "</div>" +
        "</div>" +
        " </div>";
      // 创建信息窗口
      const infoWindow = new window.BMap.InfoWindow(content, opts);
      polylineFault.addEventListener("click", e => {
        console.log(e);
        this.map.openInfoWindow(
          infoWindow,
          new window.BMap.Point(e.point.lng, e.point.lat)
        ); //开启信息窗口
      });

      // 更新信息窗口内容
      //infoWindow.setContent(content);

      //可以选择将地图视野移动到合适的位置以容纳所有点;
      // const bounds = polyline.getBounds();
      // this.map.setViewport(bounds);
    },
    initAMap2() {},
    init() {
      //创建地图实例;
      let map = new window.BMap.Map("container");
      const faultInfo = this.otdrData.faultInfo;
      const lineList = this.otdrData.lineList;
      const point = new window.BMap.Point(faultInfo.wgs84X, faultInfo.wgs84Y);
      //初始化地图，设置中心点坐标和地图级别
      map.centerAndZoom(point, 18);
      // 创建点坐标
      // const point = new window.BMap.Point(116.404, 39.915);
      //初始化地图，设置中心点坐标和地图级别
      // this.map.centerAndZoom(point, 15);
      // 开启鼠标滚轮缩放
      map.enableScrollWheelZoom(true);
      let that = this;
      var canvasLayer = new window.BMap.CanvasLayer({ update: update });
      function update() {
        if (!that.zr) {
          that.zr = zrender.init(this.canvas); //初始化zrender
          //  that.canvasWidth = this.zr.getWidth();
          //  that.canvasHeight = this.zr.getHeight();
        } else {
          that.zr.clear();
        }
        let faultLineStart = null;
        let faultLineEnd = null;

        lineList.forEach(item => {
          // const points = [];
          // points.push({
          //   lng: item.beginPointInfo.holeWgs84X,
          //   lat: item.beginPointInfo.holeWgs84Y,
          // });
          // points.push({
          //   lng: item.endPointInfo.holeWgs84X,
          //   lat: item.endPointInfo.holeWgs84Y,
          // });
          var point = map.pointToPixel(
            new window.BMap.Point(
              item.beginPointInfo.holeWgs84X,
              item.beginPointInfo.holeWgs84Y
            )
          );
          var pointMark = new zrender.Image({
            style: {
              image: "../assets/img/manhole.png",
              x: point.x - 15,
              y: point.y - 36,
              width: 30,
              height: 36,
            },
            data: {
              position: new window.BMap.Point(
                item.beginPointInfo.holeWgs84X,
                item.beginPointInfo.holeWgs84Y
              ),
            },
          });
          that.zr.add(pointMark);
        });
        that.zr.resize();
      }
      // 添加overlay;
      map.addOverlay(canvasLayer);
    },
  },
};
</script>
<style lang="scss" scoped>
.map-wrap {
  position: relative;
  margin-top: 5px;
}
.map-wrap .note {
  z-index: 999;
  position: absolute;
  bottom: 20px;
  right: 20px;
  display: block;
  width: 64px;
  height: 64px;
  line-height: 64px;
  border-radius: 999px;
  text-align: center;
  font-weight: bold;
  color: #fff;
  background: #05a605;
}
#container {
  padding: 0px;
  margin: 0px;
  width: 100%;
  height: 100%;
  min-height: 500px;
  overflow: hidden;
}
.contentL {
  width: 300px;
  height: 60px;
  background-color: #fff;

  /* 新加的代码 */
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-around;
}
</style>
