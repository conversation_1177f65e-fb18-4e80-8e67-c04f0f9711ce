<template>
  <el-card shadow="always" class="card" :body-style="{ padding: '10px 24px' }">
    <div class="header clearfix">
      <div class="header-right" v-show="isShowAudit && btnFlag">
        <el-button v-if="common.networkType == '国际'" type="primary" size="mini"
          @click="qualitativeReviewInternation">定性审核</el-button>
        <el-button v-else-if="
          (common.networkTypeTop === 0 && common.professionalType == '3') ||
          common.networkType == '一干'
        " type="primary" size="mini" @click="qualitativeReview">定性审核
        </el-button>
      </div>
      <div class="header-right" v-show="isShowQualitative && btnFlag">
        <el-button v-if="common.networkType == '国际'" type="primary" size="mini"
          @click="qualitativeInternation">定性</el-button>
        <el-button v-else-if="
          (common.networkTypeTop === 0 && common.professionalType == '3') ||
          common.networkType == '一干'
        " type="primary" size="mini" @click="qualitative">定性</el-button>
      </div>
      <div class="header-tabMenu">
        <span class="header-title" style="margin-top: 5px; margin-right: 10px">反馈单详情</span>
        <!-- <el-tabs type="card" @tab-click="handleClick">
          <el-tab-pane v-for="(tab, key) in tabMenu" :key="key" :label="tab"></el-tab-pane>
        </el-tabs> -->
        <div style="display: inline-block">
          <el-radio-group v-model="radio1" @change="handleClick" size="mini">
            <el-radio-button v-for="(tab, key) in tabMenu" :key="key" :label="key">
              {{ tab.trim() }}
            </el-radio-button>
          </el-radio-group>
        </div>
      </div>
    </div>
    <div class="content">
      <el-descriptions title="故障定性信息" class="descriptions">
        <template v-if="common.networkType == '国际'">
          <el-descriptions-item label="故障所属专业">{{
            list.professionalType
            }}</el-descriptions-item>
          <el-descriptions-item label="故障发生时间">{{
            list.alarmCreateTime
            }}</el-descriptions-item>
          <el-descriptions-item label="故障通知时间">
            {{ list.sheetCreateTime }}
          </el-descriptions-item>
          <el-descriptions-item label="故障得知时间">
            {{ list.faultKnowTime }}
          </el-descriptions-item>
          <el-descriptions-item label="故障代通时间">{{
            list.lastClearTime
            }}</el-descriptions-item>
          <el-descriptions-item label="故障代通历时">{{
            faultGenerationDuration
            }}</el-descriptions-item>
          <el-descriptions-item label="故障结束时间">
            {{ list.faultEndTime }}</el-descriptions-item>
          <el-descriptions-item label="故障处理历时">
            {{ troubleshootingDuration }}
          </el-descriptions-item>
          <el-descriptions-item label="故障发生国家">
            {{ list.faultHappenCountry }}
          </el-descriptions-item>
          <el-descriptions-item label="故障处理部门">
            {{ list.dept }}
          </el-descriptions-item>
          <el-descriptions-item label="处理人">{{
            list.person
            }}</el-descriptions-item>
          <el-descriptions-item label="挂起历时">
            {{ suspendDuration }}
          </el-descriptions-item>
          <el-descriptions-item label="故障处理净历时">
            {{ processDuration }}
          </el-descriptions-item>
          <el-descriptions-item label="是否影响业务">{{
            list.isEffectBusiness
            }}</el-descriptions-item>
          <el-descriptions-item label="影响范围" :span="2">{{
            list.effectRange
            }}</el-descriptions-item>
        </template>
        <template v-else-if="
          (common.networkTypeTop === 0 && common.professionalType == '3') ||
          common.networkType == '一干'
        ">
          <el-descriptions-item label="故障所属专业">{{
            list.professionalType
            }}</el-descriptions-item>
          <el-descriptions-item label="故障发生时间">{{
            list.alarmCreateTime
            }}</el-descriptions-item>
          <el-descriptions-item label="故障通知时间">
            {{ list.sheetCreateTime }}
          </el-descriptions-item>
          <el-descriptions-item label="故障代通时间">{{
            list.lastClearTime
            }}</el-descriptions-item>
          <el-descriptions-item label="故障代通历时">{{
            faultGenerationDuration
            }}</el-descriptions-item>
          <el-descriptions-item label="故障结束时间">
            {{ list.faultEndTime }}</el-descriptions-item>
          <el-descriptions-item label="故障处理历时">
            {{ troubleshootingDuration }}
          </el-descriptions-item>
          <el-descriptions-item label="是否为故障省">
            <span v-if="list.isFaultProvince == 1">是</span>
            <span v-else>否</span>
          </el-descriptions-item>
          <el-descriptions-item label="故障发生地区">
            {{ list.faultRegion }}
          </el-descriptions-item>
          <el-descriptions-item label="故障处理部门">
            {{ list.dept }}
          </el-descriptions-item>
          <el-descriptions-item label="处理人">{{
            list.person
            }}</el-descriptions-item>
          <el-descriptions-item label="挂起历时">{{
            suspendDuration
            }}</el-descriptions-item>
          <el-descriptions-item label="故障处理净历时">
            {{ processDuration }}
          </el-descriptions-item>
          <el-descriptions-item label="是否影响业务">{{
            list.isEffectBusiness
            }}</el-descriptions-item>
          <el-descriptions-item label="影响范围" :span="2">{{
            list.effectRange
            }}</el-descriptions-item>
        </template>

        <!-- <el-descriptions-item label="是否基站退服">{{
          list.isSiteOffline
        }}</el-descriptions-item>
        <el-descriptions-item label="退服原因" :span="2">{{
          list.siteOfflineReason
        }}</el-descriptions-item> -->
      </el-descriptions>
      <el-descriptions title="故障专业信息" class="descriptions">
        <el-descriptions-item label="故障状态">
          {{ list.faultStatus }}
        </el-descriptions-item>
        <el-descriptions-item label="网络类型">
          {{ list.networkType }}
        </el-descriptions-item>
        <el-descriptions-item label="故障分类">
          {{
            common.networkType == "国际" ? list.intFaultCate : list.faultCate
          }}
        </el-descriptions-item>
        <template v-if="list.intFaultCate == '海缆故障' && common.networkType == '国际'">
          <el-descriptions-item label="故障原因分类">
            {{ list.faultReasonCate }}</el-descriptions-item>
          <el-descriptions-item label="所在海缆">
            {{ list.seaFiberName }}</el-descriptions-item>
          <el-descriptions-item label="海缆段">
            {{ list.seaFiberSeg }}</el-descriptions-item>

          <el-descriptions-item label="运营商名称">
            {{ list.operatorName }}</el-descriptions-item>
          <el-descriptions-item label="海缆类型">
            {{ list.seaFiberType }}</el-descriptions-item>
          <el-descriptions-item label="故障海域">
            {{ list.seaFiberFaultArea }}</el-descriptions-item>

          <el-descriptions-item label="是否完成修复">
            {{ list.isRepairCompleted }}</el-descriptions-item>
          <el-descriptions-item label="关联海缆站点段">
            {{ list.connectedSeaFiberStationSection }}</el-descriptions-item>
          <el-descriptions-item label="是否超时">
            {{ list.isOverTime }}</el-descriptions-item>

          <el-descriptions-item label="故障原因描述" :span="9">
            <span style="white-space: pre-wrap">{{
              list.faultReasonDesc
              }}</span></el-descriptions-item>
          <el-descriptions-item v-if="isRouteAdjustShow" label="是否路由调整">
            {{ list.isRouteAdjust }}</el-descriptions-item>
          <el-descriptions-item v-if="isRouteNoShow" label="路由调整单号">
            <el-link :underline="false" @click="toRoutePage" type="primary" style="color: #409eff"
              v-if="list.routeNo">{{ list.routeNo }}</el-link>
            <span v-else>无</span>
          </el-descriptions-item>
        </template>
        <template v-else-if="
          list.intFaultCate == '陆缆故障' && common.networkType == '国际'
        ">
          <el-descriptions-item label="故障原因分类">
            {{ list.faultReasonCate }}</el-descriptions-item>
          <el-descriptions-item label="陆缆名称">
            {{ list.landFiberName }}</el-descriptions-item>
          <el-descriptions-item label="陆缆段">
            {{ list.landFiberSeg }}</el-descriptions-item>
          <el-descriptions-item label="供应商">
            {{ list.provider }}</el-descriptions-item>
          <el-descriptions-item label="是否完成修复">
            {{ list.isRepairCompleted }}</el-descriptions-item>
          <el-descriptions-item label="是否超时" :span="6">
            {{ list.isOverTime }}</el-descriptions-item>
          <el-descriptions-item label="故障原因描述" :span="9">
            <span style="white-space: pre-wrap">{{
              list.faultReasonDesc
              }}</span></el-descriptions-item>
          <el-descriptions-item v-if="isRouteAdjustShow" label="是否路由调整">
            {{ list.isRouteAdjust }}</el-descriptions-item>
          <el-descriptions-item v-if="isRouteNoShow" label="路由调整单号">
            <el-link :underline="false" @click="toRoutePage" type="primary" style="color: #409eff"
              v-if="list.routeNo">{{ list.routeNo }}</el-link>
            <span v-else>无</span>
          </el-descriptions-item>
        </template>
        <template v-else-if="
          list.intFaultCate == '设备故障' && common.networkType == '国际'
        ">
          <el-descriptions-item label="故障原因分类">
            {{ list.faultReasonCate }}</el-descriptions-item>
          <el-descriptions-item label="故障国家">
            {{ list.faultCountry }}</el-descriptions-item>
          <el-descriptions-item label="运营商名称">
            {{ list.operatorName }}</el-descriptions-item>
          <el-descriptions-item label="是否超时">
            {{ list.isOverTime }}</el-descriptions-item>
          <el-descriptions-item label="故障原因描述" :span="9">
            <span style="white-space: pre-wrap">{{
              list.faultReasonDesc
              }}</span></el-descriptions-item>
          <el-descriptions-item v-if="isRouteAdjustShow" label="是否路由调整">
            {{ list.isRouteAdjust }}</el-descriptions-item>
          <el-descriptions-item v-if="isRouteNoShow" label="路由调整单号">
            <el-link :underline="false" @click="toRoutePage" type="primary" style="color: #409eff"
              v-if="list.routeNo">{{ list.routeNo }}</el-link>
            <span v-else>无</span>
          </el-descriptions-item>
        </template>
        <template v-else-if="
          list.faultCate == '线路故障' &&
          ((common.networkTypeTop === 0 && common.professionalType == '3') ||
            common.networkType == '一干')
        ">
          <el-descriptions-item label="故障原因">
            {{ list.circuitFaultReason }}</el-descriptions-item>
          <el-descriptions-item label="光缆名称">
            {{ list.opticFiber }}</el-descriptions-item>
          <el-descriptions-item label="故障区间">
            {{ list.faultRange }}</el-descriptions-item>
          <el-descriptions-item label="维护主体">
            {{ list.maintainDept }}
          </el-descriptions-item>
          <el-descriptions-item label="是否保护">
            {{ list.isProtected }}</el-descriptions-item>
          <el-descriptions-item label="保护是否生效">
            {{ list.isProtectedValid }}</el-descriptions-item>
          <el-descriptions-item label="是否超时">
            {{ list.isOverTime }}</el-descriptions-item>
          <el-descriptions-item label="附件" :span="2">
            <el-tag v-for="(item, index) in attachmentArr" class="fileName_style" :key="index"
              @click="previewAppendixFile(item)" v-loading.fullscreen.lock="appendixFileLoading" :title="item.name">
              <div class="text-truncate">{{ item.name }}</div>
            </el-tag>
            <span v-if="attachmentArr.length == 0">无</span>
          </el-descriptions-item>
          <el-descriptions-item label="受影响系统" :span="6">
            {{ list.effectSystem }}
          </el-descriptions-item>
          <el-descriptions-item label="受影响电路" :span="6">
            {{ list.effectCircuit }}
          </el-descriptions-item>
          <el-descriptions-item label="故障原因描述" :span="9">
            <span style="white-space: pre-wrap">{{
              list.faultReasonDesc
              }}</span>
          </el-descriptions-item>
          <el-descriptions-item v-if="isRouteAdjustShow" label="是否路由调整">
            {{ list.isRouteAdjust }}</el-descriptions-item>
          <el-descriptions-item v-if="isRouteNoShow" label="路由调整单号">
            <el-link :underline="false" @click="toRoutePage" type="primary" style="color: #409eff"
              v-if="list.routeNo">{{ list.routeNo }}</el-link>
            <span v-else>无</span>
          </el-descriptions-item>
          <el-descriptions-item label="备注" :span="3">
            {{ list.faultComment }}</el-descriptions-item>
        </template>
        <template v-else-if="
          list.faultCate == '设备故障' &&
          ((common.networkTypeTop === 0 && common.professionalType == '3') ||
            common.networkType == '一干')
        ">
          <el-descriptions-item label="设备类型">
            {{ list.eqpType }}</el-descriptions-item>
          <el-descriptions-item label="设备名称">
            {{ list.eqpName }}</el-descriptions-item>
          <el-descriptions-item label="故障原因">
            {{ list.eqpFaultReason }}</el-descriptions-item>
          <el-descriptions-item label="故障区间">
            {{ list.faultRange }}</el-descriptions-item>
          <el-descriptions-item label="维护主体">
            {{ list.maintainDept }}</el-descriptions-item>
          <el-descriptions-item label="板卡类型">
            {{ list.cardType }}</el-descriptions-item>
          <el-descriptions-item label="是否有人机房">
            {{ list.isPersonInRoom }}</el-descriptions-item>
          <el-descriptions-item label="是否有备件">
            {{ list.hasBackupPart }}</el-descriptions-item>
          <el-descriptions-item label="是否保护">
            {{ list.isProtected }}</el-descriptions-item>
          <el-descriptions-item label="保护是否生效">
            {{ list.isProtectedValid }}</el-descriptions-item>
          <el-descriptions-item label="是否超时" :span="2">
            {{ list.isOverTime }}</el-descriptions-item>
          <el-descriptions-item label="承载业务系统" :span="6">
            {{ list.supportSystem }}</el-descriptions-item>
          <el-descriptions-item label="受影响电路" :span="6">
            {{ list.effectCircuit }}</el-descriptions-item>
          <el-descriptions-item label="故障厂家" :span="6">
            {{ list.vendor }}</el-descriptions-item>
          <el-descriptions-item label="故障原因描述" :span="9">
            <span style="white-space: pre-wrap">{{
              list.faultReasonDesc
              }}</span></el-descriptions-item>
          <el-descriptions-item v-if="isRouteAdjustShow" label="是否路由调整">
            {{ list.isRouteAdjust }}</el-descriptions-item>
          <el-descriptions-item v-if="isRouteNoShow" label="路由调整单号">
            <el-link :underline="false" @click="toRoutePage" type="primary" style="color: #409eff"
              v-if="list.routeNo">{{ list.routeNo }}</el-link>
            <span v-else>无</span>
          </el-descriptions-item>
          <el-descriptions-item label="备注" :span="6">
            {{ list.faultComment }}</el-descriptions-item>
          <el-descriptions-item label="附件" :span="6">
            <el-tag v-for="(item, index) in attachmentArr" class="fileName_style" :key="index"
              @click="previewAppendixFile(item)" v-loading.fullscreen.lock="appendixFileLoading" :title="item.name">
              <div class="text-truncate">{{ item.name }}</div>
            </el-tag>
            <span v-if="attachmentArr.length == 0">无</span>
          </el-descriptions-item>
        </template>
      </el-descriptions>
      <el-descriptions title="故障定性审核信息" v-if="
        list.auditResult != null &&
        list.auditResult != '' &&
        common.sheetStatus != '待定性审核'
      " class="descriptions">
        <el-descriptions-item label="审批结果">
          <span v-if="list.auditResult == 0">拒绝</span><span v-else-if="list.auditResult == 1">同意</span>
          <span v-else>无</span>
        </el-descriptions-item>
        <el-descriptions-item label="是否上传故障报告">
          {{ list.isUploadReport }}</el-descriptions-item>
        <el-descriptions-item label="处理时限">
          <span v-if="list.dealTimeLimit">{{ list.dealTimeLimit }}小时</span>
          <span v-else>无</span>
        </el-descriptions-item>
        <el-descriptions-item label="第二故障源流水号">
          {{ list.secondFalutSource }}</el-descriptions-item>
        <el-descriptions-item label="业务影响范围">
          {{ list.audFaultRange }}</el-descriptions-item>
        <el-descriptions-item label="故障所在省">
          {{ list.faultProvince }}</el-descriptions-item>
        <el-descriptions-item label="审批意见">
          {{ list.auditContent }}</el-descriptions-item>
        <el-descriptions-item label="是否全选入库">
          {{ list.isAllStored }}</el-descriptions-item>
      </el-descriptions>
    </div>
    <el-dialog title="定性审核" :visible.sync="dialogQualitativeReviewVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="qualitativeReviewClose" :fullscreen="false" width="83%" top="5vh">
      <qualitative-review ref="qualitativeReview" :common="common" :workItemId="workItemId" v-if="showDxsh"
        @qualitativeReviewSubmit="qualitativeReviewSubmit"></qualitative-review>
    </el-dialog>
    <el-dialog title="定性审核" :visible.sync="dialogQualitativeReviewInternationVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="qualitativeReviewInternationClose" width="83%" top="5vh">
      <qualitative-review-internation ref="qualitativeReviewForm" :common="common" :workItemId="workItemId"
        v-if="showDxshInternation"
        @qualitativeReviewInternationSubmit="qualitativeReviewInternationSubmit"></qualitative-review-internation>
    </el-dialog>
    <el-dialog title="定性" :visible.sync="dialogQualitativeInternationVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="qualitativeInternationClose" :fullscreen="false" width="83%" top="5vh">
      <qualitative-internation ref="qualitativeForm" :common="common" :workItemId="workItemId"
        :isUploadReport="isUploadReport" v-if="showDxInternation"
        @qualitativeInternationSubmit="qualitativeInternationSubmit"></qualitative-internation>
    </el-dialog>
    <el-dialog title="定性" :visible.sync="dialogQualitativeVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="qualitativeClose" width="83%" top="5vh">
      <qualitative ref="qualitative" :common="common" :workItemId="workItemId" :isUploadReport="isUploadReport"
        v-if="showDx" @qualitativeSubmit="qualitativeSubmit"></qualitative>
    </el-dialog>
    <el-dialog width="420px" title="文件下载" :visible.sync="attachmentVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" append-to-body>
      <file-download @cancel="attachmentVisible = false" :attachmentArr="attachmentArr"></file-download>
    </el-dialog>

    <!-- 使用图片预览组件 -->
    <image-preview :visible.sync="imagePreviewVisible" :file-data="currentPreviewFile"
      preview-api="/commonDict/attach/download" :use-custom-download="true" @download="downloadCurrentFile"
      @close="imagePreviewVisible = false"></image-preview>
  </el-card>
</template>

<script>
import QualitativeReview from "./QualitativeReview.vue";
import Qualitative from "./Qualitative.vue";

import { apiqueryFeedback } from "../../api/FeedbackSheet";
import FileDownload from "../../components/FileDownload.vue";
import { apiDownloadAppendixFile } from "../../workOrderWaitDetail/api/CommonApi";
import QualitativeInternation from "./QualitativeInternation.vue";
import QualitativeReviewInternation from "./QualitativeReviewInternation.vue";
import ImagePreview from "@plugin/backbone/components/ImagePreview.vue";
export default {
  name: "FeedbackSheet",
  props: {
    isShowAudit: Boolean, //判断定性审核按钮
    common: Object,
    woId: String,
    isShowQualitative: Boolean, //判断定性按钮
    qualitativeType: String,
  },
  components: {
    QualitativeReview,
    Qualitative,
    FileDownload,
    QualitativeInternation,
    QualitativeReviewInternation,
    ImagePreview,
  },
  data() {
    return {
      tabMenu: [],
      list: {},
      listAll: [],
      btnFlag: false,
      dialogQualitativeReviewVisible: false,
      //故障定性
      dialogQualitativeVisible: false,
      faultGenerationDuration: null, //故障代通历时
      troubleshootingDuration: null, //故障处理历时
      suspendDuration: null, //挂起历时
      processDuration: null, //故障处理净历时
      attachmentArr: [], //附件
      attachmentVisible: false,
      // 多个省的 定性审核区分workItemId
      workItemId: null,
      showDxsh: false,
      showDx: false,
      appendixFileLoading: false,
      isUploadReport: null,
      showDxInternation: false,
      dialogQualitativeInternationVisible: false,
      //国际定性审核
      dialogQualitativeReviewInternationVisible: false,
      showDxshInternation: false,
      radio1: 0,
      isRouteNoShow: true,
      isRouteAdjustShow: true,
      // 图片预览相关
      imagePreviewVisible: false,
      currentPreviewFile: {},
    };
  },
  mounted() {
    if (this.woId) {
      this.getFeedbackData();
    }
    console.log(this.common);
  },
  methods: {
    handleClick(index) {
      this.deal(this.listAll[index]);
      this.setBtnFlag();
    },
    deal(data) {
      this.list = data;
      this.faultGenerationDuration = this.showTime(data.lastClearDuration);
      this.troubleshootingDuration = this.showTime(data.faultDuration);
      this.suspendDuration = this.showTime(data.suspendDuration);
      this.processDuration = this.showTime(data.processDuration);
      if (data.appendix) {
        this.attachmentArr = JSON.parse(data.appendix);
      }
      this.workItemId = data.workItemId;
      this.isUploadReport = data.isUploadReport;
    },
    getFeedbackData() {
      let initIndex = 0;
      let param = {
        woId: this.woId,
      };
      apiqueryFeedback(param)
        .then(res => {
          if (res.status == 0) {
            let self = this;
            self.listAll = res?.data?.rows ?? [];
            if (self.listAll.length > 0) {
              self.list = self.listAll[0];
              self.workItemId = self.list.workItemId;
              self.isUploadReport = self.list.isUploadReport;
              self.faultGenerationDuration = self.showTime(
                self.list.lastClearDuration
              );
              self.troubleshootingDuration = self.showTime(
                self.list.faultDuration
              );
              self.suspendDuration = self.showTime(self.list.suspendDuration);
              self.processDuration = self.showTime(self.list.processDuration);
              if (
                self.isShowAudit == true ||
                self.common.sheetStatus == "已归档"
              ) {
                for (let i = 0; i < self.listAll.length; i++) {
                  if (self.listAll[i].personProName == "中国") {
                    self.tabMenu.push("联通国际公司");
                  } else if (
                    (self.common.networkTypeTop === 0 &&
                      self.common.professionalType == "3") ||
                    self.common.networkType == "一干"
                  ) {
                    let tabName = `${self.listAll[i].personProName}`;
                    self.tabMenu.push(tabName);
                  }
                }
              } else {
                for (let listIndex in self.listAll) {
                  if (self.common.networkType == "国际") {
                    let tabName =
                      "（联通国际公司）" + self.listAll[listIndex].dept;
                    self.tabMenu.push(tabName);
                  } else if (
                    (self.common.networkTypeTop === 0 &&
                      self.common.professionalType == "3") ||
                    self.common.networkType == "一干"
                  ) {
                    let tabName = `（${self.listAll[listIndex].faultRegion}）${self.listAll[listIndex].dept}`;
                    self.tabMenu.push(tabName);
                    if (
                      JSON.parse(
                        JSON.parse(sessionStorage.userInfo).attr2
                      ).orgInfo.fullOrgName.indexOf(
                        self.listAll[listIndex].dept
                      ) > -1
                    ) {
                      initIndex = listIndex;
                    }
                  }
                }
              }

              if (self.list.appendix) {
                self.attachmentArr = JSON.parse(self.list.appendix);
              }
            }
            self.radio1 = initIndex;
            self.handleClick(initIndex);
          }
        })
        .catch(err => {
          console.log(err);
        });
    },
    setBtnFlag() {
      if (this.qualitativeType == "定性审核") {
        if (this.isShowAudit == false) {
          this.btnFlag = false;
        } else {
          this.btnFlag = this.list.operatingStatus == "1" ? true : false;
        }
      } else if (this.qualitativeType == "定性") {
        if (this.isShowQualitative == false) {
          this.btnFlag = false;
        } else {
          this.btnFlag = this.list.operatingStatus == "1" ? true : false;
        }
      }
    },
    qualitativeReviewClose() {
      this.dialogQualitativeReviewVisible = false;
      this.$refs.qualitativeReview.onReset();
    },
    //定性审核提交
    qualitativeReviewSubmit(data) {
      this.$emit("qualitativeReviewSubmit", data);
      this.dialogQualitativeReviewVisible = false;
    },
    qualitativeReviewInternationClose() {
      this.dialogQualitativeReviewInternationVisible = false;
      this.$refs.qualitativeReviewForm.onReset();
    },
    //国际定性审核提交
    qualitativeReviewInternationSubmit(data) {
      this.$emit("qualitativeReviewInternationSubmit", data);
      this.dialogQualitativeReviewInternationVisible = false;
    },
    qualitativeReview() {
      this.showDxsh = false;
      this.$nextTick(() => {
        this.showDxsh = true;
      });
      this.dialogQualitativeReviewVisible = true;
    },
    qualitativeReviewInternation() {
      this.showDxshInternation = false;
      this.$nextTick(() => {
        this.showDxshInternation = true;
      });
      this.dialogQualitativeReviewInternationVisible = true;
    },
    qualitative() {
      this.showDx = false;
      this.$nextTick(() => {
        this.showDx = true;
      });
      this.dialogQualitativeVisible = true;
    },
    qualitativeInternation() {
      this.showDxInternation = false;
      this.$nextTick(() => {
        this.showDxInternation = true;
      });
      this.dialogQualitativeInternationVisible = true;
    },
    //定性提交
    qualitativeSubmit(data) {
      this.$emit("qualitativeSubmit", data);
      this.dialogQualitativeVisible = false;
    },
    qualitativeClose() {
      this.dialogQualitativeVisible = false;
      this.$refs.qualitative.onReset();
    },
    qualitativeInternationClose() {
      this.dialogQualitativeInternationVisible = false;
      this.$refs.qualitativeForm.onReset();
    },
    //国际定性提交
    qualitativeInternationSubmit() {
      this.$emit("qualitativeInternationSubmit");
      this.dialogQualitativeInternationVisible = false;
    },
    queryAttachmentList() {
      this.attachmentVisible = true;
    },
    closeAttachmentDialog() {
      this.attachmentVisible = false;
    },
    // 预览附件文件
    previewAppendixFile(data) {
      // 首先检查文件名是否为图片类型
      const fileName = data.name.toLowerCase();
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'];

      // 检查文件扩展名是否为图片
      const isImage = imageExtensions.some(ext => fileName.endsWith(ext));

      if (!isImage) {
        // 如果不是图片，直接下载
        this.downloadAppendixFile(data);
        return;
      }

      // 如果是图片，保存当前文件信息并显示预览对话框
      this.currentPreviewFile = data;
      this.imagePreviewVisible = true;
    },

    // 下载当前预览的文件
    downloadCurrentFile() {
      this.downloadAppendixFile(this.currentPreviewFile);
    },

    // 下载附件文件
    downloadAppendixFile(data) {
      this.appendixFileLoading = true;
      let param = {
        attId: data.id,
      };
      apiDownloadAppendixFile(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("文件下载成功");
          } else {
            this.$message.error("文件下载失败");
          }
          this.appendixFileLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("文件下载失败");
          this.appendixFileLoading = false;
        });
    },
    showTime(val) {
      if (val == 0 || val == "" || null == val) {
        return "0秒";
      }
      var time = "";
      var second = val % 60;
      var minute = parseInt(parseInt(val) / 60) % 60;
      time = second + "秒";
      if (minute >= 1) {
        time = minute + "分" + second + "秒";
      }
      var hour = parseInt(parseInt(val / 60) / 60) % 24;
      if (hour >= 1) {
        time = hour + "小时" + minute + "分" + second + "秒";
      }
      var day = parseInt(parseInt(parseInt(val / 60) / 60) / 24);
      if (day >= 1) {
        time = day + "天" + hour + "小时" + minute + "分" + second + "秒";
      }
      return time;
    },
    toRoutePage() {
      let url = window.location.protocol + "//" + window.location.host;
      // let url = "http://************:8088"; //测试环境地址
      // let url = "http://************"; //生产环境地址
      if (this.list.routeProcessInstId != null) {
        url +=
          "/ibpm-ui/#/contentTo?content=Y&needi=Y&no=detail&hideHeader=1&reqFrom=handle&processInstId=" +
          this.list.routeProcessInstId +
          "&jobCode=" +
          this.list.routeNo +
          "&globalUniqueID=" +
          sessionStorage.getItem("globalUniqueID");
      } else {
        url +=
          "/EOM_LIFE_ADJUST/#/transfer/draftform?hideHeader=1&hideTagsNav=1&actonShow=false&jobcode=" +
          this.list.routeNo;
      }
      window.open(url);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../assets/common.scss";

.header-tabMenu {
  margin-top: 10px;
}
</style>
