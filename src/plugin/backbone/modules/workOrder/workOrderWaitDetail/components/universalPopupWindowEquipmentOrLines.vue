<template>
    <div>
        <div class="involvedCircuitInput">
            <el-input ref="involvedCircuitInput" :readonly="false" style="height: auto" multiple
                v-model.trim="cableEquipInputDataInner" :placeholder="placeholder || `请输入${title}名称`">
                <!-- <template slot="prefix">
                    <div style="overflow: hidden; overflow-x: auto" :style="{
                        width:
                            cableEquipInputWidth - 79 + 'px',
                    }">
                        <div style="display: flex">
                            <el-tag v-for="(tag, index) in cableFaultTagData" style="margin-top: 5px; float: left"
                                :key="index" closable="true" @close="cableFaultDeleteRow(index, tag)">
                                {{ tag.circuitId }}
                            </el-tag>
                        </div>
                    </div>
                </template> -->
                <el-button style="
                    background: #b50b14;
                    border-color: #b50b14;
                    color: #fff;
                    padding: 9px 20px;
                    top: -0.5px;
                    position: relative;
                  " slot="append" @click="opticalCableSelect">选择</el-button>
            </el-input>
        </div>

        <!-- 线路或设备名称弹窗 -->
        <el-dialog destroy-on-close :before-close="close" custom-class="cableFaultDialogTableVisible"
            style="margin-top: 3%;" :title="`${title}查询`" :visible.sync="popupWindowDisplay"
            :close-on-click-modal="false" append-to-body>
            <el-form :model="lineFaultForm">
                <el-col :xs="24" :sm="8" :md="8" :offset="0">
                    <el-form-item style="display: flex; white-space: nowrap; font-size: 14px" :label="`${title}名称: `">
                        <el-input v-model="lineFaultForm.cableName" :placeholder="`请输入${title}名称`">
                            <el-button type="info" slot="append" icon="el-icon-search" @click="() => {
                                searchCircuitNumber(1);
                            }
                                "></el-button>
                            <el-button slot="append" icon="el-icon-close" @click="() => {
                                lineFaultForm.cableName = '';
                                lineFaultForm.cableTable.pageNum = 1;
                                lineFaultForm.cableTable.pageSize = 10;
                                lineFaultForm.cableTable.total = 0;
                            }
                                "></el-button>
                        </el-input>
                    </el-form-item>
                </el-col>
            </el-form>
            <el-table stripe v-loading="lineFaultForm.cableLoading" ref="involvedCircuitMultipleTable"
                :data="lineFaultForm.cableTableData" tooltip-effect="dark"
                style="width: 100%; height: 18rem; padding-bottom: 2.8rem"
                @selection-change="involvedCircuitHandleSelectionChange">
                <el-table-column type="selection" width="55"> </el-table-column>
                <el-table-column prop="name" :label="`${title}名称`" :style="{
                    width: '100%',
                }">
                </el-table-column>
                <template slot="empty">{{ empty }}</template>
            </el-table>
            <el-pagination :current-page.sync="lineFaultForm.cableTable.pageNum"
                :page-size.sync="lineFaultForm.cableTable.pageSize" :page-sizes="[5, 10, 20]"
                :total="lineFaultForm.cableTable.total" :pager-count="5" layout="->,total,sizes,prev, pager, next"
                background @size-change="searchCircuitNumberByPageSize" @current-change="searchCircuitNumberByPageNum">
            </el-pagination>
            <span slot="footer" class="dialog-footer">
                <!-- <el-button @click="popupWindowDisplay = false">取 消</el-button> -->
                <el-button type="primary" @click="confirm">确 定</el-button>
            </span>
        </el-dialog>
    </div>

</template>

<script>
import { searchCablesDevicesThemes } from "../api/CommonApi";
export default {
    props: {
        title: {
            type: String,
            default: ""
        },
        cableEquipInputData: {
            type: String,
            default: ""
        },
        currentSearchQueryType: {
            type: Number,
            default: 0
        },
        placeholder: {
            type: String,
            default: null
        }
    },
    watch: {
        "cableEquipInputData": {
            handler(newV, oldV) {
                this.cableEquipInputDataInner = newV;
            },
            deep: true,
        },
        "cableEquipInputDataInner": {
            handler(newV, oldV) {
                if (newV != oldV) {
                    this.$emit('changeCableEquipInputData', newV, this.title);
                }
            },
            deep: true,
        },
    },
    created() {
        this.cableEquipInputDataInner = this.cableEquipInputData;
    },
    methods: {
        close() {
            this.popupWindowDisplay = false;
            this.init();
        },
        init() {
            this.empty = '暂无数据';
            this.lineFaultForm.cableTable.pageNum = 1;
            this.lineFaultForm.cableTable.pageSize = 10;
            this.lineFaultForm.cableTable.total = 0;
            this.lineFaultForm.cableName = "";
            this.lineFaultForm.cableTableData = [];
        },
        confirm() {
            let isRepeat = false;
            if (this.lineFaultForm.returnCableArray && this.lineFaultForm.returnCableArray.length == 0) {
                this.$message({
                    showClose: true,
                    message: '选择数据不可以为空',
                    type: 'warning'
                });
                return;
            }
            this.lineFaultForm.returnCableArray.forEach(item => {
                console.log(this.cableEquipInputDataInner, item);

                if (this.cableEquipInputDataInner && (this.cableEquipInputDataInner.split('、')).includes(item.name)) {
                    if (!isRepeat) {
                        this.$message({
                            showClose: true,
                            message: '添加存在重复，请重新选择',
                            type: 'warning'
                        });
                    }
                    isRepeat = true;
                }
                if (isRepeat) return;
            })
            if (isRepeat) return;

            this.$emit('eventClose', false);
            this.$emit('event-select-data', this.lineFaultForm.returnCableArray, this.title);
            this.popupWindowDisplay = false;
            this.init();
        },
        searchCircuitNumberByPageSize(pageSize) {
            this.lineFaultForm.cableTable.pageSize = pageSize;
            this.searchCircuitNumber(1);
        },
        searchCircuitNumberByPageNum(pageNum) {
            this.searchCircuitNumber(pageNum);
        },
        searchCircuitNumber(pageNum) {
            this.lineFaultForm.cableTable.pageNum = pageNum;
            this.lineFaultForm.cableLoading = true;
            try {
                console.log("currentSearchQueryType")
                searchCablesDevicesThemes({
                    queryValue: this.lineFaultForm.cableName || "",
                    queryType: this.currentSearchQueryType,
                    pageNum: pageNum,
                    pageSize: this.lineFaultForm.cableTable.pageSize
                })
                    .then(res => {
                        console.log(res);
                        if (res.data.list.length == 0) {
                            this.empty = '未查询到相关数据';
                        }
                        if (res["status"] == 0) {
                            this.lineFaultForm.cableTableData = res.data.list;
                            this.lineFaultForm.cableTable.total = res.data.total;

                            // this.updateInvolvedCircuitInputWidth();
                            console.log(this.lineFaultForm.cableTableData);
                        } else if (res["status"] == 400) {
                            this.lineFaultForm.cableTableData = [];
                        }
                        this.lineFaultForm.cableLoading = false;
                    })
                    .catch(err => {
                        this.lineFaultForm.cableTableData = [];
                        this.lineFaultForm.cableLoading = false;
                    });
            } catch (error) {
                console.log(error);
                this.lineFaultForm.cableTableData = [];
                this.lineFaultForm.cableLoading = false;
            }
        },
        handlePageChange(newPage) {
            this.currentPage = newPage; // 更新当前页码
        },
        involvedCircuitHandleSelectionChange(e) {
            console.log("handleChange ", e);
            this.lineFaultForm.returnCableArray = e;
        },
        opticalCableSelect() {
            let jsonStr =
                '{"userId":"302785","username":"yangxm97","callSysId":"0001","callSysName":"电子运维"}';
            this.popupWindowDisplay = true;
        },
    },
    data() {
        return {
            // 骨干传输故障工单下 线路/设备故障对应 光缆/设备名称 选择弹窗数据
            popupWindowDisplay: false,
            lineFaultForm: {
                cableName: "",
                cableLoading: false,
                cableTableData: [],
                returnCableArray: [],
                cableTable: {
                    pageNum: 1,
                    pageSize: 10,
                    total: 0
                }
            },

            // 骨干传输故障工单下 线路/设备故障对应 光缆/设备名称 选择弹窗
            cableOrEquipPopupWindowTitle: "",
            cableFaultTagData: [], // 线路故障对应tag数据
            equipFaultTagData: [], // 设备故障对应tag数据
            cableEquipInputWidth: '10',

            empty: '暂无数据',
            cableEquipInputDataInner: ""
        }
    }
}
</script>

<style lang="scss" scoped>
::v-deep .cableFaultDialogTableVisible {
    width: 630px;

    .el-form .el-input__inner {
        width: 13rem;
    }

    .el-pagination {
        margin-top: 5px;
    }

    // .el-table__empty-block {
    //     margin-top: 10%;
    // }

    .el-dialog__body {
        padding-top: 0;
        padding-bottom: 0;

        .el-table {
            background: #fff;
            border: 1px solid #e9e9e9;
            box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12), 0 0 6px 0 rgba(0, 0, 0, 0.04);
        }
    }
}

::v-deep .el-table__body-wrapper {
    height: 100%;
    overflow-y: scroll;
}

::v-deep .pagePagination {
    .el-input__inner {
        width: 6rem;
        padding-right: 1rem;
    }
}

::v-deep .el-input__suffix .el-input__validateIcon {
    display: none !important;
}

::v-deep .involvedCircuitInput .el-input__inner {
    // color: transparent !important;
    padding-left: 1rem;
}
</style>