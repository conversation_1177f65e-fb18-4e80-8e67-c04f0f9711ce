<template>
  <div>
    <el-form ref="abendForm" :model="abendForm" label-width="120px" :rules="abendFormRules">
      <el-form-item
        label="异常终止原因:"
        prop="opResult"
        :rules="{
          required: true,
          message: '请选择异常终止原因',
        }"
      >
        <dict-select
          :value.sync="abendForm.opResult"
          :dictId="10049"
          placeholder="请选择异常终止原因"
          style="width: 250px"
        />
      </el-form-item>
      <el-form-item label="异常终止说明:" prop="processSuggestion">
        <el-input
          type="textarea"
          :rows="2"
          placeholder="请填写异常终止说明"
          v-model="abendForm.processSuggestion"
          style="width: 250px"
          show-word-limit
          maxlength="1000"
        >
        </el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: right">
      <el-button
        type="primary"
        @click="handleAbendSubmit('abendForm')"
        v-loading.fullscreen.lock="abendFullScreenLoading"
        >提 交</el-button
      >
      <el-button @click="onResetAbend">重 置</el-button>
    </div>
  </div>
</template>
<script>
import { apiAbend, apiItCloudAbend, apiCommCloudAbend } from "../api/CommonApi";
import DictSelect from "../../components/DictSelect.vue";
import {mixin} from "../../../../../../mixins"

export default {
  name: "Abend",
  props: {
    common: Object,
  },
  components: { DictSelect },
  mixins:[mixin],
  data() {
    return {  
      abendForm: {
        opResult: null,
        processSuggestion: null,
      },
      abendFormRules: {
        processSuggestion: [
        {
            validator: this.checkLength,
            max: 1000,
            form: "abendForm",
            messsage: "已超过填写字数上限",
          },
        ],
      },
      abendFullScreenLoading: false,
    };
  },
  mounted() {},
  methods: {
    handleAbendSubmit(formName) {
      this.$refs[formName].validate((valid,a) => {
        if (this.uncheck(a)) {
          this.abendFullScreenLoading = true;
          let param = {
            woId: this.common.woId,
            processInstId: this.common.processInstId,
            processDefId: this.common.processDefId,
            workItemId: this.common.workItemId,
            processNode: this.common.processNode,
            opResult: this.abendForm.opResult,
            processSuggestion: this.abendForm.processSuggestion,
          };
          if (this.common.professionalTypeName == "传输网") {
            apiAbend(param)
              .then(res => {
                if (res.status == "0") {
                  this.$message.success(res.msg);
                  this.onResetAbend();
                  this.$emit("closeDialogAbend");
                } else {
                  this.$message.error(res.msg);
                }
                this.abendFullScreenLoading = false;
              })
              .catch(error => {
                console.log(error);
                this.abendFullScreenLoading = false;
                this.$message.error("提交申请失败");
              });
          } else if (this.common.professionalTypeName == "IT云设备") {
            let formData = new FormData();
            formData.append("opType", 3);
            formData.append("jsonParam", JSON.stringify(param));
            apiItCloudAbend(formData)
              .then(res => {
                if (res.status == "0") {
                  this.$message.success(res.msg);
                  this.onResetAbend();
                  this.$emit("closeDialogAbend");
                } else {
                  this.$message.error(res.msg);
                }
                this.abendFullScreenLoading = false;
              })
              .catch(error => {
                console.log(error);
                this.abendFullScreenLoading = false;
                this.$message.error("提交申请失败");
              });
          } else if (this.common.professionalTypeName == "通信云") {
            let formData = new FormData();
            formData.append("opType", 3);
            formData.append("jsonParam", JSON.stringify(param));
            apiCommCloudAbend(formData)
              .then(res => {
                if (res.status == "0") {
                  this.$message.success(res.msg);
                  this.onResetAbend();
                  this.$emit("closeDialogAbend");
                } else {
                  this.$message.error(res.msg);
                }
                this.abendFullScreenLoading = false;
              })
              .catch(error => {
                console.log(error);
                this.abendFullScreenLoading = false;
                this.$message.error("提交申请失败");
              });
          }
        } else {
          return false;
        }
      });
    },
    onResetAbend() {
      this.abendForm = {
        ...this.$options.data,
      };
    },
  },
};
</script>
