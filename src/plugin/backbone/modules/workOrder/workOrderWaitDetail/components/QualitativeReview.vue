<template>
  <div class="qualitative-review">
    <el-form
      ref="qualitativeReviewForm"
      :inline="false"
      class="demo-form-inline"
      :model="qualitativeReviewForm"
      label-width="140px"
      :rules="qualitativeReviewFormRules"
    >
      <el-card
        shadow="never"
        header="故障定性信息"
        :body-style="{ padding: '20px 8px' }"
        class="cus-card"
      >
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item
              label="故障所属专业:"
              prop="professionalType"
              :rules="{
                required: true,
                message: '请选择故障所属专业',
              }"
            >
              <dict-select
                :value.sync="qualitativeReviewForm.professionalType"
                :dictId="10002"
                placeholder=""
                style="width: 100%"
                :notSelect="true"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障发生时间:" prop="alarmCreateTime" required>
              {{ qualitativeReviewForm.alarmCreateTime }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障通知时间:" prop="sheetCreateTime" required>
              {{ qualitativeReviewForm.sheetCreateTime }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障代通时间:" prop="lastClearTime">
              <el-date-picker
                v-model="qualitativeReviewForm.lastClearTime"
                type="datetime"
                placeholder="请选择故障代通时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
                disabled
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障代通历时:">
              {{ second2Time(qualitativeReviewForm.lastClearDuration) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障结束时间:" prop="faultEndTime">
              <el-date-picker
                v-model="qualitativeReviewForm.faultEndTime"
                type="datetime"
                placeholder="请选择故障结束时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
                disabled
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障处理历时:" required>
              {{ second2Time(this.qualitativeReviewForm.faultDuration) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否为故障省:" prop="isFaultProvince" required>
              <el-radio-group v-model="qualitativeReviewForm.isFaultProvince">
                <el-radio :disabled="true" label="1">是</el-radio>
                <el-radio :disabled="true" label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="故障发生地区:"
              prop="faultRegion"
              :rules="{
                required: true,
                message: '请选择内容',
              }"
            >
              <el-select
                style="width: 100%"
                v-model="qualitativeReviewForm.faultRegion"
                disabled
              >
                <el-option
                  v-for="(item, i) in faultRegionOptions"
                  :key="i"
                  :label="item.name"
                  :value="item.name"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障处理部门:" required>
              {{ qualitativeReviewForm.dept }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="处理人:" prop="person" required>
              {{ qualitativeReviewForm.person }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="挂起历时:" required>
              {{ second2Time(this.qualitativeReviewForm.suspendDuration) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障处理净历时:" required>
              {{ second2Time(this.qualitativeReviewForm.processDuration) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="是否影响业务:"
              prop="isEffectBusiness"
              :rules="[
                {
                  required: qualitativeReviewForm.eqpType == '6' ? true : false,
                  message: '请选择是否影响业务',
                },
              ]"
            >
              <el-radio-group
                v-model="qualitativeReviewForm.isEffectBusiness"
                @change="faultCateChange"
                disabled
              >
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item
              label="影响范围:"
              v-if="qualitativeReviewForm.isEffectBusiness == '1'"
            >
              <el-input
                type="textarea"
                :rows="2"
                disabled
                placeholder="请输入内容"
                v-model="qualitativeReviewForm.effectRange"
                style="width: 640px"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card
        shadow="never"
        header="故障专业信息"
        :body-style="{ padding: '20px 8px' }"
        class="cus-card"
      >
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item
              label="故障状态:"
              prop="faultStatus"
              :rules="{
                required: true,
                message: '请选择故障状态',
              }"
            >
              <dict-select
                :value.sync="qualitativeReviewForm.faultStatus"
                :dictId="10017"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="网络类型:"
              prop="networkType"
              :rules="{
                required: true,
                message: '请选择网络类型',
              }"
            >
              <dict-select
                :value.sync="qualitativeReviewForm.networkType"
                :dictId="10063"
                style="width: 100%"
                @change="networkTypeChange()"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="故障分类:"
              prop="faultCate"
              :rules="{
                required: true,
                message: '请选择故障分类',
              }"
            >
              <dict-select
                :value.sync="qualitativeReviewForm.faultCate"
                :dictId="10019"
                style="width: 100%"
                @change="faultCateChange('faultCate')"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row
          type="flex"
          style="flex-wrap: wrap"
          :gutter="20"
          v-if="qualitativeReviewForm.faultCate == '1'"
          key="faultCateOne"
        >
          <el-col :span="8">
            <el-form-item
              label="故障原因:"
              :rules="{
                required: true,
                message: '请选择故障原因',
              }"
              prop="circuitFaultReason"
            >
              <dict-select
                :value.sync="qualitativeReviewForm.circuitFaultReason"
                :dictId="faultReasonCode"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="光缆名称:"
              prop="opticFiber"
              :rules="{
                required: true,
                message: '光缆名称不能为空',
              }"
            >
              <el-input
                v-model="qualitativeReviewForm.opticFiber"
                style="width: 100%"
              >
                <el-button
                  style="
                    background: #b50b14;
                    border-color: #b50b14;
                    color: #fff;
                    padding: 9px 20px;
                    top: -0.5px;
                    position: relative;
                  "
                  slot="append"
                  @click="opticalCableSelect"
                  >选择</el-button
                >
              </el-input>
              <form
                id="sub__fiberOpticCable"
                name="sub__fiberOpticCable"
                hidden="true"
                method="post"
                action="/resweb_jituan/union/resAssign.out?method=selectFiberSection&requestJson={}"
                target="_blank"
              >
                <input
                  type="hidden"
                  name="fiberOpticCable"
                  id="fiberOpticCable"
                />
              </form>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="故障区间:"
              prop="faultRange"
              :rules="{
                required: true,
                message: '请输入故障区间',
              }"
            >
              <el-input
                v-model="qualitativeReviewForm.faultRange"
                style="width: 100%"
                placeholder="请输入内容"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="维护主体:"
              prop="maintainDept"
              :rules="{
                required: true,
                message: '请选择维护主体',
              }"
            >
              <dict-select
                :value.sync="qualitativeReviewForm.maintainDept"
                :dictId="10021"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="是否保护:"
              prop="isProtected"
              :rules="{
                required: true,
                message: '请选择是否保护',
              }"
            >
              <el-radio-group
                v-model="qualitativeReviewForm.isProtected"
                style="width: 100%"
                @change="faultCateChange"
              >
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="保护是否生效:"
              :rules="{
                required: true,
                message: '请选择保护是否生效',
              }"
              prop="isProtectedValid"
            >
              <el-radio-group v-model="qualitativeReviewForm.isProtectedValid">
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否超时:">
              <span v-if="qualitativeReviewForm.isOverTime == '0'">否</span
              ><span v-else-if="qualitativeReviewForm.isOverTime == '1'"
                >是</span
              >
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="相关附件:">
              <el-tag
                class="fileName_style_download"
                closable
                v-for="(item, index) in fddxFileXlArr"
                :key="index"
                @close="closeAndDeleteFile(item)"
                @click="downloadAppendixFile(item)"
                :title="item.name"
                ><div class="text-truncate">{{ item.name }}</div></el-tag
              >
              <el-tag
                class="fileName_style"
                closable
                v-for="(item, index) in importForm.relatedFilesFileList"
                :key="index"
                @close="closeFile(item)"
                :title="item.name"
                ><div class="text-truncate">{{ item.name }}</div></el-tag
              >
              <el-button size="mini" type="primary" @click="relatedFilesBrowse"
                >+上传附件</el-button
              >
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="受影响系统:">
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="qualitativeReviewForm.effectSystem"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="受影响电路:">
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="qualitativeReviewForm.effectCircuit"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              label="故障原因描述:"
              prop="falutReasonDesc"
              :rules="{
                required: true,
                message: '故障原因描述不能为空',
              }"
            >
              <el-input
                maxlength="500"
                show-word-limit
                type="textarea"
                :rows="2"
                :autosize="{ minRows: 3, maxRows: 6 }"
                placeholder="请输入内容"
                v-model="qualitativeReviewForm.falutReasonDesc"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注:">
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="qualitativeReviewForm.falutComment"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              v-if="isRouteAdjustShow"
              label="是否路由调整:"
              prop="isRouteAdjust"
            >
              <el-radio-group
                v-model="qualitativeReviewForm.isRouteAdjust"
                style="width: 100%"
                disabled
              >
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              v-if="qualitativeReviewForm.isRouteAdjust == 1 && isRouteNoShow"
              label="路由调整单号:"
            >
              <el-link
                :underline="false"
                @click="toRoutePage"
                type="primary"
                style="color: #409eff"
                >{{ qualitativeReviewForm.routeNo }}</el-link
              >
            </el-form-item>
          </el-col>
        </el-row>

        <el-row
          type="flex"
          style="flex-wrap: wrap"
          :gutter="20"
          v-if="qualitativeReviewForm.faultCate == '2'"
          key="faultCateTwo"
        >
          <el-col :span="8">
            <el-form-item
              label="设备类型:"
              prop="eqpType"
              :rules="{
                required: true,
                message: '请选择设备类型',
              }"
            >
              <dict-select
                :value.sync="qualitativeReviewForm.eqpType"
                :dictId="10046"
                style="width: 100%"
                placeholder="请选择内容"
                @change="faultCateChange('eqTypeChange')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="设备名称:">
              <el-input
                v-model="qualitativeReviewForm.eqpName"
                placeholder="请输入内容"
                style="width: 100%"
              >
                <el-button
                  style="
                    background: #b50b14;
                    border-color: #b50b14;
                    color: #fff;
                    padding: 9px 20px;
                    top: -0.5px;
                    position: relative;
                  "
                  slot="append"
                  @click="deviceSelect"
                  >选择</el-button
                >
              </el-input>
              <form
                id="sub__device"
                name="sub__device"
                hidden="true"
                method="post"
                action="/resweb_jituan/union/resAssign.out?method=selectEquipment"
                target="_blank"
              >
                <input type="hidden" name="device" id="device" />
              </form>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="故障原因:"
              :rules="{
                required: qualitativeReviewForm.faultCate == '2' ? true : false,
                message: '请选择故障原因',
              }"
              prop="eqpFaultReason"
            >
              <dict-select
                :value.sync="qualitativeReviewForm.eqpFaultReason"
                :dictId="faultReasonDictId"
                style="width: 100%"
                placeholder="请选择内容"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="故障区间:"
              :rules="{
                required: true,
                message: '请输入故障区间',
              }"
              prop="faultRange"
            >
              <el-input
                v-model="qualitativeReviewForm.faultRange"
                style="width: 100%"
                placeholder="请输入内容"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="维护主体:"
              :rules="{
                required: true,
                message: '请选择维护主体',
              }"
              prop="maintainDept"
            >
              <dict-select
                :value.sync="qualitativeReviewForm.maintainDept"
                :dictId="10021"
                style="width: 100%"
                placeholder="请选择内容"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="板卡类型:"
              prop="cardType"
              v-if="
                qualitativeReviewForm.faultCate == '2' &&
                (qualitativeReviewForm.eqpType == '1' ||
                  qualitativeReviewForm.eqpType == '2' ||
                  qualitativeReviewForm.eqpType == '3' ||
                  qualitativeReviewForm.eqpType == '4' ||
                  qualitativeReviewForm.eqpType == '5')
              "
              :rules="{
                required:
                  qualitativeReviewForm.faultCate == '2' &&
                  (qualitativeReviewForm.eqpType == '1' ||
                    qualitativeReviewForm.eqpType == '2' ||
                    qualitativeReviewForm.eqpType == '3' ||
                    qualitativeReviewForm.eqpType == '4' ||
                    qualitativeReviewForm.eqpType == '5')
                    ? true
                    : false,
                message: '请选择板卡类型',
              }"
            >
              <dict-select
                :value.sync="qualitativeReviewForm.cardType"
                :dictId="cardTypeDictId"
                style="width: 100%"
                placeholder="请选择内容"
                @change="faultCateChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="是否有人机房:"
              prop="isPersonInRoom"
              :rules="{
                required: true,
                message: '请选择是否有人机房',
              }"
            >
              <el-radio-group
                v-model="qualitativeReviewForm.isPersonInRoom"
                @change="faultCateChange"
              >
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="是否有备件:"
              prop="hasBackupPart"
              :rules="{
                required: true,
                message: '请选择是否有备件',
              }"
            >
              <el-radio-group
                v-model="qualitativeReviewForm.hasBackupPart"
                @change="faultCateChange"
              >
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="是否保护:"
              prop="isProtected"
              :rules="{
                required: true,
                message: '请选择是否保护',
              }"
            >
              <el-radio-group
                v-model="qualitativeReviewForm.isProtected"
                @change="faultCateChange"
              >
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="保护是否生效:"
              prop="isProtectedValid"
              :rules="{
                required: true,
                message: '请选择保护是否生效',
              }"
            >
              <el-radio-group
                v-model="qualitativeReviewForm.isProtectedValid"
                @change="faultCateChange"
              >
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否超时:">
              <span v-if="qualitativeReviewForm.isOverTime == '0'">否</span
              ><span v-else-if="qualitativeReviewForm.isOverTime == '1'"
                >是</span
              >
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              label="承载业务系统:"
              prop="supportSystem"
              :rules="{
                required: true,
                message: '承载业务系统不能为空',
              }"
            >
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="qualitativeReviewForm.supportSystem"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="受影响系统:">
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="qualitativeReviewForm.effectSystem"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="受影响电路:">
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="qualitativeReviewForm.effectCircuit"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              label="故障厂家:"
              prop="vendor"
              :rules="{
                required: true,
                message: '请选择故障厂家',
              }"
            >
              <el-radio-group
                v-model="qualitativeReviewForm.vendor"
                style="width: 100%"
              >
                <el-radio
                  v-for="(item, i) in vendorOptions"
                  :key="i"
                  :label="item.dictCode"
                  >{{ item.dictName }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              label="故障原因描述:"
              prop="falutReasonDesc"
              :rules="{
                required: true,
                message: '故障原因描述不能为空',
              }"
            >
              <el-input
                maxlength="500"
                show-word-limit
                type="textarea"
                :rows="2"
                :autosize="{ minRows: 3, maxRows: 6 }"
                placeholder="请输入内容"
                v-model="qualitativeReviewForm.falutReasonDesc"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注:">
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="qualitativeReviewForm.falutComment"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="相关附件:">
              <el-tag
                class="fileName_style_download"
                closable
                v-for="(item, index) in fddxFileDlArr"
                :key="index"
                @close="closeAndDeleteFileDl(item)"
                @click="downloadAppendixFile(item)"
                :title="item.name"
                ><div class="text-truncate">{{ item.name }}</div></el-tag
              >
              <el-tag
                class="fileName_style"
                closable
                v-for="(item, index) in importForm.relatedFilesFileList"
                :key="index"
                @close="closeFile(item)"
                :title="item.name"
                ><div class="text-truncate">{{ item.name }}</div></el-tag
              >
              <el-button size="mini" type="primary" @click="relatedFilesBrowse"
                >+上传附件</el-button
              >
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              v-if="isRouteAdjustShow"
              label="是否路由调整:"
              prop="isRouteAdjust"
            >
              <el-radio-group
                v-model="qualitativeReviewForm.isRouteAdjust"
                style="width: 100%"
                disabled
              >
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              v-if="qualitativeReviewForm.isRouteAdjust == 1 && isRouteNoShow"
              label="路由调整单号:"
            >
              <el-link
                :underline="false"
                @click="toRoutePage"
                type="primary"
                style="color: #409eff"
                >{{ qualitativeReviewForm.routeNo }}</el-link
              >
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card
        shadow="never"
        header="定性审核信息"
        :body-style="{ padding: '20px 8px' }"
        style="margin-top: 20px"
        class="cus-card"
      >
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item
              label="审批结果:"
              prop="auditResult"
              :rules="{
                required: true,
                message: '请选择审批结果',
              }"
            >
              <el-radio-group
                v-model="qualitativeReviewForm.auditResult"
                @change="changeAuditResult()"
              >
                <el-radio label="1">同意</el-radio>
                <el-radio label="0">拒绝</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="是否上传故障报告:"
              :rules="{
                required: true,
                message: '请选择是否上传故障报告',
              }"
              prop="isUploadReport"
            >
              <el-radio-group
                v-model="qualitativeReviewForm.isUploadReport"
                @change="changeUploadReport"
                :disabled="qualitativeReviewForm.auditResult == '1'"
              >
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="是否全选入库:"
              :rules="{
                required: true,
                message: '请选择是否全选入库',
              }"
              prop="isAllStored"
            >
              <el-radio-group
                v-model="qualitativeReviewForm.isAllStored"
                :disabled="
                  qualitativeReviewForm.auditResult == '1' ||
                  qualitativeReviewForm.auditResult == '0'
                "
              >
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="处理时限:">
              <el-radio-group
                v-model="qualitativeReviewForm.dealTimeLimit"
                style="width: 100%"
              >
                <el-radio
                  v-for="(item, i) in processTimeLimitOptions"
                  :key="i"
                  :label="item.dictCode"
                  >{{ item.dictName }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="第二故障源流水号:" prop="secondFalutSource">
              <el-input
                style="width: 100%"
                v-model="qualitativeReviewForm.secondFalutSource"
                placeholder="请输入内容"
                maxlength="255"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="业务影响范围:" prop="busEffectRange">
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="qualitativeReviewForm.busEffectRange"
                style="width: 100%"
                show-word-limit
                maxlength="255"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障所在省:">
              <el-select
                style="width: 100%"
                placeholder="请选择内容"
                v-model="qualitativeReviewForm.faultProvince"
                @change="$forceUpdate()"
                filterable
              >
                <el-option
                  v-for="(item, i) in provinceOption"
                  :key="i"
                  :label="item.name"
                  :value="item.name"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="审批意见:" prop="auditContent">
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model.trim="qualitativeReviewForm.auditContent"
                style="width: 100%"
                show-word-limit
                maxlength="255"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: center">
      <el-button
        type="primary"
        @click="handleSubmit('qualitativeReviewForm')"
        v-loading.fullscreen.lock="qualitativeReviewFullscreenLoading"
        >提交</el-button
      >
      <el-button @click="onReset">重 置</el-button>
    </div>

    <el-dialog
      width="420px"
      title="附件选择"
      :visible.sync="relatedFilesDialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <file-upload
        @change="changeFileData"
        @cancel="closeAttachmentDialog"
      ></file-upload>
    </el-dialog>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import DictSelect from "../../components/DictSelect.vue";
import {
  apiQualitativeDetail,
  apiQualitativeReview,
  apiGetProvinceDict,
} from "../api/CommonApi";
import { apiDict } from "../../api/CommonApi";
import FileUpload from "../../components/FileUpload.vue";
import {
  apiDownloadAppendixFile,
  apiDeleteFdFile,
} from "../../workOrderWaitDetail/api/CommonApi";
import moment from "moment";
import { config } from "../api/TroubleshootingTime";
import { mixin } from "../../../../../../mixins";
import { faultReasonEnumOnlineDate } from "../api/OnlineDateTime";
export default {
  name: "QualitativeReview",
  props: {
    common: Object,
    workItemId: [String, Number],
  },
  components: { DictSelect, FileUpload },
  computed: {
    ...mapGetters(["userInfo"]),
    faultReasonCode() {
      let seconds = moment(new Date(), "YYYY-MM-DD HH:mm:ss").diff(
        moment(faultReasonEnumOnlineDate, "YYYY-MM-DD HH:mm:ss"),
        "seconds"
      );
      if (
        seconds >= 0 &&
        (this.qualitativeReviewForm.networkType == "一干" ||
          this.qualitativeReviewForm.networkType == 2) &&
        this.qualitativeReviewForm.faultCate == 1
      ) {
        return 811037;
      }
      return 10020;
    },
  },
  watch: {
    workItemId(val) {
      this.qualitativeReviewForm.workItemId = val;
    },
  },
  mixins: [mixin],
  data() {
    return {
      qualitativeReviewForm: {
        woId: null,
        workItemId: null,
        processInstId: null,
        processDefId: null,
        professionalType: null,
        actionName: null,
        alarmCreateTime: null,
        sheetCreateTime: null,
        lastClearTime: null,
        lastClearDuration: 0, //故障代通历时(单位秒)
        faultEndTime: null,
        faultDuration: 0, //故障处理历时(单位秒)
        faultRegion: null,
        dept: null,
        person: null,
        suspendDuration: 0, //挂起历时
        processDuration: null, //故障处理净历时
        isEffectBusiness: null,
        effectRange: null,
        //线路故障
        faultStatus: null,
        networkType: null,
        faultCate: null,
        circuitFaultReason: null,
        opticFiber: "",
        faultRange: null,
        maintainDept: null,
        isProtected: null,
        isProtectedValid: null,
        isOverTime: null,
        relatedFiles: null,
        relatedFilesVirtual: null,
        effectSystem: "",
        effectCircuit: null,
        falutReasonDesc: null,
        falutComment: null,
        //设备故障
        eqpType: null,
        eqpName: "",
        eqpFaultReason: null,
        cardType: null,
        isPersonInRoom: null,
        hasBackupPart: null,
        isEffectBiz: null,
        supportSystem: null,
        vendor: null,
        isRouteAdjust: null,
        routeNo: null,
        routeProcessInstId: null,
        //审核信息
        auditResult: null,
        isUploadReport: "0",
        isAllStored: null,
        dealTimeLimit: null,
        secondFalutSource: null,
        busEffectRange: null, //业务影响范围
        auditContent: null,
        faultProvince: null,
        linkId: null,
        appendix: null,
        isFaultProvince: null,
      },
      qualitativeReviewFormRules: {
        auditContent: [
          {
            required: true,
            message: "请填写审批意见",
          },
          {
            validator: this.checkLength,
            max: 255,
            form: "qualitativeReviewForm",
            messsage: "已超过填写字数上限",
          },
        ],
        busEffectRange: [
          {
            validator: this.checkLength,
            max: 255,
            form: "qualitativeReviewForm",
            messsage: "已超过填写字数上限",
          },
        ],
        secondFalutSource: [
          {
            validator: this.checkLength,
            max: 255,
            form: "qualitativeReviewForm",
            messsage: "已超过填写字数上限",
          },
        ],
      },
      backSingleFullscreenLoading: false,
      faultRegionOptions: [],
      //关联附件
      relatedFilesDialogVisible: false,
      importForm: {
        relatedFilesFileList: [],
      },
      vendorOptions: [],
      processTimeLimitOptions: [], //处理时限
      qualitativeReviewFullscreenLoading: false,
      fddxFileXlArr: [], //线路 附件
      fddxFileDlArr: [], //电路 附件
      fddxFileVisible: false,
      faultReasonDictId: 0, //故障原因 多字典ID
      cardTypeDictId: 0, //板卡类型 多字典ID
      provinceOption: [],
      nowProvince: null, //当前省份
      isRouteNoShow: true,
      isRouteAdjustShow: true,
    };
  },
  mounted() {
    this.qualitativeReviewForm.workItemId = this.workItemId;
    this.qualitativeReviewForm.woId = this.common.woId;
    this.qualitativeReviewForm.processInstId = this.common.processInstId;
    this.qualitativeReviewForm.processDefId = this.common.processDefId;
    this.getVendorOptions();
    this.getProcessTimeLimitOptions();
    this.qualitativeReviewDetail();

    this.resourceBackInit(this.qualitativeReviewForm);
    this.getProvinceDict();
    let array = this.qualitativeReviewForm.alarmCreateTime.split(" ");
    this.createTimeDate = array[0];
  },
  methods: {
    //时间对比
    disabledDate(time) {
      let now = moment(this.createTimeDate);
      let a = moment(now).subtract(1, "days");
      let today = moment(a).valueOf();
      return time.getTime() <= today || time.getTime() - 8.64e6 >= Date.now();
    },
    addZero(s) {
      return s < 10 ? "0" + s : s;
    },
    getProvinceDict() {
      apiGetProvinceDict()
        .then(res => {
          if (res.status == "0") {
            this.provinceOption = res?.data ?? [];
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    getVendorOptions() {
      let param = {
        dictTypeCode: "10047",
      };
      apiDict(param)
        .then(res => {
          if (res.code == "200") {
            this.vendorOptions = res?.data ?? [];
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    getProcessTimeLimitOptions() {
      let param = {
        dictTypeCode: "10051",
      };
      apiDict(param)
        .then(res => {
          if (res.code == "200") {
            this.processTimeLimitOptions = res?.data ?? [];
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    qualitativeReviewDetail() {
      let param = {
        opType: 2,
        workItemId: this.qualitativeReviewForm.workItemId,
        woId: this.qualitativeReviewForm.woId,
      };
      apiQualitativeDetail(param)
        .then(res => {
          if (res.status == "0") {
            this.qualitativeReviewForm = res?.data ?? {};
            console.log(this.qualitativeReviewForm);
            this.eqTypeChange();

            this.qualitativeReviewForm.faultProvince =
              res?.data?.personProName ?? "";
            this.nowProvince = res?.data?.personProName ?? "";
            if (res.data.appendix) {
              if (this.qualitativeReviewForm.faultCate == "1") {
                this.fddxFileXlArr = JSON.parse(res.data.appendix);
              } else if (this.qualitativeReviewForm.faultCate == "2") {
                //设备
                this.fddxFileDlArr = JSON.parse(res.data.appendix);
              }
            }
            this.$set(this.qualitativeReviewForm, "isAllStored", "1");
            this.$set(this.qualitativeReviewForm, "isUploadReport", "0");
            this.faultCateChange();
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    relatedFilesBrowse() {
      this.relatedFilesDialogVisible = true;
    },
    changeFileData(data) {
      this.qualitativeReviewForm.relatedFiles = data.fileName;
      this.importForm.relatedFilesFileList = data.attachmentFileList;
      this.relatedFilesDialogVisible = false;
    },
    closeAttachmentDialog() {
      this.relatedFilesDialogVisible = false;
    },
    handleSubmit(formName) {
      this.$refs[formName].validate((valid, a) => {
        if (this.uncheck(a)) {
          this.qualitativeReviewFullscreenLoading = true;
          this.$set(this.qualitativeReviewForm, "actionName", "定性审核");
          let formData = new FormData();
          if (this.importForm.relatedFilesFileList.length > 0) {
            for (let item of this.importForm.relatedFilesFileList) {
              formData.append("files", item.raw);
            }
          } else {
            formData.append("files", "");
          }
          if (this.qualitativeReviewForm.isEffectBusiness == "0") {
            this.qualitativeReviewForm.effectRange = "";
          }
          formData.append(
            "jsonParam",
            JSON.stringify(this.qualitativeReviewForm)
          );
          formData.append(
            "checkParam",
            JSON.stringify(this.qualitativeReviewForm)
          );
          apiQualitativeReview(formData)
            .then(res => {
              if (res.status == "0") {
                this.$message.success("故障定性审核完成");
                this.$emit("qualitativeReviewSubmit", res.data);
              } else {
                this.$message.error("故障定性审核失败");
              }
              this.qualitativeReviewFullscreenLoading = false;
            })
            .catch(error => {
              console.log(error);
              this.$message.error("故障定性审核失败");
              this.qualitativeReviewFullscreenLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    faultCateChange(type) {
      if (type == "eqTypeChange") {
        this.eqTypeChange("eqTypeChange");
      } else if (type == "faultCate") {
        this.resetFaultCateChange();
      }
      if (this.qualitativeReviewForm.faultCate == 1) {
        //线路故障
        if (this.qualitativeReviewForm.isProtected == 0) {
          //无保护
          for (let item of config.proNotProtect) {
            if (item.pro.indexOf(this.nowProvince) != "-1") {
              let seconds = this.dealTimeout();
              this.$set(
                this.qualitativeReviewForm,
                "dealTimeLimit",
                item.timeLimit / 60 + ""
              );
              if (seconds > item.timeLimit * 60) {
                this.$set(this.qualitativeReviewForm, "isOverTimeShow", "是");
                this.$set(this.qualitativeReviewForm, "isOverTime", 1);
              } else {
                this.$set(this.qualitativeReviewForm, "isOverTimeShow", "否");
                this.$set(this.qualitativeReviewForm, "isOverTime", 0);
              }
            }
          }
        } else if (this.qualitativeReviewForm.isProtected == 1) {
          //有保护
          for (let item2 of config.proProtect) {
            if (item2.pro.indexOf(this.nowProvince) != "-1") {
              this.$set(
                this.qualitativeReviewForm,
                "dealTimeLimit",
                item2.timeLimit / 60 + ""
              );
              let seconds2 = this.dealTimeout();
              if (seconds2 > item2.timeLimit * 60) {
                this.$set(this.qualitativeReviewForm, "isOverTimeShow", "是");
                this.$set(this.qualitativeReviewForm, "isOverTime", 1);
              } else {
                this.$set(this.qualitativeReviewForm, "isOverTimeShow", "否");
                this.$set(this.qualitativeReviewForm, "isOverTime", 0);
              }
            }
          }
        }
      } else if (this.qualitativeReviewForm.faultCate == 2) {
        //设备故障
        // if (
        //   (this.qualitativeReviewForm.eqpType == "4" ||
        //     this.qualitativeReviewForm.eqpType == "2" ||
        //     this.qualitativeReviewForm.eqpType == "5") &&
        //   this.qualitativeReviewForm.cardType == "3" &&
        //   this.qualitativeReviewForm.isProtected == "1" &&
        //   this.qualitativeReviewForm.isProtectedValid == "1"
        // ) {
        //   for (let item4 of config.proEquipmentRule1) {
        //     if (item4.pro.indexOf(this.userData.provinceName) != "-1") {
        //       let seconds6 = this.dealTimeout();
        //       if (seconds6 > item4.timeLimit * 60) {
        //         this.$set(this.qualitativeReviewForm, "isOverTimeShow", "是");
        //         this.$set(this.qualitativeReviewForm, "isOverTime", 1);
        //       } else {
        //         this.$set(this.qualitativeReviewForm, "isOverTimeShow", "否");
        //         this.qualitativeReviewForm.isOverTime = 0;
        //       }
        //     }
        //   }
        // } else if (
        //   this.qualitativeReviewForm.eqpType == "6" &&
        //   this.qualitativeReviewForm.isEffectBusiness == "0"
        // ) {
        //   let seconds5 = this.dealTimeout();
        //   if (seconds5 > 720 * 60) {
        //     this.$set(this.qualitativeReviewForm, "isOverTimeShow", "是");
        //     this.$set(this.qualitativeReviewForm, "isOverTime", 1);
        //   } else {
        //     this.$set(this.qualitativeReviewForm, "isOverTimeShow", "否");
        //     this.qualitativeReviewForm.isOverTime = 0;
        //   }
        // } else if (
        //   this.qualitativeReviewForm.isPersonInRoom == "1" &&
        //   this.qualitativeReviewForm.hasBackupPart == "1"
        // ) {
        //   let seconds3 = this.dealTimeout();
        //   if (seconds3 > 60 * 60) {
        //     this.$set(this.qualitativeReviewForm, "isOverTimeShow", "是");
        //     this.$set(this.qualitativeReviewForm, "isOverTime", 1);
        //   } else {
        //     this.$set(this.qualitativeReviewForm, "isOverTimeShow", "否");
        //     this.$set(this.qualitativeReviewForm, "isOverTime", 0);
        //   }
        // } else if (
        //   (this.qualitativeReviewForm.isPersonInRoom == "1" &&
        //     this.qualitativeReviewForm.hasBackupPart == "0") ||
        //   (this.qualitativeReviewForm.isPersonInRoom == "0" &&
        //     this.qualitativeReviewForm.hasBackupPart == "1") ||
        //   (this.qualitativeReviewForm.isPersonInRoom == "0" &&
        //     this.qualitativeReviewForm.hasBackupPart == "0")
        // ) {
        //   for (let item3 of config.proEquipmentRule4) {
        //     if (item3.pro.indexOf(this.userData.provinceName) != "-1") {
        //       let seconds4 = this.dealTimeout();
        //       if (seconds4 > item3.timeLimit * 60) {
        //         this.$set(this.qualitativeReviewForm, "isOverTimeShow", "是");
        //         this.qualitativeReviewForm.isOverTime = 1;
        //       } else {
        //         this.$set(this.qualitativeReviewForm, "isOverTimeShow", "否");
        //         this.qualitativeReviewForm.isOverTime = 0;
        //       }
        //     }
        //   }
        // }
        if (
          this.qualitativeReviewForm.eqpType == "6" &&
          this.qualitativeReviewForm.isEffectBusiness == "0"
        ) {
          let seconds5 = this.dealTimeout();
          this.$set(this.qualitativeReviewForm, "dealTimeLimit", "12");
          if (seconds5 > 720 * 60) {
            this.$set(this.qualitativeReviewForm, "isOverTimeShow", "是");
            this.$set(this.qualitativeReviewForm, "isOverTime", 1);
          } else {
            this.$set(this.qualitativeReviewForm, "isOverTimeShow", "否");
            this.qualitativeReviewForm.isOverTime = 0;
          }
        } else if (
          this.qualitativeReviewForm.isProtected == "1" &&
          this.qualitativeReviewForm.isProtectedValid == "1"
        ) {
          for (let item4 of config.proEquipmentRule1) {
            if (item4.pro.indexOf(this.nowProvince) != "-1") {
              this.$set(
                this.qualitativeReviewForm,
                "dealTimeLimit",
                item4.timeLimit / 60 + ""
              );
              let seconds6 = this.dealTimeout();
              if (seconds6 > item4.timeLimit * 60) {
                this.$set(this.qualitativeReviewForm, "isOverTimeShow", "是");
                this.$set(this.qualitativeReviewForm, "isOverTime", 1);
              } else {
                this.$set(this.qualitativeReviewForm, "isOverTimeShow", "否");
                this.qualitativeReviewForm.isOverTime = 0;
              }
            }
          }
        } else if (
          this.qualitativeReviewForm.isPersonInRoom == "1" &&
          this.qualitativeReviewForm.hasBackupPart == "1"
        ) {
          this.$set(this.qualitativeReviewForm, "dealTimeLimit", "1");
          let seconds3 = this.dealTimeout();
          if (seconds3 > 60 * 60) {
            this.$set(this.qualitativeReviewForm, "isOverTimeShow", "是");
            this.$set(this.qualitativeReviewForm, "isOverTime", 1);
          } else {
            this.$set(this.qualitativeReviewForm, "isOverTimeShow", "否");
            this.$set(this.qualitativeReviewForm, "isOverTime", 0);
          }
        } else if (
          this.qualitativeReviewForm.isPersonInRoom == "0" ||
          this.qualitativeReviewForm.hasBackupPart == "0"
        ) {
          for (let item3 of config.proEquipmentRule4) {
            if (item3.pro.indexOf(this.nowProvince) != "-1") {
              this.$set(
                this.qualitativeReviewForm,
                "dealTimeLimit",
                item3.timeLimit / 60 + ""
              );
              let seconds4 = this.dealTimeout();
              if (seconds4 > item3.timeLimit * 60) {
                this.$set(this.qualitativeReviewForm, "isOverTimeShow", "是");
                this.qualitativeReviewForm.isOverTime = 1;
              } else {
                this.$set(this.qualitativeReviewForm, "isOverTimeShow", "否");
                this.qualitativeReviewForm.isOverTime = 0;
              }
            }
          }
        }
      }
    },
    // faultCateChange(type) {
    //   if (type == "eqTypeChange") {
    //     this.eqTypeChange(type);
    //   } else if (type == "faultCate") {
    //     this.resetFaultCateChange();
    //   }
    //   if (this.qualitativeReviewForm.faultCate == 1) {
    //     //线路故障
    //     if (this.qualitativeReviewForm.isProtected == 0) {
    //       //无保护
    //       for (let item of config.proNotProtect) {
    //         if (item.pro.indexOf(this.nowProvince) != "-1") {
    //           let seconds = this.dealTimeout();
    //           this.$set(
    //             this.qualitativeReviewForm,
    //             "dealTimeLimit",
    //             item.timeLimit / 60 + ""
    //           );
    //           if (seconds > item.timeLimit * 60) {
    //             this.$set(this.qualitativeReviewForm, "isOverTimeShow", "是");
    //             this.$set(this.qualitativeReviewForm, "isOverTime", 1);
    //           } else {
    //             this.$set(this.qualitativeReviewForm, "isOverTimeShow", "否");
    //             this.$set(this.qualitativeReviewForm, "isOverTime", 0);
    //           }
    //         }
    //       }
    //     } else if (this.qualitativeReviewForm.isProtected == 1) {
    //       //有保护
    //       for (let item2 of config.proProtect) {
    //         if (item2.pro.indexOf(this.nowProvince) != "-1") {
    //           let seconds2 = this.dealTimeout();
    //           this.$set(
    //             this.qualitativeReviewForm,
    //             "dealTimeLimit",
    //             item2.timeLimit / 60 + ""
    //           );
    //           if (seconds2 > item2.timeLimit * 60) {
    //             this.$set(this.qualitativeReviewForm, "isOverTimeShow", "是");
    //             this.$set(this.qualitativeReviewForm, "isOverTime", 1);
    //           } else {
    //             this.$set(this.qualitativeReviewForm, "isOverTimeShow", "否");
    //             this.$set(this.qualitativeReviewForm, "isOverTime", 0);
    //           }
    //         }
    //       }
    //     }
    //   } else if (this.qualitativeReviewForm.faultCate == 2) {
    //     //设备故障
    //     if (
    //       (this.qualitativeReviewForm.eqpType == "4" ||
    //         this.qualitativeReviewForm.eqpType == "2" ||
    //         this.qualitativeReviewForm.eqpType == "5") &&
    //       this.qualitativeReviewForm.cardType == "3" &&
    //       this.qualitativeReviewForm.isProtected == "1" &&
    //       this.qualitativeReviewForm.isProtectedValid == "1"
    //     ) {
    //       for (let item4 of config.proEquipmentRule1) {
    //         if (item4.pro.indexOf(this.nowProvince) != "-1") {
    //           this.$set(
    //             this.qualitativeReviewForm,
    //             "dealTimeLimit",
    //             item4.timeLimit / 60 + ""
    //           );
    //           let seconds6 = this.dealTimeout();
    //           if (seconds6 > item4.timeLimit * 60) {
    //             this.$set(this.qualitativeReviewForm, "isOverTimeShow", "是");
    //             this.$set(this.qualitativeReviewForm, "isOverTime", 1);
    //           } else {
    //             this.$set(this.qualitativeReviewForm, "isOverTimeShow", "否");
    //             this.qualitativeReviewForm.isOverTime = 0;
    //           }
    //         }
    //       }
    //     } else if (
    //       this.qualitativeReviewForm.eqpType == "6" &&
    //       this.qualitativeReviewForm.isEffectBusiness == "0"
    //     ) {
    //       let seconds5 = this.dealTimeout();
    //       this.$set(this.qualitativeReviewForm, "dealTimeLimit", "12");
    //       if (seconds5 > 720 * 60) {
    //         this.$set(this.qualitativeReviewForm, "isOverTimeShow", "是");
    //         this.$set(this.qualitativeReviewForm, "isOverTime", 1);
    //       } else {
    //         this.$set(this.qualitativeReviewForm, "isOverTimeShow", "否");
    //         this.qualitativeReviewForm.isOverTime = 0;
    //       }
    //     } else if (
    //       this.qualitativeReviewForm.isPersonInRoom == "1" &&
    //       this.qualitativeReviewForm.hasBackupPart == "1"
    //     ) {
    //       this.$set(this.qualitativeReviewForm, "dealTimeLimit", "1");
    //       let seconds3 = this.dealTimeout();
    //       if (seconds3 > 60 * 60) {
    //         this.$set(this.qualitativeReviewForm, "isOverTimeShow", "是");
    //         this.$set(this.qualitativeReviewForm, "isOverTime", 1);
    //       } else {
    //         this.$set(this.qualitativeReviewForm, "isOverTimeShow", "否");
    //         this.$set(this.qualitativeReviewForm, "isOverTime", 0);
    //       }
    //     } else if (
    //       (this.qualitativeReviewForm.isPersonInRoom == "1" &&
    //         this.qualitativeReviewForm.hasBackupPart == "0") ||
    //       (this.qualitativeReviewForm.isPersonInRoom == "0" &&
    //         this.qualitativeReviewForm.hasBackupPart == "1") ||
    //       (this.qualitativeReviewForm.isPersonInRoom == "0" &&
    //         this.qualitativeReviewForm.hasBackupPart == "0")
    //     ) {
    //       for (let item3 of config.proEquipmentRule4) {
    //         if (item3.pro.indexOf(this.nowProvince) != "-1") {
    //           let seconds4 = this.dealTimeout();
    //           this.$set(
    //             this.qualitativeReviewForm,
    //             "dealTimeLimit",
    //             item3.timeLimit / 60 + ""
    //           );
    //           if (seconds4 > item3.timeLimit * 60) {
    //             this.$set(this.qualitativeReviewForm, "isOverTimeShow", "是");
    //             this.qualitativeReviewForm.isOverTime = 1;
    //           } else {
    //             this.$set(this.qualitativeReviewForm, "isOverTimeShow", "否");
    //             this.qualitativeReviewForm.isOverTime = 0;
    //           }
    //         }
    //       }
    //     }
    //   }
    // },
    eqTypeChange(type) {
      if (type == "eqTypeChange") {
        this.qualitativeReviewForm.eqpFaultReason = "";
        this.qualitativeReviewForm.cardType = "";
      }
      if (this.qualitativeReviewForm.eqpType == "1") {
        //SDH
        this.faultReasonDictId = "10030";
        this.cardTypeDictId = "10037";
      } else if (this.qualitativeReviewForm.eqpType == "2") {
        //DWDM
        this.faultReasonDictId = "10031";
        this.cardTypeDictId = "10038";
      } else if (this.qualitativeReviewForm.eqpType == "3") {
        //ASON
        this.faultReasonDictId = "10032";
        this.cardTypeDictId = "10039";
      } else if (this.qualitativeReviewForm.eqpType == "4") {
        //OTN
        this.faultReasonDictId = "10033";
        this.cardTypeDictId = "10040";
      } else if (this.qualitativeReviewForm.eqpType == "5") {
        //ROADM
        this.faultReasonDictId = "10044";
        this.cardTypeDictId = "10041";
      } else if (this.qualitativeReviewForm.eqpType == "6") {
        //网管故障
        this.faultReasonDictId = "10034";
        this.qualitativeReviewForm.cardType = null;
      } else if (this.qualitativeReviewForm.eqpType == "7") {
        //配套设备
        this.faultReasonDictId = "10035";
        this.qualitativeReviewForm.cardType = null;
      } else if (this.qualitativeReviewForm.eqpType == "8") {
        //其他
        this.faultReasonDictId = "10036";
        this.qualitativeReviewForm.cardType = null;
      }
    },
    resetFaultCateChange() {
      this.qualitativeReviewForm.circuitFaultReason = null;
      this.qualitativeReviewForm.opticFiber = null;
      this.qualitativeReviewForm.faultRange = null;
      this.qualitativeReviewForm.maintainDept = null;
      this.qualitativeReviewForm.isProtected = null;
      this.qualitativeReviewForm.isProtectedValid = null;
      this.importForm.relatedFilesFileList = [];
      this.qualitativeReviewForm.effectSystem = null;
      this.qualitativeReviewForm.effectCircuit = null;
      this.qualitativeReviewForm.falutReasonDesc = null;
      this.qualitativeReviewForm.falutComment = null;
      this.qualitativeReviewForm.eqpType = null;
      this.qualitativeReviewForm.eqpName = null;
      this.qualitativeReviewForm.eqpFaultReason = null;
      this.qualitativeReviewForm.cardType = null;
      this.qualitativeReviewForm.isPersonInRoom = null;
      this.qualitativeReviewForm.hasBackupPart = null;
      this.qualitativeReviewForm.supportSystem = null;
      this.qualitativeReviewForm.vendor = null;
    },
    dealTimeout() {
      //故障代通时间（无故障代通时间 用故障结束时间） - 故障开始时间
      if (this.qualitativeReviewForm.lastClearTime) {
        let seconds = moment(
          this.qualitativeReviewForm.lastClearTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(
            this.qualitativeReviewForm.alarmCreateTime,
            "YYYY-MM-DD HH:mm:ss"
          ),
          "seconds"
        );
        return seconds;
      } else {
        let seconds = moment(
          this.qualitativeReviewForm.faultEndTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(
            this.qualitativeReviewForm.alarmCreateTime,
            "YYYY-MM-DD HH:mm:ss"
          ),
          "seconds"
        );
        return seconds;
      }
    },
    closeAndDeleteFile(tag) {
      this.deleteFile(tag, "xl");
    },
    closeAndDeleteFileDl(tag) {
      this.deleteFile(tag, "dl");
    },
    deleteFile(tag, type) {
      let param = {
        attId: tag.id,
        linkId: this.qualitativeReviewForm.linkId,
      };
      apiDeleteFdFile(param)
        .then(res => {
          if (res.status == "0") {
            if (type == "xl") {
              this.fddxFileXlArr.splice(this.fddxFileXlArr.indexOf(tag), 1);
              this.qualitativeReviewForm.appendix = JSON.stringify(
                this.fddxFileXlArr
              );
            } else if (type == "dl") {
              this.fddxFileDlArr.splice(this.fddxFileDlArr.indexOf(tag), 1);
              this.qualitativeReviewForm.appendix = JSON.stringify(
                this.fddxFileDlArr
              );
            }
            this.$message.success("附件删除成功");
          } else {
            this.$$message.error("附件删除失败");
          }
        })
        .catch(error => {
          this.$message.error("附件删除失败");
          console.log(error);
        });
    },
    closeFile(tag) {
      this.importForm.relatedFilesFileList.splice(
        this.importForm.relatedFilesFileList.indexOf(tag),
        1
      );
    },
    //审批结果事件
    changeAuditResult() {
      if (this.qualitativeReviewForm.auditResult == "1") {
        this.qualitativeReviewForm.isUploadReport = "0";
        this.qualitativeReviewForm.isAllStored = "1";
        this.$set(
          this.qualitativeReviewForm,
          "auditContent",
          "确认故障已恢复，工单返单合规，同意结单"
        );
      } else if (this.qualitativeReviewForm.auditResult == "0") {
        this.qualitativeReviewForm.isAllStored = "0";
        this.$set(this.qualitativeReviewForm, "auditContent", "");
      } else if (
        this.qualitativeReviewForm.isUploadReport == "1" &&
        this.qualitativeReviewForm.auditResult == "0"
      ) {
        this.$set(this.qualitativeReviewForm, "auditContent", "请上传故障报告");
      } else if (
        this.qualitativeReviewForm.isUploadReport == "0" &&
        this.qualitativeReviewForm.auditResult == "0"
      ) {
        this.$set(this.qualitativeReviewForm, "auditContent", "");
      }
    },
    changeUploadReport() {
      if (
        this.qualitativeReviewForm.isUploadReport == "1" &&
        this.qualitativeReviewForm.auditResult == "0"
      ) {
        this.$set(this.qualitativeReviewForm, "auditContent", "请上传故障报告");
      } else if (
        this.qualitativeReviewForm.isUploadReport == "0" &&
        this.qualitativeReviewForm.auditResult == "0"
      ) {
        this.$set(this.qualitativeReviewForm, "auditContent", "");
      }
    },
    downloadAppendixFile(data) {
      let param = {
        attId: data.id,
      };
      apiDownloadAppendixFile(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("文件下载成功");
          } else {
            this.$message.error("文件下载失败");
          }
        })
        .catch(error => {
          console.log(error);
          this.$message.error("文件下载失败");
        });
    },
    onReset() {
      // this.qualitativeReviewForm = {
      //   ...this.$options.data,
      //   woId: this.common.woId,
      //   workItemId: this.workItemId,
      //   processInstId: this.common.processInstId,
      //   processDefId: this.common.processDefId,
      // };
      // this.qualitativeReviewDetail();
      this.qualitativeReviewForm.faultStatus = null;
      this.qualitativeReviewForm.faultCate = null;
      this.qualitativeReviewForm.circuitFaultReason = null;
      this.qualitativeReviewForm.opticFiber = null;
      this.qualitativeReviewForm.faultRange = null;
      this.qualitativeReviewForm.maintainDept = null;
      this.qualitativeReviewForm.isProtected = null;
      this.qualitativeReviewForm.isProtectedValid = null;
      this.qualitativeReviewForm.isOverTime = null;
      this.qualitativeReviewForm.effectSystem = null;
      this.qualitativeReviewForm.effectCircuit = null;
      this.qualitativeReviewForm.falutReasonDesc = null;
      this.qualitativeReviewForm.falutComment = null;
      this.qualitativeReviewForm.eqpType = null;
      this.qualitativeReviewForm.eqpName = null;
      this.qualitativeReviewForm.cardType = null;
      this.qualitativeReviewForm.isPersonInRoom = null;
      this.qualitativeReviewForm.hasBackupPart = null;
      this.qualitativeReviewForm.supportSystem = null;
      this.qualitativeReviewForm.vendor = null;
      this.$set(this.qualitativeReviewForm, "auditResult", null);
      this.$set(this.qualitativeReviewForm, "isAllStored", "1");
      this.$set(this.qualitativeReviewForm, "isUploadReport", "0");
      this.$set(this.qualitativeReviewForm, "secondFalutSource", null);
      this.$set(this.qualitativeReviewForm, "busEffectRange", null);
      this.$set(this.qualitativeReviewForm, "auditContent", null);
      this.$set(this.qualitativeReviewForm, "faultProvince", null);
      this.importForm.relatedFilesFileList = [];
    },
    //光缆名称选择
    opticalCableSelect() {
      let jsonStr =
        '{"userId":"302785","username":"yangxm97","callSysId":"0001","callSysName":"电子运维"}';
      this.$nextTick(() => {
        document.querySelector("#fiberOpticCable").value = jsonStr;
        document.querySelector("#sub__fiberOpticCable").submit();
      });
    },
    //设备名称选择
    deviceSelect() {
      let jsonStr =
        '{"userId":"302785","username":"yangxm97","callSysId":"0001","callSysName":"电子运维"}';
      this.$nextTick(() => {
        document.querySelector("#device").value = jsonStr;
        document.querySelector("#sub__device").submit();
      });
    },
    second2Time(days) {
      return this.showTime(Math.abs(days));
    },
    showTime(val) {
      if (val) {
        if (val == 0) return "0秒";
        var time = "";
        var second = val % 60;
        var minute = parseInt(parseInt(val) / 60) % 60;
        time = second + "秒";
        if (minute >= 1) {
          time = minute + "分" + second + "秒";
        }
        var hour = parseInt(parseInt(val / 60) / 60) % 24;
        if (hour >= 1) {
          time = hour + "小时" + minute + "分" + second + "秒";
        }
        var day = parseInt(parseInt(parseInt(val / 60) / 60) / 24);
        if (day >= 1) {
          time = day + "天" + hour + "小时" + minute + "分" + second + "秒";
        }

        return time;
      } else {
        return "0秒";
      }
    },
    //资源系统回调方法初始化
    resourceBackInit() {
      window["__process_response_from_getDevice"] = value => {
        let form = this.qualitativeReviewForm;
        let result = JSON.parse(value);
        let objArray = JSON.parse(result?.objects);

        if (form.faultCate == "1") {
          let opticFiberArray = form.opticFiber
            ? form.opticFiber.split(",")
            : [];
          let effectSystemArray = form.effectSystem
            ? form.effectSystem.split(",")
            : [];
          let effectSystemArrayTemp = [];

          objArray.forEach(obj => {
            if (opticFiberArray.indexOf(obj.resName) < 0) {
              opticFiberArray.push(obj.resName);
            }

            effectSystemArrayTemp = JSON.parse(obj.affectedSystem);
            effectSystemArrayTemp.forEach(sys => {
              if (sys.sysName && effectSystemArray.indexOf(sys.sysName) < 0) {
                effectSystemArray.push(sys.sysName);
              }
            });
          });

          form.opticFiber = opticFiberArray.join(",");
          form.effectSystem = effectSystemArray.join(",");
          form.eqpName = "";
        } else {
          let eqpNameArray = form.eqpName ? form.eqpName.split(",") : [];

          objArray.forEach(obj => {
            if (eqpNameArray.indexOf(obj.resName) < 0) {
              eqpNameArray.push(obj.resName);
            }
          });

          form.eqpName = eqpNameArray.join(",");
          form.opticFiber = "";
          form.effectSystem = "";
        }
      };
    },
    toRoutePage() {
      let url = window.location.protocol + "//" + window.location.host;
      // let url = "http://************:8088"; //测试环境地址
      // let url = "http://************"; //生产环境地址
      if (this.qualitativeReviewForm.routeProcessInstId != null) {
        url +=
          "/ibpm-ui/#/contentTo?content=Y&needi=Y&no=detail&hideHeader=1&reqFrom=handle&processInstId=" +
          this.qualitativeReviewForm.routeProcessInstId +
          "&jobCode=" +
          this.qualitativeReviewForm.routeNo +
          "&globalUniqueID=" +
          sessionStorage.getItem("globalUniqueID");
      } else {
        url +=
          "/EOM_LIFE_ADJUST/#/transfer/draftform?hideHeader=1&hideTagsNav=1&actonShow=false&jobcode=" +
          this.qualitativeReviewForm.routeNo;
      }
      window.open(url);
    },
    networkTypeChange() {
      this.qualitativeReviewForm.circuitFaultReason = "";
    },
  },
};
</script>
<style lang="scss" scoped>
.qualitative-review {
  max-height: calc(90vh - 150px);
  min-height: 400px;
  margin: 0 4px;
  padding-left: 4px;
  padding-right: 8px;
  overflow-y: auto;
  .cus-card ::v-deep .el-card__header {
    padding: 11px 24px 10px;
    font-weight: 400;
    @include themify {
      background-color: themed("$--background-color-base");
    }
  }
  .fileName_style_download {
    margin-right: 3px;
    cursor: pointer;
    vertical-align: middle;
    div {
      display: inline-block;
      max-width: 120px;
      vertical-align: top;
    }
  }
  .fileName_style {
    margin-right: 3px;
    vertical-align: middle;
    div {
      display: inline-block;
      max-width: 120px;
      vertical-align: top;
    }
  }
}
</style>
