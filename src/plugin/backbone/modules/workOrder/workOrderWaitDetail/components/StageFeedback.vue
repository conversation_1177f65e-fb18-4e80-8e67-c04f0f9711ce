<template>
  <div>
    <el-form ref="stageBackForm" :model="stageBackForm" :rules="stageBackFormRules">
      <el-form-item
        label="反馈内容:"
        prop="processSuggestion"
        label-width="90px"
      >
      <!-- :rules="{
          required: true,
          message: '请填写反馈内容',
        }" -->
        <el-input
          type="textarea"
          :rows="3"
          placeholder="请输入内容"
          v-model="stageBackForm.processSuggestion"
          style="width: 270px"
          show-word-limit
          maxlength="1000"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="处理过程:" label-width="90px">
        <dict-select
          :value.sync="stageBackForm.opContent"
          :dictId="10048"
          style="width: 270px"
        />
      </el-form-item>
      <el-form-item
        label="是否自然灾害突发事件及其他:"
        prop="isNaturalDisaster" 
        :rules="{
          required: true,
          message: '请选择是否自然灾害突发事件及其他',
        }"
        label-width="215px">
              <el-radio-group
                v-model="stageBackForm.isNaturalDisaster"
                style="width: 100%"
              >
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: right">
      <el-button
        type="primary"
        @click="handleSubmit('stageBackForm')"
        v-loading.fullscreen.lock="stageBackFullscreenLoading"
        >提交</el-button
      >
      <el-button @click="onResetStageBackForm">重 置</el-button>
    </div>
  </div>
</template>
<script>
import { apiStageFeedBack } from "../api/CommonApi";
import DictSelect from "../../components/DictSelect.vue";
import {mixin} from "../../../../../../mixins"
export default {
  props: {
    common: Object,
  },
  name: "StageFeedback",
  components: { DictSelect },
  mixins:[mixin],
  data() {
    return {
      stageBackForm: {
        woId: "",
        processInstId: "",
        processDefId: "",
        workItemId: "",
        processNode: "",
        processSuggestion: "",
        opContent: "",
        isNaturalDisaster: "0",
      },
      stageBackFormRules: {
        processSuggestion: [
          {
            required: true,
            message: '请填写反馈内容',
          },
          {
            validator: this.checkLength,
            max: 1000,
            form: "stageBackForm",
            messsage: "已超过填写字数上限",
          },
        ],
      },
      stageBackFullscreenLoading: false,
    };
  },
  mounted() {
    this.stageBackForm.woId = this.common.woId;
    this.stageBackForm.processInstId = this.common.processInstId;
    this.stageBackForm.processDefId = this.common.processDefId;
    this.stageBackForm.workItemId = this.common.workItemId;
    this.stageBackForm.processNode = this.common.processNode;
  },
  methods: {
    handleSubmit(formName) {
      this.$refs[formName].validate((valid,a) => {
        if (this.uncheck(a)) {
          this.stageBackFullscreenLoading = true;
          apiStageFeedBack(this.stageBackForm)
            .then(res => {
              if (res.status == "0") {
                this.$message.success("阶段反馈完成");
                this.onResetStageBackForm();
                this.$emit("stageBackDialogClose");
              } else {
                this.$message.error("阶段反馈失败");
              }
              this.stageBackFullscreenLoading = false;
            })
            .catch(error => {
              console.log(error);
              this.$message.error("阶段反馈失败");
              this.stageBackFullscreenLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    onResetStageBackForm() {
      this.stageBackForm = {
        ...this.$options.data,
        woId: this.common.woId,
        workItemId: this.common.workItemId,
        processInstId: this.common.processInstId,
        processDefId: this.common.processDefId,
        processNode: this.common.processNode,
      };
    },
  },
};
</script>
