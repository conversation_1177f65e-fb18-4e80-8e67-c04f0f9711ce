<template>
  <el-card shadow="always" class="card" :body-style="{ padding: '10px 24px' }">
    <div class="header clearfix">
      <span class="header-title">关联重保信息</span>
      <div class="header-right">
        <el-button type="primary" size="mini" @click="getTableData"
          >刷新</el-button
        >
      </div>
    </div>

    <div class="content">
      <template>
        <el-table
          ref="table"
          :data="tableData"
          :border="false"
          stripe
          v-loading="tableLoading"
        >
          <el-table-column
            prop="circuitId"
            label="电路名称"
            width="240"
            show-overflow-tooltip
          >
            <!-- <template slot-scope="scope">
              <el-button type="text" @click="jumpCircuitPage(scope.row)">{{
                scope.row.circuitId
              }}</el-button>
            </template> -->
          </el-table-column>
          <el-table-column
            prop="clientName"
            label="客户名称"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="brandwidth"
            label="电路速率"
            width="120"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="aProvince"
            label="A端省分"
            width="120"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="zProvince"
            label="Z端省分"
            width="120"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="bizType"
            label="业务类型"
            width="150"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="sgNumber"
            label="关联重保工单编号"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <el-button type="text" @click="jumpCircuitPage(scope.row)">{{
                scope.row.sgNumber
              }}</el-button>
            </template>
          </el-table-column>
          <el-table-column
            prop="sgBegTime"
            label="重保开始时间"
            width="160"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="sgEndTime"
            label="重保结束时间"
            width="160"
            show-overflow-tooltip
          ></el-table-column>
        </el-table>
      </template>
      <template>
        <pagination
          ref="pagination"
          :total="form.total"
          :page.sync="form.pageNum"
          :limit.sync="form.pageSize"
          layout="->, total, sizes, prev, pager, next"
          @change="getTableData"
        />
      </template>
    </div>
  </el-card>
</template>

<script>
import moment from "moment";
import Pagination from "../../components/Pagination.vue";
import {
  apiCircuitAttachment,
  apiGetJumpCircuitInfo,
} from "../../api/RelationHeavyBao";
import crypto from "crypto";
export default {
  name: "RelationHeavyBao",
  props: {
    woId: String,
  },
  components: { Pagination },
  data() {
    return {
      tableData: [],
      form: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      tableLoading: false,
      circuitUrlV2: null,
      circuitUrlV3: null,
      systemCode: null,
      method: null,
    };
  },
  mounted() {
    this.getTableData();
    this.getJumpCircuitInfo();
  },
  methods: {
    getTableData() {
      this.tableLoading = true;

      let param = {
        woId: this.woId,
      };
      let param1 = {
        param1: JSON.stringify(param),
      };
      apiCircuitAttachment(param1)
        .then(res => {
          if (res.status == 0) {
            this.tableData = res?.data?.rows ?? [];
            this.form.total = res?.data?.totalElements ?? 0;
          }
          this.tableLoading = false;
        })
        .catch(err => {
          console.log(err);
          this.tableLoading = false;
        });
    },
    getJumpCircuitInfo() {
      apiGetJumpCircuitInfo()
        .then(res => {
          if (res.status == "0") {
            this.circuitUrlV2 = res?.data?.circuitUrlV2 ?? null;
            this.circuitUrlV3 = res?.data?.circuitUrlV3 ?? null;
            this.systemCode = res?.data?.systemCode ?? null;
            this.method = res?.data?.method ?? null;
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    jumpCircuitPage(data) {
      let nowTime = moment(new Date()).valueOf();
      var md5 = crypto.createHash("md5");
      md5.update(this.systemCode + nowTime + "@");
      var sign = md5.digest("hex");
      window.open(
        this.circuitUrlV2 +
          "?method=" +
          this.method +
          "&systemCode=" +
          this.systemCode +
          "&timestamp=" +
          nowTime +
          "&sign=" +
          sign +
          "&dispatchID=" +
          data.sgId
      );
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../assets/common.scss";
</style>
