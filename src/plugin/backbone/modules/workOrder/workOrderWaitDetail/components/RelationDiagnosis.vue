<template>
  <el-card shadow="always" class="card" :body-style="{ padding: '10px 24px' }">
    <div class="header clearfix">
      <span class="header-title">关联诊断</span>
      <template v-if="relationDiagnosis.problemClass">
        <evaluation
          :woId="woId"
          :relationDiagnosis="relationDiagnosis"
        ></evaluation>
      </template>
      <div class="header-right">
        <el-button type="primary" size="mini" @click="refreshRelationDiagnosis"
          >刷新</el-button
        >
      </div>
    </div>

    <div class="content" v-loading="contentLoading">
      <el-collapse v-if="ppResult" v-model="activeNames">
        <template>
          <el-collapse-item name="1">
            <span class="collapse-title" slot="title">诊断分析字段</span>
            <el-descriptions title="" class="descriptions">
              <el-descriptions-item label="预处理状态">{{
                relationDiagnosis.ppStatus
              }}</el-descriptions-item>
              <el-descriptions-item label="根因域">{{
                relationDiagnosis.ppCauseDomain
              }}</el-descriptions-item>
              <el-descriptions-item label="根因类型">{{
                relationDiagnosis.ppAlarmReason
              }}</el-descriptions-item>
              <el-descriptions-item label="设备型号">
                {{ relationDiagnosis.neModel }}
              </el-descriptions-item>
              <el-descriptions-item label="软件版本">{{
                relationDiagnosis.softwareVersion
              }}</el-descriptions-item>
              <el-descriptions-item label="硬件版本">
                {{ relationDiagnosis.hardwareVersion }}
              </el-descriptions-item>
              <el-descriptions-item label="根因位置" :span="3">
                <div
                  style="white-space: pre-wrap"
                  v-html="relationDiagnosis.ppCausePosition"
                ></div>
              </el-descriptions-item>
              <el-descriptions-item label="业务影响范围" :span="3">
                <div
                  style="white-space: pre-wrap"
                  v-html="relationDiagnosis.effectDesc"
                ></div>
              </el-descriptions-item>
              <el-descriptions-item label="预处理过程" :span="3">
                <div
                  style="white-space: pre-wrap"
                  v-html="relationDiagnosis.ppProcess"
                ></div>
              </el-descriptions-item>
              <el-descriptions-item label="预处理结果" :span="3">
                <div
                  style="white-space: pre-wrap"
                  v-html="relationDiagnosis.ppResult"
                ></div>
              </el-descriptions-item>
            </el-descriptions>
          </el-collapse-item>
          <el-collapse-item name="2" v-if="tabMenu.length > 0">
            <span class="collapse-title" slot="title">故障定位</span>
            <div style="display: inline-block; margin-left: 30px">
              <el-radio-group
                v-model="falultLocationTabVal"
                @change="handleClick"
                size="mini"
              >
                <el-radio-button
                  v-for="(tab, key) in tabMenu"
                  :key="key"
                  :label="tab"
                >
                  {{ tab.trim() }}
                </el-radio-button>
              </el-radio-group>
            </div>
            <div>
              <iframe
                v-if="topoSrc && isShowTopo"
                id="topo"
                title="topo"
                :src="topoSrc"
                width="100%"
                style="height: 500px; overflow-y: auto"
              ></iframe>
            </div>
            <div v-if="isShowOtdr" style="height: 500px; margin-top: 5px">
              <otdr-gis :otdrData="otdrData"></otdr-gis>
            </div>
            <div v-if="isShowFlowView" style="height: 600px">
              <flow-view :workOrderId="woId" type="pc"></flow-view>
            </div>
            <div v-if="isShowGuangshuai" style="height: 500px">
              <Guangshuai :guangshuaiData="guangshuaiData" :key="guangshuaiKey" style="height: 500px"></Guangshuai>
            </div>
          </el-collapse-item>
        </template>
      </el-collapse>
      <el-collapse v-else-if="isShowGuangshuai" v-model="activeNames">
        <template>
          <el-collapse-item name="2" v-if="tabMenu.length > 0">
            <span class="collapse-title" slot="title">故障定位</span>
            <div style="display: inline-block; margin-left: 30px">
              <el-radio-group
                v-model="falultLocationTabVal"
                @change="handleClick"
                size="mini"
              >
                <el-radio-button
                  v-for="(tab, key) in tabMenu"
                  :key="key"
                  :label="tab"
                >
                  {{ tab.trim() }}
                </el-radio-button>
              </el-radio-group>
            </div>
            <div v-if="isShowGuangshuai" style="height: 500px">
              <Guangshuai :guangshuaiData="guangshuaiData" :key="guangshuaiKey" style="height: 500px"></Guangshuai>
            </div>
          </el-collapse-item>
        </template>
      </el-collapse>
      <!--    骨干网指令查询   -->
      <!-- v-if="
          basicWorkOrderData.sheetNo &&
          relationDiagnosis.transSegCuid &&
          relationDiagnosis.transSegCuid != '无'
        " -->
      <template>
        <command-query
          :basicWorkOrderData="basicWorkOrderData"
          :relationDiagnosis="relationDiagnosis"
          :transRelationData="transRelationData"
          v-if="showTransRelation"
        >
        </command-query>
      </template>
    </div>
  </el-card>
</template>

<script>
import OtdrGis from "../../../otdr/components/OtdrGis.vue";

import Evaluation from "@/plugin/backbone/components/Evaluate/Evaluation.vue";
import CommandQuery from "@/plugin/backbone/components/CommandQueryBackbone/CommandQueryBackbone.vue";
// 光衰趋势图
import Guangshuai from "@/components/Guangshuai/Line.vue";

import {
  apiGetRelationDiagnosis,
  apiTopoIsShow,
  apiGetOtdrData,
  apiGetTransRelationDiagnosis,
  apiGuangshuai,
} from "../api/CommonApi";
import FlowView from "../../../flow/views/FlowView.vue";
import { getFlowConnects } from "../../../flow/flow-api";
export default {
  components: { OtdrGis, FlowView, Evaluation, CommandQuery, Guangshuai },
  name: "RelationDiagnosis",
  props: {
    woId: String,
    ppResult: {
      type: String,
      default: null,
    },
    basicWorkOrderData: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      relationDiagnosis: {
        ppStatus: "",
        ppCauseDomain: "",
        ppAlarmReason: "",
        neModel: "",
        softwareVersion: "",
        hardwareVersion: "",
        ppCausePosition: "",
        ppProcess: "",
        ppResult: "",
      },
      activeNames: [],
      topoSrc: null,
      contentLoading: false,
      timer: null,
      showHeader: false,
      tableData: [],
      tableLoading: false,
      falultLocationTabVal: "",
      tabMenu: [],
      isShowOtdr: false,
      isShowFlowView: false,
      isShowTopo: false,
      flowJson: null,
      completeConnects: null,
      otdrData: {},
      // 节点map
      taskMap: {},
      // 气泡显示控制
      popoverWidth: 480,
      popoverVisible: false,
      popoverStyle: {
        "border-radius": "10px",
        background: "#4ECC9F",
        transition: "all .2s",
      },
      hoverTask: null,
      hoverElement: null,
      transRelationData: {},
      showTransRelation: false,
      isShowGuangshuai: false,
      guangshuaiData: [],
      guangshuaiKey: Date.now(),
    };
  },
  mounted() {
    let self = this;
    if (self.ppResult) {
      this.getRelationDiagnosis();
    }

    this.getTransRelation();

    // 获取光衰数据
    this.getGuangshuai();
  },
  watch: {
    // 监听tabMenu变化，当有图表时确保故障定位折叠面板展开
    tabMenu: {
      handler(newVal) {
        if (newVal.length > 0 && !this.activeNames.includes("2")) {
          // 只展开故障定位折叠面板
          this.activeNames.push("2");

          // 更新key值，强制重新渲染组件
          this.guangshuaiKey = Date.now();
        }
      },
      deep: true
    }
  },
  destroyed() {
    this.clearTimer();
  },
  methods: {
    refreshRelationDiagnosis() {
      let self = this;
      if (self.ppResult) {
        this.getRelationDiagnosis();
      }
      //实时光功率是否显示调用
      this.getTransRelation();

      // 刷新光衰趋势图数据
      this.getGuangshuai();
    },
    getRelationDiagnosis() {
      this.contentLoading = true;
      let param = {
        woId: this.woId,
      };
      apiGetRelationDiagnosis(param)
        .then(res => {
          if (res.status == "0") {
            let self = this;
            if (res.data.rows.length > 0) {
              self.relationDiagnosis = res?.data?.rows?.[0] ?? {};
              let ppResult = self.relationDiagnosis?.ppResult ?? "";
              this.relationDataAssign(ppResult);

              //获取OTDR
              let ppPointLocation = res.data.rows[0].ppPointLocation;
              this.getOtdrData(
                res.data.rows[0].alarmStaId,
                res.data.rows[0].alarmCreateTime
              )
                .then(resOtdr => {
                  if (resOtdr) {
                    if (ppPointLocation && ppPointLocation != "无") {
                      this.otdrShowGet();
                    }
                  } else {
                    //展示老GIS的链接
                    this.showOldGisUrl(ppResult);
                  }
                })
                .finally(() => {
                  let topoParam = {
                    alarmId: res.data.rows[0].alarmStaId,
                    eventTime: res.data.rows[0].alarmCreateTime,
                  };
                  //获取拓扑图信息
                  apiTopoIsShow(topoParam)
                    .then(topoRes => {
                      if (topoRes.status == 200) {
                        self.topoSrc = res.data.rows[0].url + topoRes.data.url;
                        if (self.tabMenu.indexOf("拓扑图") == -1) {
                          self.tabMenu.push("拓扑图");
                        }
                        if (
                          !self.falultLocationTabVal ||
                          self.falultLocationTabVal == "拓扑图"
                        ) {
                          self.falultLocationTabVal = "拓扑图";
                        }
                        if (!self.isShowOtdr && !self.isShowFlowView) {
                          this.isShowTopo = true;
                        }
                      }
                    })
                    .catch(error => {
                      console.log(error);
                    })
                    .finally(() => {
                      //获取诊断流程图信息
                      this.getAdxInfo();
                    });
                });
            } else {
              self.activeNames = [];
            }
            if (!this._isDestroyed) {
              this.timer = setTimeout(this.getRelationDiagnosis, 60000 * 3);
            }
          }
          this.contentLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.contentLoading = false;
        });
    },
    getTransRelation() {
      let self = this;
      let param = {
        woId: this.woId,
      };
      apiGetTransRelationDiagnosis(param)
        .then(res => {
          if (res.status == "0") {
            self.transRelationData = res?.data?.rows?.[0] ?? {};
            self.showTransRelation = true;
          }
        })
        .catch(error => {
          console.log(error);
          self.showTransRelation = true;
        });
    },
    relationDataAssign(ppResult) {
      let self = this;
      if (ppResult) {
        self.activeNames = ["1", "2"];
      } else {
        self.activeNames = [];
      }
      if (
        ppResult.indexOf("断点段落文字描述：") != "-1" &&
        ppResult.indexOf("断点gis的查看地址：") != "-1"
      ) {
        let frontContent = ppResult.split("断点段落文字描述：")[0];
        let endContent = ppResult.split("断点段落文字描述：")[1];
        self.relationDiagnosis.ppResult =
          frontContent +
          "断点段落文字描述：<span style='font-weight:700'>" +
          endContent.split("断点gis的查看地址：")[0] +
          "</span>";
      } else if (ppResult.indexOf("断点段落文字描述：") != "-1") {
        let frontContent = ppResult.split("断点段落文字描述：")[0];
        let endContent = ppResult.split("断点段落文字描述：")[1];
        self.relationDiagnosis.ppResult =
          frontContent +
          "断点段落文字描述：<span style='font-weight:700'>" +
          endContent +
          "</span>";
      } else if (ppResult.indexOf("断点gis的查看地址：") != "-1") {
        self.relationDiagnosis.ppResult = "";
      }
    },
    otdrShowGet() {
      if (!this.isShowTopo && !this.isShowFlowView) {
        this.isShowOtdr = true;
      }
      if (this.tabMenu.indexOf("OTDR断点图") == -1) {
        this.tabMenu.push("OTDR断点图");
      }
      if (
        !this.falultLocationTabVal ||
        this.falultLocationTabVal == "OTDR断点图"
      ) {
        this.falultLocationTabVal = "OTDR断点图";
      }

      // 确保故障定位折叠面板展开
      if (!this.activeNames.includes("2")) {
        this.activeNames.push("2");
      }

      // 更新key值，强制重新渲染组件
      this.guangshuaiKey = Date.now();
    },
    //获取诊断流程图信息
    getAdxInfo() {
      getFlowConnects(this.woId)
        .then(res => {
          let adxStatus = res?.data?.adxStatus ?? "NO";
          if (adxStatus == "YES") {
            //展示诊断流程图
            if (this.tabMenu.indexOf("诊断流程图") == -1) {
              this.tabMenu.push("诊断流程图");
            }
            if (
              !this.falultLocationTabVal ||
              this.falultLocationTabVal == "诊断流程图"
            ) {
              this.falultLocationTabVal = "诊断流程图";
            }
            if (!this.isShowOtdr && !this.isShowTopo) {
              this.isShowFlowView = true;
            }

            // 确保故障定位折叠面板展开
            if (!this.activeNames.includes("2")) {
              this.activeNames.push("2");
            }

            // 更新key值，强制重新渲染组件
            this.guangshuaiKey = Date.now();
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    //获取OTDR数据
    getOtdrData(ysAlarmStaId, ysAlarmCreateTime) {
      return new Promise((resolve, reject) => {
        let formData = new FormData();
        formData.append("alarmUniqueId", ysAlarmStaId);
        formData.append("endTime", ysAlarmCreateTime);
        apiGetOtdrData(formData)
          .then(res => {
            this.otdrData = res?.data ?? null;
            resolve(this.otdrData);
          })
          .catch(error => {
            console.log(error);
            resolve();
          });
      });
    },
    clearTimer() {
      clearTimeout(this.timer);
      this.timer = null;
    },

    getImageUrl(val) {
      return require("../assets/img/" + val + ".png");
    },

    handleClick(index) {
      if (index == "诊断流程图") {
        this.isShowFlowView = true;
        this.isShowOtdr = false;
        this.isShowTopo = false;
        this.isShowGuangshuai = false;
      } else if (index == "OTDR断点图") {
        this.isShowOtdr = true;
        this.isShowFlowView = false;
        this.isShowTopo = false;
        this.isShowGuangshuai = false;
      } else if (index == "拓扑图") {
        this.isShowTopo = true;
        this.isShowFlowView = false;
        this.isShowOtdr = false;
        this.isShowGuangshuai = false;
      } else if (index == "光衰趋势图") {
        this.isShowGuangshuai = true;
        this.isShowTopo = false;
        this.isShowFlowView = false;
        this.isShowOtdr = false;
        // 刷新光衰趋势图数据
        this.getGuangshuai();
      }
    },

    showOldGisUrl(ppResult) {
      let self = this;
      if (
        ppResult.indexOf("断点段落文字描述：") != "-1" &&
        ppResult.indexOf("断点gis的查看地址：") != "-1"
      ) {
        let frontContent = ppResult.split("断点段落文字描述：")[0];
        let endContent = ppResult.split("断点段落文字描述：")[1];
        self.relationDiagnosis.ppResult =
          frontContent +
          "断点段落文字描述：<span style='font-weight:700'>" +
          endContent.split("断点gis的查看地址：")[0] +
          "</span>断点gis的查看地址：<a target='_blank' style='color: #c43c43;' href=" +
          endContent.split("断点gis的查看地址：")[1] +
          ">" +
          endContent.split("断点gis的查看地址：")[1] +
          "</a>";
      } else if (ppResult.indexOf("断点段落文字描述：") != "-1") {
        let frontContent = ppResult.split("断点段落文字描述：")[0];
        let endContent = ppResult.split("断点段落文字描述：")[1];
        self.relationDiagnosis.ppResult =
          frontContent +
          "断点段落文字描述：<span style='font-weight:700'>" +
          endContent +
          "</span>";
      } else if (ppResult.indexOf("断点gis的查看地址：") != "-1") {
        self.relationDiagnosis.ppResult =
          ppResult.split("断点gis的查看地址：")[0] +
          "断点gis的查看地址：<a target='_blank' style='color: #c43c43;' href=" +
          ppResult.split("断点gis的查看地址：")[1] +
          ">" +
          ppResult.split("断点gis的查看地址：")[1] +
          "</a>";
      }
    },

    // 获取光衰数据
    getGuangshuai() {
      // 更新key值，强制重新渲染组件
      this.guangshuaiKey = Date.now();
      apiGuangshuai({
        woId: this.woId,
      })
        .then(res => {
          // 使用模拟数据进行测试
          // const mockData = require("./guangshuai.json");
          // res.data = mockData.data;

          if (res?.data?.trendStatus == "YES") {
            this.isShowGuangshuai = true;
            console.log(res?.data?.trendStatus == "YES",res?.data?.trendData);
            // 确保光衰趋势图按钮在其他三种图之后显示
            // 首先移除已有的光衰趋势图按钮（如果存在）
            const index = this.tabMenu.indexOf("光衰趋势图");
            if (index !== -1) {
              this.tabMenu.splice(index, 1);
            }

            // 重新排序标签菜单，确保光衰趋势图在最后
            const orderedTabs = [];

            // 首先添加其他三种图（如果存在）
            if (this.tabMenu.indexOf("OTDR断点图") !== -1) {
              orderedTabs.push("OTDR断点图");
            }
            if (this.tabMenu.indexOf("拓扑图") !== -1) {
              orderedTabs.push("拓扑图");
            }
            if (this.tabMenu.indexOf("诊断流程图") !== -1) {
              orderedTabs.push("诊断流程图");
            }

            // 添加其他可能存在的标签（除了已添加的和光衰趋势图）
            this.tabMenu.forEach(tab => {
              if (tab !== "OTDR断点图" && tab !== "拓扑图" && tab !== "诊断流程图" && tab !== "光衰趋势图") {
                orderedTabs.push(tab);
              }
            });

            // 最后添加光衰趋势图
            orderedTabs.push("光衰趋势图");

            // 更新标签菜单
            this.tabMenu = orderedTabs;

            // 如果没有其他图表显示，则默认显示光衰趋势图
            if (!this.isShowOtdr && !this.isShowTopo && !this.isShowFlowView) {
              this.falultLocationTabVal = "光衰趋势图";
            }

            // 确保故障定位折叠面板展开
            if (!this.activeNames.includes("2")) {
              this.activeNames.push("2");
            }

            this.guangshuaiData = res?.data?.trendData || [];
          }
        })
        .catch(err => {
          console.log(err);
        });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../assets/common.scss";
::v-deep .el-collapse-item {
  .el-collapse-item__header {
    background-color: #fafafa;
    padding-left: 20px;
  }
}
::v-deep .el-collapse-item__content {
  line-height: unset;
}
</style>
