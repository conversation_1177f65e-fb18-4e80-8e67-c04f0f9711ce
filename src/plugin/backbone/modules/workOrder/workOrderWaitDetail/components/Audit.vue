<template>
  <div>
    <el-form ref="auditForm" :model="auditForm" :rules="auditFormRules" label-width="100px">
      <el-form-item
        label="审核结果:"
        prop="opResult"
        :rules="{
          required: true,
          message: '请选择审核结果',
        }"
      >
        <el-radio-group v-model="auditForm.opResult" >
          <!-- style="width: 260px" -->
          <el-radio label="1">同意</el-radio>
          <el-radio label="0">拒绝</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="审核意见:" prop="processSuggestion">
        <el-input
          type="textarea"
          :rows="2"
          placeholder="请填写审核意见"
          v-model="auditForm.processSuggestion"
          show-word-limit
          maxlength="1000"
        >
        <!-- style="width: 260px" -->
        </el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: right">
      <el-button
        type="primary"
        @click="handleAuditSubmit('auditForm')"
        v-loading.fullscreen.lock="auditFullScreenLoading"
        >提 交</el-button
      >
      <el-button @click="onResetAudit">重 置</el-button>
    </div>
  </div>
</template>
<script>
import { apiAudit, apiItCloudAudit, apiCommCloudAudit } from "../api/CommonApi";
import {mixin} from "../../../../../../mixins"
export default {
  name: "Audit",
  props: {
    common: Object,
    opContent: Number,
  },
  mixins: [mixin],
  data() {
    return {
      auditForm: {
        opResult: null,
        processSuggestion: null,
      },
      auditFormRules: {
        processSuggestion: [
          {
            validator: this.checkLength,
            max: 1000,
            form: "auditForm",
            messsage: "已超过填写字数上限",
          },
        ],
      },
      auditFullScreenLoading: false,
    };
  },
  mounted() {
    console.log(this.common.professionalTypeName);
  },
  watch: {
    opContent(val) {
      this.opContent = val;
    },
  },
  methods: {
    handleAuditSubmit(formName) {
      this.$refs[formName].validate((valid,a) => {
        if (this.uncheck(a)) {
          this.auditFullScreenLoading = true;
          let param = {
            opContent: this.opContent,
            woId: this.common.woId,
            processInstId: this.common.processInstId,
            processDefId: this.common.processDefId,
            workItemId: this.common.workItemId,
            processNode: this.common.processNode,
            opResult: this.auditForm.opResult,
            processSuggestion: this.auditForm.processSuggestion,
          };
          if (this.common.professionalTypeName == "传输网") {
            apiAudit(param)
              .then(res => {
                if (res.status == "0") {
                  this.$message.success(res.msg);
                  if (this.opContent == 1) {
                    this.$emit("closeDialogPendingReview");
                  } else if (this.opContent == 2) {
                    this.$emit("closeDialogSolutionToHangAudit");
                  }
                } else {
                  this.$message.error(res.msg);
                }
                this.auditFullScreenLoading = false;
              })
              .catch(error => {
                console.log(error);
                this.auditFullScreenLoading = false;
                this.$message.error("审核提交失败");
              });
          } else if (this.common.professionalTypeName == "IT云设备") {
            apiItCloudAudit(param)
              .then(res => {
                if (res.status == "0") {
                  this.$message.success(res.msg);
                  if (this.opContent == 1) {
                    this.$emit("closeDialogPendingReview");
                  } else if (this.opContent == 2) {
                    this.$emit("closeDialogSolutionToHangAudit");
                  }
                } else {
                  this.$message.error(res.msg);
                }
                this.auditFullScreenLoading = false;
              })
              .catch(error => {
                console.log(error);
                this.auditFullScreenLoading = false;
                this.$message.error("审核提交失败");
              });
          } else if (this.common.professionalTypeName == "通信云") {
            apiCommCloudAudit(param)
              .then(res => {
                if (res.status == "0") {
                  this.$message.success(res.msg);
                  if (this.opContent == 1) {
                    this.$emit("closeDialogPendingReview");
                  } else if (this.opContent == 2) {
                    this.$emit("closeDialogSolutionToHangAudit");
                  }
                } else {
                  this.$message.error(res.msg);
                }
                this.auditFullScreenLoading = false;
              })
              .catch(error => {
                console.log(error);
                this.auditFullScreenLoading = false;
                this.$message.error("审核提交失败");
              });
          }
        } else {
          return false;
        }
      });
    },
    onResetAudit() {
      this.auditForm = {
        ...this.$options.data,
      };
    },
  },
};
</script>
