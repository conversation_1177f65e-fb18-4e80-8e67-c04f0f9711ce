<template>
  <div class="hang-up">
    <el-form
      ref="hangUpForm"
      :model="hangUpForm"
      :rules="hangUpPersonRules"
      label-width="90px"
    >
      <el-form-item :label="personName" prop="hangUpPerson">
        <div style="width: 250px">{{ hangUpForm.hangUpPerson }}</div>
      </el-form-item>
      <el-form-item :label="dynamicTime" prop="hangUpTime">
        <div style="width: 250px">
          {{ hangUpForm.hangUpTime }}
        </div>
      </el-form-item>
      <el-form-item label="支撑材料:">
        <div style="width: 360px">
          <el-tag
            class="fileName_style"
            closable
            v-for="(item, index) in hangUpForm.hangUpFilesList"
            :key="index"
            @close="close(item)"
            :title="item.name"
          >
            <div class="text-truncate">{{ item.name }}</div>
          </el-tag>
          <el-button size="mini" type="primary" @click="attachmentBrowse"
            >+上传附件</el-button
          >
        </div>
      </el-form-item>
      <el-form-item label="操作意见:" prop="processSuggestion">
        <el-input
          type="textarea"
          :rows="2"
          placeholder="请填写操作意见"
          v-model="hangUpForm.processSuggestion"
          style="width: 360px"
          show-word-limit
          maxlength="1000"
        >
        </el-input>
      </el-form-item>
    </el-form>

    <div slot="footer" style="padding: 10px 20px; text-align: right">
      <el-button
        type="primary"
        @click="handleHangUpSubmit('hangUpForm')"
        v-loading.fullscreen.lock="hangUpFullScreenLoading"
        >提 交</el-button
      >
      <el-button @click="onResetHangUp">重 置</el-button>
    </div>
    <el-dialog
      width="420px"
      title="附件选择"
      :visible.sync="hangUpFileDialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <file-upload
        @change="changeFileData"
        @cancel="closeAttachmentDialog"
      ></file-upload>
    </el-dialog>
  </div>
</template>
<script>
import moment from "moment";
import { mapGetters } from "vuex";
import {
  apiHangUp,
  apiItCloudHangUp,
  apiCommCloudHangUp,
} from "../api/CommonApi";
import FileUpload from "../../components/FileUpload.vue";
import { mixin } from "../../../../../../mixins";
export default {
  name: "HangUp",
  props: {
    common: Object,
    opType: Number,
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  components: { FileUpload },
  mixins: [mixin],
  data() {
    return {
      personName: "",
      dynamicTime: "",
      //挂起
      hangUpForm: {
        hangUpPerson: null,
        hangUpTime: null,
        processSuggestion: null,
        hangUpFilesList: [],
        fileName: null,
      },
      hangUpPersonRules: {
        processSuggestion: [
          {
            required: true,
            message: "请填写操作意见",
          },
          {
            validator: this.checkLength,
            max: 1000,
            form: "hangUpForm",
            messsage: "已超过填写字数上限",
          },
        ],
      },
      hangUpFileDialogVisible: false,
      hangUpFullScreenLoading: false,
    };
  },
  created() {},
  mounted() {
    this.hangUpForm.hangUpPerson = this.userInfo.realName;
    this.hangUpForm.hangUpTime = moment().format("YYYY-MM-DD HH:mm:ss");
    if (this.opType == 1) {
      this.personName = "挂起人:";
      this.dynamicTime = "挂起时间:";
    } else if (this.opType == 2) {
      this.personName = "解挂人:";
      this.dynamicTime = "解挂时间:";
    }
    console.log(this.common.professionalTypeName);
  },
  methods: {
    handleHangUpSubmit(formName) {
      this.$refs[formName].validate((valid,a) => {
        if (this.uncheck(a)) {
          this.hangUpFullScreenLoading = true;
          let formData = new FormData();
          if (this.hangUpForm.hangUpFilesList.length > 0) {
            for (let item of this.hangUpForm.hangUpFilesList) {
              formData.append("files", item.raw);
            }
          } else {
            formData.append("files", "");
          }
          formData.append("opType", this.opType);
          let jsonParam = {
            woId: this.common.woId,
            processInstId: this.common.processInstId,
            processDefId: this.common.processDefId,
            workItemId: this.common.workItemId,
            processNode: this.common.processNode,
            processSuggestion: this.hangUpForm.processSuggestion,
          };
          formData.append("jsonParam", JSON.stringify(jsonParam));
          if (this.common.professionalTypeName == "传输网") {
            apiHangUp(formData)
              .then(res => {
                if (res.status == "0") {
                  this.$message.success(res.msg);

                  this.onResetHangUp();
                  this.$emit("closeDialogHangUp");
                } else {
                  this.$message.error(res.msg);
                }
                this.hangUpFullScreenLoading = false;
              })
              .catch(error => {
                console.log(error);
                if (this.opType == 1) {
                  this.$message.error("挂起申请失败");
                } else if (this.opType == 2) {
                  this.$message.error("解挂申请失败");
                }
                this.hangUpFullScreenLoading = false;
              });
          } else if (this.common.professionalTypeName == "IT云设备") {
            apiItCloudHangUp(formData)
              .then(res => {
                if (res.status == "0") {
                  this.$message.success(res.msg);

                  this.onResetHangUp();
                  this.$emit("closeDialogHangUp");
                } else {
                  this.$message.error(res.msg);
                }
                this.hangUpFullScreenLoading = false;
              })
              .catch(error => {
                console.log(error);
                if (this.opType == 1) {
                  this.$message.error("挂起申请失败");
                } else if (this.opType == 2) {
                  this.$message.error("解挂申请失败");
                }
                this.hangUpFullScreenLoading = false;
              });
          } else if (this.common.professionalTypeName == "通信云") {
            apiCommCloudHangUp(formData)
              .then(res => {
                if (res.status == "0") {
                  this.$message.success(res.msg);

                  this.onResetHangUp();
                  this.$emit("closeDialogHangUp");
                } else {
                  this.$message.error(res.msg);
                }
                this.hangUpFullScreenLoading = false;
              })
              .catch(error => {
                console.log(error);
                if (this.opType == 1) {
                  this.$message.error("挂起申请失败");
                } else if (this.opType == 2) {
                  this.$message.error("解挂申请失败");
                }
                this.hangUpFullScreenLoading = false;
              });
          }
        } else {
          return false;
        }
      });
    },
    onResetHangUp() {
      this.hangUpForm = {
        ...this.$options.data,
        hangUpPerson: this.userInfo.realName,
        hangUpTime: moment().format("YYYY-MM-DD HH:mm:ss"),
      };
    },
    attachmentBrowse() {
      this.hangUpFileDialogVisible = true;
    },
    changeFileData(data) {
      this.hangUpForm.fileName = data.fileName;
      this.hangUpForm.hangUpFilesList = data.attachmentFileList;
      this.hangUpFileDialogVisible = false;
    },
    closeAttachmentDialog() {
      this.hangUpFileDialogVisible = false;
    },
    close(tag) {
      this.hangUpForm.hangUpFilesList.splice(
        this.hangUpForm.hangUpFilesList.indexOf(tag),
        1
      );
    },
  },
};
</script>
<style lang="scss" scoped>
.hang-up {
  .fileName_style {
    margin-right: 3px;
    vertical-align: middle;

    div {
      display: inline-block;
      max-width: 320px;
      vertical-align: top;
    }
  }
}
</style>
