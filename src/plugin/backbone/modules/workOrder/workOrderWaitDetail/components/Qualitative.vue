<template>
  <div class="qualitative">
    <el-form ref="qualitativeForm" :inline="false" class="demo-form-inline" :model="qualitativeForm" label-width="130px"
      :rules="qualitativeFormRule">
      <el-card shadow="never" header="故障定性信息" :body-style="{ padding: '20px 8px' }" class="cus-card">
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item label="故障所属专业:" prop="professionalType" :rules="{
              required: true,
              message: '请选择故障所属专业',
            }">
              <dict-select :value.sync="qualitativeForm.professionalType" :dictId="10002" placeholder=""
                style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障发生时间:" prop="alarmCreateTime" required>
              {{ qualitativeForm.alarmCreateTime }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障通知时间:" prop="sheetCreateTime" required>
              {{ qualitativeForm.sheetCreateTime }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障代通时间:" prop="lastClearTime" :rules="{
              required: true,
              message: '请选择内容',
            }">
              <el-date-picker v-model="qualitativeForm.lastClearTime" type="datetime" placeholder="请选择故障代通时间"
                value-format="yyyy-MM-dd HH:mm:ss" clearable style="width: 100%" @change="computerFaultGenerationAter"
                :picker-options="pickerOptions" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障代通历时:">
              {{ second2Time(qualitativeForm.lastClearDuration) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障结束时间:" prop="faultEndTime">
              <!-- :rules="{
                required: true,
                message: '请选择内容',
              }" -->
              <el-date-picker v-model="qualitativeForm.faultEndTime" type="datetime" placeholder="请选择故障结束时间"
                value-format="yyyy-MM-dd HH:mm:ss" clearable style="width: 100%" @change="computerFaultTreatmentTime"
                :picker-options="pickerOptions" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障处理历时:" required>
              {{ second2Time(this.qualitativeForm.faultDuration) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否为故障省:" prop="isFaultProvince">
              <el-radio-group v-model="qualitativeForm.isFaultProvince">
                <el-radio :disabled="this.agentProvinceCount == 1" label="1">是</el-radio>
                <el-radio :disabled="this.agentProvinceCount == 1" label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障发生地区:" prop="faultRegion" :rules="{
              required: true,
              message: '请选择内容',
            }">
              <el-select style="width: 100%" v-model="qualitativeForm.faultRegion">
                <el-option v-for="(item, i) in faultRegionOptions" :key="i" :label="item.name" :value="item.name">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障处理部门:" required>
              {{ qualitativeForm.dept }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="处理人:" prop="person" required>
              {{ qualitativeForm.person }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="挂起历时:" required>
              {{ second2Time(this.qualitativeForm.suspendDuration) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障处理净历时:" required>
              {{ second2Time(this.qualitativeForm.processDuration) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否影响业务:" prop="isEffectBusiness" :rules="[
              {
                required: qualitativeForm.eqpType == '6' ? true : false,
                message: '请选择是否影响业务',
              },
            ]">
              <el-radio-group v-model="qualitativeForm.isEffectBusiness" @change="faultCateChange">
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="影响范围:" v-if="qualitativeForm.isEffectBusiness == '1'" prop="effectRange">
              <el-input type="textarea" :rows="2" placeholder="请输入内容" v-model="qualitativeForm.effectRange"
                style="width: 640px" show-word-limit maxlength="255">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card shadow="never" header="故障专业信息" :body-style="{ padding: '20px 8px' }" style="margin-top: 20px"
        class="cus-card">
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item label="故障状态:" prop="faultStatus" :rules="{
              required: true,
              message: '请选择故障状态',
            }">
              <dict-select :value.sync="qualitativeForm.faultStatus" :dictId="10017" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="网络类型:" prop="networkType" :rules="{
              required: true,
              message: '请选择网络类型',
            }">
              <dict-select :value.sync="qualitativeForm.networkType" :dictId="10063" style="width: 100%"
                @change="networkTypeChange()" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障分类:" prop="faultCate" :rules="{
              required: true,
              message: '请选择故障分类',
            }">
              <dict-select :value.sync="qualitativeForm.faultCate" :dictId="10019" style="width: 100%"
                @change="faultCateChange('faultCate')" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20" v-if="qualitativeForm.faultCate == '1'"
          key="faultCateOne">
          <el-col :span="8">
            <el-form-item label="故障原因:" :rules="{
              required: true,
              message: '请选择故障原因',
            }" prop="circuitFaultReason">
              <dict-select :value.sync="qualitativeForm.circuitFaultReason" :dictId="faultReasonCode"
                style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="光缆名称:" prop="opticFiber">
              <!-- 线路选择输入框 -->
              <UniversalPopupWindowEquipmentOrLines :currentSearchQueryType="2"
                @changeCableEquipInputData="changeCableEquipInputData" :cableEquipInputData="qualitativeForm.opticFiber"
                @event-select-data="eventEquipLineSelectData" :title="'光缆'" />
              <!-- <el-input
                v-model.trim="qualitativeForm.opticFiber"
                style="width: 100%"
                maxlength="255"
              >
                <el-button
                  style="
                    background: #b50b14;
                    border-color: #b50b14;
                    color: #fff;
                    padding: 9px 20px;
                    top: -0.5px;
                    position: relative;
                  "
                  slot="append"
                  @click="opticalCableSelect"
                  >选择</el-button
                >
              </el-input> -->
              <form id="sub__fiberOpticCable" name="sub__fiberOpticCable" hidden="true" method="post"
                action="/resweb_jituan/union/resAssign.out?method=selectFiberSection&requestJson={}" target="_blank">
                <input type="hidden" name="fiberOpticCable" id="fiberOpticCable" />
              </form>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障区间:" prop="faultRange">
              <el-input v-model.trim="qualitativeForm.faultRange" style="width: 100%"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="维护主体:" prop="maintainDept" :rules="{
              required: true,
              message: '请选择维护主体',
            }">
              <dict-select :value.sync="qualitativeForm.maintainDept" :dictId="10021" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否保护:" prop="isProtected" :rules="{
              required: true,
              message: '请选择是否保护',
            }">
              <el-radio-group v-model="qualitativeForm.isProtected" @change="faultCateChange">
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="保护是否生效:" :rules="{
              required: true,
              message: '请选择保护是否生效',
            }" prop="isProtectedValid">
              <el-radio-group v-model="qualitativeForm.isProtectedValid">
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否超时:">
              <span v-if="qualitativeForm.isOverTime == 0">否</span><span
                v-else-if="qualitativeForm.isOverTime == 1">是</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="相关附件:" prop="attachmentName">
              <el-tag class="fileName_style_download" closable v-for="(item, index) in fdFileXlArr" :key="index"
                @close="closeAndDeleteFile(item)" @click="downloadAppendixFile(item)" :title="item.name">
                <div class="text-truncate">{{ item.name }}</div>
              </el-tag>
              <el-tag class="fileName_style" closable v-for="item in importForm.relatedFilesFileList" :key="item.name"
                @close="closeFile(item)" :title="item.name">
                <div class="text-truncate">{{ item.name }}</div>
              </el-tag>
              <el-button size="mini" type="primary" @click="relatedFilesBrowse">+上传附件</el-button>
            </el-form-item>
          </el-col>
          <el-col :span="8"> </el-col>
          <el-col :span="24">
            <el-form-item label="受影响系统:" prop="effectSystem">
              <el-input type="textarea" :rows="2" placeholder="请输入内容" v-model.trim="qualitativeForm.effectSystem"
                style="width: 100%" show-word-limit maxlength="255">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="受影响电路:" prop="effectCircuit">
              <el-input type="textarea" :rows="2" placeholder="请输入内容" v-model.trim="qualitativeForm.effectCircuit"
                style="width: 100%" show-word-limit maxlength="255">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="故障原因描述:" prop="falutReasonDesc">
              <el-input maxlength="500" show-word-limit type="textarea" :rows="2" :autosize="{ minRows: 3, maxRows: 6 }"
                placeholder="请输入内容" v-model.trim="qualitativeForm.falutReasonDesc" style="width: 100%">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注:" prop="falutComment">
              <el-input type="textarea" :rows="2" placeholder="请输入内容" v-model.trim="qualitativeForm.falutComment"
                style="width: 100%" show-word-limit maxlength="255">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item v-if="isRouteAdjustShow" label="是否路由调整:" prop="isRouteAdjust">
              <el-radio-group v-model="qualitativeForm.isRouteAdjust" style="width: 100%" disabled>
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item v-if="qualitativeForm.isRouteAdjust == 1 && isRouteNoShow" label="路由调整单号:">
              <el-link :underline="false" @click="toRoutePage" type="primary" style="color: #409eff">{{
                qualitativeForm.routeNo }}</el-link>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20" v-if="qualitativeForm.faultCate == '2'"
          key="faultCateTwo">
          <el-col :span="8">
            <el-form-item label="设备类型:" prop="eqpType" :rules="{
              required: true,
              message: '请选择设备类型',
            }">
              <dict-select :value.sync="qualitativeForm.eqpType" :dictId="10046" style="width: 100%" placeholder="请选择内容"
                @change="faultCateChange('eqTypeChange')" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="设备名称:" prop="eqpName">
              <!-- 设备选择输入框 -->
              <UniversalPopupWindowEquipmentOrLines :currentSearchQueryType="3"
                @changeCableEquipInputData="changeCableEquipInputData" :cableEquipInputData="qualitativeForm.eqpName"
                @event-select-data="eventEquipLineSelectData" :title="'设备'" />
              <!-- <el-input v-model.trim="qualitativeForm.eqpName" style="width: 100%" maxlength="255">
                <el-button style="
                    background: #b50b14;
                    border-color: #b50b14;
                    color: #fff;
                    padding: 9px 20px;
                    top: -0.5px;
                    position: relative;
                  " slot="append" @click="deviceSelect">选择</el-button>
              </el-input> -->
              <form id="sub__device" name="sub__device" hidden="true" method="post"
                action="/resweb_jituan/union/resAssign.out?method=selectEquipment" target="_blank">
                <input type="hidden" name="device" id="device" />
              </form>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障原因:" :rules="{
              required: qualitativeForm.faultCate == '2' ? true : false,
              message: '请选择故障原因',
            }" prop="eqpFaultReason">
              <dict-select :value.sync="qualitativeForm.eqpFaultReason" :dictId="faultReasonDictId" style="width: 100%"
                placeholder="请选择内容" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障区间:" prop="faultRange">
              <el-input v-model.trim="qualitativeForm.faultRange" style="width: 100%" maxlength="255">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="维护主体:" :rules="{
              required: true,
              message: '请选择维护主体',
            }" prop="maintainDept">
              <dict-select :value.sync="qualitativeForm.maintainDept" :dictId="10021" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="板卡类型:" prop="cardType" v-if="
              qualitativeForm.faultCate == '2' &&
              (qualitativeForm.eqpType == '1' ||
                qualitativeForm.eqpType == '2' ||
                qualitativeForm.eqpType == '3' ||
                qualitativeForm.eqpType == '4' ||
                qualitativeForm.eqpType == '5')
            " :rules="{
                required:
                  qualitativeForm.faultCate == '2' &&
                    (qualitativeForm.eqpType == '1' ||
                      qualitativeForm.eqpType == '2' ||
                      qualitativeForm.eqpType == '3' ||
                      qualitativeForm.eqpType == '4' ||
                      qualitativeForm.eqpType == '5')
                    ? true
                    : false,
                message: '请选择板卡类型',
              }">
              <dict-select :value.sync="qualitativeForm.cardType" :dictId="cardTypeDictId" style="width: 100%"
                @change="faultCateChange" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否有人机房:" prop="isPersonInRoom" :rules="{
              required: true,
              message: '请选择是否有人机房',
            }">
              <el-radio-group v-model="qualitativeForm.isPersonInRoom" style="width: 100%" @change="faultCateChange">
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否有备件:" prop="hasBackupPart" :rules="{
              required: true,
              message: '请选择是否有备件',
            }">
              <el-radio-group v-model="qualitativeForm.hasBackupPart" @change="faultCateChange">
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否保护:" prop="isProtected" :rules="{
              required: true,
              message: '请选择是否保护',
            }">
              <el-radio-group v-model="qualitativeForm.isProtected" @change="faultCateChange">
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="保护是否生效:" prop="isProtectedValid" :rules="{
              required: true,
              message: '请选择保护是否生效',
            }">
              <el-radio-group v-model="qualitativeForm.isProtectedValid" @change="faultCateChange">
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否超时:">
              <span v-if="qualitativeForm.isOverTime == 0">否</span><span v-else>是</span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="承载业务系统:" prop="supportSystem">
              <el-input type="textarea" :rows="2" placeholder="请输入内容" v-model.trim="qualitativeForm.supportSystem"
                style="width: 100%" show-word-limit maxlength="255">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="受影响系统:" prop="effectSystem">
              <el-input type="textarea" :rows="2" placeholder="请输入内容" v-model.trim="qualitativeForm.effectSystem"
                style="width: 100%" show-word-limit maxlength="255">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="受影响电路:" prop="effectCircuit">
              <el-input type="textarea" :rows="2" placeholder="请输入内容" v-model.trim="qualitativeForm.effectCircuit"
                style="width: 100%" show-word-limit maxlength="255">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="故障厂家:" prop="vendor" :rules="{
              required: true,
              message: '请选择故障厂家',
            }">
              <el-radio-group v-model="qualitativeForm.vendor" style="width: 100%">
                <el-radio v-for="(item, i) in vendorOptions" :key="i" :label="item.dictCode">{{ item.dictName
                  }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="故障原因描述:" prop="falutReasonDesc">
              <el-input maxlength="500" show-word-limit type="textarea" :rows="2" :autosize="{ minRows: 3, maxRows: 6 }"
                placeholder="请输入内容" v-model.trim="qualitativeForm.falutReasonDesc" style="width: 100%">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注:" prop="falutComment">
              <el-input type="textarea" :rows="2" placeholder="请输入内容" v-model.trim="qualitativeForm.falutComment"
                style="width: 100%" show-word-limit maxlength="255">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="相关附件:" prop="attachmentName">
              <el-tag class="fileName_style_download" closable v-for="(item, index) in fdFileDlArr" :key="index"
                @close="closeAndDeleteFileDl(item)" @click="downloadAppendixFile(item)" :title="item.name">
                <div class="text-truncate">{{ item.name }}</div>
              </el-tag>
              <el-tag class="fileName_style" closable v-for="(item, index) in importForm.relatedFilesFileList"
                :key="index" @close="closeFile(item)" :title="item.name">
                <div class="text-truncate">{{ item.name }}</div>
              </el-tag>
              <el-button size="mini" type="primary" @click="relatedFilesBrowse">+上传附件</el-button>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item v-if="isRouteAdjustShow" label="是否路由调整:" prop="isRouteAdjust">
              <el-radio-group v-model="qualitativeForm.isRouteAdjust" style="width: 100%" disabled>
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item v-if="qualitativeForm.isRouteAdjust == 1 && isRouteNoShow" label="路由调整单号:">
              <el-link :underline="false" @click="toRoutePage" type="primary" style="color: #409eff">{{
                qualitativeForm.routeNo }}</el-link>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: center">
      <el-button type="primary" @click="handleSubmit('qualitativeForm')"
        v-loading.fullscreen.lock="qualitativeFullscreenLoading">提交</el-button>
      <el-button @click="onReset">重 置</el-button>
    </div>
    <el-dialog width="420px" title="附件选择" :visible.sync="relatedFilesDialogVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" append-to-body>
      <file-upload @change="changeFileData" @cancel="closeAttachmentDialog"></file-upload>
    </el-dialog>
  </div>
</template>
<script>
import moment from "moment";
import { mapGetters } from "vuex";
import DictSelect from "../../components/DictSelect.vue";
import {
  apiQualitativeDetail,
  apiQualitative,
  apiGetFaultArea,
  apiGetAgentProvince,
} from "../api/CommonApi";
import { apiDict, apiGetOrgInfo } from "../../api/CommonApi";
import FileUpload from "../../components/FileUpload.vue";
import { config } from "../api/TroubleshootingTime";
import {
  apiDownloadAppendixFile,
  apiDeleteFdFile,
} from "../../workOrderWaitDetail/api/CommonApi";
import { mixin } from "../../../../../../mixins";
import { faultReasonEnumOnlineDate } from "../api/OnlineDateTime";
import UniversalPopupWindowEquipmentOrLines from './universalPopupWindowEquipmentOrLines.vue';
export default {
  name: "Qualitative",
  props: {
    common: Object,
    workItemId: [String, Number],
    isUploadReport: [String, Number],
  },
  components: { DictSelect, FileUpload, UniversalPopupWindowEquipmentOrLines },
  computed: {
    ...mapGetters(["userInfo"]),
    faultReasonCode() {
      console.log("faultReasonEnumOnlineDate", faultReasonEnumOnlineDate);
      let seconds = moment(new Date(), "YYYY-MM-DD HH:mm:ss").diff(
        moment(faultReasonEnumOnlineDate, "YYYY-MM-DD HH:mm:ss"),
        "seconds"
      );
      if (
        seconds >= 0 &&
        this.qualitativeForm.networkType == 2 &&
        this.qualitativeForm.faultCate == 1
      ) {
        return 811037;
      }
      return 10020;
    },
  },
  mixins: [mixin],
  data() {
    var validLastClearTime = (rule, value, callback) => {
      if (this.qualitativeForm.lastClearTime) {
        let seconds3 = moment(
          this.qualitativeForm.lastClearTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(moment(new Date(), "YYYY-MM-DD HH:mm:ss"), "seconds");
        let seconds4 = moment(
          this.qualitativeForm.lastClearTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.common.failureTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        if (seconds3 > 0 || seconds4 <= 0) {
          callback(
            new Error(
              "当前时间>=故障代通时间>=故障发生时间，请重新检查后选择正确时间"
            )
          );
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    var validFaultEndTime = (rule, value, callback) => {
      if (value === "" || value === null) {
        callback(new Error("请选择故障结束时间"));
      } else {
        if (this.qualitativeForm.faultEndTime) {
          // let seconds3 = moment(
          //   this.qualitativeForm.faultEndTime,
          //   "YYYY-MM-DD HH:mm:ss"
          // ).diff(moment(new Date(), "YYYY-MM-DD HH:mm:ss"), "seconds");
          // let seconds4 = moment(
          //   this.qualitativeForm.faultEndTime,
          //   "YYYY-MM-DD HH:mm:ss"
          // ).diff(
          //   moment(this.common.failureTime, "YYYY-MM-DD HH:mm:ss"),
          //   "seconds"
          // );
          // if (seconds3 > 0 || seconds4 <= 0) {
          //   callback(
          //     new Error(
          //       "当前时间>=故障结束时间>=故障发生时间，请重新检查后选择正确时间"
          //     )
          //   );
          // } else {
          //   callback();
          // }
          let secondsProcessingDuration = moment(
            this.qualitativeForm.faultEndTime,
            "YYYY-MM-DD HH:mm:ss"
          ).diff(
            moment(this.common.failureTime, "YYYY-MM-DD HH:mm:ss"),
            "seconds"
          );
          let secondsProcessingDurationJing =
            secondsProcessingDuration - this.qualitativeForm.suspendDuration;
          if (secondsProcessingDuration <= 0) {
            callback(new Error("故障处理历时<=0，请重新检查后选择正确时间！"));
          } else if (secondsProcessingDurationJing <= 0) {
            callback(
              new Error("故障处理净历时<=0，请重新检查后选择正确时间！")
            );
          } else {
            callback();
          }
        } else {
          callback();
        }
      }
    };
    var validAttachment = (rule, value, callback) => {
      if (this.isUploadReport == "是") {
        if (
          this.importForm.relatedFilesFileList.length > 0 ||
          this.fdFileXlArr.length > 0 ||
          this.fdFileDlArr.length > 0
        ) {
          if (this.fdFileXlArr.length > 0) {
            this.qualitativeForm.attachmentName = "xlFile";
          } else {
            this.qualitativeForm.attachmentName = "dlFile";
          }

          callback();
        } else {
          this.qualitativeForm.attachmentName = null;
          callback(new Error("请添加相关附件"));
        }
      } else {
        callback();
      }
    };
    return {
      qualitativeForm: {
        woId: null,
        workItemId: null,
        processInstId: null,
        processDefId: null,
        professionalType: null,
        actionName: null,
        alarmCreateTime: null,
        sheetCreateTime: null,
        lastClearTime: null,
        lastClearDuration: 0, //故障代通历时(单位秒)
        faultEndTime: null,
        faultDuration: 0, //故障处理历时(单位秒)
        faultRegion: null,
        dept: null,
        person: null,
        suspendDuration: 0, //挂起历时
        processDuration: null, //故障处理净历时
        isEffectBusiness: null,
        effectRange: null,
        isSiteOffline: null,
        siteOfflineReason: null,
        faultStatus: null,
        networkType: null,
        faultCate: null,
        circuitFaultReason: null,
        opticFiber: "",
        faultRange: null,
        maintainDept: null,
        isProtected: null,
        isProtectedValid: null,
        isOverTime: null,
        relatedFiles: null,
        relatedFilesVirtual: null,
        effectSystem: "",
        effectCircuit: null,
        falutReasonDesc: null,
        falutComment: null,
        //设备故障
        eqpType: null,
        eqpName: "",
        eqpFaultReason: null,
        cardType: null,
        isPersonInRoom: null,
        hasBackupPart: null,
        isEffectBiz: null,
        supportSystem: null,
        vendor: null,
        isRouteAdjust: null,
        routeProcessInstId: null,
        routeNo: null,
        areaCode: null,
        category: null,
        linkId: null,
        appendix: null,
        isFaultProvince: "",
      },
      //关联附件
      relatedFilesDialogVisible: false,
      importForm: {
        relatedFilesFileList: [],
      },
      vendorOptions: [],
      qualitativeFullscreenLoading: false,
      faultRegionOptions: [],
      fdFileXlArr: [],
      fdFileDlArr: [],
      faultReasonDictId: 0, //故障原因 多字典ID
      cardTypeDictId: 0, //板卡类型 多字典ID
      userData: null,
      timeRange: "00:00:00 - 23:59:59",
      createTimeDate: "2022-10-20",
      pickerOptions: {
        disabledDate: this.disabledDate,
        selectableRange: this.timeRange,
      },
      startTimeRange: "",
      startTimeRangeByFaultEndTime: "",

      qualitativeFormRule: {
        lastClearTime: [{ validator: validLastClearTime, trigger: "blur" }],
        faultEndTime: [{ validator: validFaultEndTime, required: true }],
        attachmentName: [
          {
            validator: validAttachment,
            required: this.isUploadReport == "是" ? true : false,
            trigger: "change",
          },
        ],
        isFaultProvince: [
          {
            required: true,
            trigger: "change",
            message: "请选择是否为故障省",
          },
        ],
        falutReasonDesc: [
          {
            required: true,
            message: "请填写故障原因描述",
          },
          {
            validator: this.checkLength,
            max: 500,
            form: "qualitativeForm",
            messsage: "已超过填写字数上限",
          },
        ],
        falutComment: [
          {
            validator: this.checkLength,
            max: 255,
            form: "qualitativeForm",
            messsage: "已超过填写字数上限",
          },
        ],
        opticFiber: [
          {
            required: true,
            message: "光缆名称不能为空",
          },
          {
            validator: this.checkLength,
            max: 255,
            form: "qualitativeForm",
            messsage: "已超过填写字数上限",
          },
        ],
        eqpName: [
          {
            validator: this.checkLength,
            max: 255,
            form: "qualitativeForm",
            messsage: "已超过填写字数上限",
          },
        ],
        faultRange: [
          {
            required: true,
            message: "请输入故障区间",
          },
          {
            validator: this.checkLength,
            max: 255,
            form: "qualitativeForm",
            messsage: "已超过填写字数上限",
          },
        ],
        supportSystem: [
          {
            required: true,
            message: "承载业务系统不能为空",
          },
          {
            validator: this.checkLength,
            max: 255,
            form: "qualitativeForm",
            messsage: "已超过填写字数上限",
          },
        ],
        effectSystem: [
          {
            validator: this.checkLength,
            max: 255,
            form: "qualitativeForm",
            messsage: "已超过填写字数上限",
          },
        ],
        effectCircuit: [
          {
            validator: this.checkLength,
            max: 255,
            form: "qualitativeForm",
            messsage: "已超过填写字数上限",
          },
        ],
        effectRange: [
          {
            required: true,
            message: "请填写影响范围",
            trigger: "blur",
          },
          {
            validator: this.checkLength,
            max: 255,
            form: "evaluation",
            messsage: "已超过填写字数上限",
          },
        ],
      },
      isRouteNoShow: true,
      isRouteAdjustShow: true,
      agentProvinceCount: 0,
    };
  },
  watch: {
    "qualitativeForm.lastClearTime": {
      handler(val) {
        let valDate = val.split(" ")[0];
        let originDate = this.common.failureTime.split(" ")[0];
        let now = new Date();
        now.setMinutes(now.getMinutes() + 1, now.getSeconds(), 0);
        valDate = valDate.replace(/\-/g, "/");
        originDate = originDate.replace(/\-/g, "/");
        let year = now.getFullYear();
        let month = now.getMonth() + 1;
        let date = now.getDate();
        let nowDate =
          year + "/" + this.addZero(month) + "/" + this.addZero(date);
        let valDateUnix = Date.parse(valDate);
        let originDateUnix = Date.parse(originDate);
        let nowDateUnix = Date.parse(nowDate);

        let array = this.qualitativeForm.alarmCreateTime.split(" ");
        let createTime = array[1];
        let hour = now.getHours();
        let minute = now.getMinutes();
        let second = now.getSeconds();
        let nowTime =
          this.addZero(hour) +
          ":" +
          this.addZero(minute) +
          ":" +
          this.addZero(second);
        if (valDateUnix == originDateUnix && originDateUnix == nowDateUnix) {
          this.timeRange = createTime + " - " + nowTime;
          this.pickerOptions.selectableRange = this.timeRange;
        } else if (
          valDateUnix == originDateUnix &&
          originDateUnix < nowDateUnix
        ) {
          this.timeRange = createTime + " - 23:59:59";
          this.pickerOptions.selectableRange = this.timeRange;
        } else if (valDateUnix > originDateUnix && valDateUnix < nowDateUnix) {
          this.timeRange = "00:00:00 - 23:59:59";
          this.pickerOptions.selectableRange = this.timeRange;
        } else if (valDateUnix > originDateUnix && valDateUnix == nowDateUnix) {
          this.timeRange = "00:00:00 - " + nowTime;
          this.pickerOptions.selectableRange = this.timeRange;
        }
        console.log(this.timeRange);
      },
      deep: true,
    },
    "qualitativeForm.faultEndTime": {
      handler(val) {
        let valDate = val.split(" ")[0];
        let originDate = this.common.failureTime.split(" ")[0];
        let now = new Date();
        now.setMinutes(now.getMinutes() + 1, now.getSeconds(), 0);
        valDate = valDate.replace(/\-/g, "/");
        originDate = originDate.replace(/\-/g, "/");
        let year = now.getFullYear();
        let month = now.getMonth() + 1;
        let date = now.getDate();
        let nowDate =
          year + "/" + this.addZero(month) + "/" + this.addZero(date);
        let valDateUnix = Date.parse(valDate);
        let originDateUnix = Date.parse(originDate);
        let nowDateUnix = Date.parse(nowDate);

        let array = this.qualitativeForm.alarmCreateTime.split(" ");
        let createTime = array[1];
        let hour = now.getHours();
        let minute = now.getMinutes();
        let second = now.getSeconds();
        let nowTime =
          this.addZero(hour) +
          ":" +
          this.addZero(minute) +
          ":" +
          this.addZero(second);
        if (valDateUnix == originDateUnix && originDateUnix == nowDateUnix) {
          this.timeRange = createTime + " - " + nowTime;
          this.pickerOptions.selectableRange = this.timeRange;
        } else if (
          valDateUnix == originDateUnix &&
          originDateUnix < nowDateUnix
        ) {
          this.timeRange = createTime + " - 23:59:59";
          this.pickerOptions.selectableRange = this.timeRange;
        } else if (valDateUnix > originDateUnix && valDateUnix < nowDateUnix) {
          this.timeRange = "00:00:00 - 23:59:59";
          this.pickerOptions.selectableRange = this.timeRange;
        } else if (valDateUnix > originDateUnix && valDateUnix == nowDateUnix) {
          this.timeRange = "00:00:00 - " + nowTime;
          this.pickerOptions.selectableRange = this.timeRange;
        }
      },
      deep: true,
    },
  },
  mounted() {
    this.qualitativeForm.alarmCreateTime = this.common.failureTime;
    this.qualitativeForm.sheetCreateTime = this.common.failureInformTime;
    this.qualitativeForm.person = this.userInfo.realName;
    this.qualitativeForm.workItemId = this.workItemId;
    this.qualitativeForm.woId = this.common.woId;
    this.qualitativeForm.processInstId = this.common.processInstId;
    this.qualitativeForm.processDefId = this.common.processDefId;
    this.getVendorOptions();
    this.qualitativeDetail();
    this.getOrgInfo();
    this.userData = JSON.parse(this.userInfo.attr2);
    this.resourceBackInit(this.qualitativeForm);
    let array = this.qualitativeForm.alarmCreateTime.split(" ");
    this.createTimeDate = array[0];
    this.getAgentProvince();
  },
  methods: {
    // 光缆或者设备选择后传回
    eventEquipLineSelectData(val, type) {
      console.log(val, "===", type);
      if (type == '光缆') {
        if (this.qualitativeForm.opticFiber && this.qualitativeForm.opticFiber.length > 0) {
          this.qualitativeForm.opticFiber += '、';
        }
        val.forEach((item, i) => {
          if (i == val.length - 1) {
            this.qualitativeForm.opticFiber = ((this.qualitativeForm.opticFiber && this.qualitativeForm.opticFiber.length > 0) ? this.qualitativeForm.opticFiber : "") + item["name"];
          } else {
            this.qualitativeForm.opticFiber =
              ((this.qualitativeForm.opticFiber && this.qualitativeForm.opticFiber.length > 0) ? this.qualitativeForm.opticFiber : "") + item["name"] + "、";
          }
        });
      } else if (type == '设备') {
        if (this.qualitativeForm.eqpName && this.qualitativeForm.eqpName.length > 0) {
          this.qualitativeForm.eqpName += '、';
        }
        val.forEach((item, i) => {
          if (i == val.length - 1) {
            this.qualitativeForm.eqpName = ((this.qualitativeForm.eqpName && this.qualitativeForm.eqpName.length > 0) ? this.qualitativeForm.eqpName : "") + item["name"];
          } else {
            this.qualitativeForm.eqpName =
              ((this.qualitativeForm.eqpName && this.qualitativeForm.eqpName.length > 0) ? this.qualitativeForm.eqpName : "") + item["name"] + "、";
          }
        });
      }
    },
    // 光缆或设备输入修改返回修改
    changeCableEquipInputData(val, type) {
      if (type == '光缆') {
        this.qualitativeForm.opticFiber = val;
      } else if (type == '设备') {
        this.qualitativeForm.eqpName = val;
      }
    },
    getAgentProvince() {
      apiGetAgentProvince(this.common.woId)
        .then(res => {
          if (res.status == "0") {
            this.agentProvinceCount = res?.data?.count ?? 0;
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    //时间对比
    disabledDate(time) {
      let now = moment(this.createTimeDate);
      let a = moment(now).subtract(1, "days");
      let today = moment(a).valueOf();
      return time.getTime() <= today || time.getTime() - 8.64e6 >= Date.now();
    },
    addZero(s) {
      return s < 10 ? "0" + s : s;
    },
    dealDisabledDate(time) {
      let self = this;
      let beginDate = moment(self.common.failureInformTime).format(
        "YYYY-MM-DD"
      );
      let endDate = moment(Date.now()).format("YYYY-MM-DD");
      let arr = this.getAllDays(beginDate, endDate);
      const timeFormat = moment(time).format("YYYY-MM-DD");
      if (arr.indexOf(timeFormat) >= 0) {
        return false;
      }
      return true;
    },
    dealDisabledDateDt(time) {
      let self = this;
      let beginDate;
      let endDate;
      if (self.qualitativeForm.faultEndTime) {
        beginDate = moment(self.common.failureInformTime).format("YYYY-MM-DD");
        endDate = moment(self.qualitativeForm.faultEndTime).format(
          "YYYY-MM-DD"
        );
      } else {
        beginDate = moment(self.common.failureInformTime).format("YYYY-MM-DD");
        endDate = moment(Date.now()).format("YYYY-MM-DD");
      }
      let arr = this.getAllDays(beginDate, endDate);
      const timeFormat = moment(time).format("YYYY-MM-DD");
      if (arr.indexOf(timeFormat) >= 0) {
        return false;
      }
      return true;
    },
    getAllDays(begin_date, end_date) {
      const errArr = [],
        resultArr = [],
        dateReg = /^[2]\d{3}-[01]\d-[0123]\d$/;

      if (
        typeof begin_date !== "string" ||
        begin_date === "" ||
        !dateReg.test(begin_date)
      ) {
        return errArr;
      }

      if (
        typeof end_date !== "string" ||
        end_date === "" ||
        !dateReg.test(end_date)
      ) {
        return errArr;
      }

      try {
        const beginTimestamp = Date.parse(new Date(begin_date)),
          endTimestamp = Date.parse(new Date(end_date));

        // 开始日期小于结束日期
        if (beginTimestamp > endTimestamp) {
          return errArr;
        }

        // 开始日期等于结束日期
        if (beginTimestamp === endTimestamp) {
          resultArr.push(begin_date);
          return resultArr;
        }

        let tempTimestamp = beginTimestamp,
          tempDate = begin_date;

        // 新增日期是否和结束日期相等， 相等跳出循环
        while (tempTimestamp !== endTimestamp) {
          resultArr.push(tempDate);

          // 增加一天
          tempDate = moment(tempTimestamp).add(1, "d").format("YYYY-MM-DD");

          // 将增加时间变为时间戳
          tempTimestamp = Date.parse(new Date(tempDate));
        }

        // 将最后一天放入数组
        resultArr.push(end_date);
        return resultArr;
      } catch (err) {
        return errArr;
      }
    },
    getOrgInfo() {
      apiGetOrgInfo()
        .then(res => {
          if (res.status == "0") {
            this.qualitativeForm.areaCode = res?.data?.orgInfo?.areaCode ?? "";
            this.qualitativeForm.category = res?.data?.category ?? "";
            this.getFaultAreaOptions();
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    getVendorOptions() {
      let param = {
        dictTypeCode: "10047",
      };
      apiDict(param)
        .then(res => {
          if (res.code == "200") {
            this.vendorOptions = res?.data ?? [];
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    getFaultAreaOptions() {
      let param = {
        areaCode: this.qualitativeForm.areaCode,
        category: this.qualitativeForm.category,
      };
      apiGetFaultArea(param)
        .then(res => {
          if (res.status == "0") {
            this.faultRegionOptions = res?.data ?? [];
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    qualitativeDetail() {
      let param = {
        opType: 1,
        workItemId: this.qualitativeForm.workItemId,
        woId: this.qualitativeForm.woId,
      };
      apiQualitativeDetail(param)
        .then(res => {
          if (res.status == "0") {
            this.qualitativeForm = res?.data ?? {};
            this.eqTypeChange();
            if (res.data.appendix) {
              if (this.qualitativeForm.faultCate == "1") {
                this.fdFileXlArr = JSON.parse(res.data.appendix);
              } else if (this.qualitativeForm.faultCate == "2") {
                //设备
                this.fdFileDlArr = JSON.parse(res.data.appendix);
              }
            }
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    relatedFilesBrowse() {
      this.relatedFilesDialogVisible = true;
    },
    changeFileData(data) {
      this.qualitativeForm.relatedFiles = data.fileName;
      this.importForm.relatedFilesFileList = data.attachmentFileList;
      this.relatedFilesDialogVisible = false;
    },

    closeAttachmentDialog() {
      this.relatedFilesDialogVisible = false;
    },
    computerFaultGenerationAter() {
      let days = moment(
        this.qualitativeForm.lastClearTime,
        "YYYY-MM-DD HH:mm:ss"
      ).diff(
        moment(this.qualitativeForm.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
        "seconds"
      );
      this.qualitativeForm.lastClearDuration = days;
      this.faultCateChange();
    },
    second2Time(days) {
      //return this.showTimeNew(Math.abs(days));
      return this.showTimeNew(days);
    },
    computerFaultTreatmentTime() {
      let days = moment(
        this.qualitativeForm.faultEndTime,
        "YYYY-MM-DD HH:mm:ss"
      ).diff(
        moment(this.qualitativeForm.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
        "seconds"
      );
      this.qualitativeForm.faultDuration = days;

      //故障处理净历时  （故障结束时间-故障发生时间）-挂起历时(秒) 换算成-天-小时-分钟-秒
      if (this.qualitativeForm.suspendDuration == 0) {
        this.qualitativeForm.processDuration = this.qualitativeForm.faultDuration;
      } else {
        let seconds = moment(
          this.qualitativeForm.faultEndTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.qualitativeForm.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        this.qualitativeForm.processDuration =
          seconds - this.qualitativeForm.suspendDuration;
      }
      this.faultCateChange();
    },
    handleSubmit(formName) {
      this.$refs[formName].validate((valid, a) => {
        if (this.uncheck(a)) {
          this.qualitativeFullscreenLoading = true;
          this.$set(this.qualitativeForm, "actionName", "定性");
          let formData = new FormData();
          if (this.importForm.relatedFilesFileList.length > 0) {
            for (let item of this.importForm.relatedFilesFileList) {
              formData.append("files", item.raw);
            }
          } else {
            formData.append("files", "");
          }

          formData.append("jsonParam", JSON.stringify(this.qualitativeForm));
          apiQualitative(formData)
            .then(res => {
              if (res.status == "0") {
                this.$message.success("提交定性成功");
                this.onReset();
                this.$emit("qualitativeSubmit", res.data);
              } else {
                this.$message.error("提交定性失败");
              }
              this.qualitativeFullscreenLoading = false;
            })
            .catch(error => {
              console.log(error);
              this.$message.error("提交定性失败");
              this.qualitativeFullscreenLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    faultCateChange(type) {
      if (type == "eqTypeChange") {
        this.eqTypeChange("eqTypeChange");
      } else if (type == "faultCate") {
        this.resetFaultCateChange();
      }
      if (this.qualitativeForm.faultCate == 1) {
        //线路故障
        if (this.qualitativeForm.isProtected == 0) {
          //无保护
          for (let item of config.proNotProtect) {
            if (item.pro.indexOf(this.userData.provinceName) != "-1") {
              let seconds = this.dealTimeout();
              if (seconds > item.timeLimit * 60) {
                this.$set(this.qualitativeForm, "isOverTimeShow", "是");
                this.$set(this.qualitativeForm, "isOverTime", 1);
              } else {
                this.$set(this.qualitativeForm, "isOverTimeShow", "否");
                this.$set(this.qualitativeForm, "isOverTime", 0);
              }
            }
          }
        } else if (this.qualitativeForm.isProtected == 1) {
          //有保护
          for (let item2 of config.proProtect) {
            if (item2.pro.indexOf(this.userData.provinceName) != "-1") {
              let seconds2 = this.dealTimeout();
              if (seconds2 > item2.timeLimit * 60) {
                this.$set(this.qualitativeForm, "isOverTimeShow", "是");
                this.$set(this.qualitativeForm, "isOverTime", 1);
              } else {
                this.$set(this.qualitativeForm, "isOverTimeShow", "否");
                this.$set(this.qualitativeForm, "isOverTime", 0);
              }
            }
          }
        }
      } else if (this.qualitativeForm.faultCate == 2) {
        //设备故障
        // if (
        //   (this.qualitativeForm.eqpType == "4" ||
        //     this.qualitativeForm.eqpType == "2" ||
        //     this.qualitativeForm.eqpType == "5") &&
        //   this.qualitativeForm.cardType == "3" &&
        //   this.qualitativeForm.isProtected == "1" &&
        //   this.qualitativeForm.isProtectedValid == "1"
        // ) {
        //   for (let item4 of config.proEquipmentRule1) {
        //     if (item4.pro.indexOf(this.userData.provinceName) != "-1") {
        //       let seconds6 = this.dealTimeout();
        //       if (seconds6 > item4.timeLimit * 60) {
        //         this.$set(this.qualitativeForm, "isOverTimeShow", "是");
        //         this.$set(this.qualitativeForm, "isOverTime", 1);
        //       } else {
        //         this.$set(this.qualitativeForm, "isOverTimeShow", "否");
        //         this.qualitativeForm.isOverTime = 0;
        //       }
        //     }
        //   }
        // } else if (
        //   this.qualitativeForm.eqpType == "6" &&
        //   this.qualitativeForm.isEffectBusiness == "0"
        // ) {
        //   let seconds5 = this.dealTimeout();
        //   if (seconds5 > 720 * 60) {
        //     this.$set(this.qualitativeForm, "isOverTimeShow", "是");
        //     this.$set(this.qualitativeForm, "isOverTime", 1);
        //   } else {
        //     this.$set(this.qualitativeForm, "isOverTimeShow", "否");
        //     this.qualitativeForm.isOverTime = 0;
        //   }
        // } else if (
        //   this.qualitativeForm.isPersonInRoom == "1" &&
        //   this.qualitativeForm.hasBackupPart == "1"
        // ) {
        //   let seconds3 = this.dealTimeout();
        //   if (seconds3 > 60 * 60) {
        //     this.$set(this.qualitativeForm, "isOverTimeShow", "是");
        //     this.$set(this.qualitativeForm, "isOverTime", 1);
        //   } else {
        //     this.$set(this.qualitativeForm, "isOverTimeShow", "否");
        //     this.$set(this.qualitativeForm, "isOverTime", 0);
        //   }
        // } else if (
        //   (this.qualitativeForm.isPersonInRoom == "1" &&
        //     this.qualitativeForm.hasBackupPart == "0") ||
        //   (this.qualitativeForm.isPersonInRoom == "0" &&
        //     this.qualitativeForm.hasBackupPart == "1") ||
        //   (this.qualitativeForm.isPersonInRoom == "0" &&
        //     this.qualitativeForm.hasBackupPart == "0")
        // ) {
        //   for (let item3 of config.proEquipmentRule4) {
        //     if (item3.pro.indexOf(this.userData.provinceName) != "-1") {
        //       let seconds4 = this.dealTimeout();
        //       if (seconds4 > item3.timeLimit * 60) {
        //         this.$set(this.qualitativeForm, "isOverTimeShow", "是");
        //         this.qualitativeForm.isOverTime = 1;
        //       } else {
        //         this.$set(this.qualitativeForm, "isOverTimeShow", "否");
        //         this.qualitativeForm.isOverTime = 0;
        //       }
        //     }
        //   }
        // }
        if (
          this.qualitativeForm.eqpType == "6" &&
          this.qualitativeForm.isEffectBusiness == "0"
        ) {
          let seconds5 = this.dealTimeout();
          if (seconds5 > 720 * 60) {
            this.$set(this.qualitativeForm, "isOverTimeShow", "是");
            this.$set(this.qualitativeForm, "isOverTime", 1);
          } else {
            this.$set(this.qualitativeForm, "isOverTimeShow", "否");
            this.qualitativeForm.isOverTime = 0;
          }
        } else if (
          this.qualitativeForm.isProtected == "1" &&
          this.qualitativeForm.isProtectedValid == "1"
        ) {
          for (let item4 of config.proEquipmentRule1) {
            if (item4.pro.indexOf(this.userData.provinceName) != "-1") {
              let seconds6 = this.dealTimeout();
              if (seconds6 > item4.timeLimit * 60) {
                this.$set(this.qualitativeForm, "isOverTimeShow", "是");
                this.$set(this.qualitativeForm, "isOverTime", 1);
              } else {
                this.$set(this.qualitativeForm, "isOverTimeShow", "否");
                this.qualitativeForm.isOverTime = 0;
              }
            }
          }
        } else if (
          this.qualitativeForm.isPersonInRoom == "1" &&
          this.qualitativeForm.hasBackupPart == "1"
        ) {
          let seconds3 = this.dealTimeout();
          if (seconds3 > 60 * 60) {
            this.$set(this.qualitativeForm, "isOverTimeShow", "是");
            this.$set(this.qualitativeForm, "isOverTime", 1);
          } else {
            this.$set(this.qualitativeForm, "isOverTimeShow", "否");
            this.$set(this.qualitativeForm, "isOverTime", 0);
          }
        } else if (
          this.qualitativeForm.isPersonInRoom == "0" ||
          this.qualitativeForm.hasBackupPart == "0"
        ) {
          for (let item3 of config.proEquipmentRule4) {
            if (item3.pro.indexOf(this.userData.provinceName) != "-1") {
              let seconds4 = this.dealTimeout();
              if (seconds4 > item3.timeLimit * 60) {
                this.$set(this.qualitativeForm, "isOverTimeShow", "是");
                this.qualitativeForm.isOverTime = 1;
              } else {
                this.$set(this.qualitativeForm, "isOverTimeShow", "否");
                this.qualitativeForm.isOverTime = 0;
              }
            }
          }
        }
      }
    },
    // faultCateChange(type) {
    //   if (type == "eqTypeChange") {
    //     this.eqTypeChange(type);
    //   } else if (type == "faultCate") {
    //     this.resetFaultCateChange();
    //   }
    //   if (this.qualitativeForm.faultCate == 1) {
    //     //线路故障
    //     if (this.qualitativeForm.isProtected == 0) {
    //       //无保护
    //       for (let item of config.proNotProtect) {
    //         if (item.pro.indexOf(this.userData.provinceName) != "-1") {
    //           let seconds = this.dealTimeout();
    //           if (seconds > item.timeLimit * 60) {
    //             this.$set(this.qualitativeForm, "isOverTime", 1);
    //           } else {
    //             this.$set(this.qualitativeForm, "isOverTime", 0);
    //           }
    //         }
    //       }
    //     } else if (this.qualitativeForm.isProtected == 1) {
    //       //有保护
    //       for (let item2 of config.proProtect) {
    //         if (item2.pro.indexOf(this.userData.provinceName) != "-1") {
    //           let seconds2 = this.dealTimeout();
    //           if (seconds2 > item2.timeLimit * 60) {
    //             this.$set(this.qualitativeForm, "isOverTime", 1);
    //           } else {
    //             this.$set(this.qualitativeForm, "isOverTime", 0);
    //           }
    //         }
    //       }
    //     }
    //   } else if (this.qualitativeForm.faultCate == 2) {
    //     //设备故障
    //     if (
    //       (this.qualitativeForm.eqpType == "4" ||
    //         this.qualitativeForm.eqpType == "2" ||
    //         this.qualitativeForm.eqpType == "5") &&
    //       this.qualitativeForm.cardType == "3" &&
    //       this.qualitativeForm.isProtected == "1" &&
    //       this.qualitativeForm.isProtectedValid == "1"
    //     ) {
    //       for (let item4 of config.proEquipmentRule1) {
    //         if (item4.pro.indexOf(this.userData.provinceName) != "-1") {
    //           let seconds6 = this.dealTimeout();
    //           if (seconds6 > item4.timeLimit * 60) {
    //             this.$set(this.qualitativeForm, "isOverTime", 1);
    //           } else {
    //             this.qualitativeForm.isOverTime = 0;
    //           }
    //         }
    //       }
    //     } else if (
    //       this.qualitativeForm.eqpType == "6" &&
    //       this.qualitativeForm.isEffectBusiness == "0"
    //     ) {
    //       let seconds5 = this.dealTimeout();
    //       if (seconds5 > 720 * 60) {
    //         this.$set(this.qualitativeForm, "isOverTime", 1);
    //       } else {
    //         this.qualitativeForm.isOverTime = 0;
    //       }
    //     } else if (
    //       this.qualitativeForm.isPersonInRoom == "1" &&
    //       this.qualitativeForm.hasBackupPart == "1"
    //     ) {
    //       let seconds3 = this.dealTimeout();
    //       if (seconds3 > 60 * 60) {
    //         this.$set(this.qualitativeForm, "isOverTime", 1);
    //       } else {
    //         this.$set(this.qualitativeForm, "isOverTime", 0);
    //       }
    //     } else if (
    //       (this.qualitativeForm.isPersonInRoom == "1" &&
    //         this.qualitativeForm.hasBackupPart == "0") ||
    //       (this.qualitativeForm.isPersonInRoom == "0" &&
    //         this.qualitativeForm.hasBackupPart == "1") ||
    //       (this.qualitativeForm.isPersonInRoom == "0" &&
    //         this.qualitativeForm.hasBackupPart == "0")
    //     ) {
    //       for (let item3 of config.proEquipmentRule4) {
    //         if (item3.pro.indexOf(this.userData.provinceName) != "-1") {
    //           let seconds4 = this.dealTimeout();
    //           if (seconds4 > item3.timeLimit * 60) {
    //             this.qualitativeForm.isOverTime = 1;
    //           } else {
    //             this.qualitativeForm.isOverTime = 0;
    //           }
    //         }
    //       }
    //     }
    //   }
    // },
    eqTypeChange(type) {
      if (type == "eqTypeChange") {
        this.qualitativeForm.eqpFaultReason = "";
        this.qualitativeForm.cardType = "";
      }
      if (this.qualitativeForm.eqpType == "1") {
        //SDH
        this.faultReasonDictId = "10030";
        this.cardTypeDictId = "10037";
      } else if (this.qualitativeForm.eqpType == "2") {
        //DWDM
        this.faultReasonDictId = "10031";
        this.cardTypeDictId = "10038";
      } else if (this.qualitativeForm.eqpType == "3") {
        //ASON
        this.faultReasonDictId = "10032";
        this.cardTypeDictId = "10039";
      } else if (this.qualitativeForm.eqpType == "4") {
        //OTN
        this.faultReasonDictId = "10033";
        this.cardTypeDictId = "10040";
      } else if (this.qualitativeForm.eqpType == "5") {
        //ROADM
        this.faultReasonDictId = "10044";
        this.cardTypeDictId = "10041";
      } else if (this.qualitativeForm.eqpType == "6") {
        //网管故障
        this.faultReasonDictId = "10034";
        this.qualitativeForm.cardType = null;
      } else if (this.qualitativeForm.eqpType == "7") {
        //配套设备
        this.faultReasonDictId = "10035";
        this.qualitativeForm.cardType = null;
      } else if (this.qualitativeForm.eqpType == "8") {
        //其他
        this.faultReasonDictId = "10036";
        this.qualitativeForm.cardType = null;
      }
    },
    resetFaultCateChange() {
      this.qualitativeForm.circuitFaultReason = null;
      this.qualitativeForm.opticFiber = null;
      this.qualitativeForm.faultRange = null;
      this.qualitativeForm.maintainDept = null;
      this.qualitativeForm.isProtected = null;
      this.qualitativeForm.isProtectedValid = null;
      this.importForm.relatedFilesFileList = [];
      this.qualitativeForm.effectSystem = null;
      this.qualitativeForm.effectCircuit = null;
      this.qualitativeForm.falutReasonDesc = null;
      this.qualitativeForm.falutComment = null;
      this.qualitativeForm.eqpType = null;
      this.qualitativeForm.eqpName = null;
      this.qualitativeForm.eqpFaultReason = null;
      this.qualitativeForm.cardType = null;
      this.qualitativeForm.isPersonInRoom = null;
      this.qualitativeForm.hasBackupPart = null;
      this.qualitativeForm.supportSystem = null;
      this.qualitativeForm.vendor = null;
    },
    closeAndDeleteFile(tag) {
      this.deleteFile(tag, "xl");
    },
    closeAndDeleteFileDl(tag) {
      this.deleteFile(tag, "dl");
    },
    deleteFile(tag, type) {
      let param = {
        attId: tag.id,
        linkId: this.qualitativeForm.linkId,
      };
      apiDeleteFdFile(param)
        .then(res => {
          if (res.status == "0") {
            if (type == "xl") {
              this.fdFileXlArr.splice(this.fdFileXlArr.indexOf(tag), 1);
              this.qualitativeForm.appendix = JSON.stringify(this.fdFileXlArr);
            } else if (type == "dl") {
              this.fdFileDlArr.splice(this.fdFileDlArr.indexOf(tag), 1);
              this.qualitativeForm.appendix = JSON.stringify(this.fdFileDlArr);
            }
            this.$message.success("附件删除成功");
          } else {
            this.$message.error("附件删除失败");
          }
        })
        .catch(error => {
          this.$message.error("附件删除失败");
          console.log(error);
        });
    },
    closeFile(tag) {
      this.importForm.relatedFilesFileList.splice(
        this.importForm.relatedFilesFileList.indexOf(tag),
        1
      );
    },
    downloadAppendixFile(data) {
      let param = {
        attId: data.id,
      };
      apiDownloadAppendixFile(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("文件下载成功");
          } else {
            this.$message.error("文件下载失败");
          }
        })
        .catch(error => {
          console.log(error);
          this.$message.error("文件下载失败");
        });
    },
    dealTimeout() {
      //故障代通时间（无故障代通时间 用故障结束时间） - 故障开始时间
      if (this.qualitativeForm.lastClearTime) {
        let seconds = moment(
          this.qualitativeForm.lastClearTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.qualitativeForm.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        return seconds;
      } else {
        let seconds = moment(
          this.qualitativeForm.faultEndTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.qualitativeForm.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        return seconds;
      }
    },
    onReset() {
      // this.qualitativeForm = {
      //   ...this.$options.data,
      //   alarmCreateTime: this.common.failureTime,
      //   sheetCreateTime: this.common.failureInformTime,
      //   person: this.userInfo.realName,
      //   workItemId: this.workItemId,
      //   woId: this.common.woId,
      //   dept: this.userInfo.deptId,
      //   processInstId: this.common.processInstId,
      //   processDefId: this.common.processDefId,
      // };
      // this.qualitativeDetail();
      this.qualitativeForm.lastClearTime = null;
      this.qualitativeForm.lastClearDuration = 0;
      this.qualitativeForm.faultEndTime = null;
      this.qualitativeForm.faultDuration = 0;
      this.qualitativeForm.faultRegion = null;
      this.qualitativeForm.processDuration = 0;
      this.qualitativeForm.isEffectBusiness = null;
      this.qualitativeForm.faultStatus = null;
      this.qualitativeForm.faultCate = null;
      this.qualitativeForm.circuitFaultReason = null;
      this.qualitativeForm.opticFiber = null;
      this.qualitativeForm.faultRange = null;
      this.qualitativeForm.maintainDept = null;
      this.qualitativeForm.isProtected = null;
      this.qualitativeForm.isProtectedValid = null;
      this.qualitativeForm.isOverTime = null;
      this.qualitativeForm.effectSystem = null;
      this.qualitativeForm.effectCircuit = null;
      this.qualitativeForm.falutReasonDesc = null;
      this.qualitativeForm.falutComment = null;
      this.qualitativeForm.eqpType = null;
      this.qualitativeForm.eqpName = null;
      this.qualitativeForm.cardType = null;
      this.qualitativeForm.isPersonInRoom = null;
      this.qualitativeForm.hasBackupPart = null;
      this.qualitativeForm.supportSystem = null;
      this.qualitativeForm.vendor = null;
      this.importForm.relatedFilesFileList = [];
    },
    showTimeNew(val) {
      let isPositiveNumber = true;
      let valStr = val + "";
      if (valStr.indexOf("-") != -1) {
        //负数
        val = Math.abs(val);
        isPositiveNumber = false;
      }
      if (val) {
        if (val == 0) return "0秒";
        var time = "";
        var second = val % 60;
        var minute = parseInt(parseInt(val) / 60) % 60;
        time = second + "秒";
        if (minute >= 1) {
          time = minute + "分" + second + "秒";
        }
        var hour = parseInt(parseInt(val / 60) / 60) % 24;
        if (hour >= 1) {
          time = hour + "小时" + minute + "分" + second + "秒";
        }
        var day = parseInt(parseInt(parseInt(val / 60) / 60) / 24);
        if (day >= 1) {
          time = day + "天" + hour + "小时" + minute + "分" + second + "秒";
        }
        if (!isPositiveNumber) {
          time = "-" + time;
        }
        return time;
      } else {
        return "0秒";
      }
    },
    //光缆名称选择
    opticalCableSelect() {
      let jsonStr =
        '{"userId":"302785","username":"yangxm97","callSysId":"0001","callSysName":"电子运维"}';
      this.$nextTick(() => {
        document.querySelector("#fiberOpticCable").value = jsonStr;
        document.querySelector("#sub__fiberOpticCable").submit();
      });
    },
    //设备名称选择
    deviceSelect() {
      let jsonStr =
        '{"userId":"302785","username":"yangxm97","callSysId":"0001","callSysName":"电子运维"}';
      this.$nextTick(() => {
        document.querySelector("#device").value = jsonStr;
        document.querySelector("#sub__device").submit();
      });
    },
    //资源系统回调方法初始化
    resourceBackInit() {
      window["__process_response_from_getDevice"] = value => {
        let form = this.qualitativeForm;
        let result = JSON.parse(value);
        let objArray = JSON.parse(result?.objects);

        if (form.faultCate == "1") {
          let opticFiberArray = form.opticFiber
            ? form.opticFiber.split(",")
            : [];
          let effectSystemArray = form.effectSystem
            ? form.effectSystem.split(",")
            : [];
          let effectSystemArrayTemp = [];

          objArray.forEach(obj => {
            if (opticFiberArray.indexOf(obj.resName) < 0) {
              opticFiberArray.push(obj.resName);
            }

            effectSystemArrayTemp = JSON.parse(obj.affectedSystem);
            effectSystemArrayTemp.forEach(sys => {
              if (sys.sysName && effectSystemArray.indexOf(sys.sysName) < 0) {
                effectSystemArray.push(sys.sysName);
              }
            });
          });

          form.opticFiber = opticFiberArray.join(",");
          form.effectSystem = effectSystemArray.join(",");
          form.eqpName = "";
        } else {
          let eqpNameArray = form.eqpName ? form.eqpName.split(",") : [];

          objArray.forEach(obj => {
            if (eqpNameArray.indexOf(obj.resName) < 0) {
              eqpNameArray.push(obj.resName);
            }
          });

          form.eqpName = eqpNameArray.join(",");
          form.opticFiber = "";
          form.effectSystem = "";
        }
      };
    },
    toRoutePage() {
      let url = window.location.protocol + "//" + window.location.host;
      if (this.qualitativeForm.routeProcessInstId != null) {
        url +=
          "/ibpm-ui/#/contentTo?content=Y&needi=Y&no=detail&hideHeader=1&reqFrom=handle&processInstId=" +
          this.qualitativeForm.routeProcessInstId +
          "&jobCode=" +
          this.qualitativeForm.routeNo +
          "&globalUniqueID=" +
          sessionStorage.getItem("globalUniqueID");
      } else {
        url +=
          "/EOM_LIFE_ADJUST/#/transfer/draftform?hideHeader=1&hideTagsNav=1&actonShow=false&jobcode=" +
          this.qualitativeForm.routeNo;
      }
      window.open(url);
    },
    networkTypeChange() {
      this.qualitativeForm.circuitFaultReason = "";
    },
  },
};
</script>
<style lang="scss" scoped>
.qualitative {
  max-height: calc(90vh - 150px);
  min-height: 400px;
  margin: 0 4px;
  padding-left: 4px;
  padding-right: 8px;
  overflow-y: auto;

  .cus-card ::v-deep .el-card__header {
    padding: 11px 24px 10px;
    font-weight: 400;

    @include themify {
      background-color: themed("$--background-color-base");
    }
  }

  .fileName_style_download {
    margin-right: 3px;
    cursor: pointer;
    vertical-align: middle;

    div {
      display: inline-block;
      max-width: 120px;
      vertical-align: top;
    }
  }

  .fileName_style {
    margin-right: 3px;
    vertical-align: middle;

    div {
      display: inline-block;
      max-width: 120px;
      vertical-align: top;
    }
  }
}
</style>
