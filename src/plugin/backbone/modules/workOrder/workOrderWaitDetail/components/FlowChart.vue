<template>
  <el-card shadow="always" class="card" :body-style="{ padding: '10px 24px' }">
    <div class="header clearfix">
      <div class="header-tabMenu">
        <span class="header-title" style="margin-top: 5px; margin-right: 10px"
          >流程图</span
        >
        <div
          v-if="
            tabMenu.length > 0 &&
            common.professionalType != 23 &&
            common.professionalType != 22
          "
          style="display: inline-block"
        >
          <!-- <el-tabs type="card" @tab-click="handleClick">
          <el-tab-pane v-for="(tab, key) in tabMenu" :key="key" :label="tab.processName">
          </el-tab-pane>
        </el-tabs> -->
          <el-radio-group v-model="radio1" @change="handleClick" size="mini">
            <el-radio-button
              v-for="(tab, key) in tabMenu"
              :key="key"
              :label="key"
            >
              {{ tab.processName }}
            </el-radio-button>
          </el-radio-group>
        </div>
        <div style="border: 1px solid #e8e8e8; margin-top: 10px">
          <iframe
            v-if="woId && processInstID"
            :src="processUrl"
            frameborder="0"
            scrolling="no"
            width="100%"
            height="530"
          ></iframe>
        </div>
      </div>
    </div>
  </el-card>
</template>
<script>
import { apiGetProcessTree } from "../../api/FlowChart";
export default {
  name: "FlowChart",
  props: {
    common: Object,
  },
  data() {
    return {
      tabMenu: [],
      woId: this.common.woId,
      processInstID: "",
      processUrlPre:
        "http://************:8081/uflow-api/service/uflowGraph/showWFGraph?zoom=1&tenantId=eoms_netfm3",
      processUrl: "",
      radio1: "0",
    };
  },
  mounted() {
    this.getTabMenu();
  },
  methods: {
    //监听切换流程图
    setprocessUrl() {
      let userRealName = JSON.parse(sessionStorage.userInfo).realName;
      let username = JSON.parse(sessionStorage.userInfo).userName;
      this.processUrl =
        this.processUrlPre +
        `&processInstID=${this.processInstID}&userRealName=${userRealName}&username=${username}`;
    },
    //获取tab数据
    getTabMenu() {
      let param = {
        woId: this.woId,
      };
      let param1 = JSON.stringify(param);
      apiGetProcessTree(param1).then(res => {
        if (res.status == 0) {
          let self = this;
          if (
            this.common.professionalType == "23" ||
            this.common.professionalType == "22"
          ) {
            self.tabMenu = res.data || [];
            self.processInstID = self.tabMenu[0];
          } else {
            self.tabMenu = res.data || [];
            for (let tab in self.tabMenu) {
              if (
                JSON.parse(
                  JSON.parse(sessionStorage.userInfo).attr2
                ).orgInfo.fullOrgName.indexOf(self.tabMenu[tab].processName) >
                -1
              ) {
                self.processInstID = self.tabMenu[tab].processId;
                self.radio1 = tab;
              }
            }
          }
          if (self.processInstID == "") {
            self.processInstID = self.tabMenu[0].processId;
          }
          self.processUrlPre = res.msg;
          self.setprocessUrl();
        }
      });
    },
    //tab切换流程
    handleClick(index) {
      this.processInstID = this.tabMenu[index].processId;
      this.setprocessUrl();
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../assets/common.scss";
</style>
