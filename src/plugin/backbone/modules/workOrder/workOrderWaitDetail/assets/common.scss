.card {
  margin-bottom: 10px;
  &:last-child {
    margin-bottom: 0;
  }
}
.header {
  margin-bottom: 8px;
}
.header-title {
  font-size: 15px;
  letter-spacing: 0;
}
.header-right {
  float: right;
}
.content ::v-deep {
  @include themify() {
    border: 1px solid themed("$--border-color-light");
  }
  .el-divider {
    margin: 6px 0 12px;
  }
  .collapse-title {
    flex: 1 0 90%;
    order: 1;
  }
  .descriptions {
    .el-descriptions__header {
      height: 47px;
      padding-left: 24px;
      background: #fafafa;
      border-top: 1px solid #e9e9e9;
      border-bottom: 1px solid #e9e9e9;
      border-radius: 2px 2px 0 0;
      border-radius: 2px 2px 0px 0px;
    }
  }
  .el-collapse-item__header {
    flex: 1 0 auto;
    order: -1;
  }
}
.____content ::v-deep {
  margin: 0 40px;
  @include themify() {
    border: 1px solid themed("$--border-color-light");
  }
  .el-divider {
    margin: 6px 0 12px;
  }
  .collapse-title {
    flex: 1 0 90%;
    order: 1;
  }
  .descriptions {
    .el-descriptions__header {
      height: 47px;
      padding-left: 24px;
      background: #fafafa;
      border-top: 1px solid #e9e9e9;
      border-bottom: 1px solid #e9e9e9;
      border-radius: 2px 2px 0 0;
      border-radius: 2px 2px 0px 0px;
    }
  }
  .el-collapse-item__header {
    flex: 1 0 auto;
    order: -1;
  }
}
::v-deep .el-table__body-wrapper {
  .el-table__cell {
    .cell {
      display: flex;
      align-items: center;
      .hang__order {
        color: #b50b14;
        width: 20px;
        height: 20px;
        border: 1px solid #b50b14;
        border-radius: 50%;
        // display: flex;
        // align-items: center;
        // justify-content: center;
        margin-left: 4px;
      }
    }
  }
}

.descriptions ::v-deep {
  .el-descriptions__title {
    font-size: 13px;
    line-height: 22px;
    font-weight: unset;
  }
  .el-descriptions__body {
    padding: 0 2em;
  }
  .el-descriptions-item__label {
    white-space: nowrap;
  }
  .el-descriptions-item__cell {
    // vertical-align: inherit;
    vertical-align: top;
  }
}
.sf_sheetType {
  background: #4b508a;
  border-color: #4b508a;
  border-radius: 2px;
  color: #fff;
  width: 80px;
  text-align: center;
}
.gt_sheetType {
  background: #526a76;
  border-color: #526a76;
  border-radius: 2px;
  color: #fff;
  width: 80px;
  text-align: center;
}

.sfty_sheetType {
  background: #4b508a;
  border-color: #4b508a;
  border-radius: 2px;
  color: #fff;
  width: 80px;
  text-align: center;
}
.ggw_sheetType {
  background: #b98c6d;
  border-color: #b98c6d;
  border-radius: 2px;
  color: #fff;
  width: 80px;
  text-align: center;
}
.core_sheetType {
  background: #c8b9a6;
  border-color: #c8b9a6;
  border-radius: 2px;
  color: #fff;
  width: 80px;
  text-align: center;
}
.ip_sheetType {
  background: #a39a89;
  border-color: #a39a89;
  border-radius: 2px;
  color: #fff;
  width: 80px;
  text-align: center;
}
.pt_sheetType {
  background: #878ba6;
  border-color: #878ba6;
  border-radius: 2px;
  color: #fff;
  width: 80px;
  text-align: center;
}
.wxw_sheetType {
  background: #939391;
  border-color: #939391;
  border-radius: 2px;
  color: #fff;
  min-width: 80px;
  text-align: center;
}
.commonFlow_sheetType {
  background: #744e20;
  border-color: #744e20;
  border-radius: 2px;
  color: #fff;
  min-width: 80px;
  text-align: center;
}
.itCloud_sheetType {
  background: #536d79;
  border-color: #536d79;
  border-radius: 2px;
  color: #fff;
  width: 80px;
  text-align: center;
}
.commCloud_sheetType {
  background: #ac797a;
  border-color: #ac797a;
  border-radius: 2px;
  color: #fff;
  width: 80px;
  text-align: center;
}
.supervise_sheetType {
  background: #96a48b;
  border-color: #96a48b;
  border-radius: 2px;
  color: #fff;
  width: 80px;
  text-align: center;
  padding-left: 4px !important;
}
.sheetNo_style {
  color: #409eff;
  user-select: unset;
}
.search-input-button {
  background: #b50b14;
  border-color: #b50b14;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.1);
  border-radius: 0 2px 2px 0;
}

.link_style {
  white-space: nowrap;
  text-overflow: ellipsis;
  color: #c43c43;
  // line-height: 28px;
}
.fileName_style {
  margin-right: 3px;
  vertical-align: middle;
  margin-bottom: 3px;
  cursor: pointer;
  div {
    display: inline-block;
    max-width: 140px;
    vertical-align: top;
  }
}

.tags {
  display: inline-flex;
  white-space: nowrap;
  .el-tag {
    border: none;
    height: 18px !important;
    line-height: 18px !important;
  }
  span {
    margin-right: 5px;
    line-height: 18px;
    font-size: 13px;
    /* 样式根据需求添加 */
  }
}
.scroll-wrapper {
  overflow-x: hidden;
  white-space: nowrap;
  width: 250px;
}
