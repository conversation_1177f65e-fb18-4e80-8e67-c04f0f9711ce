import {
  getJson,
  postJson,
  getJsonBlob,
  postFormData,
  delJson,
  postJsonBlob,
  get,
} from "@/utils/axios";
const dictUrl = "commonDict/enum/list"; //公共下拉接口地址
const showButtonUrl = "backbone/workflow/op/getBtnList"; //详情页面中的按钮展示
const workOrderInfoUrl = "backbone/workflow/queryByWoId"; //详情页面中的工单基本信息
const exportWorkOrderUrl = "backbone/workflow/queryByWoId/excel"; //工单基本信息导出
const ShowAttachDownloadLinktUrl =
  "/backbone/workflow/queryIfShowAttachDownloadLink"; //基本信息判断excel是否为空
const acceptUrl = "backbone/workflow/accept"; //受理
const acceptUrlWireless = "/wireless/operation/accept"; // 无线网受理
const alarmDetailUrl = "backbone/workflow/queryAlarm"; //告警详情表格
const syncClearUrl = "backbone/workflow/clearQuery"; //同步清除
const relationDiagnosisUrl = "backbone/workflow/queryRelation"; //关联诊断
const backSingleUrl = "backbone/workflow/ackDefine/feedback"; //返单
const qualitativeDetailUrl = "backbone/workflow/ackDefine/detail"; //返查定性详情(返单)
const qualitativeUrl = "backbone/workflow/ackDefine/define"; //定性提交
const qualitativeReviewUrl = "backbone/workflow/ackDefine/defineCheck"; //定性审核
const stageFeedbackUrl = "backbone/workflow/op/stageFeedback"; //阶段反馈
const revokeUrl = "backbone/workflow/removeProcessInst"; //撤单
const hangUpUrl = "backbone/workflow/op/applyInterrupt"; //挂起/解挂 申请
const auditUrl = "backbone/workflow/op/checkInterrupt"; //挂起申请审核
const abendUrl = "backbone/workflow/op/applyAbnormalEnd"; //异常终止申请
const abendAuditUrl = "backbone/workflow/op/checkAbnormalEnd"; //异常终止审核
const afterSingleUrl = "backbone/workflow/op/addWoInfo"; //追单
const haveReadUrl = "backbone/workflow/read/setReadStatus"; //已阅
const circulatedUrl = "backbone/workflow/read/addReadInfo"; //传阅
const circulatedUserTreeUrl = "backbone/tree/searchRedeployUser"; //传阅用户树数据
const circulatedOrgTreeUrl = "backbone/tree/searchRedeployOrg"; //传阅组织树数据
const faultAreaUrl = "backbone/info/faultArea"; //故障发生地区字典数据
const queryAttachmentListUrl = "backbone/info/attachmentList"; //附件组编码查询附件列表
const downloadAppendixUrl = "commonDict/attach/download"; //公共下载附件接口
const getFeedbackHistoryUrl = "backbone/workflow/getFeedbackHistory";
const deleteFdFileUrl = "backbone/workflow/ackDefine/deleteAttach"; //删除附件接口
const manualFileUrl = "backbone/workflow/queryCircuitAttachment/excel";
const fluenceExcelUrl = "backbone/workflow/queryCutInfluenceExcel/excel"; //光缆excel
const provinceDictUrl = "backbone/info/provinceDict";
const backSingleInternationUrl = "backbone/workflow/intAckDefine/intFeedback"; //国际局返单
const qualitativeInternationDetailUrl =
  "backbone/workflow/intAckDefine/intDetail"; //国际局定性详情
const qualitativeInternationUrl = "backbone/workflow/intAckDefine/intDefine"; //国际局定性
const qualitativeReviewInternationUrl =
  "backbone/workflow/intAckDefine/intDefineCheck"; //国际局定性审核
const itCloudAcceptUrl = "backbone/workflow/it/cloud/accept"; //IT云受理
const commCloudAcceptUrl = "commCloud/workflow/accept"; //通信云受理
const oneKeyIvrNoticeUrl = "backbone/workflow/oneKeyIvr/oneKeyIvrNotice"; //一键IVR
const itCloudHangUpUrl = "backbone/workflow/it/cloud/applyInterrupt"; //IT云挂起/解挂 申请
const itCloudAuditUrl = "backbone/workflow/it/cloud/checkInterrupt"; //IT云挂起/解挂 审核
const itCloudAbendUrl = "backbone/workflow/it/cloud/applyInterrupt"; //IT云异常终止
const itCloudAbendAuditUrl = "backbone/workflow/it/cloud/checkInterrupt"; //IT云异常终止 审核
const commCloudHangUpUrl = "commCloud/workflow/applyInterrupt"; //通信云挂起/解挂 申请
const commCloudAuditUrl = "commCloud/workflow/checkInterrupt"; //通信云挂起/解挂 审核
const commCloudAbendUrl = "commCloud/workflow/applyInterrupt"; //通信云异常终止
const commCloudAbendAuditUrl = "commCloud/workflow/checkInterrupt"; //通信云异常终止 审核
const getEvaluationUrl = "/backbone/workflow/getEvaluation"; //是否已评价
const getUsersUrl = "backbone/workflow/oneKeyIvr/getUsers"; //一键IVR更据用户id获取用户信息
const batchClearAlarmUrl = "backbone/workflow/batchClearQuery"; //批量清除告警
const groupGeneralAcceptUrl = "commonFlow/workflow/accept"; //集团通用流程受理
const analyzeRelationDiagnosisUrl = "commonDict/analyze/getCotents"; //关联诊断（分析上海AI）
const topoUrl = "/netfm3topo/topoapi/topoinfo"; //topo图是否呈现接口
const otdrUrl = "commonIntegration/process/queryOtdrGisChart";
const agentProvinceUrl = "backbone/workflow/getAgentProvinceByWoId";
const transRelationDiagnosisUrl = "backbone/workflow/queryTransRelation";
const guangshuaiUrl = "commonIntegration/process/queryTrendChart";

const apiTopoIsShow = params => getJson(topoUrl, params);
const apiDict = params => getJson(dictUrl, params);
const apiGetShowButton = params =>
  postJson(showButtonUrl, params, {
    timeout: 0
  });
const apiGetWorkOrderInfo = params =>
  postJson(workOrderInfoUrl, params, {
    timeout: 0
  });
const apiExportWorkOrder = params => getJsonBlob(exportWorkOrderUrl, params);
const apiAccept = params => postJson(acceptUrl, params);
const apiWirelessAccept = params => postJson(acceptUrlWireless, params);
const apiQueryAlarmDetail = params => postJson(alarmDetailUrl, params);
const apiSyncClear = params => postJson(syncClearUrl, params);
const apiGetRelationDiagnosis = params => getJson(relationDiagnosisUrl, params);
const apiBackSingle = params => postFormData(backSingleUrl, params);
const apiQualitativeDetail = params => getJson(qualitativeDetailUrl, params);
const apiQualitative = params => postFormData(qualitativeUrl, params);
const apiQualitativeReview = params =>
  postFormData(qualitativeReviewUrl, params);
const apiStageFeedBack = params => postJson(stageFeedbackUrl, params);
const apiRevoke = params => postJson(revokeUrl, params);
const apiHangUp = params => postJson(hangUpUrl, params);
const apiAudit = params => postJson(auditUrl, params);
const apiAbend = params => postJson(abendUrl, params);
const apiAbendAudit = params => postJson(abendAuditUrl, params);
const apiAfterSingle = params => postFormData(afterSingleUrl, params);
const apiHaveRead = params => postJson(haveReadUrl, params);
const apiCirculated = params => postJson(circulatedUrl, params);
const apiCirculatedUserTree = params => getJson(circulatedUserTreeUrl, params);
const apiCirculatedOrgTree = params => getJson(circulatedOrgTreeUrl, params);
const apiGetFaultArea = params => getJson(faultAreaUrl, params);
const apiGetEvaluation = params => getJson(getEvaluationUrl, params);
const apiDownloadAppendixFile = params =>
  getJsonBlob(downloadAppendixUrl, params, {
    timeout: 0
  });
const apiGetFeedbackHistory = params => getJson(getFeedbackHistoryUrl, params);
const apiQueryAttachment = params => getJson(queryAttachmentListUrl, params);
const apiDeleteFdFile = params => delJson(deleteFdFileUrl, params);
const apiDownloadManualFile = params => postJsonBlob(manualFileUrl, params);
const apiGetProvinceDict = params => getJson(provinceDictUrl, params);
const apiBackSingleInternation = params =>
  postJson(backSingleInternationUrl, params);
const apiQualitativeInternationDetail = params =>
  getJson(qualitativeInternationDetailUrl, params);
const apiQualitativeInternation = params =>
  postJson(qualitativeInternationUrl, params);
const apiQualitativeInternationReview = params =>
  postJson(qualitativeReviewInternationUrl, params);
const apiItCloudAccept = params =>
  postJson(itCloudAcceptUrl, params, {
    timeout: 0
  });
const apiCommCloudAccept = params =>
  postJson(commCloudAcceptUrl, params, {
    timeout: 0
  });
const apioneKeyIvrNotice = params => postJson(oneKeyIvrNoticeUrl, params);
const apiItCloudHangUp = params => postJson(itCloudHangUpUrl, params); //挂起解挂 申请
const apiItCloudAudit = params => postJson(itCloudAuditUrl, params); //挂起解挂 审核
const apiItCloudAbend = params => postJson(itCloudAbendUrl, params);
const apiItCloudAbendAudit = params => postJson(itCloudAbendAuditUrl, params);
const apiCommCloudHangUp = params => postJson(commCloudHangUpUrl, params);
const apiCommCloudAudit = params => postJson(commCloudAuditUrl, params);
const apiCommCloudAbend = params => postJson(commCloudAbendUrl, params);
const apiCommCloudAbendAudit = params =>
  postJson(commCloudAbendAuditUrl, params);
const apiShowAttachDownloadLink = params =>
  postJson(ShowAttachDownloadLinktUrl, params);
const apifluenceExcel = params => postJsonBlob(fluenceExcelUrl, params);
const apiGetUsers = params => postJson(getUsersUrl, params);
const apiBatchClearAlarm = params => postJson(batchClearAlarmUrl, params);
const apiGroupGeneralAccept = params => postJson(groupGeneralAcceptUrl, params);
const apiAnalyzeRelationDiagnosis = params =>
  getJson(analyzeRelationDiagnosisUrl, params);
const apiGetOtdrData = params => postFormData(otdrUrl, params);
const apiGetAgentProvince = woId => get(agentProvinceUrl + "?woId=" + woId);
const apiGetTransRelationDiagnosis = params =>
  getJson(transRelationDiagnosisUrl, params);
const apiGuangshuai = params => getJson(guangshuaiUrl, params);

// 电路搜索
const searchCircuitNo = params => postJson('/backbone/workflow/AffectSystemCircuit/queryCirCuitListByGzzx', params);
// 线缆、设备、主题 搜索
const searchCablesDevicesThemes = params => postJson('/backbone/workflow/queryTransDataByParams', params);

export {
  apiDict,
  apiGetShowButton,
  apiGetWorkOrderInfo,
  apiExportWorkOrder,
  apiAccept,
  apiWirelessAccept,
  apiQueryAlarmDetail,
  apiSyncClear,
  apiGetRelationDiagnosis,
  apiBackSingle,
  apiQualitativeDetail,
  apiQualitative,
  apiQualitativeReview,
  apiStageFeedBack,
  apiRevoke,
  apiHangUp,
  apiAudit,
  apiAbend,
  apiAbendAudit,
  apiAfterSingle,
  apiHaveRead,
  apiCirculated,
  apiCirculatedUserTree,
  apiCirculatedOrgTree,
  apiGetFaultArea,
  apiDownloadAppendixFile,
  apiQueryAttachment,
  apiDeleteFdFile,
  apiDownloadManualFile,
  apiGetProvinceDict,
  apiBackSingleInternation,
  apiQualitativeInternationDetail,
  apiQualitativeInternation,
  apiQualitativeInternationReview,
  apiItCloudAccept,
  apioneKeyIvrNotice,
  apiGetUsers,
  apiItCloudHangUp,
  apiItCloudAudit,
  apiItCloudAbend,
  apiItCloudAbendAudit,
  apiCommCloudHangUp,
  apiCommCloudAudit,
  apiCommCloudAbend,
  apiCommCloudAbendAudit,
  apifluenceExcel,
  apiShowAttachDownloadLink,
  apiGetEvaluation,
  apiCommCloudAccept,
  apiGetFeedbackHistory,
  apiBatchClearAlarm,
  apiGroupGeneralAccept,
  apiAnalyzeRelationDiagnosis,
  apiTopoIsShow,
  apiGetOtdrData,
  apiGetAgentProvince,
  apiGetTransRelationDiagnosis,
  searchCircuitNo,
  searchCablesDevicesThemes,
  apiGuangshuai
};
