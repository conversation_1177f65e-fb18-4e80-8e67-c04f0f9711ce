<template>
  <head-fixed-layout class="draft-eidt-page">
    <template #header>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="15" :md="18" class="head-title"><span v-if="isIt">IT云设备故障工单拟稿</span><span
            v-else>骨干传输故障工单拟稿</span></el-col>
        <el-col :xs="24" :sm="9" :md="6" class="head-handle-wrap">
          <el-button v-if="!isIt" type="primary" @click="onSelectTemp">选择建单模板</el-button>
          <el-button type="primary" @click="onSheetSave" v-loading.fullscreen.lock="sheetSaveLoading">保存</el-button>
          <el-button type="primary" @click="onSheetSubmit" v-loading.fullscreen.lock="sheetCommitLoading">提交</el-button>
        </el-col>
      </el-row>
    </template>

    <el-form :model="sheetForm" ref="sheetForm" :rules="sheetFormRules" label-width="130px" label-position="right">
      <el-card shadow="always" :body-style="{ padding: '10px' }">
        <div slot="header" class="card-title">
          <span>工单基本信息</span>
        </div>
        <el-row :gutter="10" type="flex" style="flex-wrap: wrap">
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="建单人:" prop="createUser" required>
              <el-input v-model="sheetForm.createUser" placeholder="请输入建单人" readonly style="width: 100%"></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="建单部门:" prop="dept" required>
              <el-input v-model="sheetForm.dept" placeholder="请输入建单部门" readonly style="width: 100%"></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="建单时间:" prop="createTime" required>
              <el-date-picker v-model="sheetForm.createTime" type="datetime" placeholder="请选择建单时间"
                value-format="yyyy-MM-dd HH:mm:ss" readonly style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col v-if="mainProfessionalType == '骨干网'" :xs="24" :sm="14" :md="16" :offset="0">
            <el-form-item label="工单主题:" prop="title" required>
              <!-- 后续添加 -->
              <!-- <UniversalPopupWindowEquipmentOrLines :placeholder="'点击选择可调用资源管理系统系统数，勾选系统名称节点显示到输入框，并支持手动修改'"
                :currentSearchQueryType="1" @changeCableEquipInputData="changThemeInputData"
                :cableEquipInputData="sheetForm.title" @event-select-data="eventThemeSelectData" :title="'传输系统'" /> -->
              <el-input v-model="sheetForm.title" placeholder="点击选择可调用资源管理系统系统数，勾选系统名称节点显示到输入框，并支持手动修改" clearable
                style="width: 100%" maxlength="400">
                <el-button style="
                    background: #b50b14;
                    border-color: #b50b14;
                    color: #fff;
                    padding: 9px 20px;
                    top: -0.8px;
                    position: relative;
                  " slot="append" @click="getInterfaceZxData">选择</el-button>
              </el-input>
              <form id="deploy_simulation_page" name="deploy__simulation_page" hidden="true" method="post"
                action="http://***********:9080/resweb_ygzy/union/resAssign.out?method=selectSystem" target="_blank">
                <input type="hidden" name="requestJson" id="requestJson" />
              </form>
            </el-form-item>
          </el-col>
          <el-col v-else-if="isIt" :xs="24" :sm="14" :md="16" :offset="0">
            <el-form-item label="工单主题:" prop="title" required>
              <el-input v-model="sheetForm.title" placeholder="使用拼接规则手动填写" clearable style="width: 100%"
                maxlength="400"></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="10" :md="8" :offset="0">
            <el-form-item label="工单来源:" prop="source" required>
              <dict-select :value.sync="sheetForm.source" :dictId="10003" placeholder="请选择内容" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="发生时间:" prop="happenTime">
              <el-date-picker v-model="sheetForm.happenTime" type="datetime" placeholder="请选择时间"
                value-format="yyyy-MM-dd HH:mm:ss" clearable style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="紧急程度:" prop="urgent" required>
              <el-radio-group v-model="sheetForm.urgent">
                <el-radio v-for="(item, i) in urgentOption" :key="i" :label="item.dictCode">{{ item.dictName
                }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <!-- <el-col :xs="24" :sm="10" :md="8" :offset="0">
            <el-form-item label="工单优先级:" prop="faultLevel" required>
              <dict-select
                :value.sync="sheetForm.faultLevel"
                :dictId="81005"
                placeholder="请选择"
                style="width: 100%"
              />
            </el-form-item>
          </el-col> -->
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="受理时限(分钟):" prop="acceptTimeLimit" required>
              <el-input v-model="sheetForm.acceptTimeLimit" placeholder="请输入受理时限" clearable style="width: 100%"
                maxlength="11"></el-input>
            </el-form-item>
          </el-col>
          <el-col v-if="isIt" :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="预估处理时限:" prop="estimatedProTimeLimit" required>
              <el-radio-group v-model="sheetForm.estimatedProTimeLimit">
                <el-radio v-for="(item, i) in estimatedProTimeLimitOption" :key="i" :label="item.id">{{ item.name
                }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="所属专业:" prop="special" required>
              <dict-select :value.sync="sheetForm.special" :dictId="10002" style="width: 100%" :notSelect="true" />
            </el-form-item>
          </el-col>
          <template v-if="mainProfessionalType == '骨干网'">
            <el-col :xs="24" :sm="8" :md="8" :offset="0">
              <el-form-item label="网络类型:" prop="netType" required>
                <dict-select :value.sync="sheetForm.netType" :dictId="10018" style="width: 100%"
                  @change="changeNetType" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :offset="0" v-show="isOpticalCableShow">
              <el-form-item label="光缆名称:" prop="opticalCable">
                <el-input v-model="sheetForm.opticalCable" placeholder="请输入内容" style="width: 100%" maxlength="1000">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :offset="0" v-show="isOpticalCableIntShow">
              <el-form-item label="光缆名称:" prop="opticalCable">
                <el-autocomplete style="width: 100%" v-model="sheetForm.opticalCable" placeholder="请输入内容"
                  :fetch-suggestions="querySearch" maxlength="1000">
                </el-autocomplete>
              </el-form-item>
            </el-col>
          </template>

          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="告警类别:" prop="alarmType" :required="mainProfessionalType == '骨干网'">
              <dict-select :value.sync="sheetForm.alarmType" :dictId="10050" style="width: 100%" />
            </el-form-item>
          </el-col>
          <template v-if="mainProfessionalType == '骨干网'">
            <el-col :xs="24" :sm="8" :md="8" :offset="0">
              <el-form-item label="故障类别:" prop="subAlarmType" required>
                <dict-select :value.sync="sheetForm.subAlarmType" :dictId="10067" style="width: 100%" />
              </el-form-item>
            </el-col>
          </template>
          <!-- 在 告警类别 为 电路故障 且 为 骨干传输 时显示 -->
          <!-- && sheetForm.alarmType == '5' -->
          <template v-if="
            mainProfessionalType == '骨干网' && sheetForm.alarmType == '6'
          ">
            <!-- <div></div> -->
            <!-- <div>{{ sheetForm.alarmType }}</div> -->

            <el-col :xs="24" :sm="8" :md="8" :offset="0">
              <el-form-item label="是否重保:" prop="isReinsuranceVal" required>
                <el-select v-model="sheetForm.isReinsuranceVal" placeholder="请选择是否重保" style="width: 100%">
                  <el-option v-for="item in sheetForm.isReinsuranceArr" :key="item.isReinsuranceVal" :label="item.name"
                    :value="item.isReinsuranceVal">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col v-if="sheetForm.isReinsuranceVal == '是'" :xs="24" :sm="8" :md="8" :offset="0">
              <el-form-item label="重保任务:" prop="reassuranceTask" :required="sheetForm.isReinsuranceVal == '是'">
                <!-- 请输入重保任务 -->
                <el-input clearable v-model="sheetForm.reassuranceTask" placeholder="请输入重保任务"></el-input>
              </el-form-item>
            </el-col>

            <el-col v-if="sheetForm.isReinsuranceVal == '是'" :xs="24" :sm="14" :md="16" :offset="0">
              <el-form-item label="涉及电路:" prop="newInvolvedCircuit" required>
                <!-- @dblclick="involvedCircuitEdit()" -->
                <div class="involvedCircuitInput">
                  <!-- color: transparent; -->

                  <el-input ref="involvedCircuitInput" :readonly="false" style="height: auto" multiple
                    v-model="sheetForm.newInvolvedCircuit" placeholder="请输入电路编号" @input="inputInvolvedCircuit">
                    <template slot="prefix">
                      <div style="overflow: hidden; overflow-x: auto" :style="{
                        width:
                          sheetForm.involvedCircuitInputWidth - 53 + 'px',
                      }">
                        <div style="display: flex">
                          <el-tag v-for="(tag, index) in sheetForm.involvedCircuit" style="margin-top: 5px; float: left"
                            :key="index" :closable="true" @close="involvedCircuitDeleteRow(index, tag)">
                            {{ tag.circuitId }}
                          </el-tag>
                        </div>
                      </div>
                    </template>
                    <!-- <el-popover
                      slot="prefix"
                      v-if="organizeForm.builderZsList.length >= 2"
                      width="500"
                      trigger="click"
                    >
                      <el-input
                        v-model="organizeForm.builderZsName"
                        placeholder="请输入主送人员姓名/组织名称"
                      >
                    <el-button
                      type="info"
                      slot="append"
                      icon="el-icon-search"
                      @click="search('builderZs')"
                        >
                        </el-button>
                        <el-button
                          type="info"
                          slot="append"
                          icon="el-icon-close"
                          @click="clear('builderZs')"
                        >
                        </el-button>
                      </el-input>
                      <el-table
                        ref="multipleTable"
                        tooltip-effect="dark"
                        @selection-change="handleSelectionChange"
                        :data="organizeForm.builderZsListCopy"
                        max-height="240"
                      >
                        <el-table-column width="30" type="selection">
                        </el-table-column>
                        <el-table-column
                          min-width="70"
                          property="name"
                          label="姓名"
                        >
                        </el-table-column>
                        <el-table-column fixed="right" label="操作" width="50">
                          <template slot-scope="scope">
                            <el-button
                              type="text"
                              size="small"
                              @click.native.prevent="
                                handleClose('builderZs', scope.row)
                              "
                            >
                              移除
                            </el-button>
                          </template>
                        </el-table-column>
                      </el-table>
                      <el-button
                        size="small"
                        type="text"
                        @click="toggleSelection('builderZs')"
                        >批量移除
                      </el-button>
                      <el-tag slot="reference" style="margin-top: 3px">
                        +{{ organizeForm.builderZsList.length - 1 }}
                      </el-tag>
                    </el-popover> -->
                    <el-button type="info" slot="append" icon="el-icon-search"
                      @click="openInvolvedCircuitDialog"></el-button>
                  </el-input>
                </div>
              </el-form-item>
            </el-col>
          </template>

          <template v-if="isIt">
            <el-col :xs="24" :sm="8" :md="8" :offset="0">
              <el-form-item label="云池类型:" prop="cloudPoolType">
                <el-input v-model="sheetForm.cloudPoolType" placeholder="请输入内容" style="width: 100%" maxlength="100">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :offset="0">
              <el-form-item label="业务名称:" prop="busName">
                <el-input v-model="sheetForm.busName" placeholder="请输入内容" style="width: 100%" maxlength="100">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="23" :md="24" :offset="0">
              <el-form-item label="故障现象:" prop="phenomenon" required>
                <el-input v-model="sheetForm.phenomenon" placeholder="请输入内容" type="textarea" :rows="2" clearable
                  style="width: 100%" maxlength="3000" show-word-limit>
                </el-input>
              </el-form-item>
            </el-col>
          </template>
          <template v-if="mainProfessionalType == '骨干网'">
            <el-col :xs="24" :sm="23" :md="24" :offset="0">
              <el-form-item label="故障现象:" prop="phenomenon" required>
                <el-input v-model="sheetForm.phenomenon" placeholder="请输入内容" type="textarea" :rows="2" clearable
                  style="width: 100%" show-word-limit maxlength="3000">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24" :offset="0">
              <el-form-item label="工作内容:" prop="jobComment">
                <el-input v-model="sheetForm.jobComment" type="textarea" :rows="1" clearable style="width: 100%"
                  show-word-limit maxlength="3000">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="8" :md="8" :offset="0">
              <el-form-item label="影响业务列表:" prop="incidenceFile">
                <el-input v-model="sheetForm.incidenceFile" placeholder="添加附件" readonly style="width: 100%">
                  <el-button type="info" slot="append" @click="incidenceBrowse">+</el-button>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="16" :md="16" :offset="0">
              <el-form-item label="影响范围:" prop="incidence">
                <el-input v-model="sheetForm.incidence" placeholder="请输入内容" clearable style="width: 100%"
                  show-word-limit maxlength="1000">
                </el-input>
              </el-form-item>
            </el-col>
          </template>
          <el-col :span="24" :offset="0">
            <el-form-item label="备注:" prop="remarks">
              <el-input v-model="sheetForm.remarks" placeholder="请输入内容" clearable style="width: 100%" maxlength="255">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card shadow="always" :body-style="{ padding: '10px' }" style="margin-top: 10px">
        <div slot="header" class="card-title">
          <span>工单派送信息</span>
        </div>
        <el-row :gutter="10" type="flex" style="flex-wrap: wrap">
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="附件:" prop="attachmentFile">
              <el-input v-model="sheetForm.attachmentFile" placeholder="添加附件" readonly style="width: 100%">
                <el-button type="info" slot="append" @click="attachmentBrowse">+</el-button>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="建单人主送:" prop="builderZs" required>
              <el-input v-model="sheetForm.builderZs" placeholder="添加人员">
                <template v-for="(tag, index) in organizeForm.builderZsList">
                  <el-tag slot="prefix" style="margin-top: 5px" :key="index" closable="true"
                    @close="handleClose('builderZs', tag)" v-show="index < 1" v-if="tag.bz == 'user'">
                    {{ tag.name }}
                  </el-tag>
                  <el-tag slot="prefix" style="margin-top: 5px" :key="index" closable="true"
                    @close="handleClose('builderZs', tag)" v-show="index < 1" v-if="tag.bz == 'org'">
                    {{ tag.orgName }}
                  </el-tag>
                </template>
                <el-popover slot="prefix" v-if="organizeForm.builderZsList.length >= 2" width="500" trigger="click">
                  <el-input v-model="organizeForm.builderZsName" placeholder="请输入主送人员姓名/组织名称">
                    <el-button type="info" slot="append" icon="el-icon-search" @click="search('builderZs')">
                    </el-button>
                    <el-button type="info" slot="append" icon="el-icon-close" @click="clear('builderZs')">
                    </el-button>
                  </el-input>
                  <el-table ref="multipleTable" tooltip-effect="dark" @selection-change="handleSelectionChange"
                    :data="organizeForm.builderZsListCopy" max-height="240">
                    <el-table-column width="30" type="selection">
                    </el-table-column>
                    <el-table-column min-width="70" property="name" label="姓名">
                    </el-table-column>
                    <el-table-column min-width="180" property="orgName" label="组织">
                    </el-table-column>
                    <el-table-column min-width="120" property="mobilePhone" label="电话">
                    </el-table-column>
                    <el-table-column fixed="right" label="操作" width="50">
                      <template slot-scope="scope">
                        <el-button type="text" size="small" @click.native.prevent="
                          handleClose('builderZs', scope.row)
                          ">
                          移除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <el-button size="small" type="text" @click="toggleSelection('builderZs')">批量移除
                  </el-button>
                  <el-tag slot="reference" style="margin-top: 3px">
                    +{{ organizeForm.builderZsList.length - 1 }}
                  </el-tag>
                </el-popover>
                <el-button type="info" slot="append" icon="el-icon-user"
                  @click="onOpenPeopleDialog('lordSentDetermine')"></el-button>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="建单人抄送:" prop="builderCs">
              <!-- <el-input
                v-model="sheetForm.builderCs"
                placeholder="添加人员"
                style="width: 100%"
                readonly
              >
                <el-button
                  type="info"
                  slot="append"
                  icon="el-icon-user"
                  @click="onOpenPeopleDialog('ccDetermine')"
                ></el-button>
              </el-input> -->

              <el-input v-model="sheetForm.builderCs" placeholder="添加人员">
                <template v-for="(tag, index) in organizeForm.builderCsList">
                  <el-tag slot="prefix" style="margin-top: 5px" :key="index" closable="true"
                    @close="handleClose('builderCs', tag)" v-show="index < 1" v-if="tag.bz == 'user'">
                    {{ tag.name }}
                  </el-tag>
                  <el-tag slot="prefix" style="margin-top: 5px" :key="index" closable="true"
                    @close="handleClose('builderCs', tag)" v-show="index < 1" v-if="tag.bz == 'org'">
                    {{ tag.orgName }}
                  </el-tag>
                </template>
                <el-popover slot="prefix" v-if="organizeForm.builderCsList.length >= 2" width="500" trigger="click">
                  <el-input v-model="organizeForm.builderCsName" placeholder="请输入抄送人员姓名/组织名称">
                    <el-button type="info" slot="append" icon="el-icon-search" @click="search('builderCs')">
                    </el-button>
                    <el-button type="info" slot="append" icon="el-icon-close" @click="clear('builderCs')">
                    </el-button>
                  </el-input>
                  <el-table ref="multipleTable" tooltip-effect="dark" @selection-change="handleSelectionChange"
                    :data="organizeForm.builderCsListCopy" max-height="240">
                    <el-table-column width="30" type="selection">
                    </el-table-column>
                    <el-table-column min-width="70" property="name" label="姓名">
                    </el-table-column>
                    <el-table-column min-width="180" property="orgName" label="组织">
                    </el-table-column>
                    <el-table-column min-width="120" property="mobilePhone" label="电话">
                    </el-table-column>
                    <el-table-column fixed="right" label="操作" width="50">
                      <template slot-scope="scope">
                        <el-button type="text" size="small" @click.native.prevent="
                          handleClose('builderCs', scope.row)
                          ">
                          移除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <el-button size="small" type="text" @click="toggleSelection('builderCs')">批量移除
                  </el-button>
                  <el-tag slot="reference" style="margin-top: 5px">
                    +{{ organizeForm.builderCsList.length - 1 }}
                  </el-tag>
                </el-popover>
                <el-button type="info" slot="append" icon="el-icon-user" @click="onOpenPeopleDialog('ccDetermine')">
                </el-button>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col v-if="mainProfessionalType == '骨干网'" :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="通知人:" prop="notifier">
              <!-- <el-input
                v-model="sheetForm.notifier"
                placeholder="添加人员"
                style="width: 100%"
                readonly
              >
                <el-button
                  type="info"
                  slot="append"
                  icon="el-icon-user"
                  @click="onOpenPeopleDialog('notifierDetermine')"
                ></el-button>
              </el-input> -->

              <el-input v-model="sheetForm.notifier" placeholder="添加人员">
                <template v-for="(tag, index) in organizeForm.notifierList">
                  <el-tag slot="prefix" style="margin-top: 5px" :key="index" closable="true"
                    @close="handleClose('notifier', tag)" v-show="index < 1" v-if="tag.bz == 'user'">
                    {{ tag.name }}
                  </el-tag>
                  <el-tag slot="prefix" style="margin-top: 5px" :key="index" closable="true"
                    @close="handleClose('notifier', tag)" v-show="index < 1" v-if="tag.bz == 'org'">
                    {{ tag.orgName }}
                  </el-tag>
                </template>
                <el-popover slot="prefix" v-if="organizeForm.notifierList.length >= 2" width="500" trigger="click">
                  <el-input v-model="organizeForm.notifierName" placeholder="请输入通知人员姓名/组织名称">
                    <el-button type="info" slot="append" icon="el-icon-search" @click="search('notifier')">
                    </el-button>
                    <el-button type="info" slot="append" icon="el-icon-close" @click="clear('notifier')">
                    </el-button>
                  </el-input>
                  <el-table ref="multipleTable" tooltip-effect="dark" @selection-change="handleSelectionChange"
                    :data="organizeForm.notifierListCopy" max-height="240">
                    <el-table-column width="30" type="selection">
                    </el-table-column>
                    <el-table-column min-width="70" property="name" label="姓名">
                    </el-table-column>
                    <el-table-column min-width="180" property="orgName" label="组织">
                    </el-table-column>
                    <el-table-column min-width="120" property="mobilePhone" label="电话">
                    </el-table-column>
                    <el-table-column fixed="right" label="操作" width="50">
                      <template slot-scope="scope">
                        <el-button type="text" size="small" @click.native.prevent="
                          handleClose('notifier', scope.row)
                          ">
                          移除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <el-button size="small" type="text" @click="toggleSelection('notifier')">批量移除
                  </el-button>
                  <el-tag slot="reference" style="margin-top: 5px">
                    +{{ organizeForm.notifierList.length - 1 }}
                  </el-tag>
                </el-popover>
                <el-button type="info" slot="append" icon="el-icon-user"
                  @click="onOpenPeopleDialog('notifierDetermine')"></el-button>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="是否通知他人:" prop="sms">
              <el-radio-group v-model="sheetForm.sms">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <template v-if="sheetForm.sms">
            <el-col :xs="24" :sm="8" :md="8" :offset="0">
              <el-form-item label="接收人:" prop="recipient" :rules="[
                {
                  required: sheetForm.sms == '1' ? true : false,
                  message: '请选择',
                },
              ]">
                <!-- <el-input
                  v-model="sheetForm.recipientDetailUserName"
                  placeholder="添加人员"
                  style="width: 100%"
                  readonly
                >
                  <el-button
                    type="info"
                    slot="append"
                    icon="el-icon-user"
                    @click="onOpenPeopleDialog('recipientDetermine')"
                  ></el-button>
                </el-input> -->
                <el-input v-model="sheetForm.recipient" placeholder="添加人员">
                  <el-tag slot="prefix" style="margin-top: 5px" v-for="(tag, index) in organizeForm.recipientList"
                    :key="index" closable="true" @close="handleClose('recipient', tag)" v-show="index < 1">
                    {{ tag.name }}
                  </el-tag>

                  <el-popover slot="prefix" v-if="organizeForm.recipientList.length >= 2" width="500" trigger="click">
                    <el-input v-model="organizeForm.recipientName" placeholder="请输入接收人员姓名/组织名称">
                      <el-button type="info" slot="append" icon="el-icon-search" @click="search('recipient')">
                      </el-button>
                      <el-button type="info" slot="append" icon="el-icon-close" @click="clear('recipient')">
                      </el-button>
                    </el-input>
                    <el-table ref="multipleTable" tooltip-effect="dark" @selection-change="handleSelectionChange"
                      :data="organizeForm.recipientListCopy" max-height="240">
                      <el-table-column width="30" type="selection">
                      </el-table-column>
                      <el-table-column min-width="70" property="name" label="姓名">
                      </el-table-column>
                      <el-table-column min-width="180" property="orgName" label="组织">
                      </el-table-column>
                      <el-table-column min-width="120" property="mobilePhone" label="电话">
                      </el-table-column>
                      <el-table-column fixed="right" label="操作" width="50">
                        <template slot-scope="scope">
                          <el-button type="text" size="small" @click.native.prevent="
                            handleClose('recipient', scope.row)
                            ">
                            移除
                          </el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                    <el-button size="small" type="text" @click="toggleSelection('recipient')">批量移除
                    </el-button>
                    <el-tag slot="reference" style="margin-top: 5px">
                      +{{ organizeForm.recipientList.length - 1 }}
                    </el-tag>
                  </el-popover>
                  <el-button type="info" slot="append" icon="el-icon-user"
                    @click="onOpenPeopleDialog('recipientDetermine')"></el-button>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :offset="0">
              <el-form-item label="发送内容:" prop="sendContent">
                <el-input v-model="sheetForm.sendContent" placeholder="请输入内容" type="textarea" :rows="4" clearable
                  style="width: 100%" show-word-limit maxlength="1000"></el-input>
              </el-form-item>
            </el-col>
          </template>
        </el-row>
      </el-card>
    </el-form>
    <dia-tissue-tree v-if="isDiaOrgsUserTree" :title="diaPeople.title" :visible.sync="diaPeople.visible"
      :showOrgsTree="diaPeople.showOrgsTree" :showContactUserTab="diaPeople.showContactUserTab"
      :professionalType="mainProfessionalType" @on-save="onSavePeople"
      :showContactOrgTab="diaPeople.showContactOrgTab" />
    <el-dialog width="420px" title="附件选择" :visible.sync="attachmentDialogVisible" :close-on-click-modal="false"
      :close-on-press-escape="false">
      <file-upload @change="changeFileData" @cancel="closeAttachmentDialog"></file-upload>
    </el-dialog>
    <el-dialog width="420px" title="影响业务列表附件选择" :visible.sync="incidenceDialogVisible" :close-on-click-modal="false"
      :close-on-press-escape="false">
      <file-upload @change="changeIncidenceFileData" @cancel="closeIncidenceDialog"></file-upload>
    </el-dialog>

    <el-dialog custom-class="involvedCircuitDialogTableVisible"
      :title="sheetForm.isSelectedInvolvedCircuit ? '' : '电路搜索'"
      :visible.sync="sheetForm.involvedCircuitDialogTableVisible">
      <el-form :model="sheetForm">
        <el-col :xs="24" :sm="8" :md="8" :offset="0">
          <el-form-item style="display: flex; white-space: nowrap; font-size: 14px" label="电路编号: ">
            <!-- 及一个省份枚举值 -->
            <el-input type="string" v-model="sheetForm.circuitNumber" placeholder="请输入电路编号">
              <el-button type="info" slot="append" icon="el-icon-search" @click="() => {
                searchCircuitNumber(1);
              }
              "></el-button>

              <el-button slot="append" icon="el-icon-close" @click="() => {
                sheetForm.circuitNumber = '';
                circuitTable.pageNum = 1;
                circuitTable.pageSize = 10;
                circuitTable.total = 0;
              }
              "></el-button>
            </el-input>
          </el-form-item>
        </el-col>
      </el-form>
      <!-- sheetForm.involvedCircuitTableData -->
      <el-table stripe v-loading="sheetForm.circuitLoading" ref="involvedCircuitMultipleTable"
        :data="sheetForm.involvedCircuitTableData" tooltip-effect="dark"
        style="width: 100%; height: 18rem; padding-bottom: 2.8rem"
        @selection-change="involvedCircuitHandleSelectionChange">
        <el-table-column type="selection" width="55"> </el-table-column>
        <el-table-column prop="circuitId" label="电路编号" :style="{
          // width: isSelectedInvolvedCircuit ? '80%' : '100%',
          width: '100%',
        }">
        </el-table-column>
        <!-- isSelectedInvolvedCircuit -->
        <el-table-column v-if="sheetForm.isSelectedInvolvedCircuit" fixed="right" label="操作" width="120">
          <template slot-scope="scope">
            <el-button @click.native.prevent="
              involvedCircuitDeleteRow(scope.$index, scope.row)
              " type="text" size="small">
              移除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination :current-page.sync="circuitTable.pageNum" :page-size.sync="circuitTable.pageSize"
        :page-sizes="[5, 10, 20]" :total="circuitTable.total" :pager-count="5" layout="->,total,sizes,prev, pager, next"
        background @size-change="searchCircuitNumberByPageSize" @current-change="searchCircuitNumberByPageNum">
      </el-pagination>


      <!-- <div
        class="pagePagination"
        style="display: flex; justify-content: end; margin-top: 1rem"
      >
        <el-select
          style="width: 3rem"
          v-model="itemsPerPage"
          placeholder="请选择"
          @change="handleItemsPerPageChange"
        >
          <el-option label="5条" :value="5"></el-option>
          <el-option label="10条" :value="10"></el-option>
          <el-option label="20条" :value="20"></el-option>
        </el-select>
        <el-pagination
          @size-change="handleItemsPerPageChange"
          background
          layout="prev, pager, next"
          :total="totalItems"
          :page-size="itemsPerPage"
          @current-change="handlePageChange"
          :current-page="currentPage"
        >
        </el-pagination>
      </div> -->
      <div v-if="sheetForm.isSelectedInvolvedCircuit" style="margin-top: 20px">
        <el-button @click="() => { }">批量移除</el-button>
      </div>
      <div v-if="!sheetForm.isSelectedInvolvedCircuit" slot="footer" class="dialog-footer">
        <el-button @click="sheetForm.involvedCircuitDialogTableVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmInvolvedCircuit">确 定</el-button>
      </div>
    </el-dialog>

    <work-order-temp :visible.sync="tempVisible" :networkTypeTop="0" networkType="一干" professionalType="3"
      @select-change="onTempSeleted"></work-order-temp>
  </head-fixed-layout>
</template>

<script>
import { mapGetters } from "vuex";
import moment from "moment";
import HeadFixedLayout from "./components/HeadFixedLayout.vue";
import DictSelect from "./components/DictSelect.vue";
import FileUpload from "./components/FileUpload.vue";
//import DiaOrgsUserTree from "./components/DiaOrgsUserTree.vue";
import DiaTissueTree from "@/plugin/backbone/components/common/DiaTissueTree";

import WorkOrderTemp from "./components/WorkOrderTemp.vue";
import UniversalPopupWindowEquipmentOrLines from './workOrderWaitDetail/components/universalPopupWindowEquipmentOrLines.vue';
import { apiDict, apiGetOrgInfo, searchCircuitNo } from "./api/CommonApi";
import {
  apiBuildSingle,
  apiSaveToDraft,
  apiBuildItCloudSingle,
  apiSaveToItCloudDraft,
  orderNgAuthApi,
} from "./api/WorkOrderDraftEdit";
import { mixin } from "../../../../mixins";
import { shallowRef } from "vue";
export default {
  name: "WorkOrderDraftEdit",
  components: {
    HeadFixedLayout,
    DictSelect,
    FileUpload,
    WorkOrderTemp,
    DiaTissueTree,
    UniversalPopupWindowEquipmentOrLines
  },
  mixins: [mixin],
  data() {
    var validHappenTime = (rule, value, callback) => {
      if (value === "" || value === null) {
        callback(new Error("请选择发生时间"));
      } else {
        let seconds = moment(
          this.sheetForm.createTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.sheetForm.happenTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        if (seconds < 0) {
          callback(new Error("“发生时间”不能晚于“建单时间”，请重新选择"));
        } else {
          callback();
        }
      }
    };
    return {
      circuitTable: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      //  分页新增
      items: [], // 所有数据项
      currentPage: 1, // 当前页码
      itemsPerPage: 5, // 每页显示的数据条数

      sheetForm: {
        createUser: "",
        sender: null, //建单人的登录名
        dept: "",
        deptId: null,
        createTime: "",
        title: "",
        source: "2",
        happenTime: "",
        urgent: "",
        acceptTimeLimit: "30",
        special: null,
        netType: "",
        opticalCable: "",
        alarmType: "",
        phenomenon: "",
        jobComment: "定位故障，恢复业务。",
        incidenceFile: "",
        incidence: "",
        remarks: "",
        attachmentFile: "", //附件
        fileVirtual: "",
        builderZs: "",
        builderZsUserId: "",
        builderZsOrgId: "",
        builderZsUserName: "",
        builderZsOrgName: "",
        builderCs: "",
        builderCsUserId: "",
        builderCsOrgId: "",
        builderCsUserName: "",
        builderCsOrgName: "",
        notifier: "", //通知人
        notifierUserName: "",
        notifierOrgName: "",
        notifierUserId: "",
        notifierOrgId: "",
        sms: 0,
        recipient: "",
        recipientUserName: "",
        recipientOrgName: "",
        recipientUserId: "",
        recipientOrgId: "",
        sendContent:
          "1、光缆：**， ××××光缆在××省××（网元）与××（网元）之间中断，影响××××波分系统，承载的SDH系统保护倒换，已经派单给省公司，并电话通知处理，现在该省正在利用备用纤芯倒接;(该段落OLP正常切换;)(分公司正在协调处理;)2、设备：××， ××××系统中断，初步怀疑是××网元××板卡问题，承载的业务目前不受影响，已经派单给省公司，并电话通知处理，现在分公司正在利用备用波道倒接；(该系统承载的SDH系统正常倒换，业务不受影响；)(该系统承载的SDH系统倒换不正常，已经影响业务；)(影响的业务有（无）××重保电路。)",
        agentManDetail: null,
        copyManDetail: null,
        recipientDetailUserName: null,
        notifierUserDetail: null,
        //IT云设备
        estimatedProTimeLimit: null,
        cloudPoolType: "IT云设备",
        busName: null,
        faultLevel: "2",

        // 关于新增的 “涉及电路”、“是否重保”、“重保任务” 字段 的所有相关数据
        circuitLoading: false,
        involvedCircuit: "",
        newInvolvedCircuit: "", // 新的电路涉及暂存值
        involvedCircuitArray: [],
        involvedCircuitDialogTableVisible: false,
        circuitNumber: "",
        formLabelWidth: "120px",
        // 请求获取的数据
        involvedCircuitRequestTableData: [],
        // 用户当前选中了的数据
        involvedCircuitTableData: [],
        involvedCircuitMultipleSelection: [],
        isSelectedInvolvedCircuit: false,
        isReinsuranceVal: "否",
        isReinsuranceArr: [
          { name: "是", isReinsuranceVal: "是" },
          { name: "否", isReinsuranceVal: "否" },
        ],
        reassuranceTask: "",
        involvedCircuitInputWidth: "10",
        subAlarmType: ""
      },
      organizeForm: {
        builderZsList: [],
        builderZsListCopy: [],
        builderZsName: "",
        builderCsList: [],
        builderCsListCopy: [],
        builderCsName: "",
        recipientList: [],
        recipientListCopy: [],
        recipientName: "",
        notifierList: [],
        notifierListCopy: [],
        notifierName: "",
      },
      sheetFormRules: {
        createUser: [{ required: true, message: "请输入建单人" }],
        dept: [{ required: true, message: "请输入建单部门" }],
        createTime: [{ required: true, message: "请选择建单时间" }],
        title: [
          { required: true, message: "工单主题不能为空" },
          {
            validator: this.checkLength,
            max: 400,
            form: "sheetForm",
            messsage: "已超过填写字数上限",
          },
        ],
        source: [{ required: true, message: "请选择工单来源" }],
        happenTime: [{ validator: validHappenTime, required: true }],
        urgent: [{ required: true, message: "请选择紧急程度" }],
        acceptTimeLimit: [
          { required: true, message: "请输入受理时限" },
          {
            validator: this.checkLength,
            max: 11,
            form: "sheetForm",
            messsage: "已超过填写字数上限",
          },
        ],
        jobComment: [
          {
            validator: this.checkLength,
            max: 3000,
            form: "sheetForm",
            messsage: "已超过填写字数上限",
          },
        ],
        special: [{ required: true, message: "请选择所属专业" }],
        netType: [{ required: true, message: "请选择网络类型" }],
        alarmType: [{ required: true, message: "请选择告警类别" }],
        subAlarmType: [{ required: true, message: "请选择故障类别" }],
        phenomenon: [
          { required: true, message: "请输入故障现象" },
          {
            validator: this.checkLength,
            max: 3000,
            form: "sheetForm",
            messsage: "已超过填写字数上限",
          },
        ],
        incidence: [
          {
            validator: this.checkLength,
            max: 1000,
            form: "sheetForm",
            messsage: "已超过填写字数上限",
          },
        ],
        remarks: [
          {
            validator: this.checkLength,
            max: 255,
            form: "sheetForm",
            messsage: "已超过填写字数上限",
          },
        ],
        cloudPoolType: [
          {
            validator: this.checkLength,
            max: 100,
            form: "sheetForm",
            messsage: "已超过填写字数上限",
          },
        ],
        busName: [
          {
            validator: this.checkLength,
            max: 100,
            form: "sheetForm",
            messsage: "已超过填写字数上限",
          },
        ],
        sendContent: [
          {
            required: true,
            message: "请输入发送内容",
            trigger: "blur",
          },
          {
            validator: this.checkLength,
            max: 1000,
            form: "sheetForm",
            messsage: "已超过填写字数上限",
          },
        ],
        opticalCable: [
          {
            validator: this.checkLength,
            max: 1000,
            form: "sheetForm",
            messsage: "已超过填写字数上限",
          },
        ],
        builderZs: [{ required: true, message: "建单主送人不能为空" }],
        estimatedProTimeLimit: [{ required: true, message: "请选择内容" }],
        // faultLevel: [{ required: true, message: "请选择工单优先级" }],
        //
        // 关于新增的 “涉及电路”、“是否重保”、“重保任务” 字段 的所有相关数据
        newInvolvedCircuit: [{ required: true, message: "请输入电路编号" }],
        isReinsuranceVal: [{ required: true, message: "请选择是否重保" }],
        reassuranceTask: [{ required: true, message: "请输入重保任务" }],
      },
      diaPeople: {
        visible: false,
        title: "",
        saveName: "",
        saveTitleMap: {
          lordSentDetermine: "建单人主送",
          ccDetermine: "建单人抄送",
          notifierDetermine: "通知人选择",
          recipientDetermine: "接收人选择",
        },
        showOrgsTree: true,
        showOrgsTreeMap: {
          recipientDetermine: false,
          lordSentDetermine: true,
          ccDetermine: true,
        },
        showContactUserTab: false,
        showContactUserTabMap: {
          recipientDetermine: true,
          lordSentDetermine: true,
          ccDetermine: true,
          notifierDetermine: true,
        },
        showContactOrgTab: false,
        showContactOrgTabMap: {
          lordSentDetermine: true,
          ccDetermine: true,
          notifierDetermine: true,
          recipientDetermine: false,
        },
      },
      attachmentDialogVisible: false,
      isDiaOrgsUserTree: false,
      importForm: {
        //附件
        attachmentFileList: [],
        //影响业务列表附件
        incidenceFileList: [],
      },
      urgentOption: [],
      opticFiber: [],
      allInfos: [],
      sheetCommitLoading: false,
      sheetSaveLoading: false,
      incidenceDialogVisible: false,
      isOpticalCableShow: true,
      isOpticalCableIntShow: false,
      orgInfoData: {}, //组织信息数据缓存
      route: null,
      userInfoData: {},
      //预估处理时限
      estimatedProTimeLimitOption: [
        { id: "4", name: "4小时" },
        { id: "8", name: "8小时" },
      ],
      // 建单模板
      tempVisible: false,
    };
  },
  computed: {
    ...mapGetters(["userInfo", "token"]),
    mainProfessionalType() {
      return this.$route.query.mainProfessionalType;
    },
    isIt() {
      return this.mainProfessionalType == "IT云";
    },
    // 分页新增
    totalItems() {
      return this.sheetForm.involvedCircuitTableData.length; // 总数据条数
    },
    currentPageData() {
      const start = (this.currentPage - 1) * this.itemsPerPage;
      const end = start + this.itemsPerPage;
      return this.sheetForm.involvedCircuitTableData.slice(start, end); // 当前页的数据
    },
  },
  watch: {
    "sheetForm.alarmType": {
      handler(newVal) {
        console.log("sheetForm.alarmTypexxxxxxxxxxxxxxx===", this.sheetForm.subAlarmType);
        if (this.mainProfessionalType == "骨干网") {
          if (1 == newVal) {
            this.sheetForm.subAlarmType = "14001";
          } else {
            this.sheetForm.subAlarmType = "14002";
          }
        }
      },
    },
    "sheetForm.involvedCircuit": {
      handler(newV) {
        console.log('"sheetForm.involvedCircuit"', newV);
        this.sheetForm.newInvolvedCircuit = "";
        newV.forEach((item, i) => {
          // console.log(i, newV.length - 1);
          if (i == newV.length - 1) {
            this.sheetForm.newInvolvedCircuit += item["circuitId"];
          } else {
            this.sheetForm.newInvolvedCircuit =
              this.sheetForm.newInvolvedCircuit + item["circuitId"] + "、";
          }
        });
        // const a = document.querySelector(
        //   ".involvedCircuitInput .el-input__prefix"
        // );
        const b = document.querySelectorAll(
          ".involvedCircuitInput .el-input__prefix div div span"
        );

        // console.log(this.sheetForm.newInvolvedCircuit, a, b.length - 1);
        const len = b.length - 1;
        if (len != 0) {
          // a.style.setProperty("z-index", "-1", "important");

          this.$refs.involvedCircuitInput.$refs.input.style.zIndex = "0";
          this.$refs.involvedCircuitInput.$refs.input.style.position = "relative";
          // position: relative;
          console.log(this.$refs.involvedCircuitInput);
        }
        if (len == 0) {
          // a.style.setProperty("z-index", "-1", "important");

          this.$refs.involvedCircuitInput.$refs.input.style.zIndex = "9";
          this.$refs.involvedCircuitInput.$refs.input.style.position = "relative";
          // position: relative;
          console.log(this.$refs.involvedCircuitInput);
        }
      },
      deep: true,
    },
    currentPage: {
      handler(newVal) {
        console.log(newVal);
      },
    },
    "sheetForm.urgent": {
      handler(newV) {
        if (this.isIt) {
          if (newV == 0 || newV == 1 || newV == 2) {
            this.sheetForm.estimatedProTimeLimit = "8";
          } else {
            this.sheetForm.estimatedProTimeLimit = "4";
          }
        }
      },
      deep: true,
    },
    "organizeForm.builderZsList": {
      handler(newV) {
        if (newV.length > 0) {
          this.sheetForm.builderZs = "已选";
        } else {
          this.sheetForm.builderZs = "";
        }
      },
      deep: true,
    },
    "organizeForm.recipientList": {
      handler(newV) {
        if (newV.length > 0) {
          this.sheetForm.recipient = "已选";
        } else {
          this.sheetForm.recipient = "";
        }
      },
      deep: true,
    },
    "sheetForm.isReinsuranceVal": {
      handler(newV) {
        console.log(newV);
        if (newV == "是") {
          this.sheetFormRules["reassuranceTask"] = [
            { required: true, message: "请输入重保任务" },
          ];
        } else {
          this.sheetFormRules["reassuranceTask"] = [{ required: false }];
        }
      },
      deep: true,
    },
  },
  created() {
    this.route = this.$route;
    this.getUrgentOption();
    this.getOpticFiber();
    this.getOrderNgAuth();
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.updateInvolvedCircuitInputWidth);
  },
  mounted() {
    console.log("isIt===", this.isIt)
    if (!this.isIt) {
      this.$nextTick(() => {
        const inputElement = this.$refs.involvedCircuitInput.$el;
        this.sheetForm.involvedCircuitInputWidth = inputElement.offsetWidth;
        console.log("Input width:", inputElement.offsetWidth);
      });
      window.addEventListener("resize", this.updateInvolvedCircuitInputWidth);
    }


    this.sheetForm.special = this.isIt ? "23" : "3";
    this.sheetForm.urgent = this.isIt ? "2" : "0";
    //this.isIt ? "15" : "30";
    this.sheetForm.acceptTimeLimit = 30;
    this.userInfoData = JSON.parse(this.userInfo.attr2);
    this.sheetForm.createTime = moment(Date.now()).format(
      "YYYY-MM-DD HH:mm:ss"
    );
    this.sheetForm.happenTime = moment(Date.now()).format(
      "YYYY-MM-DD HH:mm:ss"
    );
    this.getOrgInfo();

    window["__process_response_from_getDevice"] = value => {
      let objStr = "";
      let result = JSON.parse(value);
      let objArray = JSON.parse(result?.system);
      objArray.forEach(obj => {
        objStr += obj.sysName + ",";
      });
      if (objStr) {
        objStr = objStr.substring(0, objStr.length - 1);
      }

      this.sheetForm.title += this.sheetForm.title ? "," + objStr : objStr;
    };
  },
  methods: {
    getOrgInfo() {
      apiGetOrgInfo()
        .then(res => {
          if (res.status == "0") {
            this.orgInfoData = res?.data ?? {};
            this.sheetForm.createUser = res?.data?.trueName ?? "";
            this.sheetForm.dept = res?.data?.orgInfo?.orgName ?? "";
            this.sheetForm.deptId = res?.data?.orgInfo?.orgId ?? "";
            this.sheetForm.sender = res?.data?.userName ?? "";
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    getOpticFiber() {
      let param = {
        dictTypeCode: "10108",
      };
      apiDict(param)
        .then(res => {
          if (res.code == 200) {
            this.opticFiber = res?.data ?? [];
            this.opticFiber.reverse();
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    querySearch(queryString, callback) {
      this.allInfos = this.opticFiber.map(function (obj) {
        return {
          value: obj.dictName,
          id: obj.dictCode,
        };
      });
      var opticFiber = this.allInfos;
      var results = queryString
        ? opticFiber.filter(this.createFilter(queryString))
        : opticFiber;
      callback(results);
    },
    changeNetType() {
      if (this.sheetForm.netType == "一干") {
        this.isOpticalCableShow = true;
        this.isOpticalCableIntShow = false;
      } else if (this.sheetForm.netType == "国际") {
        this.isOpticalCableShow = false;
        this.isOpticalCableIntShow = true;
      } else {
        this.isOpticalCableShow = true;
        this.isOpticalCableIntShow = false;
      }
    },
    createFilter(queryString) {
      return opticFiber => {
        return opticFiber.value.toLowerCase().match(queryString.toLowerCase());
      };
    },
    getUrgentOption() {
      let param = {
        dictTypeCode: "10001",
      };
      apiDict(param)
        .then(res => {
          if (res.code == 200) {
            this.urgentOption = res?.data ?? [];
            this.urgentOption.reverse();
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    onSheetSave() {
      this.entering();
      this.$confirm("是否保存到草稿？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          // this.$refs.sheetForm.validate(valid => {
          //   if (valid) {
          if (this.sheetForm.title != null && this.sheetForm.title != "") {
            let formData = new FormData();
            if (this.importForm.attachmentFileList.length > 0) {
              for (let item of this.importForm.attachmentFileList) {
                formData.append("orderFiles", item.raw);
              }
            }
            if (this.importForm.incidenceFileList.length > 0) {
              for (let item of this.importForm.incidenceFileList) {
                formData.append("affectFiles", item.raw);
              }
            }
            this.sheetSaveLoading = true;
            let self = this;

            if (this.mainProfessionalType == "骨干网") {
              this.formatNewInvolvedCircuit();
              let param = {
                sheetInfo: {
                  sender: this.sheetForm.sender, //建单人
                  senderDeptName: this.sheetForm.dept, //建单部门
                  senderName: this.sheetForm.createUser, //建单人中文名
                  senderDept: this.sheetForm.deptId, //建单部门ID
                  sheetCreateTime: this.sheetForm.createTime, //建单时间
                  sheetTitle: this.sheetForm.title, //工单主题
                  createType: this.sheetForm.source, //工单来源
                  alarmCreateTime: this.sheetForm.happenTime, //发生时间
                  emergencyLevel: this.sheetForm.urgent, //紧急程度
                  acceptTimeLimit: this.sheetForm.acceptTimeLimit, //处理时限
                  professionalType: this.sheetForm.special, //所属专业
                  networkType: this.sheetForm.netType,
                  // networkType:
                  //   self.sheetForm.netType == ""
                  //     ? null
                  //     : self.sheetForm.netType == "25100507"
                  //     ? "一干"
                  //     : "国际", //网络类型,
                  opticFiberCableSegment: this.sheetForm.opticalCable, //光缆名称
                  orgType: this.sheetForm.alarmType, //告警类别
                  subAlarmType: this.sheetForm.subAlarmType,
                  faultPhenomenon: this.sheetForm.phenomenon, //故障现象
                  workContent: this.sheetForm.jobComment, //工作内容
                  effectRange: this.sheetForm.incidence, //影响范围
                  falutComment: this.sheetForm.remarks, //备注
                  agentDeptCode: this.sheetForm.builderZsOrgId, //主送是组织 传入组织ID
                  agentManId: this.sheetForm.builderZsUserId, //主送是人员 传入人员ID
                  agentMan: this.sheetForm.builderZsUserName, //主送人员 中文名称
                  agentDeptName: this.sheetForm.builderZsOrgName, //主送部门 中文名称
                  sheetStatus: 1, //1:保存到草稿 2:提交到待办
                  copyDeptCode: this.sheetForm.builderCsOrgId, //抄送是组织 传入组织ID
                  copyManId: this.sheetForm.builderCsUserId, //抄送是用户 传入用户ID
                  copyMan: this.sheetForm.builderCsUserName,
                  copyDeptName: this.sheetForm.builderCsOrgName,
                  noticeMan: this.sheetForm.notifierUserName,
                  noticeManId: this.sheetForm.notifierUserId,
                  noticeDeptCode: this.sheetForm.notifierOrgId,
                  noticeDeptName: this.sheetForm.notifierOrgName,
                  isSendSms: this.sheetForm.sms, //是否短信通知
                  smsToUserid: this.sheetForm.recipientUserId,
                  smsToUsername: this.sheetForm.recipientUserName,
                  smsToDeptCode: this.sheetForm.recipientOrgId,
                  smsToDeptName: this.sheetForm.recipientOrgName,
                  sendContent:
                    this.sheetForm.sms == "0"
                      ? null
                      : this.sheetForm.sendContent,
                  agentManDetail: this.sheetForm.agentManDetail,
                  copyManDetail: this.sheetForm.copyManDetail,
                  recipientDetailUserName:
                    self.sheetForm.recipientDetailUserName,
                  notifierUserDetail: self.sheetForm.notifierUserDetail,
                  faultLevel: self.sheetForm.faultLevel,
                  // 涉及电路、是否重保、重保任务

                  isReinstated: this.sheetForm.isReinsuranceVal,
                  reinstatedTask: this.sheetForm.reassuranceTask,
                },
                // 涉及电路放到外面
                circuitList:
                  (this.sheetForm.involvedCircuit.length > 0
                    ? this.sheetForm.involvedCircuit
                    : this.sheetForm.newInvolvedCircuit) ||
                  this.sheetForm.newInvolvedCircuit ||
                  [],
              };
              formData.append("jsonParam", JSON.stringify(param));
              //保存到草稿
              apiSaveToDraft(formData)
                .then(res => {
                  if (res.status == "0") {
                    this.$message.success("保存成功");
                    this.closeAndTurnAroundMyDraft();
                  } else {
                    this.$message.error("保存失败");
                  }
                  this.sheetSaveLoading = false;
                })
                .catch(error => {
                  console.log(error);
                  this.$message.error("保存失败");
                  this.sheetSaveLoading = false;
                });
            } else if (this.isIt) {
              let param2 = {
                sheetInfo: {
                  sender: self.sheetForm.sender, //建单人
                  senderDeptName: self.sheetForm.dept, //建单部门
                  senderName: self.sheetForm.createUser, //建单人中文名
                  senderDept: self.sheetForm.deptId, //建单部门ID
                  sheetCreateTime: self.sheetForm.createTime, //建单时间
                  sheetTitle: self.sheetForm.title, //工单主题
                  createType: self.sheetForm.source, //工单来源
                  alarmCreateTime: self.sheetForm.happenTime, //发生时间
                  emergencyLevel: self.sheetForm.urgent, //紧急程度
                  acceptTimeLimit: self.sheetForm.acceptTimeLimit, //受理时限
                  estimatedProTimeLimit: self.sheetForm.estimatedProTimeLimit, //预估处理时限
                  professionalType: self.sheetForm.special, //所属专业
                  orgType: self.sheetForm.alarmType, //告警类别
                  cloudPoolType: self.sheetForm.cloudPoolType, //云池类型
                  busName: self.sheetForm.busName, //业务名称
                  faultPhenomenon: self.sheetForm.phenomenon, //故障现象
                  falutComment: self.sheetForm.remarks, //备注
                  agentDeptCode: self.sheetForm.builderZsOrgId, //主送是组织 传入组织ID
                  agentManId: self.sheetForm.builderZsUserId, //主送是人员 传入人员ID
                  agentMan: self.sheetForm.builderZsUserName, //主送人员 中文名称
                  agentDeptName: self.sheetForm.builderZsOrgName, //主送部门 中文名称
                  sheetStatus: 1, //1是草稿 2是提交到待办
                  copyDeptCode: self.sheetForm.builderCsOrgId, //抄送是组织 传入组织ID
                  copyManId: self.sheetForm.builderCsUserId, //抄送是用户 传入用户ID
                  copyMan: self.sheetForm.builderCsUserName,
                  copyDeptName: self.sheetForm.builderCsOrgName,
                  isSendSms: self.sheetForm.sms, //是否短信通知
                  smsToUserid: self.sheetForm.recipientUserId,
                  smsToUsername: self.sheetForm.recipientUserName,
                  smsToDeptCode: self.sheetForm.recipientOrgId,
                  smsToDeptName: self.sheetForm.recipientOrgName,
                  sendContent:
                    self.sheetForm.sms == "0"
                      ? null
                      : self.sheetForm.sendContent,
                  agentManDetail: self.sheetForm.agentManDetail,
                  copyManDetail: this.sheetForm.copyManDetail,
                  recipientDetailUserName:
                    self.sheetForm.recipientDetailUserName,
                  faultLevel: self.sheetForm.faultLevel,
                },
              };
              formData.append("jsonParam", JSON.stringify(param2));
              //保存到IT云草稿
              apiSaveToItCloudDraft(formData)
                .then(res => {
                  if (res.status == "0") {
                    this.$message.success("保存成功");
                    this.closeAndTurnAroundMyDraft();
                  } else {
                    this.$message.error("保存失败");
                  }
                  this.sheetSaveLoading = false;
                })
                .catch(error => {
                  console.log(error);
                  this.$message.error("保存失败");
                  this.sheetSaveLoading = false;
                });
            }
          } else {
            this.$message.warning("请填写主题后在做保存！");
            return false;
          }
          // });
        })
        .catch(() => { });
    },
    onSheetSubmit() {
      this.entering();
      this.$confirm("是否提交工单？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        closeOnClickModal: false,
        type: "warning",
      }).then(() => {
        this.$refs.sheetForm.validate((valid, a) => {
          if (this.uncheck(a)) {
            this.sheetCommitLoading = true;
            let formData = new FormData();
            if (this.mainProfessionalType == "骨干网") {
              if (this.importForm.attachmentFileList.length > 0) {
                for (let item of this.importForm.attachmentFileList) {
                  formData.append("orderFiles", item.raw);
                }
              }
              if (this.importForm.incidenceFileList.length > 0) {
                for (let item of this.importForm.incidenceFileList) {
                  formData.append("affectFiles", item.raw);
                }
              }
              let self = this;
              this.formatNewInvolvedCircuit();
              let param = {
                sheetInfo: {
                  sender: self.sheetForm.sender, //建单人
                  senderDeptName: self.sheetForm.dept, //建单部门
                  senderName: self.sheetForm.createUser, //建单人中文名
                  senderDept: self.sheetForm.deptId, //建单部门ID
                  sheetCreateTime: self.sheetForm.createTime, //建单时间
                  sheetTitle: self.sheetForm.title, //工单主题
                  createType: self.sheetForm.source, //工单来源
                  alarmCreateTime: self.sheetForm.happenTime, //发生时间
                  emergencyLevel: self.sheetForm.urgent, //紧急程度
                  acceptTimeLimit: self.sheetForm.acceptTimeLimit, //处理时限
                  professionalType: self.sheetForm.special, //所属专业
                  networkType: self.sheetForm.netType, //== "25100507" ? "一干" : "国际", //网络类型
                  opticFiberCableSegment: self.sheetForm.opticalCable, //光缆名称
                  orgType: self.sheetForm.alarmType, //告警类别
                  faultPhenomenon: self.sheetForm.phenomenon, //故障现象
                  workContent: self.sheetForm.jobComment, //工作内容
                  effectRange: self.sheetForm.incidence, //影响范围
                  falutComment: self.sheetForm.remarks, //备注
                  agentDeptCode: self.sheetForm.builderZsOrgId, //主送是组织 传入组织ID
                  agentManId: self.sheetForm.builderZsUserId, //主送是人员 传入人员ID
                  agentMan: self.sheetForm.builderZsUserName, //主送人员 中文名称
                  agentDeptName: self.sheetForm.builderZsOrgName, //主送部门 中文名称
                  sheetStatus: 2, //1是草稿 2是提交到待办
                  copyDeptCode: self.sheetForm.builderCsOrgId, //抄送是组织 传入组织ID
                  copyManId: self.sheetForm.builderCsUserId, //抄送是用户 传入用户ID
                  copyMan: self.sheetForm.builderCsUserName,
                  copyDeptName: self.sheetForm.builderCsOrgName,
                  noticeMan: self.sheetForm.notifierUserName,
                  noticeManId: self.sheetForm.notifierUserId,
                  noticeDeptCode: self.sheetForm.notifierOrgId,
                  noticeDeptName: self.sheetForm.notifierOrgName,
                  isSendSms: self.sheetForm.sms, //是否短信通知
                  smsToUserid: self.sheetForm.recipientUserId,
                  smsToUsername: self.sheetForm.recipientUserName,
                  smsToDeptCode: self.sheetForm.recipientOrgId,
                  smsToDeptName: self.sheetForm.recipientOrgName,
                  subAlarmType: self.sheetForm.subAlarmType,
                  sendContent:
                    self.sheetForm.sms == "0"
                      ? null
                      : self.sheetForm.sendContent,
                  agentManDetail: self.sheetForm.agentManDetail,
                  copyManDetail: this.sheetForm.copyManDetail,
                  recipientDetailUserName:
                    self.sheetForm.recipientDetailUserName,
                  notifierUserDetail: self.sheetForm.notifierUserDetail,
                  faultLevel: self.sheetForm.faultLevel,
                  // 涉及电路、是否重保、重保任务

                  isReinstated: this.sheetForm.isReinsuranceVal,
                  reinstatedTask: this.sheetForm.reassuranceTask,
                },
                // 涉及电路放到外面
                circuitList:
                  (this.sheetForm.involvedCircuit.length > 0
                    ? this.sheetForm.involvedCircuit
                    : this.sheetForm.newInvolvedCircuit) ||
                  this.sheetForm.newInvolvedCircuit ||
                  [],
              };
              formData.append("jsonParam", JSON.stringify(param));
              apiBuildSingle(formData)
                .then(res => {
                  if (res.status == "0") {
                    this.$message.success("工单提交成功");
                    this.closeAndTurnAroundTo();
                  } else {
                    if (res.msg.indexOf(":")) {
                      var n = res.msg.split(":");
                      this.$message.error("工单提交失败，原因：" + n[1]);
                    } else {
                      this.$message.error("工单提交失败");
                    }
                    this.closeAndTurnAroundRead();
                  }
                  this.sheetCommitLoading = false;
                })
                .catch(error => {
                  console.log(error);
                  if (error.msg.indexOf(":")) {
                    var n = error.msg.split(":");
                    this.$message.error("工单提交失败，原因：" + n[1]);
                  } else {
                    this.$message.error("工单提交失败");
                  }
                  this.closeAndTurnAroundRead();
                  this.sheetCommitLoading = false;
                });
            } else if (this.isIt) {
              if (this.importForm.attachmentFileList.length > 0) {
                for (let item of this.importForm.attachmentFileList) {
                  formData.append("orderFiles", item.raw);
                }
              }
              let self = this;
              let param = {
                sheetInfo: {
                  sender: self.sheetForm.sender, //建单人
                  senderDeptName: self.sheetForm.dept, //建单部门
                  senderName: self.sheetForm.createUser, //建单人中文名
                  senderDept: self.sheetForm.deptId, //建单部门ID
                  sheetCreateTime: self.sheetForm.createTime, //建单时间
                  sheetTitle: self.sheetForm.title, //工单主题
                  createType: self.sheetForm.source, //工单来源
                  alarmCreateTime: self.sheetForm.happenTime, //发生时间
                  emergencyLevel: self.sheetForm.urgent, //紧急程度
                  acceptTimeLimit: self.sheetForm.acceptTimeLimit, //受理时限
                  estimatedProTimeLimit: self.sheetForm.estimatedProTimeLimit, //预估处理时限
                  professionalType: self.sheetForm.special, //所属专业
                  orgType: self.sheetForm.alarmType, //告警类别
                  cloudPoolType: self.sheetForm.cloudPoolType, //云池类型
                  busName: self.sheetForm.busName, //业务名称
                  faultPhenomenon: self.sheetForm.phenomenon, //故障现象
                  falutComment: self.sheetForm.remarks, //备注
                  agentDeptCode: self.sheetForm.builderZsOrgId, //主送是组织 传入组织ID
                  agentManId: self.sheetForm.builderZsUserId, //主送是人员 传入人员ID
                  agentMan: self.sheetForm.builderZsUserName, //主送人员 中文名称
                  agentDeptName: self.sheetForm.builderZsOrgName, //主送部门 中文名称
                  sheetStatus: 2, //1是草稿 2是提交到待办
                  copyDeptCode: self.sheetForm.builderCsOrgId, //抄送是组织 传入组织ID
                  copyManId: self.sheetForm.builderCsUserId, //抄送是用户 传入用户ID
                  copyMan: self.sheetForm.builderCsUserName,
                  copyDeptName: self.sheetForm.builderCsOrgName,
                  isSendSms: self.sheetForm.sms, //是否短信通知
                  smsToUserid: self.sheetForm.recipientUserId,
                  smsToUsername: self.sheetForm.recipientUserName,
                  smsToDeptCode: self.sheetForm.recipientOrgId,
                  smsToDeptName: self.sheetForm.recipientOrgName,
                  sendContent:
                    self.sheetForm.sms == "0"
                      ? null
                      : self.sheetForm.sendContent,
                  agentManDetail: self.sheetForm.agentManDetail,
                  copyManDetail: this.sheetForm.copyManDetail,
                  recipientDetailUserName:
                    self.sheetForm.recipientDetailUserName,
                  faultLevel: self.sheetForm.faultLevel,
                },
              };
              formData.append("jsonParam", JSON.stringify(param));
              apiBuildItCloudSingle(formData)
                .then(res => {
                  if (res.status == "0") {
                    this.$message.success("工单提交成功");
                    this.closeAndTurnAroundTo();
                  } else {
                    if (res.msg.indexOf(":")) {
                      var n = res.msg.split(":");
                      this.$message.error("工单提交失败，原因：" + n[1]);
                    } else {
                      this.$message.error("工单提交失败");
                    }
                    this.closeAndTurnAroundRead();
                  }
                  this.sheetCommitLoading = false;
                })
                .catch(error => {
                  console.log(error);
                  if (error.msg.indexOf(":")) {
                    var n = error.msg.split(":");
                    this.$message.error("工单提交失败，原因：" + n[1]);
                  } else {
                    this.$message.error("工单提交失败");
                  }
                  this.closeAndTurnAroundRead();
                  this.sheetCommitLoading = false;
                });
            }
          } else {
            return false;
          }
        });
      });
    },
    closeAndTurnAroundRead() {
      this.$router.replace({
        name: "redirect",
        params: {
          skipType: "query",
          view: this.$route,
          isCurrentRoute: true,
        },
      });
    },
    onOpenPeopleDialog(diaSaveName) {
      this.isDiaOrgsUserTree = false;
      this.diaPeople.title = this.diaPeople.saveTitleMap[diaSaveName];
      this.diaPeople.saveName = diaSaveName;
      this.diaPeople.showOrgsTree =
        this.diaPeople.showOrgsTreeMap[diaSaveName] ?? true;
      this.diaPeople.showContactUserTab =
        this.diaPeople.showContactUserTabMap[diaSaveName] ?? false;
      this.diaPeople.showContactOrgTab =
        this.diaPeople.showContactOrgTabMap[diaSaveName] ?? false;
      this.diaPeople.visible = true;
      this.$nextTick(() => {
        this.isDiaOrgsUserTree = true;
      });
    },
    onSavePeople(val) {
      this[this.diaPeople.saveName](val);
    },
    resetForm() {
      this.$refs["sheetForm"].resetFields();
      this.organizeForm.builderZsList = [];
      this.organizeForm.builderZsListCopy = [];
      this.organizeForm.builderZsName = "";
      this.organizeForm.builderCsList = [];
      this.organizeForm.builderCsListCopy = [];
      this.organizeForm.builderCsName = "";
      this.organizeForm.recipientList = [];
      this.organizeForm.recipientListCopy = [];
      this.organizeForm.recipientName = "";
    },
    //建单人主送确定
    lordSentDetermine({
      usersChecked,
      orgsChecked,
      selectionUser,
      contactSelectionUser,
      contactSelectionOrg,
    }) {
      if (contactSelectionUser && contactSelectionUser.length > 0) {
        contactSelectionUser.map(item => {
          let zs = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.userName;
          });
          if (zs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderZsList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (selectionUser && selectionUser.length > 0) {
        selectionUser.map(item => {
          let zs = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.userName;
          });
          if (zs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderZsList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      } else {
        usersChecked.map(item => {
          let zs = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.id;
          });
          if (zs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderZsList.push({
              bz: "user",
              id: item.id,
              name: item.name,
              orgName: item.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (contactSelectionOrg && contactSelectionOrg.length > 0) {
        contactSelectionOrg.map(item => {
          let zsOrg = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.orgId;
          });
          if (zsOrg > -1 && this.isIt) {
            this.$message({
              message: "it云只能选择一个组织",
              type: "warning",
            });
          } else if (zsOrg > -1) {
            this.$message({
              message: "选择的组织已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderZsList.push({
              bz: "org",
              id: item.orgId + "",
              orgName: item.fullOrgName,
            });
          }
        });
      }
      if (orgsChecked && orgsChecked.length > 0) {
        orgsChecked.map(item => {
          let zsOrg = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.id;
          });
          if (zsOrg > -1 && this.isIt) {
            this.$message({
              message: "it云只能选择一个组织",
              type: "warning",
            });
          } else if (zsOrg > -1) {
            this.$message({
              message: "选择的组织已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderZsList.push({
              bz: "org",
              id: item.id,
              orgName: item.name,
            });
          }
        });
      }
      this.organizeForm.builderZsListCopy = JSON.parse(
        JSON.stringify(this.organizeForm.builderZsList)
      );
    },
    //抄送人确定
    ccDetermine({
      usersChecked,
      orgsChecked,
      selectionUser,
      contactSelectionUser,
      contactSelectionOrg,
    }) {
      if (contactSelectionUser && contactSelectionUser.length > 0) {
        contactSelectionUser.map(item => {
          let cs = this.organizeForm.builderCsList.findIndex(val => {
            return val.id === item.userName;
          });
          if (cs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderCsList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (selectionUser && selectionUser.length > 0) {
        selectionUser.map(item => {
          let cs = this.organizeForm.builderCsList.findIndex(val => {
            return val.id === item.userName;
          });
          if (cs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderCsList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      } else {
        usersChecked.map(item => {
          let cs = this.organizeForm.builderCsList.findIndex(val => {
            return val.id === item.id;
          });
          if (cs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderCsList.push({
              bz: "user",
              id: item.id,
              name: item.name,
              orgName: item.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (contactSelectionOrg && contactSelectionOrg.length > 0) {
        contactSelectionOrg.map(item => {
          let csOrg = this.organizeForm.builderCsList.findIndex(val => {
            return val.id === item.orgId;
          });
          if (csOrg > -1 && this.isIt) {
            this.$message({
              message: "it云只能选择一个组织",
              type: "warning",
            });
          } else if (csOrg > -1) {
            this.$message({
              message: "选择的组织已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderCsList.push({
              bz: "org",
              id: item.orgId + "",
              orgName: item.fullOrgName,
            });
          }
        });
      }
      if (orgsChecked && orgsChecked.length > 0) {
        orgsChecked.map(item => {
          let csOrg = this.organizeForm.builderCsList.findIndex(val => {
            return val.id === item.id;
          });
          if (csOrg > -1 && this.isIt) {
            this.$message({
              message: "it云只能选择一个组织",
              type: "warning",
            });
          } else if (csOrg > -1) {
            this.$message({
              message: "选择的组织已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderCsList.push({
              bz: "org",
              id: item.id,
              orgName: item.name,
            });
          }
        });
      }
      this.organizeForm.builderCsListCopy = JSON.parse(
        JSON.stringify(this.organizeForm.builderCsList)
      );
    },
    //通知人确定
    notifierDetermine({
      usersChecked,
      orgsChecked,
      selectionUser,
      contactSelectionUser,
      contactSelectionOrg,
    }) {
      if (contactSelectionUser && contactSelectionUser.length > 0) {
        contactSelectionUser.map(item => {
          let not = this.organizeForm.notifierList.findIndex(val => {
            return val.id === item.userName;
          });
          if (not > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.notifierList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (selectionUser && selectionUser.length > 0) {
        selectionUser.map(item => {
          let not = this.organizeForm.notifierList.findIndex(val => {
            return val.id === item.userName;
          });
          if (not > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.notifierList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      } else {
        usersChecked.map(item => {
          let not = this.organizeForm.notifierList.findIndex(val => {
            return val.id === item.id;
          });
          if (not > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.notifierList.push({
              bz: "user",
              id: item.id,
              name: item.name,
              orgName: item.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (contactSelectionOrg && contactSelectionOrg.length > 0) {
        contactSelectionOrg.map(item => {
          let notOrg = this.organizeForm.notifierList.findIndex(val => {
            return val.id === item.orgId;
          });
          if (notOrg > -1 && this.isIt) {
            this.$message({
              message: "it云只能选择一个组织",
              type: "warning",
            });
          } else if (notOrg > -1) {
            this.$message({
              message: "选择的组织已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.notifierList.push({
              bz: "org",
              id: item.orgId + "",
              orgName: item.fullOrgName,
            });
          }
        });
      }
      // orgsChecked.map(item => {
      //   this.organizeForm.notifierList.push({
      //     bz: "org",
      //     id: item.id,
      //     orgName: item.name,
      //   });
      // });
      if (orgsChecked && orgsChecked.length > 0) {
        orgsChecked.map(item => {
          let notOrg = this.organizeForm.notifierList.findIndex(val => {
            return val.id === item.id;
          });
          if (notOrg > -1 && this.isIt) {
            this.$message({
              message: "it云只能选择一个组织",
              type: "warning",
            });
          } else if (notOrg > -1) {
            this.$message({
              message: "选择的组织已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.notifierList.push({
              bz: "org",
              id: item.id,
              orgName: item.name,
            });
          }
        });
      }
      this.organizeForm.notifierListCopy = JSON.parse(
        JSON.stringify(this.organizeForm.notifierList)
      );
    },
    //接收人确定
    recipientDetermine({
      usersChecked,
      orgsChecked,
      selectionUser,
      contactSelectionUser,
    }) {
      if (contactSelectionUser && contactSelectionUser.length > 0) {
        contactSelectionUser.map(item => {
          let rec = this.organizeForm.recipientList.findIndex(val => {
            return val.id === item.userName;
          });
          if (rec > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.recipientList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      if (selectionUser && selectionUser.length > 0) {
        selectionUser.map(item => {
          let rec = this.organizeForm.recipientList.findIndex(val => {
            return val.id === item.userName;
          });
          if (rec > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.recipientList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      } else if (usersChecked && usersChecked.length > 0) {
        usersChecked.map(item => {
          let rec = this.organizeForm.recipientList.findIndex(val => {
            return val.id === item.id;
          });
          if (rec > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.recipientList.push({
              bz: "user",
              id: item.id,
              name: item.name,
              orgName: item.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      this.organizeForm.recipientListCopy = JSON.parse(
        JSON.stringify(this.organizeForm.recipientList)
      );
    },
    //光缆名称选择
    opticalCableSelect() {
      let jsonStr =
        '{"userId":"302785","username":"yangxm97","callSysId":"0001","callSysName":"电子运维"}';
      this.$nextTick(() => {
        document.querySelector("#fiberOpticCable").value = jsonStr;
        document.querySelector("#sub__fiberOpticCable").submit();
      });
    },
    //工单主题
    getInterfaceZxData() {
      let jsonStr =
        '{"userId":"302785","username":"yangxm97","callSysId":"0001","callSysName":"电子运维"}';
      this.$nextTick(() => {
        document.querySelector("#requestJson").value = jsonStr;
        document.querySelector("#deploy_simulation_page").submit();
      });
    },
    //附件选择
    attachmentBrowse() {
      this.attachmentDialogVisible = true;
    },
    changeFileData(data) {
      this.sheetForm.attachmentFile = data.fileName;
      this.importForm.attachmentFileList = data.attachmentFileList;
      this.attachmentDialogVisible = false;
    },
    closeAttachmentDialog() {
      this.attachmentDialogVisible = false;
    },
    //影响业务列表选择
    incidenceBrowse() {
      this.incidenceDialogVisible = true;
    },
    changeIncidenceFileData(data) {
      this.sheetForm.incidenceFile = data.fileName;
      this.importForm.incidenceFileList = data.attachmentFileList;
      this.incidenceDialogVisible = false;
    },
    closeIncidenceDialog() {
      this.incidenceDialogVisible = false;
    },
    stitchingAlgorithm(orgName, userName) {
      if (null == orgName) orgName = "";
      if (null == userName) userName = "";
      if (orgName.length !== 0 && userName.length !== 0) {
        return orgName + "," + userName;
      } else {
        if (orgName.length !== 0) {
          return orgName;
        } else if (userName.length !== 0) {
          return userName;
        } else {
          return "";
        }
      }
    },
    filterLordSentOrgNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    closeAndTurnAroundMyDraft() {
      let self = this;
      self.$store.dispatch("tagsView/delView", self.route).then(() => {
        self.$destroy();
      });
      self.$router.push({
        name: "backbone_myDraft",
      });
    },
    closeAndTurnAroundTo() {
      let self = this;
      self.$store.dispatch("tagsView/delView", self.route).then(() => {
        self.$destroy();
      });
      self.$router.push({
        name: "backbone_toDoRepairOrder",
        query: {
          globalUniqueID: sessionStorage.getItem("globalUniqueID"),
          token: this.token,
        },
      });
    },
    //多选移除
    toggleSelection(val) {
      if (this.multipleSelection == 0) {
        this.$message({
          type: "warning",
          message: "还没有选择移除的选项，请先选择要移除的选项！",
        });
        return;
      } else {
        this.multipleSelection.forEach(row => {
          this.handleClose(val, row);
        });
      }
      this.multipleSelection;
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    //移除对象
    handleClose(val, tag) {
      if (val == "builderZs") {
        this.organizeForm.builderZsList.splice(
          this.arrayIndex(this.organizeForm.builderZsList, tag),
          1
        );
        this.organizeForm.builderZsListCopy.splice(
          this.arrayIndex(this.organizeForm.builderZsListCopy, tag),
          1
        );
      }
      if (val == "builderCs") {
        this.organizeForm.builderCsList.splice(
          this.arrayIndex(this.organizeForm.builderCsList, tag),
          1
        );
        this.organizeForm.builderCsListCopy.splice(
          this.arrayIndex(this.organizeForm.builderCsListCopy, tag),
          1
        );
      }
      if (val == "notifier") {
        this.organizeForm.notifierList.splice(
          this.arrayIndex(this.organizeForm.notifierList, tag),
          1
        );
        this.organizeForm.notifierListCopy.splice(
          this.arrayIndex(this.organizeForm.notifierListCopy, tag),
          1
        );
      }
      if (val == "recipient") {
        this.organizeForm.recipientList.splice(
          this.arrayIndex(this.organizeForm.recipientList, tag),
          1
        );
        this.organizeForm.recipientListCopy.splice(
          this.arrayIndex(this.organizeForm.recipientListCopy, tag),
          1
        );
      }
    },
    //找对应对象的索引值
    arrayIndex(val1, val2) {
      for (var i = 0, len = val1.length; i < len; i++) {
        var tag = val1[i];
        if (tag.id == val2.id) {
          return i;
        }
      }
    },
    //搜索
    search(val) {
      if (val == "builderZs" && this.organizeForm.builderZsList != null) {
        this.organizeForm.builderZsListCopy = [];
        this.organizeForm.builderZsList.forEach(row => {
          if (
            row.name == this.organizeForm.builderZsName ||
            row.orgName == this.organizeForm.builderZsName
          ) {
            this.organizeForm.builderZsListCopy.push(row);
          }
        });
      }
      if (val == "builderCs" && this.organizeForm.builderCsList != null) {
        this.organizeForm.builderCsListCopy = [];
        this.organizeForm.builderCsList.forEach(row => {
          if (
            row.name == this.organizeForm.builderCsName ||
            row.orgName == this.organizeForm.builderCsName
          ) {
            this.organizeForm.builderCsListCopy.push(row);
          }
        });
      }
      if (val == "notifier" && this.organizeForm.notifierList != null) {
        this.organizeForm.notifierListCopy = [];
        this.organizeForm.notifierList.forEach(row => {
          if (
            row.name == this.organizeForm.notifierName ||
            row.orgName == this.organizeForm.notifierName
          ) {
            this.organizeForm.notifierListCopy.push(row);
          }
        });
      }
      if (val == "recipient" && this.organizeForm.recipientList != null) {
        this.organizeForm.recipientListCopy = [];
        this.organizeForm.recipientList.forEach(row => {
          if (
            row.name == this.organizeForm.recipientName ||
            row.orgName == this.organizeForm.recipientName
          ) {
            this.organizeForm.recipientListCopy.push(row);
          }
        });
      }
    },
    //清除搜索项
    clear(val) {
      if (val == "builderZs") {
        this.organizeForm.builderZsName = "";
        this.organizeForm.builderZsListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.builderZsList)
        );
      }
      if (val == "builderCs") {
        this.organizeForm.builderCsName = "";
        this.organizeForm.builderCsListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.builderCsList)
        );
      }
      if (val == "notifier") {
        this.organizeForm.notifierName = "";
        this.organizeForm.notifierListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.notifierList)
        );
      }
      if (val == "recipient") {
        this.organizeForm.recipientName = "";
        this.organizeForm.recipientListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.recipientList)
        );
      }
    },
    entering() {
      if (
        this.organizeForm.builderZsList &&
        this.organizeForm.builderZsList.length > 0
      ) {
        let userList = this.organizeForm.builderZsList.filter((item, index) => {
          return item.bz == "user";
        });
        let orgList = this.organizeForm.builderZsList.filter((item, index) => {
          return item.bz == "org";
        });
        if (userList && userList.length > 0) {
          let usersCheckedName = userList.map(item => {
            return item.name;
          });
          this.sheetForm.builderZsUserName = usersCheckedName.join(",");
          let usersCheckedId = userList.map(item => {
            return item.id;
          });
          this.sheetForm.builderZsUserId = usersCheckedId.join(",");
          const userDetailName = userList.map(item => {
            return item.name + "-" + item.orgName + "-" + item.mobilePhone;
          });
          this.sheetForm.agentManDetail = userDetailName.join(",");
        }
        if (orgList && orgList.length > 0) {
          let orgsCheckedName = orgList.map(item => {
            return item.orgName;
          });
          this.sheetForm.builderZsOrgName = orgsCheckedName.join(",");
          let orgsCheckedId = orgList.map(item => {
            return item.id;
          });
          this.sheetForm.builderZsOrgId = orgsCheckedId.join(",");
        }
      }
      if (
        this.organizeForm.builderCsList &&
        this.organizeForm.builderCsList.length > 0
      ) {
        let userList = this.organizeForm.builderCsList.filter((item, index) => {
          return item.bz == "user";
        });
        let orgList = this.organizeForm.builderCsList.filter((item, index) => {
          return item.bz == "org";
        });
        if (userList && userList.length > 0) {
          let usersCheckedName = userList.map(item => {
            return item.name;
          });
          this.sheetForm.builderCsUserName = usersCheckedName.join(",");
          let usersCheckedId = userList.map(item => {
            return item.id;
          });
          this.sheetForm.builderCsUserId = usersCheckedId.join(",");
          const userDetailName = userList.map(item => {
            return item.name + "-" + item.orgName + "-" + item.mobilePhone;
          });
          this.sheetForm.copyManDetail = userDetailName.join(",");
        }
        if (orgList && orgList.length > 0) {
          let orgsCheckedName = orgList.map(item => {
            return item.orgName;
          });
          this.sheetForm.builderCsOrgName = orgsCheckedName.join(",");
          let orgsCheckedId = orgList.map(item => {
            return item.id;
          });
          this.sheetForm.builderCsOrgId = orgsCheckedId.join(",");
        }
      }
      if (
        this.organizeForm.notifierList &&
        this.organizeForm.notifierList.length > 0
      ) {
        let userList = this.organizeForm.notifierList.filter((item, index) => {
          return item.bz == "user";
        });
        let orgList = this.organizeForm.notifierList.filter((item, index) => {
          return item.bz == "org";
        });
        if (userList && userList.length > 0) {
          let usersCheckedName = userList.map(item => {
            return item.name;
          });
          this.sheetForm.notifierUserName = usersCheckedName.join(",");
          let usersCheckedId = userList.map(item => {
            return item.id;
          });
          this.sheetForm.notifierUserId = usersCheckedId.join(",");
          const userDetailName = userList.map(item => {
            return item.name + "-" + item.orgName + "-" + item.mobilePhone;
          });
          this.sheetForm.notifierUserDetail = userDetailName.join(",");
        }
        if (orgList && orgList.length > 0) {
          let orgsCheckedName = orgList.map(item => {
            return item.orgName;
          });
          this.sheetForm.notifierOrgName = orgsCheckedName.join(",");
          let orgsCheckedId = orgList.map(item => {
            return item.id;
          });
          this.sheetForm.notifierUserId = orgsCheckedId.join(",");
        }
      }

      if (
        this.organizeForm.recipientList &&
        this.organizeForm.recipientList.length > 0
      ) {
        let userList = this.organizeForm.recipientList.filter((item, index) => {
          return item.bz == "user";
        });
        if (userList && userList.length > 0) {
          let usersCheckedName = userList.map(item => {
            return item.name;
          });
          this.sheetForm.recipientUserName = usersCheckedName.join(",");
          let usersCheckedId = userList.map(item => {
            return item.id;
          });
          this.sheetForm.recipientUserId = usersCheckedId.join(",");
          const userDetailName = userList.map(item => {
            return item.name + "-" + item.orgName + "-" + item.mobilePhone;
          });
          this.sheetForm.recipientDetailUserName = userDetailName.join(",");
        }
      }
    },
    // 建单模板
    onSelectTemp() {
      this.tempVisible = true;
    },
    onTempSeleted(temp) {
      this.sheetForm.title = temp.sheetTitle;
      this.sheetForm.phenomenon = temp.faultPhenomenon;
    },

    /**
     * 关于新增的 “涉及电路”、“是否重保”、“重保任务” 字段 的所有相关方法
     */
    updateInvolvedCircuitInputWidth() {
      this.$nextTick(() => {
        const inputElement = this.$refs.involvedCircuitInput.$el;
        this.sheetForm.involvedCircuitInputWidth = inputElement.offsetWidth;
        console.log("Input width:", inputElement.offsetWidth);
      });
    },
    openInvolvedCircuitDialog(e) {
      this.sheetForm.circuitNumber = "";
      this.circuitTable.pageNum = 1;
      this.circuitTable.pageSize = 10;
      this.circuitTable.total = 0;
      this.sheetForm.involvedCircuitTableData = [];
      this.searchCircuitNumber(1);
      this.sheetForm.involvedCircuitDialogTableVisible = true;
      if (e == "edit") {
        this.sheetForm.isSelectedInvolvedCircuit = true;
      } else {
        this.sheetForm.isSelectedInvolvedCircuit = false;
      }
    },
    involvedCircuitHandleSelectionChange(e) {
      console.log("handleChange ", e);
      this.sheetForm.involvedCircuitArray = e;
    },
    confirmInvolvedCircuit() {
      const resultArr = this.sheetForm.involvedCircuitArray.map(
        item => item.circuitId
      );
      console.log(resultArr);
      let result = false;
      if (this.sheetForm.involvedCircuit.length > 0) {
        this.sheetForm.involvedCircuit.forEach(item => {
          if (resultArr.indexOf(item.circuitId) != "-1") {
            result = true;
            this.$message({
              message: "选择的电路编号已存在",
              type: "warning",
            });
            return;
          }
        });
      }
      if (result) {
        return;
      }
      if (this.sheetForm.involvedCircuit.length > 0) {
        this.sheetForm.involvedCircuit = [
          ...this.sheetForm.involvedCircuit,
          ...this.sheetForm.involvedCircuitArray,
        ];
      } else {
        this.sheetForm.involvedCircuit = this.sheetForm.involvedCircuitArray;
      }

      this.sheetForm.involvedCircuitDialogTableVisible = false;
      // this.searchCircuitNumber();
    },
    involvedCircuitEdit() {
      this.openInvolvedCircuitDialog("edit");
    },
    involvedCircuitDeleteRow(index, row) {
      console.log(index, row);
      this.sheetForm.involvedCircuitTableData =
        this.sheetForm.involvedCircuitTableData.filter(item => {
          return item.circuitId != row.circuitId;
        });
      // this.sheetForm.involvedCircuit = this.sheetForm.involvedCircuitTableData;
      this.sheetForm.involvedCircuit = this.sheetForm.involvedCircuit.filter(
        item => {
          return item.circuitId != row.circuitId;
        }
      );
    },
    searchCircuitNumberByPageSize(pageSize) {
      this.circuitTable.pageSize = pageSize;
      this.searchCircuitNumber(1);
    },
    searchCircuitNumberByPageNum(pageNum) {
      this.searchCircuitNumber(pageNum);
    },
    searchCircuitNumber(pageNum) {
      this.circuitTable.pageNum = pageNum;
      console.log(this.sheetForm.circuitNumber);
      this.sheetForm.circuitLoading = true;
      try {
        // 30N91
        searchCircuitNo({
          circuitNo: this.sheetForm.circuitNumber || "",
          province: "集团",
          pageNum: pageNum,
          pageSize: this.circuitTable.pageSize
        })
          .then(res => {
            console.log(res);
            if (res["status"] == 0) {
              this.sheetForm.involvedCircuitTableData = res.data.list;
              this.circuitTable.total = res.data.total;

              this.updateInvolvedCircuitInputWidth();
              console.log(this.sheetForm.involvedCircuitTableData);
            } else if (res["status"] == 400) {
              this.sheetForm.involvedCircuitTableData = [];
            }
            this.sheetForm.circuitLoading = false;
          })
          .catch(err => {
            this.sheetForm.involvedCircuitTableData = [];
            this.sheetForm.circuitLoading = false;
          });
      } catch (error) {
        this.sheetForm.involvedCircuitTableData = [];
        this.sheetForm.circuitLoading = false;
      }
    },
    handlePageChange(newPage) {
      this.currentPage = newPage; // 更新当前页码
    },
    handleItemsPerPageChange(value) {
      this.currentPage = 1; // 重置到第一页
      this.itemsPerPage = value; // 更新每页显示的数据条数
    },
    inputInvolvedCircuit() {
      console.log("aaaaaaaaaaaaaaaaa");
    },
    // this.sheetForm.newInvolvedCircuit
    formatNewInvolvedCircuit() {
      if (this.sheetForm.newInvolvedCircuit.length > 0) {
        const tmpArr = this.sheetForm.newInvolvedCircuit.split("、");
        const tmpArr02 = tmpArr.map(item => ({
          circuitId: item,
        }));
        this.sheetForm.newInvolvedCircuit = tmpArr02;
        console.log(this.sheetForm.newInvolvedCircuit);
      }
    },

    // 拟稿权限
    getOrderNgAuth() {
      if (this.mainProfessionalType == "骨干网") {
        orderNgAuthApi().then(res => {
          const hasAuth = JSON.parse(res.data || []).includes(
            this.userInfo.userName
          );
          if (!hasAuth) {
            this.$alert("当前用户没有建单权限", "提示", {
              confirmButtonText: "确定",
              type: "warning",
              callback: action => {
                if (this.$store.getters.visitedViews.length > 1) {
                  let self = this;
                  self.$store
                    .dispatch("tagsView/delView", self.route)
                    .then(() => {
                      self.$destroy();
                    });
                  this.$router.go(-1);
                } else {
                  this.closeAndTurnAroundTo();
                }
              },
            });
          }
        });
      }
    },


    // 工单主题弹窗
    // 工单主题输入修改返回修改
    changThemeInputData(val, type) {
      this.sheetForm.title = val;
    },
    // 工单主题选择后传回
    eventThemeSelectData(val, type) {
      if (this.sheetForm.title && this.sheetForm.title.length > 0) {
        this.sheetForm.title += '、';
      }
      val.forEach((item, i) => {
        if (i == val.length - 1) {
          this.sheetForm.title = ((this.sheetForm.title && this.sheetForm.title.length > 0) ? this.sheetForm.title : "") + item["name"];
        } else {
          this.sheetForm.title =
            ((this.sheetForm.title && this.sheetForm.title.length > 0) ? this.sheetForm.title : "") + item["name"] + "、";
        }
      });
    },


  },
};
</script>

<style lang="scss" scoped>
.draft-eidt-page {
  .head-title {
    font-size: 24px;
    line-height: 28px;
  }

  .head-handle-wrap {
    text-align: right;
  }

  .card-title {
    font-size: 15px;
    letter-spacing: 0;
    padding-left: 10px;
  }

  .select_style {
    background: #b50b14;
    border-radius: 2px;
    border-color: #b50b14;
  }
}

::v-deep .involvedCircuitInput .el-input__inner {
  // color: transparent !important;
  padding-left: 1rem;
}

::v-deep .involvedCircuitDialogTableVisible {
  width: 630px;

  .el-form .el-input__inner {
    width: 13rem;
  }

  .el-pagination {
    margin-top: 5px;
  }

  .el-dialog__body {
    padding-top: 0;
    padding-bottom: 0;

    .el-table {
      background: #fff;
      border: 1px solid #e9e9e9;
      box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.12), 0 0 6px 0 rgba(0, 0, 0, 0.04);
    }
  }
}

::v-deep .el-table__body-wrapper {
  height: 100%;
  overflow-y: scroll;
}

::v-deep .pagePagination {
  .el-input__inner {
    width: 6rem;
    padding-right: 1rem;
  }
}
</style>
