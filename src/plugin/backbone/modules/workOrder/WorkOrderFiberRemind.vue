<template>
  <div style="padding: 0">
    <head-content-layout class="page-wrap">
      <template #header>
        <el-row style="display: flex">
          <el-col :xs="24" :sm="12" :md="14" :lg="18" style="display: flex">
            <div>
              <div style="display: inline-block; margin-left: 5px">专业：</div>
              <dict-select
                :value.sync="CprofessionalType"
                :dictId="10002"
                placeholder="请选择专业"
                style="width: 270px"
              />
            </div>
            <div>
              <div style="display: inline-block; margin-left: 20px">
                客户名称：
              </div>
              <el-input v-model="customerName" style="width: 270px"></el-input>
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :md="10" :lg="6" style="text-align: right">
            <div>
              <el-button type="primary" @click="seniorQuery">查询</el-button>
              <el-button @click="onResetForm">重置</el-button>
            </div>
          </el-col>
        </el-row>
        <div class="order__menuList">
          <el-button type="primary" @click="createSeup">+ 新建</el-button>
          <el-button @click="batchDelete">批量删除</el-button>
          <el-button @click="handlebatch('启用')">启用</el-button>
          <el-button @click="handlebatch('禁用')">禁用</el-button>
        </div>
      </template>

      <template #table>
        <el-table
          ref="table"
          :data="tableData"
          :border="false"
          stripe
          @selection-change="onSelectionChange"
          :header-cell-style="{ textAlign: 'center' }"
          :cell-style="{ textAlign: 'center' }"
          height="100%"
          v-loading="tableLoading"
        >
          <el-table-column
            type="selection"
            min-width="50"
            :index="indexMethod"
          />
          <el-table-column prop="createUserName" label="规则制定人" width="100">
          </el-table-column>
          <el-table-column
            prop="createUserDept"
            label="制定人所属组织"
            width="150"
          >
          </el-table-column>
          <el-table-column
            prop="provinceName"
            label="省份"
            width="100"
          ></el-table-column>
          <el-table-column
            prop="refreshTime"
            label="规则制定时间"
            width="180"
          ></el-table-column>
          <el-table-column
            prop="professionalType"
            label="专业"
            width="150"
          ></el-table-column>
          <el-table-column
            prop="ruleComment"
            label="描述"
            width="200"
          ></el-table-column>
          <el-table-column prop="createType" label="工单来源" width="200">
            <template slot-scope="scope">
              <div class="createType">
                <span v-for="(item, key) of scope.row.createType" :key="key">{{
                  handlecreateType(item)
                }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="ruleStatus"
            label="规则状态"
            width="100"
            align="center"
          >
            <template slot-scope="scope">
              <!-- <el-tooltip
                class="item"
                effect="dark"
                :content="scope.row.ruleStatus == 1 ? '启用' : '禁用'"
                placement="top"
              >
                <span v-if="scope.row.ruleStatus == 0"
                  ><i
                    style="font-size: x-large; color: dimgray"
                    class="el-icon-error"
                  ></i
                ></span>
                <span v-else-if="scope.row.ruleStatus == 1"
                  ><i
                    style="font-size: x-large; color: green"
                    class="el-icon-success"
                  ></i></span
              ></el-tooltip> -->
              <el-tag
                v-if="scope.row.ruleStatus == 1"
                type="success"
                effect="dark"
              >
                启用
              </el-tag>
              <el-tag
                v-if="scope.row.ruleStatus == 0"
                type="info"
                effect="dark"
              >
                禁用
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="customerName"
            label="客户名称"
            width="150"
          ></el-table-column>
          <el-table-column
            prop="busTransSystem"
            label="客户业务所属传输系统"
            width="400"
          ></el-table-column>
          <el-table-column prop="sendObject" label="通知方式" min-width="360">
            <template slot-scope="scope">
              <div class="createType">
                <span v-for="(item, key) of scope.row.sendObject" :key="key">{{
                  handleSendObject(item)
                }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100" fixed="right">
            <template slot-scope="scope">
              <el-button
                @click="clickEditor(scope.row)"
                type="text"
                size="small"
                :disabled="
                  scope.row.createUserId == createUserId ? false : true
                "
                >编辑</el-button
              >
              <el-button
                type="text"
                size="small"
                slot="reference"
                style="margin-left: 10px"
                @click="openDialog(scope.row)"
                :disabled="
                  scope.row.createUserId == createUserId ? false : true
                "
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <el-dialog
          width="410px"
          title="删除"
          :visible.sync="deleteDialogVisible"
          style="top: 36%; position: absolute; margin: 0"
        >
          <span>你确定要删除吗?</span>
          <span slot="footer">
            <el-button @click="deleteDialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="clickDelete">确 定</el-button>
          </span>
        </el-dialog>
      </template>
      <template #pagination>
        <pagination
          ref="pagination"
          :total="form.total"
          :page.sync="form.pageNum"
          :limit.sync="form.pageSize"
          @change="seniorQuery"
        />
      </template>
    </head-content-layout>
    <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      width="770px"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="180px">
        <el-form-item label="规则制定人:" prop="createUserName">
          <el-input
            disabled
            v-model="form.createUserName"
            maxlength="25"
          ></el-input>
        </el-form-item>
        <el-form-item label="制订人所属组织:" prop="createUserDept">
          <el-input
            disabled
            v-model="form.createUserDept"
            maxlength="25"
          ></el-input>
        </el-form-item>

        <el-form-item label="省份:" prop="provinceName">
          <dict-select
            :notSelect="title == '编辑' ? true : false"
            :value.sync="form.provinceName"
            :dictId="10064"
            placeholder="请选择省份"
            style="width: 240px"
          />
        </el-form-item>

        <el-form-item label="专业:" prop="professionalType">
          <dict-select
            :notSelect="true"
            :value.sync="form.professionalType"
            :dictId="10002"
            placeholder="请选择专业"
            style="width: 240px"
          />
        </el-form-item>

        <el-form-item label="描述:" prop="ruleComment">
          <el-input v-model="form.ruleComment" maxlength="25"></el-input>
        </el-form-item>

        <el-form-item label="工单来源:" prop="createType">
          <el-checkbox-group v-model="form.createType">
            <div style="display: flex">
              <el-checkbox label="电子运维新建" name="type"></el-checkbox>
              <el-checkbox label="智能监控自动" name="type"></el-checkbox>
            </div>
            <div style="display: flex">
              <el-checkbox label="智能监控手动" name="type"></el-checkbox>
              <el-checkbox label="电信共建共享" name="type"></el-checkbox>
            </div>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="客户名称:" prop="customerName">
          <el-input v-model="form.customerName"></el-input>
        </el-form-item>
        <el-form-item label="客户业务所属传输系统:" prop="busTransSystem">
          <el-input
            v-model="form.busTransSystem"
            type="textarea"
            rows="2"
            placeholder="请输入光缆系统名称，多个光缆系统以英文逗号分隔"
          ></el-input>
        </el-form-item>
        <el-form-item label="通知方式:" prop="sendObject">
          <div>
            <el-button type="text" size="small" @click="addRows()"
              >新增</el-button
            >
            <el-table
              :data="form.sendObject"
              style="width: 650px"
              max-height="196px"
            >
              <el-table-column
                label="姓名"
                width="80"
                prop="name"
                header-align="center"
              >
                <template slot-scope="scope">
                  <el-input v-model="scope.row.name"></el-input>
                </template>
              </el-table-column>
              <el-table-column
                width="40"
                prop="isSendSms"
                :key="smsCheckboxCount"
              >
                <template slot="header">
                  <el-checkbox
                    :indeterminate="isSmsIndeterminate"
                    v-model="checkAllSms"
                    @change="changeSmsCheckAll(checkAllSms)"
                  ></el-checkbox>
                </template>
                <template slot-scope="scope">
                  <el-checkbox
                    v-model="scope.row.isSendSms"
                    @change="changeSmsCheckbox(scope.row.isSendSms)"
                  ></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column
                label="手机号"
                width="150"
                prop="mobile"
                header-align="center"
              >
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.mobile"
                    @change="changeMobile(scope.row.mobile)"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column
                width="40"
                prop="isSendEmail"
                :key="emailCheckboxCount"
              >
                <template slot="header">
                  <el-checkbox
                    :indeterminate="isEmailIndeterminate"
                    v-model="checkAllEmail"
                    @change="changeEmailCheckAll(checkAllEmail)"
                  ></el-checkbox>
                </template>
                <template slot-scope="scope">
                  <el-checkbox
                    v-model="scope.row.isSendEmail"
                    @change="changeEmailCheckbox(scope.row.isSendEmail)"
                  ></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column
                label="邮箱"
                width="150"
                prop="email"
                header-align="center"
              >
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.email"
                    @change="changeEmail(scope.row.email)"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column width="40">
                <template slot-scope="scope">
                  <el-button @click="deleteRows(scope)" type="text" size="small"
                    >删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="_save"> 确 定 </el-button>
        <el-button @click="_cancel">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import DictSelect from "./components/DictSelect.vue";
import HeadContentLayout from "./components/HeadContentLayout.vue";
import Pagination from "./components/Pagination.vue";
import {
  apiQueryRule,
  apiAddRule,
  apiDeleteRule,
  apiQueryRuleByRuleId,
  apiupdateRule,
  apiupdateRuleStatus,
} from "./api/WorkOrderFiberRemind";
export default {
  name: "WorkOrderToRemind",
  components: {
    DictSelect,
    HeadContentLayout,
    Pagination,
  },
  data() {
    return {
      CprofessionalType: "", //专业
      customerName: "", //客户名称
      tableLoading: false,
      tableData: [],
      title: "新增",
      dialogVisible: false,
      form: {
        createUserName: "", //规则指定人
        createUserDept: "",
        provinceName: "所有",
        professionalType: "3",
        professionalTypeCode: 3,
        ruleComment: "",
        createType: [
          "电子运维新建",
          "智能监控自动",
          "智能监控手动",
          "电信共建共享",
        ],
        ruleStatus: 0,
        ruleId: "",
        customerName: "",
        busTransSystem: "",
        pageNum: 1,
        pageSize: 10,
        total: 0,
        sendObject: [
          {
            name: "",
            mobile: "",
            isSendSms: false,
            email: "",
            isSendEmail: false,
          },
        ],
      },
      typeList: [
        "智能监控自动",
        "智能监控手动",
        "电子运维新建",
        "电信共建共享",
      ],
      multipleSelection: [],

      createUserId: JSON.parse(sessionStorage.userInfo).userName,

      rules: {
        provinceName: [
          { required: true, message: "请选择省份", trigger: "change" },
        ],
        professionalType: [
          { required: true, message: "请选择专业", trigger: "change" },
        ],
        createType: [
          { required: true, message: "请选择工单来源", trigger: "change" },
        ],
        busTransSystem: [
          {
            required: true,
            message: "请填写业务所属传输系统",
            trigger: "blur",
          },
        ],
        customerName: [
          {
            required: true,
            message: "请填写客户名称",
            trigger: "blur",
          },
        ],
        sendObject: [
          {
            required: true,
          },
        ],
      },
      options: [{ value: "中国联通总部", label: "中国联通总部" }],
      isSmsIndeterminate: false,
      isEmailIndeterminate: false,
      checkAllSms: false,
      checkAllEmail: false,
      smsCheckboxCount: 0,
      emailCheckboxCount: 0,
      userData: null,
      deleteDialogVisible: false,
    };
  },
  mounted() {
    this.form.createUserName = JSON.parse(sessionStorage.userInfo).realName;
    this.userData = JSON.parse(JSON.parse(sessionStorage.userInfo).attr2);
    this.form.createUserDept = this.userData.orgInfo.fullOrgName.split("-")[0];
    if (this.form.createUserDept == null || this.form.createUserDept == "") {
      this.form.createUserDept = "中国联通总部";
    }
    this.getQueryRule();
  },

  methods: {
    changeEmail(val) {
      if (val != null && val != "") {
        var reg = /^[A-Za-z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
        if (!reg.test(val)) {
          this.$message({
            message: "您输入的邮箱格式有误，请检查您的邮箱格式",
            type: "warning",
          });
        }
      }
    },
    changeMobile(val) {
      if (val != null && val != "") {
        var reg = /^1[3456789]{1}\d{9}$/;
        if (!reg.test(val)) {
          this.$message({
            message: "您输入的手机号格式有误，请检查您的手机号格式",
            type: "warning",
          });
        }
      }
    },
    changeSmsCheckbox(val) {
      if (val) {
        this.smsCheckboxCount += 1;
        if (this.smsCheckboxCount > 0) {
          this.isSmsIndeterminate = true;
          if (this.smsCheckboxCount == this.form.sendObject.length) {
            this.checkAllSms = true;
            this.isSmsIndeterminate = false;
          }
        }
      } else {
        this.smsCheckboxCount += -1;
        if (this.smsCheckboxCount < this.form.sendObject.length) {
          if (this.smsCheckboxCount == 0) {
            this.isSmsIndeterminate = false;
            this.checkAllSms = false;
          } else {
            this.isSmsIndeterminate = true;
            this.checkAllSms = false;
          }
        }
      }
    },
    changeEmailCheckbox(val) {
      if (val) {
        this.emailCheckboxCount += 1;
        if (this.emailCheckboxCount > 0) {
          this.isEmailIndeterminate = true;
          if (this.emailCheckboxCount == this.form.sendObject.length) {
            this.checkAllEmail = true;
            this.isEmailIndeterminate = false;
          }
        }
      } else {
        this.emailCheckboxCount += -1;
        if (this.emailCheckboxCount < this.form.sendObject.length) {
          if (this.emailCheckboxCount == 0) {
            this.isEmailIndeterminate = false;
            this.checkAllEmail = false;
          } else {
            this.isEmailIndeterminate = true;
            this.checkAllEmail = false;
          }
        }
      }
    },
    changeSmsCheckAll(val) {
      if (val) {
        for (var i = 0; i < this.form.sendObject.length; i++) {
          this.form.sendObject[i].isSendSms = true;
        }
        this.checkAllSms = true;
        this.isSmsIndeterminate = false;
        this.smsCheckboxCount = this.form.sendObject.length;
      } else {
        for (var i = 0; i < this.form.sendObject.length; i++) {
          this.form.sendObject[i].isSendSms = false;
        }
        this.checkAllSms = false;
        this.isSmsIndeterminate = false;
        this.smsCheckboxCount = 0;
      }
    },
    changeEmailCheckAll(val) {
      if (val) {
        for (var i = 0; i < this.form.sendObject.length; i++) {
          this.form.sendObject[i].isSendEmail = true;
        }
        this.checkAllEmail = true;
        this.isEmailIndeterminate = false;
        this.emailCheckboxCount = this.form.sendObject.length;
      } else {
        for (var i = 0; i < this.form.sendObject.length; i++) {
          this.form.sendObject[i].isSendEmail = false;
        }
        this.checkAllEmail = false;
        this.isEmailIndeterminate = false;
        this.emailCheckboxCount = 0;
      }
    },
    //添加行
    addRows() {
      const newObj = {
        name: "",
        isSendSms: false,
        mobile: "",
        isSendEmail: false,
        email: "",
      };
      this.form.sendObject.splice(this.form.sendObject.length, 0, newObj);
    },
    //删除行
    deleteRows(scope) {
      this.form.sendObject.splice(scope.$index, 1);
    },
    //工单来源
    handlecreateType(item) {
      return this.typeList[item];
    },
    //通知方式
    handleSendObject(item) {
      if (item.isSendSms == true && item.isSendEmail == true) {
        return item.name + "，" + item.mobile + "，" + item.email;
      } else if (item.isSendSms == true && item.isSendEmail == false) {
        return item.name + "，" + item.mobile;
      } else if (item.isSendSms == false && item.isSendEmail == true) {
        return item.name + "，" + item.email;
      } else {
        return "";
      }
    },
    //查询数据
    getQueryRule(type = "") {
      this.tableLoading = true;
      let seniorParam = {
        professionalType: this.CprofessionalType,
        customerName: this.customerName,
      };
      let defaultParam = {
        professionalType: "",
        customerName: "",
      };
      let param = {
        pageIndex: this.form.pageNum,
        pageSize: this.form.pageSize,
        professionalType:
          type == "senior"
            ? seniorParam.professionalType
            : defaultParam.professionalType,
        customerName:
          type == "senior"
            ? seniorParam.customerName
            : defaultParam.customerName,
      };
      apiQueryRule(param)
        .then(res => {
          if (res.status == "0") {
            this.tableData = res?.data?.rows ?? [];
            let d = res?.data?.rows ?? [];
            if (d && d.length > 0) {
              d.map(el => {
                el.sendObject = JSON.parse(el.sendObject);
              });
            }
            this.form.total = res?.data?.totalElements ?? 0;
          }
          this.tableLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.tableLoading = false;
        });
    },
    //新建
    createSeup() {
      this.title = "新增";
      this.dialogVisible = true;
    },
    //批量删除
    batchDelete() {
      if (this.multipleSelection.length == 0) {
        this.$message({
          message: "请先选择规则",
          type: "warning",
        });
        return;
      }
      this.$confirm("你确定要删除吗?", "删除", {
        distinguishCancelAndClose: true,
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      }).then(() => {
        this.deleApiDeleteRule(this.multipleSelection);
      });
    },

    deleApiDeleteRule(params) {
      let param = {
        ruleId: params.join(),
      };
      apiDeleteRule(param).then(res => {
        if (res.status == "0") {
          this.getQueryRule();
          this.$message({
            message: "删除成功",
            type: "success",
          });
        }
      });
    },
    //启用 禁用
    handlebatch(type) {
      if (this.multipleSelection.length == 0) {
        this.$message({
          message: "请先选择规则",
          type: "warning",
        });
        return;
      }
      let param = {
        ruleIds: this.multipleSelection,
        ruleStatus: type == "启用" ? "1" : "0",
      };
      let formData = new FormData();
      formData.append("jsonParam", JSON.stringify(param));
      apiupdateRuleStatus(formData).then(res => {
        if (res.status == "0") {
          this.getQueryRule();
          this.$message({
            message: type == "启用" ? "已启动" : "已禁用",
            type: "success",
          });
        }
      });
    },
    //重置
    onResetForm() {
      this.CprofessionalType = ""; //专业
      this.customerName = "";
    },
    //查询
    seniorQuery() {
      this.getQueryRule("senior");
    },
    //多选
    onSelectionChange(val) {
      let s = [];
      if (val.length > 0) {
        val.forEach(item => {
          s.push(item.ruleId);
        });
      }
      this.multipleSelection = s;
    },
    //点击编辑
    clickEditor(scope) {
      this.title = "编辑";
      this.dialogVisible = true;
      let param = {
        ruleId: scope.ruleId,
      };
      apiQueryRuleByRuleId(param).then(res => {
        if (res.status == "0" && res.data.rows.length > 0) {
          const arrData = res.data.rows;
          let d = res?.data?.rows ?? [];
          if (d && d.length > 0) {
            d.map(el => {
              el.sendObject = JSON.parse(el.sendObject);
            });
          }
          const {
            createUserName,
            provinceName,
            professionalType,
            professionalTypeCode,
            ruleComment,
            createType,
            busTransSystem,
            customerName,
            sendObject,
            ruleId,
            ruleStatus,
            createUserDept,
          } = arrData[0];
          this.form.ruleId = ruleId;
          this.form.createUserDept = createUserDept;
          this.form.createUserName = createUserName; //规则制定人
          this.form.provinceName = provinceName; //省份
          this.form.professionalType = professionalType; //专业
          this.form.professionalTypeCode = professionalTypeCode;
          this.form.ruleComment = ruleComment; //描述
          this.form.ruleStatus = ruleStatus;
          const l = [];
          var createTypeArr = createType.split(",");
          createTypeArr.forEach(el => {
            if (el == "0") {
              l.push("智能监控自动");
            }
            if (el == "1") {
              l.push("智能监控手动");
            }
            if (el == "2") {
              l.push("电子运维新建");
            }
            if (el == "3") {
              l.push("电信共建共享");
            }
          });
          this.form.createType = l; //来源
          this.form.busTransSystem = busTransSystem;
          this.form.customerName = customerName;
          this.form.sendObject = sendObject;
          this.smsCheckboxCount = 0;
          this.emailCheckboxCount = 0;
          for (var i = 0; i < this.form.sendObject.length; i++) {
            if (this.form.sendObject[i].isSendSms) {
              this.smsCheckboxCount += 1;
            }
            if (this.form.sendObject[i].isSendEmail) {
              this.emailCheckboxCount += 1;
            }
          }
          if (this.smsCheckboxCount > 0) {
            if (this.smsCheckboxCount == this.form.sendObject.length) {
              this.isSmsIndeterminate = false;
              this.checkAllSms = true;
            } else {
              this.isSmsIndeterminate = true;
              this.checkAllSms = false;
            }
          }
          if (this.emailCheckboxCount > 0) {
            if (this.emailCheckboxCount == this.form.sendObject.length) {
              this.isEmailIndeterminate = false;
              this.checkAllEmail = true;
            } else {
              this.isEmailIndeterminate = true;
              this.checkAllEmail = false;
            }
          }
        }
      });
    },
    //dialog确定
    _save() {
      if (
        this.form.provinceName == "" ||
        this.form.professionalType == "" ||
        this.form.createType.length == 0 ||
        this.form.busTransSystem == "" ||
        this.form.customerName == "" ||
        this.form.sendObject.length == 0
      ) {
        this.$message({
          message: "您有未填写的必填项,新增失败",
          type: "warning",
        });
        return;
      }
      let l = [];
      console.log(this.form.createType);
      this.form.createType.forEach(el => {
        if (el == "智能监控自动") {
          l.push(0);
        }
        if (el == "智能监控手动") {
          l.push(1);
        }
        if (el == "电子运维新建") {
          l.push(2);
        }
        if (el == "电信共建共享") {
          l.push(3);
        }
      });
      l.sort(function (a, b) {
        return a - b;
      });
      var objArr = this.form.sendObject;
      var reg = /^[A-Za-z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
      var regPhone = /^1[3456789]{1}\d{9}$/;
      for (var i = 0; i < objArr.length; i++) {
        if (
          objArr[i].name == null ||
          objArr[i].name == "" ||
          ((objArr[i].mobile == null || objArr[i].mobile == "") &&
            (objArr[i].email == null || objArr[i].email == "")) ||
          (objArr[i].isSendSms == false && objArr[i].isSendEmail == false)
        ) {
          this.$message({
            message: "通知方式不能为空，请检查您输入的数据",
            type: "warning",
          });
          return;
        }
        if (
          (objArr[i].isSendSms == true &&
            (objArr[i].mobile == null || objArr[i].mobile == "")) ||
          (objArr[i].isSendEmail == true &&
            (objArr[i].email == null || objArr[i].email == ""))
        ) {
          this.$message({
            message: "选择手机号/邮箱之后，对应项不能为空，请检查您输入的数据",
            type: "warning",
          });
          return;
        }
        if (objArr[i].email != null && objArr[i].email != "") {
          if (!reg.test(objArr[i].email)) {
            this.$message({
              message: "您输入的邮箱格式有误，请检查您的邮箱格式",
              type: "warning",
            });
            return;
          }
        }
        if (objArr[i].mobile != null && objArr[i].mobile != "") {
          if (!regPhone.test(objArr[i].mobile)) {
            this.$message({
              message: "您输入的手机号格式有误，请检查您的手机号格式",
              type: "warning",
            });
            return;
          }
        }
      }
      let param = {
        createUserId: this.createUserId,
        createUserName: this.form.createUserName,
        createUserDept: this.form.createUserDept,
        provinceName: this.form.provinceName,
        professionalType:
          this.title == "新增"
            ? this.form.professionalType
            : this.form.professionalTypeCode,
        ruleComment: this.form.ruleComment,
        createType: l.toString(),
        ruleStatus: this.form.ruleStatus,
        ruleId: this.form.ruleId,
        sendObject: JSON.stringify(objArr),
        customerName: this.form.customerName,
        busTransSystem: this.form.busTransSystem,
      };
      if (this.title == "新增") {
        apiAddRule(param).then(res => {
          if (res.status == "0") {
            this.dialogVisible = false;
            this.getQueryRule();
            this.$message({
              message: "添加成功",
              type: "success",
            });
            this.$refs["form"].resetFields();
            this.form.ruleId = "";
            this.form.sendObject = [
              {
                name: "",
                mobile: "",
                isSendSms: false,
                email: "",
                isSendEmail: false,
              },
            ];
            this.isSmsIndeterminate = false;
            this.isEmailIndeterminate = false;
            this.checkAllSms = false;
            this.checkAllEmail = false;
            this.smsCheckboxCount = 0;
            this.emailCheckboxCount = 0;
          }else if(res.status == 200){
            this.$message({
              message: "同一客户不可存在多条规则！",
              type: "warning",
            });
          }
        });
      } else {
        apiupdateRule(param).then(res => {
          if (res.status == "0") {
            this.dialogVisible = false;
            this.getQueryRule();
            this.$message({
              message: "修改成功",
              type: "success",
            });
            this.$refs["form"].resetFields();
          }
        });
      }
    },
    //dialog取消
    _cancel() {
      this.dialogVisible = false;
      this.$refs["form"].resetFields();
      this.form.ruleId = "";
      this.form.sendObject = [
        {
          name: "",
          mobile: "",
          isSendSms: false,
          email: "",
          isSendEmail: false,
        },
      ];
      this.isSmsIndeterminate = false;
      this.isEmailIndeterminate = false;
      this.checkAllSms = false;
      this.checkAllEmail = false;
      this.smsCheckboxCount = 0;
      this.emailCheckboxCount = 0;
    },
    //点击叉号
    handleClose() {
      this.dialogVisible = false;
      this.$refs["form"].resetFields();
      this.form.ruleId = "";
      this.form.sendObject = [
        {
          name: "",
          mobile: "",
          isSendSms: false,
          email: "",
          isSendEmail: false,
        },
      ];
      this.isSmsIndeterminate = false;
      this.isEmailIndeterminate = false;
      this.checkAllSms = false;
      this.checkAllEmail = false;
      this.smsCheckboxCount = 0;
      this.emailCheckboxCount = 0;
    },
    openDialog(scope) {
      this.deleteRuleId = scope.ruleId;
      this.deleteDialogVisible = true;
    },
    //点击删除
    clickDelete() {
      let l = [];
      l[0] = this.deleteRuleId;
      this.deleApiDeleteRule(l);
      this.deleteDialogVisible = false;
    },
    indexMethod(index) {
      const currentPage = this.form.pageNum;
      const pageSize = this.form.pageSize;
      return (currentPage - 1) * pageSize + (index + 1);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../newIvr/assets/ivrCommon.scss";
.createType {
  display: flex;
  flex-direction: column;
}
.order__menuList {
  margin-top: 10px;
  padding-bottom: 10px;
}
::v-deep .el-dialog__body {
  padding: 0;
  padding-right: 50px;
  padding-left: 30px;
}
::v-deep .el-form-item__content {
  position: relative;
  .item__unit {
    position: absolute;
    display: block;
    top: 0;
    left: 106px;
    color: #dcdfe6;
  }
}
::v-deep .el-dialog__wrapper {
  .el-dialog {
    margin-top: 6vh !important;
    .el-dialog__body {
      .el-form {
        .el-form-item,
        .el-form-item--mini {
          margin: 0;
          margin-bottom: 14px;
        }
      }
    }
  }
}
</style>
