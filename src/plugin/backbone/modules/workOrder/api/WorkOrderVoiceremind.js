import { postJson, getJson } from "@/utils/axios";
const queryRuleUrl =
  "/backbone/workflow/faultNoticeIvrRule/queryNoticeIvrRuleList"; //查询
const addRuleUrl = "/backbone/workflow/faultNoticeIvrRule/addNoticeIvrRule"; //添加
const deleteRUle = "/backbone/workflow/faultNoticeIvrRule/deleteRules"; //删除
const queryRuleByRuleIdUrl =
  "/backbone/workflow/faultNoticeIvrRule/queryNoticeIvrRuleByRuleId"; //编辑
const updateRuleUrl =
  "/backbone/workflow/faultNoticeIvrRule/updateNoticeIvrRule"; //编辑保存 updateRuleStatus
const updateRuleStatusUrl =
  "/backbone/workflow/faultNoticeIvrRule/modifyNoticeIvrRuleStatusBatch"; //启用 ,禁用

const apiQueryRule = params => postJson(queryRuleUrl, params);
const apiAddRule = params => postJson(addRuleUrl, params);
const apiDeleteRule = params => postJson(deleteRUle, params);
const apiQueryRuleByRuleId = params => getJson(queryRuleByRuleIdUrl, params);
const apiupdateRule = params => postJson(updateRuleUrl, params);
const apiupdateRuleStatus = params => postJson(updateRuleStatusUrl, params);
export {
  apiQueryRule,
  apiAddRule,
  apiDeleteRule,
  apiQueryRuleByRuleId,
  apiupdateRule,
  apiupdateRuleStatus,
};
