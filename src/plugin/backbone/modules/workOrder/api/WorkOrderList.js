import { postJson, postJsonBlob } from "@/utils/axios";

//����
const shareListUrl = "commonprovince/processshare/getShareOrderInfoPage";
const listUrl = "backbone/workflow/queryAll";
const excelUrl = "backbone/workflow/queryAll/excel";
const countAllGrpUrl = "backbone/workflow/countAllGrp";
const apiShareQueryAll = params => postJson(shareListUrl, params, { timeout: 0 });
const apiQueryAll = params => postJson(listUrl, params, { timeout: 0 });
const apiQueryAllExcel = params =>
  postJsonBlob(excelUrl, params, { timeout: 0 });
const apicountAllGrp = params =>
  postJson(countAllGrpUrl, params, { timeout: 0 });

//ʡ�ֹ���
const provinceExcelUrl = "province/workflow/queryAll/excel";
const apiProvinceAllExcel = params =>
  postJsonBlob(provinceExcelUrl, params, { timeout: 0 });

const gtExcelUrl = "gt/orderExcel/queryAll/excel";
const apiGtAllExcel = params =>
  postJsonBlob(gtExcelUrl, params, { timeout: 0 });

const comExcelUrl = "commonprovince/orderExcel/queryAll/excel";
const apiComAllExcel = params =>
  postJsonBlob(comExcelUrl, params, { timeout: 0 });



export { apiShareQueryAll, apiQueryAll, apiQueryAllExcel, apicountAllGrp, apiProvinceAllExcel, apiGtAllExcel, apiComAllExcel};
