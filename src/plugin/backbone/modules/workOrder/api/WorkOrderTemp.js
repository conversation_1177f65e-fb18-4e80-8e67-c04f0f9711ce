import { postJson, get } from "@/utils/axios.js";

const basePath = "/backbone/sendTemplate/";

export const tempSaveApi = params =>
  postJson(`${basePath}saveTemplate`, params);

export const tempDelApi = linkIds =>
  postJson(`${basePath}delTemplate`, linkIds);

export const tempQueryApi = params =>
  postJson(`${basePath}queryTemplate`, params);

export const tempAuthApi = () => get(`${basePath}queryCreteProcessUsers`);
