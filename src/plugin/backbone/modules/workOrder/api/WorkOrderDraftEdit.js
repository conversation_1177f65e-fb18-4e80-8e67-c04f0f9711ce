import { get, postJson, postFormData } from "@/utils/axios";
const buildSingleUrl = "backbone/workflow/op/createWorkOrder";
const saveToDraftUrl = "backbone/workflow/op/createWorkOrder";
const queryWorkOrderUrl = "backbone/workflow/op/getOrderOne";
const updateDraftUrl = "backbone/workflow/op/saveOrderOne";
const provinceProcessFormUrl = "/province/dispatch/createWorkOrder";
const buildItCloudSingleUrl = "backbone/workflow/it/cloud/manCreate";
const itCloudDraft = "backbone/workflow/it/cloud/manCreate";
const buildCommCloudSingleUrl = "commCloud/workflow/manCreate";
const commCloudDraft = "commCloud/workflow/manCreate";
const commonDraftOrBuildUrl = "commonprovince/dispatch/createWorkOrder";
const buildCommonFlowSingleUrl = "commonFlow/workflow/op/createWorkOrder";
const commCommonFlowDraft = "commonFlow/workflow/op/createWorkOrder";
const queryCommonFlowWorkOrderUrl = "commonFlow/workflow/op/getOrderOne";
const buildWirelessUrl = "/wireless/dispatch/createWorkOrder";
const queryIpProfessionWorkOrder = "ipMajorFlow/workflow/op/getOrderOne";
const buildIpProfessionSingle = "ipMajorFlow/workflow/op/createWorkOrder";
const saveToIpProfessionDraft = "ipMajorFlow/workflow/op/createWorkOrder";

const apiWirelessBuild = params => postFormData(buildWirelessUrl, params);
const apiCommonDraftOrBuild = params =>
  postFormData(commonDraftOrBuildUrl, params);
const apiBuildSingle = params => postFormData(buildSingleUrl, params);
const apiSaveToDraft = params => postFormData(saveToDraftUrl, params);
const apiProvinceBuild = params => postFormData(provinceProcessFormUrl, params);
const apiQueryWorkOrder = params => postJson(queryWorkOrderUrl, params);
const apiUpdateToDraft = params => postJson(updateDraftUrl, params);
const apiBuildItCloudSingle = params =>
  postFormData(buildItCloudSingleUrl, params);
const apiSaveToItCloudDraft = params => postFormData(itCloudDraft, params);
const apiBuildCommCloudSingle = params =>
  postFormData(buildCommCloudSingleUrl, params);
const apiSaveToCommCloudDraft = params => postFormData(commCloudDraft, params);
const apiBuildCommonFlowSingle = params =>
  postFormData(buildCommonFlowSingleUrl, params);
const apiSaveToCommonFlowDraft = params =>
  postFormData(commCommonFlowDraft, params);
const apiQueryCommonFlowWorkOrder = params =>
  postJson(queryCommonFlowWorkOrderUrl, params);
const apiBuildIpProfessionSingle = params =>
  postFormData(buildIpProfessionSingle, params);
const apiSaveToIpProfessionDraft = params =>
  postFormData(saveToIpProfessionDraft, params);
const apiQueryIpProfessionWorkOrder = params =>
  postJson(queryIpProfessionWorkOrder, params);
export {
  apiWirelessBuild,
  apiBuildSingle,
  apiSaveToDraft,
  apiQueryWorkOrder,
  apiUpdateToDraft,
  apiBuildItCloudSingle,
  apiProvinceBuild,
  apiSaveToItCloudDraft,
  apiBuildCommCloudSingle,
  apiSaveToCommCloudDraft,
  apiCommonDraftOrBuild,
  apiBuildCommonFlowSingle,
  apiSaveToCommonFlowDraft,
  apiQueryCommonFlowWorkOrder,
  apiBuildIpProfessionSingle,
  apiSaveToIpProfessionDraft,
  apiQueryIpProfessionWorkOrder,
};

export const orderNgAuthApi = () =>
  get(`/backbone/sendTemplate/queryCreteProcessUsers`);
