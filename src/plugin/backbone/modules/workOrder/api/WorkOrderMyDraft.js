import { postJson } from "@/utils/axios";
const listUrl = "backbone/workflow/queryDraft";
const deleteDraftUrl = "backbone/workflow/op/delDraft";
const countDraftGrpUrl = "/backbone/workflow/countDraftGrp"; //查询专业数量
const provinceDraftsUrl = "/province/workflow/queryDraft"

const apiMyDraftList = params => postJson(listUrl, params);
const apiDeleteDraft = params => postJson(deleteDraftUrl, params);
const apicountDraftGrp = params => postJson(countDraftGrpUrl, params);
const apiProvinceDrafts = params => postJson(provinceDraftsUrl, params);
export { apiMyDraftList, apiDeleteDraft, apicountDraftGrp,  apiProvinceDrafts };
