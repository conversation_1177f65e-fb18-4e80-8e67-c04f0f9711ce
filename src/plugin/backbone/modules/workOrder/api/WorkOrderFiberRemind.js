import { postJson } from "@/utils/axios";
const queryRuleUrl = "/backbone/workflow/faultOptNoticeRule/getOptRules"; //查询
const addRuleUrl = "/backbone/workflow/faultOptNoticeRule/addOptRule"; //添加
const deleteRUle = "/backbone/workflow/faultOptNoticeRule/delOptRule"; //删除
const queryRuleByRuleIdUrl = "/backbone/workflow/faultOptNoticeRule/queryByRuleId"; //编辑
const updateRuleUrl = "/backbone/workflow/faultOptNoticeRule/updateOptRule"; //编辑保存 updateRuleStatus
const updateRuleStatusUrl = "/backbone/workflow/faultOptNoticeRule/updateRuleStatus"; //启用 ,禁用

const apiQueryRule = params => postJson(queryRuleUrl, params);
const apiAddRule = params => postJson(addRuleUrl, params);
const apiDeleteRule = params => postJson(deleteRUle, params);
const apiQueryRuleByRuleId = params => postJson(queryRuleByRuleIdUrl, params);
const apiupdateRule = params => postJson(updateRuleUrl, params);
const apiupdateRuleStatus = params => postJson(updateRuleStatusUrl, params);
export {
  apiQueryRule,
  apiAddRule,
  apiDeleteRule,
  apiQueryRuleByRuleId,
  apiupdateRule,
  apiupdateRuleStatus,
};
