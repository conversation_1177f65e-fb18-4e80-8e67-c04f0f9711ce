import { postJson, getJson } from "@/utils/axios";
const listUrl = "backbone/workflow/queryToDoByUserName";
const transferSubmitUrl = "backbone/workflow/op/transfer";
const redployUserTreeUrl = "backbone/tree/searchRedeployUser";
const redployOrgTreeUrl = "backbone/tree/searchRedeployOrg";
const inOfficeListUrl = "backbone/workflow/queryByAgentManId";
const transferToUrl = "backbone/workflow/queryByAgentManId";
const countByAgentManGrpUrl = "backbone/workflow/countByAgentManGrp";
const queryToDoCountsByUserUrl = "backbone/workflow/queryToDoCountsByUser";
const itCloudTransferSubmitUrl = "backbone/workflow/it/cloud/transfer";
const commCloudTransferSubmitUrl = "commCloud/workflow/transfer";
const transferTodoSubmitUrl = "commonprovince/operation/action"; //省分通用批量转办
const hangUpListUrl = "backbone/workflow/queryAllHang";
const countAllHangUpUrl = "backbone/workflow/countAllHangGrp";
const transferTodoSubmitUrlJtty = "commonFlow/workflow/batchRedeploy";

const apiGetTodoList = params => postJson(listUrl, params, { timeout: 0 });
const apiTransferSubmit = params => postJson(transferSubmitUrl, params);
const apiTransferTodoSubmit = params =>
  postJson(transferTodoSubmitUrl, params, { timeout: 0 });
const apiRedeployUserTree = params =>
  getJson(redployUserTreeUrl, params, { timeout: 0 });
const apiRedeployOrgTree = params =>
  getJson(redployOrgTreeUrl, params, { timeout: 0 });
const apiGetInOfficeList = params =>
  postJson(inOfficeListUrl, params, { timeout: 0 });
const apiGetTransferToList = params =>
  postJson(transferToUrl, params, { timeout: 0 });
const apiCountByAgentManGrp = params =>
  postJson(countByAgentManGrpUrl, params, { timeout: 0 });
const apiqueryToDoCountsByUser = params =>
  postJson(queryToDoCountsByUserUrl, params, { timeout: 0 });
const apiItCloudTransferSubmit = params =>
  postJson(itCloudTransferSubmitUrl, params, { timeout: 0 });
const apiCommCloudTransferSubmit = params =>
  postJson(commCloudTransferSubmitUrl, params, { timeout: 0 });
const apiGetHangUpList = params =>
  postJson(hangUpListUrl, params, { timeout: 0 });
const apiCountAllHangGrp = params =>
  postJson(countAllHangUpUrl, params, { timeout: 0 });
const apiTransferTodoSubmitJtty = params =>
  postJson(transferTodoSubmitUrlJtty, params, { timeout: 0 });
export {
  apiGetTodoList,
  apiTransferSubmit,
  apiRedeployUserTree,
  apiRedeployOrgTree,
  apiGetInOfficeList,
  apiGetTransferToList,
  apiCountByAgentManGrp,
  apiqueryToDoCountsByUser,
  apiItCloudTransferSubmit,
  apiCommCloudTransferSubmit,
  apiTransferTodoSubmit,
  apiGetHangUpList,
  apiCountAllHangGrp,
  apiTransferTodoSubmitJtty,
};
