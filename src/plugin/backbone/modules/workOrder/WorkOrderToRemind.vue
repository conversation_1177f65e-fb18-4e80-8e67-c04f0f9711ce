<template>
  <div style="padding: 0">
    <head-content-layout class="page-wrap">
      <template #header>
        <el-row style="display: flex">
          <el-col :xs="24" :sm="12" :md="14" :lg="18" style="display: flex">
            <div>
              <div style="display: inline-block; margin-left: 5px">专业：</div>
              <dict-select
                :value.sync="CprofessionalType"
                :dictId="10002"
                placeholder="请选择专业"
                style="width: 270px"
              />
            </div>
            <div>
              <div style="display: inline-block; margin-left: 20px">省份：</div>
              <dict-select
                :value.sync="province"
                :dictId="10053"
                placeholder="请选择省份"
                style="width: 240px"
              />
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :md="10" :lg="6" style="text-align: right">
            <div>
              <el-button type="primary" @click="seniorQuery">查询</el-button>
              <el-button @click="onResetForm">重置</el-button>
            </div>
          </el-col>
        </el-row>
        <div class="order__menuList">
          <el-button type="primary" @click="createSeup">+ 新建</el-button>
          <el-button @click="batchDelete">批量删除</el-button>
          <el-button @click="handlebatch('启用')">启用</el-button>
          <el-button @click="handlebatch('禁用')">禁用</el-button>
        </div>
      </template>

      <template #table>
        <el-table
          ref="table"
          :data="tableData"
          :border="false"
          stripe
          @selection-change="onSelectionChange"
          height="100%"
          v-loading="tableLoading"
        >
          <el-table-column
            type="selection"
            min-width="50"
            :index="indexMethod"
          />
          <el-table-column prop="createUserName" label="规则制定人" width="150">
          </el-table-column>
          <el-table-column
            prop="createUserDept"
            label="制定人所属组织"
            width="150"
          >
          </el-table-column>
          <el-table-column
            prop="province"
            label="省份"
            width="150"
          ></el-table-column>
          <el-table-column
            prop="professionalType"
            label="专业"
            width="150"
          ></el-table-column>
          <el-table-column
            prop="ruleComment"
            label="描述"
            width="200"
          ></el-table-column>
          <el-table-column prop="createType" label="工单来源" width="420">
            <template slot-scope="scope">
              <div
                v-for="(item, key) of scope.row.createType"
                :key="key"
                style="margin-right: 10px"
              >
                {{ handlecreateType(item) }},
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="ruleStatus" label="规则状态" width="150">
            <template slot-scope="scope">
              <span v-if="scope.row.ruleStatus == 0">未启用</span>
              <span v-else-if="scope.row.ruleStatus == 1">已启用</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="isSendAccOvertimeSoonMsg"
            label="是否发送受理即将超时短信"
            width="150"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.isSendAccOvertimeSoonMsg == 0">否</span>
              <span v-else-if="scope.row.isSendAccOvertimeSoonMsg == 1"
                >是</span
              >
            </template>
          </el-table-column>
          <el-table-column
            prop="isSendRetOvertimeSoonMsg"
            label="是否发送返单即将超时短信"
            width="150"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.isdSendRetOvertimeSoonMsg == 0">否</span>
              <span v-else-if="scope.row.isdSendRetOvertimeSoonMsg == 1"
                >是</span
              >
            </template>
          </el-table-column>
          <el-table-column
            prop="firstAccOvertimeTime"
            label="受理即将超时时间(第一次/分钟)"
            width="150"
          ></el-table-column>
          <el-table-column
            prop="secondAccOvertimeTime"
            label="受理即将超时时间（第二次）"
            width="150"
          ></el-table-column>
          <el-table-column
            prop="thirdAccOvertimeTime"
            label="受理即将超时时间（第三次）"
            width="150"
          ></el-table-column>
          <el-table-column
            prop="firstRetOvertimeTime"
            label="返单即将超时时间（第一次）"
            width="150"
          ></el-table-column>
          <el-table-column
            prop="secondRetOvertimeTime"
            label="返单即将超时时间（第二次）"
            width="150"
          ></el-table-column>
          <el-table-column
            prop="thirdRetOvertimeTime"
            label="返单即将超时时间（第三次）"
            width="150"
          ></el-table-column>
          <el-table-column
            prop="isSendAccOvertimeMsg"
            label="是否发送受理超时短信"
            width="150"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.isSendAccOvertimeMsg == 0">否</span>
              <span v-else-if="scope.row.isSendAccOvertimeMsg == 1">是</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="isSendRetOvertimeMsg"
            label="是否发送返单超时短信"
            width="150"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.isSendRetOvertimeMsg == 0">否</span>
              <span v-else-if="scope.row.isSendRetOvertimeMsg == 1">是</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150">
            <template slot-scope="scope">
              <el-button
                @click="clickEditor(scope.row)"
                type="text"
                size="small"
                :disabled="
                  scope.row.createUserId == createUserId ? false : true
                "
                >编辑</el-button
              >
              <el-popconfirm
                title="确定删除这条规则么?"
                @confirm="clickDelete(scope.row)"
              >
                <el-button
                  type="text"
                  size="small"
                  slot="reference"
                  style="margin-left: 10px"
                  :disabled="
                    scope.row.createUserId == createUserId ? false : true
                  "
                  >删除</el-button
                >
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pagination>
        <pagination
          ref="pagination"
          :total="form.total"
          :page.sync="form.pageNum"
          :limit.sync="form.pageSize"
          @change="seniorQuery"
        />
      </template>
    </head-content-layout>
    <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      width="500px"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="180px">
        <el-form-item label="规则制定人:" prop="createUserName">
          <el-input
            :disabled="title == '编辑'"
            v-model="form.createUserName"
            maxlength="25"
          ></el-input>
        </el-form-item>
        <el-form-item label="制订人所属组织:" prop="createUserDept">
          <el-input
            disabled
            v-model="form.createUserDept"
            maxlength="25"
          ></el-input>
        </el-form-item>

        <el-form-item label="省份:" prop="province">
          <dict-select
            :notSelect="title == '编辑' ? true : false"
            :value.sync="form.province"
            :dictId="10053"
            placeholder="请选择省份"
            style="width: 240px"
          />
        </el-form-item>

        <el-form-item label="专业:" prop="professionalType">
          <dict-select
            :notSelect="title == '编辑' ? true : false"
            :value.sync="form.professionalType"
            :dictId="10002"
            placeholder="请选择专业"
            style="width: 240px"
          />
        </el-form-item>

        <el-form-item label="描述:" prop="ruleComment">
          <el-input v-model="form.ruleComment" maxlength="25"></el-input>
        </el-form-item>

        <el-form-item label="工单来源:" prop="createType">
          <el-checkbox-group v-model="form.createType">
            <div style="display: flex">
              <el-checkbox label="电子运维新建" name="type"></el-checkbox>
              <el-checkbox label="智能监控自动" name="type"></el-checkbox>
            </div>
            <div style="display: flex">
              <el-checkbox label="智能监控手动" name="type"></el-checkbox>
              <el-checkbox label="电信共建共享" name="type"></el-checkbox>
            </div>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item
          label="是否发送受理即将超时短信:"
          prop="isSendAccOvertimeSoonMsg"
        >
          <el-radio-group
            @change="isSendAccOvertimeSoonMsgClcik"
            v-model="form.isSendAccOvertimeSoonMsg"
          >
            <el-radio label="是"></el-radio>
            <el-radio label="否"></el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="受理即将超时(第一次):" prop="firstAccOvertimeTime">
          <el-input v-model="form.firstAccOvertimeTime"></el-input>
          <span class="item__unit">分钟</span>
        </el-form-item>
        <el-form-item
          label="受理即将超时时间(第二次):"
          prop="secondAccOvertimeTime"
        >
          <el-input v-model="form.secondAccOvertimeTime"></el-input>
          <span class="item__unit">分钟</span>
        </el-form-item>
        <el-form-item
          label="受理即将超时时间(第三次):"
          prop="thirdAccOvertimeTime"
        >
          <el-input v-model="form.thirdAccOvertimeTime"></el-input>
          <span class="item__unit">分钟</span>
        </el-form-item>
        <el-form-item
          label="是否发送返单即将超时短信:"
          prop="isSendRetOvertimeSoonMsg"
        >
          <el-radio-group
            :disabled="form.professionalType == 3 || form.professionalTypeCode == 3"
            @change="isSendRetOvertimeSoonMsgClick"
            v-model="form.isSendRetOvertimeSoonMsg"
          >
            <el-radio label="是"></el-radio>
            <el-radio label="否"></el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="返单即将超时时间(第一次):"
          prop="firstRetOvertimeTime"
        >
          <el-input
            :disabled="form.professionalType == 3 || form.professionalTypeCode == 3"
            v-model="form.firstRetOvertimeTime"
          ></el-input>
          <span class="item__unit">分钟</span>
        </el-form-item>
        <el-form-item
          label="返单即将超时时间(第二次):"
          prop="secondRetOvertimeTime"
        >
          <el-input
            :disabled="form.professionalType == 3 || form.professionalTypeCode == 3"
            v-model="form.secondRetOvertimeTime"
          ></el-input>
          <span class="item__unit">分钟</span>
        </el-form-item>
        <el-form-item
          label="返单即将超时时间(第三次):"
          prop="thirdRetOvertimeTime"
        >
          <el-input
            :disabled="form.professionalType == 3 || form.professionalTypeCode == 3"
            v-model="form.thirdRetOvertimeTime"
          ></el-input>
          <span class="item__unit">分钟</span>
        </el-form-item>
        <el-form-item label="是否发送受理超时短信:" prop="isSendAccOvertimeMsg">
          <el-radio-group
            @change="isSendAccOvertimeMsgClick"
            v-model="form.isSendAccOvertimeMsg"
          >
            <el-radio label="是"></el-radio>
            <el-radio label="否"></el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="是否发送返单超时短信:" prop="isSendRetOvertimeMsg">
          <el-radio-group
            :disabled="form.professionalType == 3 || form.professionalTypeCode == 3"
            @change="isSendRetOvertimeMsgClick"
            v-model="form.isSendRetOvertimeMsg"
          >
            <el-radio label="是"></el-radio>
            <el-radio label="否"></el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="_save"> 确 定 </el-button>
        <el-button @click="_cancel">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import DictSelect from "./components/DictSelect.vue";
import HeadContentLayout from "./components/HeadContentLayout.vue";
import Pagination from "./components/Pagination.vue";
import {
  apiQueryRule,
  apiAddRule,
  apiDeleteRule,
  apiQueryRuleByRuleId,
  apiupdateRule,
  apiupdateRuleStatus,
} from "../workOrder/api/WorkOrderToRemind";
export default {
  name: "WorkOrderToRemind",
  components: {
    DictSelect,
    HeadContentLayout,
    Pagination,
  },
  data() {
    const checkAge = (rule, value, callback) => {
      if (this.form.isSendAccOvertimeSoonMsg == "是" && value == "") {
        callback(new Error("请填写时间(/分钟)"));
      } else if (!Number.isInteger(parseInt(value))) {
        callback(new Error("请输入数字"));
      } else {
        callback();
      }
    };
    return {
      CprofessionalType: "", //专业
      province: "全国", //省份
      tableLoading: false,
      tableData: [],
      title: "新增",
      dialogVisible: false,
      form: {
        createUserName: "", //规则指定人
        createUserDept: "",
        province: "全国",
        professionalType: "",
        professionalTypeCode: "",
        ruleComment: "",
        createType: [],
        ruleStatus: "",
        ruleId: "",
        isSendAccOvertimeSoonMsg: "否",
        isSendRetOvertimeSoonMsg: "否",
        firstAccOvertimeTime: "",
        secondAccOvertimeTime: "",
        thirdAccOvertimeTime: "",
        firstRetOvertimeTime: "",
        secondRetOvertimeTime: "",
        thirdRetOvertimeTime: "",
        isSendAccOvertimeMsg: "否",
        isSendRetOvertimeMsg: "否",
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      typeList: [
        "智能监控自动",
        "智能监控手动",
        "电子运维新建",
        "电信共建共享",
      ],
      multipleSelection: [],

      createUserId: JSON.parse(sessionStorage.userInfo).userName,

      rules: {
        province: [
          { required: true, message: "请选择省份", trigger: "change" },
        ],
        professionalType: [
          { required: true, message: "请选择专业", trigger: "change" },
        ],
        createType: [
          { required: true, message: "请选择工单来源", trigger: "change" },
        ],
        firstAccOvertimeTime: [{ validator: checkAge, trigger: "blur" }],
        secondAccOvertimeTime: [{ validator: checkAge, trigger: "blur" }],
        thirdAccOvertimeTime: [{ validator: checkAge, trigger: "blur" }],
      },
      options: [{ value: "中国联通总部", label: "中国联通总部" }],
    };
  },
  mounted() {
    this.form.createUserName = JSON.parse(sessionStorage.userInfo).realName;
    this.form.createUserDept = "中国联通总部";
    this.getQueryRule();
  },

  methods: {
    //工单来源
    handlecreateType(item) {
      return this.typeList[item];
    },
    //查询数据
    getQueryRule(type = "") {
      this.tableLoading = true;
      let seniorParam = {
        professionalType: this.CprofessionalType,
        province: this.province,
      };
      let defaultParam = {
        professionalType: "",
        province: "",
      };
      let param = {
        pageIndex: this.form.pageNum,
        pageSize: this.form.pageSize,
        param1:
          type == "senior"
            ? JSON.stringify(seniorParam)
            : JSON.stringify(defaultParam),
      };
      apiQueryRule(param)
        .then(res => {
          if (res.status == "0") {
            this.tableData = res?.data?.rows ?? [];
            this.form.total = res?.data?.totalElements ?? 0;
            console.log(res, "===========工单提醒");
          }
          this.tableLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.tableLoading = false;
        });
    },
    //新建
    createSeup() {
      this.title = "新增";
      this.dialogVisible = true;
    },
    //批量删除
    batchDelete() {
      if (this.multipleSelection.length == 0) {
        this.$message({
          message: "请先选择规则",
          type: "warning",
        });
        return;
      }
      this.$confirm("你确定要删除么?", "删除", {
        distinguishCancelAndClose: true,
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      }).then(() => {
        this.deleApiDeleteRule(this.multipleSelection);
      });
    },

    deleApiDeleteRule(params) {
      let param = {
        ruleIds: params,
      };
      let param1 = JSON.stringify(param);
      apiDeleteRule(param1).then(res => {
        if (res.status == "0") {
          this.getQueryRule();
          this.$message({
            message: "删除成功",
            type: "success",
          });
        }
      });
    },
    //启用 禁用
    handlebatch(type) {
      if (this.multipleSelection.length == 0) {
        this.$message({
          message: "请先选择规则",
          type: "warning",
        });
        return;
      }
      let seniorParam = {
        ruleIds: this.multipleSelection,
        ruleStatus: type == "启用" ? 1 : 0,
      };
      let param = {
        param1: JSON.stringify(seniorParam),
      };
      apiupdateRuleStatus(param).then(res => {
        if (res.status == "0") {
          this.getQueryRule();
          this.$message({
            message: type == "启用" ? "已启动" : "已禁用",
            type: "success",
          });
        }
      });
    },
    //重置
    onResetForm() {
      this.CprofessionalType = ""; //专业
      this.province = "";
    },
    //查询
    seniorQuery() {
      this.getQueryRule("senior");
    },
    //多选
    onSelectionChange(val) {
      let s = [];
      if (val.length > 0) {
        val.forEach(item => {
          s.push(item.ruleId);
        });
      }
      this.multipleSelection = s;
    },
    //点击编辑
    clickEditor(scope) {
      this.title = "编辑";
      this.dialogVisible = true;
      let param = {
        ruleId: scope.ruleId,
      };
      apiQueryRuleByRuleId(param).then(res => {
        if (res.status == "0" && res.data.rows.length > 0) {
          const arrData = res.data.rows;
          const {
            createUserName,
            province,
            professionalType,
            professionalTypeCode,
            ruleComment,
            createType,
            isSendAccOvertimeSoonMsg,
            firstAccOvertimeTime,
            secondAccOvertimeTime,
            thirdAccOvertimeTime,
            isSendAccOvertimeMsg,
            isSendRetOvertimeSoonMsg,
            firstRetOvertimeTime,
            secondRetOvertimeTime,
            thirdRetOvertimeTime,
            isSendRetOvertimeMsg,
            ruleId,
            createUserDept,
          } = arrData[0];
          const l = [];
          createType.forEach(el => {
            if (el == "0") {
              l.push("智能监控自动");
            }
            if (el == "1") {
              l.push("智能监控手动");
            }
            if (el == "2") {
              l.push("电子运维新建");
            }
            if (el == "3") {
              l.push("电信共建共享");
            }
            // if (el == "3") {
            //   l.push("电子运维新建");
            // }
            // if (el == "0") {
            //   l.push("智能监控自动");
            // }
            // if (el == "1") {
            //   l.push("智能监控手动");
            // }
            // if (el == "4") {
            //   l.push("电信共建共享");
            // }
          });
          this.form.ruleId = ruleId;
          this.form.createUserDept = createUserDept;
          this.form.createUserName = createUserName; //规则制定人
          this.form.province = province; //省份
          this.form.professionalType = professionalType; //专业
          this.form.professionalTypeCode = professionalTypeCode;
          this.form.ruleComment = ruleComment; //描述
          this.form.createType = l; //来源
          this.form.isSendAccOvertimeSoonMsg =
            isSendAccOvertimeSoonMsg == "0" ? "否" : "是";
          this.form.firstAccOvertimeTime = firstAccOvertimeTime;
          this.form.secondAccOvertimeTime = secondAccOvertimeTime;
          this.form.thirdAccOvertimeTime = thirdAccOvertimeTime;
          this.form.isSendAccOvertimeMsg =
            isSendAccOvertimeMsg == "0" ? "否" : "是";
          this.form.isSendRetOvertimeSoonMsg =
            isSendRetOvertimeSoonMsg == "0" ? "否" : "是";
          this.form.firstRetOvertimeTime = firstRetOvertimeTime;
          this.form.secondRetOvertimeTime = secondRetOvertimeTime;
          this.form.thirdRetOvertimeTime = thirdRetOvertimeTime;
          this.form.isSendRetOvertimeMsg =
            isSendRetOvertimeMsg == "0" ? "否" : "是";
        }
      });
    },
    //dialog确定
    _save() {
      if (
        this.form.province == "" ||
        this.form.professionalType == "" ||
        this.form.createType.length == 0
      ) {
        this.$message({
          message: "选择专业 省份 工单来源 才能新增",
          type: "warning",
        });
        return;
      }
      if (
        this.form.isSendAccOvertimeSoonMsg == "是" &&
        this.form.firstAccOvertimeTime == ""
      ) {
        this.$message({
          message: "当选择发送短信必须设置时间",
          type: "warning",
        });
        return;
      }
      let l = [];
      this.form.createType.forEach(el => {
        if (el == "智能监控自动") {
          l.push(0);
        }
        if (el == "智能监控手动") {
          l.push(1)
        }
        if (el == "电子运维新建") {
          l.push(2)
        }
        if (el == "电信共建共享") {
          l.push(3)
        }
        // if (el == "电子运维新建") {
        //   l.push(3);
        // }
        // if (el == "智能监控自动") {
        //   l.push(0);
        // }
        // if (el == "智能监控手动") {
        //   l.push(1);
        // }
        // if (el == "电信共建共享") {
        //   l.push(4);
        // }
      });
      let seniorParam = {
        createUserNameId: this.createUserId,
        createUserDept: this.form.createUserDept,
        province: this.form.province,
        professionalType:
          this.title == "新增"
            ? this.form.professionalType
            : this.form.professionalTypeCode,
        ruleComment: this.form.ruleComment,
        createType: l,
        isSendAccOvertimeSoonMsg:
          this.form.isSendAccOvertimeSoonMsg == "否" ? 0 : 1,
        isSendRetOvertimeSoonMsg:
          this.form.isSendRetOvertimeSoonMsg == "否" ? 0 : 1,
        firstAccOvertimeTime: this.form.firstAccOvertimeTime,
        secondAccOvertimeTime: this.form.secondAccOvertimeTime,
        thirdAccOvertimeTime: this.form.thirdAccOvertimeTime,
        firstRetOvertimeTime: this.form.firstRetOvertimeTime,
        secondRetOvertimeTime: this.form.secondRetOvertimeTime,
        thirdRetOvertimeTime: this.form.thirdRetOvertimeTime,
        isSendAccOvertimeMsg: this.form.isSendAccOvertimeMsg == "否" ? 0 : 1,
        isSendRetOvertimeMsg: this.form.isSendRetOvertimeMsg == "否" ? 0 : 1,
        ruleId: this.form.ruleId,
      };
      let param = {
        param1: JSON.stringify(seniorParam),
      };
      if (this.title == "新增") {
        apiAddRule(param).then(res => {
          if (res.status == "0") {
            this.dialogVisible = false;
            this.getQueryRule();
            this.$message({
              message: "添加成功",
              type: "success",
            });
            this.$refs["form"].resetFields();
          }else{
            this.$message.error("添加失败");
          }
        })
        .catch(error =>{
          if(error.status == "400"){
            this.$message.error(error.msg);
          }else{
            this.$message.error("添加失败");
          }
        });
      } else {
        apiupdateRule(param).then(res => {
          if (res.status == "0") {
            this.dialogVisible = false;
            this.getQueryRule();
            this.$message({
              message: "修改成功",
              type: "success",
            });
            this.$refs["form"].resetFields();
          }
        });
      }
    },
    //dialog取消
    _cancel() {
      this.dialogVisible = false;
      this.$refs["form"].resetFields();
    },
    //点击叉号
    handleClose() {
      this.dialogVisible = false;
      this.$refs["form"].resetFields();
    },
    //点击删除
    clickDelete(scope) {
      let l = [];
      l[0] = scope.ruleId;
      this.deleApiDeleteRule(l);
    },
    //是否发送短信
    isSendAccOvertimeSoonMsgClcik(label) {
      this.form.isSendAccOvertimeSoonMsg = label;
    },
    isSendRetOvertimeSoonMsgClick(label) {
      this.form.isSendRetOvertimeSoonMsg = label;
    },
    isSendAccOvertimeMsgClick(label) {
      this.isSendAccOvertimeMsg = label;
    },
    isSendRetOvertimeMsgClick(label) {
      this.from.isSendRetOvertimeMsg = label;
    },
    indexMethod(index) {
      const currentPage = this.form.pageNum;
      const pageSize = this.form.pageSize;
      return (currentPage - 1) * pageSize + (index + 1);
    },
  },
};
</script>

<style lang="scss" scoped>
@import "./workOrderWaitDetail/assets/common.scss";
.order__menuList {
  margin-top: 10px;
  padding-bottom: 10px;
}
::v-deep .el-dialog__body {
  padding: 0;
  padding-right: 50px;
  padding-left: 30px;
}
::v-deep .el-form-item__content {
  position: relative;
  .item__unit {
    position: absolute;
    display: block;
    top: 0;
    left: 106px;
    color: #dcdfe6;
  }
}
::v-deep .el-dialog__wrapper {
  .el-dialog {
    margin-top: 6vh !important;
    .el-dialog__body {
      .el-form {
        .el-form-item,
        .el-form-item--mini {
          margin: 0;
          margin-bottom: 14px;
        }
      }
    }
  }
}
</style>
