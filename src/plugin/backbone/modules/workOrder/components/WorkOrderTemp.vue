<template>
  <el-dialog
    title="骨干网建单模板选择"
    :visible="visible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :before-close="onBeforeClose"
    top="5vh"
    width="70%"
    @open="onOpen"
  >
    <el-form inline @submit.native.prevent>
      <el-form-item label="">
        <dict-select
          :value.sync="queryForm.professionalType"
          :dictId="10002"
          :notSelect="proNotSelect"
          placeholder="请选择专业"
        />
      </el-form-item>
      <el-form-item label="" v-if="netTypeShow">
        <dict-select
          :value.sync="queryForm.networkType"
          :dictId="10018"
          placeholder="请选择类型"
        />
      </el-form-item>
      <el-form-item label="">
        <el-input
          v-model.trim.lazy="queryForm.keyWord"
          placeholder="请输入关键词查询"
          clearable
          @keyup.native.enter="onOpen"
        ></el-input>
      </el-form-item>
      <el-form-item label="">
        <el-button type="primary" @click="onOpen">查询</el-button>
      </el-form-item>
    </el-form>

    <el-row class="temp-wrap" :gutter="20">
      <template v-if="tempList.length > 0">
        <el-col
          v-for="(item, i) in tempList"
          :key="i"
          :xs="24"
          :sm="24"
          :md="12"
          :lg="8"
        >
          <el-card class="card-temp" shadow="always">
            <div
              class="temp-cont cursor-pointer"
              :class="{ selected: selectedIndex == i }"
              @click="onSelectChange(i)"
            >
              <div class="icon-wrap">
                <div class="circle-wrap"><i class="fa fa-envelope" /></div>
              </div>
              <div style="overflow: auto">
                <div class="temp-name text-truncate" :title="item.templateName">
                  {{ item.templateName }}
                </div>
                <div
                  class="temp-secondary text-truncate"
                  :title="item.sheetTitle"
                >
                  工单主题: {{ item.sheetTitle }}
                </div>
                <div
                  class="temp-secondary text-truncate"
                  :title="item.faultPhenomenon"
                >
                  故障现象: {{ item.faultPhenomenon }}
                </div>
              </div>
            </div>
            <!-- <div class="temp-handle">
              <el-link
                class="handle-item cursor-pointer"
                :underline="false"
                :disabled="!hasAuth"
                @click="onTempEdit(item)"
                >修改</el-link
              >
              <el-link
                class="handle-item cursor-pointer"
                :underline="false"
                :disabled="!hasAuth"
                @click="onTempDel(item)"
                >删除</el-link
              >
            </div> -->
          </el-card>
        </el-col>
      </template>
      <el-col :span="24" v-else class="empty">暂无数据</el-col>
    </el-row>
    <el-pagination
      @current-change="currentChange"
      :current-page.sync="currentPage"
      :page-sizes="[9]"
      :page-size="9"
      layout="total, slot, ->, prev, pager, next, jumper"
      :total="total"
      background
    >
      <span class="el-icon--right"
        >第 {{ currentPage }} / {{ pageCount }} 页</span
      >
    </el-pagination>

    <div slot="footer">
      <!-- <el-button type="primary" :disabled="!hasAuth" @click="onTempAdd()"
        >新增模板</el-button
      > -->
      <el-button type="primary" @click="onTempSelect">提交</el-button>
    </div>

    <el-dialog
      :title="editDia.isAdd ? '添加模板' : '编辑模板'"
      :visible.sync="editDia.visible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
      width="500px"
      @close="onEditClose"
    >
      <el-form
        :model="editDia.form"
        ref="form"
        :rules="editDia.rules"
        label-width="100px"
        :inline="false"
      >
        <el-form-item label="模板名称: " prop="templateName">
          <el-input v-model="editDia.form.templateName"></el-input>
        </el-form-item>
        <el-form-item label="工单主题: " prop="sheetTitle">
          <el-input v-model="editDia.form.sheetTitle"></el-input>
        </el-form-item>
        <el-form-item label="故障现象: " prop="faultPhenomenon">
          <el-input
            type="textarea"
            v-model="editDia.form.faultPhenomenon"
          ></el-input>
        </el-form-item>
      </el-form>

      <span slot="footer">
        <el-button @click="editDia.visible = false">取消</el-button>
        <el-button type="primary" @click="onTempSave">保存</el-button>
      </span>
    </el-dialog>
  </el-dialog>
</template>

<script>
import { mapGetters } from "vuex";

import DictSelect from "./DictSelect.vue";

import * as workOrderTempApi from "../api/WorkOrderTemp.js";

export default {
  name: "WorkOrderTemp",
  components: {
    DictSelect,
  },
  props: {
    visible: Boolean,
    networkTypeTop: {
      type: Number,
      default: 0,
    },
    professionalType: {
      type: [String, Number],
    },
    networkType: {
      type: String,
    },
    proNotSelect: {
      type: Boolean,
      default: true,
    },
    netTypeShow: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      auths: [],
      tempList: [],
      queryForm: {
        networkTypeTop: this.networkTypeTop,
        professionalType: this.professionalType,
        networkType: this.networkType,
        keyWord: "",
      },
      currentPage: 1,
      total: 0,
      selectedIndex: -1,
      editDia: {
        isAdd: true,
        visible: false,
        form: {
          linkId: "",
          templateName: "",
          sheetTitle: "",
          faultPhenomenon: "",
        },
        rules: {
          templateName: [{ required: true, message: "请输入模板名称" }],
          sheetTitle: [{ required: true, message: "请输入工单主题" }],
          faultPhenomenon: [{ required: true, message: "请输入故障现象" }],
        },
      },
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
    pageCount() {
      return Math.max(1, Math.ceil(this.total / 9));
    },
    hasAuth() {
      return this.auths.includes(this.userInfo.userName);
    },
  },
  created() {
    // this.getLimits();
  },
  methods: {
    onBeforeClose(done) {
      this.$emit("update:visible", false);
      done?.();
    },
    onOpen() {
      this.currentPage = 1;
      this.getList();
    },
    getList() {
      this.selectedIndex = -1;
      workOrderTempApi
        .tempQueryApi({
          ...this.queryForm,
          keyWord: this.queryForm.keyWord,
          pageIndex: this.currentPage,
          pageSize: 9,
        })
        .then(res => {
          this.tempList = res.data.records;
          this.total = res.data.total;
        });
    },
    getLimits() {
      workOrderTempApi.tempAuthApi().then(res => {
        this.auths = JSON.parse(res.data || []);
      });
    },
    currentChange(currentPage) {
      this.currentPage = currentPage;
      this.getList();
    },
    onSelectChange(index) {
      this.selectedIndex = this.selectedIndex == index ? -1 : index;
      this.selectedIndex = index;
    },
    onTempSelect() {
      if (this.selectedIndex == -1) {
        this.$alert("请选择模板", "提示", {
          confirmButtonText: "确定",
        });
        return false;
      }
      this.$emit("select-change", this.tempList[this.selectedIndex]);
      this.onBeforeClose();
    },
    onTempAdd() {
      this.editDia.isAdd = true;
      this.editDia.visible = true;
    },
    onTempEdit(temp) {
      this.editDia.isAdd = false;
      this.editDia.form = Object.assign(this.editDia.form, temp);
      this.editDia.visible = true;
    },
    onEditClose() {
      this.editDia.form = {
        linkId: "",
        templateName: "",
        sheetTitle: "",
        faultPhenomenon: "",
      };
      this.$refs.form.resetFields();
    },
    onTempSave() {
      this.$refs.form.validate(valid => {
        if (valid) {
          workOrderTempApi
            .tempSaveApi(this.editDia.form)
            .then(res => {
              if (res.status == 0) {
                this.$message({
                  message: "模板保存成功",
                  type: "success",
                  showClose: true,
                  duration: 3000,
                });
                this.editDia.visible = false;
                this.getList();
              } else {
                this.$message({
                  message: `模板保存失败！${res.msg || ""}`,
                  type: "error",
                  showClose: true,
                  duration: 6000,
                });
              }
            })
            .catch(res => {
              this.$message({
                message: `模板保存失败！${res.msg || res.message || ""}`,
                type: "error",
                showClose: true,
                duration: 6000,
              });
            });
        }
      });
    },
    onTempDel(temp) {
      this.$confirm(`是否删除“${temp.templateName}”模板`, "删除确认", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          workOrderTempApi
            .tempDelApi([temp.linkId])
            .then(res => {
              if (res.status == 0) {
                this.$message({
                  message: "模板删除成功",
                  type: "success",
                  showClose: true,
                  duration: 3000,
                });
                this.total = this.total - 1;
                if (this.currentPage > this.pageCount) {
                  this.currentPage = this.pageCount;
                }
                this.getList();
              } else {
                this.$message({
                  message: `模板删除失败！${res.msg || ""}`,
                  type: "error",
                  showClose: true,
                  duration: 6000,
                });
              }
            })
            .catch(res => {
              this.$message({
                message: `模板删除失败！${res.msg || res.message || ""}`,
                type: "error",
                showClose: true,
                duration: 6000,
              });
            });
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss" scoped>
.temp-wrap {
  position: relative;
  max-height: 70vh;
  min-height: 380px;
  overflow-y: auto;
  .card-temp {
    margin-bottom: 15px;
    ::v-deep {
      .el-card__body {
        display: flex;
        flex-direction: column;
        padding: 0;
      }
    }
  }
  .temp-cont {
    display: flex;
    // height: 130px;
    padding: 10px;
    line-height: 2;
    transition: all 0.6s;
    &.selected {
      @include themify() {
        background-color: themed("$--color-primary-light-9");
      }
    }
    .icon-wrap {
      margin-right: 10px;
      display: flex;
      align-items: flex-start;
      .circle-wrap {
        display: inline-block;
        min-width: 30px;
        padding: 8px;
        border-radius: 50%;
        background-color: #009dff;
        color: #fff;
        line-height: 1;
      }
    }
    .temp-name {
      line-height: 2.2;
      font-weight: 700;
    }
    .temp-secondary {
      @include themify() {
        color: themed("$--color-text-secondary");
      }
    }
  }
  .temp-handle {
    height: 40px;
    @include themify() {
      background-color: themed("$frameContainerBg");
    }
    .handle-item {
      width: 50%;
      height: 100%;
    }
  }
  .empty {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    text-align: center;
  }
}
</style>
