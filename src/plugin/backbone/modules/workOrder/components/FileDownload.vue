<template>
  <div>
    <el-table :data="attachmentArr" style="width: 100%">
      <el-table-column label="文件名" prop="name"> </el-table-column>
      <el-table-column label="操作" width="120">
        <template slot-scope="scope">
          <span
            style="cursor: pointer"
            @click="downloadAppendixFile(scope.row)"
            title="下载"
          >
            <svg-icon
              style="font-size: 26px; vertical-align: middle"
              icon-class="file_download"
            ></svg-icon
          ></span>
        </template>
      </el-table-column>
    </el-table>
    <div
      slot="footer"
      style="text-align: right; margin-top: 10px; padding-bottom: 0px"
    >
      <el-button @click="cancel">取 消</el-button>
    </div>
  </div>
</template>
<script>
import { apiDownloadAppendixFile } from "../workOrderWaitDetail/api/CommonApi";
export default {
  name: "fileDownload",
  props: {
    attachmentArr: Array,
  },
  data() {
    return {};
  },
  mounted() {},
  methods: {
    downloadAppendixFile(data) {
      let param = {
        attId: data.id,
      };
      apiDownloadAppendixFile(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("文件下载成功");
          } else {
            this.$message.error("文件下载失败");
          }
        })
        .catch(error => {
          console.log(error);
          this.$message.error("文件下载失败");
        });
    },
    cancel() {
      this.$emit("cancel");
    },
  },
};
</script>
