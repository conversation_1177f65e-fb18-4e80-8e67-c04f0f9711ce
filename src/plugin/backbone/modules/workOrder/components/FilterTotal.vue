<template>
  <div class="filter-total">
    <span
      class="filter-total-item"
      :class="activeClassFunc"
      @click="onClickChange()"
      >全部</span
    >
    <template v-for="(item, i) in filters">
      <span
        :key="i"
        class="filter-total-item"
        :class="{ active: filterVal == item.value }"
        @click="
          onClickChange({
            value: item.value,
            label:item.label,
            professionalType: item.professionalType,
            networkTypeTop: item.networkTypeTop,
          })
        "
        >{{ item.label }}（{{ totals[item.value] || 0 }}）</span
      >
    </template>
  </div>
</template>

<script>
export default {
  name: "FilterTotal",
  props: {
    value: {
      type: [Number, String],
    },
    filters: Array,
    totals: Object,
  },
  data() {
    return {
      filterVal: this.value,
    };
  },
  created() {
    console.log(this.totals, "页数");
  },
  computed: {
    activeClassFunc() {
      return { active: (this.filterVal ?? "") == "" };
    },
  },
  watch: {
    value(newValue) {
      this.filterVal = newValue;
    },
  },
  methods: {
    onClickChange(val) {
      this.$emit("onConHeadFilterChange", val);
    },
  },
};
</script>

<style lang="scss" scoped>
.filter-total {
  .filter-total-item {
    display: inline-block;
    padding: 7px 15px;
    font-size: 13px;
    cursor: pointer;
    &.active {
      border-radius: 2px;
      @include themify() {
        background-color: themed("$--color-primary");
        color: themed("$--color-white");
      }
    }
  }
}
</style>
