<template>
  <div style="height: 93%">
    <el-table
      ref="table"
      :data="tableData"
      :border="false"
      stripe
      @selection-change="onSelectionChange"
      height="100%"
      v-loading="tableLoading"
    >
      <el-table-column type="selection" min-width="50" :index="indexMethod" />
      <el-table-column prop="createUserName" label="规则制定人" width="150">
      </el-table-column>
      <el-table-column prop="createUserDept" label="制定人所属组织" width="150">
      </el-table-column>
      <el-table-column
        prop="province"
        label="省份"
        width="150"
      ></el-table-column>
      <el-table-column
        prop="professionalType"
        label="专业"
        width="150"
      ></el-table-column>
      <el-table-column
        prop="ruleComment"
        label="描述"
        width="200"
      ></el-table-column>
      <el-table-column prop="createType" label="工单来源" width="450">
        <template slot-scope="scope">
          <div
            v-for="(item, key) of scope.row.createType"
            :key="key"
            style="margin-right: 10px"
          >
            {{ handlecreateType(item) }},
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="ruleStatus" label="规则状态" width="120">
      </el-table-column>
      <el-table-column
        prop="alertTimeStart"
        label="预警开始时间"
        width="150"
      ></el-table-column>
      <el-table-column
        prop="alertTimeEnd"
        label="预警结束时间"
        width="150"
      ></el-table-column>

      <el-table-column
        prop="isSendAccAlertMsg"
        label="是否发送受理预警语音"
        width="150"
      >
        <template slot-scope="scope">
          <span v-if="scope.row.isSendAccAlertMsg == 0">否</span>
          <span v-else-if="scope.row.isSendAccAlertMsg == 1">是</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="firstAccAlertTime"
        label="受理预警时间(第一次/分钟)"
        width="150"
      ></el-table-column>
      <el-table-column
        prop="secondAccAlertTime"
        label="受理预警时间（第二次）"
        width="150"
      ></el-table-column>
      <el-table-column
        prop="thirdAccAlertTime"
        label="受理预警时间（第三次）"
        width="150"
      ></el-table-column>
      <el-table-column prop="accAlertScope" label="受理预警对象" width="150">
        <template slot-scope="scope">
          {{ getAlertScope(scope.row.accAlertScope) }}
        </template>
      </el-table-column>
      <el-table-column prop="isSendRetAlertMsg" label="是否发送返单预警语音">
        <template slot-scope="scope">
          <span v-if="scope.row.isSendRetAlertMsg == 0">否</span>
          <span v-else-if="scope.row.isSendRetAlertMsg == 1">是</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="firstRetAlertTime"
        label="返单预警时间（第一次/分钟）"
        width="150"
      ></el-table-column>
      <el-table-column
        prop="secondRetAlertTime"
        label="返单预警时间（第二次）"
        width="150"
      ></el-table-column>
      <el-table-column
        prop="thirdRetAlertTime"
        label="返单预警时间（第三次）"
        width="150"
      ></el-table-column>
      <el-table-column prop="retAlertScope" label="返单预警对象" width="150">
        <template slot-scope="scope">
          {{ getAlertScope(scope.row.accAlertScope) }}
        </template>
      </el-table-column>
      <el-table-column label="预警内容" width="420">
        <span>
          【姓名】您好，你有一张故障工单【工单编号】待受理/待反馈，已发送至待办列表，即将在【受理时限】超时，请尽快处理
        </span>
      </el-table-column>
      <el-table-column
        prop="urgeTodoManPrivileges"
        label="催办权限人员"
        width="250"
      >
        <template slot-scope="scope">
          <span v-if="scope.row.urgeTodoManPrivileges != ''">
            <span
              v-for="(el, key) in scope.row.urgeTodoManPrivileges"
              :key="key"
              >{{ el.name }}
            </span>
          </span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150">
        <template slot-scope="scope">
          <el-button @click="clickEditor(scope.row)" type="text" size="small"
            >编辑</el-button
          >
          <el-popconfirm
            title="确定删除这条规则么?"
            @confirm="clickDelete(scope.row)"
          >
            <el-button
              type="text"
              size="small"
              slot="reference"
              style="margin-left: 10px"
              >删除</el-button
            >
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      ref="pagination"
      :total="form.total"
      :page.sync="form.pageNum"
      :limit.sync="form.pageSize"
      @change="seniorQuery"
    />
  </div>
</template>

<script>
import Pagination from "./Pagination.vue";
import {
  apiQueryRule,
  apiQueryRuleByRuleId,
  apiDeleteRule,
  apiupdateRuleStatus,
} from "../api/WorkOrderVoiceremind";
import Bus from "../bus";
export default {
  components: {
    Pagination,
  },
  props: {
    professionalType: String,
    province: {
      type: String,
      default: "全国",
    },
  },
  data() {
    return {
      tableLoading: false,
      form: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      tableData: [],
      typeList: [
        "电子运维新建",
        "智能监控自动",
        "智能监控手动",
        "电信共建共享",
      ],
      sclectList: [],
    };
  },
  mounted() {
    this.getTablelListData();
    Bus.$on("getList", () => {
      this.getTablelListData();
    });
  },
  methods: {
    //求情数据
    getTablelListData() {
      this.tableLoading = true;
      let seniorParam = {
        professionalType: this.professionalType,
        province: this.province,
      };
      let param = {
        pageIndex: this.form.pageNum,
        pageSize: this.form.pageSize,
        param1: JSON.stringify(seniorParam),
      };
      apiQueryRule(param)
        .then(res => {
          if (res.status == "0") {
            this.tableLoading = false;
            let r = res?.data?.rows ?? [];
            this.form.total = res.data.totalElements || 0;
            r.forEach(el => {
              if (el.urgeTodoManPrivileges != "") {
                el.urgeTodoManPrivileges = JSON.parse(el.urgeTodoManPrivileges);
              }
            });
            this.tableData = r;
          } else {
            this.tableLoading = false;
          }
        })
        .catch(err => {
          this.tableLoading = false;
          console.log(err);
        });
    },
    //编辑弹出diglog
    clickEditor(scope) {
      let param = {
        ruleId: scope.ruleId,
      };
      apiQueryRuleByRuleId(param).then(res => {
        if (res.status == "0") {
          let rowData = res.data || {};
          Bus.$emit("editor", rowData);
        }
      });
    },
    //删除
    clickDelete(row) {
      let r = [];
      r[0] = row.ruleId;
      this.deldetRule(r);
    },
    handlecreateType(item) {
      return this.typeList[item - 1];
    },
    //分页器
    seniorQuery() {
      this.getTablelListData();
    },
    //序号选择处理
    onSelectionChange(row) {
      if (row.length == 0) {
        this.sclectList = [];
      } else {
        let s = [];
        row.forEach(el => {
          s.push(el.ruleId);
        });
        this.sclectList = s;
      }
    },
    //批量删除
    batchDelete() {
      if (this.sclectList == 0) {
        this.$message({
          type: "warning",
          message: "请先选择规则",
        });
        return;
      }
      this.deldetRule(this.sclectList);
    },
    //启用禁用接口
    gettRemindSatus(stats) {
      if (this.sclectList.length == 0) {
        this.$message({
          type: "warning",
          message: "请先选择规则",
        });
        return;
      }
      let msg = stats == 0 ? "禁用" : "启用";
      let p = [];
      let r = [...this.sclectList];
      r.forEach(el => {
        p.push({
          ruleId: el,
          ruleStatus: stats,
        });
      });
      apiupdateRuleStatus(p).then(res => {
        if (res.status == "0") {
          this.$message({
            message: msg + "规则成功",
            type: "success",
          });
          this.getTablelListData();
        } else {
          this.$message.error(msg + "规则失败");
        }
      });
    },
    //删除接口
    deldetRule(r) {
      let param1 = {
        ruleIds: r,
      };
      let param = {
        param1: JSON.stringify(param1),
      };
      apiDeleteRule(param).then(res => {
        if (res.status == "0") {
          this.$message({
            message: "删除成功",
            type: "success",
          });
          this.getTablelListData();
        } else {
          this.$message.error("删除失败");
        }
      });
    },
    //选择框
    indexMethod(index) {
      const currentPage = this.form.pageNum;
      const pageSize = this.form.pageSize;
      return (currentPage - 1) * pageSize + (index + 1);
    },
    //受理/返单对象处理
    getAlertScope(key) {
      if (key.length) {
        return "主送,抄送";
      } else if (key == "1") {
        return "主送";
      } else if (key == "2") {
        return "抄送";
      } else {
        return "";
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../workOrderWaitDetail/assets/common.scss";
</style>
