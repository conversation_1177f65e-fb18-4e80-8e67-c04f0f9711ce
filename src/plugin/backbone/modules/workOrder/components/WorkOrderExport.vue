<template>
  <!-- 工单查询导出 -->
  <el-dialog
    append-to-body
    class="WordOrderExport"
    :visible.sync="visible"
    :title="title"
    :close-on-click-modal="isAutoClose"
    :before-close="beforeClose"
    @open="filterOpen"
    width="500px"
    v-loading="isLoading"
  >
    <div style="width: 100%">
      <el-button
        type="primary"
        :loading="btnLoading"
        style="margin-left: 4px"
        @click="onexport(1)"
      >
        导出当前页
      </el-button>
      <el-button
        type="primary"
        :loading="btnLoading"
        style="margin-left: 4px"
        @click="onexport(2)"
      >
        导出全量数据
      </el-button>

      <el-table
        stripe
        ref="table"
        :data="fieldList"
        style="width: 100%; margin-top: 10px"
        height="300px"
        @selection-change="handleSelectionChange"
        :cell-style="{ 'text-align': 'center' }"
        :header-cell-style="{ 'text-align': 'center' }"
      >
        <el-table-column type="selection" width="55"> </el-table-column>
        <el-table-column
          prop="dictCode"
          label="字段名称"
          min-width="70"
          show-overflow-tooltip
        >
        </el-table-column>
      </el-table>
    </div>
  </el-dialog>
</template>

<script>
import {
  apiQueryAllExcel,
  apiProvinceAllExcel,
  apiGtAllExcel,
  apiComAllExcel,
} from "../api/WorkOrderList";
import { apiDict } from "../api/CommonApi";
import { getJson } from "@/utils/axios";
import moment from "moment";
export default {
  name: "WordOrderExport",
  inheritAttrs: false,
  props: {
    visible: Boolean,
    orderType: String,
    province: String,
    areaType: String,
  },
  watch: {
    orderType(newVal, oldVal) {
      this.getFiels(newVal);
    },
  },
  data() {
    return {
      isAutoClose: false,
      isLoading: false,
      title: "工单列表自定义导出",
      fieldList: [],
      btnLoading: false,
      param: {
        pageIndex: null,
        pageSize: null,
        param1: {
          sheetTitleOrNoKeyword: null,
          sheetNo: null,
          sheetTitle: null,
          createType: null,
          startTime: null,
          endTime: null,
          sender: null,
          professionalType: null,
          emergencyLevel: null,
          processNodeId: null,
          sheetStatus: null,
          sheetLevel: null,
          agentMen: null,
          agentDept: null,
        },
      },
      startTime: moment().subtract(30, "days").format("YYYY-MM-DD 00:00:00"),
      endTime: moment().format("YYYY-MM-DD 23:59:59"),
      selectedFields: [],
    };
  },
  mounted() {
    this.getFiels();
  },
  methods: {
    getFiels(type) {
      console.log("type-------------", type);
      let a = 10054;
      switch (type) {
        case "ggw":
          a = 10104;
          break;
        case "ITy":
          a = 10105;
          break;
        case "txy":
          a = 10106;
          break;
        case "province":
          a = 10107;
          break;
        case "gt":
          a = 10109;
          break;
        case "sfty":
          a = 10402;
          break;
        //0509上线 137-151
        case "core":
          a = 10111;
          break;
        case "ip":
          a = 10113;
          break;
        case "pt":
          a = 10114;
          break;
        case "commonFlow":
          a = 10110;
          break;
        case "wxw":
          a = 10112;
          break;
        case "supervise":
          a = 811060;
          break;
      }
      let param = {
        dictTypeCode: a,
      };

      // 使用特定的API接口路径处理特定的dictTypeCode值
      const specialDictTypeCodes = [10054, 10104, 10105, 10106, 10111, 10113, 10114, 10110, 811060];
      if (specialDictTypeCodes.includes(a)) {
        // 对于特定的dictTypeCode值，使用listSort接口
        getJson("commonDict/enum/listSort", param)
          .then(res => {
            if (res.code == 200) {
              this.fieldList = res?.data ?? [];
            }
          })
          .catch(error => {
            console.log(error);
          });
      } else {
        // 对于其他dictTypeCode值，使用原来的list接口
        apiDict(param)
          .then(res => {
            if (res.code == 200) {
              this.fieldList = res?.data ?? [];
            }
          })
          .catch(error => {
            console.log(error);
          });
      }
    },

    async filterOpen() {
      console.log("@@@@ 初始化进入导出组件");
      this.btnLoading = false;
    },

    initData(param) {
      console.log("param=", param);
      this.param = JSON.parse(JSON.stringify(param));
    },
    handleSelectionChange(val) {
      if (!val || val.length == 0) {
        this.selectedFields = [];
        return;
      }
      let selectedFields = [];
      val.forEach(obj => {
        selectedFields.push(obj);
      });
      this.selectedFields = selectedFields;
    },

    /**
     * @param type 1-导出当前页 2-导出全量
     */
    onexport(type) {
      let _this = this;
      if (
        _this.selectedFields == undefined ||
        _this.selectedFields == null ||
        _this.selectedFields.length <= 0
      ) {
        var fieldList = this.fieldList;
        var selectedFields = [];
        for (var i = 0; i < fieldList.length; i++) {
          selectedFields.push(fieldList[i]);
        }
        _this.selectedFields = selectedFields;
      }
      if (
        (_this.param.param1.startTime == "" ||
          _this.param.param1.startTime == null) &&
        (_this.param.param1.endTime == "" || _this.param.param1.endTime == null)
      ) {
        _this.param.param1.startTime = _this.startTime;
        _this.param.param1.endTime = _this.endTime;
      }

      //导出字段排序modified by hq
      let selectedFieldsArr = _this.selectedFields.sort(
        (a, b) => a.dictId - b.dictId
      );
      let selectedFieldsName = selectedFieldsArr.map((obj, index) => {
        return obj.dictCode;
      });

      _this.param.param1.param = selectedFieldsName;

      if (this.orderType == "province") {
        if (this.areaType == "CITY") {
          this.province = null;
        }
        _this.param.param1.province = this.province;
      }
      let exportParam = {
        pageIndex: type == 1 ? _this.param.pageIndex : null,
        pageSize: type == 1 ? _this.param.pageSize : null,
        param1: JSON.stringify(_this.param.param1),
      };
      _this.btnLoading = true;
      if (this.orderType == "province") {
        apiProvinceAllExcel(exportParam)
          .then(res => {
            if (res.status == "0") {
              _this.btnLoading = false;
              if (type == 1) {
                _this.$notify({
                  message: "导出当前页成功",
                  type: "success",
                });
              } else if (type == 2) {
                _this.$notify({
                  message: "导出全量数据（最多5000条记录）成功",
                  type: "success",
                });
              }
            } else {
              _this.btnLoading = false;
              _this.$notify.error({
                message: "导出excel表失败",
              });
            }
          })
          .catch(error => {
            console.log(error);
            this.btnLoading = false;
            this.$notify.error({
              message: "导出excel表失败",
            });
          })
          .finally(() => {
            _this.selectedFields = [];
            this.$refs.table.clearSelection();
          });
      } else if (this.orderType == "gt") {
        apiGtAllExcel(exportParam)
          .then(res => {
            if (res.status == "0") {
              _this.btnLoading = false;
              if (type == 1) {
                _this.$notify({
                  message: "导出当前页成功",
                  type: "success",
                });
              } else if (type == 2) {
                _this.$notify({
                  message: "导出全量数据（最多5000条记录）成功",
                  type: "success",
                });
              }
            } else {
              _this.btnLoading = false;
              _this.$notify.error({
                message: "导出excel表失败",
              });
            }
          })
          .catch(error => {
            console.log(error);
            this.btnLoading = false;
            this.$notify.error({
              message: "导出excel表失败",
            });
          })
          .finally(() => {
            _this.selectedFields = [];
            this.$refs.table.clearSelection();
          });
      } else if (this.orderType == "sfty") {
        apiComAllExcel(exportParam)
          .then(res => {
            if (res.status == "0") {
              _this.btnLoading = false;
              if (type == 1) {
                _this.$notify({
                  message: "导出当前页成功",
                  type: "success",
                });
              } else if (type == 2) {
                _this.$notify({
                  message: "导出全量数据（最多5000条记录）成功",
                  type: "success",
                });
              }
            } else {
              _this.btnLoading = false;
              _this.$notify.error({
                message: "导出excel表失败",
              });
            }
          })
          .catch(error => {
            console.log(error);
            this.btnLoading = false;
            this.$notify.error({
              message: "导出excel表失败",
            });
          })
          .finally(() => {
            _this.selectedFields = [];
            this.$refs.table.clearSelection();
          });
      } else {
        apiQueryAllExcel(exportParam)
          .then(res => {
            if (res.status == "0") {
              _this.btnLoading = false;
              if (type == 1) {
                _this.$notify({
                  message: "导出当前页成功",
                  type: "success",
                });
              } else if (type == 2) {
                _this.$notify({
                  message: "导出全量数据（最多5000条记录）成功",
                  type: "success",
                });
              }
            } else {
              _this.btnLoading = false;
              _this.$notify.error({
                message: "导出excel表失败",
              });
            }
          })
          .catch(error => {
            console.log(error);
            this.btnLoading = false;
            this.$notify.error({
              message: "导出excel表失败",
            });
          })
          .finally(() => {
            _this.selectedFields = [];
            this.$refs.table.clearSelection();
          });
      }
    },
    beforeClose() {
      this.isLoading = false;
      this.$emit("update:visible", false);
    },
  },
};
</script>
