<template>
  <el-dialog
    :title="title"
    :visible="visible"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="600px"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="180px">
      <el-form-item label="规则制定人:" prop="createUserName">
        <el-input
          :disabled="title == '编辑'"
          v-model="form.createUserName"
          maxlength="25"
        ></el-input>
      </el-form-item>
      <el-form-item label="制订人所属组织:" prop="createUserDept">
        <el-input
          disabled
          v-model="form.createUserDept"
          maxlength="25"
        ></el-input>
      </el-form-item>

      <el-form-item label="省份:" prop="province">
        <dict-select
          :notSelect="title == '编辑' ? true : false"
          :value.sync="form.province"
          :dictId="10053"
          placeholder="请选择省份"
          style="width: 340px"
        />
      </el-form-item>

      <el-form-item label="专业:" prop="professionalType">
        <dict-select
          :notSelect="title == '编辑' ? true : false"
          :value.sync="form.professionalType"
          :dictId="10002"
          placeholder="请选择专业"
          style="width: 340px"
        />
      </el-form-item>

      <el-form-item label="描述:" prop="ruleComment">
        <el-input v-model="form.ruleComment" maxlength="25"></el-input>
      </el-form-item>
      <el-form-item label="预警开始时间:" prop="alertTimeStart">
        <el-time-picker
          v-model="form.alertTimeStart"
          value-format="HH:mm:ss"
          placeholder="请选择开始时间"
          style="width: 340px"
        >
        </el-time-picker>
      </el-form-item>
      <el-form-item label="预警结束时间:" prop="alertTimeEnd">
        <el-time-picker
          v-model="form.alertTimeEnd"
          value-format="HH:mm:ss"
          placeholder="请选择结束时间"
          style="width: 340px"
        >
        </el-time-picker>
      </el-form-item>

      <el-form-item label="工单来源:" prop="createType">
        <el-checkbox-group v-model="form.createType">
          <div style="display: flex">
            <el-checkbox label="电子运维新建" name="type"></el-checkbox>
            <el-checkbox label="智能监控自动" name="type"></el-checkbox>
          </div>
          <div style="display: flex">
            <el-checkbox label="智能监控手动" name="type"></el-checkbox>
            <el-checkbox label="电信共建共享" name="type"></el-checkbox>
          </div>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="是否发送受理预警语音:" prop="isSendAccAlertMsg">
        <el-radio-group
          @change="isSendAccAlertMsgClcik"
          v-model="form.isSendAccAlertMsg"
        >
          <el-radio label="是"></el-radio>
          <el-radio label="否"></el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="受理预警时间(第一次):" prop="firstAccAlertTime">
        <el-input
          :disabled="form.isSendAccAlertMsg == '否'"
          v-model="form.firstAccAlertTime"
        ></el-input>
        <span class="item__unit">分钟</span>
      </el-form-item>
      <el-form-item label="受理预警时间(第二次):" prop="secondAccAlertTime">
        <el-input
          :disabled="!form.firstAccAlertTime"
          v-model="form.secondAccAlertTime"
        ></el-input>
        <span class="item__unit">分钟</span>
      </el-form-item>
      <el-form-item label="受理预警时间(第三次):" prop="thirdAccAlertTime">
        <el-input
          :disabled="!form.secondAccAlertTime"
          v-model="form.thirdAccAlertTime"
        ></el-input>
        <span class="item__unit">分钟</span>
      </el-form-item>
      <el-form-item label="受理预警对象:" prop="accAlertScope">
        <el-checkbox-group
          :disabled="form.isSendAccAlertMsg == '否'"
          v-model="form.accAlertScope"
        >
          <el-checkbox label="主送" name="type"></el-checkbox>
          <el-checkbox label="抄送" name="type"></el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="是否发送返单预警语音:" prop="isSendRetAlertMsg">
        <el-radio-group v-model="form.isSendRetAlertMsg">
          <el-radio label="是"></el-radio>
          <el-radio label="否"></el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="返单预警时间(第一次):" prop="firstRetAlertTime">
        <el-input
          :disabled="form.isSendRetAlertMsg == '否'"
          v-model="form.firstRetAlertTime"
        ></el-input>
        <span class="item__unit">分钟</span>
      </el-form-item>
      <el-form-item label="返单预警时间(第二次):" prop="secondRetAlertTime">
        <el-input
          :disabled="!form.firstRetAlertTime"
          v-model="form.secondRetAlertTime"
        ></el-input>
        <span class="item__unit">分钟</span>
      </el-form-item>
      <el-form-item label="返单预警时间(第三次):" prop="thirdRetAlertTime">
        <el-input
          :disabled="!form.secondRetAlertTime"
          v-model="form.thirdRetAlertTime"
        ></el-input>
        <span class="item__unit">分钟</span>
      </el-form-item>

      <el-form-item label="返单预警对象:" prop="retAlertScope">
        <el-checkbox-group
          :disabled="form.isSendRetAlertMsg == '否'"
          v-model="form.retAlertScope"
        >
          <el-checkbox label="主送" name="type"></el-checkbox>
          <el-checkbox label="抄送" name="type"></el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="预警内容:" prop="alertContent">
        <div disabled class="alertContent">
          <span v-show="this.form.isSendAccAlertMsg == '是'"
            >受理：【姓名】您好，你有一张故障工单【工单编号】待受理，已发送至待办列表，即将在【受理时限时间】超时，请尽快处理。</span
          ><br />
          <span v-show="this.form.isSendRetAlertMsg == '是'">
            返单：【姓名】您好，你有一张故障工单【工单编号】待反馈，已发送至待办列表，即将在【受理时限时间】超时，请尽快处理。
          </span>
        </div>
      </el-form-item>
      <el-form-item label="催办权限人员:">
        <div style="display: flex">
          <div class="elTagBox">
            <el-tag
              :key="tag"
              v-for="tag in form.urgeTodoManPrivilegeList"
              closable
              :disable-transitions="false"
              @close="handleCloseAgen(tag)"
              style="margin-bottom 10px; "
            >
              {{ tag.name }}
            </el-tag>
          </div>
          <el-button
            style="height: 30px; margin-left 20px;"
            type="info"
            icon="el-icon-user"
            @click="onOpenPeopleDialog('ccDetermine')"
          ></el-button>
        </div>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="_save"> 确 定 </el-button>
      <el-button @click="_cancel">取 消</el-button>
    </span>
    <dia-orgs-user-tree
      :title="diaPeople.title"
      :visible.sync="diaPeople.visible"
      :showOrgsTree="diaPeople.showOrgsTree"
      @on-save="onSavePeople"
      :appendToBody="diaPeople.appendToBody"
    />
  </el-dialog>
</template>

<script>
import DiaOrgsUserTree from "../../workOrder/components/DiaOrgsUserTree.vue";
import DictSelect from "../../workOrder/components/DictSelect.vue";
import { apiAddRule, apiupdateRule } from "../api/WorkOrderVoiceremind";
import moment from "moment";
import Bus from "../bus";
export default {
  components: {
    DiaOrgsUserTree,
    DictSelect,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: "新增",
    },
  },

  data() {
    const checkAge = (rule, value, callback) => {
      if (!Number.isInteger(parseInt(value))) {
        callback(new Error("请输入数字"));
      } else {
        callback();
      }
    };
    var validHappenTime = (rule, value, callback) => {
      if (value) {
        let seconds = moment(this.form.alertTimeStart, "HH:mm:ss").diff(
          moment(value, "HH:mm:ss"),
          "seconds"
        );
        if (seconds > 0) {
          callback(new Error("结束时间不能小于开始时间"));
        } else {
          callback();
        }
      }
    };
    return {
      form: {
        createUserName: "", //规则指定人
        createUserDept: "",
        province: "全国",
        professionalType: "",
        alertTimeStart: "",
        alertTimeEnd: "",
        ruleComment: "",
        accAlertScope: [],
        retAlertScope: [],
        accAlertContent: "",
        alertContent: "",
        peopleItems: [],
        createType: [],
        ruleStatus: "",
        ruleId: "",
        isSendAccAlertMsg: "否",
        isSendRetAlertMsg: "否",
        firstAccAlertTime: "",
        secondAccAlertTime: "",
        thirdAccAlertTime: "",
        firstRetAlertTime: "",
        secondRetAlertTime: "",
        thirdRetAlertTime: "",
        urgeTodoManPrivilegeList: [],
      },
      createUserId: JSON.parse(sessionStorage.userInfo).userName,
      diaPeople: {
        title: "催办权限人员",
        visible: false,
        showOrgsTree: false, //是否显示组织树
        appendToBody: true, //嵌套dialog
      },
      rules: {
        firstAccAlertTime: [{ validator: checkAge, trigger: "blur" }],
        secondAccAlertTime: [{ validator: checkAge, trigger: "blur" }],
        thirdAccAlertTime: [{ validator: checkAge, trigger: "blur" }],
        firstRetAlertTime: [{ validator: checkAge, trigger: "blur" }],
        secondRetAlertTime: [{ validator: checkAge, trigger: "blur" }],
        thirdRetAlertTime: [{ validator: checkAge, trigger: "blur" }],
        alertTimeEnd: [{ validator: validHappenTime, trigger: "blur" }],
      },
    };
  },
  watch: {
    "form.isSendAccAlertMsg": {
      handler(newV) {
        if (newV == "否") {
          this.form.firstAccAlertTime = "";
          this.form.secondAccAlertTime = "";
          this.form.thirdAccAlertTime = "";
          this.form.accAlertScope = [];
        }
      },
      deep: true,
    },
    "form.isSendRetAlertMsg": {
      handler(newV) {
        if (newV == "否") {
          this.form.firstRetAlertTime = "";
          this.form.secondRetAlertTime = "";
          this.form.thirdRetAlertTime = "";
          this.form.retAlertScope = [];
        }
      },
      deep: true,
    },
  },
  mounted() {
    this.form.createUserName = JSON.parse(sessionStorage.userInfo).realName;
    this.form.createUserDept = "中国联通总部";
    Bus.$on("editor", row => {
      this.setEditor(row);
    });
  },
  methods: {
    //编辑打开弹窗
    setEditor(row) {
      let r = { ...row };
      r.professionalType = r.professionalType + "";
      r.createType = this.setCreateType(r.createType);
      r.isSendAccAlertMsg = r.isSendAccAlertMsg == 1 ? "是" : "否";
      r.isSendRetAlertMsg = r.isSendRetAlertMsg == 1 ? "是" : "否";
      r.accAlertScope = this.setScope(r.accAlertScope);
      r.retAlertScope = this.setScope(r.retAlertScope);
      this.form = { ...r };
      this.$emit("update:title", "编辑");
      this.$emit("update:visible", true);
    },
    //x号关闭
    handleClose() {
      this.$emit("update:visible", false);
      this.$refs.form.resetFields();
      // this.form.peopleItems = [];
      this.form.urgeTodoManPrivilegeList = [];
    },
    //表单提交
    _save() {
      if (
        this.form.province == "" ||
        this.form.professionalType == "" ||
        this.form.createType.length == 0 ||
        this.form.alertTimeStart == "" ||
        this.form.alertTimeEnd == ""
      ) {
        this.$message({
          message: "必须选择专业 省份 工单来源 预警开始结束时间 才能新增",
          type: "warning",
        });
        return;
      }
      if (
        (this.form.isSendAccAlertMsg == "是" &&
          this.form.firstAccAlertTime == "" &&
          this.form.accAlertScope == []) ||
        (this.form.isSendRetAlertMsg == "是" &&
          this.form.firstRetAlertTime == "" &&
          this.form.retAlertScope == [])
      ) {
        this.$message({
          message: "当选择发送语音必须选择发送对象和时间",
          type: "warning",
        });
        return;
      }
      let seniorParam = {
        createUserNameId: this.createUserId,
        createUserDept: this.form.createUserDept,
        province: this.form.province,
        professionalType: this.form.professionalType,
        ruleComment: this.form.ruleComment,
        alertTimeStart: this.form.alertTimeStart,
        alertTimeEnd: this.form.alertTimeEnd,
        createType: this.setCreateType(this.form.createType),
        isSendAccAlertMsg: this.form.isSendAccAlertMsg == "是" ? 1 : 0,
        isSendRetAlertMsg: this.form.isSendRetAlertMsg == "是" ? 1 : 0,
        firstAccAlertTime: this.form.firstAccAlertTime,
        secondAccAlertTime: this.form.secondAccAlertTime,
        thirdAccAlertTime: this.form.thirdAccAlertTime,
        accAlertScope: this.setScope(this.form.accAlertScope),
        firstRetAlertTime: this.form.firstRetAlertTime,
        secondRetAlertTime: this.form.secondRetAlertTime,
        thirdRetAlertTime: this.form.thirdRetAlertTime,
        retAlertScope: this.setScope(this.form.retAlertScope),
        // urgeTodoManPrivileges: this.form.peopleItems,
        urgeTodoManPrivileges: this.form.urgeTodoManPrivilegeList,
      };
      if (this.title == "编辑") {
        seniorParam.ruleId = this.form.ruleId;
      }
      let param = {
        param1: JSON.stringify(seniorParam),
      };
      if (this.title == "新增") {
        apiAddRule(param).then(res => {
          if (res.status == "0") {
            this.$emit("update:visible", false);
            this.$refs["form"].resetFields();
            this.form.peopleItems = [];
            this.$message({
              message: "添加成功",
              type: "success",
            });
            Bus.$emit("getList");
          } else {
            this.$message.error("添加失败");
          }
        });
      } else {
        apiupdateRule(param).then(res => {
          if (res.status == "0") {
            this.$emit("update:visible", false);
            this.$refs.form.resetFields();
            this.form.peopleItems = [];
            this.form.urgeTodoManPrivilegeList = [];
            Bus.$emit("getList");
            this.$message({
              message: "修改成功",
              type: "success",
            });
          } else {
            this.$message.error("修改失败");
          }
        });
      }
    },
    setCreateType(arr) {
      let l = [];
      if (Array.isArray(arr)) {
        arr.forEach(el => {
          if (el == "电子运维新建") {
            l.push(2);
          }
          if (el == "智能监控自动") {
            l.push(0);
          }
          if (el == "智能监控手动") {
            l.push(1);
          }
          if (el == "电信共建共享") {
            l.push(3);
          }
          // if (el == 1) {
          //   l.push("电子运维新建");
          // }
          // if (el == 2) {
          //   l.push("智能监控自动");
          // }
          // if (el == 3) {
          //   l.push("智能监控手动");
          // }
          // if (el == 4) {
          //   l.push("电信共建共享");
          // }
        });
      }

      return l;
    },
    setScope(arr) {
      let l = [];
      if (Array.isArray(arr)) {
        arr.forEach(el => {
          if (el == "主送") {
            l.push(1);
          } else if (el == "抄送") {
            l.push(2);
          } else if (el == 1) {
            l.push("主送");
          } else if (el == 2) {
            l.push("抄送");
          }
        });
      }

      return l;
    },
    //取消
    _cancel() {
      this.$emit("update:visible", false);
      this.$refs.form.resetFields();
      // this.form.peopleItems = [];
      this.form.urgeTodoManPrivilegeList = [];
    },
    //移除催办人员
    handleCloseAgen(tag) {
      // this.form.peopleItems.splice(this.form.peopleItems.indexOf(tag), 1);
      this.form.urgeTodoManPrivilegeList.splice(
        this.form.urgeTodoManPrivilegeList.indexOf(tag),
        1
      );
    },
    //人员树总开关
    onOpenPeopleDialog() {
      this.diaPeople.visible = true;
    },
    //人员树确定
    onSavePeople({ usersChecked }) {
      if (usersChecked.length > 0) {
        let d = [];
        usersChecked.forEach(el => {
          d.push({
            id: el.id,
            name: el.name,
          });
        });
        // this.form.peopleItems = d;
        this.from.urgeTodoManPrivilegeList = d;
      }
    },
    //是否发送受理预警语音
    isSendAccAlertMsgClcik(label) {
      this.form.isSendAccAlertMsg = label;
    },
    //是否发送返单预警语音
    isSendRetAlertMsgClick(label) {
      this.isSendRetAlertMsg = label;
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-dialog {
  .elTagBox {
    display: flex;
    flex-wrap: wrap;

    .el-tag {
      margin-bottom: 10px;
    }
  }

  .alertContent {
    width: 340px;
    border: 1px solid #ebeef5;
    cursor: not-allowed;
    padding: 10px;

    span {
      font-size: 12px;
      color: #909399;
      margin-bottom: 10px;
    }
  }
}
</style>
