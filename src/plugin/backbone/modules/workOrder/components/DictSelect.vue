<template>
  <el-select
    v-model="selectVal"
    :placeholder="placeholder"
    :disabled="notSelect"
    clearable
    filterable
    :multiple="multiple"
    @change="onChange"
  >
    <el-option
      v-for="(item, i) in dictData"
      :key="i"
      :label="item.dictName"
      :value="item.dictCode"
    >
      <span style="float: left">{{ item.dictName }}</span>
      <span v-if="item.showStar && item.showStar == '1'" style="float: right">
        <img src="../assets/img/recommended_orange.png"
      /></span>
    </el-option>
  </el-select>
</template>

<script>
import { apiDict } from "../api/CommonApi";
export default {
  name: "DictSelect",
  props: {
    value: {
      type: [Number, String, Array],
    },
    dictId: [Number, String],
    placeholder: String,
    notSelect: {
      default: false,
    },
    dictType: [Number, String],
    dictModule: [Number, String],
    dictList: Array,
    multiple: <PERSON><PERSON>an,
    woId: String,
  },
  data() {
    return {
      selectVal: this.value,
      dictData: [],
    };
  },
  watch: {
    dictId(newValue) {
      if (newValue) this.getDictData();
    },
    dictType(newValue) {
      if (newValue) this.getDictData();
    },
    dictModule(newValue) {
      if (newValue) this.getDictData();
    },
    value(newValue) {
      this.selectVal = newValue;
      // this.$emit("update:value", this.selectVal);
    },
    notSelect(newValue) {
      this.notSelect = newValue || false;
    },
    deep: true,
    immediate: true,
  },
  mounted() {
    if (this.dictId) this.getDictData();
  },
  methods: {
    getDictData() {
      let param = {
        dictTypeCode: this.dictId,
      };
      if (this.dictType) {
        param.dictType = this.dictType;
      }
      if (this.dictModule) {
        param.dictModule = this.dictModule;
      }
      this.$set(param, "woId", this.woId);
      apiDict(param)
        .then(res => {
          if (res.code == 200) {
            this.dictData = res?.data ?? [];
          }
        })
        .catch(error => {
          console.log(error);
        })
        .finally(() => {
          this.$emit("update:dict-list", this.dictData);
        });
    },
    onChange() {
      this.$emit("update:value", this.selectVal);
      this.$emit("change", this.selectVal);
    },
  },
};
</script>
