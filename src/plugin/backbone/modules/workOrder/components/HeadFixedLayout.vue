<template>
  <div class="page-wrap full-main" :style="vmStyle">
    <div class="header-wrap" ref="headerWrap">
      <slot name="header"></slot>
    </div>

    <div class="content-wrap" :style="conentStyle">
      <slot name="default"></slot>
    </div>
  </div>
</template>

<script>
import {
  addResizeListener,
  removeResizeListener,
} from "element-ui/src/utils/resize-event";
import { mapGetters } from "vuex";
import debounce from "lodash/debounce";

export default {
  name: "HeadFixedLayout",
  data() {
    return {
      domAttrs: {
        headerHeight: 58,
      },
    };
  },
  computed: {
    ...mapGetters(["frameStyle"]),
    vmStyle() {
      return {
        height: `calc(100vh - ${this.frameStyle.headerHeight - 16}px)`,
      };
    },
    conentStyle() {
      return {
        height: `calc(100% - ${this.domAttrs.headerHeight}px)`,
      };
    },
  },
  created() {
    this.getHeaderHeightFunc = debounce(this.getHeaderHeight, 200);
  },
  mounted() {
    addResizeListener(this.$refs.headerWrap, this.getHeaderHeightFunc);
  },
  beforeDestroy() {
    removeResizeListener(this.$refs.headerWrap, this.getHeaderHeightFunc);
  },
  methods: {
    getHeaderHeight() {
      if (this.$refs.headerWrap) {
        this.domAttrs.headerHeight = Math.ceil(
          window
            .getComputedStyle(this.$refs.headerWrap)
            ?.height?.replace?.(/auto|px/, "") ?? 58
        );
      } else {
        this.domAttrs.headerHeight = 58;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.page-wrap {
  min-height: 400px;
  .header-wrap {
    padding: 10px 20px 6px;
    max-height: 320px;
    overflow: auto;
    @include themify() {
      background-color: themed("$--color-white");
      border-bottom: 1px solid themed("$--border-color-base");
      box-shadow: themed("$--box-shadow-light");
    }
  }
  .content-wrap {
    padding: 20px 20px /*  30px */;
    overflow: auto;
  }
}
</style>
