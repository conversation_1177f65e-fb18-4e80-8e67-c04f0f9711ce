<template>
  <div class="stage-feedback">
    <el-form ref="stageBackForm"
             :model="stageBackForm"
             label-width="90px"
             :rules="stageBackFormRules">

      <el-form-item label="反馈人:" prop="createUserCn" required>
        <el-input
          placeholder="请输入"
          v-model="stageBackForm.createUserCn"
          style="width: 100%"
          clearable
        >
        </el-input>
      </el-form-item>
      <el-form-item label="反馈时间:" prop="createTime" required>
        <el-date-picker
          v-model="stageBackForm.createTime"
          type="datetime"
          placeholder="请选择时间"
          value-format="yyyy-MM-dd HH:mm:ss"
          clearable
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="反馈内容:" prop="stageContent" required>
        <el-input
          type="textarea"
          :rows="3"
          placeholder="请输入内容"
          v-model="stageBackForm.stageContent"
          style="width: 100%"
          clearable
        >
        </el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: right">
      <el-button
        type="primary"
        @click="handleSubmit('stageBackForm')"
        v-loading.fullscreen.lock="stageBackFullscreenLoading"
        >提 交</el-button
      >
      <el-button @click="cancelStageBack">取 消</el-button>
    </div>

  </div>
</template>
<script>
import { mapGetters } from "vuex";
import {
  apiActionPublic,
  getCurrentTime,
  apiGroupCode,
  apiFileUpload,
} from "../api/CommonApi";
import FileUpload from "./FileUpload.vue";
export default {
  props: {
    common: Object,
    actionName: String,
  },
  name: "StageFeedback",
  components: { FileUpload },
  data() {
    return {
      stageBackForm: {
        woId: "",
        processInstId: "",
        actionName: "",
        workItemId: "",
        stageContent: "",
        createUserCn:"",
        createTime:"",
      },
      stageBackFormRules: {
        createUserCn: [{ required: true, message: "反馈人不能为空" }],
        createTime: [{ required: true, message: "反馈时间不能为空" }],
        stageContent: [{ required: true, message: "反馈内容不能为空" }],
      },

      stageBackFullscreenLoading: false,
      groupKey: "",
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  mounted() {
    this.stageBackForm.woId = this.common.woId;
    this.stageBackForm.processInstId = this.common.processInstId;
    this.stageBackForm.workItemId = this.common.workItemId;
    this.stageBackForm.createUserCn = this.userInfo.realName;
    this.stageBackForm.createTime = getCurrentTime(new Date());
    this.stageBackForm.actionName = this.actionName;
    // this.stageBackForm.processDefId = this.common.processDefId;
    // this.stageBackForm.processNode = this.common.processNode;
  },
  methods: {
    handleSubmit(formName) {
      this.$refs[formName].validate(async valid => {
        if (valid) {
          this.stageBackFullscreenLoading = true;
          let params = JSON.parse(JSON.stringify(this.stageBackForm));
          // params.createUserCn = this.stageBackForm.person;// this.userInfo.userName;
          // params.createTime = this.stageBackForm.time;//getCurrentTime(new Date());
          // params.actionName = this.actionName;
          // params.stageContent = this.stageBackForm.processSuggestion;


          this.submit(params);

        }
        else {
          return false;
        }
      });
    },
    submit(params) {
      apiActionPublic(params)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("阶段反馈完成");
            this.onResetStageBackForm();
            this.$emit("stageBackDialogClose","1");
          } else {
            this.$message.error("阶段反馈失败");
          }
          this.stageBackFullscreenLoading = false;
          // this.stageBackForm.attachmentFile = "";
        })
        .catch(error => {
          console.log(error);
          this.$message.error("阶段反馈失败");
          this.stageBackFullscreenLoading = false;
        });
    },

    onResetStageBackForm() {
      this.stageBackForm = {
        createUserCn: this.userInfo.realName,
        createTime: getCurrentTime(new Date()),
        woId: this.common.woId,
        workItemId: this.common.workItemId,
        processInstId: this.common.processInstId,
        processNode: this.common.processNode,
        actionName : this.actionName
      };
    },

    cancelStageBack(){
      this.onResetStageBackForm();
      this.$emit("stageBackDialogClose","0");
    }

  },
};
</script>
