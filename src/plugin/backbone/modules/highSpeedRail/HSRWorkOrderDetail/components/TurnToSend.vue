<template>
  <div class="transCla">
    <el-form
      ref="transferForm"
      :model="transferForm"
      :rules="transferFormRules"
      label-width="90px"
    >
      <el-form-item label="转派说明:" prop="explain">
        <el-input
          type="textarea"
          placeholder="请填写转派说明"
          v-model="transferForm.explain"
          style="width: 100%"
        ></el-input>
      </el-form-item>
      <el-form-item label="转派对象:" prop="transferObject" required>
        <el-input
          v-model="transferForm.transferObject"
          placeholder="添加人员"
          style="width: 100%"
        >
          <template v-for="(tag, index) in organizeForm.builderZsList">
            <el-tag
              slot="prefix"
              style="margin-top: 5px"
              :key="index"
              closable="true"
              @close="handleClose('builderZs', tag)"
              v-show="index < 1"
              v-if="tag.bz == 'user'"
            >
              {{ tag.name }}
            </el-tag>
            <el-tag
              slot="prefix"
              style="margin-top: 5px"
              :key="index"
              closable="true"
              @close="handleClose('builderZs', tag)"
              v-show="index < 1"
              v-if="tag.bz == 'org'"
            >
              {{ tag.orgName }}
            </el-tag>
          </template>
          <el-popover
            slot="prefix"
            v-if="organizeForm.builderZsList.length >= 2"
            width="500"
            trigger="click"
          >
            <el-input
              v-model="organizeForm.builderZsName"
              placeholder="请输入转派人员姓名/组织名称"
            >
              <el-button
                type="info"
                slot="append"
                icon="el-icon-search"
                @click="search('builderZs')"
              >
              </el-button>
              <el-button
                type="info"
                slot="append"
                icon="el-icon-close"
                @click="clear('builderZs')"
              >
              </el-button>
            </el-input>
            <el-table
              ref="multipleTable"
              tooltip-effect="dark"
              @selection-change="handleSelectionChange"
              :data="organizeForm.builderZsListCopy"
              max-height="240"
            >
              <el-table-column width="30" type="selection"> </el-table-column>
              <el-table-column min-width="70" property="name" label="姓名">
              </el-table-column>
              <el-table-column min-width="180" property="orgName" label="组织">
              </el-table-column>
              <el-table-column
                min-width="120"
                property="mobilePhone"
                label="电话"
              >
              </el-table-column>
              <el-table-column fixed="right" label="操作" width="50">
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    size="small"
                    @click.native.prevent="handleClose('builderZs', scope.row)"
                    >移除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-button
              size="small"
              type="text"
              @click="toggleSelection('builderZs')"
              >批量移除
            </el-button>
            <el-tag slot="reference" style="margin-top: 3px"
              >+{{ organizeForm.builderZsList.length - 1 }}
            </el-tag>
          </el-popover>
          <el-button
            style="width: 30px"
            type="info"
            slot="append"
            icon="el-icon-user"
            @click="onOpenPeopleDialog('transferObjectDetermine')"
          ></el-button>
        </el-input>
      </el-form-item>
      <el-form-item label="附件:" prop="attachmentFile">
        <el-input
          v-model="transferForm.attachmentFile"
          placeholder="添加附件"
          clearable
          readonly
          style="width: 100%"
        >
          <el-button
            style="width: 30px"
            type="info"
            slot="append"
            @click="attachmentBrowse"
            >+</el-button
          >
        </el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: right">
      <el-button
        type="primary"
        @click="transferSubmit('transferForm')"
        v-loading.fullscreen.lock="transferSubmitLoading"
        >提 交</el-button
      >
      <el-button @click="transferCancel">取 消</el-button>
    </div>

    <!--    选择对象-->
    <!--    <dia-orgs-user-tree-other-->
    <!--      :title="diaPeople.title"-->
    <!--      :visible.sync="diaPeople.visible"-->
    <!--      :showOrgsTree="true"-->
    <!--      :professionalType="common.professionalTypeName"-->
    <!--      @on-save="onSavePeople"-->
    <!--      :appendToBody="true"-->
    <!--    />-->

    <dia-tissue-tree
      v-if="isDiaOrgsUserTree"
      :title="diaPeople.title"
      :visible.sync="diaPeople.visible"
      :showOrgsTree="true"
      :professionalType="common.professionalTypeName"
      @on-save="onSavePeople"
      :appendToBody="true"
      :userAttribution="userAttribution"
      :show-contact-user-tab="true"
      :show-contact-org-tab="true"
      :reassignmentType="reassignmentType"
    />

    <!--    附件-->
    <el-dialog
      width="420px"
      title="附件选择"
      append-to-body
      :visible.sync="attachmentDialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <file-upload
        v-if="attachmentDialogVisible"
        @change="changeFileData"
        @cancel="closeAttachmentDialog"
      ></file-upload>
    </el-dialog>
  </div>
</template>
<script>
import {
  apiRedeployUserTree,
  apiRedeployOrgTree,
} from "../../../workOrder/api/WorkOrderTodo.js";
import {
  apiActionPublic,
  getCurrentTime,
  apiFileUpload,
  apiGroupCode,
} from "../api/CommonApi";
// import DiaOrgsUserTreeOther from "./DiaOrgsUserTreeOther.vue";
import FileUpload from "../../../provinceOrder/workOrderWaitDetail/components/FileUpload";
import DiaTissueTree from "@/plugin/backbone/components/common/DiaTissueTree";

export default {
  name: "TurnToSend",
  props: {
    common: Object,
    actionName: String,
  },
  components: {
    DiaTissueTree,
    FileUpload,
  },
  data() {
    return {
      reassignmentType: "province",
      isDiaOrgsUserTree: false,
      userAttribution: "gtTurnToSendUser",
      transferForm: {
        explain: "",
        transferObject: "",
        transferObjectUserId: "", //选择的人员id
        transferObjectOrgId: "", //选择的组织id
        agentMan: null, //选择的人员名字
        agentDeptName: null, //选择的组织名字
        agentManDetail: null,
        attachmentFile: "", //附件
      },
      transferFormRules: {
        transferObject: [{ required: true, message: "转派对象不能为空" }],
      },

      importForm: {
        //附件
        attachmentFileList: [],
      },
      attachmentDialogVisible: false,

      organizeForm: {
        builderZsList: [],
        builderZsListCopy: [],
        builderZsName: "",
        builderCsList: [],
        builderCsListCopy: [],
        builderCsName: "",
      },

      //主送
      activeName: "orgTree",
      proRedepolyOrgsData: [],
      proRedeployUserData: [],
      transferObjectDialogVisible: false,
      defaultProps: {
        children: "children",
        label: "name",
        isLeaf: "isLeaf",
      },
      proOrgTreeLoading: false,
      proUserTreeLoading: false,
      // woId: null,
      // workItemId: null,
      transferSubmitLoading: false,

      diaPeople: {
        visible: false,
        title: "",
        saveName: "",
        saveTitleMap: {
          transferObjectDetermine: "选择转派人",
          // ccDetermine: "选择抄送人",
        },
        showOrgsTree: true,
      },
    };
  },
  mounted() {
    console.log(this.common);
  },
  methods: {
    //附件选择
    attachmentBrowse() {
      this.attachmentDialogVisible = true;
    },
    changeFileData(data) {
      this.transferForm.attachmentFile = data.fileName;
      this.importForm.attachmentFileList = data.attachmentFileList;
      this.attachmentDialogVisible = false;
    },
    closeAttachmentDialog() {
      this.attachmentDialogVisible = false;
    },

    //树懒加载-人员
    loadUserNode(node, resolve) {
      let param = {
        level: node.level + 1,
        nodeId: node?.data?.id ?? "",
      };
      apiRedeployUserTree(param)
        .then(res => {
          if (res.status == 0) {
            let treeData = res?.data?.data ?? [];
            return resolve(treeData);
          }
        })
        .catch(e => {
          console.log("报错了，", e);
          return resolve([]);
        });
    },
    loadOrgNode(node, resolve) {
      let param = {
        level: node.level + 1,
        nodeId: node?.data?.id ?? "",
      };
      apiRedeployOrgTree(param)
        .then(res => {
          if (res.status == 0) {
            let treeData = res?.data?.data ?? [];
            return resolve(treeData);
          }
        })
        .catch(e => {
          console.log("报错了，", e);
          return resolve([]);
        });
    },

    // selectCc() {
    //   this.ccDialogVisible = true;
    // },
    //转派提交
    transferSubmit(formName) {
      // this.entering();
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.transferSubmitLoading = true;
          let param = {
            woId: this.common.woId,
            processInstId: this.common.processInstId,
            workItemId: this.common.workItemId,
            actionName: this.actionName,
            turnReason: this.transferForm.explain,
            agentManId: this.transferForm.transferObjectUserId, // 转派对象（用户） 必需
            agentMan: this.transferForm.agentMan, // 转派对象（用户） 必需
            agentDeptCode: this.transferForm.transferObjectOrgId, // 转派对象（组织） 必需
            agentDeptName: this.transferForm.agentDeptName, // 转派对象（组织）
          };

          // 如果有附件，先上传附件
          let isUpload = false;
          let formData = new FormData();
          if (
            this.transferForm.attachmentFile != "" &&
            this.importForm.attachmentFileList.length > 0
          ) {
            isUpload = true;
            for (let item of this.importForm.attachmentFileList) {
              formData.append("files", item.raw);
            }
          } else {
            isUpload = false;
            formData.append("files", "");
          }
          // 获取上传附件id
          if (isUpload) {
            apiGroupCode()
              .then(res => {
                if (res.status == 0) {
                  let processId = res.data?.linkId;

                  let uploadUrl = `/gt/attach/upload?groupKey=${this.common.woId}&processId=${processId}&processNode=${this.common.processNode}&sheetCreateTime=${this.common.sheetCreateTime}`;
                  apiFileUpload(uploadUrl, formData)
                    .then(res => {
                      if (res.code == 200) {
                        param.linkId = processId;
                        this.submit(param);
                        this.importForm.attachmentFileList = [];
                      } else {
                        this.$message.error("上传失败，请重新上传");
                        this.importForm.attachmentFileList = [];
                        return false;
                      }
                    })
                    .catch(error => {
                      console.log(error);
                      this.transferSubmitLoading = false;
                    });
                }
              })
              .catch(error => {
                console.log(error);
                this.transferSubmitLoading = false;
              });
          } else {
            this.submit(param);
          }
        } else {
          return false;
        }
      });
    },

    submit(param) {
      apiActionPublic(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("转派成功");
            this.$emit("closeDialogTurnToSend", "1");
          } else {
            this.$message.error("转派失败");
          }
          this.transferSubmitLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("转派失败");
          this.transferSubmitLoading = false;
        });
    },

    //转派取消
    transferCancel() {
      // console.log(this.transferForm);
      // console.log(this.organizeForm);
      this.$emit("closeDialogTurnToSend", "0");
    },

    onOpenPeopleDialog(diaSaveName) {
      this.isDiaOrgsUserTree = false;
      this.diaPeople.title = this.diaPeople.saveTitleMap[diaSaveName];
      this.diaPeople.saveName = diaSaveName;
      this.diaPeople.showOrgsTree = true;
      this.diaPeople.visible = true;
      this.$nextTick(() => {
        this.isDiaOrgsUserTree = true;
      });
    },
    onSavePeople(val) {
      this[this.diaPeople.saveName](val);
    },
    transferObjectDetermine({
      usersChecked,
      orgsChecked,
      selectionUser,
      contactSelectionUser,
      contactSelectionOrg,
    }) {
      var usersArrTmp = [];
      if (contactSelectionUser && contactSelectionUser.length > 0) {
        contactSelectionUser.map(item => {
          usersArrTmp.push({
            bz: "user",
            id: item.userName,
            name: item.trueName,
            orgName: item.orgEntity.orgName,
            mobilePhone: item.mobilePhone,
          });
        });
      }
      if (selectionUser && selectionUser.length > 0) {
        selectionUser.map(item => {
          let zs = usersArrTmp.findIndex(val => {
            return val.id === item.userName;
          });
          if (zs > -1) {
          } else {
            usersArrTmp.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      } else if (usersChecked && usersChecked.length > 0) {
        usersChecked.map(item => {
          let zs = usersArrTmp.findIndex(val => {
            return val.id === item.id;
          });
          if (zs > -1) {
          } else {
            usersArrTmp.push({
              bz: "user",
              id: item.id,
              name: item.name,
              orgName: item.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }

      let usersCheckedName = usersArrTmp.map(item => {
        return item.name;
      });
      this.transferForm.agentMan = usersCheckedName.join(",");
      let usersCheckedId = usersArrTmp.map(item => {
        return item.id;
      });
      this.transferForm.transferObjectUserId = usersCheckedId.join(",");
      this.transferForm.agentManDetail = usersArrTmp
        .map(item => {
          return item.name + "-" + item.orgName + "-" + item.mobilePhone;
        })
        .join(",");

      var orgArrTmp = [];
      if (contactSelectionOrg && contactSelectionOrg.length > 0) {
        contactSelectionOrg.map(item => {
          orgArrTmp.push({
            bz: "org",
            id: item.orgId + "",
            orgName: item.fullOrgName,
          });
        });
      }
      if (orgsChecked && orgsChecked.length > 0) {
        orgsChecked.map(item => {
          let transferOrg = orgArrTmp.findIndex(val => {
            return val.id === item.id;
          });
          if (transferOrg > -1) {
          } else {
            orgArrTmp.push({
              bz: "org",
              id: item.id,
              orgName: item.name,
            });
          }
        });
      }

      let orgsCheckedName = orgArrTmp.map(item => {
        return item.orgName;
      });
      this.transferForm.agentDeptName = orgsCheckedName.join(",");

      this.transferForm.transferObject = this.stitchingAlgorithmNew(
        this.transferForm.agentDeptName,
        this.transferForm.agentManDetail
      );

      let orgsCheckedId = orgArrTmp.map(item => {
        return item.id;
      });
      this.transferForm.transferObjectOrgId = orgsCheckedId.join(",");

      // let orgsCheckedName = orgsChecked.map(item => {
      //   return item.name;
      // });
      // this.transferForm.agentDeptName = orgsCheckedName.join(",");
      // let orgsCheckedId = orgsChecked.map(item => {
      //   return item.id;
      // });
      // this.transferForm.transferObjectOrgId = orgsCheckedId.join(",");
    },

    stitchingAlgorithmNew(orgName, userName) {
      if (orgName.length !== 0 && userName.length !== 0) {
        return orgName + "," + userName;
      } else {
        if (orgName.length !== 0) {
          return orgName;
        } else if (userName.length !== 0) {
          return userName;
        } else {
          return "";
        }
      }
    },
    //多选移除
    toggleSelection(val) {
      if (this.multipleSelection == 0) {
        this.$message({
          type: "warning",
          message: "还没有选择移除的选项，请先选择要移除的选项！",
        });
        return;
      } else {
        this.multipleSelection.forEach(row => {
          this.handleClose(val, row);
        });
      }
      this.multipleSelection;
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    //移除对象
    handleClose(val, tag) {
      if (val == "builderZs") {
        this.organizeForm.builderZsList.splice(
          this.arrayIndex(this.organizeForm.builderZsList, tag),
          1
        );
        this.organizeForm.builderZsListCopy.splice(
          this.arrayIndex(this.organizeForm.builderZsListCopy, tag),
          1
        );
      }
      if (val == "builderCs") {
        this.organizeForm.builderCsList.splice(
          this.arrayIndex(this.organizeForm.builderCsList, tag),
          1
        );
        this.organizeForm.builderCsListCopy.splice(
          this.arrayIndex(this.organizeForm.builderCsListCopy, tag),
          1
        );
      }
    },
    //找对应对象的索引值
    arrayIndex(val1, val2) {
      for (var i = 0, len = val1.length; i < len; i++) {
        var tag = val1[i];
        if (tag.id == val2.id) {
          return i;
        }
      }
    },
    //搜索
    search(val) {
      if (val == "builderZs" && this.organizeForm.builderZsList != null) {
        this.organizeForm.builderZsListCopy = [];
        this.organizeForm.builderZsList.forEach(row => {
          if (
            row.name == this.organizeForm.builderZsName ||
            row.orgName == this.organizeForm.builderZsName
          ) {
            this.organizeForm.builderZsListCopy.push(row);
          }
        });
      }
      if (val == "builderCs" && this.organizeForm.builderCsList != null) {
        this.organizeForm.builderCsListCopy = [];
        this.organizeForm.builderCsList.forEach(row => {
          if (
            row.name == this.organizeForm.builderCsName ||
            row.orgName == this.organizeForm.builderCsName
          ) {
            this.organizeForm.builderCsListCopy.push(row);
          }
        });
      }
    },
    //清除搜索项
    clear(val) {
      if (val == "builderZs") {
        this.organizeForm.builderZsName = "";
        this.organizeForm.builderZsListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.builderZsList)
        );
      }
      if (val == "builderCs") {
        this.organizeForm.builderCsName = "";
        this.organizeForm.builderCsListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.builderCsList)
        );
      }
    },
    entering() {
      //先清空
      this.transferForm.agentMan = "";
      this.transferForm.transferObjectUserId = "";
      this.transferForm.agentManDetail = "";
      this.transferForm.agentDeptName = "";
      this.transferForm.transferObjectOrgId = "";

      if (
        this.organizeForm.builderZsList &&
        this.organizeForm.builderZsList.length > 0
      ) {
        let userList = this.organizeForm.builderZsList.filter((item, index) => {
          return item.bz == "user";
        });
        let orgList = this.organizeForm.builderZsList.filter((item, index) => {
          return item.bz == "org";
        });
        if (userList && userList.length > 0) {
          let usersCheckedName = userList.map(item => {
            return item.name;
          });
          this.transferForm.agentMan = usersCheckedName.join(",");
          let usersCheckedId = userList.map(item => {
            return item.id;
          });
          this.transferForm.transferObjectUserId = usersCheckedId.join(",");
          const userDetailName = userList.map(item => {
            return item.name + "-" + item.orgName + "-" + item.mobilePhone;
          });
          this.transferForm.agentManDetail = userDetailName.join(",");
        }

        if (orgList && orgList.length > 0) {
          let orgsCheckedName = orgList.map(item => {
            return item.orgName;
          });
          this.transferForm.agentDeptName = orgsCheckedName.join(",");
          let orgsCheckedId = orgList.map(item => {
            return item.id;
          });
          this.transferForm.transferObjectOrgId = orgsCheckedId.join(",");
        }
      }
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-tree {
  padding-top: 15px;
  padding-left: 10px;
  // 不可全选样式
  .el-tree-node {
    .is-leaf + .el-checkbox .el-checkbox__inner {
      display: inline-block;
    }
    .el-checkbox .el-checkbox__inner {
      display: none;
    }
  }
}
</style>
