<template>
  <div class="qualitative">
    <el-form
      ref="qualitativeForm"
      :inline="false"
      class="demo-form-inline"
      :model="qualitativeForm"
      label-width="120px"
      :rules="qualitativeFormRule"
    >
      <el-card
        shadow="never"
        class="cus-card"
      >
        <el-row type="flex" style="flex-wrap: wrap" :gutter="36">

          <el-col :span="12">
            <el-form-item label="故障处理人:" prop="recoveryPerson">
              <el-input
                v-model="qualitativeForm.recoveryPerson"
                placeholder="故障处理人"
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系方式:" prop="recoveryPhone">
              <el-input
                v-model="qualitativeForm.recoveryPhone"
                placeholder="联系方式"
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="故障分类:"
              prop="faultCate"
              :rules="{ required: true, message: '请选择故障分类', trigger: 'blur' }"
            >
              <el-select
                v-model="qualitativeForm.faultCate"
                placeholder="请选择内容"
                style="width: 100%"
                @focus="
                    getDictData(
                      'gt_faultCate',
                      'faultCateList'
                    )
                  "
                @change="faultCateChange"
              >
                <el-option
                  v-for="(item, i) in faultCateList"
                  :key="i"
                  :label="item.dictName"
                  :value="{ value: item.dictCode, label: item.dictName }"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="qualitativeForm.faultCate">
            <el-form-item
              label="故障原因:"
              prop="faultReason"
              :rules="{ required: true, message: '请选择故障原因', trigger: 'blur' }"
            >
              <el-select
                v-model="qualitativeForm.faultReason"
                placeholder="请选择内容"
                style="width: 100%"
              >
                <el-option
                  v-for="(item, i) in faultReasonList"
                  :key="i"
                  :label="item.dictName"
                  :value="item.dictName"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-else></el-col>
          <el-col :span="12">
            <el-form-item
              label="是否影响业务:"
              prop="isEffectBusiness"
            >
              <el-radio-group v-model="qualitativeForm.isEffectBusiness">
                <el-radio label="否">否</el-radio>
                <el-radio label="是">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="是否基站退服:"
              prop="isSiteOffline"
              :rules="{ required: true, message: '请选择是否基站退服', trigger: 'blur' }"
            >
              <el-radio-group v-model="qualitativeForm.isSiteOffline">
                <el-radio label="否">否</el-radio>
                <el-radio label="是">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="故障结束时间:"
              prop="businessRecoveryTime"
              :rules="{ required: true, message: '请选择故障结束时间', trigger: 'blur' }"
            >
              <el-date-picker
                v-model="qualitativeForm.businessRecoveryTime"
                type="datetime"
                placeholder="请选择时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
                style="width: 100%"
                :picker-options="recoveryPicker"
              />
            </el-form-item>
          </el-col>
<!--          <el-col :span="12">-->
<!--            <el-form-item-->
<!--              label="业务恢复时间:"-->
<!--              prop="faultRecoveryTime"-->
<!--              :rules="{ required: true, message: '请选择业务恢复时间', trigger: 'blur' }"-->
<!--            >-->
<!--              <el-date-picker-->
<!--                v-model="qualitativeForm.faultRecoveryTime"-->
<!--                type="datetime"-->
<!--                placeholder="请选择时间"-->
<!--                value-format="yyyy-MM-dd HH:mm:ss"-->
<!--                clearable-->
<!--                style="width: 100%"-->
<!--                :picker-options="recoveryPicker"-->
<!--                :default-time="recoveryDefaultTime"-->
<!--              />-->
<!--            </el-form-item>-->
<!--          </el-col>-->
          <el-col :span="12">
            <el-form-item
              label="要求反馈时间:"
              prop="needFeedbackTime" required>
              {{qualitativeForm.needFeedbackTime}}
<!--              <el-input-->
<!--                v-model="qualitativeForm.needFeedbackTime"-->
<!--                placeholder=""-->
<!--                style="width: 100%"-->
<!--                disabled-->
<!--              ></el-input>-->
            </el-form-item>
          </el-col>
<!--          <el-col :span="12">-->
<!--            <el-form-item-->
<!--              label="实际完成时间:"-->
<!--              prop="realFinishTime"-->
<!--              :rules="{ required: true, message: '请选择实际完成时间', trigger: 'blur' }"-->
<!--            >-->
<!--              <el-date-picker-->
<!--                v-model="qualitativeForm.realFinishTime"-->
<!--                type="datetime"-->
<!--                placeholder="请选择时间"-->
<!--                value-format="yyyy-MM-dd HH:mm:ss"-->
<!--                clearable-->
<!--                style="width: 100%"-->
<!--                :picker-options="recoveryPicker"-->
<!--                :default-time="recoveryDefaultTime"-->
<!--              />-->
<!--            </el-form-item>-->
<!--          </el-col>-->
          <el-col :span="24">
            <el-form-item
              label="故障原因描述:"
              prop="falutReasonDesc"
            >
              <el-input
                type="textarea"
                :rows="3"
                placeholder="请输入内容"
                v-model="qualitativeForm.falutReasonDesc"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>

        </el-row>
      </el-card>

    </el-form>
    <div style="padding: 10px 20px; text-align: center">
      <el-button
        type="primary"
        @click="handleSubmit('qualitativeForm')"
        v-loading.fullscreen.lock="qualitativeFullscreenLoading"
        >提 交</el-button
      >
      <el-button @click="onReset">重 置</el-button>
    </div>

  </div>
</template>
<script>
import moment, { now } from "moment";
import { mapGetters } from "vuex";
import {
  apiGetMajorAlarm,
  apiActionPublic,
  getCurrentTime,
  apiInitQualitative,
  apiDict,
  apiGroupCode,
  apiFileUpload,
} from "../api/CommonApi";

import { apiGetOrgInfo } from "../../../workOrder/api/CommonApi";
import { apiDeleteFdFile } from "../../../workOrder/workOrderWaitDetail/api/CommonApi";
import FileUpload from "./FileUpload.vue";
export default {
  name: "Qualitative",
  props: {
    common: Object,
    // workItemId: [String, Number],
    // isUploadReport: [String, Number],
    qData: Object,
    actionName: String,
  },
  components: { FileUpload },

  computed: {
    ...mapGetters(["userInfo"]),
  },
  data() {
    var validLastClearTime = (rule, value, callback) => {
      if (
        this.qualitativeForm.lastClearTime &&
        this.qualitativeForm.faultEndTime
      ) {
        let seconds = moment(
          this.qualitativeForm.faultEndTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.qualitativeForm.lastClearTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );

        let seconds2 = moment(
          this.qualitativeForm.lastClearTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.common.failureInformTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        if (seconds < 0 || seconds2 <= 0) {
          callback(
            new Error(
              "故障结束时间>=故障代通时间>故障通知时间，请重新检查后选择正确时间"
            )
          );
        } else {
          callback();
        }
      } else if (this.qualitativeForm.lastClearTime) {
        let seconds3 = moment(
          this.qualitativeForm.lastClearTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(moment(new Date(), "YYYY-MM-DD HH:mm:ss"), "seconds");
        let seconds4 = moment(
          this.qualitativeForm.lastClearTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.common.failureInformTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        if (seconds3 > 0 || seconds4 <= 0) {
          callback(
            new Error(
              "当前时间>=故障代通时间>故障通知时间，请重新检查后选择正确时间"
            )
          );
        }
      } else {
        callback();
      }
    };
    var validFaultEndTime = (rule, value, callback) => {
      if (value === "" || value === null) {
        callback(new Error("请选择故障结束时间"));
      } else {
        if (this.qualitativeForm.lastClearTime) {
          let clSeconds = moment(
            this.qualitativeForm.faultEndTime,
            "YYYY-MM-DD HH:mm:ss"
          ).diff(
            moment(this.qualitativeForm.lastClearTime, "YYYY-MM-DD HH:mm:ss"),
            "seconds"
          );
          if (clSeconds < 0) {
            callback(
              new Error(
                "当前时间>=故障结束时间>=故障代通时间，请重新检查后选择正确时间"
              )
            );
          }
        }

        let seconds = moment(
          this.qualitativeForm.faultEndTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.common.failureInformTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );

        let seconds2 = moment(new Date(), "YYYY-MM-DD HH:mm:ss").diff(
          moment(this.qualitativeForm.faultEndTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        if (seconds <= 0 || seconds2 < 0) {
          callback(
            new Error(
              "当前时间>=故障结束时间>故障通知时间，请重新检查后选择正确时间"
            )
          );
        } else {
          callback();
        }
      }
    };

    return {
      qualitativeForm: {
        recoveryPerson: "",
        recoveryPhone: "",
        faultCate: "",
        faultCateId: "",
        faultReason: "",
        isEffectBusiness: "否",
        isSiteOffline: "否",
        businessRecoveryTime: "",
        faultRecoveryTime: "",
        needFeedbackTime: "",
        realFinishTime: "",
        falutReasonDesc: "",
      },
      userData: null,
      recoveryPicker: {},
      // initData: {}, // 初始化数据
      formInit: {},
      // professionalTypeId:'',//------

      qualitativeFullscreenLoading: false,
      startTimeRange: "",
      startTimeRangeByFaultEndTime: "",
      recoveryDefaultTime: "",
      // 下拉框
      faultCateList: [], //故障分类
      faultReasonList: [], //故障原因


      qualitativeFormRule: {
        // faultCate: [
        //   { required: true, message: "请选择故障分类", trigger: "blur" },
        // ],
        // faultReason: [
        //   { required: true, message: "请选择故障原因", trigger: "blur" },
        // ],
        // isSiteOffline: [
        //   { required: true, message: "请选择是否基站退服", trigger: "blur" },
        // ],
        // businessRecoveryTime: [
        //   {
        //     required: true,
        //     message: "请选择故障结束时间",
        //     trigger: "blur",
        //   },
        // ],
        // faultRecoveryTime: [
        //   {
        //     required: true,
        //     message: "请选择业务恢复时间",
        //     trigger: "blur",
        //   },
        // ],
        // realFinishTime: [
        //   {
        //     required: true,
        //     message: "请选择实际完成时间",
        //     trigger: "blur",
        //   },
        // ],
        // lastClearTime: [{ validator: validLastClearTime, trigger: "blur" }],
        // faultEndTime: [{ validator: validFaultEndTime, required: true }],
      },
    };
  },
  mounted() {
    // console.log(this.qData);
    console.log(this.common);
    this.getMajorAlarm();

    this.qualitativeForm.recoveryPerson = this.userInfo.realName;
    this.qualitativeForm.recoveryPhone = JSON.parse(this.userInfo.attr2).mobilePhone;
    this.qualitativeForm.needFeedbackTime = this.qData.needCompletionTime;
  },

  // watch: {
  //   "qualitativeForm.faultRecoveryTime": {
  //     immediate: false,
  //     handler: function (newVal, oldVal) {
  //       if (
  //         (newVal != null || newVal != "") &&
  //         oldVal != undefined &&
  //         newVal != oldVal
  //       ) {
  //         this.computerFaultTreatmentTime();
  //         const time1 = new Date(
  //           this.qualitativeForm.alarmCreateTime.slice(0, 10) + " 00:00:00"
  //         ).getTime();
  //         const time2 = new Date(newVal.slice(0, 10) + " 00:00:00").getTime();
  //         console.log(getCurrentTime(Date.now()).slice(0, 10) + " 00:00:00");
  //         const time3 = new Date(
  //           getCurrentTime(Date.now()).slice(0, 10) + " 00:00:00"
  //         ).getTime();
  //         const nowTimeStr = getCurrentTime(Date.now()).slice(-8);
  //         if (time2 > time1) {
  //           this.recoveryPicker.selectableRange = `"00:00:00" - "23:59:59"`;
  //         } else {
  //           this.recoveryPicker.selectableRange = `${this.recoveryDefaultTime} - "23:59:59"`;
  //         }
  //         if (time2 == time3) {
  //           this.recoveryPicker.selectableRange = `"00:00:00" - ${nowTimeStr}`;
  //         }
  //       }
  //     },
  //   },
  // },
  async created() {
    this.formInit = JSON.parse(JSON.stringify(this.qualitativeForm));
    this.userData = JSON.parse(this.userInfo.attr2);
    // 获取地区，请求参数
    // this.getOrgInfo();
  },
  methods: {
    // dealDisabledDate(time) {
    //   let self = this;
    //   let beginDate = moment(self.common.failureInformTime).format(
    //     "YYYY-MM-DD"
    //   );
    //   let endDate = moment(Date.now()).format("YYYY-MM-DD");
    //   let arr = this.getAllDays(beginDate, endDate);
    //   const timeFormat = moment(time).format("YYYY-MM-DD");
    //   if (arr.indexOf(timeFormat) >= 0) {
    //     return false;
    //   }
    //   return true;
    // },
    // dealDisabledDateDt(time) {
    //   let self = this;
    //   let beginDate;
    //   let endDate;
    //   if (self.qualitativeForm.faultEndTime) {
    //     beginDate = moment(self.common.failureInformTime).format("YYYY-MM-DD");
    //     endDate = moment(self.qualitativeForm.faultEndTime).format(
    //       "YYYY-MM-DD"
    //     );
    //   } else {
    //     beginDate = moment(self.common.failureInformTime).format("YYYY-MM-DD");
    //     endDate = moment(Date.now()).format("YYYY-MM-DD");
    //   }
    //   let arr = this.getAllDays(beginDate, endDate);
    //   const timeFormat = moment(time).format("YYYY-MM-DD");
    //   if (arr.indexOf(timeFormat) >= 0) {
    //     return false;
    //   }
    //   return true;
    // },
    // getAllDays(begin_date, end_date) {
    //   const errArr = [],
    //     resultArr = [],
    //     dateReg = /^[2]\d{3}-[01]\d-[0123]\d$/;
    //
    //   if (
    //     typeof begin_date !== "string" ||
    //     begin_date === "" ||
    //     !dateReg.test(begin_date)
    //   ) {
    //     return errArr;
    //   }
    //
    //   if (
    //     typeof end_date !== "string" ||
    //     end_date === "" ||
    //     !dateReg.test(end_date)
    //   ) {
    //     return errArr;
    //   }
    //
    //   try {
    //     const beginTimestamp = Date.parse(new Date(begin_date)),
    //       endTimestamp = Date.parse(new Date(end_date));
    //
    //     // 开始日期小于结束日期
    //     if (beginTimestamp > endTimestamp) {
    //       return errArr;
    //     }
    //
    //     // 开始日期等于结束日期
    //     if (beginTimestamp === endTimestamp) {
    //       resultArr.push(begin_date);
    //       return resultArr;
    //     }
    //
    //     let tempTimestamp = beginTimestamp,
    //       tempDate = begin_date;
    //
    //     // 新增日期是否和结束日期相等， 相等跳出循环
    //     while (tempTimestamp !== endTimestamp) {
    //       resultArr.push(tempDate);
    //
    //       // 增加一天
    //       tempDate = moment(tempTimestamp).add(1, "d").format("YYYY-MM-DD");
    //
    //       // 将增加时间变为时间戳
    //       tempTimestamp = Date.parse(new Date(tempDate));
    //     }
    //
    //     // 将最后一天放入数组
    //     resultArr.push(end_date);
    //     return resultArr;
    //   } catch (err) {
    //     return errArr;
    //   }
    // },


    // getOrgInfo() {
    //   apiGetOrgInfo()
    //     .then(res => {
    //       if (res.status == "0") {
    //         this.qualitativeForm.areaCode = res?.data?.orgInfo?.areaCode ?? "";
    //         this.qualitativeForm.category = res?.data?.category ?? "";
    //         this.getFaultAreaOptions();
    //       }
    //     })
    //     .catch(error => {
    //       console.log(error);
    //     });
    // },

    // getFaultAreaOptions() {
    //   let param = {
    //     areaCode: this.qualitativeForm.areaCode,
    //     category: this.qualitativeForm.category,
    //   };
    //   apiGetFaultArea(param)
    //     .then(res => {
    //       if (res.status == "0") {
    //         this.faultRegionOptions = res?.data ?? [];
    //       }
    //     })
    //     .catch(error => {
    //       console.log(error);
    //     });
    // },

    // computerFaultGenerationAter() {
    //   let days = moment(
    //     this.qualitativeForm.lastClearTime,
    //     "YYYY-MM-DD HH:mm:ss"
    //   ).diff(
    //     moment(this.qualitativeForm.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
    //     "seconds"
    //   );
    //   this.qualitativeForm.lastClearDuration = days;
    // },

    // second2Time(days) {
    //   return this.showTimeNew(Math.abs(days));
    // },

    // computerFaultTreatmentTime() {
    //   let dealTime = moment(
    //     this.qualitativeForm.faultRecoveryTime,
    //     "YYYY-MM-DD HH:mm:ss"
    //   ).diff(
    //     moment(this.qualitativeForm.faultNoticeTime, "YYYY-MM-DD HH:mm:ss"),
    //     "seconds"
    //   );
    //
    //   let recoveryTime = moment(
    //     this.qualitativeForm.faultRecoveryTime,
    //     "YYYY-MM-DD HH:mm:ss"
    //   ).diff(
    //     moment(this.qualitativeForm.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
    //     "seconds"
    //   );
    //   this.qualitativeForm.faultDuration = this.second2Time(dealTime);
    //   this.qualitativeForm.recoveryDuration = this.second2Time(recoveryTime);
    // },

    //获取主告警
    getMajorAlarm() {
      let params = {
        woId:this.qData.woId
      }
      apiGetMajorAlarm(params)
        .then(res => {
          if (res.status == "0") {
            console.log(res.data.alarmClearTime);
            this.qualitativeForm.businessRecoveryTime = res.data.alarmClearTime;
          } else {
            this.$message.error(res.msg);
          }
        })
        .catch(error => {
          console.log(error);
          this.$message.error(error.msg);
        });
    },

    handleSubmit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.qualitativeFullscreenLoading = true;
          // this.$set(this.qualitativeForm, "actionName", "反馈");

          let paramter = {
            woId: this.common.woId,
            processInstId: this.common.processInstId,
            workItemId: this.common.workItemId,
            actionName: this.actionName,
          };

          let params = {};
          params = Object.assign(paramter, this.qualitativeForm);

          this.submitFn(params);
        } else {
          return false;
        }
      });
    },
    submitFn(params) {
      apiActionPublic(params)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("提交返单成功");
            this.onReset();
            this.$emit("closeDialogQualitative", res.data);
          } else {
            this.$message.error(res.msg);
          }
          this.qualitativeFullscreenLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error(error.msg);
          this.qualitativeFullscreenLoading = false;
        });
    },


    // dealTimeout() {
    //   //故障代通时间（无故障代通时间 用故障结束时间） - 故障开始时间
    //   if (this.qualitativeForm.lastClearTime) {
    //     let seconds = moment(
    //       this.qualitativeForm.lastClearTime,
    //       "YYYY-MM-DD HH:mm:ss"
    //     ).diff(
    //       moment(this.qualitativeForm.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
    //       "seconds"
    //     );
    //     return seconds;
    //   } else {
    //     let seconds = moment(
    //       this.qualitativeForm.faultEndTime,
    //       "YYYY-MM-DD HH:mm:ss"
    //     ).diff(
    //       moment(this.qualitativeForm.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
    //       "seconds"
    //     );
    //     return seconds;
    //   }
    // },
    // showTimeNew(val) {
    //   if (val) {
    //     if (val == 0) return "0秒";
    //     var time = "";
    //     var second = val % 60;
    //     var minute = parseInt(parseInt(val) / 60) % 60;
    //     time = second + "秒";
    //     if (minute >= 1) {
    //       time = minute + "分" + second + "秒";
    //     }
    //     var hour = parseInt(parseInt(val / 60) / 60) % 24;
    //     if (hour >= 1) {
    //       time = hour + "小时" + minute + "分" + second + "秒";
    //     }
    //     var day = parseInt(parseInt(parseInt(val / 60) / 60) / 24);
    //     if (day >= 1) {
    //       time = day + "天" + hour + "小时" + minute + "分" + second + "秒";
    //     }
    //     return time;
    //   } else {
    //     return "0秒";
    //   }
    // },

    // 设置恢复时间，组件显示
    setRecoveryPicker() {
      // 故障恢复时间 >= 故障发生时间 且 故障恢复时间 <= 当前时间
      const alarmCreateTime = this.qualitativeForm.alarmCreateTime;
      this.recoveryDefaultTime = getCurrentTime(
        new Date(alarmCreateTime).getTime() + 60 * 1000
      ).slice(-8);
      this.recoveryPicker = {
        disabledDate: time => {
          return (
            time.getTime() <
              new Date(alarmCreateTime).getTime() - 3600 * 1000 * 24 ||
            time.getTime() > new Date(Date.now()).getTime()
          );
        },
        selectableRange: `${this.recoveryDefaultTime} - "23:59:59"`,
      };
    },

    //重置
    onReset() {
      // this.qualitativeForm = JSON.parse(JSON.stringify(this.formInit));
      this.qualitativeForm =  {
        recoveryPerson: "",
        recoveryPhone: "",
        faultCate: "",
        faultCateId: "",
        faultReason: "",
        isEffectBusiness: "否",
        isSiteOffline: "否",
        businessRecoveryTime: "",
        faultRecoveryTime: "",
        needFeedbackTime: this.qData.needCompletionTime,
        realFinishTime: "",
        falutReasonDesc: "",
      },
        this.$nextTick(() => {
          this.$refs.qualitativeForm.clearValidate();
        });
    },

    //获取字典值
    getDictData(dictId, selectName) {
      this[selectName] = [];
      let param = {
        dictType: dictId,
      };

      apiDict(param)
        .then(res => {
          if (res.code == 200) {
            this[selectName] = res.data;
          }
        })
        .catch(error => {
          console.log(error);
          return false;
        });
    },

    //选择完故障分类
    faultCateChange(val) {
      const { value, label } = val;
      this.qualitativeForm.faultCate = label;
      this.qualitativeForm.faultCateId = value;
      this.faultReasonList = [];
      // 获取故障原因，下拉列表
      this.getDictData(
        "gt_" +
        this.qualitativeForm.faultCateId +
        "_faultReason",
        "faultReasonList"
      );
    },
  },
};
</script>
<style lang="scss" scoped>
.qualitative {
  /*height: 500px;*/

  /*.el-dialog__body{*/
  /*  padding: 20px !important;*/
  /*}*/
  .cus-card {
    padding: 14px;
    ::v-deep .el-card__header {
      /*padding: 20px;*/
      /*font-weight: 400;*/
      @include themify {
        background-color: themed("$--background-color-base");
      }
    }
  }
}
</style>
