<template>
  <div class="back-single-internation">
    <el-form
      ref="backSingleForm"
      :inline="false"
      class="demo-form-inline"
      :model="backSingleForm"
      label-width="130px"
      :rules="backSingleFormRule"
    >
      <el-card
        shadow="never"
        header="故障定性信息"
        :body-style="{ padding: '20px 8px' }"
        class="cus-card"
      >
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item
              label="故障所属专业:"
              prop="professionalType"
              :rules="{
                required: true,
                message: '请选择故障所属专业',
              }"
            >
              <dict-select
                :value.sync="backSingleForm.professionalType"
                :dictId="10002"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障发生时间:" prop="alarmCreateTime" required>
              {{ backSingleForm.alarmCreateTime }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障通知时间:" prop="sheetCreateTime" required>
              {{ backSingleForm.sheetCreateTime }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="故障得知时间:"
              prop="faultKnowTime"
              :rules="{
                required: true,
                message: '请选择内容',
              }"
            >
              <el-date-picker
                v-model="backSingleForm.faultKnowTime"
                type="datetime"
                placeholder="请选择时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障代通时间:" prop="lastClearTime">
              <el-date-picker
                v-model="backSingleForm.lastClearTime"
                type="datetime"
                placeholder="请选择时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
                style="width: 100%"
                @change="computerFaultGenerationAter"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障代通历时:">
              {{ second2Time(backSingleForm.lastClearDuration) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障结束时间:" prop="faultEndTime">
              <el-date-picker
                v-model="backSingleForm.faultEndTime"
                type="datetime"
                placeholder="请选择时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
                style="width: 100%"
                @change="computerFaultTreatmentTime"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障处理历时:" required>
              {{ second2Time(this.backSingleForm.faultDuration) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="故障发生国家:"
              prop="faultHappenCountry"
              :rules="{
                required: true,
                message: '请输入内容',
              }"
            >
              <el-input
                v-model="backSingleForm.faultHappenCountry"
                placeholder="请输入内容"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障处理部门:" prop="dept" required>
              {{ backSingleForm.dept }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="处理人:" prop="person" required>
              {{ backSingleForm.person }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="挂起历时:" required>
              {{ second2Time(this.backSingleForm.suspendDuration) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障处理净历时:" required>
              {{ second2Time(this.backSingleForm.processDuration) }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item
              label="是否影响业务:"
              prop="isEffectBusiness"
              :rules="{
                required: true,
                message: '请选择内容',
              }"
            >
              <el-radio-group
                v-model="backSingleForm.isEffectBusiness"
                style="width: 100%"
              >
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item
              label="影响范围:"
              v-if="backSingleForm.isEffectBusiness == '1'"
              prop="effectRange"
              :rules="[
                {
                  required:
                    backSingleForm.isEffectBusiness == '1' ? true : false,
                  message: '请填写影响范围',
                  trigger: 'blur',
                },
              ]"
            >
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="backSingleForm.effectRange"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card
        shadow="never"
        header="故障专业信息"
        :body-style="{ padding: '20px' }"
        style="margin-top: 20px"
        class="cus-card"
      >
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item
              label="故障状态:"
              prop="faultStatus"
              :rules="{
                required: true,
                message: '请选择故障状态',
              }"
            >
              <dict-select
                :value.sync="backSingleForm.faultStatus"
                :dictId="10017"
                style="width: 100%"
                placeholder="请选择内容"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="网络类型:"
              prop="networkType"
              :rules="{
                required: true,
                message: '请选择网络类型',
              }"
            >
              <dict-select
                :value.sync="backSingleForm.networkType"
                :dictId="10018"
                style="width: 100%"
                placeholder="请选择内容"
                :notSelect="true"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="故障分类:"
              prop="faultCate"
              :rules="{
                required: true,
                message: '请选择故障分类',
              }"
            >
              <dict-select
                :value.sync="backSingleForm.faultCate"
                :dictId="10055"
                style="width: 100%"
                @change="faultCateChange"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row
          type="flex"
          style="flex-wrap: wrap"
          :gutter="20"
          v-if="backSingleForm.faultCate == '1'"
          key="faultReasonCate"
        >
          <el-col :span="8">
            <el-form-item
              label="故障原因分类:"
              :rules="{
                required: true,
                message: '请输入内容',
              }"
              prop="faultReasonCate"
            >
              <el-input
                v-model="backSingleForm.faultReasonCate"
                style="width: 100%"
                placeholder="请输入内容"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="海陆缆名称:"
              prop="seaLandFiberName"
              :rules="{
                required: true,
                message: '请输入内容',
              }"
            >
              <el-input
                v-model="backSingleForm.seaLandFiberName"
                placeholder="请输入内容"
                style="width: 100%"
              >
                <el-button
                  style="
                    background: #b50b14;
                    border-color: #b50b14;
                    color: #fff;
                    padding: 9px 20px;
                    top: -0.8px;
                    position: relative;
                  "
                  slot="append"
                  @click="selectSeaLandFiberName"
                >
                  选择
                </el-button>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="海陆缆段:"
              prop="seaLandFiberSeg"
              :rules="{
                required: true,
                message: '请输入内容',
              }"
            >
              <el-input
                v-model="backSingleForm.seaLandFiberSeg"
                style="width: 100%"
                placeholder="请输入内容"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="运营商名称:"
              prop="operatorName"
              :rules="{
                required: true,
                message: '请输入内容',
              }"
            >
              <el-input
                v-model="backSingleForm.operatorName"
                style="width: 100%"
                placeholder="请输入内容"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="海陆缆类型:"
              prop="seaLandFiberType"
              :rules="{
                required: true,
                message: '请选择内容',
              }"
            >
              <dict-select
                :value.sync="backSingleForm.seaLandFiberType"
                :dictId="10056"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="海陆缆故障区域:">
              <el-input
                v-model="backSingleForm.seaLandFiberFaultArea"
                style="width: 100%"
                placeholder="请输入内容"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否超时:">
              {{ backSingleForm.isOverTimeShow }}
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item
              label="故障原因描述:"
              prop="falutReasonDesc"
              :rules="{
                required: backSingleForm.faultCate == '1' ? true : false,
                message: '请填写故障原因描述',
              }"
            >
              <el-input
                type="textarea"
                :rows="2"
                v-model="backSingleForm.falutReasonDesc"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row
          type="flex"
          style="flex-wrap: wrap"
          :gutter="20"
          v-if="backSingleForm.faultCate == '2'"
          key="faultCateTwo"
        >
          <el-col :span="8">
            <el-form-item
              label="故障原因分类:"
              prop="faultReasonCate"
              :rules="{
                required: true,
                message: '请输入内容',
              }"
            >
              <el-input
                v-model="backSingleForm.faultReasonCate"
                style="width: 100%"
                placeholder="请输入内容"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="故障国家:"
              prop="faultCountry"
              :rules="{
                required: true,
                message: '请输入内容',
              }"
            >
              <el-input
                placeholder="请输入内容"
                v-model="backSingleForm.faultCountry"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="运营商名称:"
              :rules="{
                required: backSingleForm.faultCate == '2' ? true : false,
                message: '请输入内容',
              }"
              prop="operatorName"
            >
              <el-input
                placeholder="请输入内容"
                v-model="backSingleForm.operatorName"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否超时:">
              {{ backSingleForm.isOverTimeShow }}
            </el-form-item></el-col
          >
          <el-col :span="24">
            <el-form-item
              label="故障原因描述:"
              prop="falutReasonDesc"
              :rules="{
                required: true,
                message: '请填写故障原因描述',
              }"
            >
              <el-input
                type="textarea"
                :rows="2"
                v-model="backSingleForm.falutReasonDesc"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: center">
      <el-button
        type="primary"
        @click="handleSubmit('backSingleForm')"
        v-loading.fullscreen.lock="backSingleFullscreenLoading"
        >提 交</el-button
      >
      <!-- <el-button type="primary" @click="nextStepEvaluation()">下一步</el-button> -->
      <el-button @click="onReset">重 置</el-button>
    </div>

    <el-dialog
      width="620px"
      title="满意度评价"
      :visible.sync="evaluationDialogVisible"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      append-to-body
    >
      <el-form ref="evaluation" :model="evaluation">
        <el-form-item label="派单准确度:" label-width="90px">
          <el-rate v-model="evaluation.orderAccuracy" :colors="colors">
          </el-rate>
        </el-form-item>
        <el-form-item label="诊断准确度:" label-width="90px">
          <el-rate v-model="evaluation.diagnosticrAccuracy" :colors="colors">
          </el-rate>
        </el-form-item>
        <el-form-item
          v-if="
            evaluation.orderAccuracy <= 3 || evaluation.diagnosticrAccuracy <= 3
          "
          label="反馈问题:"
          label-width="90px"
          prop="feedbackProblemCheckList"
          :rules="[
            {
              required:
                evaluation.orderAccuracy > 3 &&
                evaluation.diagnosticrAccuracy > 3
                  ? false
                  : true,
              message: '请选择要反馈的问题',
              trigger: 'blur',
            },
          ]"
        >
          <el-checkbox-group
            v-model="evaluation.feedbackProblemCheckList"
            @change="feedbackChange"
          >
            <el-checkbox label="派单超时"></el-checkbox>
            <el-checkbox label="工单基础信息错误"></el-checkbox>
            <el-checkbox label="诊断信息不完整"></el-checkbox>
            <el-checkbox label="诊断信息错误"></el-checkbox>
            <el-checkbox label="其他"></el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item
          v-if="
            viewsOnContentShow &&
            (evaluation.orderAccuracy <= 3 ||
              evaluation.diagnosticrAccuracy <= 3)
          "
          prop="viewsOnContent"
        >
          <el-input
            type="textarea"
            :rows="3"
            placeholder="为了下次给您更好的服务，请留下宝贵意见，对您和我们都很重要"
            v-model="evaluation.viewsOnContent"
          >
          </el-input>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button
          type="primary"
          @click="handleSubmit('evaluation')"
          v-loading.fullscreen.lock="backSingleFullscreenLoading"
          >提 交</el-button
        >
        <el-button @click="evaluationDialogVisible = false">上一步</el-button>
      </span>
    </el-dialog>
    <el-dialog
      width="620px"
      title="海陆缆名称"
      :visible.sync="seaLandDialogVisible"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      append-to-body
      top="5vh"
    >
      <div>
        <el-checkbox-group v-model="seaLandCheckBox" size="small">
          <el-checkbox
            v-for="(item, index) in seaLandCheckBoxArr"
            :key="index"
            :label="item.dictName"
            border
            style="width: 140px; margin-top: 10px; margin-left: 0px"
          ></el-checkbox>
        </el-checkbox-group>
      </div>
      <span slot="footer">
        <el-button type="primary" @click="seaLandCheckBoxDetermine"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
<script>
import moment from "moment";
import { mapGetters } from "vuex";
import DictSelect from "../../../workOrder/components/DictSelect.vue";
import { apiBackSingleInternation } from "../api/CommonApi";
import { apiDict, apiGetOrgInfo } from "../../../workOrder/api/CommonApi";
export default {
  name: "BackSingleInternation",
  props: {
    common: Object,
    timing: Object,
  },
  components: { DictSelect },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  data() {
    var validLastClearTime = (rule, value, callback) => {
      if (
        this.backSingleForm.lastClearTime &&
        this.backSingleForm.faultEndTime
      ) {
        let seconds = moment(
          this.backSingleForm.faultEndTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.backSingleForm.lastClearTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );

        let seconds2 = moment(
          this.backSingleForm.lastClearTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.common.failureInformTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        if (seconds < 0 || seconds2 <= 0) {
          callback(
            new Error(
              "故障结束时间>=故障代通时间>故障通知时间，请重新检查后选择正确时间"
            )
          );
        } else {
          callback();
        }
      } else if (this.backSingleForm.lastClearTime) {
        let seconds3 = moment(
          this.backSingleForm.lastClearTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(moment(new Date(), "YYYY-MM-DD HH:mm:ss"), "seconds");
        let seconds4 = moment(
          this.backSingleForm.lastClearTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.common.failureInformTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        if (seconds3 > 0 || seconds4 <= 0) {
          callback(
            new Error(
              "当前时间>=故障代通时间>故障通知时间，请重新检查后选择正确时间"
            )
          );
        }
      } else {
        callback();
      }
    };
    var validFaultEndTime = (rule, value, callback) => {
      if (value === "" || value === null) {
        callback(new Error("请选择故障结束时间"));
      } else {
        if (this.backSingleForm.lastClearTime) {
          let clSeconds = moment(
            this.backSingleForm.faultEndTime,
            "YYYY-MM-DD HH:mm:ss"
          ).diff(
            moment(this.backSingleForm.lastClearTime, "YYYY-MM-DD HH:mm:ss"),
            "seconds"
          );
          if (clSeconds < 0) {
            callback(
              new Error(
                "当前时间>=故障结束时间>=故障代通时间，请重新检查后选择正确时间"
              )
            );
          }
        }
        let seconds = moment(
          this.backSingleForm.faultEndTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.common.failureInformTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );

        let seconds2 = moment(new Date(), "YYYY-MM-DD HH:mm:ss").diff(
          moment(this.backSingleForm.faultEndTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        if (seconds <= 0 || seconds2 < 0) {
          callback(
            new Error(
              "当前时间>=故障结束时间>故障通知时间，请重新检查后选择正确时间"
            )
          );
        } else {
          callback();
        }
      }
    };
    return {
      backSingleForm: {
        woId: null,
        workItemId: null,
        processInstId: null,
        processDefId: null,
        professionalType: null,
        alarmCreateTime: null,
        sheetCreateTime: null,
        lastClearTime: null,
        faultKnowTime: null, //故障得知时间
        lastClearDuration: 0, //故障代通历时
        faultEndTime: null,
        faultDuration: 0, //故障处理历时
        faultHappenCountry: null,
        dept: null,
        person: null,
        suspendDuration: 0, //挂起历时 单位秒
        processDuration: 0, //故障处理净历时 单位秒
        isEffectBusiness: null,
        effectRange: null,
        //海陆缆故障
        faultStatus: null,
        networkType: null,
        faultCate: null,
        faultReasonCate: null,
        seaLandFiberName: null,
        seaLandFiberSeg: null,
        operatorName: null,
        seaLandFiberType: null,
        seaLandFiberFaultArea: null,
        isOverTime: 0,
        isOverTimeShow: "否",
        falutReasonDesc:
          this.common.faultCauseDescription ||
          "样例：距xx机房xxkm处，光缆因xxx（原因）中断。 处理方式：xxxx （熔接或者倒带后）恢复。",
        //设备故障
        faultCountry: null,
        actionName: "",
      },
      backSingleFullscreenLoading: false,

      evaluationDialogVisible: false,
      evaluation: {
        orderAccuracy: null,
        diagnosticrAccuracy: null,
        viewsOnContent: null,
        feedbackProblemCheckList: [],
      },
      colors: ["#99A9BF", "#F7BA2A", "#FF9900"], // 等同于 { 2: '#99A9BF', 4: { value: '#F7BA2A', excluded: true }, 5: '#FF9900' }
      //当前人的部门
      userData: null,
      backSingleFormRule: {
        lastClearTime: [{ validator: validLastClearTime, trigger: "blur" }],
        faultEndTime: [{ validator: validFaultEndTime, required: true }],
      },
      viewsOnContentShow: false,
      seaLandCheckBox: [],
      seaLandCheckBoxArr: [],
      seaLandDialogVisible: false,
    };
  },
  mounted() {
    this.backSingleForm.alarmCreateTime = this.common.failureTime;
    this.backSingleForm.sheetCreateTime = this.common.failureInformTime;
    this.backSingleForm.person = this.userInfo.realName;
    this.backSingleForm.workItemId = this.common.workItemId;
    this.backSingleForm.woId = this.common.woId;
    this.backSingleForm.processInstId = this.common.processInstId;
    this.backSingleForm.processDefId = this.common.processDefId;
    this.backSingleForm.actionName = this.common.actionName;
    this.backSingleForm.professionalType = this.common.professionalType + "";
    this.backSingleForm.networkType = this.common.networkType;
    this.backSingleForm.suspendDuration = this.common.hangOver;
    //  this.getOrgInfo();
    this.userData = JSON.parse(this.userInfo.attr2);
    this.backSingleForm.dept = this.userData.orgInfo.fullOrgName;
    this.getSeaLandOptions();
  },
  methods: {
    getSeaLandOptions() {
      let param = {
        dictTypeCode: "10057",
      };
      apiDict(param)
        .then(res => {
          if (res.code == "200") {
            this.seaLandCheckBoxArr = res?.data ?? [];
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    getOrgInfo() {
      apiGetOrgInfo()
        .then(res => {
          if (res.status == "0") {
            this.backSingleForm.dept = res?.data?.orgInfo?.fullOrgName ?? "";
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    computerFaultGenerationAter() {
      if (this.backSingleForm.lastClearTime) {
        let days = moment(
          this.backSingleForm.lastClearTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.backSingleForm.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        this.backSingleForm.lastClearDuration = days;
      } else {
        this.backSingleForm.lastClearDuration = 0;
      }
    },
    second2Time(days) {
      return this.showTime(Math.abs(days));
    },
    computerFaultTreatmentTime() {
      let days = moment(
        this.backSingleForm.faultEndTime,
        "YYYY-MM-DD HH:mm:ss"
      ).diff(
        moment(this.backSingleForm.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
        "seconds"
      );
      this.backSingleForm.faultDuration = days;

      //故障处理净历时  （故障结束时间-故障发生时间）-挂起历时(秒) 换算成-天-小时-分钟-秒
      if (this.backSingleForm.suspendDuration == 0) {
        this.backSingleForm.processDuration = this.backSingleForm.faultDuration;
      } else {
        let seconds = moment(
          this.backSingleForm.faultEndTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.backSingleForm.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        this.backSingleForm.processDuration =
          seconds - this.backSingleForm.suspendDuration;
      }
    },
    //挂起历时
    computerSuspendDuration() {
      if (this.timing.hangTime != "" && null != this.timing.hangTime) {
        if (
          this.timing.liftHangTime != "" &&
          null != this.timing.liftHangTime
        ) {
          let seconds = moment(
            this.timing.liftHangTime,
            "YYYY-MM-DD HH:mm:ss"
          ).diff(
            moment(this.timing.hangTime, "YYYY-MM-DD HH:mm:ss"),
            "seconds"
          );
          this.backSingleForm.suspendDuration = seconds;
        } else {
          this.backSingleForm.suspendDuration = 0;
        }
      } else {
        this.backSingleForm.suspendDuration = 0;
      }
    },
    // //下一步评价
    // nextStepEvaluation() {
    //   this.$refs.backSingleForm.validate(valid => {
    //     if (valid) {
    //       this.evaluationDialogVisible = true;
    //     } else {
    //       return false;
    //     }
    //   });
    // },
    handleSubmit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          let self = this;
          this.backSingleFullscreenLoading = true;
          // let formData = new FormData();
          // formData.append("param1", JSON.stringify(self.backSingleForm));
          // let evaluateParam = {
          //   woId: this.common.woId,
          //   sheetCreateTime: this.backSingleForm.sheetCreateTime,
          //   sendAccuracy: this.evaluation.orderAccuracy,
          //   diagnoseAccuracy: this.evaluation.diagnosticrAccuracy,
          //   evaluateContent: this.evaluation.viewsOnContent,
          //   problemClass:
          //     this.evaluation.feedbackProblemCheckList.length > 0
          //       ? this.evaluation.feedbackProblemCheckList.join(",")
          //       : "",
          // };
          // formData.append("evaluateParam", JSON.stringify(evaluateParam));

          apiBackSingleInternation({
            param1: JSON.stringify(self.backSingleForm),
          })
            .then(res => {
              if (res.status == "0") {
                this.$message.success("提交返单成功");
                //   this.onReset();
                this.$emit("closeBackSingleDialog", res.data);
              } else {
                this.$message.error("提交返单失败");
              }
              this.backSingleFullscreenLoading = false;
            })
            .catch(error => {
              console.log(error);
              this.$message.error("提交返单失败");
              this.backSingleFullscreenLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    faultCateChange() {
      this.resetFaultCateChange();
    },
    resetFaultCateChange() {
      this.backSingleForm.faultReasonCate = null;
      this.backSingleForm.seaLandFiberName = null;
      this.backSingleForm.seaLandFiberSeg = null;
      this.backSingleForm.operatorName = null;
      this.backSingleForm.seaLandFiberType = null;
      this.backSingleForm.seaLandFiberFaultArea = null;
      this.backSingleForm.falutReasonDesc =
        this.common.faultCauseDescription ||
        "样例：距xx机房xxkm处，光缆因xxx（原因）中断。 处理方式：xxxx （熔接或者倒带后）恢复。";
      this.backSingleForm.faultCountry = null;
    },
    onReset() {
      this.backSingleForm = {
        ...this.$options.data,
        alarmCreateTime: this.common.failureTime,
        sheetCreateTime: this.common.failureInformTime,
        woId: this.common.woId,
        workItemId: this.common.workItemId,
        processInstId: this.common.processInstId,
        processDefId: this.common.processDefId,
        person: this.userInfo.realName,
        dept: this.userData.orgInfo.fullOrgName,
        professionalType: "3",
        lastClearDuration: 0, //故障代通历时
        faultDuration: 0, //故障处理净历时
        suspendDuration: this.common.hangOver, //挂起历时 单位秒
        processDuration: 0, //故障处理净历时 单位秒
        networkType: this.common.networkType,
        isOverTime: 0,
        isOverTimeShow: "否",
        falutReasonDesc:
          this.common.faultCauseDescription ||
          "样例：距xx机房xxkm处，光缆因xxx（原因）中断。 处理方式：xxxx （熔接或者倒带后）恢复。",
        actionName: this.common.actionName,
        faultReasonCate: null,
        seaLandFiberName: null,
        seaLandFiberSeg: null,
        operatorName: null,
        seaLandFiberType: null,
        seaLandFiberFaultArea: null,
        faultCountry: null,
        faultEndTime: null,
        lastClearTime: null,
      };
    },
    onResetEvaluation() {
      this.evaluation = {
        ...this.$options.data,
      };
    },
    showTime(val) {
      if (val) {
        if (val == 0) return "0秒";
        var time = "";
        var second = val % 60;
        var minute = parseInt(parseInt(val) / 60) % 60;
        time = second + "秒";
        if (minute >= 1) {
          time = minute + "分" + second + "秒";
        }
        var hour = parseInt(parseInt(val / 60) / 60) % 24;
        if (hour >= 1) {
          time = hour + "小时" + minute + "分" + second + "秒";
        }
        var day = parseInt(parseInt(parseInt(val / 60) / 60) / 24);
        if (day >= 1) {
          time = day + "天" + hour + "小时" + minute + "分" + second + "秒";
        }

        return time;
      } else {
        return "0秒";
      }
    },
    feedbackChange(val) {
      if (val.length > 0) {
        if (val.join(",").indexOf("其他") != -1) {
          this.viewsOnContentShow = true;
        } else {
          this.viewsOnContentShow = false;
        }
      }
    },
    selectSeaLandFiberName() {
      this.seaLandDialogVisible = true;
    },
    seaLandCheckBoxDetermine() {
      this.$set(
        this.backSingleForm,
        "seaLandFiberName",
        this.seaLandCheckBox.join(",")
      );
      this.seaLandDialogVisible = false;
    },
  },
};
</script>
<style lang="scss" scoped>
.back-single-internation {
  max-height: calc(90vh - 150px);
  min-height: 400px;
  margin: 0 4px;
  padding-left: 4px;
  padding-right: 8px;
  overflow-y: auto;
  .cus-card ::v-deep .el-card__header {
    padding: 11px 24px 10px;
    font-weight: 400;
    @include themify {
      background-color: themed("$--background-color-base");
    }
  }
  .fileName_style {
    margin-right: 3px;
    vertical-align: middle;
    div {
      display: inline-block;
      max-width: 120px;
      vertical-align: top;
    }
  }
}
</style>
