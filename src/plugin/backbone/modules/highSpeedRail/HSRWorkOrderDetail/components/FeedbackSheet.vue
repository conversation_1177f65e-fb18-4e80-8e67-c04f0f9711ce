<template>
  <el-card shadow="always" class="card" :body-style="{ padding: '10px 24px' }">
    <div class="header clearfix">
      <div class="header-tabMenu">
        <span class="header-title" style="margin-top: 5px; margin-right: 10px"
          >反馈单详情</span
        >

        <div style="display: inline-block">
          <el-radio-group v-model="showIndex" @change="handleClick" size="mini">
            <el-radio-button
              v-for="(tab, key) in tabMenu"
              :key="key"
              :label="key"
            >
              {{ tab }}
            </el-radio-button>
          </el-radio-group>
        </div>
      </div>
    </div>
    <div class="content">
      <!--      <div class="header-tabMenu" v-if="tabMenu.length > 0">-->
      <!--        <el-tabs type="card" @tab-click="handleClick">-->
      <!--          <el-tab-pane-->
      <!--            v-for="(tab, key) in tabMenu"-->
      <!--            :key="key"-->
      <!--            :label="tab"-->
      <!--          >-->
      <!--          </el-tab-pane>-->
      <!--        </el-tabs>-->
      <!--      </div>-->

      <el-descriptions
        class="descriptions"
        style="margin-top: 20px; margin-bottom: 8px"
      >
        <el-descriptions-item label="故障处理人">{{
          personFeedInfo.recoveryPerson
        }}</el-descriptions-item>
        <el-descriptions-item label="联系方式">{{
          personFeedInfo.recoveryPhone
        }}</el-descriptions-item>
        <el-descriptions-item label="故障分类">{{
          personFeedInfo.faultCate
        }}</el-descriptions-item>
        <el-descriptions-item label="故障原因">{{
          personFeedInfo.faultReason
        }}</el-descriptions-item>
        <el-descriptions-item label="是否影响业务">{{
          personFeedInfo.isEffectBusiness
        }}</el-descriptions-item>
        <el-descriptions-item label="是否基站退服">{{
          personFeedInfo.isSiteOffline
        }}</el-descriptions-item>
        <el-descriptions-item label="故障结束时间">{{
          personFeedInfo.businessRecoveryTime
        }}</el-descriptions-item>
<!--        <el-descriptions-item label="业务恢复时间">-->
<!--          {{ personFeedInfo.businessRecoveryTime }}-->
<!--        </el-descriptions-item>-->
        <el-descriptions-item label="要求反馈时间">{{
          personFeedInfo.needFeedbackTime
        }}</el-descriptions-item>
        <el-descriptions-item label="实际完成时间">{{
          personFeedInfo.realFinishTime
        }}</el-descriptions-item>
        <el-descriptions-item label="故障原因描述">{{
          personFeedInfo.falutReasonDesc
        }}</el-descriptions-item>
      </el-descriptions>
    </div>
    <!--    <el-collapse v-model="activeNames">-->
    <!--      <el-collapse-item name="1">-->
    <!--        <template slot="title"-->
    <!--          ><span class="header-title">反馈单详情</span></template-->
    <!--        >-->
    <!--        -->
    <!--      </el-collapse-item>-->
    <!--    </el-collapse>-->
  </el-card>
</template>

<script>
import { apiqueryFeedback } from "../api/CommonApi";
export default {
  name: "FeedbackSheet",
  props: {
    // isShowAudit: Boolean, //判断定性审核按钮
    // common: Object,
    woId: String,
    // isShowQualitative: Boolean, //判断定性按钮
    // qualitativeType: String,
    // basicWorkOrderData: Object,
  },
  components: {},
  data() {
    return {
      activeNames: ["1"],
      tabMenu: [],
      // professionalTypeId: "",
      personFeedInfo: {},
      allFeedInfo: [],
      showIndex: 0,
      // radio1: 0,
      // descriText: ["很不满意", "不满意", "一般", "很满意", "非常满意"],
    };
  },
  mounted() {
    if (this.woId) {
      this.getFeedbackData();
    }
    console.log(this.common, this.basicWorkOrderData, "反馈单");
  },
  methods: {
    handleClick(tab) {
      this.showIndex = tab;
      this.personFeedInfo = this.allFeedInfo[this.showIndex];
      // this.processInstId = this.tabMenu[index].processId;
      // this.setprocessUrl();
    },

    getFeedbackData() {
      let param = {
        woId: this.woId,
      };
      apiqueryFeedback(param)
        .then(res => {
          console.log("============反馈单");
          console.log(res);

          if (res.status == 0) {
            let data = res?.data ?? [];
            if (data.length > 0) {
              this.allFeedInfo = data;
              this.allFeedInfo.forEach(item => {
                this.tabMenu.push(item.auditUser);
              });
              this.showIndex = 0;
              this.personFeedInfo = this.allFeedInfo[this.showIndex];
            }
          }
        })
        .catch(err => {
          console.log(err);
        });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../assets/common.scss";
.header-tabMenu {
  /*margin-top: 10px;*/
}
.custom-theme-default .el-collapse {
  border-top: none;
}
::v-deep .el-collapse-item__wrap {
  border-bottom: 0;
}
</style>
