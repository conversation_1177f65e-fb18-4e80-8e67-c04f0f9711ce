<template>
  <el-card shadow="always" class="card" :body-style="{ padding: '10px 24px' }">
    <div class="header clearfix">
      <span class="header-title">关联诊断</span>
      <div class="header-right">
        <el-button type="primary" size="mini" @click="getRelationDiagnosis"
          >刷新</el-button
        >
      </div>
    </div>

    <div class="content" v-loading="contentLoading">
      <el-collapse v-model="activeNames">
        <el-collapse-item name="1">
          <span class="collapse-title" slot="title">诊断分析字段</span>
          <el-descriptions class="descriptions" style="padding-top: 15px">
            <el-descriptions-item label="预处理状态">{{
              relationDiagnosis.PPStatusName
            }}</el-descriptions-item>
            <el-descriptions-item label="根因域">{{
              relationDiagnosis.PPCauseDomainName
            }}</el-descriptions-item>
            <el-descriptions-item label="根因类型">{{
              relationDiagnosis.PPAlarmReasonName
            }}</el-descriptions-item>
            <el-descriptions-item label="设备型号">
              {{ relationDiagnosis.equipType }}
            </el-descriptions-item>
            <el-descriptions-item label="软件版本">{{
              relationDiagnosis.softwareVersion
            }}</el-descriptions-item>
            <el-descriptions-item label="硬件版本">
              {{ relationDiagnosis.hardwareVersion }}
            </el-descriptions-item>
            <el-descriptions-item label="根因位置" :span="3">
              <div
                style="white-space: pre-wrap"
                v-html="relationDiagnosis.ppCausePosition"
              >
                {{ relationDiagnosis.ppCausePosition }}
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="预处理过程" :span="3">
              <div
                style="white-space: pre-wrap"
                v-html="relationDiagnosis.PPProcess"
              >
                {{ relationDiagnosis.PPProcess }}
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="预处理结果" :span="3">
              <div
                style="white-space: pre-wrap"
                v-html="relationDiagnosis.PPResult"
              ></div>
            </el-descriptions-item>
          </el-descriptions>
        </el-collapse-item>
      </el-collapse>

      <!-- <el-descriptions title="拓扑图" :colon="false" class="descriptions">
      </el-descriptions> -->
      <el-collapse name="2">
        <el-collapse-item>
          <span class="collapse-title" slot="title">拓扑图</span>
          <div>
            <iframe
              v-if="topoSrc"
              id="topo"
              :src="topoSrc"
              frameborder="0"
              width="100%"
              scrolling="true"
              style="height: 500px; overflow-y: auto"
            ></iframe>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
  </el-card>
</template>

<script>
import { apiGetRelationDiagnosis } from "../api/CommonApi";
export default {
  name: "RelationDiagnosis",
  props: {
    woId: String,
    alarmsData: Array,
  },
  data() {
    return {
      relationDiagnosis: {},
      topoBasicUrl: "/nfm3/netfm3topo/#/layoutTopo/Topo_IframeSysTopo",
      topoSrc: "",
      contentLoading: false,
      timer: null,
      activeNames: [],
    };
  },
  mounted() {
    this.getRelationDiagnosis();
  },
  destroyed() {
    this.clearTimer();
  },
  methods: {
    getRelationDiagnosis() {
      this.contentLoading = true;
      let param = {
        woId: this.woId,
      };
      console.log(this.alarmsData);

      this.topoSrc =
        this.topoBasicUrl +
        "?alarmId=" +
        this.alarmsData[0]?.alarmStaId +
        "&eventTime=" +
        this.alarmsData[0]?.alarmCreateTime;

      apiGetRelationDiagnosis(param)
        .then(res => {
          if (res.status == "0") {
            let self = this;

            if (res.data.length > 0) {
              self.relationDiagnosis = res.data[0];
              // 预处理状态 有值,折叠板展开显示
              if (this.relationDiagnosis.PPStatusName != "") {
                this.activeNames = ["1"];
              }
            }
            if (!this._isDestroyed) {
              this.timer = setTimeout(this.getRelationDiagnosis, 60000 * 3);
            }
          }
          this.contentLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.contentLoading = false;
        });
    },
    clearTimer() {
      clearTimeout(this.timer);
      this.timer = null;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../assets/common.scss";
::v-deep .el-collapse-item {
  .el-collapse-item__header {
    background-color: #fafafa;
    padding-left: 20px;
  }
}
</style>
