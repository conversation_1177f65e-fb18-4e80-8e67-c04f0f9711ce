<template>
  <div class="hang-up">
    <el-form ref="hangUpForm" :model="hangUpForm" label-width="90px">
      <!-- 挂起 -->
      <template v-if="opType == 1">
        <el-form-item :label="personName" prop="hangUserCh">
          <div style="width: 250px">{{ hangUpForm.hangUserCh }}</div>
        </el-form-item>
        <el-form-item :label="dynamicTime" prop="hangTime">
          <div style="width: 250px">
            {{ hangUpForm.hangTime }}
          </div></el-form-item
        >
        <el-form-item
          label="挂起时限:"
          prop="hangLimit"
          :rules="{
            required: true,
            message: '请选择挂起时限',
          }"
        >
          <el-select
            v-model="hangUpForm.hangLimit"
            placeholder="请选择"
            style="width: 100%"
          >
            <el-option
              v-for="item in handupDays"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="挂起原因:"
          prop="hangReason"
          :rules="{
            required: true,
            message: '请填写挂起原因',
          }"
        >
          <el-input
            type="textarea"
            :rows="2"
            placeholder="请填写挂起原因"
            v-model="hangUpForm.hangReason"
            style="width: 100%"
          >
          </el-input>
        </el-form-item>
      </template>
      <template v-if="opType == 2">
        <p>是否确定解挂？</p>
      </template>
    </el-form>

    <div slot="footer" style="padding: 10px 20px; text-align: right">
      <el-button
        type="primary"
        @click="handleHangUpSubmit('hangUpForm')"
        v-loading.fullscreen.lock="hangUpFullScreenLoading"
        >提 交</el-button
      >
      <el-button @click="handleCancel" v-if="opType == 2">取 消</el-button>
      <el-button @click="onResetHangUp" v-if="opType == 1">重 置</el-button>
    </div>
  </div>
</template>
<script>
import moment from "moment";
import { mapGetters } from "vuex";
import { apiActionPublic } from "../api/CommonApi";
export default {
  name: "HangUp",
  props: {
    common: Object,
    opType: Number,
    actionName: String,
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  data() {
    return {
      personName: "",
      dynamicTime: "",
      //挂起
      hangUpForm: {
        hangUpPerson: "",
        hangUpTime: "",
        hangLimit: "",
      },
      hangUpFileDialogVisible: false,
      hangUpFullScreenLoading: false,
      handupDays: [
        {
          value: "3",
          label: "3天",
        },
        {
          value: "4",
          label: "4天",
        },
        {
          value: "5",
          label: "5天",
        },
        {
          value: "6",
          label: "6天",
        },
        {
          value: "7",
          label: "7天",
        },
      ],
    };
  },
  created() {},
  mounted() {
    this.hangUpForm.hangUserCh = this.userInfo.realName;
    this.hangUpForm.hangTime = moment().format("YYYY-MM-DD HH:mm:ss");
    this.hangUpForm.unHangTime = moment().format("YYYY-MM-DD HH:mm:ss");
    if (this.opType == 1) {
      this.personName = "挂起人:";
      this.dynamicTime = "挂起时间:";
    } else if (this.opType == 2) {
      this.personName = "解挂人:";
      this.dynamicTime = "解挂时间:";
    }
    console.log(this.common.professionalTypeName);
  },
  methods: {
    handleHangUpSubmit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.hangUpFullScreenLoading = true;
          let params = {
            woId: this.common.woId,
            processInstId: this.common.processInstId,
            workItemId: this.common.workItemId,
            processNode: this.common.processNode,
            actionName: this.actionName,
            hangUserCh: this.userInfo.realName,
            hangUserEn: this.userInfo.userName,
          };
          if (this.opType == 1) {
            params.hangTime = this.hangUpForm.hangTime;
            params.hangLimit = this.hangUpForm.hangLimit;
            params.hangReason = this.hangUpForm.hangReason;
          } else {
            params.unHangTime = this.hangUpForm.unHangTime;
            params.unHangType = "手动解挂";
          }

          apiActionPublic(params)
            .then(res => {
              if (res.status == "0") {
                if (this.opType == 1) {
                  this.$message.success("挂起申请成功");
                } else if (this.opType == 2) {
                  this.$message.success("解挂申请成功");
                }

                this.onResetHangUp();
                this.$emit("closeDialogHangUp");
              } else {
                if (this.opType == 1) {
                  this.$message.error(res.msg);
                } else if (this.opType == 2) {
                  this.$message.error(res.msg);
                }
              }
              this.hangUpFullScreenLoading = false;
            })
            .catch(error => {
              console.log(error);
              if (this.opType == 1) {
                this.$message.error(error.msg);
              } else if (this.opType == 2) {
                this.$message.error(error.msg);
              }
              this.hangUpFullScreenLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    onResetHangUp() {
      this.hangUpForm = {
        ...this.$options.data,
        hangUpPerson: this.userInfo.realName,
        hangUpTime: moment().format("YYYY-MM-DD HH:mm:ss"),
      };
    },
    handleCancel() {
      this.$emit("closeDialogHangUp");
    },
  },
};
</script>
<style lang="scss" scoped>
.hang-up {
  .fileName_style {
    margin-right: 3px;
    vertical-align: middle;
    div {
      display: inline-block;
      max-width: 320px;
      vertical-align: top;
    }
  }
}
</style>
