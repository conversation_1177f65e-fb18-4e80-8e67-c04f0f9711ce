<template>
  <el-card shadow="always" class="card" :body-style="{ padding: '10px 24px' }">
<!--    <el-collapse v-model="activeNames">-->
<!--      <el-collapse-item name="1">-->
    <div class="header clearfix">
      <span class="header-title">处理详情</span>
    </div>
    <div class="content">
      <el-collapse>
        <el-collapse-item v-for="(item, index) of detailsList" :key="index">
          <span class="collapse-title" slot="title">{{ item.name }}</span>
          <div
            v-for="(itemCont, key) of item.content"
            :key="key"
            class="content__list"
          >
            <span class="detail-p" v-html="itemCont.handleContent"></span>
            <template v-if="itemCont.files.length > 0">
              附件：【
              <span style="margin-bottom: 0">
                    <el-tag
                      class="fileName_style"
                      v-for="(itemFile, index) of itemCont.files"
                      @click="handleDownload(itemFile.attId)"
                      :title="itemFile.attOrigName"
                      :key="index"
                    ><div class="text-truncate">
                        {{ itemFile.attOrigName }}
                      </div></el-tag
                    > </span
              >】
            </template>
            <template v-if="item.name == '现场打点'">
              <i class="el-icon-place" @click="mapVisible = true"></i>
            </template>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
<!--      </el-collapse-item>-->
<!--    </el-collapse>-->
    <el-dialog
      title="现场打点"
      :visible.sync="mapVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="1000px"
    >
      <map-location :woId="woId"></map-location>
    </el-dialog>
  </el-card>
</template>

<script>
import { apiFileDownload, apiGetProcessInfo } from "../api/CommonApi";
import mapLocation from "./MapContainer.vue";
export default {
  name: "DealDetails",
  props: {
    woId: String,
  },
  components: { mapLocation },
  data() {
    return {
      detailsList: [],
      appendixFileLoading: false,
      activeNames: ["1"],
      mapVisible: false,
    };
  },
  mounted() {
    this.getDetailData();
  },
  computed: {},

  methods: {
    getDetailData() {
      let param = {
        woId: this.woId,
      };
      apiGetProcessInfo(param)
        .then(res => {
          this.detailsList = res.data;
        })
        .catch(err => {
          console.log(err);
        });
    },
    handleDownload(attId) {
      let param = {
        attId: attId,
        watermark: "", //水印
      };
      apiFileDownload(param)
        .then(res => {})
        .catch(err => {
          console.log(err);
        });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../assets/common.scss";
.custom-theme-default .el-collapse {
  border-top: 0;
}

::v-deep .el-collapse-item__wrap {
  border-bottom: 0;
}
.content ::v-deep .el-collapse-item {
  .el-collapse-item__header {
    background-color: #fafafa;
    padding-left: 20px;
  }
}
.content ::v-deep .content__list {
  padding-top: 10px;
  padding-left: 25px;
}
::v-deep .detail-p {
  display: inline-block;
  padding: 0px 5px;
  margin: 0;
}

.fujian {
  display: flex;
  margin-top: 5px;
  .label {
    width: 100px;
    display: block;
  }
  .links {
    flex: 1;
  }
}
.el-icon-place {
  font-size: 22px;
  color: #b50b14;
}
</style>
