<template>
  <el-card shadow="always" class="card" :body-style="{ padding: '10px 24px' }">
    <div class="header clearfix">
      <span class="header-title">基础信息</span>
      <div class="header-right">
        <!-- <el-button
          type="primary"
          size="mini"
          @click="exportBasicWorkOrderInfo"
          v-loading.fullscreen.lock="exportLoading"
          >导出Excel表</el-button
        > -->
      </div>
    </div>
    <div class="content">
      <el-descriptions title="工单基本信息" class="descriptions">
        <el-descriptions-item label="网络故障工单编号">{{
          basicWorkOrderData.faultWoCode
        }}</el-descriptions-item>
        <el-descriptions-item label="网络类型">{{
          basicWorkOrderData.neType
        }}</el-descriptions-item>
        <el-descriptions-item label="故障类型">
          {{ basicWorkOrderData.faultType }}
        </el-descriptions-item>
        <el-descriptions-item label="受理时限(单位：分钟)">{{
          basicWorkOrderData.acceptTimeLimit
        }}</el-descriptions-item>

        <el-descriptions-item label="反馈时限(单位：小时)">{{
          basicWorkOrderData.feedbackTimeLimit
        }}</el-descriptions-item>
        <el-descriptions-item label="高铁线路名称">
          {{ basicWorkOrderData.hsrLineName }}
        </el-descriptions-item>

        <el-descriptions-item label="小区/基站名称">
          {{ basicWorkOrderData.bsName }}
        </el-descriptions-item>

        <el-descriptions-item label="网元唯一标识">
          {{ basicWorkOrderData.cellCgi }}
        </el-descriptions-item>

        <el-descriptions-item label="小区ID">
          {{ basicWorkOrderData.tacId }}
        </el-descriptions-item>

        <el-descriptions-item label="省份">
          {{ basicWorkOrderData.provinceName }}
        </el-descriptions-item>

        <el-descriptions-item label="城市">
          {{ basicWorkOrderData.cityName }}
        </el-descriptions-item>

        <el-descriptions-item label="红线内外标识">
          {{ basicWorkOrderData.redLineState }}
        </el-descriptions-item>

        <el-descriptions-item label="告警对象标识">
          {{ basicWorkOrderData.neIdn }}
        </el-descriptions-item>

        <el-descriptions-item label="基站ID">
          {{ basicWorkOrderData.bsCode }}
        </el-descriptions-item>

        <el-descriptions-item label="站址名称"v-if="basicWorkOrderData.stName != null && basicWorkOrderData.stName != ''">
          {{ basicWorkOrderData.stName }}
        </el-descriptions-item>

        <el-descriptions-item label="工单优先级">
          {{ basicWorkOrderData.faultLevel }}
        </el-descriptions-item>

        <el-descriptions-item label="故障详情" span="3">
          <text-collapse
            :text="return2Br(basicWorkOrderData.faultPhenomenon)"
            :max-lines="2"
          ></text-collapse>
        </el-descriptions-item>
      </el-descriptions>
      <el-descriptions title="工单派送信息" class="descriptions">
        <el-descriptions-item label="主送">
          {{ basicWorkOrderData.agentMan }}
        </el-descriptions-item>
        <!--        <el-descriptions-item label="次送">-->
        <!--          {{ basicWorkOrderData.acceptMan }}-->
        <!--        </el-descriptions-item>-->
        <el-descriptions-item label="抄送">
          {{ basicWorkOrderData.copyMan }}
        </el-descriptions-item>
        <!--        <el-descriptions-item label="短信通知">{{-->
        <!--          basicWorkOrderData.isSendSms == 1 ? "是" : "否"-->
        <!--        }}</el-descriptions-item>-->
        <!--        <el-descriptions-item-->
        <!--          :span="2"-->
        <!--          v-if="basicWorkOrderData.isSendSms == 1"-->
        <!--          label="接收人"-->
        <!--          >{{ basicWorkOrderData.smsToUsername }}</el-descriptions-item-->
        <!--        >-->
        <!--        <el-descriptions-item-->
        <!--          v-if="basicWorkOrderData.isSendSms == 1"-->
        <!--          label="发送内容"-->
        <!--          :span="3"-->
        <!--          >{{ basicWorkOrderData.sendContent }}-->
        <!--        </el-descriptions-item>-->
      </el-descriptions>
    </div>
    <el-dialog
      width="500px"
      title="文件列表"
      :visible.sync="impactServiceVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <file-download
        @cancel="impactServiceVisible = false"
        :attachmentArr="impactServiceArr"
      ></file-download>
    </el-dialog>
    <el-dialog
      width="500px"
      title="文件列表"
      :visible.sync="attachmentVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <file-download
        @cancel="attachmentVisible = false"
        :attachmentArr="attachmentArr"
      ></file-download>
    </el-dialog>
  </el-card>
</template>

<script>
  import TextCollapse from "@plugin/backbone/components/TextCollapse.vue";

  import {
  apiExportWorkOrder,
  apiDownloadManualFile,
  apifluenceExcel,
} from "../api/CommonApi";
import { apiFileDownload } from "../api/CommonApi";
export default {
  props: {
    basicWorkOrderData: Object,
    woId: String,
    workItemId: [String, Number],
  },
  components: {  TextCollapse },
  name: "BaseInfo",
  data() {
    return {
      builderZs: "",
      builderCs: "",
      notifier: "",
      recipient: "",
      exportLoading: false,
      impactServiceArr: [],
      impactServiceVisible: false,
      attachmentArr: [],
      attachmentVisible: false,
      manualFileName: "电路信息",
      OpticCableName: "影响系统信息",
      OpticCableLoading: false,
      manualFileFullscreenLoading: false,
      appendixFileLoading: false,
      professionalType: "",
    };
  },
  mounted() {
    if (this.basicWorkOrderData.appendixFileUrl) {
      this.impactServiceArr = JSON.parse(
        this.basicWorkOrderData.appendixFileUrl
      );
    }
    if (this.basicWorkOrderData.appendix) {
      this.attachmentArr = JSON.parse(this.basicWorkOrderData.appendix);
    }
  },
  methods: {
    exportBasicWorkOrderInfo() {
      this.exportLoading = true;
      let param = {
        woId: this.woId,
        workItemId: this.workItemId,
      };
      apiExportWorkOrder(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("导出成功");
          } else {
            this.$message.error("导出失败");
          }
          this.exportLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("导出失败");
          this.exportLoading = false;
        });
    },
    queryImpactServiceList() {
      this.impactServiceVisible = true;
    },
    queryAttachmentList() {
      this.attachmentVisible = true;
    },
    //电缆附件下载
    downloadOpticCable() {
      this.OpticCableLoading = true;
      let param = {
        param1: JSON.stringify({
          woId: this.woId,
        }),
      };
      apifluenceExcel(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("文件下载成功");
          } else {
            this.$message.success("文件下载失败");
          }
          this.OpticCableLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("文件下载失败");
          this.OpticCableLoading = false;
        });
    },
    // downloadAppendixFile(data) {
    //   this.appendixFileLoading = true;
    //   let param = {
    //     attId: data.id,
    //   };
    //   apiDownloadAppendixFile(param)
    //     .then(res => {
    //       if (res.status == "0") {
    //         this.$message.success("文件下载成功");
    //       } else {
    //         this.$message.error("文件下载失败");
    //       }
    //       this.appendixFileLoading = false;
    //     })
    //     .catch(error => {
    //       console.log(error);
    //       this.$message.error("文件下载失败");
    //       this.appendixFileLoading = false;
    //     });
    // },
    downloadManualFile() {
      this.manualFileFullscreenLoading = true;
      let param = {
        param1: JSON.stringify({
          woId: this.woId,
        }),
      };
      apiDownloadManualFile(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("文件下载成功");
          } else {
            this.$message.success("文件下载失败");
          }
          this.manualFileFullscreenLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("文件下载失败");
          this.manualFileFullscreenLoading = false;
        });
    },
    stitchingAlgorithm(orgName, userName) {
      if (
        null != orgName &&
        orgName.length !== 0 &&
        orgName !== '无' &&
        null != userName &&
        userName.length !== 0 &&
        userName !== '无'
      ) {
        return orgName + "," + userName;
      } else {
        if (null != orgName && orgName.length !== 0 && orgName != "无") {
          return orgName;
        } else if (null != userName && userName.length !== 0 && userName != "无") {
          return userName;
        } else {
          return "无";
        }
      }
    },
    // 回车\r\n转为<br/>标签
    return2Br(str) {
      if (str) {
        return str.replaceAll("\\r\\n", "<br/>");
      }
    },
    handleDownload(attId) {
      let param = {
        attId: attId,
        watermark: "", //水印
      };
      apiFileDownload(param)
        .then(res => {})
        .catch(err => {
          console.log(err);
        });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../assets/common.scss";
::v-deep .el-descriptions-item__content {
  position: relative;
  /*white-space: pre-wrap;*/
  word-break: break-all;
  padding-right: 8px;
}
::v-deep .el-descriptions-item__cell {
  vertical-align: top;
}
</style>
