<template>
  <div class="afterSingle">
    <el-form ref="afterSingleForm" :model="afterSingleForm" label-width="110px">
      <el-form-item
        label="追单内容"
        prop="content"
        :rules="{
          required: true,
          message: '请填写追单内容',
        }"
      >
        <el-input
          type="textarea"
          :rows="2"
          v-model="afterSingleForm.content"
          style="width: 420px"
        >
        </el-input>
      </el-form-item>
      <el-form-item
        label="系统名称"
        v-if="
          common.isSender == 1 &&
          common.sheetStatus != '待定性审核' &&
          common.sheetStatus != '挂起'
        "
      >
        <el-input
          type="textarea"
          :rows="2"
          placeholder="多个系统以逗号分隔"
          v-model="afterSingleForm.systemName"
          style="width: 420px"
        >
        </el-input>
      </el-form-item>
      <el-form-item label="附件:">
        <div style="width: 400px">
          <el-tag
            class="fileName_style"
            closable
            v-for="(item, index) in importForm.attachmentFileList"
            :key="index"
            @close="close(item)"
            :title="item.name"
            ><div class="text-truncate">{{ item.name }}</div></el-tag
          >
          <el-button size="mini" type="primary" @click="attachmentBrowse"
            >+上传附件</el-button
          >
        </div>
      </el-form-item>
      <el-form-item
        label="是否流转省份:"
        v-if="
          common.isSender == 1 &&
          common.sheetStatus != '待定性审核' &&
          common.sheetStatus != '挂起'
        "
        prop="transferStatusRadio"
        :rules="{
          required: true,
          message: '请选择是否流转省份',
        }"
      >
        <el-radio-group v-model="afterSingleForm.transferStatusRadio">
          <el-radio label="1">是</el-radio>
          <el-radio label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        label="主送:"
        prop="mainSending"
        v-if="afterSingleForm.transferStatusRadio == 1"
        :rules="{
          required: true,
          message: '请选择主送人',
        }"
      >
        <el-input
          v-model="afterSingleForm.mainSending"
          placeholder="添加人员"
          style="width: 420px"
          readonly
        >
          <template v-slot:append>
            <el-button
              type="info"
              icon="el-icon-user"
              @click="onOpenPeopleDialog('lordSentDetermine')"
            ></el-button>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item
        label="抄送:"
        prop="cc"
        v-if="afterSingleForm.transferStatusRadio == 1"
      >
        <el-input
          v-model="afterSingleForm.cc"
          placeholder="添加人员"
          style="width: 420px"
          readonly
        >
          <template v-slot:append>
            <el-button
              type="info"
              icon="el-icon-user"
              @click="onOpenPeopleDialog('ccDetermine')"
            ></el-button>
          </template>
        </el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: right">
      <el-button
        type="primary"
        @click="handleSubmit('afterSingleForm')"
        v-loading.fullscreen.lock="afterSingleFullscreenLoading"
        >提 交</el-button
      >
      <el-button @click="onResetAfterSingleForm">重 置</el-button>
    </div>
    <el-dialog
      width="420px"
      title="附件选择"
      :visible.sync="attachmentDialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <file-upload
        @change="changeFileData"
        @cancel="closeAttachmentDialog"
      ></file-upload>
    </el-dialog>
    <dia-orgs-user-tree
      :title="diaPeople.title"
      :visible.sync="diaPeople.visible"
      :showOrgsTree="diaPeople.showOrgsTree"
      @on-save="onSavePeople"
      :appendToBody="true"
      :orgDefaultChecked="rawAgenManDeptCodeArr"
      :userDefaultChecked="rawAgentManIdArr"
    />
  </div>
</template>
<script>
import { apiAfterSingle } from "../api/CommonApi";

import FileUpload from "../../../workOrder/components/FileUpload";
import DiaOrgsUserTree from "../../../workOrder/components/DiaOrgsUserTree.vue";

export default {
  name: "AfterSingle",
  props: {
    common: Object,
  },
  components: { FileUpload, DiaOrgsUserTree },
  data() {
    return {
      afterSingleForm: {
        content: null,
        systemName: null,
        mainSending: null,
        lordSentUserName: null,
        lordSentOrgName: null,
        lordSentUserId: null,
        lordSentOrgId: null,
        cc: null,
        transferStatusRadio: "0",
        ccUserName: null,
        ccOrgName: null,
        ccUserId: null,
        ccOrgId: null,
        agentManDetail: null,
        copyManDetail: null,
      },
      afterSingleFullscreenLoading: false,
      attachmentDialogVisible: false,
      importForm: {
        //附件
        attachmentFileList: [],
      },
      diaPeople: {
        visible: false,
        title: "",
        saveName: "",
        saveTitleMap: {
          lordSentDetermine: "主送",
          ccDetermine: "建单人抄送",
        },
        showOrgsTree: true,
        showOrgsTreeMap: {
          recipientDetermine: false,
        },
      },
      rawAgentManIdArr: [],
      rawAgenManDeptCodeArr: [],
    };
  },
  mounted() {
    if (this.common.isSender == 1) {
      this.afterSingleForm.mainSending = this.stitchingAlgorithm(
        this.common.agentDeptName,
        this.common.agentMan
      );
      this.afterSingleForm.lordSentUserName = this.common.agentMan;
      this.afterSingleForm.lordSentOrgName = this.common.agentDeptName;
      if (this.common.agentManId) {
        this.afterSingleForm.lordSentUserId = this.common.agentManId;
        this.rawAgentManIdArr = this.common.agentManId.split(",");
      }
      if (this.common.agentDeptCode) {
        this.afterSingleForm.lordSentOrgId = this.common.agentDeptCode;
        this.rawAgenManDeptCodeArr = this.common.agentDeptCode.split(",");
      }
    }
  },
  watch: {},
  methods: {
    handleSubmit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.afterSingleFullscreenLoading = true;
          let formData = new FormData();
          if (this.importForm.attachmentFileList.length > 0) {
            for (let item of this.importForm.attachmentFileList) {
              formData.append("files", item.raw);
            }
          } else {
            formData.append("files", "");
          }
          formData.append("isSender", this.common.isSender);
          let param = {
            woId: this.common.woId,
            processInstId: this.common.processInstId,
            processDefId: this.common.processDefId,
            workItemId: null, //this.common.workItemId,
            processNode: this.common.processNode,
            appendContent: this.afterSingleForm.content,
            sysName: this.afterSingleForm.systemName,
            isSendtoProvince: this.afterSingleForm.transferStatusRadio || "0",
            agentManId:
              this.afterSingleForm.transferStatusRadio == 0
                ? this.common.agentManId
                : this.afterSingleForm.lordSentUserId,
            agentMan:
              this.afterSingleForm.transferStatusRadio == 0
                ? this.common.agentMan
                : this.afterSingleForm.lordSentUserName,
            agentDeptCode:
              this.afterSingleForm.transferStatusRadio == 0
                ? this.common.agentDeptCode
                : this.afterSingleForm.lordSentOrgId,
            agentDeptName:
              this.afterSingleForm.transferStatusRadio == 0
                ? this.common.agentDeptName
                : this.afterSingleForm.lordSentOrgName,
            copyManId:
              this.afterSingleForm.transferStatusRadio == 0
                ? this.common.copyManId
                : this.afterSingleForm.ccUserId,
            copyMan:
              this.afterSingleForm.transferStatusRadio == 0
                ? this.common.copyMan
                : this.afterSingleForm.ccUserName,
            copyDeptCode:
              this.afterSingleForm.transferStatusRadio == 0
                ? this.common.copyDeptCode
                : this.afterSingleForm.ccOrgId,
            copyDeptName:
              this.afterSingleForm.transferStatusRadio == 0
                ? this.common.copyDeptName
                : this.afterSingleForm.ccOrgName,
          };
          formData.append("jsonParam", JSON.stringify(param));
          apiAfterSingle(formData)
            .then(res => {
              if (res.status == "0") {
                this.$message.success("追单提交成功");
                this.$emit("closeAfterSingleDialog", this.common.isSender);
              } else {
                this.$message.error("追单提交失败");
              }
              this.afterSingleFullscreenLoading = false;
            })
            .catch(error => {
              console.log(error);
              this.afterSingleFullscreenLoading = false;
              this.$message.error("追单提交失败");
            });
        } else {
          return false;
        }
      });
    },
    //附件选择
    attachmentBrowse() {
      this.attachmentDialogVisible = true;
    },
    changeFileData(data) {
      this.importForm.attachmentFileList = data.attachmentFileList;
      this.attachmentDialogVisible = false;
    },
    closeAttachmentDialog() {
      this.attachmentDialogVisible = false;
    },
    onResetAfterSingleForm() {
      this.afterSingleForm = {
        content: null,
        systemName: null,
        mainSending: null,
        lordSentUserName: null,
        lordSentOrgName: null,
        lordSentUserId: null,
        lordSentOrgId: null,
        cc: null,
        transferStatusRadio: "0",
        ccUserName: null,
        ccOrgName: null,
        ccUserId: null,
        ccOrgId: null,
      };
      this.importForm.attachmentFileList = [];
    },
    onOpenPeopleDialog(diaSaveName) {
      this.diaPeople.title = this.diaPeople.saveTitleMap[diaSaveName];
      this.diaPeople.saveName = diaSaveName;
      this.diaPeople.showOrgsTree =
        this.diaPeople.showOrgsTreeMap[diaSaveName] ?? true;
      this.diaPeople.visible = true;
    },
    onSavePeople(val) {
      this[this.diaPeople.saveName](val);
    },
    //主送确定
    lordSentDetermine({ usersChecked, orgsChecked, selectionUser }) {
      if (selectionUser && selectionUser.length > 0) {
        let usersCheckedName = selectionUser.map(item => {
          return item.trueName;
        });
        this.afterSingleForm.lordSentUserName = usersCheckedName.join(",");
        let usersCheckedId = selectionUser.map(item => {
          return item.userName;
        });
        this.afterSingleForm.lordSentUserId = usersCheckedId.join(",");
        this.afterSingleForm.agentManDetail = selectionUser
          .map(item => {
            return (
              item.trueName +
              "-" +
              item.orgEntity.orgName +
              "-" +
              item.mobilePhone
            );
          })
          .join(",");
      } else {
        let usersCheckedName = usersChecked.map(item => {
          return item.name;
        });
        this.afterSingleForm.lordSentUserName = usersCheckedName.join(",");
        let usersCheckedId = usersChecked.map(item => {
          return item.id;
        });
        this.afterSingleForm.lordSentUserId = usersCheckedId.join(",");
        this.afterSingleForm.agentManDetail = usersChecked
          .map(item => {
            return item.name + "-" + item.orgName + "-" + item.mobilePhone;
          })
          .join(",");
      }

      let orgsCheckedName = orgsChecked.map(item => {
        return item.name;
      });
      this.afterSingleForm.lordSentOrgName = orgsCheckedName.join(",");
      this.afterSingleForm.mainSending = this.stitchingAlgorithm(
        this.afterSingleForm.lordSentOrgName,
        this.afterSingleForm.agentManDetail
      );
      let orgsCheckedId = orgsChecked.map(item => {
        return item.id;
      });
      this.afterSingleForm.lordSentOrgId = orgsCheckedId.join(",");
    },
    //抄送
    ccDetermine({ usersChecked, orgsChecked, selectionUser }) {
      if (selectionUser && selectionUser.length > 0) {
        let usersCheckedName = selectionUser.map(item => {
          return item.trueName;
        });
        this.afterSingleForm.ccUserName = usersCheckedName.join(",");
        let usersCheckedId = selectionUser.map(item => {
          return item.userName;
        });
        this.afterSingleForm.ccUserId = usersCheckedId.join(",");
        this.afterSingleForm.copyManDetail = selectionUser
          .map(item => {
            return (
              item.trueName +
              "-" +
              item.orgEntity.orgName +
              "-" +
              item.mobilePhone
            );
          })
          .join(",");
      } else {
        let usersCheckedName = usersChecked.map(item => {
          return item.name;
        });
        this.afterSingleForm.ccUserName = usersCheckedName.join(",");
        let usersCheckedId = usersChecked.map(item => {
          return item.id;
        });
        this.afterSingleForm.ccUserId = usersCheckedId.join(",");
        this.afterSingleForm.copyManDetail = usersChecked
          .map(item => {
            return item.name + "-" + item.orgName + "-" + item.mobilePhone;
          })
          .join(",");
      }

      let orgsCheckedName = orgsChecked.map(item => {
        return item.name;
      });
      this.afterSingleForm.ccOrgName = orgsCheckedName.join(",");
      this.afterSingleForm.cc = this.stitchingAlgorithm(
        this.afterSingleForm.ccOrgName,
        this.afterSingleForm.copyManDetail
      );
      let orgsCheckedId = orgsChecked.map(item => {
        return item.id;
      });
      this.afterSingleForm.ccOrgId = orgsCheckedId.join(",");
    },
    close(tag) {
      this.importForm.attachmentFileList.splice(
        this.importForm.attachmentFileList.indexOf(tag),
        1
      );
    },
    stitchingAlgorithm(orgName, userName) {
      if (
        null != orgName &&
        orgName.length !== 0 &&
        null != userName &&
        userName.length !== 0
      ) {
        return orgName + "," + userName;
      } else {
        if (null != orgName && orgName.length !== 0) {
          return orgName;
        } else if (null != userName && userName.length !== 0) {
          return userName;
        } else {
          return "";
        }
      }
    },

    stitchingAlgorithmArr(orgName, userName) {
      if (orgName.length !== 0 && userName.length !== 0) {
        return orgName.join(",") + "," + userName.join(",");
      } else {
        if (orgName.length !== 0) {
          return orgName.join(",");
        } else if (userName.length !== 0) {
          return userName.join(",");
        } else {
          return "";
        }
      }
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-tree {
  padding-top: 15px;
  padding-left: 10px;
  // 不可全选样式
  .el-tree-node {
    .is-leaf + .el-checkbox .el-checkbox__inner {
      display: inline-block;
    }
    .el-checkbox .el-checkbox__inner {
      display: none;
    }
  }
}
.afterSingle {
  .fileName_style {
    margin-right: 3px;
    vertical-align: middle;
    div {
      display: inline-block;
      max-width: 360px;
      vertical-align: top;
    }
  }
}
</style>
