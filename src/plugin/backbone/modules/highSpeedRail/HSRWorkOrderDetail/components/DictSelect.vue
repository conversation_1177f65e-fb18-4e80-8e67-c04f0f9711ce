<template>
  <el-select
    v-model="selectVal"
    :placeholder="placeholder"
    :disabled="notSelect"
    clearable
    filterable
    @change="onChange"
  >
    <el-option
      v-for="(item, i) in dictData"
      :key="i"
      :label="item.dictName"
      :value="item"
    >
    </el-option>
  </el-select>
</template>

<script>
import { apiDict } from "../api/CommonApi";
export default {
  name: "DictSelect",
  props: {
    value: {
      type: [Number, String],
    },
    dictId: [Number, String],
    placeholder: String,
    notSelect: {
      default: false,
    },
  },
  data() {
    return {
      selectVal: this.value,
      dictData: [],
    };
  },
  watch: {
    dictId(newValue) {
      if (newValue) this.getDictData();
    },
    value(newValue) {
      this.selectVal = newValue;
      // this.$emit("update:value", this.selectVal);
    },
    notSelect(newValue) {
      this.notSelect = newValue || false;
    },
    deep: true,
    immediate: true,
  },
  mounted() {
    if (this.dictId) this.getDictData();
  },
  methods: {
    getDictData() {
      let param = {
        dictType: this.dictId,
      };
      apiDict(param)
        .then(res => {
          if (res.code == 200) {
            this.dictData = res?.data ?? [];
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    onChange(val) {
      this.selectVal = val.dictName;
      this.$emit("update:value", this.selectVal);
      this.$emit("change", val);
    },
  },
};
</script>
