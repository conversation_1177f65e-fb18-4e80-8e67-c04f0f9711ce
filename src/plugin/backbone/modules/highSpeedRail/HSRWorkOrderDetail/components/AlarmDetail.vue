<template>
  <el-card shadow="always" class="card" :body-style="{ padding: '10px 24px' }">
    <div class="header clearfix">
      <span class="header-title">告警详情</span>
      <div class="header-right">
<!--        <el-button type="primary" size="mini" @click="getTableData('refresh')"-->
<!--          >刷新</el-button-->
<!--        >-->
        <!-- <el-button
          type="primary"
          size="mini"
          @click="selectDetailDialogVisible = true"
          >查询</el-button
        > -->
        <el-button
          type="primary"
          size="mini"
          v-if="tableData.length > 0"
          @click="syncClearAlarm"
          v-loading.fullscreen.lock="syncClearAlarmFullscreenLoading"
          >同步清除告警</el-button
        >
      </div>
    </div>

    <div class="_el-table">
      <el-table
        :data="tableData"
        border
        stripe
        v-loading="tableLoading"
      >
        <el-table-column
          type="index"
          width="60px"
          label="序号"
        ></el-table-column>
<!--        @row-click="opentableList"-->
        <el-table-column
          prop="alarmTitle"
          label="告警标题"
          min-width="200"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="isMajorAlarm"
          label="是否为主告警"
          width="106"
        ></el-table-column>
        <el-table-column
          label="告警级别"
          prop="alarmLevelName"
          width="80"
        ></el-table-column>
        <el-table-column
          label="告警详情"
          prop="alarmDetail"
          min-width="300"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <!-- tips悬浮提示 -->
            <el-popover
              trigger="hover"
              placement="top"
              width="300"
            >
              <p style="max-width: 300px">{{ scope.row.alarmDetail }}</p>
              <div slot="reference" class="ellipsis">
                {{ scope.row.alarmDetail }}
              </div>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column
          label="告警发生时间"
          prop="alarmCreateTime"
          width="160"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="实际清除时间"
          prop="alarmClearTime"
          width="160"
          show-overflow-tooltip
        ></el-table-column>
      </el-table>
    </div>
    <div style="display: flex;justify-content: space-between;align-items: center">
      <span>共计<span style="color: #b50b14">{{form.total}}</span>条告警，其中追加告警<span style="color: #b50b14">{{form.addTotal}}</span>条。</span>
      <pagination
        ref="pagination"
        :total="form.total"
        :page.sync="form.pageNum"
        :limit.sync="form.pageSize"
        layout="->, total, sizes, prev, pager, next"
        @change="getTableData('filterQuery')"
      />
    </div>

    <el-dialog
      title="快速查询"
      :visible.sync="selectDetailDialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="selectDetailDialogClose"
      width="550px"
    >
      <el-form
        ref="selectDetailForm"
        :model="selectDetailForm"
        label-width="100px"
      >
        <el-form-item label="标题:">
          <el-input
            v-model="selectDetailForm.alarmTitle"
            style="width: 300px"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="主告警:">
          <el-select
            v-model="selectDetailForm.isMajorAlarm"
            clearable
            style="width: 300px"
          >
            <el-option label="是" value="1"></el-option>
            <el-option label="否" value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="追加告警:">
          <el-select
            v-model="selectDetailForm.isAppendAlarm"
            clearable
            style="width: 300px"
          >
            <el-option label="是" value="1"></el-option>
            <el-option label="否" value="0"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="selectDetailSubmit()"
          v-loading.fullscreen.lock="selectDetailScrrenLoading"
          >提 交</el-button
        >
        <el-button @click="onResetTurnSingle">重 置</el-button>
      </div>
    </el-dialog>

    <el-dialog
      :visible.sync="tableListVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="selectDetailDialogClose"
      width="1300px"
      title="告警详情"
    >
      <div style="height: 500px; overflow-y: auto">
        <el-descriptions class="margin-top" :column="3" border>
          <el-descriptions-item>
            <template slot="label"> 告警流水号 </template>
            <div class="tdOverflow" :title="tableListData.alarmId">
              {{ tableListData.alarmId }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 原始告警号 </template>
            <div class="tdOverflow" :title="tableListData.oriAlarmId">
              {{ tableListData.oriAlarmId }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 告警标题 </template>
            <div class="tdOverflow" :title="tableListData.alarmTitle">
              {{ tableListData.alarmTitle }}
            </div>
          </el-descriptions-item>

          <el-descriptions-item>
            <template slot="label"> 告警区县 </template>
            <div class="tdOverflow">
              {{ tableListData.alarmRegion }}{{ tableListData.alarmCity }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 专业 </template>
            <div class="tdOverflow">
              {{ tableListData.professionalTypeName }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 告警对象 </template>
            <div class="tdOverflow">
              {{ tableListData.locateNeName }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 告警类型 </template>
            <div class="tdOverflow">
              {{ tableListData.orgTypeName }}
            </div>
          </el-descriptions-item>

          <el-descriptions-item>
            <template slot="label"> 故障发生时间 </template>
            <div class="tdOverflow">
              {{ tableListData.alarmCreateTime }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 添加时间 </template>
            <div class="tdOverflow">
              {{ tableListData.sheetCreateTime }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 清除方式 </template>
            <div class="tdOverflow">
              {{ tableListData.alarmClearType }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 主告警 </template>
            <div class="tdOverflow">
              {{ tableListData.isMajorAlarm }}
            </div>
          </el-descriptions-item>

          <el-descriptions-item>
            <template slot="label"> 次告警 </template>
            <div class="tdOverflow">
              {{ tableListData.isAppendAlarm }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 实际清除时间 </template>
            <div class="tdOverflow">
              {{ tableListData.alarmClearTime }}
            </div>
          </el-descriptions-item>

          <el-descriptions-item>
            <template slot="label"> 网络业务类型 </template>
            <div class="tdOverflow" :title="tableListData.networkTypeName">
              {{ tableListData.networkTypeName }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 告警定位 </template>
            <div class="tdOverflow" :title="tableListData.alarmLocation">
              {{ tableListData.alarmLocation }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 告警级别 </template>
            <div class="tdOverflow" :title="tableListData.alarmLevelName">
              {{ tableListData.alarmLevelName }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 告警处理响应级别 </template>
            <div class="tdOverflow" :title="tableListData.alarmHandleLevel">
              {{ tableListData.alarmHandleLevel }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 业务是否中断 </template>
            <div class="tdOverflow" :title="tableListData.businessDown">
              {{ tableListData.businessDown }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 业务影响范围</template>
            <div class="tdOverflow" :title="tableListData.effectRange">
              {{ tableListData.effectRange }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 设备类型 </template>
            <div class="tdOverflow" :title="tableListData.neType">
              {{ tableListData.neType }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 设备厂家 </template>
            <div class="tdOverflow" :title="tableListData.alarmVendor">
              {{ tableListData.alarmVendor }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 设备型号 </template>
            <div class="tdOverflow" :title="tableListData.equipType">
              {{ tableListData.equipType }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 告警逻辑分类 </template>
            <div class="tdOverflow" :title="tableListData.alarmType">
              {{ tableListData.alarmType }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 告警逻辑子类 </template>
            <div class="tdOverflow" :title="tableListData.alarmSubType">
              {{ tableListData.alarmSubType }}
            </div>
          </el-descriptions-item>

          <el-descriptions-item>
            <template slot="label"> 资源系统网元唯一标识 </template>
            <div class="tdOverflow" :title="tableListData.neId">
              {{ tableListData.neId }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 告警描述 </template>
            <div
              :title="tableListData.alarmDetail"
              v-html="return2Br(tableListData.alarmDetail)"
            ></div>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </el-card>
</template>

<script>
import Pagination from "../../../workOrder/components/Pagination.vue";
import { apiQueryAlarmDetail, apiSyncClear } from "../api/CommonApi";

export default {
  name: "AlarmDetail",
  components: {
    Pagination,
  },
  props: {
    woId: String,
  },
  data() {
    return {
      form: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        addTotal: 0,
      },
      selectDetailForm: {
        alarmTitle: "",
        isMajorAlarm: "",
        isAppendAlarm: "",
      },
      tableData: [],
      tableListData: {},
      tableLoading: false,
      selectDetailDialogVisible: false,
      tableListVisible: false,
      selectDetailScrrenLoading: false,
      alarmStaIdArr: [],
      alarmCreateTime: "",
      syncClearAlarmFullscreenLoading: false,
    };
  },
  mounted() {
    this.getTableData("firstQuery");
  },
  methods: {
    getTableData(type) {
      if (type == "refresh") {
        this.selectDetailForm.alarmTitle = "";
        this.selectDetailForm.isMajorAlarm = "";
        this.selectDetailForm.isAppendAlarm = "";
      }
      this.tableLoading = true;
      let param = {
        pageIndex: this.form.pageNum,
        pageSize: this.form.pageSize,
        woId: this.woId,
      };
      apiQueryAlarmDetail(param)
        .then(res => {
          if (res.status == "0") {
            this.tableData = res?.data?.info ?? [];
            this.tableListData = res?.data?.info ?? [];
            this.form.total = res?.data?.total ?? 0;
            this.form.addTotal = res?.data?.addTotal ?? 0;
            this.alarmStaIdArr = this.tableData.map(item => {
              return item.alarmStaId;
            });
          }
          this.tableLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.tableLoading = false;
        });
    },
    //打开详情框
    opentableList(row) {
      // this.tableListVisible = true;
      // this.tableListData = row;
    },
    selectDetailDialogClose() {
      this.selectDetailDialogVisible = false;
      this.tableListVisible = false;
    },
    selectDetailSubmit() {
      this.selectDetailDialogVisible = false;
      this.getTableData("filterQuery");
    },
    onResetTurnSingle() {
      this.selectDetailForm = {
        ...this.$options.data,
      };
    },
    syncClearAlarm() {
      this.syncClearAlarmFullscreenLoading = true;
      let param = {
        uniqueId: this.alarmStaIdArr,
        alarmCreateTime: this.tableData[0].alarmCreateTime,
      };
      apiSyncClear(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("同步清除成功");
          } else {
            this.$message.error("同步清除失败");
          }
          this.syncClearAlarmFullscreenLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("同步清除失败");
          this.syncClearAlarmFullscreenLoading = false;
        });
    },
    // 回车\r\n转为<br/>标签
    return2Br(str) {
      if (str) {
        return str.replaceAll("\\r\\n", "<br/>");
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../assets/common.scss";
._el-table {
  width: 100%;

}

.ellipsis {
  display: -webkit-box;
  overflow: hidden;
  white-space: normal !important;
  text-overflow: ellipsis;
  word-wrap: break-word;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.el-popover {
  max-height: 120px;
  overflow: auto;
}
::v-deep .el-descriptions-row {
  .is-bordered-label {
    width: 140px;
  }
  .tdOverflow {
    width: 260px;
    height: 50px;
    overflow-x: auto;
    // text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 50px;
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .tdOverflow::-webkit-scrollbar {
    display: none;
  }
}
</style>
