import { getJson, postJson } from "@/utils/axios";

const applyCleanAlarmUrl =
  "/commonFlow/workflow/op/applyCleanAlarm"; //告警详情清除申请
const checkClearAlarmUrl =
  "/commonFlow/workflow/op/checkClearAlarm"; //人工确认告警清除

const apiApplyCleanAlarm = params =>
  postJson(applyCleanAlarmUrl, params);

const apiCheckClearAlarm = params =>
  postJson(checkClearAlarmUrl, params);

export { apiApplyCleanAlarm, apiCheckClearAlarm };
