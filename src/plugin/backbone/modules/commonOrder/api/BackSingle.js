import { getJson, postFormData } from "@/utils/axios";
const faultAreaUrl = "backbone/info/faultArea"; //故障发生地区字典数据
const getEvaluationUrl = "/backbone/workflow/getEvaluation"; //是否已评价

const backSingleUrl =
  "/commonFlow/workflow/ackDefine/feedback"; 
const qualitativeUrl =
  "/commonFlow/workflow/ackDefine/define"; 

const apiGetFaultArea = params => getJson(faultAreaUrl, params);
const apiBackSingle = params =>
  postFormData(backSingleUrl, params);
const apiGetEvaluation = params => getJson(getEvaluationUrl, params);
const apiQualitative = params =>
  postFormData(qualitativeUrl, params);

export { apiGetFaultArea, apiBackSingle, apiGetEvaluation, apiQualitative };
