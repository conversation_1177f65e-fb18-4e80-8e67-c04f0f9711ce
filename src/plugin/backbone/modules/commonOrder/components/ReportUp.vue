<template>
  <div class="hang-up">
    <el-form ref="reportUpForm" :model="reportUpForm" label-width="120px">
      <el-form-item label="描述:" prop="opContent">
        <el-input
          type="textarea"
          :rows="2"
          v-model="reportUpForm.opContent"
          style="width: 100%"
        >
        </el-input>
      </el-form-item>

      <!-- <el-form-item :label="personName" prop="hangUpPerson">
        <div style="width: 250px">{{ hangUpForm.hangUpPerson }}</div>
      </el-form-item> -->
      <!-- <el-form-item :label="dynamicTime" prop="hangUpTime">
        <div style="width: 250px">
          {{ hangUpForm.hangUpTime }}
        </div>
      </el-form-item> -->
      <el-form-item
        label="上传故障报告:"
        prop="fileName"
        :rules="{
          required: true,
          message: '请上传故障报告',
        }"
      >
        <div style="width: 100%">
          <el-tag
            class="fileName_style"
            closable
            v-for="(item, index) in reportUpForm.reportUpFilesList"
            :key="index"
            @close="close(item)"
            :title="item.name"
          >
            <div class="text-truncate">{{ item.name }}</div>
          </el-tag>
          <el-button size="mini" type="primary" @click="attachmentBrowse"
            >+上传附件</el-button
          >
        </div>
      </el-form-item>
    </el-form>

    <div slot="footer" style="padding: 10px 20px; text-align: right">
      <el-button
        type="primary"
        @click="handleHangUpSubmit()"
        v-loading.fullscreen.lock="reportUpFullScreenLoading"
        >提 交</el-button
      >
      <el-button @click="onResetHangUp">重 置</el-button>
    </div>
    <el-dialog
      width="420px"
      title="附件选择"
      :visible.sync="reportUpFileDialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <file-upload
        @change="changeFileData"
        @cancel="closeAttachmentDialog"
      ></file-upload>
    </el-dialog>
  </div>
</template>
<script>
import moment from "moment";
import { mapGetters } from "vuex";
import { apiReportUp } from "../api/CommonApi";
import FileUpload from "../../workOrder/components/FileUpload.vue";
export default {
  name: "HangUp",
  props: {
    common: Object,
    sheetCreateTime: String,
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  components: { FileUpload },
  data() {
    return {
      personName: "",
      dynamicTime: "",
      reportUpForm: {
        opPerson: "", //上报人
        opPersonName: "", //上报人（中文）
        opDeptName: "", //上报人所属组织
        opContent: "", //描述
        reportUpFilesList: [],
        fileName: null,
      },
      // attr2: [],
      reportUpFileDialogVisible: false,
      reportUpFullScreenLoading: false,
    };
  },
  created() {},
  mounted() {
    let attr2 = this.userInfo.attr2;
    this.reportUpForm.opPerson = JSON.parse(attr2).userName;
    this.reportUpForm.opPersonName = JSON.parse(attr2).trueName;
    this.reportUpForm.opDeptName = JSON.parse(attr2).orgInfo.fullOrgName;
  },
  methods: {
    handleHangUpSubmit() {
      let formData = new FormData();
      if (this.reportUpForm.reportUpFilesList.length > 0) {
        for (let item of this.reportUpForm.reportUpFilesList) {
          formData.append("files", item.raw);
        }
      } else {
        this.$message({
          message: "请上传故障报告",
          type: "warning",
        });
        return;
      }
      let jsonParam = {
        //工单ID
        woId: this.common.woId,
        //工作项ID
        workItemId: this.common.workItemId,
        //流程实例ID
        processInstId: this.common.processInstId,
        //建单时间
        sheetCreateTime: this.sheetCreateTime,
        //上报时间
        //上报人所属组织
        opDeptName: this.reportUpForm.opDeptName,
        //上报人
        opPerson: this.reportUpForm.opPerson,
        //上报人（中文名）
        opPersonName: this.reportUpForm.opPersonName,
        //描述
        opContent: this.reportUpForm.opContent,
      };
      formData.append("jsonParam", JSON.stringify(jsonParam));
      this.reportUpFullScreenLoading = true;
      apiReportUp(formData)
        .then(res => {
          if (res.status == "0") {
            this.$message.success(res.msg);
            this.onResetHangUp();
            this.$emit("closeDialogHangUp");
          } else {
            this.$message.error(res.msg);
          }
          this.reportUpFullScreenLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("故障报告上传失败");
          this.reportUpFullScreenLoading = false;
        });
    },
    onResetHangUp() {
      this.reportUpForm = {
        ...this.$options.data,
        hangUpPerson: this.userInfo.realName,
        hangUpTime: moment().format("YYYY-MM-DD HH:mm:ss"),
      };
    },
    attachmentBrowse() {
      this.reportUpFileDialogVisible = true;
    },

    changeFileData(data) {
      this.reportUpForm.fileName = data.fileName;
      this.reportUpForm.reportUpFilesList = data.attachmentFileList;
      this.reportUpFileDialogVisible = false;
    },
    closeAttachmentDialog() {
      this.reportUpFileDialogVisible = false;
    },

    close(tag) {
      this.reportUpForm.reportUpFilesList.splice(
        this.reportUpForm.reportUpFilesList.indexOf(tag),
        1
      );
    },
  },
};
</script>
<style lang="scss" scoped>
.hang-up {
  .fileName_style {
    margin-right: 3px;
    vertical-align: middle;

    div {
      display: inline-block;
      max-width: 320px;
      vertical-align: top;
    }
  }
}
</style>
