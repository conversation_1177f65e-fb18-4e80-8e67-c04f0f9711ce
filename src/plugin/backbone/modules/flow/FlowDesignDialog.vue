<template>
  <el-dialog
    class="flow-design-dialog"
    :visible.sync="dialogVisible"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    :show-close="false"
    :modal="false"
    fullscreen
    append-to-body
    :before-close="handleClose"
  >
    <im-border-layout
      :id="scopeDomId"
      layout="48 380 0 0"
      style="height: 98vh"
      :border="false"
      :collapse-left="collapsedLeft"
      :collapse-right="collapsedRight"
      :collapse-top="collapsedTop"
      left-resizable
      right-resizable
    >
      <div
        slot="top"
        style="
          display: flex;
          align-items: center;
          justify-content: space-between;
        "
      >
        <span class="el-dialog__title" style="float: left">
          <el-tag style="margin-left: 10px">{{ flowRecord.name }}</el-tag>
        </span>
        <div style="float: right; margin-top: 8px">
          <el-button
            :loading="saveLoading"
            type="success"
            icon="el-icon-check"
            @click="saveFlow"
            >保存
          </el-button>
          <el-divider direction="vertical"></el-divider>
          <el-button type="info" @click="handleClose">关闭</el-button>
          <el-divider direction="vertical"></el-divider>
        </div>
      </div>

      <im-panel slot="right" title="控制面板">
        <span slot="extra">
          <em
            class="el-icon-d-arrow-right"
            style="font-size: 14px; cursor: pointer; color: #409eff"
            @click="collapsedRight = true"
          ></em>
        </span>
        <div>
          <el-form label-width="120px">
            <el-tabs v-model="activeTabValue">
              <el-tab-pane label="流程信息" name="flow">
                <el-form-item label="流程标识">
                  <el-tag type="primary">{{ flowRecord.key }}</el-tag>
                </el-form-item>
                <el-form-item label="流程名称">
                  <el-input
                    readonly
                    v-model="flowRecord.name"
                    type="textarea"
                    clearable
                    placeholder="请输入流程名称"
                  ></el-input>
                </el-form-item>
                <el-form-item label="主题色">
                  <el-color-picker v-model="themeColor"></el-color-picker>
                </el-form-item>
              </el-tab-pane>
              <el-tab-pane v-if="element" label="节点配置" name="element">
                <el-form-item label="编号">
                  <el-input v-model="element.id"></el-input>
                </el-form-item>
                <el-form-item label="名称">
                  <el-input v-model="element.name"></el-input>
                </el-form-item>
                <template v-if="element.isPath()">
                  <el-form-item label="连线风格">
                    <el-select
                      v-model="element.pathStyle"
                      @change="onPathStyleChange"
                    >
                      <el-option label="折线段" value="broken"></el-option>
                      <el-option label="垂平线" value="hv"></el-option>
                      <el-option label="直线" value="straight"></el-option>
                    </el-select>
                  </el-form-item>
                </template>

                <template v-else>
                  <el-form-item label="宽度">
                    <el-input v-model="elementWidth" type="number"> </el-input>
                  </el-form-item>
                  <el-form-item label="高度">
                    <el-input v-model="elementHeight" type="number"> </el-input>
                  </el-form-item>
                  <el-form-item label="X">
                    <el-input v-model="elementX" type="number"> </el-input>
                  </el-form-item>
                  <el-form-item label="Y">
                    <el-input v-model="elementY" type="number"> </el-input>
                  </el-form-item>
                  <el-form-item label="详细描述">
                    <el-input
                      type="textarea"
                      :rows="6"
                      v-model="element.meta.describe"
                    >
                    </el-input>
                  </el-form-item>
                </template>
              </el-tab-pane>
            </el-tabs>
          </el-form>
        </div>
      </im-panel>
      <im-panel slot="rightOfCollapse" style="width: 32px; height: 100%">
        <em
          class="el-icon-d-arrow-left"
          style="font-size: 14px; cursor: pointer; color: #409eff"
          @click="collapsedRight = !collapsedRight"
        ></em>
        <div
          style="margin-top: 5px; margin-left: -1px; cursor: pointer"
          @click="collapsedRight = !collapsedRight"
        >
          <div>控制面板</div>
        </div>
      </im-panel>

      <im-panel class="layout-dialog-center" title="流程设计">
        <span slot="extra">
          <div>
            <el-divider direction="vertical"></el-divider>
            <span>
              <em
                class="el-icon-rank"
                style="cursor: pointer"
                title="铺开"
                @click="collapsedRight = !collapsedRight"
              ></em>
            </span>
          </div>
        </span>
        <div
          ref="flow"
          style="height: 100%; box-sizing: border-box; position: relative"
        ></div>
      </im-panel>
    </im-border-layout>
  </el-dialog>
</template>

<script>
import wf from "./wastflow.es";
import { saveFlow } from "./flow-api";
import { ImBorderLayout, ImPanel } from "itsm-common";
export default {
  name: "FlowDesignDialog",
  components: { ImBorderLayout, ImPanel },
  props: {
    visible: Boolean,
    title: String,
    flowRecord: Object,
  },
  data() {
    return {
      scopeDomId: new Date().getTime().toString(16),
      saveLoading: false,
      dialogVisible: false,
      flowLoading: false,

      // 整体页面布局属性控制
      collapsedLeft: false,
      collapsedRight: false,
      collapsedTop: false,

      activeTabValue: "flow",
      element: null,
      initThemeColor: "#B50B14",
      themeColor: null,
    };
  },
  created() {
    this.dialogVisible = this.visible;
  },
  computed: {
    elementWidth: {
      get() {
        return this.element.attr("width");
      },
      set(val) {
        this.element.attr("width", parseFloat(val));
        this.flow.updateElements(this.element);
      },
    },
    elementHeight: {
      get() {
        return this.element.attr("height");
      },
      set(val) {
        this.element.attr("height", parseFloat(val));
        this.flow.updateElements(this.element);
      },
    },
    elementX: {
      get() {
        return this.element.attr("x");
      },
      set(val) {
        this.element.attr("x", parseFloat(val));
        this.flow.updateElements(this.element);
      },
    },
    elementY: {
      get() {
        return this.element.attr("y");
      },
      set(val) {
        this.element.attr("y", parseFloat(val));
        this.flow.updateElements(this.element);
      },
    },
  },
  beforeDestroy() {
    if (this.flow) {
      this.flow.destroy();
    }
  },
  methods: {
    // 初始化流程
    initFlow() {
      let dom = this.$refs.flow;
      if (!dom) return;
      this.themeColor = this.initThemeColor;
      let flow = this.flow;
      if (!flow) {
        flow = this.flow = wf.render(dom, {
          // 网格
          grid: true,
          // 是否开启菜单(编辑模式)
          menu: {
            draggable: false,
            style: {
              width: "50px",
              padding: "5px 5px 10px",
            },
          },
          // 是否支持平移
          panable: true,
          // 双击文本是否开启编辑
          textEditOnDblClick: false,
          // 是否单行显示（如果超长使用三个点风格的省略号）
          nowrap: false,
          pathStyle: "hv",
          /**
           * 拖拽菜单中排除哪些类型不显示
           */
          excludeTypes: [
            "manual",
            "message",
            "service",
            "businessTask",
            "or",
            "join",
          ],
          /** 默认条件类型 */
          defaultConditionType: "Always",
          /** 导出时忽略校验 */
          ignoreValidateOnExport: true,
          /** 默认快捷追加的节点函数 */
          defaultNextNodeFn: (flowO, x, y) => {
            return flowO.createCustomHtmlNode("custom-node", x, y);
          },
          onNodeCreated(node) {
            if (node.text) {
              Object.assign(node.text.node.style, {
                fontFamily: "PingFangSC-Regular",
                fontSize: "12px",
                color: "#000000",
                transform: "translate(-50%, calc(-50% + 10px))",
              });
            }
          },

          settings: {
            // 主题色
            themeColor: this.themeColor,
            // 这里的item必须是已经注册的自定义节点(html)
            customMenuItems: ["custom-node"],
          },
          // 元素单击事件
          clickElement: this.clickElement,
          // 元素双击事件
          dblclickElement: this.dblclickElement,
          // 空白点击事件
          clickBlank: this.clickBlank,
        });

        // 自定义开始和结束节点覆盖内置
        flow.registerHTML(
          "start",
          (flowO, options) => {
            let themeColor = (options && options.color) || flowO.themeColor;
            if (options && options.scene == "menu") {
              return `<div style='height: 100%; width: 100%; background: #fff;border-radius: 50%; border: 1px solid ${themeColor}; color: ${themeColor}; display: flex;align-items: center;justify-content: center;font-size: 10px;'>开始</div>`;
            }
            return `<div style='height: 100%; width: 100%; background: #fff;box-shadow: 0px 2px 4px 0px rgba(0,0,0,0.11); border: 2px solid ${themeColor};box-sizing: border-box; color: ${themeColor}; display: flex;align-items: center;justify-content: center;font-family: PingFangSC-Semibold;font-size: 12px;font-weight: 600;'>开始</div>`;
          },
          {
            width: 137,
            height: 46,
            text: true,
          }
        );
        this.endFlowRegisterHTML(flow);
        this.customCodeFlow(flow);

        let { key, name, json } = this.flowRecord;
        this.flow.processId = key;
        this.flow.processName = name;
        if (json) {
          // 加载流程数据
          this.flow.setData(json);
        } else {
          this.flow.reset();
        }
        this.flow.overview();
      }
    },
    endFlowRegisterHTML(flow) {
      // 自定义结束
      flow.registerHTML(
        "end",
        (flowO, options) => {
          let themeColor = (options && options.color) || flowO.themeColor;
          if (options && options.scene == "menu") {
            return `<div style='height: 100%; width: 100%; background: ${themeColor};border-radius: 50%; border: 1px solid ${themeColor}; color: #fff; display: flex;align-items: center;justify-content: center;font-size: 10px;'>结束</div>`;
          }
          return `<div style='height: 100%; width: 100%; background: ${themeColor};color: #fff;box-shadow: 0px 2px 4px 0px rgba(0,0,0,0.11);display: flex;align-items: center;justify-content: center;font-family: PingFangSC-Regular;font-size: 12px;font-weight: 400;'>结束</div>`;
        },
        {
          width: 137,
          height: 46,
          text: true,
        }
      );
    },
    customCodeFlow(flow) {
      // 自定义节点(支持设置初始化大小)
      flow.registerHTML(
        "custom-node",
        (flowO, options, element) => {
          let themeColor = (options && options.color) || flowO.themeColor;
          if (options && options.scene == "menu") {
            return `<div style='height: 100%; width: 100%; background: ${themeColor};color: #fff;border: 1px solid ghostwhite; box-sizing: border-box; overflow: hidden;display: flex;align-items: center;justify-content: center;'>
                          <span style="font-size: 10px;">节点</span>
                      </div>`;
          }
          return `<div style='height: 100%; width: 100%; background: ${themeColor};color: #fff;border: 1px solid #BDBDBD; box-shadow: 0px 2px 4px 0px rgba(0,0,0,0.11); box-sizing: border-box; overflow: hidden;'>
                        <div style="width: 100%; height: 20px; display: flex; align-items: center;">
                              <svg width="15px" height="15px" style="margin: 0 5px; " viewBox="0 0 15 15" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                  <g id="dagou-4" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                      <g fill="#FFFFFF" fill-rule="nonzero">
                                          <path d="M7.48104559,0.0158428012 C3.3586701,0.0158428012 0.0161238514,3.35772473 0.0161238514,7.48093488 C0.0161238514,11.6033274 3.3586701,14.9453797 7.48104559,14.9453797 C11.6037617,14.9453797 14.9454733,11.6033274 14.9454733,7.48093488 C14.9454733,3.35772473 11.6037788,0.0158428012 7.48104559,0.0158428012 Z M6.80243183,9.85575088 L6.8026192,9.85609156 L6.12400543,10.5343987 L5.44505099,9.85609156 L5.44539166,9.85575088 L3.40936302,7.81990959 L4.08795974,7.14129582 L6.12381807,9.17713711 L10.8744721,4.42667042 L11.5527111,5.10560782 L6.80243183,9.85575088 Z"></path>
                                      </g>
                                  </g>
                              </svg>
                              <span style="font-size: 12px;">已执行</span>
                        </div>
                        <div style="width: 100%; height: calc(100% - 20px);background: #fff"></div>
                    </div>`;
        },
        {
          width: 137,
          height: 66,
          text: true,
        }
      );
    },
    clickElement(element, evt) {
      this.element = element;
      this.activeTabValue = "element";
    },
    clickBlank() {
      this.element = null;
      this.activeTabValue = "flow";
    },
    onPathStyleChange(pathStyle) {
      if (this.element && this.element.isPath()) {
        this.flow.updatePath(this.element);
      }
    },
    /**
     * 保存流程图
     * @returns {Promise<void>}
     */
    async saveFlow() {
      let data = this.flow.getData();
      let flowJson = JSON.stringify(data, null, 4);
      console.log(flowJson);
      saveFlow({
        ...this.flowRecord,
        json: flowJson,
      })
        .then(res => {
          this.$emit("on-refresh", res.data);
          this.$message.info("保存成功");
          this.handleClose();
        })
        .catch(err => {
          console.error(err);
          this.$message.error("保存失败");
        });
    },
    /*关闭布局设计弹出框*/
    handleClose() {
      this.dialogVisible = false;
      this.$emit("update:visible", false);
    },
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
      if (val) {
        this.$nextTick(this.initFlow);
      }
    },
    themeColor(val) {
      if (this.flow) {
        this.flow.setThemeColor(val);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.flow-design-dialog {
  /deep/ .el-dialog {
    &__header {
      padding: unset !important;
      border-bottom: unset !important;
    }

    &__body {
      padding: 5px;
    }
  }

  /deep/ .im-panel-title {
    padding: 8px 4px 8px 16px;
  }

  /deep/ .el-tabs__item {
    height: 35px !important;
    line-height: 35px !important;
  }

  /deep/ .layout-dialog-center > .el-card__body {
    height: calc(100% - 52px) !important;
  }

  .column-search-input {
    margin: 8px 0;
  }
}
</style>
