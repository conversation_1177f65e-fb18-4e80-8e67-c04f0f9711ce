<template>
  <el-drawer
    size="50%"
    :visible.sync="drawer"
    append-to-body
    :before-close="handleFlowDrawerClose"
  >
    <div class="title" style="display: flex; align-items: center" slot="title">
      <span class="label" style="margin-left: 8px">{{ title }}</span>
    </div>
    <div style="padding: 0 20px; height: 100%; position: relative">
      <el-form
        ref="form"
        size="small"
        :model="flowRecord"
        label-width="120px"
        style="height: calc(100% - 90px); overflow: auto"
      >
        <el-form-item
          prop="name"
          label="流程名称"
          :rules="[{ required: true, message: '流程名称不能为空' }]"
        >
          <el-input
            placeholder="请输入流程名称"
            v-model="flowRecord.name"
            clearable
          ></el-input>
        </el-form-item>

        <el-form-item
          prop="key"
          label="流程编码"
          :rules="[{ required: true, message: '流程编码不能为空' }]"
        >
          <el-input
            placeholder="请输入流程编码，支持数字，字母，下划线组合,首字母不能为数字"
            v-model="flowRecord.key"
            clearable
          ></el-input>
        </el-form-item>

        <el-form-item
          prop="version"
          label="版本号"
          :rules="[{ required: true, message: '版本号不能为空' }]"
        >
          <el-input
            placeholder="请输入版本号"
            v-model="flowRecord.version"
            clearable
          ></el-input>
        </el-form-item>
      </el-form>

      <div
        style="
          position: absolute;
          bottom: 5px;
          z-index: 3000;
          left: 20px;
          right: 20px;
          height: 100px;
          background: #fff;
          text-align: right;
        "
      >
        <el-divider></el-divider>
        <el-button @click="cancel()">取消</el-button>
        <el-button type="primary" @click="saveflow()">保存</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { saveFlow } from "./flow-api";
export default {
  name: "FlowDrawer",
  props: {
    title: String,
    flowRecord: Object,
    visible: Boolean,
  },
  data() {
    return {
      drawer: false,
    };
  },
  computed: {},
  mounted() {},
  methods: {
    handleOnShow() {},

    /** 关闭编辑抽屉 */
    handleFlowDrawerClose() {
      this.drawer = false;
      this.$emit("update:visible", false);
    },

    /** new version flow */
    async saveflow() {
      this.$refs.form.validate(ok => {
        if (ok) {
          saveFlow(this.flowRecord)
            .then(res => {
              this.$emit("on-refresh", res.data);
              this.$message.info("保存成功");
              this.recordChanged = false;
              this.handleFlowDrawerClose();
            })
            .catch(err => {
              console.error(err);
              this.$message.error("保存失败");
            })
            .then(() => {});
        } else {
          this.$message.error("校验未通过");
        }
      });
    },

    /** 取消 */
    cancel() {
      this.clearFlowValidates();
      this.handleFlowDrawerClose();
    },

    /** 清除校验 */
    clearFlowValidates() {
      this.$refs.form.clearValidate();
    },
  },
  watch: {
    visible: {
      handler(val) {
        this.drawer = val;
        if (val) {
          this.handleOnShow();
        }
      },
      immediate: true,
    },
  },
};
</script>
