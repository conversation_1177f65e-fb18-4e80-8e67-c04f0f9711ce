<template>
  <div
    ref="flow"
    class="wast-flow"
    style="width: 100%; height: 100%; min-height: 360px; overflow: hidden"
  ></div>
</template>
<script>
import wf from "./wastflow.es";
import zoom_out from "./zoom_out.png";
import zoom_in from "./zoom_in.png";
import overview from "./overview.png";
export default {
  name: "flow-view",
  props: {
    // 流程图JSON
    flowJson: [String, Object],
    // 初始颜色
    initColor: String,
    // 点亮颜色
    completeColor: String,
    // 当前环节颜色
    currentColor: String,
    // 点亮数据
    completeConnects: Array,
    // 开启动画(暂时未实现)
    animation: <PERSON><PERSON>an,
    type: String,
    isPortraitScreen: String,
  },
  data() {
    return {
      flow: null,
    };
  },
  mounted() {
    this.initFlow();
    this.updateFlow();
  },
  beforeDestroy() {
    if (this.flow) {
      this.flow.destroy();
      this.flow = null;
    }
  },
  methods: {
    initFlow() {
      if (!this.flow) {
        let offsetTop = -2;
        if (this.isPortraitScreen == "true" && !this.type) {
          offsetTop = -60;
        } else if (this.isPortraitScreen == "false" && !this.type) {
          offsetTop = -2;
        }
        let flow = (this.flow = wf.render(this.$refs.flow, {
          background: "#FFF",
          menu: false,
          panable: true,
          editable: false,
          textEditOnDblClick: false,
          nowrap: false,
          disableZoomOnMouseWheel: this.type == "pc" ? true : false,
          // others settings
          settings: {
            themeColor: this.initColor || "#C0C5C4",
          },
          onNodeCreated(node, _flow) {
            if (node.text) {
              Object.assign(node.text.node.style, {
                fontFamily: "PingFangSC-Regular",
                fontSize: "12px",
                color: "#000000",
                transform: "translate(-50%, calc(-50% + 10px))",
              });
            }
          },
          overviewPaddingLeft: 20,
          // 自适应后微调
          overviewOffsetTop: offsetTop,
          // 元素的悬浮事件(PC)
          mouseoverElement: this.mouseoverElement,
          // 鼠标离开事件(PC)
          mouseoutElement: this.mouseoutElement,
          // 元素单击事件
          clickElement: this.clickElement,
          // 元素双击事件
          dblclickElement: this.dblclickElement,
          // 空白点击事件
          clickBlank: this.clickBlank,
        }));

        // 工具栏设置
        flow.setToolStyle({
          top: "35px",
          right: "25px",
          display: "flex",
          width: "123px",
          justifyContent: "space-around",
          flexDirection: "unset",
        });

        // 自定义缩放图标
        flow.registerHTML("zoomReset", ``);
        flow.registerHTML("zoomIn", `<img width="36" src="${zoom_in}">`);
        flow.registerHTML("zoomOut", `<img width="36" src="${zoom_out}">`);
        flow.registerHTML("overview", `<img width="36" src="${overview}">`);

        // 自定义开始
        this.registerFlowStart(flow);
        // 自定义结束
        this.registerFlowEnd(flow);

        // 自定义节点(支持设置初始化大小)
        this.registerFlowCustomNode(flow);
      }
    },
    registerFlowStart(flow) {
      flow.registerHTML(
        "start",
        (flowO, options, element) => {
          let themeColor = (options && options.color) || flowO.themeColor;
          let color = themeColor;
          return `<div style='height: 100%; width: 100%; background: #fff; border: 2px solid ${themeColor}; box-sizing: border-box; color: ${color}; display: flex;align-items: center;justify-content: center;font-size: .9em;'>开始</div>`;
        },
        {
          width: 180,
          height: 60,
          text: true,
        }
      );
    },
    registerFlowEnd(flow) {
      flow.registerHTML("end", (flowO, options, element) => {
        let themeColor = (options && options.color) || flowO.themeColor;
        let color = "#fff";
        return `<div style='height: 100%; width: 100%; background: ${themeColor};color: ${color}; display: flex;align-items: center;justify-content: center;font-size: .9em;'>结束</div>`;
      });
    },
    registerFlowCustomNode(flow) {
      flow.registerHTML("custom-node", (flowO, options, element) => {
        let themeColor = (options && options.color) || flowO.themeColor;
        let status = element && element.data("status");
        if (status == "init") {
          // 未完成
          return `<div style='height: 100%; width: 100%; background: #ECECEC;border: 1px solid #BDBDBD; box-shadow: 0px 2px 4px 0px rgba(0,0,0,0.11); box-sizing: border-box; overflow: hidden;'>
                          <div style="width: 100%; height: 20px; display: flex; align-items: center;border-bottom: 1px solid #BDBDBD;">
                                <svg width="15px" height="15px" style="margin: 0 5px;"  viewBox="0 0 15 15" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                        <g transform="translate(-0.000000, 0.000000)" fill="#797979" fill-rule="nonzero" id="形状">
                                            <path d="M7.29329427,0.0130208333 C3.26660156,0.0130208333 0.00162760417,3.27799479 0.00162760417,7.3046875 C0.00162760417,11.3313802 3.26660156,14.5963542 7.29329427,14.5963542 C11.319987,14.5963542 14.5849609,11.3313802 14.5849609,7.3046875 C14.5849609,3.27799479 11.319987,0.0130208333 7.29329427,0.0130208333 Z M11.4599609,7.82552083 L3.125,7.82552083 L3.125,6.78385417 L11.4599609,6.78385417 L11.4599609,7.82552083 Z"></path>
                                        </g>
                                    </g>
                                </svg>
                                <span style="font-family: PingFangSC-Semibold;font-size: 12px;color: #797979;font-weight: 600;">不涉及</span>
                                <div style="flex: 2;">
                                    <div class="execute-tag" style="float: right;border-left: 1px solid #BDBDBD;height: 100%; cursor: pointer;" title="执行">
                                        <svg style="pointer-events: none;" width="18px" height="18px" viewBox="0 0 18 18" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                            <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                                <g>
                                                    <rect x="0" y="0" width="20" height="20"></rect>
                                                    <polygon fill="#797979" transform="translate(11.333333, 10.000000) rotate(90.000000) translate(-11.333333, -10.000000) " points="11.3333333 6 16.6666667 14 6 14"></polygon>
                                                </g>
                                            </g>
                                        </svg>
                                    </div>
                                </div>
                          </div>
                          <div style="width: 100%; height: calc(100% - 20px);background: #fff"></div>
                      </div>`;
        } else {
          return `<div style='height: 100%; width: 100%; background: ${themeColor};color: #fff;border: 1px solid #BDBDBD; box-shadow: 0px 2px 4px 0px rgba(0,0,0,0.11); box-sizing: border-box; overflow: hidden;'>
                          <div style="width: 100%; height: 20px; display: flex; align-items: center;">
                                <svg width="15px" height="15px" style="margin: 0 5px; " viewBox="0 0 15 15" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                    <g id="dagou-4" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                        <g fill="#FFFFFF" fill-rule="nonzero">
                                            <path d="M7.48104559,0.0158428012 C3.3586701,0.0158428012 0.0161238514,3.35772473 0.0161238514,7.48093488 C0.0161238514,11.6033274 3.3586701,14.9453797 7.48104559,14.9453797 C11.6037617,14.9453797 14.9454733,11.6033274 14.9454733,7.48093488 C14.9454733,3.35772473 11.6037788,0.0158428012 7.48104559,0.0158428012 Z M6.80243183,9.85575088 L6.8026192,9.85609156 L6.12400543,10.5343987 L5.44505099,9.85609156 L5.44539166,9.85575088 L3.40936302,7.81990959 L4.08795974,7.14129582 L6.12381807,9.17713711 L10.8744721,4.42667042 L11.5527111,5.10560782 L6.80243183,9.85575088 Z"></path>
                                        </g>
                                    </g>
                                </svg>
                                <span style="font-size: 12px;">已执行</span>
                          </div>
                          <div style="width: 100%; height: calc(100% - 20px);background: #fff"></div>
                      </div>`;
        }
      });
    },
    updateFlow(onlyUpdateStatus) {
      if (!this.$refs.flow) {
        return;
      }
      if (!this.flow) {
        this.initFlow();
      }
      let flow = this.flow;
      if (!onlyUpdateStatus) {
        // 初始化数据
        flow.setData(this.flowJson);
        // 初始化将状态设置init
        flow.resetStatus("init");
        // 自适应
        flow.overview();
      }

      // 将所有元素设置为灰色
      flow.setElementsColor(this.initColor || "#C0C5C4");
      // 设置点亮颜色
      this.lightenFlow(flow);
    },
    lightenFlow(flow) {
      let completeConnects = this.completeConnects || [];
      for (let connect of completeConnects) {
        if (connect.id) {
          flow.completeConnectById(
            connect.id,
            this.completeColor || "#B50B14",
            this.currentColor
          );
        } else if (connect.fromId && connect.toId) {
          flow.completeConnect(
            connect.fromId,
            connect.toId,
            this.completeColor || "#B50B14",
            this.currentColor
          );
        } else {
          console.error("connect data invalid, not supported", connect);
        }
      }
    },
    mouseoverElement(element, evt) {
      if (!this.flow.isMobile()) {
        this.$emit("mouseover-element", element, evt);
      }
    },
    mouseoutElement(element, evt) {
      if (!this.flow.isMobile()) {
        this.$emit("mouseout-element", element, evt);
      }
    },
    clickElement(element, evt) {
      if (evt.target.classList.contains("execute-tag")) {
        // 点击了手动运行的图标
        this.$emit("manual-execute", element, evt);
      } else {
        this.$emit("click-element", element, evt);
      }
    },
    dblclickElement(element, evt) {
      this.$emit("dblclick-element", element, evt);
    },
    clickBlank() {
      this.$emit("click-blank");
    },
    // 自适应
    fit() {
      if (this.flow) {
        this.flow.overview();
      }
    },
  },
  watch: {
    flowJson() {
      this.updateFlow();
    },
    completeConnects() {
      this.updateFlow(true);
    },
  },
};
</script>

<style lang="scss" scoped>
.wast-flow {
  ::v-deep div[data-id] {
    span {
      line-height: 1.5 !important;
    }
  }
}
</style>
