var t,e,a,i,o=Object.defineProperty,r=Object.prototype.hasOwnProperty,n=Object.getOwnPropertySymbols,s=Object.prototype.propertyIsEnumerable,l=Object.assign,h=(t,e)=>{var a={};for(var i in t)r.call(t,i)&&e.indexOf(i)<0&&(a[i]=t[i]);if(null!=t&&n)for(var i of n(t))e.indexOf(i)<0&&s.call(t,i)&&(a[i]=t[i]);return a},d=(t,e,a)=>("symbol"!=typeof e&&(e+=""),e in t?o(t,e,{enumerable:!0,configurable:!0,writable:!0,value:a}):t[e]=a),c=(t,e,a)=>{if(!e.has(t))throw TypeError("Cannot "+a)},u=(t,e,a,i)=>(c(t,e,"write to private field"),i?i.call(t,a):e.set(t,a),a),p="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAKAAAACCCAMAAAD2dTMiAAAABlBMVEX////a2vL9uN09AAAA3UlEQVR4nO3VIQ4AQRACwd3/f/rUatSlSWjUOCqYOeflll8FBIECu68CgsB/rlue/gXxYoEC6WKB80D6U6T0L4gXCxRIFwucB9KfIqV/QbxYoEC6WOA8kP4UKf0L4sUCBdLFAueB9KdI6V8QLxYokC4WOA+kP0VK/4J4sUCBdLHAeSD9KVL6F8SLBQqkiwXOA+lPkdK/IF4sUCBdLHAeSH+KlP4F8WKBAuligfNA+lOk9C+IFwsUSBcLnAfSnyKlf0G8WKBAuljgPJD+FCn9C+LFAgXSxQLngfSnSPkAtCAPcZpa2JAAAAAASUVORK5CYII=";const m=navigator.userAgent,g={isFF:m.indexOf("Firefox")>-1,isChrome:m.indexOf("Chrome")>-1,isIE:m.indexOf("MSIE")>-1,isEdge:m.indexOf("Windows NT 6.1; Trident/7.0;")>-1,isMobile:/mobile/i.test(m)},f=g.isMobile,y=f?"touchmove":"mousemove",x=f?"touchstart":"mousedown",v=f?"touchend":"mouseup",w=f?"touchend":"mouseout",A=t=>t.touches&&!f&&/mobile/i.test(navigator.userAgent)||(t.touches||!f||/mobile/i.test(navigator.userAgent))&&f?t.touches[0]:t,{floor:E,random:M,sqrt:T}=Math;let C=-1,b=0;function S(){let t=Date.now(),e=t-t%1e3;if(C==e){if(999==b){for(;C==e;)t=Date.now(),e=t-t%1e3;b=0}return(e+ ++b).toString(36)}return b=0,C=e,e.toString(36)}const z=function(t,e,a){let i=document.createElement(t);return L(i,a),e.appendChild(i),i},N=function(t,e,a,i){let o=document.createElementNS(t,e);return L(o,i),a.appendChild(o),o},L=function(t,e){let a=arguments,i=a.length;if(3==i){let e=a[1],i=a[2];e&&(i?t.setAttribute(e,i):t.removeAttribute(e))}else{if(!e)return;for(let a in e){let i=e[a];i?t.setAttribute(a,i):t.removeAttribute(a)}}},k=function(t){if("string"==typeof t)return t;if(!Array.isArray(t))return null;let e=[];for(let a of t){let[t,i,o]=a;e.push(t),e.push(i||0),o&&(e.push(","),e.push(o||0))}return e.join("")},D=(t,e,a)=>{let i=`on${e}`;t.addEventListener?t.addEventListener(e,a):t.attachEvent?t.attachEvent(i,a):t[i]=a},I=t=>{t.__touchstart||(D(t,"touchstart",(e=>{t.__touchstart_time=(new Date).getTime()})),t.__touchstart=!0)},H=(t,e)=>{I(t),D(t,"touchend",(a=>{(new Date).getTime()-t.__touchstart_time<1500&&e(a)})),D(t,"click",e)},O=(t,e)=>{f?H(t,(a=>{let i=(new Date).getTime();t.__lastTouchTime?(i-t.__lastTouchTime<200&&e(a),t.__lastTouchTime=i):t.__lastTouchTime=i})):D(t,"dblclick",e)},R=(t,e)=>{if(window.navigator.msSaveOrOpenBlob)window.navigator.msSaveBlob(t,e);else{const a=window.URL.createObjectURL(new Blob([t])),i=document.createElement("a");let o=document.body;i.style.display="none",i.download=e,i.href=a,o.appendChild(i),i.click(),o.removeChild(i),URL.revokeObjectURL(a)}},P=(t,e,a,i)=>{let o=t-a,r=e-i;return T(o*o+r*r)},B=t=>{t.preventDefault(),t.stopPropagation()},V="http://www.w3.org/2000/svg",j="http://www.w3.org/1999/xlink",Y=(t,e)=>{if(!e)return;let a=t.dataset.id,i=`${a}${e}`,o=t.querySelector("marker[id='"+i+"']");o||(o=z("marker",t.querySelector("defs"),{id:i}),o=t.querySelector("marker[id='"+i+"']"),o.outerHTML=`<marker id="${i}" fill="${e}" markerHeight="5" markerWidth="5" orient="auto" refX="2.5" refY="2.5"><use xlink:href="#${a}path" transform="rotate(180 2.5 2.5) scale(1,1)" stroke-width="1" stroke="none"></use></marker>`),i=`connect-condition-${e}`,o=t.querySelector("marker[id='"+i+"']"),o||(o=z("marker",t.querySelector("defs"),{id:i}),o=t.querySelector("marker[id='"+i+"']"),o.outerHTML=`<marker id="${i}" viewBox="0 0 20 20" refX="-15" refY="10" markerWidth="10" markerHeight="10" orient="auto"><path d="M 0 10 L 8 6 L 16 10 L 8 14 Z" fill="#fff" stroke="${e}"  stroke-width="1"></path></marker>`)};function U(t,e){let a=e.length;if(0==a)return t;let i=e[0],o=e[1];if(!i)return null;if("string"==typeof i)return 2==a?t[i]=o:t[i];if("object"==typeof i&&1==a){for(let e in i)t[e]=i[e];return i}return null}const X=["x","y","width","height","cursor","text-anchor","font-family","font-size","font-style","left","top","width","height","opacity","color","borderColor","max-width","max-height"];function G(t,e,a){this.isSvg()||!X.includes(e)?a?t.setAttribute(e,a.toString()):t.removeAttribute(e):K(t,e,a)}function K(t,e,a){let i="string"==typeof a?a:`${a}px`,o=t.style;switch(e){case"x":o.left=i;break;case"y":o.top=i;break;case"width":case"height":o[e]=i;break;default:o[e]=a}}function Q(t,e){let a={};for(let i in e){let o=t.data(i);if(!o){let a=e[i],r=typeof a;o="object"==r?{}:"function"==r?a(t):a}a[i]=o}return a}function Z(t,e,a){for(let i in e){let o=a[i];if(!o){let a=e[i],r=typeof a;o="object"==r?{}:"function"==r?a(t):a}t.data(i,o)}}class q{constructor(t,e){d(this,"_id"),d(this,"_type"),d(this,"_node"),d(this,"_datas"),d(this,"_attrs"),this._type=e,this._node=t,this._datas={},this._attrs={},this.id=S()}get id(){return this._id}set id(t){this._id=t,L(this.node,{"data-id":t})}get type(){return this._type}get text(){return this.data("text")}get icon(){return this.data("icon")}get name(){let t=this.data("text");return t&&t.attr("text")}get uuid(){return this.data("uuid")}set name(t){let e=this.data("text");e&&e.attr("text",t)}get node(){return this._node}get datas(){return this._datas}get attrs(){return this._attrs}get meta(){let t=this.data("meta");return t||this.data("meta",t={}),t}isSvg(){return!1}repaint(){let t=this.node.parentNode;t&&(t.removeChild(this.node),t.appendChild(this.node))}isGateway(){return!1}isPath(){return!1}isText(){return!1}isTask(){return!1}drag(t,e,a){let i=this,o={};const r=e=>{if("function"==typeof t){const{pageX:a,pageY:r}=A(e);let{pageX:n,pageY:s}=o;t.call(i,a-n,r-s,e)}},n=t=>{"function"==typeof a&&a.call(i,t),delete o.pageX,delete o.pageY,document.removeEventListener(y,r),document.removeEventListener(v,n)};let s=function(t){(t=>{const{pageX:a,pageY:r}=A(t);Object.assign(o,{pageX:a,pageY:r}),"function"==typeof e&&e.call(i,t)})(t),document.addEventListener(y,r),document.addEventListener(v,n),t.stopPropagation(),t.preventDefault()};return this.mousedownFn=s,D(this.node,x,s),this}undrag(){((t,e,a)=>{let i=`on${e}`;t.removeEventListener?t.removeEventListener(e,a):t.deachEvent?t.deachEvent(i,a):t[i]=null})(this.node,x,this.mousedownFn)}click(t){let e=this;return H(this.node,(a=>{t.call(e,a)})),this}dblclick(t){let e=this;return O(this.node,(a=>{t.call(e,a)})),this}mouseover(t){let e=this;return D(this.node,y,(a=>{t.call(e,a)})),this}mouseout(t){let e=this;return D(this.node,w,(a=>{t.call(e,a)})),this}hover(t,e){return"function"==typeof t&&this.mouseover(t),"function"==typeof e&&this.mouseout(e),this}longtap(t){let e=this;var a,i;return a=this.node,i=a=>{t.call(e,a)},f?(I(a),D(a,"touchend",(t=>{(new Date).getTime()-a.__touchstart_time>=1500&&i(t)}))):console.warn("longtap not supported for PC"),this}hide(){return K(this.node,"display","none"),this.hidden=!0,this}show(){return K(this.node,"display",null),this.hidden=!1,this}isHidden(){return this.hidden}attr(){let t=arguments,e=t.length,a=t[0],i=t[1],o=typeof a,r=!1;if(1==e){if("object"==o&&a){r=!0;for(let t in a){let e=a[t];G.call(this,this.node,t,e)}}}else e>1&&"string"==o&&(G.call(this,this.node,a,i),r=!0);let n=U(this.attrs||(this._attrs={}),t);return r?this:n}data(){let t=arguments,e=t[0],a=t.length>1||e&&"object"==typeof e,i=U(this.datas||(this._datas={}),t);return a?this:i}removeData(t){delete this.datas[t]}remove(){if(this.removed)return;let t;(t=this.data("text"))&&"function"==typeof t.remove&&t.remove(),(t=this.data("icon"))&&"function"==typeof t.remove&&t.remove(),this.node.remove(),this._attrs=null,this._datas=null,this.removed=!0}toString(){return"id = "+this.id+", name = "+(this.name||"")}}class F extends q{constructor(t,e){super(t,e)}get handler(){let t=this.data("handler");return t||this.data("handler",t={}),t}get customType(){return this.data("customType")}get nodeType(){return this.data("nodeType")}set nodeType(t){if(!this.supportedType(t))throw new Error(`nodeType '${t}' not supported for type '`+this.type+"'");this.data("nodeType",t),this.updateTypeView()}get status(){return this.data("status")}set status(t){this.data("status",t)}updateTypeView(){}supportedType(t){return!0}}class W extends F{constructor(t){super(t,"html")}updateHTML(t){this.node.innerHTML=t}}class J extends W{constructor(t){super(t)}get gateway(){return this.data("gateway")}set gateway(t){if(!["XOR","OR","AND"].includes(t))throw new Error(`gateway '${t}' not supported`);this.data("gateway",t),this.setHtmlType(t.toLowerCase())}isGateway(){return!0}}class $ extends q{constructor(t,e){super(t,"html"),d(this,"_nowrap"),this._nowrap=e,Object.assign(t.style,{transform:"translate(-50%, -50%)",textAlign:"center",overflow:"hidden",boxSizing:"border-box"})}isText(){return!0}setText(t){return this.node.innerHTML=`<span title="${t}" style="word-break: break-all;font-size: 12px;">${t}</span>`,Object.assign(this.attrs,{text:t}),this._updateWhiteSpace(),this}setWidth(t){this.attr("width",t)}_updateWhiteSpace(){let t=this.node;this._nowrap?Object.assign(t.style,{textOverflow:"ellipsis",overflow:"hidden",whiteSpace:"nowrap"}):Object.assign(t.style,{textOverflow:null,overflow:null,whiteSpace:null})}attr(){let t=arguments,[e,a]=t,i=t.length;if("text"==e&&"string"==typeof a)return this.setText(a);if(1==i&&"object"==typeof e){let{text:t,width:a,height:i}=e,o=h(e,["text","width","height"]);return t&&this.setText(t),super.attr(l({},o))}return super.attr(...t)}}class _ extends q{constructor(t,e){super(t,e)}isSvg(){return!0}}class tt extends F{constructor(t,e){super(t,e)}isSvg(){return!0}}class et extends tt{constructor(t){super(t,"rect")}isTask(){return!0}supportedType(t){return["Business","Service","Script","Manual","Message"].includes(t)}updateTypeView(){let t=this.icon;console.log(t),console.log(this.nodeType),t&&t.setHtmlType(this.nodeType.toLowerCase())}get asynchronous(){return this.handler.asynchronous}set asynchronous(t){this.handler.asynchronous=!!t}get timeout(){return this.handler.timeout}set timeout(t){this.handler.timeout=Number(t)}get delay(){return this.handler.delay}set delay(t){this.handler.delay=Number(t)}get iterate(){return this.handler.iterate}set iterate(t){this.handler.iterate=Number(t)}get policy(){return this.handler.policy}set policy(t){if(!["Stop","Continue"].includes(t))throw new Error(`policy ${t} not supported`);this.handler.policy=t}get retryOnError(){return this.handler.retryOnError||!1}set retryOnError(t){this.handler.retryOnError=!!t}get retryCount(){return this.handler.retryCount||0}set retryCount(t){this.handler.retryCount=Number(t)}get skip(){return this.handler.skip||!1}set skip(t){this.handler.skip=!!t}}class at extends _{constructor(t){super(t,"path"),L(t,{fill:"none"})}get pathStyle(){return this.data("pathStyle")}set pathStyle(t){if(t&&!["broken","straight","hv","qbc","cbc"].includes(t))throw new Error(`pathStyle ${t} not supported`);this.data("pathStyle",t)}get conditionType(){return this.data("conditionType")||"Always"}set conditionType(t){if(t){if(!["Always","Script","HandlerCall","Never"].includes(t))throw new Error(`conditionType '${t}' not supported`);this.data("conditionType",t)}this.updatePathView()}get script(){return this.data("script")}set script(t){this.data("script",t)}get isCondition(){let t=this.conditionType;return"Script"==t||"HandlerCall"==t}get from(){return this.data("from")}get to(){return this.data("to")}updatePathView(){let t=this.attr("stroke"),e=this.node.parentNode;Y(e,t);let a=this.conditionType,i=this.node.style;switch(a){case"Always":i.markerStart=null;break;case"Script":case"HandlerCall":i.markerStart=`url(#connect-condition-${t})`}}attr(){let t=arguments,[e,a]=t,i=t.length;if("path"==e&&"string"==typeof a)return super.attr("d",a);if("object"==typeof e&&e&&1==i){let{path:t}=e,a=h(e,["path"]);if(t){let e=k(t);return super.attr(l({d:e},a))}return super.attr(l({},a))}return super.attr(...t)}getPointAtLength(t){return this.node.getPointAtLength(t)}getBBox(){return this.node.getBBox()}isPath(){return!0}remove(){let{qbc_control:t,cbc_control1:e,cbc_control2:a}=this.data();return console.log("remove path ",t),"qbc"==this.pathStyle?t&&t.remove():"cbc"==this.pathStyle&&(e&&e.remove(),a&&a.remove()),super.remove()}}class it extends _{constructor(t){super(t,"image")}setHref(t){this.node.setAttributeNS(j,"xlink:href",t)}}class ot extends _{constructor(t){super(t,"circle")}}class rt extends _{constructor(t){super(t,"text")}setText(t){return this.node.innerHTML=`<tspan dy="4">${t}</tspan>`,Object.assign(this.attrs,{text:t}),this}attr(){let t=arguments,[e,a]=t,i=t.length;if("text"==e&&"string"==typeof a)return this.setText(a);if(1==i&&e&&"object"==typeof e){let{text:t}=e,a=h(e,["text"]);return t&&this.setText(t),super.attr(l({},a))}return super.attr(...t)}}let nt=0;var st={props:{actions:[],index:-1},methods:{addAction(t){let{undo:e,redo:a}=t||{};"function"==typeof e&&"function"==typeof a?(t.uid=++nt,this.actions.splice(++this.index,this.actions.length-this.index,t),this.actions.length>20&&(this.actions.shift(),--this.index)):console.error("Error: undo redo should be defined as function")},clearHistory(){this.actions=[],this.index=-1},undo:function(){let t;(t=this.actions[this.index])&&(t.undo(),--this.index)},redo:function(){let t;(t=this.actions[this.index+1])&&(++this.index,t.redo())}}};class lt extends class{get id(){return null}get node(){return null}get removed(){return!1}}{constructor(t,e,a){let i;super(),d(this,"_id"),d(this,"_node"),d(this,"_removed",!1),i="string"==typeof t?document.querySelector(dom):t;let o=S(),r=N(V,"svg",i,{width:e||"100%",height:a||"100%",version:"1.1",xmlns:V,"xmlns:xlink":j,"data-id":o,style:"overflow: visible; position: relative; user-select: none; cursor: default;"});N(V,"defs",r),this._id=o,this._node=r}get id(){return this._id}get node(){return this._node}get removed(){return this._removed}rect(t,e,a,i,o){let r={x:t,y:e,width:a,height:i,rx:o,ry:o},n=N(V,"rect",this.node),s=new et(n);return s.attr(r),s}path(t){let e=N(V,"path",this.node),a=new at(e);return a.attr("d",k(t)),a}image(t,e,a,i,o,r){let n={x:e,y:a,width:i,height:o,rx:r,ry:r},s=N(V,"image",this.node,n),l=new it(s);return l.setHref(t),l}text(t,e){let a=N(V,"text",this.node,{x:t,y:e});return new rt(a)}circle(t,e,a){let i=N(V,"circle",this.node),o=new ot(i);return o.attr({x:t,y:e,r:a}),o}clear(){this.node.querySelectorAll("*[data-id]").forEach((t=>{t.remove()}))}remove(){this.clear(),this.node.remove(),this._node=null,this._removed=!0}}const ht={linkName:"",nodeName:"节点名称",nodeBackgroundColor:"#fff",nodeStrokeWith:"3",themeColor:"#409eff"};var dt={grid:!0,background:null,width:"100%",height:"100%",menu:!0,toolbar:!1,enablePropertyPop:!0,pathStyle:"broken",maxPathTextWidth:100,nowrap:!1,panable:!0,editable:!0,uuid:!0,disableAlignLine:!1,enableHistory:!0,defaultConditionType:"Always",zoomable:!0,overviewOffsetWidth:60,overviewOffsetHeight:20,textEditOnClick:!1,textEditOnDblClick:!0,excludeTypes:[],alertMessage(t){console.warn("alertMessage",t)},mouseover(t){},mouseoverElement(t,e){},mouseoutElement(t,e){},clickElement(t,e){},dblclickElement(t,e){},clickBlank(t){},dblclickBlank(t){},onContextMenu(t){},onConnectCreated(t){},onNodeCreated(t){},onRemoveElement(t){},settings:ht};const{isChrome:ct}=g,ut=`xmlns="${V}"`,pt="width: 100%;height: 100%;",{sqrt:mt,min:gt,max:ft,abs:yt,sin:xt,cos:vt,tan:wt,atan:At}=Math,{removeEventListener:Et,addEventListener:Mt}=document,{assign:Tt,keys:Ct,values:bt}=Object,St=t=>`<svg style="${pt}vertical-align: middle;fill: currentColor;overflow: hidden;" viewBox="0 0 1024 1024" version="1.1" ${ut}>${t}</svg>`,zt={empty:"",select:St('<path d="M736.21333333 638.08l69.76-54.08c5.22666667-4.05333333 8-10.45333333 7.46666667-17.06666667-0.53333333-6.61333333-4.37333333-12.48-10.24-15.57333333L450.02666666 361.38666666c-6.82666667-3.62666667-15.04-2.98666667-21.12 1.70666667-6.08 4.69333333-8.85333333 12.58666667-7.04 20.05333333l95.57333334 389.54666667c1.6 6.4 6.29333333 11.62666667 12.48 13.76 6.18666667 2.13333333 13.12 1.06666667 18.34666666-2.98666667l69.86666667-54.08L769.06666666 924.37333333c3.2 4.05333333 7.78666667 6.72 12.90666667 7.36 0.85333333 0.10666667 1.6 0.10666667 2.45333333 0.10666667 4.26666667 0 8.53333333-1.38666667 11.94666667-4.05333334l87.25333333-67.52c8.53333333-6.61333333 10.02666667-18.88 3.52-27.30666666L736.21333333 638.08zM178.24 282.02666666l-31.25333334-21.01333333c-14.61333333 21.76-27.30666667 44.90666667-37.86666666 68.90666667l34.56 15.14666666c9.6-21.86666667 21.22666667-43.09333333 34.56-63.04zM549.54666666 103.89333333l3.2-37.54666667c-26.02666667-2.24-52.69333333-2.13333333-78.50666666 0.21333334l3.41333333 37.54666666c23.57333333-2.13333333 48-2.24 71.89333333-0.21333333zM120.74666666 413.22666666l-36.69333333-8.74666666c-6.08 25.38666667-9.92 51.52-11.30666667 77.76l37.65333334 2.13333333c1.38666667-24 4.8-47.89333333 10.34666666-71.14666667zM112.64 556.26666666l-37.44 4.48c3.09333333 26.02666667 8.64 51.94666667 16.32 76.90666667l36.05333333-11.09333333c-7.04-22.82666667-12.05333333-46.50666667-14.93333333-70.29333334zM919.36 327.46666666c-10.66666667-23.89333333-23.46666667-46.93333333-38.29333334-68.58666666l-31.14666666 21.22666666c13.44 19.84 25.28 40.96 34.98666666 62.82666667l34.45333334-15.46666667zM831.36 197.97333333c-18.34666667-18.77333333-38.4-35.94666667-59.62666667-51.09333333L749.86666666 177.6c19.52 13.86666667 37.86666667 29.65333333 54.61333334 46.82666666l26.88-26.45333333zM703.89333333 107.09333333c-23.68-11.2-48.53333333-20.37333333-73.81333333-27.09333333l-9.81333334 36.37333333c23.14666667 6.18666667 45.86666667 14.61333333 67.52 24.85333333l16.10666667-34.13333333zM277.76 178.98666666l-22.08-30.50666666c-21.22666667 15.36-41.17333333 32.64-59.41333334 51.52l27.09333334 26.13333333c16.74666667-17.28 35.09333333-33.06666667 54.4-47.14666667zM348.90666666 918.08c24.32 9.81333333 49.6 17.49333333 75.2 22.82666666l7.68-36.90666666c-23.46666667-4.90666667-46.61333333-11.94666667-68.8-20.90666667l-14.08 34.98666667zM503.25333333 912.42666666l-1.06666667 37.65333334c4.16 0.10666667 8.42666667 0.21333333 12.58666667 0.21333333 21.97333333 0 44.16-1.6 65.81333333-4.90666667l-5.54666666-37.22666666c-23.57333333 3.52-47.89333333 4.90666667-71.78666667 4.26666666zM944.85333333 401.81333333l-36.58666667 8.96c5.65333333 23.14666667 9.28 47.14666667 10.77333334 71.04l37.65333333-2.34666667c-1.70666667-26.13333333-5.65333333-52.26666667-11.84-77.65333333zM396.8 80.64c-25.28 6.93333333-50.02666667 16.21333333-73.6 27.62666666l16.32 33.92c21.54666667-10.34666667 44.26666667-18.88 67.30666666-25.17333333L396.8 80.64zM121.17333333 710.4c11.94666667 23.25333333 26.13333333 45.54666667 42.13333333 66.34666666l29.86666667-22.93333333c-14.61333333-18.98666667-27.52-39.46666667-38.50666667-60.69333333L121.17333333 710.4zM216.42666666 834.66666666c19.30666667 17.70666667 40.42666667 33.70666667 62.50666667 47.68l20.05333333-31.89333333c-20.26666667-12.8-39.46666667-27.41333333-57.17333333-43.62666667l-25.38666667 27.84z"></path>'),start:St('<path d="M400.43759 732.273456a71.33239 71.33239 0 0 0 34.157473 8.810938 75.556813 75.556813 0 0 0 41.157944-12.069778l219.790665-144.837341a85.574729 85.574729 0 0 0 38.502593-72.418671 83.402169 83.402169 0 0 0-37.295615-70.608203l-221.842527-144.837341a72.41867 72.41867 0 0 0-74.591231-3.741631 84.488449 84.488449 0 0 0-41.761433 75.436115v289.674681a84.488449 84.488449 0 0 0 41.882131 74.591231z m42.002829-82.315889V374.163131l207.600188 135.664309v0.965582a13.156058 13.156058 0 0 1 0 2.293258z"></path><path d="M149.989688 874.093352a509.948138 509.948138 0 1 0-109.714286-162.700613 513.206978 513.206978 0 0 0 109.714286 162.700613zM84.571489 512a428.11504 428.11504 0 1 1 427.511551 428.11504A428.597831 428.597831 0 0 1 84.571489 512z" ></path>'),end:St('<path d="M512 0c282.752 0 512 229.248 512 512s-229.248 512-512 512S0 794.752 0 512 229.248 0 512 0z m0 85.333333C276.352 85.333333 85.333333 276.352 85.333333 512s191.018667 426.666667 426.666667 426.666667 426.666667-191.018667 426.666667-426.666667S747.648 85.333333 512 85.333333z m85.333333 256a85.333333 85.333333 0 0 1 85.333334 85.333334v170.666666a85.333333 85.333333 0 0 1-85.333334 85.333334h-170.666666a85.333333 85.333333 0 0 1-85.333334-85.333334v-170.666666a85.333333 85.333333 0 0 1 85.333334-85.333334h170.666666z"></path>'),xor:St('<path d="M485.871492 47.174338c-9.168729-0.008066-18.33651 3.392487-25.137616 10.224908L57.381424 460.755494c-13.603161 13.602212-13.540056 36.674443 0.062631 50.276655l403.288872 403.286025c13.603161 13.606956 36.673969 13.66674 50.276655 0l403.352926-403.350079c13.602686-13.601737 13.539581-36.674918-0.064054-50.27713L511.009107 57.399246c-6.801106-6.801106-15.969361-10.217316-25.137615-10.224908z m-0.030841 59.805036l378.943153 378.946-378.943153 378.94173-378.943153-378.94173 378.943153-378.946zM344.31891 317.829311c-0.006643 0-4.560641 0.872083-4.564436 0.872083-0.004745 0-3.864114 2.615773-3.866961 2.615773l-14.581525 14.584847c-0.004745 0-2.661797 3.902546-2.663696 3.94857 0 0.004745-0.824161 4.498011-0.823686 4.498011 0 0.004745 0.886317 4.452936 0.88774 4.452935 0 0.004745 2.532741 3.94857 2.535588 3.94857l133.188084 133.184763-133.12403 133.12498v-0.041754c0 0.004745-2.661797 3.947621-2.663696 3.947621 0 0.004745-0.823686 4.499434-0.823686 4.499434 0 0.009489 0.886317 4.452936 0.88774 4.452936 0 0 2.533215 3.901597 2.535588 3.947621l14.582474 14.579627c0.004745 0.004745 3.990798 2.617197 3.994119 2.617197 0.004745 0 4.434431 0.872083 4.438227 0.872083 0.004745 0 4.497536-0.825584 4.500858-0.825584 0.004745 0 3.928642-2.663695 3.931014-2.663696l133.125929-133.128775 133.154871 133.156769c0.004745 0.004745 3.991273 2.617197 3.99412 2.617197 0.004745 0 4.434431 0.872083 4.438226 0.872083 0.004745 0 4.498011-0.827008 4.501807-0.827008 0.004745 0 3.926744-2.661797 3.929116-2.661797l14.582949-14.580577c0.004745-0.004745 2.597743-3.855573 2.600116-3.855573 0-0.004745 0.88774-4.545458 0.88774-4.591007 0-0.004745-0.886791-4.452936-0.888689-4.452936 0 0-2.59632-3.994119-2.599167-3.994119l-133.140163-133.142535 133.141112-133.139214c0.004745 0 2.596794-3.856048 2.599167-3.856048 0-0.004745 0.88774-4.544509 0.88774-4.544509 0-0.009489-0.886317-4.452936-0.887266-4.452935 0-0.004745-2.659899-3.94857-2.662746-3.94857l-14.582949-14.584372c-0.004745 0-3.863639-2.616248-3.86696-2.616248-0.004745 0-4.433957-0.873032-4.437753-0.873032-0.004745 0-4.561116 0.873032-4.564436 0.873032-0.004745 0-3.864588 2.616248-3.866961 2.616248l-133.143484 133.143484-133.203268-133.207538v-0.041754c-0.004745 0-3.927693-2.5242-3.931014-2.5242-0.004745 0-4.431584-0.871608-4.436804-0.872083h-0.000949z"></path>'),or:St('<path d="M485.871492 47.174338c-9.168729-0.008066-18.33651 3.392487-25.137616 10.224908L57.381424 460.755494c-13.603161 13.602212-13.540056 36.674443 0.062631 50.276655l403.288872 403.286025c13.603161 13.606956 36.673969 13.66674 50.276655 0l403.352926-403.350079c13.602686-13.601737 13.539581-36.674918-0.064054-50.27713L511.009107 57.399246c-6.801106-6.801106-15.969361-10.217316-25.137615-10.224908z m-0.030841 59.805036l378.943153 378.946-378.943153 378.94173-378.943153-378.94173 378.943153-378.946z m-8.957589 186.164969l-0.000949 0.197381c0 0.004745-3.969921 0.735434-3.971345 0.735435-0.004745 0-3.251568 2.203456-3.25394 2.203455-0.004745 0-2.260393 3.350733-2.262291 3.350734 0 0.004745-0.763903 3.994119-0.765326 3.994119V439.440713l-96.147347-96.147348h0.004745c-0.004745 0-3.414787-2.203456-3.416211-2.203456-0.005694 0-3.803856-0.771969-3.832798-0.777188-0.037009 0.005694-3.945249 0.732113-3.947147 0.732113-0.004745-0.004745-3.352157 2.249005-3.35358 2.249006l-12.636658 12.629066-2.306416 3.397231c-0.004745 0-0.713608 3.901597-0.713609 3.901597 0 0.004745 0.765801 3.856048 0.766275 3.856048 0 0.004745 2.188747 3.397232 2.189696 3.397232l96.099901 96.101323H303.419279l-0.026571-0.032264c0 0.004745-3.953315 0.871608-3.955212 0.871608-0.004745 0.004745-3.250619 2.20393-3.252992 2.20393v-0.023249c-0.004745 0.004745-2.259918 3.352157-2.261816 3.352157-0.004745 0-0.764852 3.993171-0.766275 3.99317v17.871526s0.759158 4.040143 0.786677 4.040143c0 0.009489 2.251378 3.259634 2.251852 3.259635 0.004745 0 3.271022 2.203456 3.271971 2.203455 0.004745 0.009489 3.970396 0.872083 3.971819 0.872083h135.870285L343.2912 601.205873v-0.03606c-0.004745 0.004745-2.305942 3.397232-2.306891 3.397231-0.004745 0.004745-0.714557 3.901597-0.714557 3.901597 0 0.009015 0.767224 3.856048 0.767698 3.856048 0 0 2.196813 3.397232 2.189696 3.397232l12.638556 12.629066c0.004745 0.004745 3.453219 2.249005 3.454168 2.249005l3.847033 0.780509c0.005694 0 3.896852-0.734011 3.89875-0.734011 0 0 3.402451-2.295504 3.403874-2.295503l96.160633-96.160633v136.107996l-0.025147-0.026571c0 0.004745 0.787626 4.039194 0.786677 4.039195 0 0.004745 2.251378 3.259634 2.251378 3.259634 0.004745 0.004745 3.271022 2.203456 3.271971 2.203456 0 0 3.942402 0.826059 3.971819 0.872082l17.865832-0.004745c0.004745 0 4.048209-0.825584 4.050582-0.825584 0 0 3.250619-2.20393 3.252991-2.20393 0.004745-0.004745 2.24948-3.25916 2.251853-3.25916 0 0 0.774816-4.040143 0.776713-4.040143v-136.030657l96.091834 96.087564c-0.004745 0.009489 3.452271 2.249005 3.454168 2.249005 0.004745 0.004745 3.846084 0.735434 3.847508 0.735435 0.005694 0 3.896378-0.735434 3.89875-0.735435 0 0 3.401976-2.295504 3.404823-2.295503l12.632862-12.637132c0.004745 0 2.239041-3.352157 2.26751-3.352157 0-0.009489 0.778137-3.947621 0.759157-3.99317 0-0.004745-0.767698-3.856997-0.768647-3.856997-0.005694 0.004745-2.236194-3.442781-2.265612-3.442781l-95.851276-95.857444h135.679072c0.004745 0.004745 4.048209-0.826533 4.049633-0.826533 0.004745 0 3.251568-2.20393 3.252991-2.203931 0.004745-0.009015 2.249005-3.25916 2.251852-3.259159 0 0 0.774816-4.039194 0.776714-4.039195v-17.867255c0-0.004745-0.771494-3.947621-0.770071-3.99412-0.004745-0.004745-2.240465-3.350733-2.267984-3.350733-0.004745-0.004745-3.271496-2.20393-3.27292-2.20393 0 0.004745-4.036822-0.871608-4.03872-0.825584h-136.09044l96.283047-96.280201c0.004745-0.004745 2.238092-3.351682 2.266561-3.351682 0-0.009489 0.77956-3.947621 0.77956-3.947621 0-0.004745-0.766749-3.856522-0.767224-3.856522-0.004745-0.004745-2.299299-3.397232-2.300248-3.397231l-12.635709-12.638082c-0.004745 0-3.341244-2.249005-3.342667-2.249005-0.004745-0.004745-3.846084-0.734485-3.848456-0.734485-0.005694 0-3.960432 0.734011-3.961855 0.734011-0.004745 0.004745-3.352631 2.249005-3.35358 2.249005l-96.162531 96.161582V303.47838c0-0.004745-0.771494-3.948096-0.770071-3.994119-0.004745-0.009489-2.239516-3.351208-2.239516-3.351208-0.004745-0.004745-3.271496-2.203456-3.272445-2.203456-0.004745 0-4.048209-0.780509-4.049158-0.780509l-17.868679-0.004745z m-113.645937 47.167429l0.016132-0.002372h-0.029892l0.014235 0.002847z"></path>'),and:St('<path d="M485.871492 47.174338c-9.168729-0.008066-18.33651 3.392487-25.137616 10.224908L57.381424 460.755494c-13.603161 13.602212-13.540056 36.674443 0.062631 50.276655l403.288872 403.286025c13.603161 13.606956 36.673969 13.66674 50.276655 0l403.352926-403.350079c13.602686-13.601737 13.539581-36.674918-0.064054-50.27713L511.009107 57.399246c-6.801106-6.801106-15.969361-10.217316-25.137615-10.224908z m-0.030841 59.805036l378.943153 378.946-378.943153 378.94173-378.943153-378.94173 378.943153-378.946z m-10.34495 156.490911c0 0.004745-4.581992 0.872083-4.584365 0.872082-0.004745 0-3.752138 2.525623-3.754984 2.525624-0.004745 0.004745-2.608182 3.856048-2.610555 3.856047 0 0.004745-0.882521 4.590533-0.883944 4.590533V463.610402H275.283938l-0.03179-0.037009c0 0.004745-4.563488 1.010154-4.56586 1.010155-0.004745 0.004745-3.751663 2.525149-3.754036 2.525149l0.003796-0.046499c-0.004745 0.004745-2.608182 3.856048-2.610554 3.856048-0.004745 0-0.882996 4.591007-0.884893 4.591007v20.625371s0.876827 4.641776 0.908142 4.673566c0 0.004745 2.599167 3.764474 2.599167 3.764474 0.004745 0.004745 3.775387 2.525149 3.776811 2.525149 0.004745 0.004745 4.581518 1.009206 4.583415 1.009206h188.355615v188.264041l-0.029892-0.02752c0 0.004745 0.909092 4.673566 0.908617 4.673566 0 0.004745 2.598218 3.764 2.598218 3.764 0.004745 0.004745 3.776811 2.5242 3.777285 2.5242 0 0 4.550203 0.964131 4.584365 1.010629l20.6211-0.004745c0.005219 0 4.671668-0.964131 4.674515-0.964131 0 0 3.752138-2.5242 3.754985-2.5242 0.004745-0.004745 2.595371-3.765423 2.598218-3.765423 0 0 0.895332-4.659332 0.896755-4.659331V508.125523h188.309116c0.005219 0.004745 4.672142-0.963656 4.67404-0.963656 0.004745 0 3.753087-2.525149 3.754985-2.525149 0.004745-0.004745 2.595846-3.764474 2.598692-3.764474 0 0 0.894857-4.659332 0.896756-4.659332v-20.6211c0-0.004745-0.890587-4.545458-0.889164-4.591008-0.004745-0.004745-2.585882-3.856048-2.618146-3.856047-0.004745-0.004745-3.775862-2.5242-3.77681-2.5242 0 0.004745-4.659332-1.010629-4.661704-0.964605h-188.289188V275.364866c0-0.004745-0.891062-4.544034-0.889638-4.590059-0.004745-0.004745-2.585882-3.856997-2.585882-3.856996-0.004745-0.004745-3.774913-2.5242-3.775862-2.5242-0.004745 0-4.672617-0.918581-4.674514-0.918582l-20.624897-0.004744z"></path>'),join:`<svg style="${pt}transform: rotate(45deg); vertical-align: middle;fill: currentColor;overflow: hidden;" viewBox="0 0 1024 1024" version="1.1" ${ut}><path d="M485.871492 47.174338c-9.168729-0.008066-18.33651 3.392487-25.137616 10.224908L57.381424 460.755494c-13.603161 13.602212-13.540056 36.674443 0.062631 50.276655l403.288872 403.286025c13.603161 13.606956 36.673969 13.66674 50.276655 0l403.352926-403.350079c13.602686-13.601737 13.539581-36.674918-0.064054-50.27713L511.009107 57.399246c-6.801106-6.801106-15.969361-10.217316-25.137615-10.224908z m-0.030841 59.805036l378.943153 378.946-378.943153 378.94173-378.943153-378.94173 378.943153-378.946zM344.31891 317.829311c-0.006643 0-4.560641 0.872083-4.564436 0.872083-0.004745 0-3.864114 2.615773-3.866961 2.615773l-14.581525 14.584847c-0.004745 0-2.661797 3.902546-2.663696 3.94857 0 0.004745-0.824161 4.498011-0.823686 4.498011 0 0.004745 0.886317 4.452936 0.88774 4.452935 0 0.004745 2.532741 3.94857 2.535588 3.94857l133.188084 133.184763-133.12403 133.12498v-0.041754c0 0.004745-2.661797 3.947621-2.663696 3.947621 0 0.004745-0.823686 4.499434-0.823686 4.499434 0 0.009489 0.886317 4.452936 0.88774 4.452936 0 0 2.533215 3.901597 2.535588 3.947621l14.582474 14.579627c0.004745 0.004745 3.990798 2.617197 3.994119 2.617197 0.004745 0 4.434431 0.872083 4.438227 0.872083 0.004745 0 4.497536-0.825584 4.500858-0.825584 0.004745 0 3.928642-2.663695 3.931014-2.663696l133.125929-133.128775 133.154871 133.156769c0.004745 0.004745 3.991273 2.617197 3.99412 2.617197 0.004745 0 4.434431 0.872083 4.438226 0.872083 0.004745 0 4.498011-0.827008 4.501807-0.827008 0.004745 0 3.926744-2.661797 3.929116-2.661797l14.582949-14.580577c0.004745-0.004745 2.597743-3.855573 2.600116-3.855573 0-0.004745 0.88774-4.545458 0.88774-4.591007 0-0.004745-0.886791-4.452936-0.888689-4.452936 0 0-2.59632-3.994119-2.599167-3.994119l-133.140163-133.142535 133.141112-133.139214c0.004745 0 2.596794-3.856048 2.599167-3.856048 0-0.004745 0.88774-4.544509 0.88774-4.544509 0-0.009489-0.886317-4.452936-0.887266-4.452935 0-0.004745-2.659899-3.94857-2.662746-3.94857l-14.582949-14.584372c-0.004745 0-3.863639-2.616248-3.86696-2.616248-0.004745 0-4.433957-0.873032-4.437753-0.873032-0.004745 0-4.561116 0.873032-4.564436 0.873032-0.004745 0-3.864588 2.616248-3.866961 2.616248l-133.143484 133.143484-133.203268-133.207538v-0.041754c-0.004745 0-3.927693-2.5242-3.931014-2.5242-0.004745 0-4.431584-0.871608-4.436804-0.872083h-0.000949z"></path></svg>`,manual:St('<path d="M512 0C230.4 0 0 230.4 0 512s230.4 512 512 512 512-230.4 512-512S793.6 0 512 0zM512 960c-249.6 0-448-198.4-448-448 0-249.6 198.4-448 448-448 249.6 0 448 198.4 448 448C960 761.6 761.6 960 512 960z"></path><path d="M704 320c0-108.8-83.2-192-192-192C403.2 128 320 211.2 320 320s83.2 192 192 192C620.8 512 704 428.8 704 320zM512 448C441.6 448 384 390.4 384 320c0-70.4 57.6-128 128-128 70.4 0 128 57.6 128 128C640 390.4 582.4 448 512 448z"></path><path d="M512 512c-179.2 0-320 115.2-320 256 0 19.2 12.8 32 32 32S256 787.2 256 768c0-108.8 115.2-192 256-192 140.8 0 256 83.2 256 192 0 19.2 12.8 32 32 32S832 787.2 832 768C832 627.2 691.2 512 512 512z"></path>'),business:St('<path d="M91.18976 196.096v631.808h841.62048V196.096H91.18976z m35.84 214.016H289.28v176.351232H127.02976V410.112zM325.12 410.112h571.85024v176.351232H325.12V410.112z m-198.09024 212.191232H289.28V792.064H127.02976v-169.760768z m198.09024 0h571.85024V792.064H325.12v-169.760768z"></path>'),businessTask:St('<path d="M219.9808 107.52C110.046208 107.52 20.48 196.708352 20.48 306.432v411.136c0 109.723648 89.566208 198.912 199.5008 198.912h584.0384c109.934592 0 199.5008-89.188352 199.5008-198.912v-411.136c0-109.723648-89.566208-198.912-199.5008-198.912H219.9808z m0 61.44h584.0384C881.094144 168.96 942.08 229.798912 942.08 306.432v411.136c0 76.633088-60.985856 137.472-138.0608 137.472H219.9808C142.905856 855.04 81.92 794.201088 81.92 717.568v-411.136C81.92 229.798912 142.905856 168.96 219.9808 168.96z m-36.82304 73.680896v389.05856h518.257152v-389.05856H183.158272z m22.070272 134.710272h474.118144v105.67168H327.20896v-105.41056h-22.06976v105.41056h-99.91168v-105.67168z m0 127.741952h99.911168v104.536064h-99.91168V505.09312z m121.980928 0h352.137216v104.536064H327.20896V505.09312z"></path><path d="M207.238144 267.552256v110.1696h471.880704V267.552256z"></path>'),service:St('<path d="M688 80v44.992a180.672 180.672 0 0 0-72.992 30.016l-33.024-31.008-44 47.008 32 29.984a180 180 0 0 0-28.992 71.008H496v64h44.992c4.672 26.528 15.168 50.752 30.016 72l-34.016 32.992 46.016 46.016 32.992-34.016a179.776 179.776 0 0 0 72 30.016V528h64v-44.992a180 180 0 0 0 71.008-28.992l29.984 32 47.008-44.032-31.008-32.96c15.072-21.44 25.28-46.24 30.016-73.024H944v-64h-44.992a179.776 179.776 0 0 0-30.016-72l30.016-30.016-45.024-44.992-29.984 30.016a179.776 179.776 0 0 0-72-30.016V80h-64z m32 106.016A117.248 117.248 0 0 1 838.016 304a117.248 117.248 0 0 1-118.016 118.016A117.248 117.248 0 0 1 601.984 304 117.248 117.248 0 0 1 720 185.984zM304.992 374.976l-59.008 24 23.04 57.984a221.12 221.12 0 0 0-75.04 74.016l-56.96-23.008-24 59.008 56.96 22.976a216.832 216.832 0 0 0-6.976 53.024c0 18.24 2.72 36.032 6.976 52.992l-56.96 23.008 24 58.976 56.96-22.976a219.872 219.872 0 0 0 75.008 74.976l-23.008 57.024 59.008 24 23.008-57.024a215.36 215.36 0 0 0 52.992 7.04c18.24 0 36.096-2.752 52.992-7.04l23.04 57.024 58.976-24-23.008-57.024a221.12 221.12 0 0 0 74.016-74.976l57.984 22.976 24-58.976-57.984-23.008c4.224-16.96 6.976-34.784 6.976-52.992 0-18.24-2.752-36.096-6.976-53.024l57.984-22.976-24-59.008-57.984 23.008a220 220 0 0 0-74.016-74.016l23.008-57.984-59.008-24-23.008 57.984a216.896 216.896 0 0 0-52.992-7.008c-18.24 0-36.032 2.784-52.992 7.04l-23.008-58.016z m76 115.008a152.096 152.096 0 0 1 152.992 152.96c0 85.248-67.776 154.016-152.96 154.016a153.792 153.792 0 0 1-154.016-153.984c0-85.216 68.8-153.024 153.984-153.024z"></path>'),message:St('<path d="M580.266667 733.866667c0 18.773333-30.72 34.133333-68.266667 34.133333s-68.266667-15.36-68.266667-34.133333h136.533334zM717.141333 716.8H306.858667c-14.336 0-26.624-8.874667-31.744-22.186667-5.12-13.653333-1.024-28.672 9.898666-37.888l52.906667-45.056c3.754667-3.413333 6.144-8.192 6.144-13.312V443.733333c0-94.208 75.434667-170.666667 167.936-170.666666s167.936 76.458667 167.936 170.666666v154.624c0 5.12 2.048 9.898667 6.144 13.312l52.906667 45.056c10.922667 9.216 15.018667 24.234667 9.898666 37.888-5.12 13.312-17.408 22.186667-31.744 22.186667zM512 307.2c-73.728 0-133.802667 61.098667-133.802667 136.533333v154.624c0 15.36-6.485333 29.354667-18.090666 39.253334L307.2 682.666667h409.941333v17.066666-17.066666l-53.248-45.056c-11.605333-9.557333-18.090667-23.893333-18.090666-39.253334V443.733333c0-75.434667-60.074667-136.533333-133.802667-136.533333z"></path><path d="M512 597.333333a16.657067 16.657067 0 0 1-15.018667-24.576l21.845334-43.690666H477.866667a17.749333 17.749333 0 0 1-14.677334-8.192 17.169067 17.169067 0 0 1-0.682666-16.725334l34.133333-68.266666c4.096-8.533333 14.336-11.946667 22.869333-7.509334 8.533333 4.096 11.946667 14.336 7.509334 22.869334l-21.845334 43.690666H546.133333c5.802667 0 11.264 3.072 14.677334 8.192 3.072 5.12 3.413333 11.264 0.682666 16.725334l-34.133333 68.266666c-3.072 5.802667-9.216 9.216-15.36 9.216z" ></path><path d="M512 1024C229.717333 1024 0 794.282667 0 512S229.717333 0 512 0s512 229.717333 512 512-229.717333 512-512 512z m0-975.189333C256.682667 48.810667 48.810667 256.682667 48.810667 512S256.682667 975.189333 512 975.189333 975.189333 767.317333 975.189333 512 767.317333 48.810667 512 48.810667z"></path><path d="M541.354667 301.738667c2.730667-4.778667 4.778667-10.24 4.778666-16.384 0-18.773333-15.36-34.133333-34.133333-34.133334s-34.133333 15.36-34.133333 34.133334c0 6.144 2.048 11.605333 4.778666 16.384h58.709334z"></path>'),reset:St('<path d="M790.2 590.67l105.978 32.29C847.364 783.876 697.86 901 521 901c-216.496 0-392-175.504-392-392s175.504-392 392-392c108.502 0 206.708 44.083 277.685 115.315l-76.64 76.64C670.99 257.13 599.997 225 521.5 225 366.032 225 240 351.032 240 506.5 240 661.968 366.032 788 521.5 788c126.148 0 232.916-82.978 268.7-197.33z"></path><path d="M855.58 173.003L650.426 363.491l228.569 32.285z"></path>'),imp:St('<path d="M746.666667 469.333333H554.666667V213.333333l42.666666 42.666667 51.2 51.2L746.666667 213.333333l59.733333 59.733334-98.133333 98.133333 21.333333 21.333333 38.4 34.133334 42.666667 42.666666h-64zM512 213.333333v85.333334H298.666667v426.666666h426.666666v-213.333333h85.333334v298.666667H213.333333V213.333333h298.666667z"></path>'),exp:St('<path d="M469.333333 162.133333h85.333334v469.333334c0 12.8-8.533333 21.333333-21.333334 21.333333h-42.666666c-12.8 0-21.333333-8.533333-21.333334-21.333333v-469.333334z"></path><path d="M315.733333 392.533333L285.866667 362.666667c-8.533333-8.533333-8.533333-21.333333 0-29.866667l211.2-211.2c8.533333-8.533333 21.333333-8.533333 29.866666 0l44.8 44.8-226.133333 226.133333c-8.533333 8.533333-21.333333 8.533333-29.866667 0z"></path><path d="M452.266667 166.4l44.8-44.8c8.533333-8.533333 21.333333-8.533333 29.866666 0l211.2 211.2c8.533333 8.533333 8.533333 21.333333 0 29.866667l-29.866666 29.866666c-8.533333 8.533333-21.333333 8.533333-29.866667 0L452.266667 166.4zM896 503.466667h-42.666667c-12.8 0-21.333333 8.533333-21.333333 21.333333v277.333333c0 12.8-8.533333 21.333333-21.333333 21.333334H213.333333c-12.8 0-21.333333-8.533333-21.333333-21.333334v-277.333333c0-12.8-8.533333-21.333333-21.333333-21.333333H128c-12.8 0-21.333333 8.533333-21.333333 21.333333v362.666667c0 12.8 8.533333 21.333333 21.333333 21.333333h768c12.8 0 21.333333-8.533333 21.333333-21.333333v-362.666667c0-12.8-8.533333-21.333333-21.333333-21.333333z"></path><path d="M277.333333 588.8H149.333333v-85.333333h128c12.8 0 21.333333 8.533333 21.333334 21.333333v42.666667c0 10.666667-8.533333 21.333333-21.333334 21.333333zM874.666667 588.8h-128c-12.8 0-21.333333-8.533333-21.333334-21.333333v-42.666667c0-12.8 8.533333-21.333333 21.333334-21.333333h128v85.333333z"></path>'),picture:St('<path d="M986.112 446.7712a38.4 38.4 0 0 0 38.4-38.4V144.128a140.9536 140.9536 0 0 0-140.8-140.8L140.7488 3.6864a140.9536 140.9536 0 0 0-140.8 140.8v735.3856a140.9536 140.9536 0 0 0 140.8 140.8l742.9632-0.4096a140.9536 140.9536 0 0 0 140.8-140.8V588.288c0-3.6864-1.1264-7.0144-2.0992-10.3936a37.8368 37.8368 0 0 0-11.9808-29.5936L785.8176 342.6304c-26.0096-23.8592-65.8432-24.576-96.2048 1.9968l-163.1232 182.8864-146.2272-84.9408a70.8608 70.8608 0 0 0-53.3504-13.6192 70.3488 70.3488 0 0 0-44.544 26.4704L179.6096 563.8656a38.4 38.4 0 0 0 55.7056 52.8384l103.8336-109.568 145.9712 84.8384c25.9584 20.0192 62.976 18.9952 91.5968-5.888l162.3552-182.1184 208.5888 191.0272v284.4672c0 35.2768-28.7232 64-64 64l-742.912 0.4096c-35.2768 0-64-28.7232-64-64V144.4864c0-35.2768 28.7232-64 64-64l742.9632-0.4096c35.2768 0 64 28.7232 64 64v264.2944c0 21.1968 17.2032 38.4 38.4 38.4z"></path><path d="M264.4992 248.4224m-49.664 0a49.664 49.664 0 1 0 99.328 0 49.664 49.664 0 1 0-99.328 0Z"></path>'),del:`<svg style="${pt}vertical-align: middle;fill: currentColor;overflow: hidden;transform: scale(1.2);" viewBox="0 0 1024 1024" version="1.1" ${ut}><path d="M202.666667 256h-42.666667a32 32 0 0 1 0-64h704a32 32 0 0 1 0 64H266.666667v565.333333a53.333333 53.333333 0 0 0 53.333333 53.333334h384a53.333333 53.333333 0 0 0 53.333333-53.333334V352a32 32 0 0 1 64 0v469.333333c0 64.8-52.533333 117.333333-117.333333 117.333334H320c-64.8 0-117.333333-52.533333-117.333333-117.333334V256z m224-106.666667a32 32 0 0 1 0-64h170.666666a32 32 0 0 1 0 64H426.666667z m-32 288a32 32 0 0 1 64 0v256a32 32 0 0 1-64 0V437.333333z m170.666666 0a32 32 0 0 1 64 0v256a32 32 0 0 1-64 0V437.333333z"></path></svg>`,overview:`<svg style="${pt}vertical-align: middle;fill: currentColor;overflow: hidden;transform: scale(0.85);" viewBox="0 0 1024 1024" version="1.1" ${ut}><path d="M512 349.888A162.112 162.112 0 1 0 674.112 512 162.32 162.32 0 0 0 512 349.888z m0 231.6A69.488 69.488 0 1 1 581.488 512 69.552 69.552 0 0 1 512 581.488z"></path><path d="M972.8 460.8h-54.4a410.304 410.304 0 0 0-355.2-355.2V51.2a51.2 51.2 0 1 0-102.4 0v54.4a410.304 410.304 0 0 0-355.2 355.2H51.2a51.2 51.2 0 1 0 0 102.4h54.4a410.304 410.304 0 0 0 355.2 355.2v54.4a51.2 51.2 0 0 0 102.4 0v-54.4a410.304 410.304 0 0 0 355.2-355.2h54.4a51.2 51.2 0 0 0 0-102.4zM512 819.2A307.2 307.2 0 1 1 819.2 512 307.456 307.456 0 0 1 512 819.2z"></path></svg>`,zoomReset:St('<path d="M514.2 854.1c-188 0-340.9-152.9-340.9-340.9s152.9-340.9 340.9-340.9 340.9 152.9 340.9 340.9-152.9 340.9-340.9 340.9z m0-598.8C372 255.3 256.3 371 256.3 513.2S372 771.1 514.2 771.1s257.9-115.7 257.9-257.9-115.7-257.9-257.9-257.9z"></path><path d="M888.5 554.7H742.1c-22.9 0-41.5-18.6-41.5-41.5s18.6-41.5 41.5-41.5h146.5c22.9 0 41.5 18.6 41.5 41.5-0.1 22.9-18.6 41.5-41.6 41.5zM288 554.7H141.5c-22.9 0-41.5-18.6-41.5-41.5s18.6-41.5 41.5-41.5H288c22.9 0 41.5 18.6 41.5 41.5s-18.6 41.5-41.5 41.5zM515 327.7c-22.9 0-41.5-18.6-41.5-41.5V139.7c0-22.9 18.6-41.5 41.5-41.5s41.5 18.6 41.5 41.5v146.4c0 23-18.5 41.6-41.5 41.6zM515 928.2c-22.9 0-41.5-18.6-41.5-41.5V740.3c0-22.9 18.6-41.5 41.5-41.5s41.5 18.6 41.5 41.5v146.5c0 22.8-18.5 41.4-41.5 41.4z"></path>'),zoomIn:St('<path d="M919.264 905.984l-138.912-138.912C851.808 692.32 896 591.328 896 480c0-229.376-186.624-416-416-416S64 250.624 64 480s186.624 416 416 416c95.008 0 182.432-32.384 252.544-86.208l141.44 141.44a31.904 31.904 0 0 0 45.248 0 32 32 0 0 0 0.032-45.248zM128 480C128 285.92 285.92 128 480 128s352 157.92 352 352-157.92 352-352 352S128 674.08 128 480z"></path><path d="M625.792 448H512v-112a32 32 0 0 0-64 0V448h-112a32 32 0 0 0 0 64H448v112a32 32 0 1 0 64 0V512h113.792a32 32 0 1 0 0-64z"></path>'),zoomOut:St('<path d="M919.264 905.984l-138.912-138.912C851.808 692.32 896 591.328 896 480c0-229.376-186.624-416-416-416S64 250.624 64 480s186.624 416 416 416c95.008 0 182.432-32.384 252.544-86.208l141.44 141.44a31.904 31.904 0 0 0 45.248 0 32 32 0 0 0 0.032-45.248zM128 480C128 285.92 285.92 128 480 128s352 157.92 352 352-157.92 352-352 352S128 674.08 128 480z" ></path><path d="M625.792 448H336a32 32 0 0 0 0 64h289.792a32 32 0 1 0 0-64z"></path>'),exchange:`<svg style="${pt}vertical-align: middle;fill: currentColor;overflow: hidden;transform: scale(1.2);" viewBox="0 0 1024 1024" version="1.1" ${ut}><path d="M825.6 448a37.12 37.12 0 0 0-37.12 37.76v243.2a56.96 56.96 0 0 1-56.32 56.32H291.84a56.96 56.96 0 0 1-56.32-56.32v-441.6a56.96 56.96 0 0 1 56.32-56.32h248.32A37.12 37.12 0 0 0 576 192a33.28 33.28 0 0 0-37.12-32.64H291.84a128 128 0 0 0-128 128v439.68a128 128 0 0 0 128 128h439.68a128 128 0 0 0 128-128V488.32c-1.28-23.04-15.36-40.32-33.92-40.32z"></path><path d="M362.24 647.68a33.92 33.92 0 0 0 46.72 0l412.16-412.16a36.48 36.48 0 1 0-51.2-51.2L362.24 600.96a42.24 42.24 0 0 0 0 46.72z"></path></svg>`},Nt=l({},zt);W.prototype.setHtmlType=function(t){let e=Nt[t];e?this.updateHTML(e):console.error(`html type ['${t}'] is not register`)};class Lt extends W{constructor(i,o){super(i),a.add(this),t.set(this,void 0),e.set(this,void 0),Object.assign(i.style,{position:"absolute",borderRadius:"4px",padding:"5px",fontSize:"12px",background:"#fff",boxShadow:"0 1px 4px rgba(0,0,0,.3)"}),u(this,e,o)}setData(e){var o,r,n;return Array.isArray(e)&&(u(this,t,e),(o=this,r=a,n=i,c(o,r,"access private method"),n).call(this)),this}}t=new WeakMap,e=new WeakMap,a=new WeakSet,i=function(){let e=(c(a=this,i=t,"read from private field"),o?o.call(a):i.get(a));var a,i,o;let r=[],n={},s=0;for(let t of e){let{type:e,text:a,action:i}=t,o=zt[e.toLowerCase()];n[++s]=i,r.push(`<div data-index="${s}" style="display: flex; padding:4px; cursor: pointer;align-items: center;">\n                        <div style="width: 18px;height: 18px">\n                            ${o}\n                        </div>\n                        <div style="margin-left: 10px;">${a}</div>\n                       </div>`)}this.updateHTML(r.join(""));let l=this.node.children;for(let t of l){let e=t.dataset.index;H(t,(t=>{n[e]()})),D(t,"mouseover",(()=>{t.style.background="#ddd"})),D(t,w,(()=>{t.style.background="unset"}))}this.click((t=>{B(t)})).dblclick((t=>{B(t)}))};const kt={Start:"Start",End:"End",Join:"Join",Split:"Split",Business:"Business",Service:"Service",Manual:"Manual",Message:"Message",Custom:"Custom"},Dt=(t,e,a)=>`<div class="menu-item" data-type="${t}" ${e?'draggable="true"':""} title="${a}"></div>`,It=(t,e)=>`<div class="tool-item" data-type="${t}" title="${e}"></div>`,Ht='<div style="border: 1px dashed #dcdfe6; width: 40%;margin: 8px;opacity: .7;"></div>',Ot=`\n    <div class="flow-menu" style="display:none;z-index: 100;">\n        ${Dt("select",1,"圈选，可使用快捷键按住ctrl替代")}\n        ${Ht}\n        ${Dt("start",1,"开始")}\n        ${Dt("end",1,"结束")}\n        ${Ht}\n        ${Dt("businessTask",1,"业务节点")}\n        ${Dt("service",1,"服务节点")}\n        ${Dt("message",1,"消息节点")}\n        ${Dt("manual",1,"手工节点")}\n        <div class="flow-menu-custom-items"></div>\n        ${Ht}\n        ${Dt("xor",1,"有且仅有一个满足条件的分支通过")}\n        ${Dt("or",1,"至少一个满足条件的分支通过,与汇聚网关组合使用")}\n        ${Dt("and",1,"所有分支强制通过,与汇聚网关组合使用")}\n        ${Dt("join",1,"汇聚网关")}\n        ${Ht}\n        ${Dt("reset",0,"重置")}\n        ${Dt("imp",0,"导入")}\n        ${Dt("exp",0,"导出")}\n    </div>\n    <div class="flow-wrapper" style="position: absolute;width: 100%; height: 100%;left: 0; top: 0">\n       <div class="flow-wrapper-body"  style="position: relative;${pt}">\n            <div class="text-editor" contenteditable style="min-width: 50px; height: 24px; display: none;position: absolute;font-size: 13px;background: #fff;transform: translate(-50%, -50%);outline: 1px solid transparent;z-index: 100;"></div>\n       </div>\n    </div>\n    <div class="flow-tools" style="display:none;z-index: 100;">\n        ${It("overview","大纲，全貌视图")+It("zoomReset","初始大小")+It("zoomIn","放大")+It("zoomOut","缩小")}\n    </div>\n    <input class="flow-import-file" type="file" accept=".json" style="display:none;opacity: 0;width: 0;"/>\n`,Rt=(t,e)=>{"function"==typeof e.option.onNodeCreated&&e.option.onNodeCreated.call(e,t,e)};class Pt{constructor(t,e){"string"==typeof t&&(t=document.querySelector(t)),this.customHtmlTypes=l({},Nt),this.option=Tt({},dt,e||{}),this.settings?this.option.settings=Tt({},ht,this.settings):this.option.settings=l({},ht);let{width:a="100%",height:i="100%"}=this.option,o={position:"relative",overflow:this.option.overflow||"hidden"};Tt(t.style,o);let r=t.innerHTML||"";t.innerHTML=r+Ot,this.option.menu&&this.initMenu(t.children[0]),this.initInput(t.querySelector(".text-editor")),this.initFileInput(t.querySelector(".flow-import-file"));let n=t.querySelector(".flow-wrapper-body");this.flowWrapper=n,this.paper=new lt(n,a,i),this.connectColors=[this.themeColor],this.initPaper(),this.dom=t,this.init(),this.initStyles(),this.elements={},this.groupSelectElements=[],this.selectElement=null;let s=this,h=t=>s.setUUID(t);this.nodeDatas={gateway:"XOR",handler:{},uuid:h,meta:{}},this.connectDatas={priority:0,conditionType:"Script",script:"",uuid:h,meta:{},pathStyle:"broken"},this.initControlElements(),this.cnt=0,this.translateX=0,this.translateY=0,this.offsetX=0,this.offsetY=0,this.scaleValue=1,this.zoomInterval=.1,this.elementZIndex=0}isMobile(){return g.isMobile}registerHTML(t,e,a){"string"==typeof t&&t&&(this.customHtmlTypes[t]={innerHTML:e,options:a})}getCustomInnerHTML(t,e,a){let i=this.customHtmlTypes[t];if("string"==typeof i)return i;if(i){let t=i.innerHTML;if(t)return"function"==typeof t?t(this,e,a):t}return null}getCustomOptions(t){return this.customHtmlTypes[t]&&this.customHtmlTypes[t].options}initControlElements(){let t=this.paper,e=this,a={fill:"#fff",stroke:this.themeColor},i=[0,0,5,5,2.5],o=this.nw=t.rect(...i).attr(l(l({},a),{cursor:"nw-resize"})),r=this.w=t.rect(...i).attr(l(l({},a),{cursor:"w-resize"})),n=this.sw=t.rect(...i).attr(l(l({},a),{cursor:"sw-resize"})),s=this.n=t.rect(...i).attr(l(l({},a),{cursor:"n-resize"})),h=this.s=t.rect(...i).attr(l(l({},a),{cursor:"s-resize"})),d=this.ne=t.rect(...i).attr(l(l({},a),{cursor:"ne-resize"})),c=this.e=t.rect(...i).attr(l(l({},a),{cursor:"e-resize"})),u=this.se=t.rect(...i).attr(l(l({},a),{cursor:"se-resize"})),p=[function(t,a,i,o){e.resizeOnMove(this,t,a,i,o)},function(){e.resizeOnStart(this),e.dragingElement=this},function(){e.dragingElement=null}];o.data("dtn","nw").data("dgl",u).drag(...p).hide(),r.data("dtn","w").data("dgl",c).drag(...p).hide(),n.data("dtn","sw").data("dgl",d).drag(...p).hide(),s.data("dtn","n").data("dgl",h).drag(...p).hide(),h.data("dtn","s").data("dgl",s).drag(...p).hide(),d.data("dtn","ne").data("dgl",n).drag(...p).hide(),c.data("dtn","e").data("dgl",r).drag(...p).hide(),u.data("dtn","se").data("dgl",o).drag(...p).hide(),a={stroke:this.themeColor||"#1DC967","stroke-width":2},this.dropNw=t.path("").attr(l({},a)).hide(),this.dropNe=t.path("").attr(l({},a)).hide(),this.dropSw=t.path("").attr(l({},a)).hide(),this.dropSe=t.path("").attr(l({},a)).hide(),this.dashOuterPath=t.path("").attr("stroke-dasharray","2 2").hide(),this.groupSelection=this.renderRect(0,0,0,0,4).attr({fill:"transparent",cursor:"move","stroke-width":2,"stroke-dasharray":"8 8"}).hide(),this.dragableGroupSelection(),this.groupSelection.click((function(t){e.showGroupSelectionTool(),B(t)}));let m={stroke:this.themeColor,"stroke-width":2,opacity:.5,"stroke-dasharray":"12 12"};this.horizontalLine=this.renderRect(0,0,0,1e-4).attr(l({},m)).hide(),this.verticalLine=this.renderRect(0,0,1e-4,0).attr(l({},m)).hide(),this.connectRect=this.renderRect(0,0,0,0).attr({fill:"transparent","stroke-width":1,stroke:this.themeColor,opacity:.8,"stroke-dasharray":"2 2"}).hide(),this.connectRect.hover((()=>{}),(function(){this.hide()})).click((function(t){B(t);let a=this.data("target");a&&e.handleClickElement(a,t)})).dblclick((function(t){B(t);let a=this.data("target");a&&e.handleDblclickElement(a,t)})),this.popupMenu&&this.popupMenu.remove(),this.popupMenu=new Lt(z("div",this.dom)).attr({x:0,y:0,width:100,height:"auto",color:this.themeColor}).hide();let g=[0,0,16,16],f=this.exchange;f&&f.remove(),f=this.exchange=this.renderHtmlNode("exchange",...g).attr({opacity:.9,title:"修改类型",cursor:"pointer"}).mouseover((function(){e.dragingElement={}})).mouseout((function(){e.dragingElement=null})).hide(),f.click((function(t){let a=f.data("from");e.exchangePopupMenu(a,t)}));let y=this.linkTool=t.image("data:image/png;base64,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",...g).hide();y.attr({opacity:.5,title:"拖拽到目标节点完成连线"}).mouseover((function(){this.attr("opacity",1)})).mouseout((function(){this.attr("opacity",.5)})),y.drag((function(t,a,i,o,r){e.linkToolOnDragMove(this,t,a,i,o,r)}),(function(){e.dragingElement=y}),(function(){e.linkToolOnDragUp(this),e.dragingElement=null})),this.nextTaskTool=t.image("data:image/png;base64,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",...g).attr({opacity:.5,title:"快速追加下一个任务",cursor:"pointer"}).mouseover((function(){this.attr("opacity",1),e.dragingElement={}})).mouseout((function(){this.attr("opacity",.5),e.dragingElement=null})).hide(),this.nextTaskTool.click((function(t){e.nextNode()})),(this.nextSplitTool=t.image("data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iaWNvbiIgc3R5bGU9IndpZHRoOiAxZW07aGVpZ2h0OiAxZW07dmVydGljYWwtYWxpZ246IG1pZGRsZTtmaWxsOiBjdXJyZW50Q29sb3I7b3ZlcmZsb3c6IGhpZGRlbjsiIHZpZXdCb3g9IjAgMCAxMDI0IDEwMjQiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiBwLWlkPSIzOTA0OSI+PHBhdGggZD0iTTc3OS43MzMzMzMgNjRjMi40NzQ2NjcgMCA0Ljg2NCAwLjc2OCA2LjgyNjY2NyAyLjE3NmwxOTIuMDQyNjY3IDEzNy4xNzMzMzNhMTEuNzMzMzMzIDExLjczMzMzMyAwIDAgMSAwIDE5LjExNDY2N2wtMTkyIDEzNy4xNzMzMzNBMTEuNzMzMzMzIDExLjczMzMzMyAwIDAgMSA3NjggMzUwLjA4TDc2Ny45NTczMzMgMjU2aC0yMzcuOTk0NjY2Yy02LjgyNjY2NyAwLTE0LjI1MDY2NyA3LjQ2NjY2Ny0xNS43ODY2NjcgMTguOTAxMzMzbC0wLjI5ODY2NyA0LjQ4djQ2NS4yMzczMzRjMCAxMi4zNzMzMzMgNi42MTMzMzMgMjEuMjQ4IDEzLjQ4MjY2NyAyMy4wNGwyLjU2IDAuMzQxMzMzSDc2OEw3NjggNjc0LjM0NjY2N2ExMS43MzMzMzMgMTEuNzMzMzMzIDAgMCAxIDE4LjU2LTkuNTU3MzM0bDE5Mi4wNDI2NjcgMTM3LjE3MzMzNGExMS43MzMzMzMgMTEuNzMzMzMzIDAgMCAxIDAgMTkuMTE0NjY2bC0xOTIgMTM3LjE3MzMzNGExMS43MzMzMzMgMTEuNzMzMzMzIDAgMCAxLTE4LjYwMjY2Ny05LjU1NzMzNEw3NjcuOTU3MzMzIDg1My4zMzMzMzNoLTIzNy45OTQ2NjZjLTU0LjkxMiAwLTk3Ljg3NzMzMy00NS44MjQtMTAxLjIwNTMzNC0xMDEuNzE3MzMzbC0wLjIxMzMzMy02Ljk5NzMzM0w0MjguNTAxMzMzIDU1NC42NjY2NjdIMjkxLjM3MDY2N2ExMjguMDQyNjY3IDEyOC4wNDI2NjcgMCAxIDEgMC04NS4zMzMzMzRoMTM3LjEzMDY2NlYyNzkuMzgxMzMzYzAtNTYuNjE4NjY3IDQwLjc4OTMzMy0xMDQuNzA0IDk0LjY3NzMzNC0xMDguNDU4NjY2TDUyOS45MiAxNzAuNjY2NjY3aDIzNy45OTQ2NjdMNzY4IDc1LjczMzMzM2MwLTYuNDg1MzMzIDUuMjQ4LTExLjczMzMzMyAxMS43MzMzMzMtMTEuNzMzMzMzeiIgcC1pZD0iMzkwNTAiPjwvcGF0aD48L3N2Zz4=",...g).attr({opacity:.5,title:"快速追加分支任务",cursor:"pointer"}).mouseover((function(){this.attr("opacity",1),e.dragingElement={}})).mouseout((function(){this.attr("opacity",.5),e.dragingElement=null})).hide()).click((function(t){e.nextSplit()}));let x=this.nextEndTool;x&&x.remove(),x=this.nextEndTool=this.renderBuiltInHtmlNode("end",...g).attr({opacity:.5,title:"快速追加结束任务",cursor:"pointer"}).mouseover((function(){this.attr("opacity",1),e.dragingElement={}})).mouseout((function(){this.attr("opacity",.5),e.dragingElement=null})).hide(),x.click((function(t){e.nextEnd()})),this.deleteTool&&this.deleteTool.remove(),this.deleteTool=this.renderBuiltInHtmlNode("del",...g).attr({opacity:.5,title:"删除元素",cursor:"pointer"}).mouseover((function(){this.attr("opacity",1),e.dragingElement={}})).mouseout((function(){this.attr("opacity",.5),e.dragingElement=null})).hide(),this.deleteTool.click((function(t){e.deleteTool.data("host")?e.deleteSelectElement():e.deleteGroupSelectElements(),e.deleteTool.hide()}))}exchangePopupMenu(t,e){if(!t)return;let a=this,{pageX:i,pageY:o}=A(e),{left:r,top:n,right:s}=this.dom.getBoundingClientRect(),h=i-r,d=o-n,c=t.nodeType,u=[],p=a.option.excludeTypes||[];u=c==kt.Split?[{type:"XOR",text:"独占"},{type:"OR",text:"条件"},{type:"AND",text:"并行"}].map((e=>{let{type:i}=e;return l(l({},e),{action:()=>{t.gateway=i,a.hidePopupMenu(0)}})})):[{type:kt.Business,text:"业务节点"},{type:kt.Service,text:"服务节点"},{type:kt.Message,text:"消息节点"},{type:kt.Manual,text:"手工节点"}].filter((t=>t.type!=c&&!p.includes(t.type.toLowerCase()))).map((e=>{let{type:i}=e;return l(l({},e),{action:()=>{t.nodeType=i,a.hidePopupMenu(0)}})}));let{width:m}=this.popupMenu.attrs;i>s-m&&(h=h-m-20),this.popupMenu.setData(u).attr({x:h+10,y:d-5}).show()}dragableGroupSelection(){let t=this,e=this.groupSelection,a={ox:0,oy:0,dx:0,dy:0};e.drag(((i,o)=>{if(!t.option.editable)return;let r=t.scaleValue||1;i/=r,o/=r;let n={x:a.ox+i,y:a.oy+o};e.attr(n);let s=i-a.dx,l=o-a.dy;a.dx=i,a.dy=o,t.elementsPanTo(this.groupSelectElements,s,l)}),(()=>{if(t.option.editable)return t.dragingElement=e,a.ox=e.attr("x"),a.oy=e.attr("y"),e.attr({opacity:.8}),e.attr("cursor","move"),t.hideEditElements(t.selectElement),t.enableHistory()&&(a.undoData=JSON.stringify(t.getData())),!1}),(()=>{if(t.option.editable){if(t.dragingElement=null,e.attr({opacity:1}),t.enableHistory()){let e=JSON.stringify(t.getData()),i=a.undoData;t.addAction({undo:()=>t.setData(i),redo:()=>t.setData(e)})}a={ox:0,oy:0,dx:0,dy:0}}}))}nextNode(){let t=this.nextTaskTool.data("from");if(!t)return;let{x:e,y:a,width:i,height:o}=t.attrs,r=a+o/2,n=this.createNextNode(e+i+150,a),{height:s}=n.attrs;n.attr({y:r-s/2}),this.updateElements(n);let l=this.createPath(t,n);this.hideEditElements(l)}createNextNode(t,e){let a=this.option.defaultNextNodeFn;return"function"==typeof a?a(this,t,e):this.createBusinessNode(t,e)}nextSplit(){let t=this.nextSplitTool.data("from");if(!t)return;let e="AND"==t.data("gateway"),{x:a,y:i,width:o,height:r}=t.attrs,n=i+r/2,s=this.createNextNode(a+o+150,i),{height:l}=s.attrs;s.attr({y:n-l/2-150}),this.updateElements(s);let h=this.createPath(t,s);e||this.setConnectType(h,this.option.defaultConditionType||"Script"),this.hideEditElements(h);let d=this.createNextNode(a+o+150,i),{height:c}=d.attrs;d.attr({y:n-c/2+150}),this.updateElements(d);let u=this.createPath(t,d);e||this.setConnectType(u,this.option.defaultConditionType||"Script"),this.hideEditElements(u)}nextEnd(){let t=this.nextTaskTool.data("from");if(!t)return;let{x:e,y:a,width:i,height:o}=t.attrs,r=a+o/2,n=this.createEndNode(e+i+150,a),{height:s}=n.attrs;n.attr({y:r-s/2}),this.createPath(t,n)}setWrapperStyle(t){let e=this.flowWrapper;t&&"object"==typeof t&&Tt(e.style,t)}initStyles(){let t={position:"relative",overflow:this.option.overflow||"hidden"};this.option.background?t.background=`${this.option.background}`:this.option.grid&&(t.background=`url("${p}")`),Tt(this.dom.style,t)}setBackground(t){Tt(this.dom.style,{background:t})}setBackgroundGrid(t){t?this.setBackground(`url("${p}")`):this.setBackground("unset")}enableScroll(){Tt(this.dom.style,{overflow:"auto"})}disableScroll(){Tt(this.dom.style,{overflow:"hidden"})}get settings(){return this.option.settings||{}}get themeColor(){return this.settings.themeColor}initMenu(t){let e=this;this.menu=t,Tt(t.style,{position:"absolute",left:0,top:0,width:"60px",display:"flex",flexDirection:"column",alignItems:"center",fontSize:"14px",color:this.themeColor,padding:"5px 5px 20px",background:"hsla(0,0%,100%,.9)",boxShadow:"0 1px 4px rgba(0,0,0,.3)",userSelect:"none"},this.option.menu.style||{});const a={dragmenu:!1,type:null,element:null},i=(i,o)=>{const{pageX:r,pageY:n}=A(i);let s=o==t;if(!s){let t=o.dataset.type;a.type=t}a.px=r,a.py=n;let{x:l,y:h,left:d,top:c}=e.dom.getBoundingClientRect(),u=l||d,p=h||c;a.offsetX=u,a.offsetY=p,a.dragmenu=s,s&&(a.left=Number(t.style.left.replace("px","")),a.top=Number(t.style.top.replace("px","")))},o=i=>{let{type:o,element:r,dragmenu:n,left:s,top:l}=a;const{pageX:h,pageY:d}=A(i);let c=h-a.px,u=d-a.py;if(n){let e=s+c<=0?0:s+c,a=l+u<=0?0:l+u;Tt(t.style,{left:`${e}px`,top:`${a}px`})}else{if(!r){let t=a.px-a.offsetX-20,n=a.py-a.offsetY-20,s=this.scaleValue||1;t=(t-e.offsetX)/s,n=(n-e.offsetY)/s,"manual"==o?a.element=r=e.createManualNode(t,n):"service"==o?a.element=r=e.createServiceNode(t,n):"businessTask"==o?a.element=r=e.createBusinessNode(t,n):"message"==o?a.element=r=e.createMessageNode(t,n):"start"==o?a.element=r=e.createStartNode(t,n):"end"==o?a.element=r=e.createEndNode(t,n):"xor"==o?(a.element=r=e.createSplitNode(t,n),r.data("gateway","XOR")):"or"==o?(a.element=r=e.createSplitNode(t,n),r.updateHTML(zt.or),r.data("gateway","OR")):"and"==o?(a.element=r=e.createSplitNode(t,n),r.updateHTML(zt.and),r.data("gateway","AND")):a.element=r="join"==o?e.createJoinNode(t,n):e.createCustomHtmlNode(o,t,n),e.handleClickElement(r,i),e.selectElement=r,e.elementDragStart(r)}e.elementDragMove(r,c,u)}},r=t=>{if(a.element){let t=a.element;t.attr({opacity:1}),e.handleElementAction(t)}delete a.type,delete a.element,delete a.dragmenu,e.hideAlignLines(),Et(y,o),Et(v,r)};"object"==typeof this.option.menu&&this.option.menu.draggable&&(Tt(t.style,{cursor:"move"}),D(t,x,(function(e){i(e,t),Mt(y,o),Mt(v,r),B(e)})));let n=this.settings.customMenuItems;if(Array.isArray(n)&&n.length>0){let e=t.querySelector(".flow-menu-custom-items");for(let t of n)z("div",e,{class:"menu-item","data-type":t,draggable:!0,style:"width: 32px; height: 32px; margin: 4px 0px; cursor: move",title:t})}t.querySelectorAll(".menu-item").forEach((t=>{let a=t.dataset.type,n=32,s=32;"or"!=a&&"xor"!=a&&"and"!=a&&"join"!=a||(n=s=40),Tt(t.style,{width:`${n}px`,height:`${s}px`,margin:"4px 0"}),(e.option.excludeTypes||[]).includes(a)&&Tt(t.style,{display:"none"}),"select"==a?(t.style.cursor="pointer",t.innerHTML=zt.select,H(t,(function(t){e.groupSelectionFlag=!0,e.paper.node.style.cursor="crosshair",B(t)}))):"reset"==a?(t.style.cursor="pointer",t.innerHTML=zt.reset,H(t,(function(t){e.reset(),B(t)}))):"exp"==a?(t.style.cursor="pointer",t.innerHTML=zt.exp,H(t,(function(t){e.exportJSON(),B(t)}))):"imp"==a?(t.style.cursor="pointer",t.innerHTML=zt.imp,H(t,(function(t){e.handleImport(),B(t)}))):"picture"==a?(t.style.cursor="pointer",t.innerHTML=zt.picture,H(t,(function(t){e.exportImage(),B(t)}))):(Tt(t.style,{cursor:"move"}),setTimeout((()=>{let e=this.getCustomInnerHTML(a,{scene:"menu"});t.innerHTML=e,D(t,x,(function(e){i(e,t),Mt(y,o),Mt(v,r),B(e)}))}),0))}))}addAction(t){}setMenuThemeColor(t){let e=this.menu;e&&(Tt(e.style,{color:t}),e.querySelectorAll(".menu-item").forEach((t=>{let e=t.dataset.type,a=this.getCustomInnerHTML(e,{scene:"menu"});a&&(t.innerHTML=a)})))}setToolsStyle(t){"object"!=typeof t&&(t={});let e=this.flowToolsDom;e&&Tt(e.style,t||{})}initInput(t){let e=this;this.input=t,D(t,"input",(function(t){let a=t.target.innerText.replace(/[\s\r\n]/g,"");e.updateActiveText(a,!1)})),D(t,"blur",(function(t){e.endInputEdit()})),D(t,x,(function(t){e.disablePan=!0})),this.textElement=null}openElementPropertyPop(t){this.option.editable}resetConnectPathData(t,e){let a=t.data("from"),i=t.data("to");this.resetPathData(t,a,i,e)}beginInputEdit(t){if(!this.option.editable)return;let e=this.input;if(t.data("text")){let a=t.data("text");this.textElement=a;let i=a.attr("text"),{x:o,y:r}=a.attrs;e.innerHTML=i,Tt(e.style,{left:o+"px",top:r+"px",display:"block"}),e.focus(),a.hide()}}endInputEdit(){this.input.innerText="",this.input.style.display="none";let t=this.textElement;t&&t.show(),this.disablePan=!1}initFileInput(t){let e=this;this.fileInput=t,D(t,"change",(t=>{e.onImportFile(t)}))}initData(){this.elements={},this.containers={},this.groupSelectElements=[]}initPaper(){let t=this.paper.node;t.querySelector("defs").innerHTML=`<path d="M5,0 0,2.5 5,5 3.5,3 3.5,2z" id="${this.paper.id}path"></path>`;for(let e of this.connectColors||[])Y(t,e)}init(){let t=this,e=t.paper.node;if(H(e,(e=>t.handleClickBlank(e))),D(e,"contextmenu",(e=>t.handleContextmenu(e))),O(e,(e=>t.handleDblClickBlank(e))),this.handleKeyboardEvents(),this.handleMouseDragMoveEvents(),this.setScaleable(this.option.zoomable),this.option.zoomable){let t=this,e=this.dom.querySelector(".flow-tools");this.flowToolsDom=e,Tt(e.style,{position:"absolute",right:"5px",top:"75%",transform:"translate(0, -50%) scale(.95)",width:"48px",display:"flex",flexDirection:"column",alignItems:"center",fontSize:"14px",color:this.themeColor,padding:"5px",userSelect:"none"}),setTimeout((()=>{e.querySelectorAll(".tool-item").forEach((e=>{let a=e.dataset.type;Tt(e.style,{width:"36px",height:"36px",margin:"2px 0",cursor:"pointer",background:"hsla(0,0%,100%,.9)",boxShadow:"0 1px 4px rgba(0,0,0,.3)"});let i=t.getCustomInnerHTML(a);e.innerHTML=i,i||(e.style.display="none");H(e,(function(e){B(e),"overview"==a?t.overview():"zoomReset"==a?(t.zoomReset(),ct&&t.triggerTextRefreshPos()):"zoomIn"==a?(t.zoomIn(),ct&&t.triggerTextRefreshPos()):"zoomOut"==a&&(t.zoomOut(),ct&&t.triggerTextRefreshPos())})),D(e,x,(t=>{B(t)}))}))}),0)}}setToolStyle(t){console.log("setToolStyle ",t,this.flowToolsDom),this.flowToolsDom&&Object.assign(this.flowToolsDom.style,t||{})}handleKeyboardEvents(){let t=this;this.handleDocumentKeyDown||(this.handleDocumentKeyDown=e=>{if(46==e.keyCode)t.deleteSelectElement();else if(8==e.keyCode){let t=document.activeElement;if(t.getAttribute&&"readonly"==t.getAttribute("readonly"))return!1}else 16==e.keyCode?t.shiftMode=!0:17==e.keyCode?(t.resetGroupSelection(),t.groupSelectionFlag=!0,t.paper.node.style.cursor="crosshair"):89==e.keyCode?e.ctrlKey&&t.redo():90==e.keyCode&&e.ctrlKey&&t.undo()}),this.handleDocumentKeyUp||(this.handleDocumentKeyUp=e=>{16==e.keyCode&&(t.shiftMode=!1),17==e.keyCode&&(t.groupSelectionFlag=!1,t.paper.node.style.cursor="default")}),Et("keydown",this.handleDocumentKeyDown),Et("keyup",this.handleDocumentKeyUp),Mt("keydown",this.handleDocumentKeyDown),Mt("keyup",this.handleDocumentKeyUp)}handleMouseDragMoveEvents(){let t=this;const e={moved:!1},a=a=>{const{pageX:i,pageY:o}=A(a);if(e.px=i,e.py=o,t.groupSelectionMode()){let{x:e,y:a,left:r,top:n}=t.dom.getBoundingClientRect(),s={x:i-(e||r||0),y:o-(a||n||0)},l=t.scaleValue||1;s.x=(s.x-t.offsetX)/l,s.y=(s.y-t.offsetY)/l,this.groupSelection.data("start",s)}else t.paper.node.style.cursor="grab",e.translateX=t.translateX,e.translateY=t.translateY},i=a=>{const{pageX:i,pageY:o}=A(a);let r=i-e.px,n=o-e.py;if(r*r+n*n>0&&(e.moved=!0),t.groupSelectionMode()){let{x:t,y:e}=this.groupSelection.data("start"),a=this.scaleValue||1;r/=a,n/=a;let i=r,o=n;r<0&&(t+=r,i=-r),n<0&&(e+=n,o=-n),this.groupSelection.attr({x:t,y:e,width:i,height:o,stroke:this.themeColor}).show()}else t.translateX=e.translateX+r,t.translateY=e.translateY+n,t.cancelSelect(),t.translateTo(t.translateX,t.translateY)},o=a=>{try{t.groupSelectionMode()?t.showGroupSelection():e.moved&&t.panTo(t.translateX/this.scaleValue,t.translateY/this.scaleValue,!0),e.moved?t.connectRect.hide():t.resetGroupSelection(),t.groupSelectionFlag=!1,e.moved=!1,t.paper.node.style.cursor="default"}finally{Et("mousemove",i),Et("touchmove",i),Et("mouseup",o),Et("touchend",o)}},r=e=>{if(!t.dragingElement&&!t.disablePan){if(!t.enablePanable()&&!t.groupSelectionMode())return;if(t.isMobile()){if((e.touches||e.originalEvent.touches).length>1)return Et("mousemove",i),void Et("touchmove",i)}a(e),t.endInputEdit(),Mt("mousemove",i),Mt("touchmove",i),Mt("mouseup",o),Mt("touchend",o),B(e)}};D(t.dom,"mousedown",r),D(t.dom,"touchstart",r)}triggerTextRefreshPos(){this.paper.node.querySelectorAll("text").forEach((t=>{let e=t.getAttribute("x");null!=e&&t.setAttribute("x",e)}))}setScaleable(t){let e=this;if(t)if(g.isMobile){let t=!1,a=null,i=null,o=e=>{let o=e.touches||e.originalEvent.touches;if(o.length>=2&&t){if(null===i||null===a)return;let t=P(o[0].pageX,o[0].pageY,o[1].pageX,o[1].pageY)-i;this.setScale(a+this.zoomInterval*(t/50))}};D(e.dom,"touchstart",(e=>{let o=e.touches||e.originalEvent.touches;o.length>=2&&(a=this.scaleValue,t=!0,i=P(o[0].pageX,o[0].pageY,o[1].pageX,o[1].pageY))})),D(e.dom,"touchend",(()=>{t=!1,i=null,a=null})),D(e.dom,"touchmove",o)}else{let t=t=>{if(this.option.disableZoomOnMouseWheel)return;(t.wheelDelta||-t.detail)>0?e.zoomIn():e.zoomOut(),B(t)};D(e.dom,"wheel",t)}}zoomIn(){this.setScale(this.scaleValue+this.zoomInterval)}zoomOut(){this.setScale(this.scaleValue-this.zoomInterval)}zoomReset(){this.setScale(1)}setScale(t){if(t==this.scaleValue)return;0!=this.translateX||this.translateY,t<=.01&&(t=.01),this.scaleValue=t,this.updateWrapperTransform();let{onScaleChange:e}=this.option;"function"==typeof e&&e(t)}overview(){let t=bt(this.elements),e=0,a=0,i=0,o=0,r=!0;for(let T of t){if("path"==T.type)continue;let{x:t,y:n,width:s,height:l}=T.attrs;r?(r=!1,e=t,a=n,i=t+s,o=n+l):(e=gt(e,t),a=gt(a,n),i=ft(i,t+s),o=ft(o,n+l))}let n=i-e,s=o-a,{width:l,height:h}=this.flowWrapper.parentNode.getBoundingClientRect();if(console.log("flowWrapper parentNode",l,h),0==l||0==h)return void console.warn("flow wrapper maybe display none, overview is cancel");let{overviewPadding:d,overviewPaddingLeft:c,overviewPaddingTop:u}=this.option;console.log("overviewPaddingTop",u),c=c||d||20,u=u||d||20,l-=2*c,h-=2*u;let p=e+n/2,m=a+s/2,g=l/2+c,f=h/2+u,y=l,x=h;console.log("viewHeight ",x);let v=1;(y<n||x<s)&&(v=gt(y/(n||1),x/(s||1)));let w=(g-p)*v,A=(f-m)*v,{overviewOffsetLeft:E=0,overviewOffsetTop:M=0}=this.option;this.translateTo(w+E,A+M),this.setScale(v)}groupSelectionMode(){return this.option.editable&&this.groupSelectionFlag}enablePanable(){return this.option.panable}enableHistory(){return this.option.enableHistory}showGroupSelectionTool(){this.cancelSelect();let{x:t,y:e,width:a,height:i}=this.groupSelection.attrs,o=t+a+5,r=e-5;this.deleteTool.attr({x:o,y:r}).data("host",null).show()}hideGroupSelectionTool(){this.deleteTool.hide()}showGroupSelection(){let t=this.groupSelection;if(!t||!t.attrs)return;let{x:e,y:a,width:i,height:o}=t.attrs;if(!(e&&a&&i&&o))return;let r=this.groupSelectElements=[],n=this.elements,s=[];for(let l in n){let e=n[l];if("path"!=e.type){if(this.isDropContainer(e,t)){r.push(e);let t=e.data("out");for(let e in t||{})s.includes(e)||(s.push(e),r.push(t[e]));let a=e.data("in");for(let e in a||{})s.includes(e)||(s.push(e),r.push(a[e]))}}}0==this.groupSelectElements.length?(this.groupSelection.hide(),this.groupSelectionVisible=!1):(this.groupSelection.show(),this.groupSelectionVisible=!0,this.showGroupSelectionTool())}resetGroupSelection(){this.groupSelection.hide(),this.groupSelectElements=[],this.groupSelectionVisible&&this.hideGroupSelectionTool()}cancelSelect(){let t=this.selectElement;t&&(this.hideEditElements(t),this.selectElement=null)}handleClickBlank(t){this.option.clickBlank&&this.option.clickBlank(t),this.cancelSelect()}handleDblClickBlank(t){this.option.dblclickBlank&&this.option.dblclickBlank(t),this.option.enablePropertyPop&&this.openElementPropertyPop()}handleContextmenu(t){return this.option.onContextMenu&&this.option.onContextMenu(t),B(t),!1}renderBuiltInHtmlNode(t,e,a,i,o,r){let n;if(null==(n=zt[t])||null==n)return void console.error(`html type [${t}] is not register `);let s=z("div",this.flowWrapper);s.innerHTML=n,s.style.position="absolute";let l="function"==typeof r?r(s):new W(s);return l.attr({x:e||0,y:a||0,width:i||0,height:o||0}),l}renderHtmlNode(t,e,a,i,o,r){let n=z("div",this.flowWrapper);n.style.position="absolute";let s="function"==typeof r?r(n):new W(n),l=this.getCustomInnerHTML(t,{},s);return null==l?(console.error(`html type [${t}] is null or not register`),void s.remove()):(n.innerHTML=l,s.attr({x:e||0,y:a||0,width:i||0,height:o||0}),s)}renderHtmlText(t,e,a){let i=z("div",this.flowWrapper);i.style.position="absolute";let o=new $(i,!!this.option.nowrap);return o.attr({x:t,y:e}),o.setWidth(a),o}setUUID(t){if(t&&this.option.uuid){let e=(()=>{let t=[],e="0123456789abcdef";for(var a=0;a<36;a++)t[a]=e.substr(E(16*M()),1);return t[14]="4",t[19]=e.substr(3&t[19]|8,1),t[8]=t[13]=t[18]=t[23]="-",t.join("")})();return t.data("uuid",e),e}return null}renderRect(...t){return this.paper.rect(...t)}renderCircle(t,e,a){return this.paper.circle(t,e,a)}renderImage(...t){return this.paper.image(...t)}renderPath(t,e){return this.paper.path(t).attr(e)}getCoordinate(t,e,a,i,o){if(a!=t)return o*(i-e)/(a-t)+(e*a-i*t)/(a-t);console.error("call getCoordinate error for x2 == x1")}getPathData(t,e){let a=t.attr("x")-5,i=t.attr("y")-5,o=t.attr("width")+10,r=t.attr("height")+10,n=e.attr("x")-5,s=e.attr("y")-5,l=e.attr("width")+10,h=e.attr("height")+10,d=a+o/2,c=i+r/2,u=n+l/2,p=s+h/2,m={},g={},f="e",y="w";if(u<d&&p<c){let t={};if(t.y=i,t.x=this.getCoordinate(c,d,p,u,t.y),t.x>=a)m=t,f="n";else{let t={};t.x=a,t.y=this.getCoordinate(d,c,u,p,t.x),m=t,f="w"}let e={};if(e.y=s+h,e.x=this.getCoordinate(c,d,p,u,e.y),e.x>=n&&e.x<=n+l)g=e,y="s";else{let t={};t.x=n+l,t.y=this.getCoordinate(d,c,u,p,t.x),g=t,y="e"}}else if(u<d&&p==c)Tt(m,{x:a,y:c}),Tt(g,{x:n+l,y:p}),f="w",y="e";else if(u<d&&p>c){let t={};if(t.y=i+r,t.x=this.getCoordinate(c,d,p,u,t.y),t.x>=a)m=t,f="s";else{let t={};t.x=a,t.y=this.getCoordinate(d,c,u,p,t.x),m=t,f="w"}let e={};if(e.y=s,e.x=this.getCoordinate(c,d,p,u,e.y),e.x>=n&&e.x<=n+l)g=e,y="n";else{let t={};t.x=n+l,t.y=this.getCoordinate(d,c,u,p,t.x),g=t,y="e"}}else if(u==d&&p<c)Tt(m,{x:d,y:i}),Tt(g,{x:u,y:s+h}),f="n",y="s";else if(u==d&&p>c)Tt(m,{x:d,y:i+r}),Tt(g,{x:u,y:s}),f="s",y="n";else if(u>d&&p<c){let t={};if(t.y=i,t.x=this.getCoordinate(c,d,p,u,t.y),t.x<=a+o)m=t,f="n";else{let t={};t.x=a+o,t.y=this.getCoordinate(d,c,u,p,t.x),m=t,f="e"}let e={};if(e.y=s+h,e.x=this.getCoordinate(c,d,p,u,e.y),e.x>=n)g=e,y="s";else{let t={};t.x=n,t.y=this.getCoordinate(d,c,u,p,t.x),g=t,y="w"}}else if(u>d&&p==c)Tt(m,{x:a+o,y:p}),Tt(g,{x:n,y:p}),f="e",y="w";else{let t={};if(t.y=i+r,t.x=this.getCoordinate(c,d,p,u,t.y),t.x<=a+o)m=t,f="s";else{let t={};t.x=a+o,t.y=this.getCoordinate(d,c,u,p,t.x),m=t,f="e"}let e={};if(e.y=s,e.x=this.getCoordinate(c,d,p,u,e.y),e.x>=n)g=e,y="n";else{let t={};t.x=n,t.y=this.getCoordinate(d,c,u,p,t.x),g=t,y="w"}}return{data:"M"+m.x+","+m.y+"L"+g.x+","+g.y,start:m,end:g,fromSide:f,toSide:y}}getH2VPathData(t){let e=t.data("pathStyleSet");e?(e.startOffset,e.endOffset,e.startDirection,e.endDirection):t.data("pathStyleSet",e={});let a=t.data("from"),i=t.data("to");let o=a.attr("x")-3,r=a.attr("y")-3,n=a.attr("width")+6,s=a.attr("height")+6,l=i.attr("x")-3,h=i.attr("y")-3,d=i.attr("width")+6,c=i.attr("height")+6,u=o+n/2,p=r+s/2,m=l+d/2,g=h+c/2,f={},y={},x=[];if(m<u&&g<p)if(u-(l+d)>10)if(r-g>10)x.push(["M",u,r]),x.push(["L",u,g]),x.push(["L",l+d+3,g]);else{let t=(o+l+d)/2;x.push(["M",o,p]),x.push(["L",t,p]),x.push(["L",t,g]),x.push(["L",l+d+3,g])}else{let t=(p+g)/2;x.push(["M",u,r]),x.push(["L",u,t]),x.push(["L",m,t]),x.push(["L",m,h+c+3])}else if(m<u&&g==p)x.push(["M",o,p]),x.push(["L",l+d+3,g]);else if(m<u&&g>p)if(o-m>10)if(h-p>10)x.push(["M",o,p]),x.push(["L",m,p]),x.push(["L",m,h-3]);else{let t=(o+l+d)/2;x.push(["M",o,p]),x.push(["L",t,p]),x.push(["L",t,g]),x.push(["L",l+d+3,g])}else{let t=(r+s+h)/2;x.push(["M",u,r+s]),x.push(["L",u,t]),x.push(["L",m,t]),x.push(["L",m,h-3])}else if(m==u&&g<p)x.push(["M",u,r]),x.push(["L",u,h+c+3]);else if(m==u&&g>p)x.push(["M",u,r+s]),x.push(["L",u,h-3]);else if(m>u&&g<p)if(r-g>10)if(l-u>10)x.push(["M",u,r]),x.push(["L",u,g]),x.push(["L",l-3,g]);else{let t=(r+h+c)/2;x.push(["M",u,r]),x.push(["L",u,t]),x.push(["L",m,t]),x.push(["L",m,h+c+3])}else{let t=(o+n+l)/2;x.push(["M",o+n,p]),x.push(["L",t,p]),x.push(["L",t,g]),x.push(["L",l-3,g])}else if(m>u&&g==p)x.push(["M",o+n,p]),x.push(["L",l-3,p]);else if(m-o-n>10&&l-u>10)if(g-r-s>10)x.push(["M",u,r+s]),x.push(["L",u,g]),x.push(["L",l-3,g]);else{let t=(o+n+l)/2;x.push(["M",o+n,p]),x.push(["L",t,p]),x.push(["L",t,g]),x.push(["L",l-3,g])}else{let t=(r+s+h)/2;x.push(["M",u,r+s]),x.push(["L",u,t]),x.push(["L",m,t]),x.push(["L",m,h-3])}let v={x:x[0][1],y:x[0][2]},w={x:x[1][1],y:x[1][2]};if(x.length>2){let t={x:x[2][1],y:x[2][2]};Math.abs(w.x-v.x+w.y-v.y)>Math.abs(t.x-w.x+t.y-w.y)?(f=v,y=w):(f=w,y=t)}else f=v,y=w;return{data:k(x),start:f,end:y,points:x}}deleteGroupSelectElements(){let t=this,e=this.groupSelectElements;if(!e||0==e.length)return;let a=null,i=this.enableHistory();i&&(a=JSON.stringify(this.getData()));for(let o of e)!0!==o.removed&&this.removeElement(o);if(i){let e=JSON.stringify(this.getData());this.addAction({undo(){t.setData(a)},redo(){t.setData(e)}})}this.resetGroupSelection()}deleteSelectElement(){if(this.selectElement){let t=this,e=this.selectElement,a=null,i=null;if(this.enableHistory()){let{in:o={},out:r={}}=e.data();if("path"==e.type||0==Ct(o).length&&0==Ct(r).length){let i=e.id,o=this.toElementData(e);a={undo(){t.fromElementData(o)},redo(){t.deleteElementById(i)}},this.addAction(a)}else i=JSON.stringify(this.getData())}if(this.removeElement(this.selectElement),this.selectElement=null,this.enableHistory()&&null==a){let e=JSON.stringify(this.getData());this.addAction({undo(){t.setData(i)},redo(){t.setData(e)}})}}}deleteElementById(t){let e=this.getElementById(t);e&&this.removeElement(e)}removeElement(t){this.selectElement==t&&this.hideEditElements(t);let e=t.type,a=t.data(),i=this;if(setTimeout((()=>{try{"function"==typeof i.option.onRemoveElement&&i.option.onRemoveElement(t)}catch(e){}}),0),"path"==e){let e=t.data("from"),a=t.data("to");if(e&&e.data("out")){delete e.data("out")[t.id]}if(a&&a.data("in")){delete a.data("in")[t.id]}this.removePathRelationRects(t),t.data("text").remove();let i=t.id;t.remove(),this.unregister(i)}else{for(let t in a){let e=a[t];if(e&&e.remove&&e.remove(),("in"==t||"out"==t)&&e&&e instanceof Object)for(let t in e)this.removeElement(e[t]),delete e[t]}let e=t.id;this.unregister(e),t.remove()}}removePathRelationRects(t){let e=null;if(t&&(e=t.pathStyle)){if("qbc"!=e){let e=t.data("qbc_control");e&&e.remove()}if("cbc"!=e){let e=t.data("cbc_control1"),a=t.data("cbc_control2");e&&e.remove(),a&&e.remove()}let a=t.data("start");if(a){let e=a.data("right");for(a.remove();e;){let t=e;t.data("leftRect").remove(),e=e.data("right"),t.remove()}t.removeData("start"),t.removeData("end")}}}bindSelectEvent(t){let e=this;t.click((function(a){B(a),e.handleClickElement(t,a)})),setTimeout((()=>{t.data("text")&&(t.data("text").click((function(a){e.option.textEditOnClick?e.beginInputEdit(t):e.handleClickElement(t,a),B(a)})),t.data("text").dblclick((function(a){e.option.textEditOnDblClick?e.beginInputEdit(t):e.handleDblclickElement(t,a),B(a)})))}),0),t.dblclick((function(a){B(a),e.handleDblclickElement(t,a)})),"path"==t.type&&t.hover((function(){let a=e.getConnectBoundRect(t);e.connectRect.attr(a).show(),e.connectRect.data("target",t),e.option.editable||e.connectRect.attr({stroke:"transparent"})}),(function(){}))}handleClickElement(t,e){let a=this,i=a.selectElement;i&&a.hideEditElements(i),a.showEditElements(a.selectElement=t),a.option.clickElement&&a.option.clickElement(t,e)}handleDblclickElement(t,e){let{dblclickElement:a,textEditOnDblClick:i}=this.option;"function"==typeof a&&a(t,e),i&&this.beginInputEdit(t)}checkEventInsideElement(t,e){let a=A(t);if(!a)return!1;let{pageX:i,pageY:o}=a,{left:r,top:n,width:s,height:l}=e.node.getBoundingClientRect();return i>=r&&i<=r+s&&o>=n&&o<=n+l}bindOptionMouseover(t){let e=this.option.mouseoverElement;if("function"==typeof e&&(t.mouseover((a=>{e(t,a)})),t.data("text"))){t.data("text").mouseover((a=>{e(t,a)}))}let a=this.option.mouseoutElement;if("function"==typeof a){const e=e=>{this.checkEventInsideElement(e,t)||a(t,e)};if(t.mouseout(e),t.data("text")){t.data("text").mouseout(e)}}}bindMouseOverOutEvent(t){this.bindOptionMouseover(t);let e=this;if(!t.isPath()){if("Start"!=t.data("nodeType")){const a=()=>{e.option.editable&&(e.dropNode=null,e.dragingLine&&(e.showDropRect(t),e.dropNode=t))};t.mouseover(a).mouseout((function(){e.option.editable&&(e.hideDropRect(),this.attr("cursor","default"),e.dropNode=null)})),setTimeout((()=>{t.data("text")&&t.data("text").mouseover(a)}),0)}}}bindTouchEvent(t){let e=this.option.longtapElement;console.log(" longtapElement ",e),"function"==typeof e&&(t.longtap((a=>{e(t,a)})),setTimeout((()=>{if(t.data("text")){t.data("text").longtap((a=>{e(t,a)}))}}),0))}showDropRect(t){let{dropNw:e,dropNe:a,dropSw:i,dropSe:o}=this,{x:r,y:n,width:s,height:l}=t.attrs,h=r-5,d=n-5,c=r+s+5,u=n+l+5;e.attr("d","M"+(h+5)+","+d+"H"+h+"V"+(d+5)).show(),a.attr("d","M"+(c-5)+","+d+"H"+c+"V"+(d+5)).show(),i.attr("d","M"+(h+5)+","+u+"H"+h+"V"+(u-5)).show(),o.attr("d","M"+(c-5)+","+u+"H"+c+"V"+(u-5)).show()}hideDropRect(){let{dropNw:t,dropNe:e,dropSw:a,dropSe:i}=this;t.hide(),e.hide(),a.hide(),i.hide()}translateTo(t,e){this.translateX=t,this.translateY=e,this.updateWrapperTransform()}updateWrapperTransform(){let t=this.translateX,e=this.translateY,a=this.scaleValue||1;Tt(this.flowWrapper.style,{transform:`translate(${t}px, ${e}px) scale(${a})`});let i=this.flowWrapper.parentNode,{left:o,top:r}=i.getBoundingClientRect(),{left:n,top:s}=this.flowWrapper.getBoundingClientRect();this.offsetX=n-o,this.offsetY=s-r}elementsPanTo(t,e,a){for(let i in t){let o=t[i];this.moveTo(o,e,a)}for(let i in t){let e=t[i];"path"==e.type&&this.updatePath(e)}}panTo(t,e,a){if(this.hideEditElements(null),this.enableHistory()&&a){let a=this;this.addAction({undo(){a.panTo(-t,-e,!1)},redo(){a.panTo(t,e,!1)}})}this.translateTo(0,0),this.elementsPanTo(this.elements,t,e),this.moveTo(this.groupSelection,t,e)}moveTo(t,e,a){if(!t||!t.type)return;if("path"==t.type){let i=t&&t.data("start");if(i){this.moveTo(i,e,a);let t=i.data("right");for(;t;){this.moveTo(t,e,a);let i=t.data("leftRect");this.moveTo(i,e,a),t=t.data("right")}}}else{let{x:i,y:o}=t.attrs;t.attr({x:i+e,y:o+a})}let i=t.data("text");if(i){let{x:t,y:o}=i.attrs;i.attr({x:t+e,y:o+a})}let o=t.data("icon");if(o){let{x:t,y:i}=o.attrs;o.attr({x:t+e,y:i+a})}}setPanable(t){this.option.panable=!!t}getContextMenuFn(t){}createStartNode(t,e){let a=48,i=48,o=this.getCustomOptions("start");return o&&(a=o.width||48,i=o.height||48),this.createHTMLNode("start",t||100,e||150,a,i,"Start").attr({color:this.themeColor})}createEndNode(t,e){let a=48,i=48,o=this.getCustomOptions("start");return o&&(a=o.width||48,i=o.height||48),this.createHTMLNode("end",t||100,e||150,a,i,"End").attr({color:this.themeColor})}createSplitNode(t,e){return this.createHTMLNode("xor",t||100,e||150,64,64,"Split",(t=>new J(t))).attr({color:this.themeColor})}createJoinNode(t,e){return this.createHTMLNode("join",t||100,e||150,64,64,"Join").attr({color:this.themeColor})}createHTMLNode(t,e,a,i,o,r,n){let s=this.renderHtmlNode(t,e,a,i,o,n);return this.setUUID(s),s.data("handler",{}),s.data("nodeType",r),this.initElement(s),s}createNode(t,e,a,i){a=a||100,i=i||80;let o=this.renderRect(t,e,a||100,i||80,4);this.setUUID(o),o.attr({stroke:this.themeColor,"stroke-width":this.settings.nodeStrokeWith,fill:this.settings.nodeBackgroundColor}),o.data("handler",{});let r=this.renderHtmlText(0,0,.8*a).attr({text:this.settings.nodeName+" "+this.nextId()});o.data("text",r),this.dragableElement(r,o);let n=this.renderHtmlNode("empty",t+5,e+5,20,20).attr({color:this.themeColor});return o.data("icon",n),Rt(o,this),this.initElement(o),o}handleElementAction(t){if(t&&this.enableHistory()){let e=this,a=t.id,i=this.toElementData(t);this.addAction({undo(){e.deleteElementById(a)},redo(){e.fromElementData(i)}})}}createCustomHtmlNode(t,e,a){let i=180,o=80,r=this.getCustomOptions(t);r&&(i=r.width||180,o=r.height||80);const n=this.createHTMLNode(t,e,a,i,o);n.data("nodeType",kt.Custom),n.data("customType",t);let s=this.renderHtmlText(0,0,.8*i).attr({text:this.settings.nodeName+" "+this.nextId()});return n.data("text",s),this.dragableElement(s,n),Rt(n,this),n}createBusinessNode(t,e){return this.createTypeNode(kt.Business,t,e)}createServiceNode(t,e){return this.createTypeNode(kt.Service,t,e)}createManualNode(t,e){return this.createTypeNode(kt.Manual,t,e)}createMessageNode(t,e){return this.createTypeNode(kt.Message,t,e)}createTypeNode(t,e,a){let i=this.createNode(e,a);i.data("nodeType",t);let o=this.renderHtmlNode(t.toLowerCase(),e+5,a+5,20,20).attr({color:this.themeColor});return i.data("icon",o),i}createNodeElement(t){let{id:e,type:a,component:i}=t,{type:o,attrs:r,textAttrs:n}=i,s=null;"rect"==o&&(s=this.renderRect(0,0,0,0,0)),s.id=e,r.rx&&(s.attrs.r=r.rx),s.data("nodeType",a),s.attr(r);let l=this.renderHtmlText(0,0,.8*r.width).attr(n);s.data("text",l),this.dragableElement(l,s);let h=a.toLowerCase(),{x:d,y:c}=r,u=this.renderHtmlNode(h,d+5,c+5,20,20).attr({color:this.themeColor});return s.data("icon",u),this.initElement(s),s}loadCustomHTMLElement(t,e,a,i,o){let r=this.loadHTMLElement(t,e,a,i,o);return r.data("customType",e),r}loadHTMLElement(t,e,a,i,o){let{attrs:r,textAttrs:n}=a,s=this.renderHtmlNode(e,0,0,0,0,o);if(s.id=t,s.data("nodeType",i),s.attr(r),n&&"object"==typeof n&&Ct(n).length>0){let t=this.renderHtmlText(0,0,.8*r.width).attr(n);s.data("text",t),this.dragableElement(t,s)}return Rt(s,this),this.initElement(s),s}loadImageElement(t,e,a,i){let{attrs:o}=a;o.src=e;let r=this.renderImage("",0,0,0,0);return r.id=t,r.data("nodeType",i),r.attr(o),this.initElement(r),r}createConnectElement(t,e,a){let{id:i,pathStyle:o,component:r}=t,{attrs:n,textAttrs:s}=r,l=this.paper.path("").attr(n);l.id=i,this.setConnectArrow(l);let h=this.renderHtmlText(0,0,this.option.maxPathTextWidth||100).attr(s);l.data("text",h),this.dragableElement(h),l.data("from",e),l.data("to",a);let d=e.data("out")||{};d[i]=l,e.data("out",d);let c=a.data("in")||{};if(c[i]=l,a.data("in",c),!o||"broken"==o){let t,i,o=function(t){if(Array.isArray(t))return t;let e=[];try{if("string"==typeof t){let a=null,i=[],o=-1;for(let r=0,n=t.length;r<n;++r){let n=t.charAt(r);switch(n){case"M":case"L":case"H":case"V":case"h":case"v":a&&(i.push(parseFloat(t.substring(o,r).trim())),e.push([a,...i])),i=[],a=n,o=r+1;break;case",":i.push(parseFloat(t.substring(o,r).trim())),o=r+1}}if(a){i.push(parseFloat(t.substring(o).trim()));let r=[a];r.push(...i),e.push(r)}}}catch(a){console.error(a)}return e}(n.path||n.d),r=o.length,s=[];for(let n=0;n<r;n++){let h=o[n],d=this.createControlDragRect(h[1],h[2],l);if(d.data("cpIndex",-1),s.push(d),0==n)t=d,t.data("fromNode",e),t.data("type","start"),l.data("start",t);else{let t=s[n-1];t.data("right",d),d.data("left",t);let e=this.createControlDragRect((t.attr("x")+d.attr("x")+5)/2,(t.attr("y")+d.attr("y")+5)/2,l);e.data("cpIndex",0),e.data("left",t),e.data("right",d),t.data("rightRect",e),d.data("leftRect",e)}n==r-1&&(i=d,i.data("toNode",a),i.data("type","end"),l.data("end",i))}}return this.hideEditElements(l),this.bindSelectEvent(l),this.register(l),l}createPath(t,e){let a=this.themeColor,i=this.paper.path("").attr({stroke:a,"stroke-width":2});this.setConnectArrow(i,a),this.setUUID(i);let o=this.option.pathStyle||"broken";i.data("pathStyle",o),i.data("from",t),i.data("to",e);let r=t.data("out")||{};r[i.id]=i,t.data("out",r);let n=t.data("nodeType"),s="AND"==t.data("gateway"),l=Ct(r).length>1&&!s;var h,d;l||"Split"!=n||s||(l=!0),l?this.setConnectType(i,this.option.defaultConditionType||"Script"):this.setConnectType(i,"Always"),h=i,"function"==typeof(d=this).option.onConnectCreated&&d.option.onConnectCreated.call(d,h,d);let c=e.data("in")||{};return c[i.id]=i,e.data("in",c),this.resetPathData(i,t,e,o),this.bindSelectEvent(i),this.hideEditElements(null),this.selectElement=i,this.register(i),this.handleElementAction(i),i}resetPathData(t,e,a,i){if("hv"==i)this.updateH2VPath(t);else if("qbc"==i)this.removePathRelationRects(t),this.getPathData(e,a);else if("cbc"==i)this.removePathRelationRects(t),this.getPathData(e,a);else{this.removePathRelationRects(t);let o=this.getPathData(e,a),r=o.start,n=o.end;t.attr("d",o.data),t.data("pathStyle",i);let s=r.x,l=r.y,h=n.x,d=n.y,c=(s+h)/2,u=(l+d)/2;if("broken"==i){let i=this.createControlDragRect(s,l,t),o=this.createControlDragRect(h,d,t);i.data("fromNode",e),o.data("toNode",a),t.data("start",i),t.data("end",o),i.data("right",o),o.data("left",i),i.data("type","start"),o.data("type","end");let r=this.createControlDragRect(c,u,t);r.data("cpIndex",0),r.data("left",i),r.data("right",o),i.data("rightRect",r),o.data("leftRect",r)}let p=t.data("text");p?p.attr({x:c-2.5-10,y:u-2.5-10,dx:0,dy:0}):(p=this.renderHtmlText(c-2.5-10,u-2.5-10,this.option.maxPathTextWidth||100).attr({text:this.settings.linkName||" "}),t.data("text",p),this.dragableElement(p))}}getConnectBoundRect(t){if("path"!=t.type)return null;let{x:e,y:a,width:i,height:o}=t.getBBox();return{x:e-5,y:a-5,width:i+10,height:o+10}}createDragRect(t,e,a,i){let o=this.renderRect(t-2.5,e-2.5,5,5,2.5,2.5).attr({fill:"#fff",stroke:this.themeColor,cursor:"move"});return a&&o.data(a),this.dragableElement(o,o,i),o}createControlDragRect(t,e,a){let i=this.renderRect(t-2.5,e-2.5,5,5,2.5,2.5).attr({fill:"#fff",stroke:this.themeColor,cursor:"move"});i.data("type","center"),a&&i.data("host",a);let o=this;return i.drag((function(t,e,a,i){o.controlOnMove(this,t,e,a,i)}),(function(){o.controlOnStart(this),o.dragingElement=this}),(function(){o.controlOnUp(this),o.dragingElement=null})),i}controlOnMove(t,e,a,i,o){let r=this.scaleValue||1;e/=r,a/=r,t.attr({x:t.ox+e,y:t.oy+a});let n=t.data("type"),s=t.data("host");if("start"==n){t.hide();let e=t.data("right");t.data("rightRect").attr({x:(t.attr("x")+e.attr("x"))/2,y:(t.attr("y")+e.attr("y"))/2}),this.updatePathDataAndText(s)}else if("end"==n){t.hide();let e=t.data("left");t.data("leftRect").attr({x:(t.attr("x")+e.attr("x"))/2,y:(t.attr("y")+e.attr("y"))/2}),this.updatePathDataAndText(s)}else this.updatePathByControlRect(t);this.validateDropLink(s,"start"==n),this.handleAlignLines(t)}controlOnStart(t){t.ox=t.attr("x"),t.oy=t.attr("y"),t.data("dragging",!0);let e=t.data("host"),a=t.data("type");"start"!=a&&"end"!=a||(this.dragingLine=e)}controlOnUp(t){t.data("disableRestore",null),t.data("restore")&&(t.data("restore",null),t.data("cpIndex",0));let e=t.data("type"),a=t.data("host"),i=this.dropNode;if(i){if(!this.validateDropLink(a,"start"==e))return this.updatePathBound(a),this.updatePathDataAndText(a),void(this.dragingLine=null);let o=a;if("start"==e){t.show();let e=t.data("fromNode");e.data("out")&&delete e.data("out")[o.id];let a=i;o.data("from",a),t.data("fromNode",a);let r=a.data("out")||{};r[o.id]=o,a.data("out",r),this.updatePathBound(o),this.updatePathDataAndText(o)}else if("end"==e){t.show();let e=o.data("to");e.data("in")&&delete e.data("in")[o.id];let a=i;o.data("to",a),t.data("toNode",a);let r=a.data("in")||{};r[o.id]=o,a.data("in",r),this.updatePathBound(o),this.updatePathDataAndText(o)}}else this.updatePathBound(a),this.updatePathDataAndText(a);this.dragingLine=null,this.hideAlignLines()}dragableElement(t,e,a){let i=this,o={moved:!1};e||(e=t),t.drag(((t,r)=>{if(i.option.editable){if(!o.moved){if(!(t*t+r*r>0))return;o.moved=!0}i.elementDragMove(e,t,r),a&&a()}}),(()=>{if(i.option.editable)return i.elementDragStart(e),i.dragingElement=t,i.enableHistory()&&(o.undoData=JSON.stringify(i.getData())),!1}),(()=>{if(i.option.editable){if(i.elementDragUp(e),i.dragingElement=null,t.attr({opacity:1}),i.hideAlignLines(),i.enableHistory()&&o.moved){let t=JSON.stringify(i.getData()),e=o.undoData;i.addAction({undo:()=>i.setData(e),redo:()=>i.setData(t)})}o={}}}))}getVisibleNodes(){let t=this.elements,e=[];for(let a of bt(t))"path"!=a.type&&e.push(a);return e}handleAlignLines(t){if(!this.option.disableAlignLine&&t)try{let{x:e,y:a,width:i,height:o}=t.attrs,r={x:e+i/2,y:a+o/2},n=this.getVisibleNodes(),s=null;(s=t.data("left"))&&n.push(s),(s=t.data("right"))&&n.push(s);let l=!1,h=!1,d=0,c=0;for(let u of n){if(u==t)continue;let{x:e,y:a,width:i,height:o}=u.attrs,n={x:e+i/2,y:a+o/2},s=r.x-n.x,p=r.y-n.y;!h&&s*s<25&&(h=!0,d=n.x),!l&&p*p<25&&(l=!0,c=n.y)}l?(this.horizontalLine.attr({width:"100%",y:c}).show(),t.attr({y:c-o/2})):this.horizontalLine.hide(),h?(this.verticalLine.attr({x:d,height:"100%"}).show(),t.attr({x:d-i/2})):this.verticalLine.hide()}catch(e){console.error(e)}}hideAlignLines(){this.option.disableAlignLine||(this.horizontalLine.hide(),this.verticalLine.hide())}initElement(t){this.dragableElement(t),this.updateElements(t,!0),this.hideEditElements(t),this.bindSelectEvent(t),this.bindMouseOverOutEvent(t),this.bindTouchEvent(t),this.register(t)}elementDragStart(t){t.ox=t.attr("x"),t.oy=t.attr("y"),t.attr({opacity:.8}),this.endInputEdit(),t.attr("cursor","move")}elementDragMove(t,e,a){let i=this.scaleValue,o={x:t.ox+e/i,y:t.oy+a/i};t.attr(o);let{editable:r}=t.data();t.isText()||!1===r||(this.handleAlignLines(t),this.updateElements(t),this.showEditElements(t))}elementDragUp(t){if(t.isText()){let{ox:e,oy:a}=t,{x:i,y:o,dx:r=0,dy:n=0}=t.attr();t.attr({dx:r+i-e,dy:n+o-a})}delete t.ox,delete t.oy}resizeOnStart(t){t.ox=t.attr("x"),t.oy=t.attr("y")}resizeOnMove(t,e,a,i,o){let r,n,s,l,h=this.scaleValue||1;t.attr({x:t.ox+e/h,y:t.oy+a/h});let d=t.data("dgl"),c=t.data("host");if(d){r=gt(t.attr("x"),d.attr("x"))****,n=gt(t.attr("y"),d.attr("y"))****,s=yt(t.attr("x")-d.attr("x"))-10,l=yt(t.attr("y")-d.attr("y"))-10,s=ft(s,80),l=ft(l,30);var u=t.data("dtn");"w"==u||"e"==u?c.attr({x:r,width:s}):"n"==u||"s"==u?c.attr({y:n,height:l}):c.attr({x:r,y:n,width:s,height:l})}this.updateElements(c),this.showEditElements(c)}linkToolOnDragMove(t,e,a){let{x:i,y:o}=t.attrs,r=this.scaleValue||1,n=i+e/r,s=o+a/r,l=t.data("dropEndRect"),h=t.data("from");null==l?(l=this.renderRect(n,s,20,5).attr({fill:"#000000",cursor:"move",opacity:0}).hide(),t.data("dropEndRect",l)):l.attr({x:n,y:s});let d=this.getPathData(h,l),c=t.data("virtualPath");null==c?(c=this.paper.path(d.data).attr({stroke:this.themeColor,"stroke-width":2,fill:this.themeColor,"stroke-dasharray":"2 2"}),t.data("virtualPath",c)):(c.attr("d",d.data),c.attr("stroke",this.themeColor)),c.show(),c.data("from",h),this.dragingLine=c,this.validateDropLink(c)}linkToolOnDragUp(t){this.hideDropRect();let e=t.data("virtualPath");e&&e.hide();let a=this.dropNode;if(a){if(!this.validateDropLink(e))return console.log("vk false"),void(this.dragingLine=null);let t=e.data("from");if(t==a)return void(this.dragingLine=null);let i=this.isConnected(t,a,!0),o=this.createPath(t,a),r=o.data("pathStyle");if(this.dropNode=null,this.dragingLine=null,i&&"broken"==r){let t,e=o.data("start").attr("x"),a=o.data("start").attr("y"),i=o.data("end").attr("x"),r=o.data("end").attr("y"),n=0,s=0,l=3.141592653;if(a==r)n=(e+i)/2,t=yt(i-e),s=a-t/2*wt(15/180*l),s=ft(s,1);else if(e==i);else{t=mt((i-e)*(i-e)+(r-a)*(r-a));let o=t/2*wt(15/180*l),h=(e+i)/2,d=(a+r)/2,c=At(-(i-e)/(r-a)),u=xt(c);n=o*vt(c)+h,s=o*u+d}if(n||s){let t=o.data("start").data("rightRect");t.attr({x:n,y:s}),this.updatePathByControlRect(t)}}}else this.dragingLine=null}validateDropLink(t,e){let a=this.dropNode;if(a){let i=t.data("from"),o=t.data("to"),r=e?a.data("out"):i.data("out");if(r)for(let t in r){if(r[t].data("to")==(e?o:a))return a.attr("cursor","not-allowed"),!1}return!0}return!0}hideEditElements(t){let e=null;if(t&&"rect"!=(e=t.type)&&"image"!=e&&"html"!=e){if("path"==e){let e=t.data("start");if(e){e.hide();let t=e.data("right");for(;t;){t.hide(),t.data("leftRect").hide(),t=t.data("right")}}this.deleteTool.hide()}}else{let{nw:t,w:e,sw:a,n:i,s:o,ne:r,e:n,se:s,dashOuterPath:l,exchange:h,linkTool:d,nextTaskTool:c,nextSplitTool:u,nextEndTool:p,deleteTool:m}=this;t.hide(),e.hide(),a.hide(),i.hide(),o.hide(),r.hide(),n.hide(),s.hide(),l.hide(),h.hide(),d.hide(),c.hide(),u.hide(),p.hide(),m.hide()}t&&t.attr("cursor","auto"),this.hidePopupMenu()}hidePopupMenu(t){let e;(e=this.popupMenu)&&setTimeout((()=>{e.hide()}),t||200)}setSelectElement(t){this.selectElement=t,this.showEditElements(t)}showEditElements(t){if(!this.option.editable)return;let e=t.type,a="image"==e;if("rect"==e||a||"html"==e){let{x:e,y:i,width:o,height:r}=t.attrs,n=t.data("nodeType"),{nw:s,w:l,sw:h,n:d,s:c,ne:u,e:p,se:m,dashOuterPath:g,exchange:f,linkTool:y,nextTaskTool:x,nextSplitTool:v,nextEndTool:w,deleteTool:A}=this,E=e-5,M=i-5,T=e+o+5,C=i+r+5,b=T-E,S=C-M,z="M"+E+","+M+"L"+E+","+C+"L"+T+","+C+"L"+T+","+M+"L"+E+","+M;g.attr({path:z,fill:"none",stroke:"#909399"}).show(),a||(s.data("host",t).attr({x:E-2.5,y:M-2.5}).show(),l.data("host",t).attr({x:E-2.5,y:M+S/2-2.5}).show(),h.data("host",t).attr({x:E-2.5,y:C-2.5}).show(),d.data("host",t).attr({x:E+b/2-2.5,y:M-2.5}).show(),c.data("host",t).attr({x:E+b/2-2.5,y:C-2.5}).show(),u.data("host",t).attr({x:T-2.5,y:M-2.5}).show(),p.data("host",t).attr({x:T-2.5,y:M+S/2-2.5}).show(),m.data("host",t).attr({x:T-2.5,y:C-2.5}).show()),"End"!=n?(y.data("from",t).attr({x:T+10,y:M}).show(),x.data("from",t).attr({x:T+10,y:M+16}).show(),v.data("from",t).attr({x:T+10,y:M+32}).show(),w.data("from",t).attr({x:T+10,y:M+48}).show(),A.attr({x:T+10+16,y:M-5}).data("host",t).show(),"Start"!=n&&"Join"!=n&&f.data("from",t).attr({x:T+10+16,y:M+13}).show()):A.attr({x:T+10,y:M-5}).data("host",t).show()}else if("path"==e){let e=t.data("start");if(e){e.show();let t=e.data("right");for(;t;){t.show(),t.data("leftRect").show(),t=t.data("right")}}let{x:a,y:i}=t.getPointAtLength(15);this.deleteTool.attr({x:a,y:i}).data("host",t).show()}t.attr("cursor","move")}updateActiveText(t,e){let a=this.textElement;a&&(a.attr("text",t),e&&a.show())}updateElements(t){let e=this,a=t.data("text"),{x:i,y:o,width:r,height:n}=t.attrs;a&&(a.attr({x:i+r/2,y:o+n/2}),a.setWidth(.8*r));let s=t.data("icon");if(s&&s.attr({x:i+5,y:o+5}),t.data("in")){let a=t.data("in");for(let t in a)e.updatePath(a[t])}if(t.data("out")){let a=t.data("out");for(let t in a)e.updatePath(a[t])}}updatePath(t){let e=t.data("pathStyle");switch(e){case"broken":this.updateBrokenPath(t);break;case"straight":this.resetPathData(t,t.data("from"),t.data("to"),e);break;case"hv":this.updateH2VPath(t);break;case"qbc":this.updateQbcPath(t)}}updateBrokenPath(t){let e=t.data("start");if(e){let a=t.data("end");if(e.data("fromNode")){let t=e.data("fromNode"),i=e.data("right"),o=a==i&&null!=a.data("toNode"),r=this.getPathData(t,o?a.data("toNode"):i).start;e.attr({x:r.x-2.5,y:r.y-2.5}),e.data("rightRect").attr({x:(e.attr("x")+i.attr("x"))/2,y:(e.attr("y")+i.attr("y"))/2})}if(a.data("toNode")){let t=a.data("toNode"),i=a.data("left"),o=e==i&&null!=e.data("fromNode"),r=this.getPathData(o?e.data("fromNode"):i,t).end;a.attr({x:r.x-2.5,y:r.y-2.5}),a.data("leftRect").attr({x:(i.attr("x")+a.attr("x"))/2,y:(i.attr("y")+a.attr("y"))/2})}let i=e.data("rightRect"),o=t.data("text");this.updateText(o,{x:i.attr("x")-10,y:i.attr("y")-10});let r="M"+(e.attr("x")+2.5)+","+(e.attr("y")+2.5),n=e.data("right");for(;n;)r+="L"+(n.attr("x")+2.5)+","+(n.attr("y")+2.5),n=n.data("right");t.attr("d",r)}else{let e=t.data("from"),a=t.data("to");this.resetPathData(t,e,a,"broken")}}updateH2VPath(t){this.removePathRelationRects(t);let e=this.getH2VPathData(t);t.attr("d",e.data);let a=e.start,i=e.end,o=a.x,r=a.y,n=(o+i.x)/2-2.5-10,s=(r+i.y)/2-2.5-10,l=t.data("text");l?this.updateText(l,{x:n,y:s}):(l=this.renderHtmlText(n,s,this.option.maxPathTextWidth||100).attr({text:this.settings.linkName||" "}),t.data("text",l),this.dragableElement(l))}updateQbcPath(t){this.removePathRelationRects(t);let e,a,{qbc_control:i,from:o,to:r,fromSide:n,toSide:s}=t.data(),{x:l,y:h,width:d,height:c}=o.attr(),{x:u,y:p,width:m,height:g}=r.attr(),f={},y={},x=0,v=0;if(i)x=i.data("qbc_control_offsetx"),v=i.data("qbc_control_offsety");else{let e=this.getPathData(o,r);n=e.fromSide,s=e.toSide;const a=()=>{i.attr(),this.updateQbcPath(t)};i=this.createDragRect(0,0,{editable:!1,qbc_control_offsetx:0,qbc_control_offsety:0},a),t.data("qbc_control",i)}"n"==n||"s"==n?(f.x=l+d/2,f.y="n"==n?h-5:h+c+5):(f.x="w"==n?l-5:l+d+5,f.y=h+c/2),"n"==s||"s"==s?(y.x=u+m/2,y.y="n"==s?p-5:p+g+5):(y.x="w"==s?u-5:u+m+5,y.y=p+g/2);let w=(f.x+y.x)/2,A=(f.y+y.y)/2,E=f.x==y.x?0:-(y.y-f.y)/(y.x-f.x);if(0==E)e=w,a=A+50;else{let t=At(E);e=50*xt(t)+w,a=50*vt(t)+A}let M="M"+f.x+","+f.y+"Q"+e+","+a+","+y.x+","+y.y;t.attr("d",M),t.data("fromSide",n),t.data("toSide",s),i.attr({x:e+x,y:a+v})}updatePathDataAndText(t){if("broken"==t.data("pathStyle")){let e=t.data("start"),a=e.data("rightRect");this.updateText(t.data("text"),{x:a.attr("x")-10,y:a.attr("y")-10});let i="M"+(e.attr("x")+2.5)+","+(e.attr("y")+2.5),o=e.data("right");for(;o;)i+="L"+(o.attr("x")+2.5)+","+(o.attr("y")+2.5),o=o.data("right");t.attr("d",i)}}updateText(t,e){let{x:a,y:i}=e,o=h(e,["x","y"]),{dx:r=0,dy:n=0}=t.attr();t.attr(l({x:a+r,y:i+n},o))}updatePathByControlRect(t){let e=t.data("host"),a=t.data("cpIndex"),i=t.data("left"),o=t.data("right"),r=t.data("disableRestore");if(t.data("restore"))t.attr({x:(i.attr("x")+o.attr("x"))/2,y:(i.attr("y")+o.attr("y"))/2});else{if(i&&i.data("fromNode")){let e=i.data("fromNode"),a=this.getPathData(e,t).start;i.attr({x:a.x-2.5,y:a.y-2.5})}if(o&&o.data("toNode")){let e=o.data("toNode"),a=this.getPathData(t,e).end;o.attr({x:a.x-2.5,y:a.y-2.5})}if(-1==a){let e=t.data("leftRect");e.attr("x",(i.attr("x")+t.attr("x"))/2),e.attr("y",(i.attr("y")+t.attr("y"))/2);let a=t.data("rightRect");if(a.attr("x",(o.attr("x")+t.attr("x"))/2),a.attr("y",(o.attr("y")+t.attr("y"))/2),!r){let r=t.attr("x"),n=t.attr("y"),s=i.attr("x"),l=i.attr("y"),h=o.attr("x"),d=o.attr("y"),c=(r-s)*(r-h)>0&&(n-l)*(n-d)>0;((t,e,a,i,o,r)=>{let n=T((t-a)*(t-a)+(e-i)*(e-i)),s=T((a-o)*(a-o)+(i-r)*(i-r)),l=T((o-t)*(o-t)+(r-e)*(r-e)),h=(n+s+l)/2;return 2*T(h*(h-n)*(h-s)*(h-l))/s})(r,n,s,l,h,d)<=3&&!c&&(e.remove(),a.remove(),t.data("restore",!0),i.data("right",o),i.data("rightRect",t),o.data("left",i),o.data("leftRect",t),t.attr({x:(i.attr("x")+o.attr("x"))/2,y:(i.attr("y")+o.attr("y"))/2}))}}else{t.data("disableRestore",!0);let a=this.createControlDragRect((i.attr("x")+t.attr("x")+5)/2,(i.attr("y")+t.attr("y")+5)/2,e);a.data("cpIndex",0),a.data("left",i),a.data("right",t);let r=this.createControlDragRect((o.attr("x")+t.attr("x")+5)/2,(o.attr("y")+t.attr("y")+5)/2,e);r.data("cpIndex",0),r.data("left",t),r.data("right",o),t.data("leftRect",a),t.data("rightRect",r),o.data("leftRect",r),i.data("rightRect",a),o.data("left",t),i.data("right",t),t.data("cpIndex",-1)}this.updatePathDataAndText(e)}}updatePathBound(t){let e=t.data("from"),a=t.data("to"),i=t.data("start"),o=t.data("end"),r=i.data("right"),n=i.data("rightRect"),s=e,l=r;r==o&&(l=a);let h=this.getPathData(s,l).start;i.attr({x:h.x-2.5,y:h.y-2.5}),n.attr({x:(i.attr("x")+r.attr("x"))/2,y:(i.attr("y")+r.attr("y"))/2}),a=t.data("to");let d=o.data("leftRect"),c=o.data("left");s=c,i==c&&(s=e),l=a;let u=this.getPathData(s,l).end;o.attr({x:u.x-2.5,y:u.y-2.5}),d.attr({x:(c.attr("x")+o.attr("x"))/2,y:(c.attr("y")+o.attr("y"))/2})}isDropContainer(t,e){if(!t||!e)return;let{x:a,y:i}=t.attrs,{x:o,y:r,width:n,height:s}=e.attrs;return a>=o&&a<=o+n&&i>=r&&i<=r+s}nextId(){return++this.cnt}unregister(t){this.elements[t]=null,delete this.elements[t]}register(t){let e=t.id;e&&(this.elements[e]=t)}isConnected(t,e,a){let i={},o=t.data("out");for(i[t.id]=t;o&&Ct(o).length;){let t={};for(let a in o){let r=o[a].data("to");if(r==e)return!0;if(!i[r.id]){let e=r.data("out");for(let a in e)t[a]=e[a]}}o=t}return!!a&&this.isConnected(e,t,!1)}isAloneConnect(t){if("path"!=t.type)return null;let e=t.data("from");return e&&1==Ct(e.data("out")).length}getSourceGatewayType(t){if("path"!=t.type)return null;let e=t.data("from");return"Split"!=e.data("nodeType")?null:e.data("gateway")}setElementName(t,e){let a=null;t&&(a=t.data("text"))&&a.attr("text",e)}setConnectArrow(t,e){t.node&&(e||(e=t.attr("stroke"),Y(this.paper.node,e)),t.node.style.markerEnd=`url(#${this.paper.id}${e})`)}setConnectColor(t,e){t.attr("stroke",e),this.setConnectArrow(t,e),this.setConnectType(t)}setHtmlNodeColor(t,e){t.attr("color",e);let a=t.nodeType,i=null;if(a==kt.Start?i="start":a==kt.End?i="end":a==kt.Custom&&(i=t.customType),i){let a=this.getCustomInnerHTML(i,{color:e,scene:"element"},t);a&&t.updateHTML(a)}}setConnectType(t,e){t.conditionType=e}toElementData(t){let e=t.type,{connectDatas:a,nodeDatas:i}=this;if("path"!=e){let a={},o=t.data("nodeType"),r=t.data("customType"),n={};o==kt.Custom&&(n.customType=r);let s=t.data("text"),h=l({},t.attrs),d={type:e,attrs:h,textAttrs:s&&s.attrs};return delete h.opacity,delete h.cursor,"image"==e&&(delete h.src,delete d.textAttrs),Tt(a,l(l(l({id:t.id,name:s&&s.attr("text"),type:o},n),Q(t,i)),{component:d})),a}{let i=t.data("from"),o=t.data("to"),r=t.data("text");return l(l({id:t.id,name:r.attr("text"),fromId:i.id,toId:o.id},Q(t,a)),{component:{type:e,attrs:t.attrs,textAttrs:r.attrs}})}}fromElementData(t){let e,{id:a,type:i,customType:o,gateway:r,component:n}=t,{connectDatas:s,nodeDatas:l,elements:h}=this;if("path"==n.type){let{fromId:a,toId:i}=t,o=h[a],r=h[i];e=this.createConnectElement(t,o,r),Z(e,s,t),this.setConnectType(e,t.conditionType)}else switch(i){case"Start":e=this.loadHTMLElement(a,"start",n,"Start"),Z(e,l,t);break;case"End":e=this.loadHTMLElement(a,"end",n,"End"),Z(e,l,t);break;case"Split":{let i=r.toLowerCase();e=this.loadHTMLElement(a,i,n,"Split",(t=>new J(t))),Z(e,l,t);break}case"Join":e=this.loadHTMLElement(a,"join",n,"Join"),Z(e,l,t);break;case"Custom":e=this.loadCustomHTMLElement(a,o,n,"Custom"),Z(e,l,t);break;default:e=this.createNodeElement(t),Z(e,l,t),e.data("nodeType",i)}h[a]=e}syncElements(){let{elements:t}=this,e={},a=[];for(let i in t){let o=t[i],r=o.id;r||(r=i,o.id=r),o.isPath()&&a.push(o),e[r]&&e[r].remove(),e[r]=o}for(let i of a){let{from:t,to:a}=i.data(),o=t.id,r=a.id;o!=r&&e[o]&&e[r]||(i.remove(),delete e[i.id])}this.elements=e}getData(){let{processId:t,processName:e}=this,a={id:t,name:e},i=null,o=a.nodes=[],r=a.connects=[];this.syncElements();let{elements:n}=this;for(let s in n){let t=n[s],e=this.toElementData(t);if(t.isPath())r.push(e);else{"Start"==t.data("nodeType")&&(i=s),o.push(e)}}return a.startNodeId=i,a}setData(t){if("string"==typeof t)try{t=JSON.parse(t)}catch(r){}let{id:e,name:a,nodes:i=[],connects:o=[]}=t||{};this.reset(),Tt(this,{processId:e,processName:a});for(let n of i)this.fromElementData(n);for(let n of o)this.fromElementData(n);this.setElementsColor(this.themeColor)}validate(){let{processId:t,processName:e}=this;if(!t)return"流程id不能为空";if(!e)return"流程名称不能为空";let a=[],i=[],{elements:o}=this,r=[];for(let h in o){let t=o[h];if("path"!=t.type){let e=t.data("nodeType"),o=t.data("text"),n=o&&o.attr("text"),s=t.data("in")||{},l=t.data("out")||{};if("Start"==e){if(a.push(h),0==Ct(l).length)return this.setSelectElement(t),"开始节点没有定义出口"}else if("End"==e){if(i.push(h),0==Ct(s).length)return this.setSelectElement(t),"结束节点没有定义入口"}else{if(0==Ct(s).length)return this.setSelectElement(t),`节点[id=${h},name=${n}]没有定义入口`;if(0==Ct(l).length)return this.setSelectElement(t),`节点[id=${h},name=${n}]没有定义出口`;r.push(t)}}}let n=[],s=[];for(let h of r){let t=h.id,e=h.data("text"),a=e&&e.attr("text");if(this.checkClosedLoop(h))return this.setSelectElement(h),`从节点[id=${t},name=${a}]开始存在闭环`;let i=h.data("nodeType"),o=h.data("gateway");"Split"==i&&"XOR"!=o&&n.push(h),"Join"==i&&s.push(h)}let l=a.length;if(0==l)return"流程没有找到开始节点";if(l>1)return"流程开始节点有且只能有一个";if(0==i.length)return"流程没有找到结束节点";for(let h of n){let t=this.matchJoinElement(h,s),{id:e}=h;if(!t)return this.setSelectElement(h),`并行分支节点[id=${e}]没有匹配到相对应的聚合网关。`;let a=s.indexOf(t);s.splice(a,1)}return null}matchJoinElement(t,e){if(!e||0==e.length)return null;let a=this.getExitPaths(t),i=[];for(let o of e){let t=o.id,e=!1,r=0;for(let i of a){let a=i.indexOf(t);if(-1==a){e=!0;break}r=ft(r,a)}e||i.push({maxIndex:r,joinElement:o})}return 0==i.length?null:(i.sort(((t,e)=>t.maxIndex>e.maxIndex?-1:1)),i[0].joinElement)}getExitPaths(t,e){if(!t)return null;let a=t.data("nodeType");if("End"==a||"Termination"==a)return[[t.id]];let i=t.data("out");if(!i||0==Ct(i).length)return[[t.id]];e||(e=[]);let o=[];for(let r in i){let a=i[r].data("to");if(!e.includes(r)){let i=this.getExitPaths(a,[...e,r]);for(let e of i)e.unshift(t.id),o.push(e)}}return o}checkClosedLoop(t){let e=(t,a)=>{if("End"==t.data("nodeType"))return!0;let i=t.id;if(a.includes(i))return!1;a.push(i);let o=t.data("out")||{};if(0==Ct(o).length)return!1;for(let r in o){let t=o[r].data("to");if(e(t,a))return!0}return!1};return!e(t,[])}setEditable(t){this.option.editable=t,t||this.hideEditElements(this.selectElement),this.endInputEdit()}getElementById(t){return this.elements[t]}getElementByUUID(t){if(!t)return null;let e=bt(this.elements);for(let a of e){if(a.data().uuid==t)return a}return null}getStartNode(){let{startNodeId:t}=this.getData();return this.getElementById(t)}getNextNodes(t){if(!t)return null;let e="string"==typeof t?this.getElementById(t):t;if(!e)return null;let a=[];try{let t=e.data("out");for(let e in t){let i=t[e];a.push(this.toElementData(i.to))}}catch(i){}return a}setElementsColor(t){let{connectColors:e,dom:a,elements:i,groupSelection:o,nw:r,w:n,sw:s,n:l,s:h,ne:d,e:c,se:u,dashOuterPath:p}=this;e.includes(t)||(e.push(t),Y(a,t));for(let m in i){let e=i[m],a=e.type;if("path"==a)this.setConnectColor(e,t);else if("html"==a)this.setHtmlNodeColor(e,t);else{e.attr("stroke",t);let a=e.data("icon");a&&a.attr("color",t)}}r.attr("stroke",t),n.attr("stroke",t),s.attr("stroke",t),l.attr("stroke",t),h.attr("stroke",t),d.attr("stroke",t),c.attr("stroke",t),u.attr("stroke",t),p.attr("stroke",t),o.attr("stroke",t)}setThemeColor(t){t&&(this.settings.themeColor=t,this.setElementsColor(t),this.setMenuThemeColor(t),this.setToolsStyle({color:t}),this.popupMenu&&this.popupMenu.attr({color:t}),this.horizontalLine&&(this.horizontalLine.attr("stroke",t),this.verticalLine.attr("stroke",t)),this.dropNw&&(this.dropNw.attr("stroke",t),this.dropNe.attr("stroke",t),this.dropSw.attr("stroke",t),this.dropSe.attr("stroke",t)),this.connectRect&&this.connectRect.attr("stroke",t))}alertMessage(t,e){"function"==typeof this.option.alertMessage&&this.option.alertMessage(t,e)}setElementColorById(t,e){let a=this.getElementById(t);return this.setElementColor(a,e),a}setElementColorByUUID(t,e){let a=this.getElementByUUID(t);return this.setElementColor(a,e),a}setElementColor(t,e){if(!t)return;let a=t.type;if("path"==a){let a={stroke:e};t.attr(a),this.setConnectArrow(t),this.setConnectType(t)}else"html"==a?this.setHtmlNodeColor(t,e):t.attr({stroke:e});let i=t.icon;i&&i.attr("color",e)}clearCompleteRecords(){this.completeRecords=[]}resetStatus(t,e){let{elements:a}=this;for(let i in a){let o=a[i];o&&(o.status=t||"init",this.setElementColor(o,e||this.themeColor))}}toFront(t){t&&(t.isSvg()?t.repaint():(t.node.style["z-index"]=++this.elementZIndex,t.text&&(t.text.node.style["z-index"]=++this.elementZIndex)))}completeElement(t,e){if(t.status="completed",this.setElementColor(t,e),this.toFront(t),!t.isPath()){let a=t.data("in");if(a&&1==Ct(a).length)for(let t in a){let i=a[t],o=i.data("from");if(o.nodeType==kt.Start&&"completed"!=o.status){this.completeElement(i,e),this.completeElement(o,e);break}}if(a=t.data("out"),a&&1==Ct(a).length)for(let t in a){let i=a[t],o=i.data("to");if(o.nodeType==kt.End&&"completed"!=o.status){this.completeElement(i,e),this.completeElement(o,e);break}}}}completeElementColorById(t,e){let a=this.setElementColorById(t,e);a&&(this.completeFrontLines(a,e),this.toFront(a))}completeElementColorByUUID(t,e){let a=this.setElementColorByUUID(t,e);a&&(this.completeFrontLines(a,e),this.toFront(a))}completeFrontLines(t,e){if(console.log(" completeFrontLines ",this.completeRecords),!t)return;this.completeRecords||(this.completeRecords=[]),this.completeRecords.push(t.id);let a=t.data("in");for(let i in a){let t=a[i];if(!this.completeRecords.includes(i)){this.completeRecords.push(i);let a=t.data("from");this.completeRecords.includes(a.id)&&(console.log("connectElement.conditionType ",t.conditionType),"Always"==t.conditionType&&this.setElementColor(t,e))}}}getFrontNodes(t){let e=[];try{let a=null;if(a="string"==typeof t?this.getElementById(t):t,!a)return e;if("path"==a.type&&(a=a.data("to")),!a)return e;let i=(t,o)=>{let r=t.data&&t.data("in");if(r)for(let n in r||{})if(!o.includes(n)){o.push(n);let t=r[n].data("from");t==a||e.includes(t)||(e.unshift(t),i(t,o))}};return i(a,[]),e.map((t=>this.toElementData(t)))}catch(a){console.error(a)}return e}completeConnect(t,e,a,i){a=a||this.option.settings.completeColor;let o=this.getElementById(t);if(!o)return void this.alertMessage("开始节点[id="+t+"]不存在",5);let r=null,n=null,s=o.data("out");if(s)for(let l in s){let t=s[l],a=t.data("to");if(a.id==e){n=t,r=a;break}}r?(this.completeElement(o,a),this.completeElement(n,a),r.nodeType==kt.End?this.completeElement(r,a):i&&this.setElementColor(r,i)):this.alertMessage("结束节点[id="+e+"]不存在或无效",5)}completeConnectById(t,e,a){e=e||this.option.settings.completeColor;let i=this.getElementById(t);if(!i||!i.isPath())return void this.alertMessage("连线[id="+t+"]不存在",5);let o=i.data("from"),r=i.data("to");this.completeElement(o,e),this.completeElement(i,e),r.nodeType==kt.End?this.completeElement(r,e):a&&this.setElementColor(r,a)}getConnect(t,e){for(let a in this.elements){let i=this.elements[a];if("path"==i.type&&i.data("from").id==t&&i.data("to").id==e)return i}return null}importJSON(t,e){this.setData(t),e||this.overview()}exportJSON(){if(!this.option.ignoreValidateOnExport){let t=this.validate();if(t)return void this.alertMessage("流程图错误："+t,5)}let t=this.getData();((t,e)=>{let a=new Blob([t]),i="导出文件";"string"==typeof e&&(i=e),R(a,i)})(JSON.stringify(t,null,4),`${this.processId||"flow"}.json`)}onImportFile(t){let e=this,a=t.target.files;if(a&&a.length>0){let t=a[0];if(!t.name.toLowerCase().endsWith(".json"))return void this.alertMessage("只支持JSON格式文件",5);let i=new FileReader;i.readAsText(t,"utf-8"),i.onload=t=>{let a=t.target.result;e.importJSON(a)}}t.target.value=null}handleImport(){this.fileInput.click()}exportImage(){const t=this.flowWrapper,e=(new XMLSerializer).serializeToString(t);const a=`data:image/svg+xml;base64,${btoa(unescape(encodeURIComponent(`<svg ${ut} width="2000" height="2000">\n                <foreignObject x="0" y="0" ${pt}>\n                    ${e}\n                </foreignObject>\n             </svg>`)))}`,i=new Image;i.src=a,i.onload=()=>{console.log("l");const t=document.createElement("canvas");t.width=i.width,t.height=i.height;t.getContext("2d").drawImage(i,0,0);let e=t.toDataURL("image/png").split(","),a=atob(e[1]),o=a.length,r=new Uint8Array(o);for(;o--;)r[o]=a.charCodeAt(o);const n=new Blob([r],{type:"image/png"});R(n,`${this.processId||"flow"}.png`)}}clearElements(){for(let t in this.elements){this.elements[t].remove(),this.elements[t]=null}this.paper.clear()}reset(){this.clearElements(),this.initData(),this.initControlElements(),this.initPaper()}destroy(){this.clearElements(),this.paper.remove(),this.dom.innerHTML="",this.initData(),Et("keydown",this.handleDocumentKeyDown),Et("keyup",this.handleDocumentKeyUp)}}let{props:Bt,methods:Vt}=st;Tt(Pt.prototype,Bt),Tt(Pt.prototype,Vt);const jt={render:(t,e)=>new Pt(t,e)};try{Object.assign(window,{"wastflow-version":"1.0.0-beta46",__wf:jt})}catch(Yt){console.warn(Yt)}export{jt as default};
