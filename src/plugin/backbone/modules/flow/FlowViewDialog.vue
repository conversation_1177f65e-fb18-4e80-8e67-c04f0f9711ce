<template>
  <el-dialog
    class="flow-view-dialog"
    width="90%"
    title="流程图查看"
    :visible.sync="dialogVisible"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    append-to-body
    :before-close="handleClose"
  >
    <div>
      <el-checkbox-group
        v-model="completeConnectIds"
        @change="handleCheckChange"
      >
        <el-checkbox
          v-for="(connect, index) in connects"
          :key="connect.id"
          :label="connect.id"
          >{{ connect.connectName }}</el-checkbox
        >
      </el-checkbox-group>
    </div>

    <div style="height: 60vh">
      <flow-view
        :flow-json="flowRecord.json"
        :complete-connects="completeConnects"
      ></flow-view>
    </div>
  </el-dialog>
</template>

<script>
import FlowView from "./flow-view";

export default {
  name: "FlowViewDialog",
  components: { FlowView },
  props: {
    visible: <PERSON><PERSON><PERSON>,
    title: String,
    flowRecord: Object,
  },
  data() {
    return {
      dialogVisible: false,
      completeConnects: [],
      completeConnectIds: [],
    };
  },
  created() {
    this.dialogVisible = this.visible;
  },
  computed: {
    connects() {
      try {
        let { nodes, connects } = JSON.parse(this.flowRecord.json);
        let nodeMap = {};
        for (let node of nodes) {
          nodeMap[node.id] = node;
        }
        for (let connect of connects) {
          let { fromId, toId } = connect;
          let fromNode = nodeMap[fromId];
          let toNode = nodeMap[toId];
          let fromNodeName = fromNode.type == "Start" ? "开始" : fromNode.name;
          let toNodeName = toNode.type == "End" ? "结束" : toNode.name;
          connect.connectName = fromNodeName + "-" + toNodeName;
        }
        return connects;
      } catch (e) {}
      return [];
    },
  },
  methods: {
    handleCheckChange(ids) {
      console.log("ids", ids);
      this.completeConnects = ids.map(id => ({ id }));
      console.log("this.completeConnects", this.completeConnects);
    },

    /*关闭布局设计弹出框*/
    handleClose() {
      this.dialogVisible = false;
      this.$emit("update:visible", false);
    },
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
      this.completeConnectIds = [];
      this.completeConnects = [];
    },
  },
};
</script>
