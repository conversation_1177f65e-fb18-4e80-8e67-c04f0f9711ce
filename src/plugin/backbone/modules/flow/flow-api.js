import { postJson, get } from "@/utils/axios";
const contextPath = "/commonIntegration";

/**
 * 适配响应数据
 *
 * @param res
 */
const responseData = res => {
  if (!res) {
    return null;
  }
  if (res.config && res.data && res.request) {
    return res.data;
  }
  return res;
};

/**
 * 适配响应error数据
 *
 * @param res
 */
const responseErrorData = err => {
  if (!err) {
    return null;
  }
  if (err.config && err.isAxiosError && err.request && err.response) {
    err = err.response.data;
  }
  return err;
};

/**
 * 包装promise适配大平台和本地外壳
 * */
const wrapperPromise = promise => {
  return new Promise((resolve, reject) => {
    promise
      .then(res => {
        resolve(responseData(res));
      })
      .catch(err => {
        reject(responseErrorData(err));
      });
  });
};

/***
 * Flow分页查询
 *
 * @param params
 * @returns {*}
 */
export const getFlowPage = params => {
  return wrapperPromise(
    postJson(`${contextPath}/process/page`, params, {
      headers: {
        "Content-Type": "application/json",
      },
    })
  );
};

export const getFlowConnects = woId => {
  return wrapperPromise(
    get(`${contextPath}/process/queryTroubleshootingFlowChart?woId=${woId}`)
  );
};

/***
 * 根据id获取Flow
 *
 * @param id
 * @returns {Promise | Promise<unknown>}
 */
export const getFlow = id => {
  return wrapperPromise(get(`${contextPath}/process/info?id=${id}`));
};

/**
 * 删除Flow
 * */
export const deleteFlow = id => {
  return wrapperPromise(
    postJson(`${contextPath}/process/delete`, Array.isArray(id) ? id : [id], {
      headers: {
        "Content-Type": "application/json",
      },
    })
  );
};

/**
 * 保存<Flow>基本信息
 * */
export const saveFlow = record => {
  return wrapperPromise(
    postJson(`${contextPath}/process/save`, record, {
      headers: {
        "Content-Type": "application/json",
      },
    })
  );
};

/**
 * 创建新版本<Flow>基本信息
 * */
export const newVersionFlow = record => {
  return wrapperPromise(
    postJson(`${contextPath}/process/newVersion`, record, {
      headers: {
        "Content-Type": "application/json",
      },
    })
  );
};
