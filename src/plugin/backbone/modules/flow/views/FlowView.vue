<template>
  <div style="height: 100%" :class="[type != 'pc' ? 'full-main' : '']">
    <div v-if="isShowFlowView" :style="vmStyle">
      <flow-view
        :flow-json="flowJson"
        :complete-connects="completeConnects"
        @click-element="handleClickElement"
        @mouseover-element="handleMouseoverElement"
        :type="type"
        :isPortraitScreen="isPortraitScreen"
      ></flow-view>
    </div>
    <!-- 构建一个看不见的元素并能触发气泡 -->
    <template>
      <el-popover
        v-model="popoverVisible"
        ref="popover"
        :visible-arrow="false"
        popper-class="node-detail-propper"
        append-to-body
        class="popover-class"
        :width="popoverWidth"
      >
        <div :style="getFontSize()" class="flow-table-class" v-if="hoverTask">
          <div :style="getPopoverTitle()">
            <div style="margin-left: 5px; font-weight: bold">
              {{ hoverTask.taskName }}
            </div>
            <div style="cursor: pointer" @click="hidePopover">
              <em class="el-icon-close"></em>
            </div>
          </div>
          <div
            v-if="processId == hwProcessIdFixed"
            style="background: #fff; padding: 10px; display: flex; width: 100%"
          >
            <div style="margin-right: 10px; margin-left: 5px; width: 52%">
              <span>操作时间:</span>
              <span style="margin-left: 2px" v-if="hoverTask.startTime">{{
                hoverTask.startTime
              }}</span>
              <span v-else>--</span>
            </div>
            <div>
              <span>执行时长:</span>
              <span style="margin-left: 2px" v-if="hoverTask.duration"
                >{{ hoverTask.duration }}(ms)</span
              ><span v-else>--</span>
            </div>
          </div>
          <div :class="getFontClass()" :style="getTableStyle()">
            <!-- background: #fff; padding: 0 10px -->
            <el-table
              stripe
              :data="hoverTask.result || []"
              :header-cell-style="{ background: '#F2F2F2' }"
              class="max-height-table"
            >
              <!-- <template v-if="processId != hwProcessIdFixed"> -->
              <el-table-column
                label="类型"
                align="left"
                prop="accessType"
                width="50px"
              >
                <template slot-scope="{ row }">
                  <span v-if="row.accessType && removeSpaces(row.accessType)">{{
                    row.accessType
                  }}</span>
                  <span v-else>--</span>
                </template>
              </el-table-column>
              <el-table-column
                label="名称"
                align="left"
                prop="entityName"
                width="120px"
                ><template slot-scope="{ row }">
                  <span v-if="row.entityName && removeSpaces(row.entityName)">
                    {{ row.entityName }}</span
                  >
                  <span v-else>--</span>
                </template></el-table-column
              >
              <el-table-column label="值" align="left" prop="entityValue"
                ><template slot-scope="{ row }">
                  <span v-if="row.entityValue && removeSpaces(row.entityValue)">
                    {{ row.entityValue }}</span
                  >
                  <span v-else>--</span>
                </template></el-table-column
              >
              <!-- </template> -->
              <!-- <template v-else>
                <el-table-column label="序号" type="index" width="50">
                </el-table-column>
                <el-table-column
                  label="描述"
                  align="left"
                  prop="describe"
                ></el-table-column>
              </template> -->
            </el-table>
          </div>
        </div>
        <div class="trigger-div" ref="reference" slot="reference"></div>
      </el-popover>
    </template>
  </div>
</template>
<script>
import { getFlowPage, getFlowConnects } from "../flow-api";
import { mapGetters } from "vuex";

import FlowView from "../flow-view.vue";
import { hwProcessId } from "../api/HwProcessId";
export default {
  components: {
    FlowView,
  },
  props: {
    workOrderId: String,
    type: String,
  },
  name: "FlowViewPage",
  computed: {
    ...mapGetters(["frameStyle"]),
    vmStyle() {
      if (this.type != "pc") {
        return {
          height: `calc(100vh - ${this.frameStyle.headerHeight}px)`,
        };
      } else {
        return { height: "600px" };
      }
    },
    popoverWidth() {
      if (this.type != "pc") {
        return 330;
      } else {
        return 480;
      }
    },
    popoverTableHeight() {
      if (this.type != "pc") {
        return "230px";
      } else {
        return "223px";
      }
    },
  },
  data() {
    return {
      woId: null,
      // 点亮数据
      completeConnects: null,
      // 节点map
      taskMap: {},
      //流程图JSON
      flowJson: null,

      // 气泡显示控制
      popoverVisible: false,
      popoverStyle: {
        // height: "300px",
        // background: "#F8E9E8",
        transition: "all .2s",
        padding: "0px",
        paddingBottom: "10px",
      },
      hoverTask: null,
      hoverElement: null,
      isShowFlowView: false,
      //app判断是否横竖屏 横屏为false,竖屏为true
      isPortraitScreen: null,
      processId: null,
      hwProcessIdFixed: hwProcessId,
    };
  },
  mounted() {
    this.isPortraitScreen = this.$route.query.isPortraitScreen;
    this.woId = this.$route.query.woId || this.workOrderId;
    //"6d6c65a741eb4b678c3e1ff8bcf97424"; "20240425100012_vwldDxqBAe0pTMJcmyz";//this.$route.query.woId || this.workOrderId; //||"3ca13494304c4d04a90960a80ea837c6"; //this.$route.query.woId;
    this.getAdxInfo();

    try {
      let dom = (this.parentScrollDom = document.querySelector(
        ".content-wrap"
      ));
      dom.addEventListener("scroll", this.hidePopover);
    } catch (error) {}
  },
  beforeDestroy() {
    if (this.parentScrollDom) {
      this.parentScrollDom.removeEventListener("scroll", this.hidePopover);
    }
  },

  methods: {
    //获取诊断流程图信息
    getAdxInfo() {
      getFlowConnects(this.woId)
        .then(res => {
          let adxStatus = res?.data?.adxStatus ?? "NO";
          if (adxStatus == "YES") {
            //展示诊断流程图
            this.isShowFlowView = true;
            this.completeConnects = res?.data?.chartData?.linkList ?? [];
            let taskList = res?.data?.chartData?.taskList ?? [];
            this.taskMap = {};
            for (let task of taskList) {
              this.taskMap[task.taskId] = task;
            }
            let processId = res?.data?.chartData?.processId ?? "";
            this.processId = processId;
            let version = res?.data?.chartData?.processVersion ?? "";
            this.getFlowData(processId, version);
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    //获取流程图数据
    getFlowData(processId, version) {
      let param = {
        version: version,
        key: processId,
        pageNum: 1,
        pageSize: 1,
      };
      getFlowPage(param)
        .then(res => {
          this.flowJson = res?.data?.records[0]?.json ?? "";
        })
        .catch(error => {
          console.log(error);
        });
    },
    hidePopover() {
      this.popoverVisible = false;
    },
    handleClickElement(element, evt) {
      if (!this.type) {
        this.showPopover(element);
      }
    },
    /**
     * PC端节点悬浮事件处理 - 显示气泡并更新位置
     *
     * @param element
     * @param evt
     */
    handleMouseoverElement(element, evt) {
      if (this.type) {
        this.showPopover(element);
      }
    },
    showPopover(element) {
      this.hoverTask = null;
      // if (this.processId == this.hwProcessIdFixed) {
      //   this.getHwProcessPopover(element);
      // } else {
      let { id } = element;
      let hoverTask = this.taskMap[id];
      if (!hoverTask) {
        // 如果节点不存在
        this.hidePopover();
        return;
      }
      if (hoverTask.result.length <= 0) {
        this.hidePopover();
        return;
      }
      // 判断数据是否为空
      this.hoverTask = hoverTask;
      // }

      if (this.type != "pc" && this.isPortraitScreen == "true") {
        // if (
        //   this.processId == this.hwProcessIdFixed &&
        //   !element?.meta?.describe
        // ) {
        //   this.popoverVisible = false;
        // } else {
        this.popoverVisible = true;
        this.hoverElement = element;
        this.$nextTick(() => {
          let { innerWidth, innerHeight } = window;
          let propperLeft = innerWidth - this.popoverWidth - 10;
          let propperTop = innerHeight / 2;
          setTimeout(() => {
            // 解决propper组件位置内部自动调整的问题
            this.nodeDetailPropperAssign(propperLeft, propperTop);
          }, 50);
        });
        //}
      } else {
        // if (this.processId == this.hwProcessIdFixed && !element.meta.describe) {
        //   this.popoverVisible = false;
        // } else {
        // 检查是否显示
        this.popoverVisible = true;
        this.hoverElement = element;
        this.$nextTick(() => {
          // 更新气泡位置
          let {
            left,
            top,
            width,
            right,
          } = element.node.getBoundingClientRect();
          let { innerWidth, innerHeight } = window;
          let propperLeft;
          let propperTop;
          if (right + this.popoverWidth > innerWidth + 10) {
            propperLeft = left - this.popoverWidth - 20;
            propperTop = top - 30;
          } else {
            propperLeft = left + width;
            propperTop = top - 30;
          }

          let diff = top + 300 - innerHeight;
          if (diff > -10) {
            propperTop -= Math.abs(diff) + 40;
          }
          setTimeout(() => {
            // 解决propper组件位置内部自动调整的问题
            this.nodeDetailPropperAssign(propperLeft, propperTop);
          }, 50);
        });
        // }
      }
    },
    getHwProcessPopover(element) {
      if (element?.meta?.describe) {
        let hoverTask = {};
        this.$set(hoverTask, "result", []);
        this.$set(hoverTask, "taskName", element.name);
        if (element.meta.describe.indexOf("|$|") != -1) {
          let describeArr = element.meta.describe.split("|$|");
          console.log(describeArr);
          describeArr.forEach(item => {
            hoverTask.result.push({ describe: item });
          });
        } else {
          hoverTask.result.push({ describe: element.meta.describe });
        }
        console.log(hoverTask);
        this.hoverTask = hoverTask;
      } else {
        this.hidePopover();
      }
    },
    nodeDetailPropperAssign(propperLeft, propperTop) {
      Object.assign(document.body.querySelector(".node-detail-propper").style, {
        left: propperLeft + "px",
        top: propperTop + "px",
        ...this.popoverStyle,
      });
    },
    getPopoverTitle() {
      return {
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        padding: "10px",
        backgroundColor: "#F8E9E8",
      };
    },
    getFontSize() {
      return {
        fontSize: this.type != "pc" ? "11px" : "13px",
      };
    },
    getTableStyle() {
      if (this.processId != this.hwProcessIdFixed) {
        return {
          background: "#fff",
          padding: "0 10px",
          marginTop: "10px",
        };
      } else {
        return {
          background: "#fff",
          padding: "0 10px",
        };
      }
    },
    getFontClass() {
      if (this.type != "pc") {
        return "app-font-size-class";
      }
    },
    removeSpaces(str) {
      return str.replace(/\s+/g, "");
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-table .el-table__body-wrapper {
  scrollbar-width: thin !important;
}
::v-deep .custom-theme-default .el-popover .el-table .cell {
  line-height: unset !important;
}
.max-height-table {
  max-height: 223px; /* 你想要的最大高度 */
  overflow-y: auto;
}

.flow-table-class {
  ::v-deep .el-table--small .el-table__cell {
    padding: 4px 0 !important;
  }
  .app-font-size-class {
    ::v-deep .el-table--small {
      font-size: 11px !important;
    }
  }
}
</style>
