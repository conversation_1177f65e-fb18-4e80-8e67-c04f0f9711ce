<template>
  <div class="flow-manage">
    <el-form
      inline
      label-width="100px"
      style="margin-top: 5px; width: calc(100% - 200px)"
    >
      <el-form-item label="流程名称">
        <el-input v-model="param.name" clearable placeholder="请输入流程名称" />
      </el-form-item>
      <el-form-item label="流程标识">
        <el-input v-model="param.key" clearable placeholder="请输入流程标识" />
      </el-form-item>
      <el-form-item>
        <el-button @click="query" type="primary" icon="el-icon-search"
          >查询</el-button
        >
        <el-button @click="reset" icon="el-icon-refresh">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row style="margin: 5px 2px">
      <el-col :span="24">
        <span>
          <div style="display: flex">
            <el-button @click="toAddFlow" type="success" icon="el-icon-plus"
              >新建</el-button
            >
            <el-button type="danger" icon="el-icon-delete" @click="batchDelete"
              >删除</el-button
            >
          </div>
        </span>
      </el-col>
    </el-row>

    <im-table
      ref="table"
      url="/"
      :columns="columns"
      request-data-root="data"
      read-property="records"
      row-key="id"
      :pagination="pagination"
      :request="request"
      @on-load-before="onBeforeLoad"
    >
      <template v-slot:index="{ $index, offsetIndex }">
        <div>{{ $index + offsetIndex + 1 }}</div>
      </template>
      <template v-slot:used="{ row }">
        <el-tag v-if="!row.used">未发布</el-tag>
        <el-tag type="success" v-else>已发布</el-tag>
      </template>
      <template v-slot:status="{ row }">
        <el-switch
          v-model="row.status"
          :active-value="1"
          :inactive-value="0"
        ></el-switch>
      </template>
      <template v-slot:ops="{ row }">
        <el-button
          @click="toEditFlow(row)"
          type="primary"
          icon="el-icon-edit"
          title="编辑"
          >编辑
        </el-button>
        <el-button
          @click="toAddVersion(row)"
          icon="el-icon-plus"
          type="primary"
          title="新增版本"
          >新增版本
        </el-button>
        <el-button
          @click="toDesignFlow(row)"
          icon="el-icon-s-order"
          type="success"
          title="设计"
          >设计
        </el-button>
        <el-button
          @click="viewFlow(row)"
          icon="el-icon-s-order"
          type="success"
          title="查看"
          >查看
        </el-button>
        <el-button
          @click="delFlow(row)"
          icon="el-icon-delete"
          type="danger"
          title="删除"
          >删除
        </el-button>
      </template>
    </im-table>

    <!-- 流程新增和修改抽屉 -->
    <flow-drawer
      ref="flowDrawer"
      :visible.sync="flowDrawerVisible"
      :title="flowOptTitle"
      :flow-record="flowRecord"
      @on-refresh="refresh"
    ></flow-drawer>

    <!-- 流程新版本抽屉 -->
    <flow-new-version-drawer
      ref="flowNewVersionDrawer"
      :visible.sync="flowNewVersionDrawerVisible"
      title="添加新版本"
      :flow-record="flowRecord"
      @on-refresh="refresh"
    ></flow-new-version-drawer>

    <!-- 流程设计弹出框 -->
    <flow-design-dialog
      :visible.sync="flowDesignDialogVisible"
      :flow-record="flowRecord"
      @on-refresh="refresh"
    ></flow-design-dialog>

    <!-- 流程图查看 -->
    <flow-view-dialog
      :visible.sync="flowViewDialogVisible"
      :flow-record="flowRecord"
    ></flow-view-dialog>
  </div>
</template>

<script>
import { deleteFlow, getFlowPage } from "./flow-api";
import FlowDrawer from "./FlowDrawer";
import FlowDesignDialog from "./FlowDesignDialog";
import FlowViewDialog from "./FlowViewDialog";
import FlowNewVersionDrawer from "./FlowNewVersionDrawer";

import { ImTable } from "itsm-common";

export default {
  name: "FlowManage.vue",
  components: {
    ImTable,
    FlowNewVersionDrawer,
    FlowDrawer,
    FlowDesignDialog,
    FlowViewDialog,
  },
  data() {
    return {
      param: {
        name: null,
        key: null,
      },
      columns: [
        { label: "选择", type: "selection" },
        { label: "序号", slot: "index", align: "center", width: "120px" },
        { label: "流程名称", prop: "name" },
        { label: "流程编码", prop: "key" },
        { label: "流程版本", prop: "version" },
        { label: "最后一次更新时间", prop: "lastUpdateTime" },
        { label: "操作", width: 550, slot: "ops" },
      ],
      pagination: {
        currentPage: "pageNum",
        total: "total",
        pageSize: "pageSize",
      },

      flowOptTitle: "流程维护",
      flowDrawerVisible: false,
      flowRecord: {
        id: null,
        name: null,
        key: null,
        version: null,
      },
      flowDesignDialogVisible: false,
      flowViewDialogVisible: false,

      // 新版本
      flowNewVersionDrawerVisible: false,
    };
  },
  mounted() {
    this.query();
  },
  methods: {
    // 查询
    query() {
      this.$refs.table.loadPage(1);
    },
    // 重置
    reset() {
      this.param = {};
    },
    // 自定义api请求
    request(params) {
      return getFlowPage({ ...this.param, ...params });
    },
    /** 新增模型 */
    toAddFlow() {
      this.flowRecord = {
        id: null,
        name: null,
        key: null,
        version: null,
      };
      this.flowOptTitle = "新建流程";
      this.flowDrawerVisible = true;
      this.$nextTick(() => {
        this.$refs.flowDrawer.clearFlowValidates();
      });
    },
    /** 编辑流程 */
    toEditFlow(row) {
      this.flowOptTitle = "编辑流程";
      this.flowDrawerVisible = true;
      this.flowRecord = { ...row };
      console.log("this.flowRecord", this.flowRecord);
      this.$nextTick(() => {
        this.$refs.flowDrawer.clearFlowValidates();
      });
    },
    /** 新增版本 */
    toAddVersion(row) {
      this.flowNewVersionDrawerVisible = true;
      this.flowRecord = { ...row };
      console.log("this.flowRecord", this.flowRecord);
      this.$nextTick(() => {
        this.$refs.flowNewVersionDrawer.clearFlowValidates();
      });
    },
    /** 设计流程 **/
    toDesignFlow(row) {
      this.flowRecord = { ...row };
      this.flowDesignDialogVisible = true;
    },
    /** 查看流程图 **/
    viewFlow(row) {
      this.flowRecord = { ...row };
      this.flowViewDialogVisible = true;
    },
    /** 删除流程 */
    delFlow(item) {
      let { id } = item;
      this.$confirm(`确定要删除流程 - 【${item.name}】吗?`).then(() => {
        deleteFlow(id)
          .then(() => {
            this.delMessage();
          })
          .catch(err => {
            console.error(err);
            this.$message.error(err && err.data);
          });
      });
    },
    /**批量删除*/
    batchDelete() {
      let rows = this.$refs.table.getCheckedRows();
      let ids = rows.map(row => row.id);
      this.$confirm(`确定要删除吗?`).then(() => {
        deleteFlow(ids)
          .then(() => {
            this.delMessage();
          })
          .catch(err => {
            console.error(err);
            this.$message.error(err && err.data);
          });
      });
    },

    delMessage() {
      this.$message.info("删除成功");
      let data = this.$refs.table.getData();
      if (data.length > 1) {
        this.refresh();
      } else {
        this.query();
      }
    },

    /** 刷新 */
    refresh() {
      this.$refs.table.load();
    },
  },

  watch: {},
};
</script>
