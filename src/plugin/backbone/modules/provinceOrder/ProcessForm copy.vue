<template>
  <head-fixed-layout class="draft-eidt-page">
    <template #header>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="15" :md="18" class="head-title"
          >省份故障工单拟稿</el-col
        >
        <el-col :xs="24" :sm="9" :md="6" class="head-handle-wrap">
          <el-button
            type="primary"
            @click="onSheetSave"
            v-loading.fullscreen.lock="sheetSaveLoading"
            >保存</el-button
          >
          <el-button
            type="primary"
            @click="onSheetSubmit"
            v-loading.fullscreen.lock="sheetCommitLoading"
            >提交</el-button
          >
        </el-col>
      </el-row>
    </template>

    <el-form
      :model="sheetForm"
      ref="sheetForm"
      :rules="sheetFormRules"
      label-width="130px"
      label-position="right"
    >
      <el-card shadow="always" :body-style="{ padding: '10px' }">
        <div slot="header" class="card-title">
          <span>工单基本信息</span>
        </div>
        <el-row :gutter="10" type="flex" style="flex-wrap: wrap">
          <el-col :xs="24" :sm="16" :md="16" :offset="0">
            <el-form-item label="工单编号:" prop="sheetNo" required>
              <el-input
                v-model="sheetForm.sheetNo"
                placeholder="请输入工单编号"
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="24" :md="24" :offset="0">
            <el-form-item label="工单主题:" prop="sheetTitle" required>
              <el-input
                v-model="sheetForm.sheetTitle"
                placeholder="请输入内容"
                clearable
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="建单时间:" prop="sheetCreateTime">
              <el-input v-model="sheetForm.createTime" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="建单人:" prop="senderName">
              <el-input
                v-model="sheetForm.senderName"
                placeholder="请输入建人"
                readonly
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="建单人部门:" prop="deptName">
              <el-input
                v-model="sheetForm.deptName"
                placeholder="请输入建单部门"
                readonly
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="10" :md="8" :offset="0">
            <el-form-item label="信息来源:" prop="createType" required>
              <dict-select
                :value.sync="sheetForm.createType"
                :dictId="10003"
                placeholder="请选择内容"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="归属地市:" prop="faultRegion" required>
              <dict-select
                :value.sync="sheetForm.faultRegion"
                :dictId="10002"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="发生时间:" prop="alarmCreateTime">
              <el-date-picker
                v-model="sheetForm.alarmCreateTime"
                type="datetime"
                placeholder="请选择时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="16" :md="16" :offset="0">
            <el-form-item label="紧急程度:" prop="emergencyLevel" required>
              <el-radio-group v-model="sheetForm.emergencyLevel">
                <el-radio
                  v-for="(item, i) in urgentOption"
                  :key="i"
                  :label="item.dictCode"
                  >{{ item.dictName }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item
              label="预估处理时限:"
              prop="processTimeLimit"
              required
            >
              <el-radio-group v-model="sheetForm.processTimeLimit">
                <el-radio
                  v-for="(item, i) in estimatedProTimeLimitOption"
                  :key="i"
                  :label="item.id"
                  >{{ item.name }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item
              label="受理时限(分钟):"
              prop="acceptTimeLimit"
              required
            >
              <el-input
                v-model="sheetForm.acceptTimeLimit"
                placeholder="请输入受理时限"
                clearable
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="所属专业:" prop="professionalType" required>
              <dict-select
                :value.sync="sheetForm.professionalType"
                :dictId="10002"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="网络类型:" prop="networkType" required>
              <dict-select
                :value.sync="sheetForm.networkType"
                :dictId="10018"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="业务中断:" prop="businessDown" required>
              <el-radio-group v-model="sheetForm.businessDown">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="经度:" prop="longitude">
              <el-input
                v-model="sheetForm.longitude"
                placeholder="请输入内容"
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="纬度:" prop="latitude">
              <el-input
                v-model="sheetForm.latitude"
                placeholder="请输入内容"
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="24" :md="24" :offset="0">
            <el-form-item label="故障现象:" prop="faultPhenomenon" required>
              <el-input
                v-model="sheetForm.faultPhenomenon"
                placeholder="请输入内容"
                clearable
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="物业联系人:" prop="propertyUser">
              <el-input
                v-model="sheetForm.propertyUser"
                placeholder="请输入内容"
                clearable
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="物业联系电话:" prop="propertyPhone">
              <el-input
                v-model="sheetForm.propertyPhone"
                placeholder="请输入内容"
                clearable
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card
        shadow="always"
        :body-style="{ padding: '10px' }"
        style="margin-top: 10px"
      >
        <div slot="header" class="card-title">
          <span>工单派送信息</span>
        </div>
        <el-row :gutter="10" type="flex" style="flex-wrap: wrap">
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="附件:" prop="attachmentFile">
              <el-input
                v-model="sheetForm.attachmentFile"
                placeholder="添加附件"
                readonly
                style="width: 100%"
              >
                <el-button type="info" slot="append" @click="attachmentBrowse"
                  >+</el-button
                >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24" :offset="0">
            <el-form-item label="备注:" prop="falutComment">
              <el-input
                v-model="sheetForm.falutComment"
                placeholder="请输入内容"
                clearable
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="主送:" prop="agentMan">
              <el-input
                v-model="sheetForm.agentMan"
                placeholder="添加人员"
                style="width: 100%"
                readonly
              >
                <el-button
                  type="info"
                  slot="append"
                  icon="el-icon-user"
                  @click="onOpenPeopleDialog('lordSentDetermine')"
                ></el-button>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="次送:" prop="acceptMan">
              <el-input
                v-model="sheetForm.acceptMan"
                placeholder="添加人员"
                style="width: 100%"
                readonly
              >
                <el-button
                  type="info"
                  slot="append"
                  icon="el-icon-user"
                  @click="onOpenPeopleDialog('secondDetermine')"
                ></el-button>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="抄送:" prop="copyMan">
              <el-input
                v-model="sheetForm.copyMan"
                placeholder="添加人员"
                style="width: 100%"
                readonly
              >
                <el-button
                  type="info"
                  slot="append"
                  icon="el-icon-user"
                  @click="onOpenPeopleDialog('ccDetermine')"
                ></el-button>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
    </el-form>
    <dia-orgs-user-tree
      :title="diaPeople.title"
      :visible.sync="diaPeople.visible"
      :showOrgsTree="diaPeople.showOrgsTree"
      :showContactUserTab="diaPeople.showContactUserTab"
      :professionalType="mainProfessionalType"
      @on-save="onSavePeople"
    />
    <el-dialog
      width="420px"
      title="附件选择"
      :visible.sync="attachmentDialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <file-upload
        @change="changeFileData"
        @cancel="closeAttachmentDialog"
      ></file-upload>
    </el-dialog>
    <el-dialog
      width="420px"
      title="影响业务列表附件选择"
      :visible.sync="incidenceDialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <file-upload
        @change="changeIncidenceFileData"
        @cancel="closeIncidenceDialog"
      ></file-upload>
    </el-dialog>
  </head-fixed-layout>
</template>

<script>
import { mapGetters } from "vuex";
import moment from "moment";
import HeadFixedLayout from "./../workOrder/components/HeadFixedLayout.vue";
import DictSelect from "./../workOrder/components/DictSelect.vue";
import FileUpload from "./../workOrder/components/FileUpload.vue";
import DiaOrgsUserTree from "./../workOrder/components/DiaOrgsUserTree.vue";

import {
  apiDict,
  apiGetOrgInfo,
  apiSaveContactUser,
  getCurrentTime,
} from "./../workOrder/api/CommonApi";
import {
  apiBuildSingle,
  apiProvinceBuild,
  apiBuildItCloudSingle,
} from "./../workOrder/api/WorkOrderDraftEdit";
export default {
  name: "WorkOrderDraftEdit",
  components: {
    HeadFixedLayout,
    DictSelect,
    FileUpload,
    DiaOrgsUserTree,
  },
  data() {
    var validHappenTime = (rule, value, callback) => {
      if (value === "" || value === null) {
        callback(new Error("请选择发生时间"));
      } else {
        let seconds = moment(
          this.sheetForm.createTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.sheetForm.happenTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        if (seconds < 0) {
          callback(new Error("“发生时间”不能晚于“建单时间”，请重新选择"));
        } else {
          callback();
        }
      }
    };
    return {
      sheetForm: {
        sheetNo: "001000",
        sender: "", //建单人的登录名
        senderName: "", //建单人真实姓名
        deptName: "", // 创建人部门
        sheetCreateTime: getCurrentTime(Date.now()), // 建单时间
        acceptTimeLimit: "30", // 受理时限
      },
      sheetFormRules: {
        // senderName: [{ required: true, message: "请输入建单人" }],
        // deptName: [{ required: true, message: "请输入建单部门" }],
        // sheetCreateTime: [{ required: true, message: "请选择建单时间" }],
        sheetTitle: [{ required: true, message: "工单主题不能为空" }],
        createType: [{ required: true, message: "请选择信息来源" }],
        alarmCreateTime: [{ validator: validHappenTime, required: true }],
        emergencyLevel: [{ required: true, message: "请选择紧急程度" }],
        acceptTimeLimit: [{ required: true, message: "请输入受理时限" }],
        professionalType: [{ required: true, message: "请选择所属专业" }],
        netType: [{ required: true, message: "请选择网络类型" }],
        faultPhenomenon: [{ required: true, message: "请输入故障现象" }],
        agentMan: [
          { required: true, message: "建单主送人不能为空", trigger: "blur" },
        ],
        acceptMan: [
          { required: true, message: "建单主送人不能为空", trigger: "blur" },
        ],
      },
      diaPeople: {
        visible: false,
        title: "",
        saveName: "",
        saveTitleMap: {
          lordSentDetermine: "建单人主送",
          secondDetermine: "建单人次送",
          ccDetermine: "建单人抄送",
          notifierDetermine: "通知人选择",
          recipientDetermine: "接收人选择",
        },
        showOrgsTree: true,
        showOrgsTreeMap: {
          recipientDetermine: false,
        },
        showContactUserTab: false,
        showContactUserTabMap: {
          recipientDetermine: true,
        },
      },
      attachmentDialogVisible: false,
      importForm: {
        //附件
        attachmentFileList: [],
        //影响业务列表附件
        incidenceFileList: [],
      },
      urgentOption: [],
      sheetCommitLoading: false,
      sheetSaveLoading: false,
      incidenceDialogVisible: false,
      orgInfoData: {}, //组织信息数据缓存
      route: null,
      userInfoData: {},
      //预估处理时限
      estimatedProTimeLimitOption: [
        { id: "8", name: "8小时" },
        { id: "72", name: "72小时" },
      ],
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
    ...mapGetters(["token"]),
    mainProfessionalType() {
      return this.$route.query.mainProfessionalType;
    },
  },
  watch: {},
  created() {
    this.route = this.$route;
    this.getUrgentOption();
  },
  mounted() {
    this.sheetForm.special = this.mainProfessionalType == "IT云" ? "23" : "3";
    this.userInfoData = JSON.parse(this.userInfo.attr2);
    this.sheetForm.createTime = moment(Date.now()).format(
      "YYYY-MM-DD HH:mm:ss"
    );
    this.sheetForm.happenTime = moment(Date.now()).format(
      "YYYY-MM-DD HH:mm:ss"
    );
    this.getOrgInfo();

    window["__process_response_from_getDevice"] = value => {
      let objStr = "";
      let result = JSON.parse(value);
      let objArray = JSON.parse(result?.system);
      objArray.forEach(obj => {
        objStr += obj.sysName + ",";
      });
      if (objStr) {
        objStr = objStr.substr(0, objStr.length - 1);
      }

      this.sheetForm.title += this.sheetForm.title ? "," + objStr : objStr;
    };
  },
  methods: {
    getOrgInfo() {
      apiGetOrgInfo()
        .then(res => {
          if (res.status == "0") {
            this.orgInfoData = res.data;
            this.sheetForm.senderName = res.data.trueName;
            this.sheetForm.deptName = res?.data?.orgInfo?.orgName ?? "";
            this.sheetForm.deptId = res?.data?.orgInfo?.orgId ?? "";
            this.sheetForm.sender = res?.data?.userName ?? "";
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    getUrgentOption() {
      let param = {
        dictTypeCode: "10001",
      };
      apiDict(param)
        .then(res => {
          if (res.code == 200) {
            this.urgentOption = res?.data ?? [];
            this.urgentOption.reverse();
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    onSheetSave() {
      let self = this;
      this.$confirm("是否保存到草稿？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$refs.sheetForm.validate(valid => {
            if (valid) {
              if (this.importForm.attachmentFileList.length > 0) {
                for (let item of this.importForm.attachmentFileList) {
                  formData.append("orderFiles", item.raw);
                }
              }
              if (this.importForm.incidenceFileList.length > 0) {
                for (let item of this.importForm.incidenceFileList) {
                  formData.append("affectFiles", item.raw);
                }
              }
              let formData = new FormData();

              this.sheetSaveLoading = true;
              let param = {
                sheetInfo: {
                  sheetNo: self.sheetForm.sheetNo, //工单编号
                  sender: self.sheetForm.sender, //建单人
                  senderDeptName: self.sheetForm.deptName, //建单部门
                  senderName: self.sheetForm.senderName, //建单人中文名
                  senderDept: self.sheetForm.deptId, //建单部门ID
                  sheetCreateTime: self.sheetForm.sheetCreateTime, //建单时间
                  sheetTitle: self.sheetForm.sheetTitle, //工单主题
                  createType: self.sheetForm.createType, //信息来源
                  alarmCreateTime: self.sheetForm.alarmCreateTime, //发生时间
                  faultRegion: self.sheetForm.faultRegion, // 发生地区
                  emergencyLevel: self.sheetForm.emergencyLevel, //紧急程度
                  acceptTimeLimit: self.sheetForm.acceptTimeLimit, //受理时限
                  processTimeLimit: self.sheetForm.processTimeLimit, // 处理时限

                  professionalType: self.sheetForm.professionalType, //所属专业
                  networkType: self.sheetForm.networkType, //网络类型
                  businessDown: self.sheetForm.businessDown, // 业务中断

                  longitude: self.sheetForm.longitude, //经度
                  latitude: self.sheetForm.latitude, //维度
                  faultPhenomenon: self.sheetForm.faultPhenomenon, //故障现象
                  propertyUser: self.sheetForm.propertyUser, // 物业联系人
                  propertyPhone: self.sheetForm.propertyPhone, // 物业联系电话
                  falutComment: self.sheetForm.falutComment, //备注

                  agentDeptCode: self.sheetForm.agentManOrgId, //主送是组织 传入组织ID
                  agentManId: self.sheetForm.agentManUserId, //主送是人员 传入人员ID
                  agentMan: self.sheetForm.bagentManUserName, //主送人员 中文名称
                  agentDeptName: self.sheetForm.agentManOrgName, //主送部门 中文名称

                  acceptManCode: self.sheetForm.acceptManOrgId, //主送是组织 传入组织ID
                  acceptManId: self.sheetForm.acceptManUserId, //主送是人员 传入人员ID
                  acceptMan: self.sheetForm.acceptManUserName, //主送人员 中文名称
                  acceptDeptName: self.sheetForm.acceptManOrgName, //主送部门 中文名称

                  copyDeptCode: self.sheetForm.copyManOrgId, //抄送是组织 传入组织ID
                  copyManId: self.sheetForm.copyManUserId, //抄送是用户 传入用户ID
                  copyMan: self.sheetForm.copyManUserName,
                  copyDeptName: self.sheetForm.copyManOrgName,

                  sheetStatus: 1, //1是草稿 2是提交到待办
                },
              };
              formData.append("jsonParam", JSON.stringify(param));
              //保存到草稿
              apiProvinceBuild(formData)
                .then(res => {
                  if (res.status == "0") {
                    this.$message.success("保存成功");
                    this.closeAndTurnAroundMyDraft();
                  } else {
                    this.$message.error("保存失败");
                  }
                  this.sheetSaveLoading = false;
                })
                .catch(error => {
                  console.log(error);
                  this.$message.error("保存失败");
                  this.sheetSaveLoading = false;
                });
            } else {
              return false;
            }
          });
        })
        .catch(() => {});
    },
    onSheetSubmit() {
      this.$confirm("是否提交工单？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        closeOnClickModal: false,
        type: "warning",
      }).then(() => {
        this.$refs.sheetForm.validate(valid => {
          if (valid) {
            this.saveContactUser();
            this.sheetCommitLoading = true;
            let formData = new FormData();
            let self = this;
            let param = {
              sheetInfo: {
                sheetNo: self.sheetForm.sheetNo, //工单编号
                sender: self.sheetForm.sender, //建单人
                senderDeptName: self.sheetForm.deptName, //建单部门
                senderName: self.sheetForm.senderName, //建单人中文名
                senderDept: self.sheetForm.deptId, //建单部门ID
                sheetCreateTime: self.sheetForm.sheetCreateTime, //建单时间
                sheetTitle: self.sheetForm.sheetTitle, //工单主题
                createType: self.sheetForm.createType, //信息来源
                alarmCreateTime: self.sheetForm.alarmCreateTime, //发生时间
                faultRegion: self.sheetForm.faultRegion, // 发生地区
                emergencyLevel: self.sheetForm.emergencyLevel, //紧急程度
                acceptTimeLimit: self.sheetForm.acceptTimeLimit, //受理时限
                processTimeLimit: self.sheetForm.processTimeLimit, // 处理时限

                professionalType: self.sheetForm.professionalType, //所属专业
                networkType: self.sheetForm.networkType, //网络类型
                businessDown: self.sheetForm.businessDown, // 业务中断

                longitude: self.sheetForm.longitude, //经度
                latitude: self.sheetForm.latitude, //维度
                faultPhenomenon: self.sheetForm.faultPhenomenon, //故障现象
                propertyUser: self.sheetForm.propertyUser, // 物业联系人
                propertyPhone: self.sheetForm.propertyPhone, // 物业联系电话
                falutComment: self.sheetForm.falutComment, //备注

                agentDeptCode: self.sheetForm.agentManOrgId, //主送是组织 传入组织ID
                agentManId: self.sheetForm.agentManUserId, //主送是人员 传入人员ID
                agentMan: self.sheetForm.agentManUserName, //主送人员 中文名称
                agentDeptName: self.sheetForm.agentManOrgName, //主送部门 中文名称

                acceptManCode: self.sheetForm.acceptManOrgId, //主送是组织 传入组织ID
                acceptManId: self.sheetForm.acceptManUserId, //主送是人员 传入人员ID
                acceptMan: self.sheetForm.acceptManUserName, //主送人员 中文名称
                acceptDeptName: self.sheetForm.acceptManOrgName, //主送部门 中文名称

                copyDeptCode: self.sheetForm.copyManOrgId, //抄送是组织 传入组织ID
                copyManId: self.sheetForm.copyManUserId, //抄送是用户 传入用户ID
                copyMan: self.sheetForm.copyManUserName,
                copyDeptName: self.sheetForm.copyManOrgName,

                sheetStatus: 2, //1是草稿 2是提交到待办
              },
            };
            formData.append("jsonParam", JSON.stringify(param));
            apiProvinceBuild(formData)
              .then(res => {
                if (res.status == "0") {
                  this.$message.success("工单提交成功");
                  this.closeAndTurnAroundTo();
                } else {
                  this.$message.error("工单提交失败");
                }
                this.sheetCommitLoading = false;
              })
              .catch(error => {
                console.log(error);
                this.$message.error("工单提交失败");
                this.sheetCommitLoading = false;
              });
          } else {
            return false;
          }
        });
      });
    },
    saveContactUser() {
      if (this.sheetForm.recipientUserId) {
        apiSaveContactUser({
          operateUser: this.userInfoData.userName,
          userNames: this.sheetForm.recipientUserId,
        })
          .then(res => {
            if (res.status == "0") {
              console.log(res);
            }
          })
          .catch(error => {
            console.log(error);
          });
      }
    },
    onOpenPeopleDialog(diaSaveName) {
      this.diaPeople.title = this.diaPeople.saveTitleMap[diaSaveName];
      this.diaPeople.saveName = diaSaveName;
      this.diaPeople.showOrgsTree =
        this.diaPeople.showOrgsTreeMap[diaSaveName] ?? true;
      this.diaPeople.showContactUserTab =
        this.diaPeople.showContactUserTabMap[diaSaveName] ?? false;
      this.diaPeople.visible = true;
    },
    onSavePeople(val) {
      this[this.diaPeople.saveName](val);
    },
    //建单人主送确定
    lordSentDetermine({ usersChecked, orgsChecked, selectionUser }) {
      if (selectionUser && selectionUser.length > 0) {
        let usersCheckedName = selectionUser.map(item => {
          return item.trueName;
        });
        this.sheetForm.agentManUserName = usersCheckedName.join(",");
        let usersCheckedId = selectionUser.map(item => {
          return item.userName;
        });
        this.sheetForm.agentManUserId = usersCheckedId.join(",");
        const userDetailName = selectionUser.map(item => {
          return (
            item.trueName +
            "-" +
            item.orgEntity.orgName +
            "-" +
            item.mobilePhone
          );
        });
        this.sheetForm.agentManDetail = userDetailName.join(",");
      } else {
        let usersCheckedName = usersChecked.map(item => {
          return item.name;
        });
        this.sheetForm.agentManUserName = usersCheckedName.join(",");
        let usersCheckedId = usersChecked.map(item => {
          return item.id;
        });
        this.sheetForm.agentManUserId = usersCheckedId.join(",");
        const userDetailName = usersChecked.map(item => {
          return item.name + "-" + item.orgName + "-" + item.mobilePhone;
        });
        this.sheetForm.agentManDetail = userDetailName.join(",");
      }

      let orgsCheckedName = orgsChecked.map(item => {
        return item.name;
      });
      this.sheetForm.agentManOrgName = orgsCheckedName.join(",");

      this.sheetForm.agentMan = this.stitchingAlgorithm(
        this.sheetForm.agentManOrgName,
        this.sheetForm.agentManDetail
      );
      console.log(this.sheetForm.agentMan, "主");
      let orgsCheckedId = orgsChecked.map(item => {
        return item.id;
      });
      this.sheetForm.agentManOrgId = orgsCheckedId.join(",");
    },
    //建单人主送确定
    secondDetermine({ usersChecked, orgsChecked, selectionUser }) {
      if (selectionUser && selectionUser.length > 0) {
        let usersCheckedName = selectionUser.map(item => {
          return item.trueName;
        });
        this.sheetForm.acceptManUserName = usersCheckedName.join(",");
        let usersCheckedId = selectionUser.map(item => {
          return item.userName;
        });
        this.sheetForm.acceptManUserId = usersCheckedId.join(",");
        const userDetailName = selectionUser.map(item => {
          return (
            item.trueName +
            "-" +
            item.orgEntity.orgName +
            "-" +
            item.mobilePhone
          );
        });
        this.sheetForm.acceptManDetail = userDetailName.join(",");
      } else {
        let usersCheckedName = usersChecked.map(item => {
          return item.name;
        });
        this.sheetForm.acceptManUserName = usersCheckedName.join(",");
        let usersCheckedId = usersChecked.map(item => {
          return item.id;
        });
        this.sheetForm.acceptManUserId = usersCheckedId.join(",");
        const userDetailName = usersChecked.map(item => {
          return item.name + "-" + item.orgName + "-" + item.mobilePhone;
        });
        this.sheetForm.acceptManDetail = userDetailName.join(",");
      }

      let orgsCheckedName = orgsChecked.map(item => {
        return item.name;
      });
      this.sheetForm.acceptManOrgName = orgsCheckedName.join(",");

      this.sheetForm.acceptMan = this.stitchingAlgorithm(
        this.sheetForm.acceptManOrgName,
        this.sheetForm.acceptManDetail
      );

      let orgsCheckedId = orgsChecked.map(item => {
        return item.id;
      });
      this.sheetForm.acceptManOrgId = orgsCheckedId.join(",");
    },
    //抄送人确定
    ccDetermine({ usersChecked, orgsChecked, selectionUser }) {
      if (selectionUser && selectionUser.length > 0) {
        let usersCheckedName = selectionUser.map(item => {
          return item.trueName;
        });
        this.sheetForm.copyManUserName = usersCheckedName.join(",");
        let usersCheckedId = selectionUser.map(item => {
          return item.userName;
        });
        this.sheetForm.copyManUserId = usersCheckedId.join(",");
        const userDetailName = selectionUser.map(item => {
          return (
            item.trueName +
            "-" +
            item.orgEntity.orgName +
            "-" +
            item.mobilePhone
          );
        });
        this.sheetForm.copyManDetail = userDetailName.join(",");
      } else {
        let usersCheckedName = usersChecked.map(item => {
          return item.name;
        });
        this.sheetForm.copyManUserName = usersCheckedName.join(",");

        let usersCheckedId = usersChecked.map(item => {
          return item.id;
        });
        this.sheetForm.copyManUserId = usersCheckedId.join(",");
        const userDetailName = usersChecked.map(item => {
          return item.name + "-" + item.orgName + "-" + item.mobilePhone;
        });
        this.sheetForm.copyManDetail = userDetailName.join(",");
      }

      let orgsCheckedName = orgsChecked.map(item => {
        return item.name;
      });
      this.sheetForm.copyManOrgName = orgsCheckedName.join(",");
      this.sheetForm.copyMan = this.stitchingAlgorithm(
        this.sheetForm.copyManOrgName,
        this.sheetForm.copyManDetail
      );

      let orgsCheckedId = orgsChecked.map(item => {
        return item.id;
      });
      this.sheetForm.copyManOrgId = orgsCheckedId.join(",");
    },
    //通知人确定
    notifierDetermine({ usersChecked, orgsChecked, selectionUser }) {
      let userDetailName = "";
      if (selectionUser && selectionUser.length > 0) {
        let usersCheckedName = selectionUser.map(item => {
          return item.trueName;
        });
        this.sheetForm.notifierUserName = usersCheckedName.join(",");
        let usersCheckedId = selectionUser.map(item => {
          return item.userName;
        });
        this.sheetForm.notifierUserId = usersCheckedId.join(",");
        userDetailName = selectionUser
          .map(item => {
            return (
              item.trueName +
              "-" +
              item.orgEntity.orgName +
              "-" +
              item.mobilePhone
            );
          })
          .join(",");
      } else {
        let usersCheckedName = usersChecked.map(item => {
          return item.name;
        });
        this.sheetForm.notifierUserName = usersCheckedName.join(",");

        let usersCheckedId = usersChecked.map(item => {
          return item.id;
        });
        this.sheetForm.notifierUserId = usersCheckedId.join(",");
        userDetailName = usersChecked
          .map(item => {
            return item.name + "-" + item.orgName + "-" + item.mobilePhone;
          })
          .join(",");
      }

      let orgsCheckedName = orgsChecked.map(item => {
        return item.name;
      });
      this.sheetForm.notifierOrgName = orgsCheckedName.join(",");
      this.sheetForm.notifier = this.stitchingAlgorithm(
        this.sheetForm.notifierOrgName,
        userDetailName
      );
      let orgsCheckedId = orgsChecked.map(item => {
        return item.id;
      });
      this.sheetForm.notifierOrgId = orgsCheckedId.join(",");
    },
    //接收人确定
    recipientDetermine({
      usersChecked,
      orgsChecked,
      selectionUser,
      contactSelectionUser,
    }) {
      let contactUserName = "";
      let treeUserName = "";
      let contactUserId = "";
      let treeUserId = "";
      let contactUserDetailName = "";
      let userDetailName = "";
      if (contactSelectionUser && contactSelectionUser.length > 0) {
        let usersCheckedName = contactSelectionUser.map(item => {
          return item.trueName;
        });
        contactUserName = usersCheckedName.join(",");
        contactUserId = contactSelectionUser
          .map(item => {
            return item.userName;
          })
          .join(",");
        contactUserDetailName = contactSelectionUser
          .map(item => {
            return (
              item.trueName +
              "-" +
              item.orgEntity.orgName +
              "-" +
              item.mobilePhone
            );
          })
          .join(",");
      }
      if (selectionUser && selectionUser.length > 0) {
        let usersCheckedName = selectionUser.map(item => {
          return item.trueName;
        });
        treeUserName = usersCheckedName.join(",");
        let usersCheckedId = selectionUser.map(item => {
          return item.userName;
        });
        treeUserId = usersCheckedId.join(",");
        userDetailName = selectionUser
          .map(item => {
            return (
              item.trueName +
              "-" +
              item.orgEntity.orgName +
              "-" +
              item.mobilePhone
            );
          })
          .join(",");
      } else if (usersChecked && usersChecked.length > 0) {
        let usersCheckedName = usersChecked.map(item => {
          return item.name;
        });
        treeUserName = usersCheckedName.join(",");
        let usersCheckedId = usersChecked.map(item => {
          return item.id;
        });
        treeUserId = usersCheckedId.join(",");
        userDetailName = usersChecked
          .map(item => {
            return item.name + "-" + item.orgName + "-" + item.mobilePhone;
          })
          .join(",");
      }
      this.sheetForm.recipientUserName = this.stitchingAlgorithm(
        treeUserName,
        contactUserName
      );
      this.sheetForm.recipientDetailUserName = this.stitchingAlgorithm(
        contactUserDetailName,
        userDetailName
      );
      this.sheetForm.recipientUserId = this.stitchingAlgorithm(
        treeUserId,
        contactUserId
      );
    },
    //光缆名称选择
    opticalCableSelect() {
      let jsonStr =
        '{"userId":"302785","username":"yangxm97","callSysId":"0001","callSysName":"电子运维"}';
      this.$nextTick(() => {
        document.querySelector("#fiberOpticCable").value = jsonStr;
        document.querySelector("#sub__fiberOpticCable").submit();
      });
    },
    //工单主题
    getInterfaceZxData() {
      let jsonStr =
        '{"userId":"302785","username":"yangxm97","callSysId":"0001","callSysName":"电子运维"}';
      this.$nextTick(() => {
        document.querySelector("#requestJson").value = jsonStr;
        document.querySelector("#deploy_simulation_page").submit();
      });
    },
    //附件选择
    attachmentBrowse() {
      this.attachmentDialogVisible = true;
    },
    changeFileData(data) {
      this.sheetForm.attachmentFile = data.fileName;
      this.importForm.attachmentFileList = data.attachmentFileList;
      this.attachmentDialogVisible = false;
    },
    closeAttachmentDialog() {
      this.attachmentDialogVisible = false;
    },
    //影响业务列表选择
    incidenceBrowse() {
      this.incidenceDialogVisible = true;
    },
    changeIncidenceFileData(data) {
      this.sheetForm.incidenceFile = data.fileName;
      this.importForm.incidenceFileList = data.attachmentFileList;
      this.incidenceDialogVisible = false;
    },
    closeIncidenceDialog() {
      this.incidenceDialogVisible = false;
    },
    stitchingAlgorithm(orgName, userName) {
      if (orgName.length !== 0 && userName.length !== 0) {
        return orgName + "," + userName;
      } else {
        if (orgName.length !== 0) {
          return orgName;
        } else if (userName.length !== 0) {
          return userName;
        } else {
          return "";
        }
      }
    },
    filterLordSentOrgNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    closeAndTurnAroundMyDraft() {
      let self = this;
      self.$store.dispatch("tagsView/delView", self.route).then(() => {
        self.$destroy();
      });
      self.$router.push({
        name: "backbone_myDraft",
      });
    },
    closeAndTurnAroundTo() {
      let self = this;
      self.$store.dispatch("tagsView/delView", self.route).then(() => {
        self.$destroy();
      });
      self.$router.push({
        name: "backbone_toDoRepairOrder",
        query: {
          globalUniqueID: sessionStorage.getItem("globalUniqueID"),
          token: this.token,
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.draft-eidt-page {
  .head-title {
    font-size: 24px;
    line-height: 28px;
  }
  .head-handle-wrap {
    text-align: right;
  }
  .card-title {
    font-size: 15px;
    letter-spacing: 0;
    padding-left: 10px;
  }

  ::v-deep .el-tree {
    padding-top: 15px;
    padding-left: 10px;
    // 不可全选样式
    .el-tree-node {
      .is-leaf + .el-checkbox .el-checkbox__inner {
        display: inline-block;
      }
      .el-checkbox .el-checkbox__inner {
        display: none;
      }
    }
  }

  .select_style {
    background: #b50b14;
    border-radius: 2px;
    border-color: #b50b14;
  }
}
</style>
