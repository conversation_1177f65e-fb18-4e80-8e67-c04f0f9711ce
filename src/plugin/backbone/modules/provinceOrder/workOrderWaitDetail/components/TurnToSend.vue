<template>
  <div>
    <el-form
      ref="transferForm"
      :model="transferForm"
      :rules="transferFormRules"
      label-width="90px"
    >
      <el-form-item label="转派说明:">
        <el-input
          :rows="2"
          type="textarea"
          placeholder="请填写转派说明"
          v-model="transferForm.explain"
          style="width: 100%"
          show-word-limit
          maxlength="255"
          @keyup.native="descTip(255,'explain','showTip')"
        ></el-input>
        <div class="el-form-item__error"  v-if="showTip">已超过填写字数上限</div>
      </el-form-item>
      <el-form-item label="转派对象:" prop="transferObject" required>
        <el-input
          v-model="transferForm.transferObject"
          placeholder="添加人员"
          style="width: 310px"
          readonly
        >
          <el-button
            type="info"
            slot="append"
            icon="el-icon-user"
            @click="onOpenPeopleDialog('transferObjectDetermine')"
          ></el-button>
        </el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: right">
      <el-button
        type="primary"
        @click="transferSubmit('transferForm')"
        v-loading.fullscreen.lock="transferSubmitLoading"
        >提 交</el-button
      >
      <el-button @click="transferCancel">取 消</el-button>
    </div>

    <dia-tissue-tree
      v-if="isDiaOrgsUserTree"
      :title="diaPeople.title"
      :visible.sync="diaPeople.visible"
      :showOrgsTree="false"
      :multiple-select-enable="diaPeople.multipleSelectEnable"
      :single-select-tip="diaPeople.singleSelectTip"
      :professionalType="common.professionalTypeName"
      @on-save="onSavePeople"
      :appendToBody="true"
      :userAttribution="userAttribution"
      :show-contact-user-tab="true"
      :show-contact-org-tab="false"
      :reassignmentType="reassignmentType"
    />
  </div>
</template>
<script>
import {
  apiRedeployUserTree,
  apiRedeployOrgTree,
} from "../../../workOrder/api/WorkOrderTodo.js";
import { apiActionPublic, getCurrentTime } from "../api/CommonApi";
// import DiaOrgsUserTreeOther from "./DiaOrgsUserTreeOther.vue";
import DiaTissueTree from "@/plugin/backbone/components/common/DiaTissueTree";
export default {
  name: "TurnToSend",
  props: {
    common: Object,
    type: String,
    actionName: String,
  },
  components: {
    DiaTissueTree,
  },
  data() {
    return {
      showTip:false,
      showTime:5000,
      reassignmentType:'province',
      isDiaOrgsUserTree:false,
      userAttribution:"poTurnToSendUser",
      transferForm: {
        explain: "",
        transferObject: "",
        // cc: "",
        transferObjectUserId: "",
        transferObjectOrgId: "",
        // ccUserId: "",
        // ccOrgId: "",
        agentMan: null,
        copyMan: null,
        agentDeptName: null,
        copyDeptName: null,
        agentManDetail: null,
        copyManDetail: null,
      },
      transferFormRules: {
        transferObject: [{ required: true, message: "转派对象不能为空" }],
      },
      //抄送
      // ccActiveName: "orgTree",
      // ccDialogVisible: false,
      // ccProRedepolyOrgsData: [],
      // ccProRedeployUserData: [],
      //主送
      activeName: "orgTree",
      proRedepolyOrgsData: [],
      proRedeployUserData: [],
      transferObjectDialogVisible: false,
      defaultProps: {
        children: "children",
        label: "name",
        isLeaf: "isLeaf",
      },
      proOrgTreeLoading: false,
      proUserTreeLoading: false,
      woId: null,
      workItemId: null,
      transferSubmitLoading: false,
      diaPeople: {
        visible: false,
        title: "",
        saveName: "",
        saveTitleMap: {
          transferObjectDetermine: "选择转派人",
          // ccDetermine: "选择抄送人",
        },
        multipleSelectEnable:true,
        singleSelectTip:'',
        showOrgsTree: true,
      },
    };
  },
  mounted() {
    console.log(this.common);
  },
  methods: {
    descTip(count,name,showName){
      if (this.transferForm[name].length>=count){
        this[showName] = true;
        setTimeout(() => {
          this[showName] = false;
        }, this.showTime);
      }
      else{
        this[showName] = false;
      }
    },

    //树懒加载-人员
    loadUserNode(node, resolve) {
      let param = {
        level: node.level + 1,
        nodeId: node?.data?.id ?? "",
      };
      apiRedeployUserTree(param)
        .then(res => {
          if (res.status == 0) {
            let treeData = res?.data?.data ?? [];
            return resolve(treeData);
          }
        })
        .catch(e => {
          console.log("报错了，", e);
          return resolve([]);
        });
    },
    loadOrgNode(node, resolve) {
      let param = {
        level: node.level + 1,
        nodeId: node?.data?.id ?? "",
      };
      apiRedeployOrgTree(param)
        .then(res => {
          if (res.status == 0) {
            let treeData = res?.data?.data ?? [];
            return resolve(treeData);
          }
        })
        .catch(e => {
          console.log("报错了，", e);
          return resolve([]);
        });
    },
    selectTransferObject() {
      this.transferObjectDialogVisible = true;
    },
    // selectCc() {
    //   this.ccDialogVisible = true;
    // },
    //转派提交
    transferSubmit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.transferSubmitLoading = true;
          let param = {
            woId: this.common.woId,
            processInstId: this.common.processInstId,
            workItemId: this.$route.query.workItemId,
            actionName: this.actionName,
            turnUser: this.transferForm.transferObjectUserId,
            turnReason: this.transferForm.explain,
            createTime: getCurrentTime(Date.now()),
          };
          apiActionPublic(param)
            .then(res => {
              if (res.status == "0") {
                this.$message.success("转派成功");
                this.$emit("closeDialogTurnToSend", "1");
              } else {
                this.$message.error("转派失败");
              }
              this.transferSubmitLoading = false;
            })
            .catch(error => {
              console.log(error);
              this.$message.error("转派失败");
              this.transferSubmitLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    //转派取消
    transferCancel() {
      this.$emit("closeDialogTurnToSend", "0");
    },
    transferObjectHandleClose() {
      this.transferObjectDialogVisible = false;
      this.resetTransferObjectChecked();
    },
    resetTransferObjectChecked() {
      this.$refs.transferObjectOrgTree.setCheckedKeys([]);
      this.$refs.transferObjectUserTree.setCheckedKeys([]);
    },
    onOpenPeopleDialog(diaSaveName) {
      this.isDiaOrgsUserTree = false;
      this.diaPeople.title = this.diaPeople.saveTitleMap[diaSaveName];
      this.diaPeople.saveName = diaSaveName;
      this.diaPeople.showOrgsTree = true;
      this.diaPeople.multipleSelectEnable = false;
      this.diaPeople.singleSelectTip = '请选择单个人员转派';
      this.diaPeople.visible = true;
      this.$nextTick(() => {
        this.isDiaOrgsUserTree = true;
      });
    },
    onSavePeople(val) {
      this[this.diaPeople.saveName](val);
    },
    transferObjectDetermine({ usersChecked, orgsChecked, selectionUser, contactSelectionUser, contactSelectionOrg, })
    {
      if (contactSelectionUser && contactSelectionUser.length > 0) {
        let usersCheckedId = contactSelectionUser.map(item => {
          return item.userName;
        });
        this.transferForm.transferObjectUserId = usersCheckedId.join(",");
        this.transferForm.transferObject = contactSelectionUser
          .map(item => {
            return (
              item.trueName +
              "-" +
              item.orgEntity.orgName +
              "-" +
              item.mobilePhone
            );
          })
          .join(",");
      }
      if (selectionUser && selectionUser.length > 0) {
        // let usersCheckedName = selectionUser.map(item => {
        //   return item.trueName;
        // });
        // this.transferForm.agentMan = usersCheckedName.join(",");
        let usersCheckedId = selectionUser.map(item => {
          return item.userName;
        });
        this.transferForm.transferObjectUserId = usersCheckedId.join(",");
        this.transferForm.transferObject = selectionUser
          .map(item => {
            return (
              item.trueName +
              "-" +
              item.orgEntity.orgName +
              "-" +
              item.mobilePhone
            );
          })
          .join(",");
      } else if (usersChecked && usersChecked.length > 0)  {
        // let usersCheckedName = usersChecked.map(item => {
        //   return item.name;
        // });
        // this.transferForm.agentMan = usersCheckedName.join(",");
        let usersCheckedId = usersChecked.map(item => {
          return item.id;
        });
        this.transferForm.transferObjectUserId = usersCheckedId.join(",");
        this.transferForm.transferObject = usersChecked
          .map(item => {
            return item.name + "-" + item.orgName + "-" + item.mobilePhone;
          })
          .join(",");
      }

      // let orgsCheckedName = orgsChecked.map(item => {
      //   return item.name;
      // });
      // this.transferForm.agentDeptName = orgsCheckedName.join(",");
      // this.transferForm.transferObject = this.stitchingAlgorithmNew(
      //   this.transferForm.agentDeptName,
      //   this.transferForm.agentManDetail
      // );
      // let orgsCheckedId = orgsChecked.map(item => {
      //   return item.id;
      // });
      // this.transferForm.transferObjectOrgId = orgsCheckedId.join(",");
    },
    stitchingAlgorithmNew(orgName, userName) {
      if (orgName.length !== 0 && userName.length !== 0) {
        return orgName + "," + userName;
      } else {
        if (orgName.length !== 0) {
          return orgName;
        } else if (userName.length !== 0) {
          return userName;
        } else {
          return "";
        }
      }
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-tree {
  padding-top: 15px;
  padding-left: 10px;
  // 不可全选样式
  .el-tree-node {
    .is-leaf + .el-checkbox .el-checkbox__inner {
      display: inline-block;
    }
    .el-checkbox .el-checkbox__inner {
      display: none;
    }
  }
}
</style>
