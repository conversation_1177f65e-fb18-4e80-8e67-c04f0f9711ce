<template>
  <div class="stage-feedback">
    <el-form ref="stageBackForm" :model="stageBackForm" label-width="90px">
      <el-form-item
        label="反馈类型:"
        prop="stageType"
        :rules="{
          required: true,
          message: '请选择反馈类型',
        }"
      >
        <el-select
          v-model="stageBackForm.stageType"
          placeholder="请选择"
          style="width: 100%"
        >
          <el-option
            v-for="(item, index) in stageTypeList"
            :key="index"
            :label="item.value"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="反馈内容:" prop="processSuggestion">
        <el-input
          type="textarea"
          :rows="3"
          placeholder="请输入内容"
          v-model="stageBackForm.processSuggestion"
          style="width: 100%"
          clearable
          show-word-limit
          maxlength="900"
          @keyup.native="descTip(900,'processSuggestion','showTip')"
        >
        </el-input>
        <div class="el-form-item__error"  v-if="showTip">已超过填写字数上限</div>
      </el-form-item>
      <el-form-item label="附件:" prop="attachmentFile">
        <el-input
          v-model="stageBackForm.attachmentFile"
          placeholder="添加附件"
          clearable
          readonly
          style="width: 100%"
        >
          <el-button type="info" slot="append" @click="attachmentBrowse"
            >+</el-button
          >
        </el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: right">
      <el-button
        type="primary"
        @click="handleSubmit('stageBackForm')"
        v-loading.fullscreen.lock="stageBackFullscreenLoading"
        >提交</el-button
      >
      <el-button @click="onResetStageBackForm">重 置</el-button>
    </div>
    <el-dialog
      width="420px"
      title="附件选择"
      append-to-body
      :visible.sync="attachmentDialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <file-upload
        v-if="attachmentDialogVisible"
        @change="changeFileData"
        @cancel="closeAttachmentDialog"
      ></file-upload>
    </el-dialog>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import {
  apiActionPublic,
  getCurrentTime,
  apiGroupCode,
  apiFileUpload,
} from "../api/CommonApi";
import FileUpload from "./FileUpload.vue";
export default {
  props: {
    common: Object,
    actionName: String,
  },
  name: "StageFeedback",
  components: { FileUpload },
  data() {
    return {
      showTip:false,
      showTime:5000,
      stageBackForm: {
        woId: "",
        stageType: "",
        processInstId: "",
        processDefId: "",
        workItemId: "",
        processNode: "",
        processSuggestion: "",
        attachmentFile: "", //附件
      },
      stageTypeList: [
        {
          value: "到达现场",
        },
        {
          value: "正在处理",
        },
        {
          value: "进度反馈",
        },
        {
          value: "业务恢复",
        },
        {
          value: "问题定位",
        },
      ],
      importForm: {
        //附件
        attachmentFileList: [],
      },
      stageBackFullscreenLoading: false,
      attachmentDialogVisible: false,
      groupKey: "",
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  mounted() {
    this.stageBackForm.woId = this.common.woId;
    this.stageBackForm.processInstId = this.common.processInstId;
    this.stageBackForm.processDefId = this.common.processDefId;
    this.stageBackForm.workItemId = this.common.workItemId;
    this.stageBackForm.processNode = this.common.processNode;
  },
  methods: {

    descTip(count,name,showName){
      if (this.stageBackForm[name].length>=count){
        this[showName] = true;
        setTimeout(() => {
          this[showName] = false;
        }, this.showTime);
      }
      else{
        this[showName] = false;
      }
    },
    handleSubmit(formName) {
      this.$refs[formName].validate(async valid => {
        if (valid) {
          this.stageBackFullscreenLoading = true;
          let params = JSON.parse(JSON.stringify(this.stageBackForm));
          params.createUserEn = this.userInfo.userName;
          params.createTime = getCurrentTime(new Date());
          params.actionName = this.actionName;
          params.stageContent = this.stageBackForm.processSuggestion;

          // 如果有附件，先上传附件
          let isUpload = false;
          let formData = new FormData();
          if (
            this.stageBackForm.attachmentFile != "" &&
            this.importForm.attachmentFileList.length > 0
          ) {
            isUpload = true;
            for (let item of this.importForm.attachmentFileList) {
              formData.append("files", item.raw);
            }
          } else {
            isUpload = false;
            formData.append("files", "");
          }
          // 获取上传附件id
          if (isUpload) {
            apiGroupCode()
              .then(res => {
                if (res.status == 0) {
                  let processId = res.data?.linkId;

                  let uploadUrl = `/province/attach/upload?groupKey=${this.common.woId}&processId=${processId}&processNode=${this.common.processNode}&sheetCreateTime=${this.common.sheetCreateTime}`;
                  apiFileUpload(uploadUrl, formData)
                    .then(res => {
                      if (res.code == 200) {
                        params.linkId = processId;
                        this.submit(params);
                        this.importForm.attachmentFileList = [];
                      } else {
                        this.$message.error("上传失败，请重新上传");
                        this.importForm.attachmentFileList = [];
                        return false;
                      }
                    })
                    .catch(error => {
                      console.log(error);
                    });
                }
              })
              .catch(error => {
                console.log(error);
              });
          } else {
            this.submit(params);
          }
        } else {
          return false;
        }
      });
    },
    submit(params) {
      apiActionPublic(params)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("阶段反馈完成");
            this.onResetStageBackForm();
            this.$emit("stageBackDialogClose");
          } else {
            this.$message.error("阶段反馈失败");
          }
          this.stageBackFullscreenLoading = false;
          this.stageBackForm.attachmentFile = "";
        })
        .catch(error => {
          console.log(error);
          this.$message.error("阶段反馈失败");
          this.stageBackFullscreenLoading = false;
        });
    },
    onResetStageBackForm() {
      this.stageBackForm = {
        ...this.$options.data,
        woId: this.common.woId,
        workItemId: this.common.workItemId,
        processInstId: this.common.processInstId,
        processDefId: this.common.processDefId,
        processNode: this.common.processNode,
      };
    },
    //附件选择
    attachmentBrowse() {
      this.attachmentDialogVisible = true;
    },
    changeFileData(data) {
      this.stageBackForm.attachmentFile = data.fileName;
      this.importForm.attachmentFileList = data.attachmentFileList;
      this.attachmentDialogVisible = false;
    },
    closeAttachmentDialog() {
      this.attachmentDialogVisible = false;
    },
  },
};
</script>
