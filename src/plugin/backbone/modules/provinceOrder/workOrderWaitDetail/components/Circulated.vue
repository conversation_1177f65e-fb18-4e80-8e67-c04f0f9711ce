<template>
  <div>
    <el-form ref="circulatedForm" :model="circulatedForm" label-width="90px">
      <el-form-item
        label="传阅对象:"
        prop="circulatedObject"
        :rules="{
          required: true,
          message: '请选择传阅对象',
        }"
      >
        <!-- <el-button
          type="primary"
          icon="el-icon-user"
          @click="selectCirculatedObject"
        ></el-button> -->
        <el-input
          v-model="circulatedForm.circulatedObject"
          placeholder="添加人员"
          style="width: 100%"
          readonly
        >
          <template v-slot:append>
            <el-button
              type="info"
              icon="el-icon-user"
              @click="onOpenPeopleDialog('circulatedDetermine')"
            ></el-button>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="传阅说明">
        <el-input type="textarea" v-model="circulatedForm.explain"></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: right">
      <el-button type="primary" @click="circulatedSubmit">提 交</el-button>
      <el-button @click="circulatedCancel">取 消</el-button>
    </div>
    <el-dialog
      title="选择传阅人"
      :visible.sync="circulatedObjectDialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="600px"
      :before-close="circulatedObjectHandleClose"
      top="5vh"
      append-to-body
    >
      <div style="height: 460px">
        <el-tabs v-model="activeName" type="border-card">
          <el-tab-pane label="组织树" name="orgTree"
            ><el-tree
              ref="circulatedObjectOrgTree"
              :data="proCirculatedOrgsData"
              show-checkbox
              node-key="id"
              :props="defaultProps"
              style="height: 405px; overflow-y: auto"
              :load="loadOrgNode"
              lazy
            >
            </el-tree
          ></el-tab-pane>
          <el-tab-pane label="人员树" name="peopleTree">
            <el-tree
              ref="circulatedObjectUserTree"
              :data="proCirculatedUserData"
              show-checkbox
              node-key="id"
              :props="defaultProps"
              style="height: 405px; overflow-y: auto"
              :load="loadUserNode"
              lazy
            >
            </el-tree
          ></el-tab-pane>
        </el-tabs>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="circulatedDetermine">确 定</el-button>
        <el-button @click="circulatedObjectHandleClose">取 消</el-button>
      </span>
    </el-dialog>
    <dia-orgs-user-tree-other
      :title="diaPeople.title"
      :visible.sync="diaPeople.visible"
      :showOrgsTree="diaPeople.showOrgsTree"
      @on-save="onSavePeople"
      :appendToBody="true"
    />
  </div>
</template>
<script>
import {
  apiCirculated,
  apiCirculatedUserTree,
  apiCirculatedOrgTree,
} from "../api/CommonApi";
import DiaOrgsUserTreeOther from "../../../workOrder/components/DiaOrgsUserTreeOther.vue";
export default {
  name: "Circulated",
  props: {
    common: Object,
  },
  components: {
    DiaOrgsUserTreeOther,
  },
  data() {
    return {
      circulatedForm: {
        circulatedObject: null,
        circulatedUserId: null,
        circulatedOrgId: null,
        explain: null,
        circulatedUser: null,
        circulatedOrg: null,
      },
      circulatedObjectDialogVisible: false, //传阅弹窗
      activeName: "orgTree",
      proCirculatedOrgsData: [],
      proCirculatedUserData: [],
      defaultProps: {
        children: "children",
        label: "name",
        isLeaf: "isLeaf",
      },
      diaPeople: {
        visible: false,
        title: "",
        saveName: "",
        saveTitleMap: {
          circulatedDetermine: "选择传阅人",
        },
        showOrgsTree: true,
      },
    };
  },
  mounted() {},
  methods: {
    selectCirculatedObject() {
      this.circulatedObjectDialogVisible = true;
    },
    //树懒加载-人员
    loadUserNode(node, resolve) {
      let param = {
        level: node.level + 1,
        nodeId: node?.data?.id ?? "",
      };
      apiCirculatedUserTree(param)
        .then(res => {
          if (res.status == 0) {
            let treeData = res?.data?.data ?? [];
            return resolve(treeData);
          }
        })
        .catch(e => {
          console.log("报错了，", e);
          return resolve([]);
        });
    },
    loadOrgNode(node, resolve) {
      let param = {
        level: node.level + 1,
        nodeId: node?.data?.id ?? "",
      };
      apiCirculatedOrgTree(param)
        .then(res => {
          if (res.status == 0) {
            let treeData = res?.data?.data ?? [];
            return resolve(treeData);
          }
        })
        .catch(e => {
          console.log("报错了，", e);
          return resolve([]);
        });
    },
    onOpenPeopleDialog(diaSaveName) {
      this.diaPeople.title = this.diaPeople.saveTitleMap[diaSaveName];
      this.diaPeople.saveName = diaSaveName;
      this.diaPeople.showOrgsTree = true;
      this.diaPeople.visible = true;
    },
    onSavePeople(val) {
      this[this.diaPeople.saveName](val);
    },
    //传阅提交
    circulatedSubmit() {
      let param = {
        woId: this.common.woId,
        copyManId: this.circulatedForm.circulatedUserId,
        copyMan: this.circulatedForm.circulatedUser,
        copyDeptCode: this.circulatedForm.circulatedOrgId,
        copyDeptName: this.circulatedForm.circulatedOrg,
        circularDesc: this.circulatedForm.explain,
      };
      apiCirculated(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("传阅成功");
            this.$emit("closeDialogCirculated");
          } else {
            this.$message.error("传阅失败");
          }
        })
        .catch(error => {
          console.log(error);
          this.$message.error("传阅失败");
        });
    },
    circulatedCancel() {
      this.circulatedForm = {
        ...this.$options.data,
      };
      this.$emit("closeDialogCirculated");
    },
    resetCirculatedChecked() {
      this.$refs.circulatedObjectOrgTree.setCheckedKeys([]);
      this.$refs.circulatedObjectUserTree.setCheckedKeys([]);
      this.circulatedForm = {
        ...this.$options.data,
      };
    },
    circulatedObjectHandleClose() {
      this.resetCirculatedChecked();
      this.circulatedObjectDialogVisible = false;
    },
    circulatedDetermine({ usersChecked, orgsChecked, selectionUser }) {
      if (selectionUser && selectionUser.length > 0) {
        let usersCheckedName = selectionUser.map(item => {
          return item.trueName;
        });
        this.circulatedForm.circulatedUser = usersCheckedName.join(",");
        let usersCheckedId = selectionUser.map(item => {
          return item.userName;
        });
        this.circulatedForm.circulatedUserId = usersCheckedId.join(",");
      } else {
        let usersCheckedName = usersChecked.map(item => {
          return item.name;
        });
        let usersCheckedId = usersChecked.map(item => {
          return item.id;
        });
        this.circulatedForm.circulatedUserId = usersCheckedId.join(",");
        this.circulatedForm.circulatedUser = usersCheckedName.join(",");
      }

      let orgsCheckedName = orgsChecked.map(item => {
        return item.name;
      });
      this.circulatedForm.circulatedOrg = orgsCheckedName.join(",");
      this.circulatedForm.circulatedObject = this.stitchingAlgorithm(
        this.circulatedForm.circulatedOrg,
        this.circulatedForm.circulatedUser
      );
      let orgsCheckedId = orgsChecked.map(item => {
        return item.id;
      });
      this.circulatedForm.circulatedOrgId = orgsCheckedId.join(",");
    },
    stitchingAlgorithm(orgName, userName) {
      if (orgName.length !== 0 && userName.length !== 0) {
        return orgName + "," + userName;
      } else {
        if (orgName.length !== 0) {
          return orgName;
        } else if (userName.length !== 0) {
          return userName;
        } else {
          return "";
        }
      }
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-tree {
  padding-top: 15px;
  padding-left: 10px;
  // 不可全选样式
  .el-tree-node {
    .is-leaf + .el-checkbox .el-checkbox__inner {
      display: inline-block;
    }
    .el-checkbox .el-checkbox__inner {
      display: none;
    }
  }
}
</style>
