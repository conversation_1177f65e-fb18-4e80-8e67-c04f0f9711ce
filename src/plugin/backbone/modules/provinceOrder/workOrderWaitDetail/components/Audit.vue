<template>
  <div>
    <el-form ref="auditForm" :model="auditForm" label-width="80px">
      <el-form-item
        label="审核结果:"
        prop="opResult"
        :rules="{
          required: true,
          message: '请选择审核结果',
        }"
      >
        <el-radio-group v-model="auditForm.opResult" style="width: 260px">
          <el-radio label="Y">同意</el-radio>
          <el-radio label="N">拒绝</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="审核意见:" prop="processSuggestion">
        <el-input
          type="textarea"
          :rows="2"
          placeholder="请填写审核意见"
          v-model="auditForm.processSuggestion"
          style="width: 260px"
          show-word-limit
          maxlength="500"
          @keyup.native="descTip(500,'processSuggestion','showTip')"
        >
        </el-input>
        <div class="el-form-item__error"  v-if="showTip">已超过填写字数上限</div>
      </el-form-item>
    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: right">
      <el-button
        type="primary"
        @click="handleAuditSubmit('auditForm')"
        v-loading.fullscreen.lock="auditFullScreenLoading"
        >提 交</el-button
      >
      <el-button @click="onResetAudit">重 置</el-button>
    </div>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import { apiActionPublic, getCurrentTime } from "../api/CommonApi";
export default {
  name: "Audit",
  props: {
    common: Object,
    opContent: Number,
    actionName: String,
  },
  data() {
    return {
      showTip:false,
      showTime:5000,
      auditForm: {
        opResult: null,
        processSuggestion: null,
      },
      auditFullScreenLoading: false,
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  mounted() {
    console.log(this.common.professionalTypeName);
  },
  watch: {
    opContent(val) {
      this.opContent = val;
    },
  },
  methods: {
    descTip(count,name,showName){
      if (this.auditForm[name].length>=count){
        this[showName] = true;
        setTimeout(() => {
          this[showName] = false;
        }, this.showTime);
      }
      else{
        this[showName] = false;
      }
    },
    handleAuditSubmit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.auditFullScreenLoading = true;
          let param = {
            // opContent: this.opContent,
            // woId: this.common.woId,
            // processInstId: this.common.processInstId,
            // processDefId: this.common.processDefId,
            // workItemId: this.common.workItemId,
            // processNode: this.common.processNode,
            // opResult: this.auditForm.opResult,
            // processSuggestion: this.auditForm.processSuggestion,

            woId: this.common.woId,
            workItemId: this.common.workItemId,
            processInstId: this.common.processInstId,
            actionName: this.actionName,
            hangAuditUser: this.userInfo.userName,
            hangAuditTime: getCurrentTime(Date.now()),
            hangAuditResult: this.auditForm.opResult,
            hangAuditOpinion: this.auditForm.processSuggestion,
          };
          apiActionPublic(param)
            .then(res => {
              if (res.status == "0") {
                this.$message.success("审核提交成功");
                if (this.opContent == 1) {
                  this.$emit("closeDialogPendingReview");
                } else if (this.opContent == 2) {
                  this.$emit("closeDialogSolutionToHangAudit");
                }
              } else {
                this.$message.error(res.msg);
              }
              this.auditFullScreenLoading = false;
            })
            .catch(error => {
              console.log(error);
              this.auditFullScreenLoading = false;
              this.$message.error(error.msg);
            });
        } else {
          return false;
        }
      });
    },
    onResetAudit() {
      this.auditForm = {
        ...this.$options.data,
      };
    },
  },
};
</script>
