<template>
  <el-card shadow="always" class="card" :body-style="{ padding: '10px 24px' }">
    <el-collapse v-model="activeNames">
      <el-collapse-item name="1">
        <template slot="title"
          ><span class="header-title">处理详情</span></template
        >
        <div class="content">
          <el-collapse>
            <el-collapse-item v-for="(item, index) of detailsList" :key="index">
              <span class="collapse-title" slot="title">{{ item.name }}</span>
              <div
                v-for="(itemCont, key) of item.content"
                :key="key"
                class="content__list"
              >
                <span
                  class="detail-p"
                  v-html="itemCont.handleContent"
                  @click="handleBtnClick($event)"
                ></span>
                <template v-if="itemCont.files.length > 0">
                  附件：【
                  <span style="margin-bottom: 0">
                    <el-tag
                      class="fileName_style"
                      v-for="(itemFile, index) of itemCont.files"
                      @click="handleDownload(itemFile.attId)"
                      :title="itemFile.attOrigName"
                      :key="index"
                      ><div class="text-truncate">
                        {{ itemFile.attOrigName }}
                      </div></el-tag
                    > </span
                  >】
                </template>
                <template v-if="item.name == '现场打点'">
                  <i class="el-icon-place" @click="mapVisible = true"></i>
                </template>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </el-collapse-item>
    </el-collapse>
    <el-dialog
      title="现场打点"
      :visible.sync="mapVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="1000px"
    >
      <map-location :woId="woId"></map-location>
    </el-dialog>
    <el-dialog
      title="反馈单详情"
      :visible.sync="feedbackHistoryVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="dialogBackSingleClose"
      :fullscreen="false"
      width="83%"
      top="5vh"
    >
      <div class="content">
        <el-descriptions
          title="故障定性信息"
          class="descriptions"
          v-if="list.faultQuality.isShow && list.faultQuality.isShow == 'Y'"
        >
          <el-descriptions-item label="故障所属专业">{{
            list.faultQuality.professionalType
          }}</el-descriptions-item>
          <!--          <el-descriptions-item label="故障等级">{{-->
          <!--            list.faultQuality.faultLevel-->
          <!--            }}</el-descriptions-item>-->
          <el-descriptions-item label="工单优先级">{{
            list.faultQuality.faultLevel
          }}</el-descriptions-item>
          <el-descriptions-item label="故障发生时间">{{
            list.faultQuality.alarmCreateTime
          }}</el-descriptions-item>
          <el-descriptions-item label="通知时间">{{
            list.faultQuality.faultNoticeTime
          }}</el-descriptions-item>
          <el-descriptions-item label="故障恢复时间">{{
            list.faultQuality.faultRecoveryTime
          }}</el-descriptions-item>
          <el-descriptions-item label="故障处理历时">{{
            list.faultQuality.faultDuration
          }}</el-descriptions-item>
          <el-descriptions-item label="故障恢复历时">{{
            list.faultQuality.recoveryDuration
          }}</el-descriptions-item>

          <el-descriptions-item label="故障处理部门">
            {{ list.faultQuality.dept }}
          </el-descriptions-item>
          <el-descriptions-item label="故障修复人">{{
            list.faultQuality.recoveryPerson
          }}</el-descriptions-item>
          <el-descriptions-item label="修复人电话">{{
            list.faultQuality.recoveryPhone
          }}</el-descriptions-item>
          <el-descriptions-item
            label="关联工单"
            v-if="list.faultQuality.linkedSheetNo"
            >{{ list.faultQuality.linkedSheetNo }}</el-descriptions-item
          >

          <el-descriptions-item label="是否影响业务">{{
            list.faultQuality.isEffectBusiness
          }}</el-descriptions-item>
          <el-descriptions-item
            label="是否基站退服"
            v-if="professionalTypeId == 7"
            >{{ list.faultQuality.isSiteOffline }}</el-descriptions-item
          >
          <el-descriptions-item
            label="退服原因"
            v-if="
              professionalTypeId == 7 && list.faultQuality.isSiteOffline == '是'
            "
            >{{ list.faultQuality.siteOfflineReason }}</el-descriptions-item
          >
          <el-descriptions-item
            label="是否为铁塔原因"
            v-if="professionalTypeId == 7"
            >{{ list.faultQuality.isTowerReason }}</el-descriptions-item
          >
        </el-descriptions>
        <el-descriptions
          title="故障专业信息"
          class="descriptions"
          v-if="list.faultMajor.isShow && list.faultMajor.isShow == 'Y'"
        >
          <!-- 无线网、核心网、动环网、数据网、铁塔、接入网 IDC29、IP互联网33、核心网其他32 -->
          <template
            v-if="
              professionalTypeId == 7 ||
              professionalTypeId == 1 ||
              professionalTypeId == 4 ||
              professionalTypeId == 29 ||
              professionalTypeId == 33 ||
              professionalTypeId == 32 ||
              professionalTypeId == 5 ||
              professionalTypeId == 9 ||
              professionalTypeId == 10
            "
          >
            <el-descriptions-item label="故障分类">
              {{ list.faultMajor.faultCate }}</el-descriptions-item
            >
            <el-descriptions-item label="故障原因">
              {{ list.faultMajor.faultReason }}</el-descriptions-item
            >
            <el-descriptions-item
              label="故障厂家"
              :span="6"
              v-if="
                professionalTypeId == 7 &&
                (list.faultMajor.faultCate == '传输系统' ||
                  list.faultMajor.faultCate == '基站设备')
              "
            >
              {{ list.faultMajor.vendor }}</el-descriptions-item
            >
            <el-descriptions-item
              label="故障厂家"
              :span="6"
              v-if="professionalTypeId != 7"
            >
              {{ list.faultMajor.vendor }}</el-descriptions-item
            >
            <el-descriptions-item label="设备类型">
              {{ list.faultMajor.eqpType }}</el-descriptions-item
            >
            <el-descriptions-item label="设备名称">
              {{ list.faultMajor.eqpName }}</el-descriptions-item
            >
            <el-descriptions-item label="故障处理过程及原因描述" :span="3">
              {{ list.faultMajor.falutReasonDesc }}</el-descriptions-item
            >
            <el-descriptions-item label="备注" :span="3">
              {{ list.faultMajor.falutComment }}</el-descriptions-item
            >
          </template>
          <!-- 增值平台、移动数通网、固网、IT监控、IPTV、智能网、vIMS、5GC、物联网、省份云、MEC -->
          <template
            v-if="
              professionalTypeId == 8 ||
              professionalTypeId == 2 ||
              professionalTypeId == 6 ||
              professionalTypeId == 15 ||
              professionalTypeId == 16 ||
              professionalTypeId == 18 ||
              professionalTypeId == 19 ||
              professionalTypeId == 20 ||
              professionalTypeId == 21 ||
              professionalTypeId == 24 ||
              professionalTypeId == 25
            "
          >
            <el-descriptions-item label="故障处理过程及原因描述" :span="3">
              {{ list.faultMajor.falutReasonDesc }}</el-descriptions-item
            >
            <el-descriptions-item label="备注" :span="3">
              {{ list.faultMajor.falutComment }}</el-descriptions-item
            >
          </template>
          <!-- 传输网 -->
          <template v-if="professionalTypeId == 3">
            <el-descriptions-item label="故障状态">
              {{ list.faultMajor.faultStatus }}</el-descriptions-item
            >
            <el-descriptions-item label="网络类型">
              {{ list.faultMajor.networkType }}</el-descriptions-item
            >
            <el-descriptions-item label="故障源">
              {{ list.faultMajor.faultSource }}</el-descriptions-item
            >
            <el-descriptions-item label="处理方法">
              {{ list.faultMajor.processMethod }}</el-descriptions-item
            >
            <el-descriptions-item label="故障分类">
              {{ list.faultMajor.faultCate }}</el-descriptions-item
            >
            <el-descriptions-item label="故障原因">
              {{ list.faultMajor.faultReason }}</el-descriptions-item
            >
            <el-descriptions-item label="设备类型">
              {{ list.faultMajor.eqpType }}</el-descriptions-item
            >
            <el-descriptions-item label="设备名称">
              {{ list.faultMajor.eqpName }}</el-descriptions-item
            >

            <el-descriptions-item label="机盘名称">
              {{ list.faultMajor.diskName }}</el-descriptions-item
            >
            <el-descriptions-item label="业务中断系统">
              {{ list.faultMajor.interSystem }}</el-descriptions-item
            >
            <el-descriptions-item label="受影响的系统">
              {{ list.faultMajor.effectSystem }}</el-descriptions-item
            >
            <el-descriptions-item label="中断电路">
              {{ list.faultMajor.interCircuit }}</el-descriptions-item
            >
            <el-descriptions-item label="故障区间">
              {{ list.faultMajor.faultRange }}</el-descriptions-item
            >
            <el-descriptions-item label="受影响的电路">
              {{ list.faultMajor.effectCircuit }}</el-descriptions-item
            >
            <el-descriptions-item label="故障处理过程及原因描述" :span="3">
              {{ list.faultMajor.falutReasonDesc }}</el-descriptions-item
            >
            <el-descriptions-item label="备注" :span="3">
              {{ list.faultMajor.falutComment }}</el-descriptions-item
            >
          </template>
        </el-descriptions>
      </div>
    </el-dialog>
  </el-card>
</template>

<script>
import {
  apiFileDownload,
  apiGetProcessInfo,
  apiqueryFeedback,
} from "../api/CommonApi";
import mapLocation from "./MapContainer.vue";
export default {
  name: "DealDetails",
  props: {
    woId: String,
  },
  components: { mapLocation },
  data() {
    return {
      detailsList: [],
      appendixFileLoading: false,
      feedbackHistoryVisible: false,
      activeNames: ["1"],
      mapVisible: false,
      professionalTypeId: "",
      list: {
        faultQuality: {}, // 故障定性
        faultMajor: {}, // 故障专业信息
        // auditInfo: {}, // 审核信息
        // chartererJudge: {}, // 包机人评定
      },
    };
  },
  mounted() {
    this.getDetailData();
  },
  computed: {},

  methods: {
    getFeedbackData(linkId) {
      let param = {
        linkId: linkId,
      };

      apiqueryFeedback(param)
        .then(res => {
          if (res.status == 0) {
            let data = res?.data ?? [];
            if (data.length > 0) {
              this.list.faultQuality = data[0].faultQuality;
              this.list.chartererJudge = data[0].chartererJudge;
              this.list.faultQuality = data[0].faultQuality;
              this.list.faultMajor = data[0].faultMajor;
              this.professionalTypeId = data[0].faultQuality.professionalTypeId;
            }
          }
        })
        .catch(err => {
          console.log(err);
        });
    },
    dialogBackSingleClose() {
      this.feedbackHistoryVisible = false;
    },
    handleBtnClick(e) {
      console.log(e);
      if (e.target.id === "btn") {
        const linkId = e.target.dataset.linkid;
        this.getFeedbackData(linkId);
        this.feedbackHistoryVisible = true;
      }
    },
    getDetailData() {
      let param = {
        woId: this.woId,
      };
      apiGetProcessInfo(param)
        .then(res => {
          this.detailsList = res.data;
        })
        .catch(err => {
          console.log(err);
        });
    },
    handleDownload(attId) {
      let param = {
        attId: attId,
        watermark: "", //水印
      };
      apiFileDownload(param)
        .then(res => {})
        .catch(err => {
          console.log(err);
        });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../assets/common.scss";
.custom-theme-default .el-collapse {
  border-top: 0;
}

::v-deep .el-collapse-item__wrap {
  border-bottom: 0;
}
.content ::v-deep .el-collapse-item {
  .el-collapse-item__header {
    background-color: #fafafa;
    padding-left: 20px;
  }
}
.content ::v-deep .content__list {
  padding-top: 10px;
  padding-left: 25px;
}
::v-deep .detail-p {
  display: inline-block;
  padding: 0px 5px;
  margin: 0;
}

.fujian {
  display: flex;
  margin-top: 5px;
  .label {
    width: 100px;
    display: block;
  }
  .links {
    flex: 1;
  }
}
.el-icon-place {
  font-size: 22px;
  color: #b50b14;
}
</style>
