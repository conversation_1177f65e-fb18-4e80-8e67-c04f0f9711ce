<template>
  <div>
    <h3 style="margin-top: -5px">
      告警信息
      <el-button type="primary" @click="handleClear()" style="float: right"
        >消除</el-button
      >
    </h3>
    <el-table
      ref="table"
      :data="tableData"
      border
      stripe
      v-loading="tableLoading"
    >
      <el-table-column
        type="selection"
        :selectable="selectDisableRoom"
      ></el-table-column>
      <el-table-column type="index" width="60px" label="序号"></el-table-column>
      <el-table-column label="告警地区" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          {{ scope.row.alarmProvince }}{{ scope.row.alarmRegion
          }}{{ scope.row.alarmCity }}
        </template>
      </el-table-column>
      <el-table-column
        prop="professionalTypeName"
        label="专业"
        show-overflow-tooltip
      >
      </el-table-column>

      <el-table-column
        prop="locateNeName"
        label="告警对象"
        width="150"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="alarmTitle"
        label="标题"
        width="200"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="orgTypeName"
        label="告警类型"
        width="100"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        label="故障发生时间"
        prop="alarmCreateTime"
        width="160"
        show-overflow-tooltip
      ></el-table-column>
      <!-- <el-table-column
        label="清除方式"
        prop="alarmClearType"
        width="100"
      ></el-table-column> -->
      <el-table-column
        label="实际清除时间"
        prop="alarmClearTime"
        width="160"
        show-overflow-tooltip
      ></el-table-column>
      <!-- <el-table-column label="操作" width="100" fixed="right">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.alarmClearTime == ''"
            type="primary"
            @click="handleClear(scope.row)"
          >
            消除
          </el-button>
        </template>
      </el-table-column> -->
    </el-table>
    <h3>消障确认</h3>
    <el-form
      ref="form"
      :model="form"
      label-width="115px"
      class="audit-form"
      :rules="rules"
    >
      <el-form-item label="是否确认消障:" prop="isClearConfirm" required>
        <el-radio-group v-model="form.isClearConfirm">
          <el-radio label="Y">是</el-radio>
          <el-radio label="N">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="备注:" prop="falutComment">
        <el-input
          v-model="form.falutComment"
          placeholder="请输入备注"
          style="width: 100%"
          show-word-limit
          maxlength="255"
          @keyup.native="descTip(255, 'falutComment', 'showTip')"
        >
        </el-input>
        <div class="el-form-item__error" v-if="showTip">已超过填写字数上限</div>
      </el-form-item>
    </el-form>
    <!-- 选择确认消障时间 -->
    <el-dialog
      title="选择消障时间"
      :visible.sync="dialogSelectTimeVisible"
      :close-on-click-modal="false"
      append-to-body
      width="300px"
      @close="clearTimeDialogClose"
    >
      <!--      <el-date-picker-->
      <!--        v-model="selectAlarmClearTime"-->
      <!--        type="datetime"-->
      <!--        placeholder="请选择时间"-->
      <!--        value-format="yyyy-MM-dd HH:mm:ss"-->
      <!--        clearable-->
      <!--        style="width: 100%"-->
      <!--      />-->
      <el-form
        ref="clearParms"
        :model="clearParms"
        class="audit-form"
        :rules="clearParmsRule"
      >
        <el-form-item label="" prop="alarmCreateTime" required>
          <el-date-picker
            v-model="clearParms.alarmCreateTime"
            type="datetime"
            placeholder="请选择时间"
            value-format="yyyy-MM-dd HH:mm:ss"
            clearable
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button type="primary" @click="handleSelectTime('clearParms')"
          >提 交</el-button
        >
        <el-button @click="dialogSelectTimeVisible = false">取消</el-button>
      </div>
    </el-dialog>
    <div slot="footer" style="padding: 10px 20px; text-align: right">
      <el-button
        type="primary"
        @click="handleAuditSubmit('form')"
        v-loading.fullscreen.lock="fullScreenLoading"
        >提 交</el-button
      >
      <!-- <el-button @click="onResetAudit">重 置</el-button> -->
    </div>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import {
  apiActionPublic,
  getCurrentTime,
  apiQueryAlarmDetail,
  apiclearQuery,
} from "../api/CommonApi";
import moment from "moment";
export default {
  name: "Audit",
  props: {
    common: Object,
    actionName: String,
  },
  data() {
    var validAlarmCreateTime = (rule, value, callback) => {
      if (value === "" || value === null) {
        callback(new Error("请选择时间"));
      } else {
        let seconds3 = moment(
          this.clearParms.alarmCreateTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(moment(new Date(), "YYYY-MM-DD HH:mm:ss"), "seconds");

        var isWrong = false;
        this.$refs.table.selection.forEach(item => {
          let seconds4 = moment(
            this.clearParms.alarmCreateTime,
            "YYYY-MM-DD HH:mm:ss"
          ).diff(
            moment(item.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
            "seconds"
          );
          if (seconds4 < 0) {
            isWrong = true;
          }
        });

        if (seconds3 > 0) {
          callback(new Error("消障时间不能晚于当前时间，请重新填写。"));
        }

        if (isWrong) {
          callback(new Error("消障时间早于所选告警的发生时间，请重新填写。"));
        } else {
          callback();
        }
      }
    };
    return {
      showTip: false,
      showTime: 5000,
      tableLoading: false,
      tableData: [],
      form: {
        isClearConfirm: "N",
      },
      fullScreenLoading: false,
      dialogSelectTimeVisible: false,
      // selectAlarmClearTime: "",
      clearParms: {
        alarmCreateTime: "",
      },
      clearParmsRule: {
        alarmCreateTime: [
          { validator: validAlarmCreateTime, required: true, trigger: "blur" },
        ],
      },
      // disabledPicker: {
      //   disabledDate: this.disabledDate,
      //   selectableRange: this.timeRange,
      // },
      // disabledPicker: {
      //   disabledDate: time => {
      //
      //   },
      //   selectableRange: "00:00:00 - 23:59:59",
      // },
      rules: {
        isClearConfirm: [{ required: true, message: "请选择" }],
      },
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  mounted() {
    this.getTableData();
    console.log(this.userInfo);
  },
  watch: {
    // selectAlarmClearTime: {
    //   immediate: false,
    //   handler: function (newVal) {
    //     // 故障发生时间的  时分秒。加1分钟
    //     let alarmCreateTimeStr = getCurrentTime(
    //       new Date(this.tableData[0].alarmCreateTime).getTime() + 60 * 1000
    //     ).slice(-8);
    //     this.disabledPicker.selectableRange = `${alarmCreateTimeStr} - 23:59:59`;
    //   },
    // },
  },
  methods: {
    descTip(count, name, showName) {
      if (this.form[name].length >= count) {
        this[showName] = true;
        setTimeout(() => {
          this[showName] = false;
        }, this.showTime);
      } else {
        this[showName] = false;
      }
    },
    handleAuditSubmit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.fullScreenLoading = true;
          let param = {
            woId: this.common.woId,
            workItemId: this.common.workItemId,
            processInstId: this.common.processInstId,
            actionName: this.actionName,
            createUserEn: this.userInfo.userName,
            dept: this.userInfo.deptId,
            auditTime: getCurrentTime(Date.now()),
            isClearConfirm: this.form.isClearConfirm,
          };
          apiActionPublic(param)
            .then(res => {
              if (res.status == "0") {
                this.$message.success("消障确认成功");
                this.$emit("closeDialogEliminateFaults");
              } else {
                this.$message.error(res.msg);
              }
              this.fullScreenLoading = false;
            })
            .catch(error => {
              console.log(error);
              this.fullScreenLoading = false;
              this.$message.error(error.msg);
            });
        } else {
          return false;
        }
      });
    },
    getTableData() {
      this.tableLoading = true;
      let param = {
        woId: this.common.woId,
      };
      apiQueryAlarmDetail(param)
        .then(res => {
          if (res.status == "0") {
            this.tableData = res?.data?.info ?? [];
          }
          this.tableLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.tableLoading = false;
        });
    },
    selectDisableRoom(row) {
      if (row.alarmClearTime == "") {
        return true;
      } else {
        return false;
      }
    },
    handleSelectTime(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          apiclearQuery(this.clearParms)
            .then(res => {
              if (res.status == "0") {
                this.form.isClearConfirm = "Y";
                this.tableData.forEach(item => {
                  this.$refs.table.selection.forEach(selItem => {
                    if (selItem.alarmStaId == item.alarmStaId)
                      item.alarmClearTime = this.clearParms.alarmCreateTime;
                  });
                });
                this.dialogSelectTimeVisible = false;
                this.$message.success("消除成功！");
              }
            })
            .catch(error => {
              console.log(error);
            });
        } else {
          return false;
        }
      });
      // this.clearParms.alarmCreateTime = this.selectAlarmClearTime;
    },
    // 批量消除
    handleClear(row) {
      this.clearParms.uniqueId = [];
      if (this.$refs.table.selection.length != 0) {
        this.dialogSelectTimeVisible = true;
        this.$nextTick(() => {
          this.$refs.clearParms.clearValidate();
        });

        this.$refs.table.selection.forEach(item => {
          this.clearParms.uniqueId.push(item.alarmStaId);
        });
      } else {
        this.$message.error("选中要消除的工单");
        return false;
      }
    },
    onResetAudit() {
      this.form = {
        ...this.$options.data,
      };
    },
    clearTimeDialogClose() {
      this.clearParms.alarmCreateTime = "";
    },
  },
};
</script>
<style scoped>
.custom-theme-default .el-rate {
  padding-top: 5px;
}
</style>
