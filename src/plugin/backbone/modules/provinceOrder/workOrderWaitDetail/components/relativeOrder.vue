<template>
  <section>
    <el-form :model="searchForm" label-width="80px">
      <el-row :gutter="20">
        <el-col :span="9">
          <el-form-item label-width="0" prop="jobCode">
            <el-input
              v-model="searchForm.titleOrNo"
              placeholder="请输入工单主题或工单编号关键字"
              clearable
              style="width: 100%"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="9">
          <el-form-item label="故障专业" prop="type">
            <el-select
              v-model="searchForm.professionalType"
              placeholder="请选择内容"
              style="width: 100%"
              clearable
            >
              <el-option
                v-for="item in professionalList"
                :key="item.dictCode"
                :label="item.dictName"
                :value="item.dictCode"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-button type="primary" @click="handleRelativeOrder"
            >查询</el-button
          >
        </el-col>
      </el-row>
    </el-form>
    <el-table
      ref="tableRef"
      :data="tableData"
      border
      style="width: 100%"
      stripe
      v-loading="tableLoading"
      @selection-change="handleSelectionChange"
      class="relative-table"
    >
      <el-table-column
        type="selection"
        width="55"
        align="center"
        label-class-name="DisabledSelection"
      >
      </el-table-column>
      <el-table-column prop="sheetNo" label="工单编号" width="260">
        <template slot-scope="scope">
          <el-button
            type="text"
            class="sheetNo_style"
            @click="toDoDetail(scope.row)"
            ><span style="font-size: 14px">{{
              scope.row.sheetNo
            }}</span></el-button
          >
        </template>
      </el-table-column>
      <el-table-column prop="sheetTitle" label="工单主题" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-html="scope.row.sheetTitle" style="white-space: pre"></span>
        </template>
      </el-table-column>
      <el-table-column
        prop="professionalTypeName"
        label="故障专业"
        width="140"
      />
      <el-table-column prop="sheetCreateTime" label="建单时间" width="160" />
    </el-table>
    <div class="relative-footer">
      <span class="tip">默认展示当前工单主告警发生时间前后一小时内的工单</span>
      <pagination
        ref="pagination"
        :total="pageObj.total"
        :page.sync="pageObj.pageNum"
        :limit.sync="pageObj.pageSize"
        layout="->, total, sizes, prev, pager, next"
        @change="handleRelativeOrder"
      />
    </div>
    <div style="padding: 10px 20px; text-align: center">
      <el-button type="primary" @click="handleSubmit()">提交</el-button>
      <el-button @click="onReset">取消</el-button>
    </div>

    <!-- 工单详情，抽屉组件 -->
    <el-dialog
      title="工单详情"
      :visible.sync="isShowDetail"
      append-to-body
      width="90%"
      top="50px"
    >
      <work-order-to-do-detail
        :detailData="detailData"
        :key="detailData.woId"
      ></work-order-to-do-detail>
    </el-dialog>
  </section>
</template>

<script>
import Pagination from "../../../workOrder/components/Pagination.vue";
import { apiHandleRelative } from "../api/CommonApi";
export default {
  name: "RelativeOrder",
  components: {
    Pagination,
    WorkOrderToDoDetail: () => import("./../WorkOrderToDoDetail.vue"),
  },
  props: {
    formData: Object,
    professionalList: Array,
  },
  data() {
    return {
      searchForm: {
        titleOrNo: "",
        professionalType: "",
      },
      typeList: [],
      tableLoading: false,
      isShowDetail: false,
      detailUrl: "",
      tableData: [],
      detailData: {},
      multipleSelection: [],
      pageObj: {
        total: 0,
        pageNum: 1,
        pageSize: 10,
      },
    };
  },
  mounted() {
    this.handleRelativeOrder();
  },
  methods: {
    // 获取关联工单列表数据
    handleRelativeOrder() {
      this.tableLoading = true;
      let params = {
        woId: this.formData.woId,
        titleOrNo: this.searchForm.titleOrNo,
        professionalType: this.searchForm.professionalType,
        pageNum: this.pageObj.pageNum,
        pageSize: this.pageObj.pageSize,
      };
      apiHandleRelative(params).then(resp => {
        this.tableLoading = false;
        if (resp.status == 0) {
          this.tableData = resp.data.list;
          this.pageObj.total = resp.data.total;
        }
      });
    },
    handleSelectionChange(val) {
      if (val.length > 1) {
        this.$refs.tableRef.clearSelection(); //清空列表的选中
        this.$refs.tableRef.toggleRowSelection(val[val.length - 1]); //只显示选中最后一个 这时val还是多选的列表(就是你选中的几个数据)
      }
      this.multipleSelection = [val[val.length - 1]];
    },
    handleSubmit() {
      if (this.multipleSelection.length > 0) {
        this.$emit("submitRelativeOrder", this.multipleSelection);
      } else {
        this.$message.warning("请选择一条关联工单");
      }
    },

    onReset() {
      this.$emit("closeRelativeOrder");
    },
    //跳转详情
    toDoDetail(row) {
      debugger;
      this.isShowDetail = true;
      this.detailData = row;
      // this.detailUrl =
      //   "http://127.0.0.1:8228/#/layoutprovinceOrder_detail/provinceOrder_detail?" +
      //   "processInstId=" +
      //   data.processInstId +
      //   "&woId=" +
      //   data.woId +
      //   "&processNode=" +
      //   data.processNode +
      //   "&fromPage=工单查询&networkType=" +
      //   data.networkType;
    },
  },
};
</script>
<style lang="scss" scoped>
.relative-footer {
  position: relative;
  .tip {
    position: absolute;
    top: 11px;
    left: 0;
    font-size: 12px;
  }
}
.sheetNo_style {
  color: #409eff;
  -webkit-user-select: unset;
  -moz-user-select: unset;
  user-select: unset;
}
</style>
<style lang="scss">
/*表格全选框去除空框*/
.DisabledSelection .el-checkbox__inner {
  display: none;
  position: relative;
}

/*表格全选框改为：选择*/
.DisabledSelection::before {
  content: "选择";
  position: absolute;
  left: 7px;
  line-height: 14px;
  padding: 5px 0 0 6px;
}
.custom-theme-default .relative-table .el-table__cell:first-child .cell {
  padding-left: 14px;
}
.custom-theme-default .relative-table .el-table-column--selection .cell {
  padding-left: 14px;
}
</style>
