<template>
  <div class="qualitative-review">
    <el-form
      ref="qualitativeReviewForm"
      :inline="false"
      class="demo-form-inline"
      :model="qualitativeReviewForm"
      label-width="140px"
    >
      <el-card
        shadow="never"
        header="故障专业信息"
        :body-style="{ padding: '20px 8px' }"
        class="cus-card"
      >
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item
              label="故障状态:"
              prop="faultStatus"
              :rules="{
                required: true,
                message: '请选择故障状态',
              }"
            >
              <dict-select
                :value.sync="qualitativeReviewForm.faultStatus"
                :dictId="10017"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="网络类型:"
              prop="networkType"
              :rules="{
                required: true,
                message: '请选择网络类型',
              }"
            >
              <dict-select
                :value.sync="qualitativeReviewForm.networkType"
                :dictId="10018"
                style="width: 100%"
                :notSelect="true"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="故障分类:"
              prop="faultCate"
              :rules="{
                required: true,
                message: '请选择故障分类',
              }"
            >
              <dict-select
                :value.sync="qualitativeReviewForm.faultCate"
                :dictId="10055"
                style="width: 100%"
                @change="faultCateChange"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row
          type="flex"
          style="flex-wrap: wrap"
          :gutter="20"
          v-if="qualitativeReviewForm.faultCate == '1'"
          key="faultCateOne"
        >
          <el-col :span="8">
            <el-form-item
              label="故障原因分类:"
              :rules="{
                required: true,
                message: '请输入内容',
              }"
              prop="faultReasonCate"
            >
              <el-input
                v-model="qualitativeReviewForm.faultReasonCate"
                style="width: 100%"
                placeholder="请输入内容"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="海陆缆名称:"
              prop="seaLandFiberName"
              :rules="{
                required: true,
                message: '请输入内容',
              }"
            >
              <el-input
                v-model="qualitativeReviewForm.seaLandFiberName"
                placeholder="请输入内容"
                style="width: 100%"
              >
                <el-button
                  style="
                    background: #b50b14;
                    border-color: #b50b14;
                    color: #fff;
                    padding: 9px 20px;
                    top: -0.8px;
                    position: relative;
                  "
                  slot="append"
                  @click="selectSeaLandFiberName"
                >
                  选择
                </el-button>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="海陆缆段:"
              prop="seaLandFiberSeg"
              :rules="{
                required: true,
                message: '请输入内容',
              }"
            >
              <el-input
                v-model="qualitativeReviewForm.seaLandFiberSeg"
                style="width: 100%"
                placeholder="请输入内容"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="运营商名称:"
              prop="operatorName"
              :rules="{
                required: true,
                message: '请输入内容',
              }"
            >
              <el-input
                v-model="qualitativeReviewForm.operatorName"
                style="width: 100%"
                placeholder="请输入内容"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="海陆缆类型:"
              prop="seaLandFiberType"
              :rules="{
                required: true,
                message: '请选择内容',
              }"
            >
              <dict-select
                :value.sync="qualitativeReviewForm.seaLandFiberType"
                :dictId="10056"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="海陆缆故障区域:">
              <el-input
                v-model="qualitativeReviewForm.seaLandFiberFaultArea"
                style="width: 100%"
                placeholder="请输入内容"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否超时:">
              <span v-if="qualitativeReviewForm.isOverTime == '0'">否</span
              ><span v-else-if="qualitativeReviewForm.isOverTime == '1'"
                >是</span
              >
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              label="故障原因描述:"
              prop="falutReasonDesc"
              :rules="{
                required: true,
                message: '故障原因描述不能为空',
              }"
            >
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="qualitativeReviewForm.falutReasonDesc"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row
          type="flex"
          style="flex-wrap: wrap"
          :gutter="20"
          v-if="qualitativeReviewForm.faultCate == '2'"
          key="faultCateTwo"
        >
          <el-col :span="8">
            <el-form-item
              label="故障原因分类:"
              prop="faultReasonCate"
              :rules="{
                required: true,
                message: '请输入内容',
              }"
            >
              <el-input
                v-model="qualitativeReviewForm.faultReasonCate"
                style="width: 100%"
                placeholder="请输入内容"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="故障国家:"
              prop="faultCountry"
              :rules="{
                required: true,
                message: '请输入内容',
              }"
            >
              <el-input
                placeholder="请输入内容"
                v-model="qualitativeReviewForm.faultCountry"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="运营商名称:"
              :rules="{
                required: true,
                message: '请输入内容',
              }"
              prop="operatorName"
            >
              <el-input
                placeholder="请输入内容"
                v-model="qualitativeReviewForm.operatorName"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="是否影响业务:"
              prop="isEffectBusiness"
              :rules="{
                required: true,
                message: '请选择是否影响业务',
              }"
            >
              <el-radio-group v-model="qualitativeReviewForm.isEffectBusiness">
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="影响范围:"
              v-if="qualitativeReviewForm.isEffectBusiness == '1'"
              prop="effectRange"
              :rules="[
                {
                  required:
                    qualitativeReviewForm.isEffectBusiness == '1'
                      ? true
                      : false,
                  message: '请填写影响范围',
                  trigger: 'blur',
                },
              ]"
            >
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="qualitativeReviewForm.effectRange"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否超时:">
              <span v-if="qualitativeReviewForm.isOverTime == '0'">否</span
              ><span v-else-if="qualitativeReviewForm.isOverTime == '1'"
                >是</span
              >
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              label="故障原因描述:"
              prop="falutReasonDesc"
              :rules="{
                required: true,
                message: '故障原因描述不能为空',
              }"
            >
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="qualitativeReviewForm.falutReasonDesc"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card
        shadow="never"
        header="定性审核信息"
        :body-style="{ padding: '20px 8px' }"
        style="margin-top: 20px"
        class="cus-card"
      >
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item
              label="审批结果:"
              prop="auditResult"
              :rules="{
                required: true,
                message: '请选择审批结果',
              }"
            >
              <el-radio-group
                v-model="qualitativeReviewForm.auditResult"
                @change="changeAuditResult()"
              >
                <el-radio label="1">同意</el-radio>
                <el-radio label="0">拒绝</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="是否上传故障报告:"
              :rules="{
                required: true,
                message: '请选择是否上传故障报告',
              }"
              prop="isUploadReport"
            >
              <el-radio-group
                v-model="qualitativeReviewForm.isUploadReport"
                @change="changeUploadReport"
                :disabled="qualitativeReviewForm.auditResult == '1'"
              >
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="是否全选入库:"
              :rules="{
                required: true,
                message: '请选择是否全选入库',
              }"
              prop="isAllStored"
            >
              <el-radio-group v-model="qualitativeReviewForm.isAllStored">
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="处理时限:">
              <el-radio-group
                v-model="qualitativeReviewForm.dealTimeLimit"
                style="width: 100%"
              >
                <el-radio
                  v-for="(item, i) in processTimeLimitOptions"
                  :key="i"
                  :label="item.dictCode"
                  >{{ item.dictName }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="第二故障源流水号:">
              <el-input
                style="width: 100%"
                v-model="qualitativeReviewForm.secondFalutSource"
                placeholder="请输入内容"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="业务影响范围:">
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="qualitativeReviewForm.busEffectRange"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障所在省:">
              <el-input
                style="width: 100%"
                placeholder="请选择内容"
                v-model="qualitativeReviewForm.faultProvince"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              label="审批意见:"
              :rules="{
                required: true,
                message: '请填写审批意见',
              }"
              prop="auditContent"
            >
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="qualitativeReviewForm.auditContent"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: center">
      <el-button
        type="primary"
        @click="handleSubmit('qualitativeReviewForm')"
        v-loading.fullscreen.lock="qualitativeReviewFullscreenLoading"
        >提交</el-button
      >
      <el-button @click="onReset">重 置</el-button>
    </div>
    <el-dialog
      width="620px"
      title="海陆缆名称"
      :visible.sync="seaLandDialogVisible"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      append-to-body
      top="5vh"
    >
      <div>
        <el-checkbox-group v-model="seaLandCheckBox" size="small">
          <el-checkbox
            v-for="(item, index) in seaLandCheckBoxArr"
            :key="index"
            :label="item.dictName"
            border
            style="width: 140px; margin-top: 10px; margin-left: 0px"
          ></el-checkbox>
        </el-checkbox-group>
      </div>
      <span slot="footer">
        <el-button type="primary" @click="seaLandCheckBoxDetermine"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import DictSelect from "../../../workOrder/components/DictSelect.vue";
import {
  apiQualitativeInternationDetail,
  apiQualitativeInternationReview,
  apiGetProvinceDict,
} from "../api/CommonApi";
import { apiDict } from "../../../workOrder/api/CommonApi";
export default {
  name: "QualitativeReviewInternation",
  props: {
    common: Object,
    workItemId: [String, Number],
  },
  components: { DictSelect },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  data() {
    return {
      qualitativeReviewForm: {
        woId: null,
        workItemId: null,
        processInstId: null,
        processDefId: null,
        //海陆缆故障
        faultStatus: null,
        networkType: null,
        faultCate: null,
        faultReasonCate: null,
        seaLandFiberName: null,
        seaLandFiberSeg: null,
        operatorName: null,
        seaLandFiberType: null,
        seaLandFiberFaultArea: null,
        isOverTime: null,
        falutReasonDesc: null,
        //设备故障
        faultCountry: null,
        isEffectBusiness: null,
        effectRange: null,
        //审核信息
        auditResult: null,
        isUploadReport: "0",
        isAllStored: null,
        dealTimeLimit: null,
        secondFalutSource: null,
        busEffectRange: null, //业务影响范围
        auditContent: null,
        faultProvince: null,
      },
      backSingleFullscreenLoading: false,
      processTimeLimitOptions: [], //处理时限
      qualitativeReviewFullscreenLoading: false,
      provinceOption: [],
      seaLandCheckBox: [],
      seaLandCheckBoxArr: [],
      seaLandDialogVisible: false,
    };
  },
  mounted() {
    this.qualitativeReviewForm.workItemId = this.workItemId;
    this.qualitativeReviewForm.woId = this.common.woId;
    this.qualitativeReviewForm.processInstId = this.common.processInstId;
    this.qualitativeReviewForm.processDefId = this.common.processDefId;
    this.getProcessTimeLimitOptions();
    this.qualitativeReviewDetail();

    this.getProvinceDict();
    this.getSeaLandOptions();
  },
  methods: {
    getSeaLandOptions() {
      let param = {
        dictTypeCode: "10057",
      };
      apiDict(param)
        .then(res => {
          if (res.code == "200") {
            this.seaLandCheckBoxArr = res?.data ?? [];
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    getProvinceDict() {
      apiGetProvinceDict()
        .then(res => {
          if (res.status == "0") {
            this.provinceOption = res?.data ?? [];
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    getProcessTimeLimitOptions() {
      let param = {
        dictTypeCode: "10051",
      };
      apiDict(param)
        .then(res => {
          if (res.code == "200") {
            this.processTimeLimitOptions = res?.data ?? [];
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    qualitativeReviewDetail() {
      let param = {
        opType: 2,
        workItemId: this.qualitativeReviewForm.workItemId,
        woId: this.qualitativeReviewForm.woId,
      };
      apiQualitativeInternationDetail(param)
        .then(res => {
          if (res.status == "0") {
            this.qualitativeReviewForm = res?.data?.rows?.[0] ?? {};
            this.$set(this.qualitativeReviewForm, "isAllStored", "1");
            this.$set(this.qualitativeReviewForm, "isUploadReport", "0");
            this.$set(
              this.qualitativeReviewForm,
              "faultProvince",
              "联通国际公司"
            );
            if (this.qualitativeReviewForm.seaLandFiberName) {
              this.seaLandCheckBox = this.qualitativeReviewForm.seaLandFiberName.split(
                ","
              );
            }
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    handleSubmit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.qualitativeReviewFullscreenLoading = true;
          this.$set(this.qualitativeReviewForm, "actionName", "定性审核");
          if (this.qualitativeReviewForm.isEffectBusiness == "0") {
            this.qualitativeReviewForm.effectRange = "";
          }
          apiQualitativeInternationReview({
            param1: JSON.stringify(this.qualitativeReviewForm),
          })
            .then(res => {
              if (res.status == "0") {
                this.$message.success("故障定性审核完成");
                this.$emit("qualitativeReviewInternationSubmit", res.data);
              } else {
                this.$message.error("故障定性审核失败");
              }
              this.qualitativeReviewFullscreenLoading = false;
            })
            .catch(error => {
              console.log(error);
              this.$message.error("故障定性审核失败");
              this.qualitativeReviewFullscreenLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    faultCateChange() {
      this.resetFaultCateChange();
    },
    resetFaultCateChange() {
      this.qualitativeReviewForm.faultReasonCate = null;
      this.qualitativeReviewForm.seaLandFiberName = null;
      this.qualitativeReviewForm.seaLandFiberSeg = null;
      this.qualitativeReviewForm.operatorName = null;
      this.qualitativeReviewForm.seaLandFiberType = null;
      this.qualitativeReviewForm.seaLandFiberFaultArea = null;
      this.qualitativeReviewForm.faultCountry = null;
      this.qualitativeReviewForm.isEffectBusiness = null;
    },
    //审批结果事件
    changeAuditResult() {
      if (this.qualitativeReviewForm.auditResult == "1") {
        this.qualitativeReviewForm.isUploadReport = "0";
        this.qualitativeReviewForm.isAllStored = "1";
        this.$set(this.qualitativeReviewForm, "auditContent", "");
      } else if (
        this.qualitativeReviewForm.isUploadReport == "1" &&
        this.qualitativeReviewForm.auditResult == "0"
      ) {
        this.$set(this.qualitativeReviewForm, "auditContent", "请上传故障报告");
      } else if (
        this.qualitativeReviewForm.isUploadReport == "0" &&
        this.qualitativeReviewForm.auditResult == "0"
      ) {
        this.$set(this.qualitativeReviewForm, "auditContent", "");
      }
    },
    changeUploadReport() {
      if (
        this.qualitativeReviewForm.isUploadReport == "1" &&
        this.qualitativeReviewForm.auditResult == "0"
      ) {
        this.$set(this.qualitativeReviewForm, "auditContent", "请上传故障报告");
      } else if (
        this.qualitativeReviewForm.isUploadReport == "0" &&
        this.qualitativeReviewForm.auditResult == "0"
      ) {
        this.$set(this.qualitativeReviewForm, "auditContent", "");
      }
    },
    selectSeaLandFiberName() {
      this.seaLandDialogVisible = true;
    },
    seaLandCheckBoxDetermine() {
      this.$set(
        this.qualitativeReviewForm,
        "seaLandFiberName",
        this.seaLandCheckBox.join(",")
      );
      this.seaLandDialogVisible = false;
    },
    onReset() {
      this.qualitativeReviewForm = {
        ...this.$options.data,
        woId: this.common.woId,
        workItemId: this.workItemId,
        processInstId: this.common.processInstId,
        processDefId: this.common.processDefId,
        faultProvince: "联通国际公司",
      };
      this.qualitativeReviewDetail();
    },
  },
};
</script>
<style lang="scss" scoped>
.qualitative-review {
  max-height: calc(90vh - 150px);
  min-height: 400px;
  margin: 0 4px;
  padding-left: 4px;
  padding-right: 8px;
  overflow-y: auto;
  .cus-card ::v-deep .el-card__header {
    padding: 11px 24px 10px;
    font-weight: 400;
    @include themify {
      background-color: themed("$--background-color-base");
    }
  }
  .fileName_style_download {
    margin-right: 3px;
    cursor: pointer;
    vertical-align: middle;
    div {
      display: inline-block;
      max-width: 120px;
      vertical-align: top;
    }
  }
  .fileName_style {
    margin-right: 3px;
    vertical-align: middle;
    div {
      display: inline-block;
      max-width: 120px;
      vertical-align: top;
    }
  }
}
</style>
