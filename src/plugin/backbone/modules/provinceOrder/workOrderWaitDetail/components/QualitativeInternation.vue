<template>
  <div class="qualitative">
    <el-form
      ref="qualitativeForm"
      :inline="false"
      class="demo-form-inline"
      :model="qualitativeForm"
      label-width="130px"
      :rules="qualitativeFormRule"
    >
      <el-card
        shadow="never"
        header="故障定性信息"
        :body-style="{ padding: '20px 8px' }"
        class="cus-card"
      >
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item
              label="故障所属专业:"
              prop="professionalType"
              :rules="{
                required: true,
                message: '请选择故障所属专业',
              }"
            >
              <dict-select
                :value.sync="qualitativeForm.professionalType"
                :dictId="10002"
                placeholder=""
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障发生时间:" prop="alarmCreateTime" required>
              {{ qualitativeForm.alarmCreateTime }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障通知时间:" prop="sheetCreateTime" required>
              {{ qualitativeForm.sheetCreateTime }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障得知时间:" prop="faultKnowTime" required>
              {{ qualitativeForm.faultKnowTime }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障代通时间:" prop="lastClearTime">
              <el-date-picker
                v-model="qualitativeForm.lastClearTime"
                type="datetime"
                placeholder="请选择故障代通时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
                style="width: 100%"
                @change="computerFaultGenerationAter"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障代通历时:">
              {{ second2Time(qualitativeForm.lastClearDuration) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障结束时间:" prop="faultEndTime">
              <el-date-picker
                v-model="qualitativeForm.faultEndTime"
                type="datetime"
                placeholder="请选择故障结束时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
                style="width: 100%"
                @change="computerFaultTreatmentTime"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障处理历时:" required>
              {{ second2Time(this.qualitativeForm.faultDuration) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="故障发生国家:"
              prop="faultHappenCountry"
              :rules="{
                required: true,
                message: '请输入内容',
              }"
            >
              <el-input
                v-model="qualitativeForm.faultHappenCountry"
                placeholder="请输入内容"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障处理部门:" required>
              {{ qualitativeForm.dept }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="处理人:" prop="person" required>
              {{ qualitativeForm.person }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="挂起历时:" required>
              {{ second2Time(this.qualitativeForm.suspendDuration) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障处理净历时:" required>
              {{ second2Time(this.qualitativeForm.processDuration) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="是否影响业务:"
              prop="isEffectBusiness"
              :rules="{
                required: true,
                message: '请选择内容',
              }"
            >
              <el-radio-group v-model="qualitativeForm.isEffectBusiness">
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item
              label="影响范围:"
              v-if="qualitativeForm.isEffectBusiness == '1'"
            >
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="qualitativeForm.effectRange"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card
        shadow="never"
        header="故障专业信息"
        :body-style="{ padding: '20px 8px' }"
        style="margin-top: 20px"
        class="cus-card"
      >
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item
              label="故障状态:"
              prop="faultStatus"
              :rules="{
                required: true,
                message: '请选择内容',
              }"
            >
              <dict-select
                :value.sync="qualitativeForm.faultStatus"
                :dictId="10017"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="网络类型:"
              prop="networkType"
              :rules="{
                required: true,
                message: '请选择网络类型',
              }"
            >
              <dict-select
                :value.sync="qualitativeForm.networkType"
                :dictId="10018"
                style="width: 100%"
                :notSelect="true"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="故障分类:"
              prop="faultCate"
              :rules="{
                required: true,
                message: '请选择故障分类',
              }"
            >
              <dict-select
                :value.sync="qualitativeForm.faultCate"
                :dictId="10055"
                style="width: 100%"
                @change="faultCateChange"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row
          type="flex"
          style="flex-wrap: wrap"
          :gutter="20"
          v-if="qualitativeForm.faultCate == '1'"
          key="faultCateOne"
        >
          <el-col :span="8">
            <el-form-item
              label="故障原因分类:"
              :rules="{
                required: true,
                message: '请输入内容',
              }"
              prop="faultReasonCate"
            >
              <el-input
                v-model="qualitativeForm.faultReasonCate"
                style="width: 100%"
                placeholder="请输入内容"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="海陆缆名称:"
              prop="seaLandFiberName"
              :rules="{
                required: true,
                message: '请输入内容',
              }"
            >
              <el-input
                v-model="qualitativeForm.seaLandFiberName"
                placeholder="请输入内容"
                style="width: 100%"
              >
                <el-button
                  style="
                    background: #b50b14;
                    border-color: #b50b14;
                    color: #fff;
                    padding: 9px 20px;
                    top: -0.8px;
                    position: relative;
                  "
                  slot="append"
                  @click="selectSeaLandFiberName"
                >
                  选择
                </el-button>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="海陆缆段:"
              prop="seaLandFiberSeg"
              :rules="{
                required: true,
                message: '请输入内容',
              }"
            >
              <el-input
                v-model="qualitativeForm.seaLandFiberSeg"
                style="width: 100%"
                placeholder="请输入内容"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="运营商名称:"
              prop="operatorName"
              :rules="{
                required: true,
                message: '请输入内容',
              }"
            >
              <el-input
                v-model="qualitativeForm.operatorName"
                style="width: 100%"
                placeholder="请输入内容"
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item
              label="海陆缆类型:"
              prop="seaLandFiberType"
              :rules="{
                required: true,
                message: '请选择内容',
              }"
            >
              <dict-select
                :value.sync="qualitativeForm.seaLandFiberType"
                :dictId="10056"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="海陆缆故障区域:">
              <el-input
                v-model="qualitativeForm.seaLandFiberFaultArea"
                style="width: 100%"
                placeholder="请输入内容"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否超时:">
              <span v-if="qualitativeForm.isOverTime == '0'">否</span
              ><span v-else-if="qualitativeForm.isOverTime == '1'">是</span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              label="故障原因描述:"
              prop="falutReasonDesc"
              :rules="{
                required: true,
                message: '故障原因描述不能为空',
              }"
            >
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="qualitativeForm.falutReasonDesc"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row
          type="flex"
          style="flex-wrap: wrap"
          :gutter="20"
          v-if="qualitativeForm.faultCate == '2'"
          key="faultCateTwo"
        >
          <el-col :span="8">
            <el-form-item
              label="故障原因分类:"
              prop="faultReasonCate"
              :rules="{
                required: true,
                message: '请输入内容',
              }"
            >
              <el-input
                v-model="qualitativeForm.faultReasonCate"
                style="width: 100%"
                placeholder="请输入内容"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="故障国家:"
              prop="faultCountry"
              :rules="{
                required: true,
                message: '请输入内容',
              }"
            >
              <el-input
                placeholder="请输入内容"
                v-model="qualitativeForm.faultCountry"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="运营商名称:"
              :rules="{
                required:
                  qualitativeForm.faultCate == '设备故障' ? true : false,
                message: '请输入内容',
              }"
              prop="operatorName"
            >
              <el-input
                placeholder="请输入内容"
                v-model="qualitativeForm.operatorName"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否超时:">
              <span v-if="qualitativeForm.isOverTime == '0'">否</span
              ><span v-else-if="qualitativeForm.isOverTime == '1'">是</span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              label="故障原因描述:"
              prop="falutReasonDesc"
              :rules="{
                required: true,
                message: '故障原因描述不能为空',
              }"
            >
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="qualitativeForm.falutReasonDesc"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: center">
      <el-button
        type="primary"
        @click="handleSubmit('qualitativeForm')"
        v-loading.fullscreen.lock="qualitativeFullscreenLoading"
        >提交</el-button
      >
      <el-button @click="onReset">重 置</el-button>
    </div>
    <el-dialog
      width="620px"
      title="海陆缆名称"
      :visible.sync="seaLandDialogVisible"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      append-to-body
      top="5vh"
    >
      <div>
        <el-checkbox-group v-model="seaLandCheckBox" size="small">
          <el-checkbox
            v-for="(item, index) in seaLandCheckBoxArr"
            :key="index"
            :label="item.dictName"
            border
            style="width: 140px; margin-top: 10px; margin-left: 0px"
          ></el-checkbox>
        </el-checkbox-group>
      </div>
      <span slot="footer">
        <el-button type="primary" @click="seaLandCheckBoxDetermine"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
<script>
import moment from "moment";
import { mapGetters } from "vuex";
import DictSelect from "../../../workOrder/components/DictSelect.vue";
import {
  apiQualitativeInternationDetail,
  apiQualitativeInternation,
} from "../api/CommonApi";
import { apiDict } from "../../../workOrder/api/CommonApi";

export default {
  name: "QualitativeInternation",
  props: {
    common: Object,
    workItemId: [String, Number],
  },
  components: { DictSelect },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  data() {
    var validLastClearTime = (rule, value, callback) => {
      if (
        this.qualitativeForm.lastClearTime &&
        this.qualitativeForm.faultEndTime
      ) {
        let seconds = moment(
          this.qualitativeForm.faultEndTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.qualitativeForm.lastClearTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );

        let seconds2 = moment(
          this.qualitativeForm.lastClearTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.common.failureInformTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        if (seconds < 0 || seconds2 <= 0) {
          callback(
            new Error(
              "故障结束时间>=故障代通时间>故障通知时间，请重新检查后选择正确时间"
            )
          );
        } else {
          callback();
        }
      } else if (this.qualitativeForm.lastClearTime) {
        let seconds3 = moment(
          this.qualitativeForm.lastClearTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(moment(new Date(), "YYYY-MM-DD HH:mm:ss"), "seconds");
        let seconds4 = moment(
          this.qualitativeForm.lastClearTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.common.failureInformTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        if (seconds3 > 0 || seconds4 <= 0) {
          callback(
            new Error(
              "当前时间>=故障代通时间>故障通知时间，请重新检查后选择正确时间"
            )
          );
        }
      } else {
        callback();
      }
    };
    var validFaultEndTime = (rule, value, callback) => {
      if (value === "" || value === null) {
        callback(new Error("请选择故障结束时间"));
      } else {
        if (this.qualitativeForm.lastClearTime) {
          let clSeconds = moment(
            this.qualitativeForm.faultEndTime,
            "YYYY-MM-DD HH:mm:ss"
          ).diff(
            moment(this.qualitativeForm.lastClearTime, "YYYY-MM-DD HH:mm:ss"),
            "seconds"
          );
          if (clSeconds < 0) {
            callback(
              new Error(
                "当前时间>=故障结束时间>=故障代通时间，请重新检查后选择正确时间"
              )
            );
          }
        }

        let seconds = moment(
          this.qualitativeForm.faultEndTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.common.failureInformTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );

        let seconds2 = moment(new Date(), "YYYY-MM-DD HH:mm:ss").diff(
          moment(this.qualitativeForm.faultEndTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        if (seconds <= 0 || seconds2 < 0) {
          callback(
            new Error(
              "当前时间>=故障结束时间>故障通知时间，请重新检查后选择正确时间"
            )
          );
        } else {
          callback();
        }
      }
    };
    return {
      qualitativeForm: {
        woId: null,
        workItemId: null,
        processInstId: null,
        processDefId: null,
        professionalType: null,
        alarmCreateTime: null,
        sheetCreateTime: null,
        faultKnowTime: null,
        lastClearTime: null,
        lastClearDuration: 0, //故障代通历时(单位秒)
        faultEndTime: null,
        faultDuration: 0, //故障处理历时(单位秒)
        faultHappenCountry: null,
        dept: null,
        person: null,
        suspendDuration: 0, //挂起历时
        processDuration: null, //故障处理净历时
        isEffectBusiness: null,
        effectRange: null,
        //海陆缆故障
        faultStatus: null,
        networkType: null,
        faultCate: null,
        faultReasonCate: null,
        seaLandFiberName: null,
        seaLandFiberSeg: null,
        operatorName: null,
        seaLandFiberType: null,
        seaLandFiberFaultArea: null,
        isOverTime: null,
        falutReasonDesc: null,
        //设备故障
        faultCountry: null,
      },
      qualitativeFullscreenLoading: false,
      userData: null,

      qualitativeFormRule: {
        lastClearTime: [{ validator: validLastClearTime, trigger: "blur" }],
        faultEndTime: [{ validator: validFaultEndTime, required: true }],
      },
      //海陆缆名称
      seaLandCheckBox: [],
      seaLandCheckBoxArr: [],
      seaLandDialogVisible: false,
    };
  },
  mounted() {
    this.qualitativeForm.alarmCreateTime = this.common.failureTime;
    this.qualitativeForm.sheetCreateTime = this.common.failureInformTime;
    this.qualitativeForm.person = this.userInfo.realName;
    this.qualitativeForm.workItemId = this.workItemId;
    this.qualitativeForm.woId = this.common.woId;
    this.qualitativeForm.processInstId = this.common.processInstId;
    this.qualitativeForm.processDefId = this.common.processDefId;
    this.qualitativeDetail();
    this.userData = JSON.parse(this.userInfo.attr2);
    this.getSeaLandOptions();
  },
  methods: {
    getSeaLandOptions() {
      let param = {
        dictTypeCode: "10057",
      };
      apiDict(param)
        .then(res => {
          if (res.code == "200") {
            this.seaLandCheckBoxArr = res?.data ?? [];
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    qualitativeDetail() {
      let param = {
        opType: 1,
        workItemId: this.qualitativeForm.workItemId,
        woId: this.qualitativeForm.woId,
      };
      apiQualitativeInternationDetail(param)
        .then(res => {
          if (res.status == "0") {
            this.qualitativeForm = res?.data?.rows?.[0] ?? {};
            if (this.qualitativeForm.seaLandFiberName) {
              this.seaLandCheckBox = this.qualitativeForm.seaLandFiberName.split(
                ","
              );
            }
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    computerFaultGenerationAter() {
      let days = moment(
        this.qualitativeForm.lastClearTime,
        "YYYY-MM-DD HH:mm:ss"
      ).diff(
        moment(this.qualitativeForm.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
        "seconds"
      );
      this.qualitativeForm.lastClearDuration = days;
    },
    second2Time(days) {
      return this.showTimeNew(Math.abs(days));
    },
    computerFaultTreatmentTime() {
      let days = moment(
        this.qualitativeForm.faultEndTime,
        "YYYY-MM-DD HH:mm:ss"
      ).diff(
        moment(this.qualitativeForm.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
        "seconds"
      );
      this.qualitativeForm.faultDuration = days;

      //故障处理净历时  （故障结束时间-故障发生时间）-挂起历时(秒) 换算成-天-小时-分钟-秒
      if (this.qualitativeForm.suspendDuration == 0) {
        this.qualitativeForm.processDuration = this.qualitativeForm.faultDuration;
      } else {
        let seconds = moment(
          this.qualitativeForm.faultEndTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.qualitativeForm.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        this.qualitativeForm.processDuration =
          seconds - this.qualitativeForm.suspendDuration;
      }
    },
    handleSubmit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.qualitativeFullscreenLoading = true;
          // let formData = new FormData();
          // formData.append("jsonParam", JSON.stringify(this.qualitativeForm));
          this.$set(this.qualitativeForm, "actionName", "定性");
          apiQualitativeInternation({
            param1: JSON.stringify(this.qualitativeForm),
          })
            .then(res => {
              if (res.status == "0") {
                this.$message.success("提交定性成功");
                //   this.onReset();
                this.$emit("qualitativeInternationSubmit", res.data);
              } else {
                this.$message.error("提交定性失败");
              }
              this.qualitativeFullscreenLoading = false;
            })
            .catch(error => {
              console.log(error);
              this.$message.error("提交定性失败");
              this.qualitativeFullscreenLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    selectSeaLandFiberName() {
      this.seaLandDialogVisible = true;
    },
    seaLandCheckBoxDetermine() {
      this.$set(
        this.qualitativeForm,
        "seaLandFiberName",
        this.seaLandCheckBox.join(",")
      );
      this.seaLandDialogVisible = false;
    },
    faultCateChange(type) {
      this.resetFaultCateChange();
    },
    resetFaultCateChange() {
      this.qualitativeForm.faultReasonCate = null;
      this.qualitativeForm.seaLandFiberName = null;
      this.qualitativeForm.seaLandFiberSeg = null;
      this.qualitativeForm.operatorName = null;
      this.qualitativeForm.seaLandFiberType = null;
      this.qualitativeForm.seaLandFiberFaultArea = null;
      this.qualitativeForm.faultCountry = null;
    },
    onReset() {
      this.qualitativeForm = {
        ...this.$options.data,
        alarmCreateTime: this.common.failureTime,
        sheetCreateTime: this.common.failureInformTime,
        person: this.userInfo.realName,
        workItemId: this.workItemId,
        woId: this.common.woId,
        dept: this.userInfo.deptId,
        processInstId: this.common.processInstId,
        processDefId: this.common.processDefId,
      };
      this.qualitativeDetail();
    },
    showTimeNew(val) {
      if (val) {
        if (val == 0) return "0秒";
        var time = "";
        var second = val % 60;
        var minute = parseInt(parseInt(val) / 60) % 60;
        time = second + "秒";
        if (minute >= 1) {
          time = minute + "分" + second + "秒";
        }
        var hour = parseInt(parseInt(val / 60) / 60) % 24;
        if (hour >= 1) {
          time = hour + "小时" + minute + "分" + second + "秒";
        }
        var day = parseInt(parseInt(parseInt(val / 60) / 60) / 24);
        if (day >= 1) {
          time = day + "天" + hour + "小时" + minute + "分" + second + "秒";
        }
        return time;
      } else {
        return "0秒";
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.qualitative {
  max-height: calc(90vh - 150px);
  min-height: 400px;
  margin: 0 4px;
  padding-left: 4px;
  padding-right: 8px;
  overflow-y: auto;
  .cus-card ::v-deep .el-card__header {
    padding: 11px 24px 10px;
    font-weight: 400;
    @include themify {
      background-color: themed("$--background-color-base");
    }
  }
  .fileName_style_download {
    margin-right: 3px;
    cursor: pointer;
    vertical-align: middle;
    div {
      display: inline-block;
      max-width: 120px;
      vertical-align: top;
    }
  }
  .fileName_style {
    margin-right: 3px;
    vertical-align: middle;
    div {
      display: inline-block;
      max-width: 120px;
      vertical-align: top;
    }
  }
}
</style>
