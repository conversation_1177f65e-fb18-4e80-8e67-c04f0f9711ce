<template>
  <div class="afterSingle">
    <el-form ref="resourceForm" :model="resourceForm" label-width="150px">
      <el-row type="flex" style="flex-wrap: wrap" :gutter="20" key="tieta">
        <template
          v-if="resourceType == 'EUtranCell' || resourceType == 'NrCellDU'"
        >
          <el-col :span="8">
            <el-form-item label="小区覆盖类型:">
              <el-input v-model="cellCoverType" disabled> </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item
              label="是否变更:"
              prop="isCellCoverTypeChange"
              :rules="{
                required: true,
                message: '请选择',
              }"
            >
              <el-radio-group
                v-model="resourceForm.isCellCoverTypeChange"
                @change="
                  clearValidateForItem(
                    resourceForm.isCellCoverTypeChange,
                    'cellCoverTypeChanged'
                  )
                "
              >
                <el-radio label="是">是</el-radio>
                <el-radio label="否">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item
              label-width="180px"
              label="小区覆盖类型变更后:"
              prop="cellCoverTypeChanged"
              :rules="{
                required:
                  resourceForm.isCellCoverTypeChange == '是' ? true : false,
                message: '请输入',
              }"
            >
              <dict-select
                :notSelect="resourceForm.isCellCoverTypeChange == '否'"
                :value.sync="resourceForm.cellCoverTypeChanged"
                :dictId="86002"
                placeholder="请选择"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所属行政区域类型:">
              <el-input v-model="regionsType" disabled> </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item
              label="是否变更:"
              prop="isRegionsTypeChange"
              :rules="{
                required: true,
                message: '请选择',
              }"
            >
              <el-radio-group
                v-model="resourceForm.isRegionsTypeChange"
                @change="
                  clearValidateForItem(
                    resourceForm.isRegionsTypeChange,
                    'regionsTypeChanged'
                  )
                "
              >
                <el-radio label="是">是</el-radio>
                <el-radio label="否">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item
              label-width="180px"
              label="所属行政区域类型变更后:"
              prop="regionsTypeChanged"
              :rules="{
                required:
                  resourceForm.isRegionsTypeChange == '是' ? true : false,
                message: '请输入',
              }"
            >
              <dict-select
                :notSelect="resourceForm.isRegionsTypeChange == '否'"
                :value.sync="resourceForm.regionsTypeChanged"
                :dictId="86001"
                placeholder="请选择"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </template>
        <el-col :span="8">
          <el-form-item>
            <div slot="label" style="display: inline-block">
              <span @click="setCUShow(true)">
                <em
                  class="el-icon-arrow-down"
                  v-if="
                    resourceType == 'GNodeB' &&
                    resourceForm.isSitenoChange == '是' &&
                    !showCU
                  "
                ></em>
              </span>
              <span @click="setCUShow(false)">
                <em
                  class="el-icon-arrow-up"
                  v-if="
                    resourceType == 'GNodeB' &&
                    resourceForm.isSitenoChange == '是' &&
                    showCU
                  "
                ></em>
              </span>
              {{ jfName }}
            </div>
            <el-input v-model="siteno" disabled> </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item
            label="是否变更:"
            prop="isSitenoChange"
            :rules="{
              required: true,
              message: '请选择',
            }"
          >
            <el-radio-group
              v-model="resourceForm.isSitenoChange"
              @change="
                clearValidateForItem(
                  resourceForm.isSitenoChange,
                  'sitenoChanged'
                )
              "
            >
              <el-radio label="是">是</el-radio>
              <el-radio label="否">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="9">
          <el-form-item
            label-width="180px"
            :label="jfChangedName"
            prop="sitenoChanged"
            :rules="{
              required: resourceForm.isSitenoChange == '是' ? true : false,
              message: '请输入',
            }"
          >
            <el-input
              :disabled="resourceForm.isSitenoChange == '否'"
              v-model="resourceForm.sitenoChanged"
              style="width: 100%"
              placeholder="请输入变更后的机房"
              maxlength="255"
              oninput="value=value.replace(/\s*/g,'')"
              @blur="resourceForm.sitenoChanged = $event.target.value"
              @keyup.native="descTip(255, 'sitenoChanged', 'showSiteno')"
            ></el-input>
            <div class="el-form-item__error" v-if="showSiteno">
              已超过填写字数上限
            </div>
          </el-form-item>
        </el-col>
        <template
          v-if="
            resourceType == 'GNodeB' &&
            resourceForm.isSitenoChange == '是' &&
            showCU
          "
        >
          <el-col :span="8">
            <el-form-item label="CU所属机房:">
              <el-input disabled> </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item
              label="是否变更:"
              prop="isSitenoChange"
              :rules="{
                required: true,
                message: '请选择',
              }"
            >
              <el-radio-group disabled v-model="resourceForm.isSitenoChange">
                <el-radio label="是">是</el-radio>
                <el-radio label="否">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item
              label-width="180px"
              label="CU所属机房变更后:"
              prop="sitenoChanged"
            >
              <el-input
                v-model="resourceForm.sitenoChanged"
                style="width: 100%"
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
        </template>
        <template v-if="resourceInitData.location">
          <el-col :span="8">
            <el-form-item>
              <div slot="label" style="display: inline-block">
                <span @click="setLonShow(true)">
                  <em
                    class="el-icon-arrow-down"
                    v-if="
                      resourceType == 'EUtranCell' &&
                      resourceForm.isJFLonChange == '是' &&
                      !showLon
                    "
                  ></em>
                </span>
                <span @click="setLonShow(false)">
                  <em
                    class="el-icon-arrow-up"
                    v-if="
                      resourceType == 'EUtranCell' &&
                      resourceForm.isJFLonChange == '是' &&
                      showLon
                    "
                  ></em>
                </span>
                {{ jfLonName }}
              </div>
              <el-input v-model="jfLon" disabled> </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item
              label="是否变更:"
              prop="isJFLonChange"
              :rules="{
                required: true,
                message: '请选择',
              }"
            >
              <el-radio-group
                v-model="resourceForm.isJFLonChange"
                :disabled="resourceForm.isSitenoChange == '是'"
                @change="
                  clearValidateForItem(
                    resourceForm.isJFLonChange,
                    'jfLonChanged'
                  )
                "
              >
                <el-radio label="是">是</el-radio>
                <el-radio label="否">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item
              label-width="180px"
              :label="jfLonChangedName"
              prop="jfLonChanged"
              :rules="[
                { validator: validateLongitude, trigger: 'change' },
                { validator: validateLongitude, trigger: 'blur' },
                {
                  required: resourceForm.isJFLonChange == '是' ? true : false,
                  message: '请输入',
                },
              ]"
            >
              <el-input
                :disabled="resourceForm.isJFLonChange == '否'"
                v-model="resourceForm.jfLonChanged"
                style="width: 100%"
                placeholder="请输入变更后的放置点经度"
                maxlength="255"
                oninput="value=value.replace(/\s*/g,'')"
                @blur="resourceForm.jfLonChanged = $event.target.value"
                @keyup.native="descTip(255, 'jfLonChanged', 'showJfLon')"
              ></el-input>
              <div class="el-form-item__error" v-if="showJfLon">
                已超过填写字数上限
              </div>
            </el-form-item>
          </el-col>
        </template>
        <template
          v-if="
            resourceType == 'EUtranCell' &&
            resourceForm.isJFLonChange == '是' &&
            showLon
          "
        >
          <el-col :span="8">
            <el-form-item label="小区经度:">
              <el-input disabled> </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item
              label="是否变更:"
              prop="isJFLonChange"
              :rules="{
                required: true,
                message: '请选择',
              }"
            >
              <el-radio-group disabled v-model="resourceForm.isJFLonChange">
                <el-radio label="是">是</el-radio>
                <el-radio label="否">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item
              label-width="180px"
              label="小区经度变更后:"
              prop="jfLonChanged"
              :rules="{
                required: true,
                message: '请输入',
              }"
            >
              <el-input disabled v-model="resourceForm.jfLonChanged"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="RRU经度:">
              <el-input disabled> </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item
              label="是否变更:"
              prop="isJFLonChange"
              :rules="{
                required: true,
                message: '请选择',
              }"
            >
              <el-radio-group disabled v-model="resourceForm.isJFLonChange">
                <el-radio label="是">是</el-radio>
                <el-radio label="否">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item
              label-width="180px"
              label="RRU经度变更后:"
              prop="jfLonChanged"
              :rules="{
                required: true,
                message: '请输入',
              }"
            >
              <el-input disabled v-model="resourceForm.jfLonChanged"></el-input>
            </el-form-item>
          </el-col>
        </template>
        <template v-if="resourceInitData.location">
          <el-col :span="8">
            <el-form-item>
              <div slot="label" style="display: inline-block">
                <span @click="setLatShow(true)">
                  <em
                    class="el-icon-arrow-down"
                    v-if="
                      resourceType == 'EUtranCell' &&
                      resourceForm.isJFLatChange == '是' &&
                      !showLat
                    "
                  ></em>
                </span>
                <span @click="setLatShow(false)">
                  <em
                    class="el-icon-arrow-up"
                    v-if="
                      resourceType == 'EUtranCell' &&
                      resourceForm.isJFLatChange == '是' &&
                      showLat
                    "
                  ></em>
                </span>
                {{ jfLatName }}
              </div>
              <el-input v-model="jfLat" disabled> </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item
              label="是否变更:"
              prop="isJFLatChange"
              :rules="{
                required: true,
                message: '请选择',
              }"
            >
              <el-radio-group
                v-model="resourceForm.isJFLatChange"
                :disabled="resourceForm.isSitenoChange == '是'"
                @change="
                  clearValidateForItem(
                    resourceForm.isJFLatChange,
                    'jfLatChanged'
                  )
                "
              >
                <el-radio label="是">是</el-radio>
                <el-radio label="否">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item
              label-width="180px"
              :label="jfLatChangedName"
              prop="jfLatChanged"
              :rules="[
                { validator: validateLatitude, trigger: 'change' },
                { validator: validateLatitude, trigger: 'blur' },
                {
                  required: resourceForm.isJFLatChange == '是' ? true : false,
                  message: '请输入',
                },
              ]"
            >
              <el-input
                :disabled="resourceForm.isJFLatChange == '否'"
                v-model="resourceForm.jfLatChanged"
                style="width: 100%"
                placeholder="请输入变更后的放置点纬度"
                maxlength="255"
                oninput="value=value.replace(/\s*/g,'')"
                @blur="resourceForm.jfLatChanged = $event.target.value"
                @keyup.native="descTip(255, 'jfLatChanged', 'showJfLat')"
              ></el-input>
              <div class="el-form-item__error" v-if="showJfLat">
                已超过填写字数上限
              </div>
            </el-form-item>
          </el-col>
        </template>
        <template
          v-if="
            resourceType == 'EUtranCell' &&
            resourceForm.isJFLatChange == '是' &&
            showLat
          "
        >
          <el-col :span="8">
            <el-form-item label="小区纬度:">
              <el-input disabled> </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item
              label="是否变更:"
              prop="isJFLatChange"
              :rules="{
                required: true,
                message: '请选择',
              }"
            >
              <el-radio-group disabled v-model="resourceForm.isJFLatChange">
                <el-radio label="是">是</el-radio>
                <el-radio label="否">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item
              label-width="180px"
              label="小区纬度变更后:"
              prop="jfLatChanged"
              :rules="{
                required: true,
                message: '请输入',
              }"
            >
              <el-input v-model="resourceForm.jfLatChanged" disabled></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="RRU纬度:">
              <el-input disabled> </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item
              label="是否变更:"
              prop="isJFLatChange"
              :rules="{
                required: true,
                message: '请选择',
              }"
            >
              <el-radio-group disabled v-model="resourceForm.isJFLatChange">
                <el-radio label="是">是</el-radio>
                <el-radio label="否">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="9">
            <el-form-item
              label-width="180px"
              label="RRU纬度变更后:"
              prop="jfLatChanged"
              :rules="{
                required: true,
                message: '请输入',
              }"
            >
              <el-input v-model="resourceForm.jfLatChanged" disabled></el-input>
            </el-form-item>
          </el-col>
        </template>
        <el-col :span="15">
          <el-form-item label="所属行政区域:">
            <el-tooltip
              popper-class="toolCla"
              class="item"
              effect="dark"
              :content="districtName"
              placement="top-start"
            >
              <el-input v-model="districtName" disabled> </el-input>
            </el-tooltip>
          </el-form-item>
        </el-col>
        <el-col :span="9">
          <el-form-item
            label-width="180px"
            label="是否变更:"
            prop="isDistrictNameChange"
            :rules="{
              required: true,
              message: '请选择',
            }"
          >
            <el-radio-group
              v-model="resourceForm.isDistrictNameChange"
              :disabled="resourceForm.isSitenoChange == '是'"
              @change="clearValidateForRegion"
            >
              <el-radio label="是">是</el-radio>
              <el-radio label="否">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="15">
          <el-form-item
            label="所属行政区域变更后:"
            prop=""
            :rules="{
              required:
                resourceForm.isDistrictNameChange == '是' ? true : false,
              message: '请选择',
            }"
          >
            <el-row>
              <el-col :span="6">
                <el-form-item
                  label-width="0"
                  label=""
                  prop=""
                  style="margin-bottom: 0px"
                >
                  <el-select
                    class="selectCusCla"
                    v-model="resourceForm.provinceChanged"
                    disabled
                  ></el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6" style="padding-left: 5px">
                <el-form-item
                  label-width="0"
                  label=""
                  style="margin-bottom: 0px"
                  prop="cityChanged"
                  :rules="{
                    required:
                      resourceForm.isDistrictNameChange == '是' ? true : false,
                    message: '请选择',
                  }"
                >
                  <el-select
                    :disabled="resourceForm.isDistrictNameChange == '否'"
                    class="selectCusCla"
                    v-model="resourceForm.cityChanged"
                    clearable
                    placeholder="请选择"
                    @change="handleCityChange"
                  >
                    <el-option
                      v-for="city in cities"
                      :key="city.id"
                      :label="city.name"
                      :value="city.name"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6" style="padding-left: 5px">
                <el-form-item
                  label-width="0"
                  label=""
                  style="margin-bottom: 0px"
                  prop="areaChanged"
                  :rules="{
                    required:
                      resourceForm.isDistrictNameChange == '是' ? true : false,
                    message: '请选择',
                  }"
                >
                  <el-select
                    :disabled="resourceForm.isDistrictNameChange == '否'"
                    class="selectCusCla"
                    v-model="resourceForm.areaChanged"
                    clearable
                    placeholder="请选择"
                    @change="handleAreaChange"
                  >
                    <el-option
                      v-for="area in areas"
                      :key="area.id"
                      :label="area.name"
                      :value="area.name"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6" style="padding-left: 5px">
                <el-form-item
                  label-width="0"
                  label=""
                  style="margin-bottom: 0px"
                  prop="townChanged"
                  :rules="{
                    required:
                      resourceForm.isDistrictNameChange == '是' ? true : false,
                    message: '请选择',
                  }"
                >
                  <el-select
                    :disabled="resourceForm.isDistrictNameChange == '否'"
                    class="selectCusCla"
                    v-model="resourceForm.townChanged"
                    clearable
                    placeholder="请选择"
                    @change="handleTownChange"
                  >
                    <el-option
                      v-for="area in towns"
                      :key="area.id"
                      :label="area.name"
                      :value="area.name"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注信息:" prop="remarks">
            <el-input
              show-word-limit
              placeholder="请输入备注信息"
              maxlength="255"
              @keyup.native="descTip(255, 'remarks', 'showNote')"
              type="textarea"
              :rows="2"
              v-model="resourceForm.remarks"
              style="width: 100%"
              oninput="value=value.replace(/\s*/g,'')"
              @blur="resourceForm.remarks = $event.target.value"
            >
            </el-input>
            <div class="el-form-item__error" v-if="showNote">
              已超过填写字数上限
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item>
        <span slot="label" style="display: inline-block">
          <el-tooltip
            popper-class="my-popper"
            effect="dark"
            content="最多支持上传三张照片"
            placement="bottom"
          >
            <em class="el-icon-question" style="color: #b50b14"></em>
          </el-tooltip>
          照片:
        </span>
        <div style="width: 400px">
          <el-tag
            class="fileName_style"
            readonly
            v-for="(item, index) in importForm.attachmentFileList"
            :key="index"
            @close="close(item)"
            :title="item.name"
            ><div class="text-truncate">{{ item.name }}</div></el-tag
          >
          <el-button size="mini" type="primary" @click="attachmentBrowse"
            >+上传照片</el-button
          >
        </div>
      </el-form-item>
    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: right">
      <el-button
        type="primary"
        @click="handleSubmit('resourceForm')"
        v-loading.fullscreen.lock="afterSingleFullscreenLoading"
        >提 交</el-button
      >
      <el-button @click="onResetResourceForm">重 置</el-button>
    </div>
    <el-dialog
      width="420px"
      title="照片选择"
      :visible.sync="attachmentDialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <file-upload
        @change="changeFileData"
        @cancel="closeAttachmentDialog"
      ></file-upload>
    </el-dialog>
  </div>
</template>
<script>
import {
  apicorrectionSubmit,
  apiResourceInitUrl,
  apiCorrectionGetRegion,
} from "../api/CommonApi";
import FileUpload from "./ImageUpload";
import { mapGetters } from "vuex";
import DictSelect from "../../../workOrder/components/DictSelect.vue";

export default {
  name: "AfterSingle",
  props: {
    common: Object,
    // actionName:String,
    // mainProfessionalType:String,
  },
  components: { FileUpload, DictSelect },
  data() {
    return {
      showSiteno: false,
      showJfLon: false,
      showJfLat: false,
      showNote: false,
      showTime: 5000,
      showCU: true,
      showLon: true,
      showLat: true,
      isDiaOrgsUserTree: false,
      userAttribution: "cpResource",
      resourceInitData: {},
      resourceType: "",
      jfName: "",
      jfChangedName: "",
      siteno: "", //BBU所属机房、DU所属机房、AAU所属机房
      jfLonName: "",
      jfLonChangedName: "",
      jfLatName: "",
      jfLatChangedName: "",
      jfLon: "", //机房/室外放置点经度
      jfLat: "", //机房/室外放置点纬度
      districtName: "", //所属行政区域
      cellCoverType: "", //小区覆盖类型
      regionsType: "", //所属行政区域类型
      resourceForm: {
        isSitenoChange: "否",
        sitenoChanged: "",
        isJFLonChange: "否", //经度
        jfLonChanged: "",
        isJFLatChange: "否", //纬度
        jfLatChanged: "",
        isDistrictNameChange: "否",
        provinceChanged: "",
        cityChanged: "",
        areaChanged: "",
        townChanged: "",
        isCellCoverTypeChange: "否",
        cellCoverTypeChanged: "",
        isRegionsTypeChange: "否",
        regionsTypeChanged: "",
        remarks: "",
      },
      updDistrictId: "", //更爱后的区域id
      updDistrictName: "", //更爱后的区域地址
      cities: [],
      areas: [],
      towns: [],
      afterSingleFullscreenLoading: false,
      attachmentDialogVisible: false,
      importForm: {
        //附件
        attachmentFileList: [],
      },

      rawAgentManIdArr: [],
      rawAgenManDeptCodeArr: [],
    };
  },
  async created() {
    this.resourceInit();
  },
  mounted() {},

  computed: {
    ...mapGetters(["userInfo"]),
  },
  watch: {},
  methods: {
    validateLongitude(rule, value, callback) {
      //经度,整数部分为0-180小数部分为0到15位
      value = value.toString().replace(/\.?0+$/, "");
      if (
        this.resourceForm.isJFLonChange == "是" &&
        (value <= 73.4 || value >= 135.1)
      ) {
        callback(new Error("经度范围【73.4,135.1】,小数部分4到10位"));
      }
      var longreg = /^[\-\+]?(0?\d{1,2}\.\d{4,10}|1[0-7]?\d{1}\.\d{4,10}|180\.0{4,10})$/;
      if (this.resourceForm.isJFLonChange == "是" && !longreg.test(value)) {
        callback(new Error("经度范围【73.4,135.1】,小数部分4到10位"));
      }
      callback();
    },
    validateLatitude(rule, value, callback) {
      //纬度,整数部分为0-90小数部分为0到15位
      // value = parseFloat(value);
      value = value.toString().replace(/\.?0+$/, "");
      if (
        this.resourceForm.isJFLatChange == "是" &&
        (value <= 3.8 || value >= 53.6)
      ) {
        callback(new Error("纬度范围【3.8,53.6】,小数部分4到10位"));
      }
      var latreg = /^[\-\+]?([0-8]?\d{1}\.\d{4,10}|90\.0{4,10})$/;
      if (this.resourceForm.isJFLatChange == "是" && !latreg.test(value)) {
        callback(new Error("纬度范围【3.8,53.6】,小数部分4到10位"));
      }
      callback();
    },
    descTip(count, name, showName) {
      if (this.resourceForm[name].length >= count) {
        this[showName] = true;
        setTimeout(() => {
          this[showName] = false;
        }, this.showTime);
      } else {
        this[showName] = false;
      }
    },
    clearValidateForItem(val, itemName) {
      if (val == "否") {
        this.$refs.resourceForm.clearValidate([itemName]);
      }
      //当AAU所属机房、DU所属机房、RRU所属机房、BBU所属机房选择变更时，所属行政区域及机房放置点经纬度需置灰且不可修改
      if (itemName == "sitenoChanged" && val == "是") {
        this.resourceForm.isDistrictNameChange = "否";
        this.resourceForm.isJFLatChange = "否";
        this.resourceForm.isJFLonChange = "否";
      }
    },
    clearValidateForRegion(val) {
      if (val == "否") {
        this.$refs.resourceForm.clearValidate(["cityChanged"]);
        this.$refs.resourceForm.clearValidate(["areaChanged"]);
        this.$refs.resourceForm.clearValidate(["townChanged"]);
      }
    },
    //资源勘误初始化
    resourceInit() {
      let param = {
        woId: this.common.woId,
      };
      apiResourceInitUrl(param)
        .then(res => {
          if (res.status == "0") {
            this.resourceInitData = res.data;
            //this.resourceInitData.location是否支持修改经纬度
            this.resourceType = res.data.locateNeClass;
            if (this.resourceType == "ENodeB") {
              this.jfName = "BBU所属机房:";
              this.jfChangedName = "BBU所属机房变更后:";
            } else if (this.resourceType == "GNodeB") {
              this.jfName = "DU所属机房:";
              this.jfChangedName = "DU所属机房变更后:";
            } else if (this.resourceType == "EUtranCell") {
              this.jfName = "RRU所属机房:";
              this.jfChangedName = "RRU所属机房变更后:";
            } else if (this.resourceType == "NrCellDU") {
              this.jfName = "AAU所属机房:";
              this.jfChangedName = "AAU所属机房变更后:";
            }

            if (res.data.positType == "2080001") {
              this.jfLonName = "机房放置点经度:";
              this.jfLonChangedName = "机房放置点经度变更后:";
              this.jfLatName = "机房放置点纬度:";
              this.jfLatChangedName = "机房放置点纬度变更后:";
            } else if (res.data.positType == "2080004") {
              this.jfLonName = "室外放置点经度:";
              this.jfLonChangedName = "室外放置点经度变更后:";
              this.jfLatName = "室外放置点纬度:";
              this.jfLatChangedName = "室外放置点纬度变更后:";
            }
            this.$forceUpdate();

            this.siteno = res.data.roomName; //BBU所属机房、DU所属机房、AAU所属机房
            this.jfLon = res.data.longitude; //机房/室外放置点经度
            this.jfLat = res.data.latitude; //机房/室外放置点纬度
            this.districtName = res.data.districtName; //所属行政区域
            this.cellCoverType = res.data.cellCoverTypeName; //小区覆盖类型
            this.regionsType = res.data.regionsTypeName; //所属行政区域类型
            this.resourceForm.provinceChanged = res.data.privinceName; //省

            //获取区域枚举
            this.getRegionById(res.data.regionId);
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    //获取区域
    getRegionById(id) {
      let param = {
        id: id,
      };
      apiCorrectionGetRegion(param)
        .then(res => {
          if (res.status == "0") {
            this.cities = res.data;
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    setCUShow(val) {
      this.showCU = val;
    },
    setLonShow(val) {
      this.showLon = val;
    },
    setLatShow(val) {
      this.showLat = val;
    },
    handleCityChange(val) {
      console.log(val);
      let ids = this.cities.filter(item => item.name == val);

      // this.resourceForm.cityChanged = val.name;
      this.resourceForm.areaChanged = "";
      this.resourceForm.townChanged = "";
      this.updDistrictName = "";
      this.updDistrictId = "";

      let param = {
        id: ids[0].id,
      };
      apiCorrectionGetRegion(param)
        .then(res => {
          if (res.status == "0") {
            this.areas = res.data;
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    handleAreaChange(val) {
      let ids = this.areas.filter(item => item.name == val);
      // this.resourceForm.areaChanged = val.name;
      this.resourceForm.townChanged = "";
      this.updDistrictName = "";
      this.updDistrictId = "";

      // 根据地区value筛选出对应乡镇列表
      let param = {
        id: ids[0].id,
      };
      apiCorrectionGetRegion(param)
        .then(res => {
          if (res.status == "0") {
            this.towns = res.data;
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    handleTownChange(val) {
      let ids = this.towns.filter(item => item.name == val);
      this.resourceForm.townChanged = ids[0].name;
      this.updDistrictName = ids[0].fullName;
      this.updDistrictId = ids[0].id;
    },

    handleSubmit(formName) {
      if (
        this.resourceForm.isSitenoChange == "否" &&
        this.resourceForm.isJFLonChange == "否" &&
        this.resourceForm.isJFLatChange == "否" &&
        this.resourceForm.isDistrictNameChange == "否" &&
        this.resourceForm.isCellCoverTypeChange == "否" &&
        this.resourceForm.isRegionsTypeChange == "否"
      ) {
        this.$message.error("不存在需要变更的资源，请确认后再提交");
        return false;
      }

      this.$refs[formName].validate(valid => {
        if (valid) {
          let self = this;
          this.afterSingleFullscreenLoading = true;
          // 如果有附件
          let formData = new FormData();
          if (self.importForm.attachmentFileList.length > 0) {
            for (let item of self.importForm.attachmentFileList) {
              formData.append("files", item.raw);
            }
          } else {
            formData.append("files", "");
          }

          let param = {
            woId: this.common.woId,
            processInstId: this.common.processInstId,
            // processDefId: this.common.processDefId,
            // workItemId: this.common.workItemId,
            // processNode: this.common.processNode,
            actionName: "资源勘误",
            correction: this.resourceInitData,
          };

          param.correction.updCellCoverType =
            this.resourceForm.isCellCoverTypeChange == "是"
              ? this.resourceForm.cellCoverTypeChanged
              : ""; //变更小区覆盖类型
          param.correction.upddDistrictId =
            this.resourceForm.isDistrictNameChange == "是"
              ? this.updDistrictId
              : ""; //变更所属行政区域id取最后一级城镇的id
          param.correction.updDistrictName =
            this.resourceForm.isDistrictNameChange == "是"
              ? this.updDistrictName
              : ""; //变更所属行政区域名称
          param.correction.updRegionsType =
            this.resourceForm.isRegionsTypeChange == "是"
              ? this.resourceForm.regionsTypeChanged
              : ""; //变更所属行政区域类型
          param.correction.updLongitude =
            this.resourceForm.isJFLonChange == "是"
              ? this.resourceForm.jfLonChanged
              : ""; //变更经度
          param.correction.updLatitude =
            this.resourceForm.isJFLatChange == "是"
              ? this.resourceForm.jfLatChanged
              : ""; //变更纬度
          // updRoomId = ;//变更机房CUID
          param.correction.updRoomName =
            this.resourceForm.isSitenoChange == "是"
              ? this.resourceForm.sitenoChanged
              : ""; //变更机房名称
          param.correction.remarks = this.resourceForm.remarks; //备注

          formData.append("param", JSON.stringify(param));
          this.submit(formData);
        } else {
          return false;
        }
      });
    },

    submit(param) {
      apicorrectionSubmit(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success(res.msg);
            this.onResetResourceForm();
            this.$emit("rcDialog");
          } else {
            this.$message.error(res.msg);
          }
          this.afterSingleFullscreenLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.afterSingleFullscreenLoading = false;
          this.$message.error(error.msg);
        });
    },

    //附件关闭
    close(tag) {
      this.importForm.attachmentFileList.splice(
        this.importForm.attachmentFileList.indexOf(tag),
        1
      );
    },
    //附件选择
    attachmentBrowse() {
      this.attachmentDialogVisible = true;
    },
    changeFileData(data) {
      this.importForm.attachmentFileList = data.attachmentFileList;
      this.attachmentDialogVisible = false;
    },
    closeAttachmentDialog() {
      this.attachmentDialogVisible = false;
    },
    onResetResourceForm() {
      this.resourceForm = {
        isSitenoChange: "否",
        sitenoChanged: "",
        isJFLonChange: "否", //经度
        jfLonChanged: "",
        isJFLatChange: "否", //纬度
        jfLatChanged: "",
        isDistrictNameChange: "否",
        provinceChanged: this.resourceInitData.privinceName,
        cityChanged: "",
        areaChanged: "",
        townChanged: "",
        isCellCoverTypeChange: "否",
        cellCoverTypeChanged: "",
        isRegionsTypeChange: "否",
        regionsTypeChanged: "",
        remarks: "",
      };
      this.importForm.attachmentFileList = [];
      this.$refs.resourceForm.clearValidate();
    },

    close(tag) {
      this.importForm.attachmentFileList.splice(
        this.importForm.attachmentFileList.indexOf(tag),
        1
      );
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-tree {
  padding-top: 15px;
  padding-left: 10px;
  // 不可全选样式
  .el-tree-node {
    .is-leaf + .el-checkbox .el-checkbox__inner {
      display: inline-block;
    }
    .el-checkbox .el-checkbox__inner {
      display: none;
    }
  }
}
.afterSingle {
  .fileName_style {
    margin-right: 3px;
    vertical-align: middle;
    div {
      display: inline-block;
      max-width: 360px;
      vertical-align: top;
    }
  }
}

::v-deep .el-form-item__label {
  padding: 0 10px 0 0 !important;
}
</style>
<style lang="scss">
.toolCla {
  background: #6c6c6c !important;

  .popper__arrow::after {
    border-top-color: #6c6c6c !important;
    border-bottom-color: #6c6c6c !important;
  }
}
</style>
