<template>
  <el-card shadow="always" class="card" :body-style="{ padding: '10px 24px' }">
    <el-collapse v-model="activeNames">
      <el-collapse-item name="1">
        <template slot="title"
          ><span class="header-title">反馈单详情</span></template
        >
        <div class="content">
          <el-descriptions
            title="故障定性信息"
            class="descriptions"
            v-if="list.faultQuality.isShow && list.faultQuality.isShow == 'Y'"
          >
            <el-descriptions-item label="故障所属专业">{{
              list.faultQuality.professionalType
            }}</el-descriptions-item>
            <!--            <el-descriptions-item label="故障等级">{{-->
            <!--              list.faultQuality.faultLevel-->
            <!--            }}</el-descriptions-item>-->

            <el-descriptions-item label="工单优先级">{{
              list.faultQuality.faultLevel
            }}</el-descriptions-item>
            <el-descriptions-item label="故障发生时间">{{
              list.faultQuality.alarmCreateTime
            }}</el-descriptions-item>
            <el-descriptions-item label="通知时间">{{
              list.faultQuality.faultNoticeTime
            }}</el-descriptions-item>
            <el-descriptions-item label="故障恢复时间">{{
              list.faultQuality.faultRecoveryTime
            }}</el-descriptions-item>
            <el-descriptions-item label="故障处理历时">{{
              list.faultQuality.faultDuration
            }}</el-descriptions-item>
            <el-descriptions-item label="故障恢复历时">{{
              list.faultQuality.recoveryDuration
            }}</el-descriptions-item>

            <el-descriptions-item label="故障处理部门">
              {{ list.faultQuality.dept }}
            </el-descriptions-item>
            <el-descriptions-item label="故障修复人">{{
              list.faultQuality.recoveryPerson
            }}</el-descriptions-item>
            <el-descriptions-item label="修复人电话">{{
              list.faultQuality.recoveryPhone
            }}</el-descriptions-item>
            <el-descriptions-item
              label="关联工单"
              v-if="list.faultQuality.linkedSheetNo"
              >{{ list.faultQuality.linkedSheetNo }}</el-descriptions-item
            >

            <el-descriptions-item label="是否影响业务">{{
              list.faultQuality.isEffectBusiness
            }}</el-descriptions-item>
            <el-descriptions-item
              label="是否基站退服"
              v-if="professionalTypeId == 7"
              >{{ list.faultQuality.isSiteOffline }}</el-descriptions-item
            >
            <el-descriptions-item
              label="退服原因"
              v-if="
                professionalTypeId == 7 &&
                list.faultQuality.isSiteOffline == '是'
              "
              >{{ list.faultQuality.siteOfflineReason }}</el-descriptions-item
            >
            <el-descriptions-item
              label="是否为铁塔原因"
              v-if="professionalTypeId == 7"
              >{{ list.faultQuality.isTowerReason }}</el-descriptions-item
            >
          </el-descriptions>
          <el-descriptions
            title="故障专业信息"
            class="descriptions"
            v-if="list.faultMajor.isShow && list.faultMajor.isShow == 'Y'"
          >
            <!-- 无线网、核心网、动环网、数据网、铁塔、接入网 IDC29、IP互联网33、核心网其他32 -->
            <template
              v-if="
                professionalTypeId == 7 ||
                professionalTypeId == 1 ||
                professionalTypeId == 4 ||
                professionalTypeId == 29 ||
                professionalTypeId == 33 ||
                professionalTypeId == 32 ||
                professionalTypeId == 5 ||
                professionalTypeId == 9 ||
                professionalTypeId == 10
              "
            >
              <el-descriptions-item label="故障分类">
                {{ list.faultMajor.faultCate }}</el-descriptions-item
              >
              <el-descriptions-item label="故障原因">
                {{ list.faultMajor.faultReason }}</el-descriptions-item
              >
              <el-descriptions-item
                label="故障厂家"
                :span="6"
                v-if="
                  professionalTypeId == 7 &&
                  (list.faultMajor.faultCate == '传输系统' ||
                    list.faultMajor.faultCate == '基站设备')
                "
              >
                {{ list.faultMajor.vendor }}</el-descriptions-item
              >
              <el-descriptions-item
                label="故障厂家"
                :span="6"
                v-if="professionalTypeId != 7"
              >
                {{ list.faultMajor.vendor }}</el-descriptions-item
              >
              <el-descriptions-item label="设备类型">
                {{ list.faultMajor.eqpType }}</el-descriptions-item
              >
              <el-descriptions-item label="设备名称">
                {{ list.faultMajor.eqpName }}</el-descriptions-item
              >
              <el-descriptions-item label="故障处理过程及原因描述" :span="3">
                {{ list.faultMajor.falutReasonDesc }}</el-descriptions-item
              >
              <el-descriptions-item label="备注" :span="3">
                {{ list.faultMajor.falutComment }}</el-descriptions-item
              >
            </template>
            <!-- 增值平台、移动数通网、固网、IT监控、IPTV、智能网、vIMS、5GC、物联网、省份云、MEC -->
            <template
              v-if="
                professionalTypeId == 8 ||
                professionalTypeId == 2 ||
                professionalTypeId == 6 ||
                professionalTypeId == 15 ||
                professionalTypeId == 16 ||
                professionalTypeId == 18 ||
                professionalTypeId == 19 ||
                professionalTypeId == 20 ||
                professionalTypeId == 21 ||
                professionalTypeId == 24 ||
                professionalTypeId == 25
              "
            >
              <el-descriptions-item label="故障处理过程及原因描述" :span="3">
                {{ list.faultMajor.falutReasonDesc }}</el-descriptions-item
              >
              <el-descriptions-item label="备注" :span="3">
                {{ list.faultMajor.falutComment }}</el-descriptions-item
              >
            </template>
            <!-- 传输网 -->
            <template v-if="professionalTypeId == 3">
              <el-descriptions-item label="故障状态">
                {{ list.faultMajor.faultStatus }}</el-descriptions-item
              >
              <el-descriptions-item label="网络类型">
                {{ list.faultMajor.networkType }}</el-descriptions-item
              >
              <el-descriptions-item label="故障源">
                {{ list.faultMajor.faultSource }}</el-descriptions-item
              >
              <el-descriptions-item label="处理方法">
                {{ list.faultMajor.processMethod }}</el-descriptions-item
              >
              <el-descriptions-item label="故障分类">
                {{ list.faultMajor.faultCate }}</el-descriptions-item
              >
              <el-descriptions-item label="故障原因">
                {{ list.faultMajor.faultReason }}</el-descriptions-item
              >
              <el-descriptions-item label="设备类型">
                {{ list.faultMajor.eqpType }}</el-descriptions-item
              >
              <el-descriptions-item label="设备名称">
                {{ list.faultMajor.eqpName }}</el-descriptions-item
              >

              <el-descriptions-item label="机盘名称">
                {{ list.faultMajor.diskName }}</el-descriptions-item
              >
              <el-descriptions-item label="业务中断系统">
                {{ list.faultMajor.interSystem }}</el-descriptions-item
              >
              <el-descriptions-item label="受影响的系统">
                {{ list.faultMajor.effectSystem }}</el-descriptions-item
              >
              <el-descriptions-item label="中断电路">
                {{ list.faultMajor.interCircuit }}</el-descriptions-item
              >
              <el-descriptions-item label="故障区间">
                {{ list.faultMajor.faultRange }}</el-descriptions-item
              >
              <el-descriptions-item label="受影响的电路">
                {{ list.faultMajor.effectCircuit }}</el-descriptions-item
              >
              <el-descriptions-item label="故障处理过程及原因描述" :span="3">
                {{ list.faultMajor.falutReasonDesc }}</el-descriptions-item
              >
              <el-descriptions-item label="备注" :span="3">
                {{ list.faultMajor.falutComment }}</el-descriptions-item
              >
            </template>
          </el-descriptions>
          <el-descriptions
            title="审核信息"
            v-if="list.auditInfo.isShow && list.auditInfo.isShow == 'Y'"
            class="descriptions"
          >
            <el-descriptions-item label="审核人">
              {{ list.auditInfo.auditUser }}</el-descriptions-item
            >
            <el-descriptions-item label="审核人名称">
              {{ list.auditInfo.auditTrueName }}
            </el-descriptions-item>
            <el-descriptions-item label="审核人电话">
              {{ list.auditInfo.auditPhone }}</el-descriptions-item
            >
            <el-descriptions-item label="审核人部门编码">
              {{ list.auditInfo.auditOrgCode }}</el-descriptions-item
            >
            <el-descriptions-item label="审核人部门名称">
              {{ list.auditInfo.auditOrgName }}</el-descriptions-item
            >
            <el-descriptions-item label="审核时间">
              {{ list.auditInfo.auditTime }}</el-descriptions-item
            >
            <el-descriptions-item label="审批意见">
              {{ list.auditInfo.auditOpinion }}</el-descriptions-item
            >
            <el-descriptions-item label="审批结果">
              <template v-if="list.auditInfo.auditResult == 'Y'">是</template>
              <template v-else>否</template>
            </el-descriptions-item>
          </el-descriptions>
          <el-descriptions
            title="包机人评定"
            v-if="
              list.chartererJudge.isShow && list.chartererJudge.isShow == 'Y'
            "
            class="descriptions"
          >
            <el-descriptions-item label="修障质量">
              <el-rate
                :value="Number(list.chartererJudge.repairQuality)"
                show-text
                disabled
                :texts="descriText"
              ></el-rate
            ></el-descriptions-item>
            <el-descriptions-item label="反馈质量">
              <el-rate
                :value="Number(list.chartererJudge.fdbkQuality)"
                show-text
                disabled
                :texts="descriText"
              ></el-rate>
            </el-descriptions-item>
            <el-descriptions-item label="满意度">
              <el-rate
                :value="Number(list.chartererJudge.satisfaction)"
                show-text
                disabled
                :texts="descriText"
              ></el-rate
            ></el-descriptions-item>
            <el-descriptions-item label="评价意见">
              {{ list.chartererJudge.evaluationOpinion }}</el-descriptions-item
            >
          </el-descriptions>
          <el-descriptions
            title="派单评价"
            v-if="
              basicWorkOrderData.woAccuracy != null &&
              basicWorkOrderData.woAccuracy != '无'
            "
            class="descriptions"
          >
            <el-descriptions-item label="工单准确性">
              <el-rate
                :value="Number(basicWorkOrderData.woAccuracy)"
                show-text
                disabled
                :texts="descriText"
              ></el-rate
            ></el-descriptions-item>
            <el-descriptions-item label="派单及时性">
              <el-rate
                :value="Number(basicWorkOrderData.disOnTime)"
                show-text
                disabled
                :texts="descriText"
              ></el-rate>
            </el-descriptions-item>
            <el-descriptions-item label="中台评价">
              <el-rate
                :value="Number(basicWorkOrderData.ztEvaluation)"
                show-text
                disabled
                :texts="descriText"
              ></el-rate
            ></el-descriptions-item>
            <el-descriptions-item label="评价意见">
              {{ basicWorkOrderData.opinion }}</el-descriptions-item
            >
          </el-descriptions>
        </div>
      </el-collapse-item>
    </el-collapse>
  </el-card>
</template>

<script>
import { apiqueryFeedback } from "../api/CommonApi";
export default {
  name: "FeedbackSheet",
  props: {
    isShowAudit: Boolean, //判断定性审核按钮
    common: Object,
    woId: String,
    isShowQualitative: Boolean, //判断定性按钮
    qualitativeType: String,
    basicWorkOrderData: Object,
  },
  components: {},
  data() {
    return {
      activeNames: ["1"],
      tabMenu: [],
      professionalTypeId: "",
      list: {
        faultQuality: {}, // 故障定性
        faultMajor: {}, // 故障专业信息
        auditInfo: {}, // 审核信息
        chartererJudge: {}, // 包机人评定
      },
      descriText: ["很不满意", "不满意", "一般", "很满意", "非常满意"],
    };
  },
  mounted() {
    if (this.woId) {
      this.getFeedbackData();
    }
    console.log(this.common, this.basicWorkOrderData, "反馈单");
  },
  methods: {
    getFeedbackData() {
      let param = {
        woId: this.woId,
      };
      apiqueryFeedback(param)
        .then(res => {
          if (res.status == 0) {
            let data = res?.data ?? [];
            if (data.length > 0) {
              this.list.faultQuality = data[0].faultQuality;
              this.list.chartererJudge = data[0].chartererJudge;
              this.list.faultQuality = data[0].faultQuality;
              this.list.faultMajor = data[0].faultMajor;
              this.professionalTypeId = data[0].faultQuality.professionalTypeId;
            }
          }
        })
        .catch(err => {
          console.log(err);
        });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../assets/common.scss";
.header-tabMenu {
  margin-top: 10px;
}
.custom-theme-default .el-collapse {
  border-top: none;
}
::v-deep .el-collapse-item__wrap {
  border-bottom: 0;
}
</style>
