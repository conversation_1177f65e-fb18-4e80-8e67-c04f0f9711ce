<template>
  <el-scrollbar class="qualitative" ref="scroll">
    <el-form
      ref="qualitativeForm"
      :inline="false"
      class="demo-form-inline"
      :model="qualitativeForm"
      label-width="130px"
      :rules="qualitativeFormRule"
      v-loading="formLoading"
    >
      <el-card
        shadow="never"
        header="故障定性信息"
        :body-style="{ padding: '20px 8px' }"
        class="cus-card"
      >
        <el-row type="flex" style="flex-wrap: wrap" :gutter="30">
          <el-col :span="8">
            <el-form-item label="故障所属专业:" prop="professionalType">
              <el-select
                v-model="qualitativeForm.professionalType"
                placeholder="请选择内容"
                style="width: 100%"
                @change="professionalTypeChange"
                :disabled="relativeDisabled"
              >
                <el-option
                  v-for="(item, i) in professionalList"
                  :key="i"
                  :label="item.dictName"
                  :value="{ value: item.dictCode, label: item.dictName }"
                >
                  <span style="float: left">{{ item.dictName }}</span>
                  <span
                    v-if="tjProfessionalTypeList.indexOf(item.dictName) > -1"
                    style="
                      float: right;
                      display: flex;
                      align-items: center;
                      height: 100%;
                    "
                  >
                    <img
                      src="../assets/tj.png"
                      style="width: 18px; height: 18px"
                      alt=""
                    />
                  </span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="工单优先级:"
              prop="faultLevel"
              :rules="{
                required: true,
                message: '请选择工单优先级',
              }"
            >
              <el-select
                v-model="qualitativeForm.faultLevel"
                placeholder="请选择内容"
                style="width: 100%"
                :disabled="relativeDisabled"
              >
                <el-option
                  v-for="(item, i) in levelList"
                  :key="i"
                  :label="item.dictName"
                  :value="item.dictName"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="故障发生时间:"
              prop="alarmCreateTime"
              :rules="{
                required: true,
                message: '请选择故障发生时间',
              }"
            >
              <el-input
                v-model="qualitativeForm.alarmCreateTime"
                readonly
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="通知时间:"
              prop="faultNoticeTime"
              :rules="{
                required: true,
                message: '请选择通知时间',
              }"
            >
              <el-input
                v-model="qualitativeForm.faultNoticeTime"
                readonly
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障恢复时间:" prop="faultRecoveryTime">
              <el-date-picker
                v-model="qualitativeForm.faultRecoveryTime"
                type="datetime"
                placeholder="请选择时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
                style="width: 100%"
                :picker-options="recoveryPicker"
                :default-time="recoveryDefaultTime"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障处理历时:" prop="faultDuration">
              {{ qualitativeForm.faultDuration }}
              <!-- {{ second2Time(qualitativeForm.faultDuration) }} -->
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障恢复历时:" prop="recoveryDuration">
              <!-- {{ second2Time(qualitativeForm.recoveryDuration) }} -->
              {{ qualitativeForm.recoveryDuration }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="挂起历时:" prop="suspendDuration">
              {{ qualitativeForm.suspendDuration }}分钟
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障处理部门:" required>
              <el-input
                v-model="qualitativeForm.dept"
                placeholder="故障处理部门"
                style="width: 100%"
                maxlength="50"
                @keyup.native="descTip(50, 'dept', 'showgzclbm')"
              ></el-input>
              <div class="el-form-item__error" v-if="showgzclbm">
                已超过填写字数上限
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障修复人:" prop="recoveryPerson">
              <el-input
                v-model="qualitativeForm.recoveryPerson"
                placeholder="故障修复人"
                style="width: 100%"
                maxlength="50"
                @keyup.native="descTip(50, 'recoveryPerson', 'showgzxfr')"
              ></el-input>
              <div class="el-form-item__error" v-if="showgzxfr">
                已超过填写字数上限
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="修复人电话:" prop="recoveryPhone">
              <el-input
                v-model="qualitativeForm.recoveryPhone"
                placeholder="修复人电话"
                style="width: 100%"
                maxlength="50"
                @keyup.native="descTip(50, 'recoveryPhone', 'showxfrdh')"
              ></el-input>
              <div class="el-form-item__error" v-if="showxfrdh">
                已超过填写字数上限
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="关联工单:" prop="relativeOrder">
              <el-input
                v-model="qualitativeForm.linkedSheetNo"
                style="width: 100%"
                readonly
                ><el-button
                  slot="append"
                  class="append-btn"
                  @click="relativeOrderDialogVisible = true"
                  >选择</el-button
                ></el-input
              >
              <el-tooltip
                v-if="dynamicTags.length != 0"
                class="item"
                effect="dark"
                :content="dynamicTags[0]"
                placement="top"
              >
                <template v-for="tag in dynamicTags">
                  <el-tag
                    :key="tag"
                    v-if="tag != ''"
                    closable
                    :disable-transitions="false"
                    @close="handleTagClose(tag)"
                    class="relative-tag"
                  >
                    <a class="text-hide" href="javascript:;">{{ tag }}</a>
                  </el-tag>
                </template>
              </el-tooltip>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="是否影响业务:"
              prop="isEffectBusiness"
              :rules="[
                {
                  required: true,
                  message: '请选择是否影响业务',
                },
              ]"
            >
              <el-radio-group
                v-model="qualitativeForm.isEffectBusiness"
                :disabled="relativeDisabled"
              >
                <el-radio label="否">否</el-radio>
                <el-radio label="是">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="qualitativeForm.professionalTypeId == '7'">
            <el-form-item
              label="是否基站退服:"
              prop="isSiteOffline"
              :rules="{
                required: true,
                message: '请选择是否基站退服',
                trigger: 'change',
              }"
            >
              <el-radio-group
                v-model="qualitativeForm.isSiteOffline"
                :disabled="relativeDisabled"
              >
                <el-radio label="否">否</el-radio>
                <el-radio label="是">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="qualitativeForm.isSiteOffline == '是'">
            <el-form-item label="退服原因:">
              <el-select
                v-model="qualitativeForm.siteOfflineReason"
                placeholder="请选择内容"
                style="width: 100%"
              >
                <el-option
                  v-for="(item, i) in siteOfflineReasonList"
                  :key="i"
                  :label="item.dictName"
                  :value="item.dictName"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="qualitativeForm.professionalTypeId == '7'">
            <el-form-item
              label="是否铁塔原因:"
              prop="isTowerReason"
              :rules="{
                required: true,
                message: '请选择是否铁塔原因',
                trigger: 'change',
              }"
            >
              <el-radio-group
                v-model="qualitativeForm.isTowerReason"
                style="width: 250px"
                :disabled="relativeDisabled"
              >
                <el-radio label="否">否</el-radio>
                <el-radio label="是">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="附件:" prop="attachmentFile">
              <el-input
                v-model="qualitativeForm.attachmentFile"
                placeholder="添加附件"
                clearable
                readonly
                style="width: 100%"
              >
                <el-button type="info" slot="append" @click="attachmentBrowse"
                  >+</el-button
                >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card
        shadow="never"
        header="故障专业信息"
        :body-style="{ padding: '20px 8px' }"
        style="margin-top: 20px"
        class="cus-card"
      >
        <!-- 无线网、核心网、动环网4、数据网、铁塔、接入网 IDC29 IP互联网33、核心网其他32 -->
        <template
          v-if="
            qualitativeForm.professionalTypeId == '7' ||
            qualitativeForm.professionalTypeId == '1' ||
            qualitativeForm.professionalTypeId == '4' ||
            qualitativeForm.professionalTypeId == '29' ||
            qualitativeForm.professionalTypeId == '33' ||
            qualitativeForm.professionalTypeId == '32' ||
            qualitativeForm.professionalTypeId == '5' ||
            qualitativeForm.professionalTypeId == '9' ||
            qualitativeForm.professionalTypeId == '10'
          "
        >
          <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
            <el-col :span="8">
              <el-form-item
                label="故障发生地区:"
                prop="pList01.faultRegion"
                :rules="{
                  required: true,
                  message: '请选择内容',
                  trigger: ['blur'],
                }"
              >
                <el-select
                  style="width: 100%"
                  v-model="qualitativeForm.pList01.faultRegion"
                  :disabled="relativeDisabled"
                >
                  <el-option
                    v-for="(item, i) in faultRegionOptions"
                    :key="i"
                    :label="item.name"
                    :value="item.name"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="故障分类:"
                prop="pList01.faultCate"
                :rules="{
                  required: true,
                  message: '请选择故障分类',
                  trigger: ['blur'],
                }"
              >
                <el-select
                  v-model="qualitativeForm.pList01.faultCate"
                  placeholder="请选择内容"
                  style="width: 100%"
                  :disabled="relativeDisabled"
                  @focus="
                    getDictData(
                      qualitativeForm.professionalTypeId + '_faultCate',
                      'faultCateList'
                    )
                  "
                  @change="faultCateChange"
                >
                  <el-option
                    v-for="(item, i) in faultCateList"
                    :key="i"
                    :label="item.dictName"
                    :value="{ value: item.dictCode, label: item.dictName }"
                  >
                    <span style="float: left">{{ item.dictName }}</span>
                    <span
                      v-if="tjFaultCateList.indexOf(item.dictName) > -1"
                      style="
                        float: right;
                        display: flex;
                        align-items: center;
                        height: 100%;
                      "
                    >
                      <img
                        src="../assets/tj.png"
                        style="width: 18px; height: 18px"
                        alt=""
                      />
                    </span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="故障原因:"
                :rules="{
                  required: true,
                  message: '请选择故障原因',
                  trigger: ['blur'],
                }"
                prop="pList01.faultReason"
              >
                <el-select
                  v-model="qualitativeForm.pList01.faultReason"
                  placeholder="请选择内容"
                  style="width: 100%"
                  :disabled="relativeDisabled"
                >
                  <el-option
                    v-for="(item, i) in faultReasonList"
                    :key="i"
                    :label="item.dictName"
                    :value="item.dictName"
                  >
                    <span style="float: left">{{ item.dictName }}</span>
                    <span
                      v-if="tjFaultReasonList.indexOf(item.dictName) > -1"
                      style="
                        float: right;
                        display: flex;
                        align-items: center;
                        height: 100%;
                      "
                    >
                      <img
                        src="../assets/tj.png"
                        style="width: 18px; height: 18px"
                        alt=""
                      />
                    </span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col
              :span="8"
              v-if="
                qualitativeForm.professionalTypeId == '7' &&
                (qualitativeForm.pList01.faultCate == '基站设备' ||
                  qualitativeForm.pList01.faultCate == '传输系统')
              "
            >
              <el-form-item
                label="故障厂家:"
                prop="pList01.vendor"
                :rules="{
                  required:
                    qualitativeForm.pList01.faultCate == '基站设备' ||
                    qualitativeForm.pList01.faultCate == '传输系统'
                      ? true
                      : false,
                  message: '请选择故障厂家',
                  trigger: ['blur'],
                }"
              >
                <el-select
                  style="width: 100%"
                  v-model="qualitativeForm.pList01.vendor"
                  :disabled="relativeDisabled"
                  @focus="
                    getDictData(
                      qualitativeForm.professionalTypeId +
                        '_' +
                        qualitativeForm.pList01.faultCateId +
                        '_vendor',
                      'vendorList'
                    )
                  "
                >
                  <el-option
                    v-for="(item, i) in vendorList"
                    :key="i"
                    :label="item.dictName"
                    :value="item.dictName"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="qualitativeForm.professionalTypeId != '7'">
              <el-form-item
                label="故障厂家:"
                prop="pList01.vendor"
                :rules="{
                  required: true,
                  message: '请选择故障厂家',
                  trigger: ['blur'],
                }"
              >
                <el-select
                  style="width: 100%"
                  v-model="qualitativeForm.pList01.vendor"
                  :disabled="relativeDisabled"
                  @focus="getDictData('fault_vendor', 'vendorList')"
                >
                  <el-option
                    v-for="(item, i) in vendorList"
                    :key="i"
                    :label="item.dictName"
                    :value="item.dictName"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="设备类型:" prop="pList01.eqpType">
                <el-select
                  style="width: 100%"
                  :disabled="relativeDisabled"
                  v-model="qualitativeForm.pList01.eqpType"
                >
                  <el-option
                    v-for="(item, i) in deviceTypeList"
                    :key="i"
                    :label="item.dictName"
                    :value="item.dictName"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="设备名称:">
                <el-input
                  v-model="qualitativeForm.pList01.eqpName"
                  placeholder="请输入内容"
                  :disabled="relativeDisabled"
                  style="width: 100%"
                  maxlength="255"
                  @keyup.native="
                    desc2Tip(255, 'pList01', 'eqpName', 'showsbmc')
                  "
                >
                </el-input>
                <div class="el-form-item__error" v-if="showsbmc">
                  已超过填写字数上限
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </template>
        <!-- 传输网 -->
        <template v-if="qualitativeForm.professionalTypeId == '3'">
          <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
            <el-col :span="8">
              <el-form-item
                label="故障状态:"
                prop="pList02.faultStatus"
                :rules="{
                  required: true,
                  message: '请选择故障状态',
                }"
              >
                <el-select
                  style="width: 100%"
                  :disabled="relativeDisabled"
                  v-model="qualitativeForm.pList02.faultStatus"
                >
                  <el-option
                    v-for="(item, i) in faultStatusList"
                    :key="i"
                    :label="item.dictName"
                    :value="item.dictName"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="网络类型:"
                prop="pList02.networkType"
                :rules="{
                  required: true,
                  message: '请选择网络类型',
                }"
              >
                <el-select
                  v-model="qualitativeForm.pList02.networkType"
                  placeholder="请选择内容"
                  style="width: 100%"
                  :disabled="relativeDisabled"
                  @focus="
                    getDictData(
                      qualitativeForm.professionalTypeId + '_networkType',
                      'networkTypeList'
                    )
                  "
                >
                  <el-option
                    v-for="(item, i) in networkTypeList"
                    :key="i"
                    :label="item.dictName"
                    :value="item.dictName"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="故障源:">
                <el-input
                  placeholder="请输入内容"
                  v-model="qualitativeForm.pList02.faultSource"
                  style="width: 100%"
                  maxlength="50"
                  :disabled="relativeDisabled"
                  @keyup.native="
                    desc2Tip(50, 'pList02', 'faultSource', 'showgzy')
                  "
                >
                </el-input>
                <div class="el-form-item__error" v-if="showgzy">
                  已超过填写字数上限
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="处理方法:">
                <el-input
                  placeholder="请输入内容"
                  v-model="qualitativeForm.pList02.processMethod"
                  style="width: 100%"
                  maxlength="255"
                  :disabled="relativeDisabled"
                  @keyup.native="
                    desc2Tip(255, 'pList02', 'processMethod', 'showclff')
                  "
                >
                </el-input>
                <div class="el-form-item__error" v-if="showclff">
                  已超过填写字数上限
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="故障分类:"
                prop="pList02.faultCate"
                :rules="{
                  required: true,
                  message: '请选择故障分类',
                  trigger: ['blur'],
                }"
              >
                <el-select
                  v-model="qualitativeForm.pList02.faultCate"
                  placeholder="请选择内容"
                  style="width: 100%"
                  :disabled="relativeDisabled"
                  @focus="
                    getDictData(
                      qualitativeForm.professionalTypeId + '_faultCate',
                      'faultCateList'
                    )
                  "
                  @change="faultCateChange"
                >
                  <el-option
                    v-for="(item, i) in faultCateList"
                    :key="i"
                    :label="item.dictName"
                    :value="{ value: item.dictCode, label: item.dictName }"
                  >
                    <span style="float: left">{{ item.dictName }}</span>
                    <span
                      v-if="tjFaultCateList.indexOf(item.dictName) > -1"
                      style="
                        float: right;
                        display: flex;
                        align-items: center;
                        height: 100%;
                      "
                    >
                      <img
                        src="../assets/tj.png"
                        style="width: 18px; height: 18px"
                        alt=""
                      />
                    </span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="故障原因:"
                :rules="{
                  required: true,
                  message: '请选择故障原因',
                  trigger: ['blur'],
                }"
                prop="pList02.faultReason"
              >
                <el-select
                  v-model="qualitativeForm.pList02.faultReason"
                  placeholder="请选择内容"
                  :disabled="relativeDisabled"
                  style="width: 100%"
                >
                  <el-option
                    v-for="(item, i) in faultReasonList"
                    :key="i"
                    :label="item.dictName"
                    :value="item.dictName"
                  >
                    <span style="float: left">{{ item.dictName }}</span>
                    <span
                      v-if="tjFaultReasonList.indexOf(item.dictName) > -1"
                      style="
                        float: right;
                        display: flex;
                        align-items: center;
                        height: 100%;
                      "
                    >
                      <img
                        src="../assets/tj.png"
                        style="width: 18px; height: 18px"
                        alt=""
                      />
                    </span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="设备类型:" prop="pList02.eqpType">
                <el-select
                  style="width: 100%"
                  :disabled="relativeDisabled"
                  v-model="qualitativeForm.pList02.eqpType"
                >
                  <el-option
                    v-for="(item, i) in deviceTypeList"
                    :key="i"
                    :label="item.dictName"
                    :value="item.dictName"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="设备名称:">
                <el-input
                  v-model="qualitativeForm.pList02.eqpName"
                  placeholder="请输入内容"
                  style="width: 100%"
                  maxlength="255"
                  :disabled="relativeDisabled"
                  @keyup.native="
                    desc2Tip(255, 'pList02', 'eqpName', 'showsbmc')
                  "
                >
                </el-input>
                <div class="el-form-item__error" v-if="showsbmc">
                  已超过填写字数上限
                </div>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="机盘名称:">
                <el-input
                  placeholder="请输入内容"
                  v-model="qualitativeForm.pList02.diskName"
                  style="width: 100%"
                  maxlength="50"
                  :disabled="relativeDisabled"
                  @keyup.native="
                    desc2Tip(50, 'pList02', 'diskName', 'showjpmc')
                  "
                >
                </el-input>
                <div class="el-form-item__error" v-if="showjpmc">
                  已超过填写字数上限
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="业务中断系统:">
                <el-input
                  placeholder="请输入内容"
                  v-model="qualitativeForm.pList02.interSystem"
                  style="width: 100%"
                  maxlength="50"
                  :disabled="relativeDisabled"
                  @keyup.native="
                    desc2Tip(50, 'pList02', 'interSystem', 'showywzdxt')
                  "
                >
                </el-input>
                <div class="el-form-item__error" v-if="showywzdxt">
                  已超过填写字数上限
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="受影响的系统:">
                <el-input
                  placeholder="请输入内容"
                  v-model="qualitativeForm.pList02.effectSystem"
                  style="width: 100%"
                  maxlength="50"
                  :disabled="relativeDisabled"
                  @keyup.native="
                    desc2Tip(50, 'pList02', 'effectSystem', 'showsyxdxt')
                  "
                >
                </el-input>
                <div class="el-form-item__error" v-if="showsyxdxt">
                  已超过填写字数上限
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="中断电路:">
                <el-input
                  placeholder="请输入内容"
                  v-model="qualitativeForm.pList02.interCircuit"
                  style="width: 100%"
                  maxlength="50"
                  :disabled="relativeDisabled"
                  @keyup.native="
                    desc2Tip(50, 'pList02', 'interCircuit', 'showzddl')
                  "
                >
                </el-input>
                <div class="el-form-item__error" v-if="showzddl">
                  已超过填写字数上限
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="故障区间:">
                <el-input
                  placeholder="请输入内容"
                  v-model="qualitativeForm.pList02.faultRange"
                  style="width: 100%"
                  maxlength="50"
                  :disabled="relativeDisabled"
                  @keyup.native="
                    desc2Tip(50, 'pList02', 'faultRange', 'showgzqjshowgzqj')
                  "
                >
                </el-input>
                <div class="el-form-item__error" v-if="showgzqj">
                  已超过填写字数上限
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="受影响的电路:">
                <el-input
                  placeholder="请输入内容"
                  v-model="qualitativeForm.pList02.effectCircuit"
                  style="width: 100%"
                  maxlength="50"
                  :disabled="relativeDisabled"
                  @keyup.native="
                    desc2Tip(50, 'pList02', 'effectCircuit', 'showsyxddl')
                  "
                >
                </el-input>
                <div class="el-form-item__error" v-if="showsyxddl">
                  已超过填写字数上限
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </template>
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="24">
            <el-form-item
              label="故障处理过程及原因描述:"
              prop="falutReasonDesc"
              :rules="{
                required: true,
                message: '故障原因描述不能为空',
              }"
            >
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="qualitativeForm.falutReasonDesc"
                style="width: 100%"
                show-word-limit
                maxlength="255"
                @keyup.native="descTip(255, 'falutReasonDesc', 'showgzclgc')"
              >
              </el-input>
              <div class="el-form-item__error" v-if="showgzclgc">
                已超过填写字数上限
              </div>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="备注:">
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="qualitativeForm.falutComment"
                style="width: 100%"
                show-word-limit
                maxlength="255"
                @keyup.native="descTip(255, 'falutComment', 'showbz')"
              >
              </el-input>
              <div class="el-form-item__error" v-if="showbz">
                已超过填写字数上限
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
    </el-form>
    <div style="padding: 10px 20px; text-align: center">
      <el-button
        type="primary"
        @click="handleSubmit('qualitativeForm')"
        v-loading.fullscreen.lock="qualitativeFullscreenLoading"
        >提交</el-button
      >
      <el-button @click="onReset">重 置</el-button>
    </div>
    <el-dialog
      width="420px"
      title="附件选择"
      append-to-body
      :visible.sync="attachmentDialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <file-upload
        v-if="attachmentDialogVisible"
        @change="changeFileData"
        @cancel="closeAttachmentDialog"
      ></file-upload>
    </el-dialog>
    <!-- 关联工单 -->
    <el-dialog
      width="1200px"
      title="关联工单"
      append-to-body
      :visible.sync="relativeOrderDialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="relativeOrderDialogVisible = false"
    >
      <relative-order
        v-if="relativeOrderDialogVisible"
        :formData="common"
        :professionalList="professionalList"
        @submitRelativeOrder="submitRelativeOrder"
        @closeRelativeOrder="closeRelativeOrder"
      ></relative-order>
    </el-dialog>
  </el-scrollbar>
</template>
<script>
import moment from "moment";
import { mapGetters } from "vuex";
import {
  apiGetFaultArea,
  apiActionPublic,
  apiInitQualitative,
  apiqueryFeedback,
  apiDict,
  apiGroupCode,
  apiFileUpload,
} from "../api/CommonApi";

import { apiGetOrgInfo } from "../../../workOrder/api/CommonApi";
import { apiDeleteFdFile } from "../../../workOrder/workOrderWaitDetail/api/CommonApi";
import FileUpload from "./FileUpload.vue";
import RelativeOrder from "./relativeOrder.vue";
export default {
  name: "Qualitative",
  props: {
    common: Object,
    workItemId: [String, Number],
    isUploadReport: [String, Number],
    qData: Object,
  },
  components: { FileUpload, RelativeOrder },

  computed: {
    ...mapGetters(["userInfo"]),
  },
  data() {
    var validFaultRecoveryTime = (rule, value, callback) => {
      if (value === "" || value === null) {
        callback(new Error("请选择时间"));
      } else {
        // debugger
        if (this.qualitativeForm.faultRecoveryTime) {
          let seconds3 = moment(
            this.qualitativeForm.faultRecoveryTime,
            "YYYY-MM-DD HH:mm:ss"
          ).diff(moment(new Date(), "YYYY-MM-DD HH:mm:ss"), "seconds");
          let seconds4 = moment(
            this.qualitativeForm.faultRecoveryTime,
            "YYYY-MM-DD HH:mm:ss"
          ).diff(
            moment(this.qualitativeForm.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
            "seconds"
          );
          if (seconds3 > 0 || seconds4 <= 0) {
            callback(
              new Error(
                "当前时间>=故障恢复时间>=故障发生时间，请重新检查后选择正确时间"
              )
            );
          } else {
            callback();
          }
        } else {
          callback();
        }
      }
    };
    var validLastClearTime = (rule, value, callback) => {
      if (
        this.qualitativeForm.lastClearTime &&
        this.qualitativeForm.faultEndTime
      ) {
        let seconds = moment(
          this.qualitativeForm.faultEndTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.qualitativeForm.lastClearTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );

        let seconds2 = moment(
          this.qualitativeForm.lastClearTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.common.failureInformTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        if (seconds < 0 || seconds2 <= 0) {
          callback(
            new Error(
              "故障结束时间>=故障代通时间>故障通知时间，请重新检查后选择正确时间"
            )
          );
        } else {
          callback();
        }
      } else if (this.qualitativeForm.lastClearTime) {
        let seconds3 = moment(
          this.qualitativeForm.lastClearTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(moment(new Date(), "YYYY-MM-DD HH:mm:ss"), "seconds");
        let seconds4 = moment(
          this.qualitativeForm.lastClearTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.common.failureInformTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        if (seconds3 > 0 || seconds4 <= 0) {
          callback(
            new Error(
              "当前时间>=故障代通时间>故障通知时间，请重新检查后选择正确时间"
            )
          );
        }
      } else {
        callback();
      }
    };
    var validFaultEndTime = (rule, value, callback) => {
      if (value === "" || value === null) {
        callback(new Error("请选择故障结束时间"));
      } else {
        if (this.qualitativeForm.lastClearTime) {
          let clSeconds = moment(
            this.qualitativeForm.faultEndTime,
            "YYYY-MM-DD HH:mm:ss"
          ).diff(
            moment(this.qualitativeForm.lastClearTime, "YYYY-MM-DD HH:mm:ss"),
            "seconds"
          );
          if (clSeconds < 0) {
            callback(
              new Error(
                "当前时间>=故障结束时间>=故障代通时间，请重新检查后选择正确时间"
              )
            );
          }
        }

        let seconds = moment(
          this.qualitativeForm.faultEndTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.common.failureInformTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );

        let seconds2 = moment(new Date(), "YYYY-MM-DD HH:mm:ss").diff(
          moment(this.qualitativeForm.faultEndTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        if (seconds <= 0 || seconds2 < 0) {
          callback(
            new Error(
              "当前时间>=故障结束时间>故障通知时间，请重新检查后选择正确时间"
            )
          );
        } else {
          callback();
        }
      }
    };

    return {
      showgzclbm: false,
      showgzxfr: false,
      showxfrdh: false,
      showtfyy: false,
      showsbmc: false,
      showgzclgc: false,
      showbz: false,
      showgzy: false,
      showclff: false,
      showjpmc: false,
      showywzdxt: false,
      showsyxdxt: false,
      showzddl: false,
      showgzqj: false,
      showsyxddl: false,
      relativeOrderDialogVisible: false,
      showTipTime: 5000,
      tjProfessionalTypeList: [],
      tjFaultCateList: [],
      tjFaultReasonList: [],
      qualitativeForm: {
        professionalType: "",
        faultLevel: "",
        alarmCreateTime: "",
        faultNoticeTime: "",
        faultRecoveryTime: "",
        faultDuration: "",
        recoveryDuration: "",
        suspendDuration: "",
        dept: "",
        recoveryPerson: "",
        recoveryPhone: "",
        isEffectBusiness: "否",
        isSiteOffline: "否",
        isTowerReason: "否",
        linkedSheetNo: "",
        falutReasonDesc: "",
        falutComment: "",
        attachmentFile: "", //附件

        // 不同故障的专业信息
        pList01: {
          faultRegion: "",
          faultCate: "",
          faultReason: "",
          vendor: "",
          eqpType: "",
          eqpName: "",
        },
        pList02: {
          faultStatus: "",
          networkType: "",
          faultSource: "",
          faultCate: "",
          faultReason: "",
          eqpType: "",
          eqpName: "",
          diskName: "",
          interSystem: "",
          effectSystem: "",
          interCircuit: "",
          faultRange: "",
          effectCircuit: "",
        },
      },
      linkedSheetNoCopy: "",
      formLoading: false,
      userData: null,
      timeRange: "00:00:00 - 23:59:59",
      recoveryPicker: {
        disabledDate: this.disabledDate,
        selectableRange: this.timeRange,
      },
      initData: {}, // 初始化数据
      formInit: {},
      //关联附件
      importForm: {
        attachmentFileList: [],
      },
      attachmentDialogVisible: false,
      relativeDisabled: false,
      qualitativeFullscreenLoading: false,
      startTimeRange: "",
      startTimeRangeByFaultEndTime: "",
      recoveryDefaultTime: "",
      // 下拉框
      professionalList: [], //故障专业
      levelList: [], // 故障等级
      faultCateList: [], //故障分类
      faultReasonList: [], //故障原因
      vendorList: [], // 设备厂家
      deviceTypeList: [], // 设备类型
      networkTypeList: [], //网络类型
      faultStatusList: [], // 故障状态
      faultRegionOptions: [], //地区
      siteOfflineReasonList: [], //退服原因
      dynamicTags: [],
      qualitativeFormRule: {
        professionalType: [
          { required: true, message: "请选择故障所属专业", trigger: "blur" },
        ],
        faultRecoveryTime: [
          {
            validator: validFaultRecoveryTime,
            required: true,
            trigger: "blur",
          },
          // {
          //   required: true,
          //   message: "请选择故障恢复时间",
          //   trigger: "blur",
          // },
        ],
        alarmCreateTime: [
          {
            required: true,
            message: "请选择故障发生时间",
            trigger: "blur",
          },
        ],
        lastClearTime: [{ validator: validLastClearTime, trigger: "blur" }],
        faultEndTime: [{ validator: validFaultEndTime, required: true }],
      },
    };
  },

  watch: {
    "qualitativeForm.faultRecoveryTime": {
      immediate: false,
      // handler: function (newVal, oldVal) {
      handler(val) {
        // if (
        //   (newVal != null || newVal != "") &&
        //   oldVal != undefined &&
        //   newVal != oldVal
        // ) {
        // debugger
        this.computerFaultTreatmentTime();
        let valDate = val.split(" ")[0];
        let originDate = this.qualitativeForm.alarmCreateTime.split(" ")[0];
        let now = new Date();
        now.setMinutes(now.getMinutes() + 1, now.getSeconds(), 0);
        valDate = valDate.replace(/\-/g, "/");
        originDate = originDate.replace(/\-/g, "/");
        let year = now.getFullYear();
        let month = now.getMonth() + 1;
        let date = now.getDate();
        let nowDate =
          year + "/" + this.addZero(month) + "/" + this.addZero(date);
        let valDateUnix = Date.parse(valDate);
        let originDateUnix = Date.parse(originDate);
        let nowDateUnix = Date.parse(nowDate);

        let array = this.qualitativeForm.alarmCreateTime.split(" ");
        let createTime = array[1];
        let hour = now.getHours();
        let minute = now.getMinutes();
        let second = now.getSeconds();
        let nowTime =
          this.addZero(hour) +
          ":" +
          this.addZero(minute) +
          ":" +
          this.addZero(second);
        if (valDateUnix == originDateUnix && originDateUnix == nowDateUnix) {
          this.timeRange = createTime + " - " + nowTime;
          this.recoveryPicker.selectableRange = this.timeRange;
        } else if (
          valDateUnix == originDateUnix &&
          originDateUnix < nowDateUnix
        ) {
          this.timeRange = createTime + " - 23:59:59";
          this.recoveryPicker.selectableRange = this.timeRange;
        } else if (valDateUnix > originDateUnix && valDateUnix < nowDateUnix) {
          this.timeRange = "00:00:00 - 23:59:59";
          this.recoveryPicker.selectableRange = this.timeRange;
        } else if (valDateUnix > originDateUnix && valDateUnix == nowDateUnix) {
          this.timeRange = "00:00:00 - " + nowTime;
          this.recoveryPicker.selectableRange = this.timeRange;
        }
        // const time1 = new Date(
        //   this.qualitativeForm.alarmCreateTime.slice(0, 10) + " 00:00:00"
        // ).getTime();
        // const time2 = new Date(newVal.slice(0, 10) + " 00:00:00").getTime();
        //
        // console.log(getCurrentTime(Date.now()).slice(0, 10) + " 00:00:00");
        // const time3 = new Date(
        //   getCurrentTime(Date.now()).slice(0, 10) + " 00:00:00"
        // ).getTime();
        // const nowTimeStr = getCurrentTime(Date.now()).slice(-8);
        // if (time2 > time1) {
        //   this.recoveryPicker.selectableRange = `"00:00:00" - "23:59:59"`;
        // } else {
        //   this.recoveryPicker.selectableRange = `${this.recoveryDefaultTime} - "23:59:59"`;
        // }
        // if (time2 == time3) {
        //   this.recoveryPicker.selectableRange = `"00:00:00" - ${nowTimeStr}`;
        // }
        // }
      },
    },
  },
  async created() {
    this.formInit = JSON.parse(JSON.stringify(this.qualitativeForm));
    // 初始化 下拉框
    await this.getDictData("com_professional_type", "professionalList");
    // await this.getDictData("fault_level", "levelList");
    this.getDictData("device_type", "deviceTypeList");
    this.getDictData("fault_status", "faultStatusList");
    this.getDictData("site_offline_reason", "siteOfflineReasonList");
    this.levelList = [
      { dictName: "故障影响3级", dictCode: "3" },
      { dictName: "故障影响4级", dictCode: "4" },
      { dictName: "故障影响5级", dictCode: "5" },
      { dictName: "故障影响6级", dictCode: "6" },
      { dictName: "故障影响7级", dictCode: "7" },
      { dictName: "故障影响8级", dictCode: "8" },
    ];
    // 初始化 数据
    await this.getInit(this.common.woId);
    this.userData = JSON.parse(this.userInfo.attr2);
    // 获取地区，请求参数
    this.getOrgInfo();
  },
  methods: {
    addZero(s) {
      return s < 10 ? "0" + s : s;
    },
    //时间对比
    disabledDate(time) {
      let array = this.qualitativeForm.alarmCreateTime.split(" ");
      let createTimeDate = array[0];
      let now = moment(createTimeDate);
      let a = moment(now).subtract(1, "days");
      let today = moment(a).valueOf();
      return time.getTime() <= today || time.getTime() - 8.64e6 >= Date.now();
    },

    descTip(count, name, showName) {
      if (this.qualitativeForm[name].length >= count) {
        this[showName] = true;
        setTimeout(() => {
          this[showName] = false;
        }, this.showTipTime);
      } else {
        this[showName] = false;
      }
    },
    desc2Tip(count, name1, name2, showName) {
      if (this.qualitativeForm[name1][name2].length >= count) {
        this[showName] = true;
        setTimeout(() => {
          this[showName] = false;
        }, this.showTipTime);
      } else {
        this[showName] = false;
      }
    },
    // dealDisabledDate(time) {
    //   let self = this;
    //   let beginDate = moment(self.common.failureInformTime).format(
    //     "YYYY-MM-DD"
    //   );
    //   let endDate = moment(Date.now()).format("YYYY-MM-DD");
    //   let arr = this.getAllDays(beginDate, endDate);
    //   const timeFormat = moment(time).format("YYYY-MM-DD");
    //   if (arr.indexOf(timeFormat) >= 0) {
    //     return false;
    //   }
    //   return true;
    // },
    // dealDisabledDateDt(time) {
    //   let self = this;
    //   let beginDate;
    //   let endDate;
    //   if (self.qualitativeForm.faultEndTime) {
    //     beginDate = moment(self.common.failureInformTime).format("YYYY-MM-DD");
    //     endDate = moment(self.qualitativeForm.faultEndTime).format(
    //       "YYYY-MM-DD"
    //     );
    //   } else {
    //     beginDate = moment(self.common.failureInformTime).format("YYYY-MM-DD");
    //     endDate = moment(Date.now()).format("YYYY-MM-DD");
    //   }
    //   let arr = this.getAllDays(beginDate, endDate);
    //   const timeFormat = moment(time).format("YYYY-MM-DD");
    //   if (arr.indexOf(timeFormat) >= 0) {
    //     return false;
    //   }
    //   return true;
    // },
    getAllDays(begin_date, end_date) {
      const errArr = [],
        resultArr = [],
        dateReg = /^[2]\d{3}-[01]\d-[0123]\d$/;

      if (
        typeof begin_date !== "string" ||
        begin_date === "" ||
        !dateReg.test(begin_date)
      ) {
        return errArr;
      }

      if (
        typeof end_date !== "string" ||
        end_date === "" ||
        !dateReg.test(end_date)
      ) {
        return errArr;
      }

      try {
        const beginTimestamp = Date.parse(new Date(begin_date)),
          endTimestamp = Date.parse(new Date(end_date));

        // 开始日期小于结束日期
        if (beginTimestamp > endTimestamp) {
          return errArr;
        }

        // 开始日期等于结束日期
        if (beginTimestamp === endTimestamp) {
          resultArr.push(begin_date);
          return resultArr;
        }

        let tempTimestamp = beginTimestamp,
          tempDate = begin_date;

        // 新增日期是否和结束日期相等， 相等跳出循环
        while (tempTimestamp !== endTimestamp) {
          resultArr.push(tempDate);

          // 增加一天
          tempDate = moment(tempTimestamp).add(1, "d").format("YYYY-MM-DD");

          // 将增加时间变为时间戳
          tempTimestamp = Date.parse(new Date(tempDate));
        }

        // 将最后一天放入数组
        resultArr.push(end_date);
        return resultArr;
      } catch (err) {
        return errArr;
      }
    },
    getInit(woId, isInit) {
      this.formLoading = true;
      let param = {
        woId: woId,
        init: isInit ? true : null,
      };
      apiInitQualitative(param)
        .then(res => {
          if (res.status == "0") {
            // this.qualitativeForm = JSON.parse(JSON.stringify(res.data));
            this.initData = JSON.parse(JSON.stringify(res.data.fdbkForm));
            this.tjProfessionalTypeList = res.data.professionalTypeList;
            this.tjFaultReasonList = res.data.faultReasonList;
            this.tjFaultCateList = res.data.faultCateList;

            // 回显 - 定性信息
            Object.keys(this.qualitativeForm).forEach(item => {
              Object.keys(res.data.fdbkForm).forEach(i => {
                if (res.data.fdbkForm[i] !== null && item == i) {
                  this.qualitativeForm[item] = res.data.fdbkForm[i];
                }
              });
            });

            this.computerFaultTreatmentTime();
            // 云表部分字段赋值回显
            this.qualitativeForm.workItemId = this.workItemId;
            this.qualitativeForm.woId = this.common.woId;
            this.qualitativeForm.processInstId = this.common.processInstId;
            this.qualitativeForm.processDefId = this.common.processDefId;
            if (this.linkedSheetNoCopy != "") {
              this.qualitativeForm.linkedSheetNo = this.linkedSheetNoCopy;
            }

            this.dynamicTags[0] = this.qualitativeForm?.linkedSheetNo || "";
            if (this.qualitativeForm.professionalType == "无线网") {
              this.qualitativeForm.professionalTypeId = "7";
            } else {
              this.professionalList.forEach(item => {
                if (this.qualitativeForm.professionalType == item.dictName) {
                  this.qualitativeForm.professionalTypeId = item.dictCode;
                }
              });
            }
            this.handleData(res.data.fdbkForm);
          }
          this.formLoading = false;
        })
        .catch(error => {
          this.formLoading = false;

          console.log(error);
        });
    },
    handleData(fdbkForm) {
      /*
              专业信息
              —— 无线网、核心网、动环网、数据网、铁塔、接入网 pList01 IDC29 IP互联网33 核心网其他32
              —— 传输网 pList02
            */

      let checkProfessionalTypeId = this.checkProfessionalTypeId();
      if (checkProfessionalTypeId) {
        Object.keys(this.qualitativeForm.pList01).forEach(item => {
          Object.keys(fdbkForm).forEach(i => {
            if (fdbkForm[i] !== null && item == i) {
              this.qualitativeForm.pList01[item] = fdbkForm[i];
            }
          });
        });
      } else if (this.qualitativeForm.professionalTypeId == "3") {
        Object.keys(this.qualitativeForm.pList02).forEach(item => {
          Object.keys(fdbkForm).forEach(i => {
            if (fdbkForm[i] !== null && item == i) {
              this.qualitativeForm.pList02[item] = fdbkForm[i];
            }
          });
        });
      }

      this.handleData1(fdbkForm);
    },
    handleData1(fdbkForm) {
      if (fdbkForm.isEffectBusiness == null) {
        this.qualitativeForm.isEffectBusiness = "否";
      }
      if (fdbkForm.isSiteOffline == null) {
        this.qualitativeForm.isSiteOffline = "否";
      }
      if (fdbkForm.isTowerReason == null) {
        this.qualitativeForm.isTowerReason = "否";
      }
      if (fdbkForm.isSiteOffline == "是") {
        this.qualitativeForm.siteOfflineReason = fdbkForm.siteOfflineReason;
      }
      // 设置 时间组件，数据显示
      this.setRecoveryPicker();
      this.$nextTick(() => {
        this.$refs.qualitativeForm.clearValidate();
      });
    },
    getOrgInfo() {
      apiGetOrgInfo()
        .then(res => {
          if (res.status == "0") {
            this.qualitativeForm.areaCode = res?.data?.orgInfo?.areaCode ?? "";
            this.qualitativeForm.category = res?.data?.category ?? "";
            this.getFaultAreaOptions();
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    getFaultAreaOptions() {
      let param = {
        areaCode: this.qualitativeForm.areaCode,
        category: this.qualitativeForm.category,
      };
      apiGetFaultArea(param)
        .then(res => {
          if (res.status == "0") {
            this.faultRegionOptions = res?.data ?? [];
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    computerFaultGenerationAter() {
      let days = moment(
        this.qualitativeForm.lastClearTime,
        "YYYY-MM-DD HH:mm:ss"
      ).diff(
        moment(this.qualitativeForm.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
        "seconds"
      );
      this.qualitativeForm.lastClearDuration = days;
    },
    second2Time(days) {
      return this.showTimeNew(Math.abs(days));
    },
    computerFaultTreatmentTime() {
      let dealTime = moment(
        this.qualitativeForm.faultRecoveryTime,
        "YYYY-MM-DD HH:mm:ss"
      ).diff(
        moment(this.qualitativeForm.faultNoticeTime, "YYYY-MM-DD HH:mm:ss"),
        "seconds"
      );

      let recoveryTime = moment(
        this.qualitativeForm.faultRecoveryTime,
        "YYYY-MM-DD HH:mm:ss"
      ).diff(
        moment(this.qualitativeForm.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
        "seconds"
      );
      this.qualitativeForm.faultDuration = this.second2Time(dealTime);
      this.qualitativeForm.recoveryDuration = this.second2Time(recoveryTime);
    },
    checkProfessionalTypeId() {
      let ids = ["7", "1", "4", "29", "33", "32", "5", "9", "10"];
      return ids.indexOf(this.qualitativeForm.professionalTypeId) > -1;
    },
    handleSubmit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.qualitativeFullscreenLoading = true;
          this.$set(this.qualitativeForm, "actionName", "故障定性");
          //
          this.submitDeal();
        } else {
          return false;
        }
      });
    },
    submitDeal() {
      // 如果有附件，先上传附件
      let isUpload = false;
      let formData = new FormData();
      if (
        this.qualitativeForm.attachmentFile != "" &&
        this.importForm.attachmentFileList.length > 0
      ) {
        isUpload = true;
        for (let item of this.importForm.attachmentFileList) {
          formData.append("files", item.raw);
        }
      } else {
        isUpload = false;
        formData.append("files", "");
      }
      let paramter = {
        woId: this.common.woId,
        processInstId: this.common.processInstId,
        workItemId: this.common.workItemId,
        actionName: this.common.actionName,
      };
      // 如果是否基站退服，是否，清除退服原因
      if (
        this.qualitativeForm.isSiteOffline == "否" &&
        this.qualitativeForm.siteOfflineReason != undefined
      ) {
        delete this.qualitativeForm.siteOfflineReason;
      }
      let params = {};
      let checkProfessionalTypeId = this.checkProfessionalTypeId();
      if (checkProfessionalTypeId) {
        params = Object.assign(
          paramter,
          this.qualitativeForm,
          this.qualitativeForm.pList01
        );
      } else if (this.qualitativeForm.professionalTypeId == "3") {
        params = Object.assign(
          paramter,
          this.qualitativeForm,
          this.qualitativeForm.pList02
        );
      } else {
        params = Object.assign(paramter, this.qualitativeForm);
      }
      delete params.pList01;
      delete params.pList02;
      this.uploadFile(isUpload, formData, params);
    },
    uploadFile(isUpload, formData, params) {
      // 获取上传附件id
      if (isUpload) {
        apiGroupCode()
          .then(res => {
            if (res.status == 0) {
              let processId = res.data?.linkId;

              let uploadUrl = `/province/attach/upload?groupKey=${this.common.woId}&processId=${processId}&processNode=${this.common.processNode}&sheetCreateTime=${this.common.sheetCreateTime}`;
              apiFileUpload(uploadUrl, formData)
                .then(res => {
                  if (res.code == 200) {
                    params.linkId = processId;
                    this.submitFn(params);
                  } else {
                    this.$message.error("上传失败，请重新上传");
                    return false;
                  }
                })
                .catch(error => {
                  console.log(error);
                });
            }
          })
          .catch(error => {
            console.log(error);
          });
      } else {
        this.submitFn(params);
      }
    },

    submitFn(params) {
      apiActionPublic(params)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("提交定性成功");
            this.onReset();
            this.$emit("closeDialogQualitative", res.data);
          } else {
            this.$message.error(res.msg);
          }
          this.qualitativeFullscreenLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error(error.msg);
          this.qualitativeFullscreenLoading = false;
        });
    },
    closeAndDeleteFile(tag) {
      this.deleteFile(tag, "xl");
    },
    closeAndDeleteFileDl(tag) {
      this.deleteFile(tag, "dl");
    },
    deleteFile(tag, type) {
      let param = {
        attId: tag.id,
        linkId: this.qualitativeForm.linkId,
      };
      apiDeleteFdFile(param)
        .then(res => {
          if (res.status == "0") {
            if (type == "xl") {
              this.fdFileXlArr.splice(this.fdFileXlArr.indexOf(tag), 1);
              this.qualitativeForm.appendix = JSON.stringify(this.fdFileXlArr);
            } else if (type == "dl") {
              this.fdFileDlArr.splice(this.fdFileDlArr.indexOf(tag), 1);
              this.qualitativeForm.appendix = JSON.stringify(this.fdFileDlArr);
            }
            this.$message.success("附件删除成功");
          } else {
            this.$message.error("附件删除失败");
          }
        })
        .catch(error => {
          this.$message.error("附件删除失败");
          console.log(error);
        });
    },
    closeFile(tag) {
      this.importForm.relatedFilesFileList.splice(
        this.importForm.relatedFilesFileList.indexOf(tag),
        1
      );
    },

    dealTimeout() {
      //故障代通时间（无故障代通时间 用故障结束时间） - 故障开始时间
      if (this.qualitativeForm.lastClearTime) {
        let seconds = moment(
          this.qualitativeForm.lastClearTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.qualitativeForm.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        return seconds;
      } else {
        let seconds = moment(
          this.qualitativeForm.faultEndTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.qualitativeForm.alarmCreateTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        return seconds;
      }
    },
    onReset() {
      this.qualitativeForm = JSON.parse(JSON.stringify(this.formInit));
      this.$nextTick(() => {
        this.$refs.qualitativeForm.clearValidate();
      });
      this.getInit(this.common.woId);
      this.$refs.scroll.wrap.scrollTop = 0;
    },
    showTimeNew(val) {
      if (val) {
        if (val == 0) return "0秒";
        var time = "";
        var second = val % 60;
        var minute = parseInt(parseInt(val) / 60) % 60;
        time = second + "秒";
        if (minute >= 1) {
          time = minute + "分" + second + "秒";
        }
        var hour = parseInt(parseInt(val / 60) / 60) % 24;
        if (hour >= 1) {
          time = hour + "小时" + minute + "分" + second + "秒";
        }
        var day = parseInt(parseInt(parseInt(val / 60) / 60) / 24);
        if (day >= 1) {
          time = day + "天" + hour + "小时" + minute + "分" + second + "秒";
        }
        return time;
      } else {
        return "0秒";
      }
    },

    // 设置恢复时间，组件显示
    setRecoveryPicker() {
      // 故障恢复时间 >= 故障发生时间 且 故障恢复时间 <= 当前时间
      // this.recoveryPicker.disabledDate = this.disabledDate();
      // const alarmCreateTime = this.qualitativeForm.alarmCreateTime;
      // this.recoveryDefaultTime = getCurrentTime(
      //   new Date(alarmCreateTime).getTime() + 60 * 1000
      // ).slice(-8);
      // this.recoveryPicker = {
      //   disabledDate: time => {
      //     return (
      //       time.getTime() <
      //         new Date(alarmCreateTime).getTime() - 3600 * 1000 * 24 ||
      //       time.getTime() > new Date(Date.now()).getTime()
      //     );
      //   },
      //   selectableRange: `${this.recoveryDefaultTime} - "23:59:59"`,
      // };
    },

    getDictData(dictId, selectName) {
      this[selectName] = [];
      let param = {
        dictType: dictId,
      };

      apiDict(param)
        .then(res => {
          if (res.code == 200) {
            this[selectName] = res.data;
          }
        })
        .catch(error => {
          console.log(error);
          return false;
        });
    },

    // 专业类型——下拉枚举联动
    professionalTypeChange(val) {
      const { value, label } = val;
      this.qualitativeForm.professionalType = label;

      this.qualitativeForm.professionalTypeId = value;
      this.$set(
        this.qualitativeForm,
        this.qualitativeForm.professionalType,
        label
      );

      //无线网、核心网、动环网、数据网、铁塔、接入网、IDC29 IP互联网33
      let checkProfessionalTypeId = this.checkProfessionalTypeId();
      if (checkProfessionalTypeId) {
        this.qualitativeForm.pList01.faultCate = this.initData.faultCate;
        this.qualitativeForm.pList01.falutReason = this.initData.falutReason;
        this.qualitativeForm.pList01.vendor = "";
      } else if (this.qualitativeForm.professionalTypeId == "3") {
        //传输网
        this.qualitativeForm.pList02.faultStatus = this.initData.faultStatus;
        this.qualitativeForm.pList02.networkType = this.initData.networkType;
        this.qualitativeForm.pList02.faultCate = this.initData.faultCate;
        this.qualitativeForm.pList02.falutReason = this.initData.falutReason;
      }

      this.$nextTick(() => {
        this.$refs["qualitativeForm"].clearValidate();
      });
    },
    faultCateChange(val) {
      const { value, label } = val;

      let checkProfessionalTypeId = this.checkProfessionalTypeId();
      if (checkProfessionalTypeId) {
        this.qualitativeForm.pList01.faultCate = label;
        this.qualitativeForm.pList01.faultCateId = value;
        this.faultReasonList = [];
        this.qualitativeForm.pList01.faultReason = "";
        // 获取故障原因，下拉列表
        this.getDictData(
          this.qualitativeForm.professionalTypeId +
            "_" +
            this.qualitativeForm.pList01.faultCateId +
            "_faultReason",
          "faultReasonList"
        );
      } else if (this.qualitativeForm.professionalTypeId == "3") {
        //传输网
        this.qualitativeForm.pList02.faultCate = label;
        this.qualitativeForm.pList02.faultCateId = value;
        this.faultReasonList = [];
        this.qualitativeForm.pList02.faultReason = "";
        // 获取故障原因，下拉列表
        this.getDictData(
          this.qualitativeForm.professionalTypeId +
            "_" +
            this.qualitativeForm.pList02.faultCateId +
            "_faultReason",
          "faultReasonList"
        );
      }
      //获取无线网故障厂家下拉列表
      if (this.qualitativeForm.professionalTypeId == "7") {
        this.qualitativeForm.pList01.vendor = "";
      }
    },
    //附件选择
    attachmentBrowse() {
      this.attachmentDialogVisible = true;
    },
    changeFileData(data) {
      this.qualitativeForm.attachmentFile = data.fileName;
      this.importForm.attachmentFileList = data.attachmentFileList;
      this.attachmentDialogVisible = false;
    },
    closeAttachmentDialog() {
      this.attachmentDialogVisible = false;
    },
    handleTagClose(tag) {
      this.dynamicTags = [];
      this.qualitativeForm.linkedSheetNo = "";
      this.linkedSheetNoCopy = "";
      this.getInit(this.common.woId, true);
      this.relativeDisabled = false;
    },
    submitRelativeOrder(tableSelect) {

      this.relativeOrderDialogVisible = false;
      this.dynamicTags = [tableSelect[0].sheetNo];
      this.qualitativeForm.linkedSheetNo = tableSelect[0].sheetNo;
      this.linkedSheetNoCopy = tableSelect[0].sheetNo;
      // 调用接口，回填关联工单新信息
      this.getInit(tableSelect[0].woId);
      this.relativeDisabled = true;
      // let param = {
      //   woId: this.common.woId,
      // };
      // apiInitQualitative(param)
      //   .then(resp => {
      //     if (resp.status == 0) {
      //       this.relativeDisabled = true;

      //       let relativeData = resp.data.fdbkForm;
      //       this.qualitativeForm.professionalType =
      //         relativeData.professionalType;
      //       this.qualitativeForm.isEffectBusiness =
      //         relativeData.isEffectBusiness;

      //       this.qualitativeForm.isSiteOffline = relativeData.isSiteOffline;
      //       this.qualitativeForm.isTowerReason = relativeData.isTowerReason;
      //       this.handleData(relativeData);
      //     }
      //   })
      //   .catch(err => {
      //     console.log(err);
      //   });
    },
    closeRelativeOrder() {
      this.relativeOrderDialogVisible = false;
    },
  },
};
</script>
<style lang="scss" scoped>
.qualitative {
  height: 500px;
  .cus-card ::v-deep .el-card__header {
    padding: 11px 24px 10px;
    font-weight: 400;
    @include themify {
      background-color: themed("$--background-color-base");
    }
  }
  .text-hide {
    display: inline-block;
    white-space: nowrap; /* 防止文本换行 */
    overflow: hidden; /* 隐藏溢出的文本 */
    text-overflow: ellipsis; /* 显示省略号 */
    width: 130px; /* 设置容器宽度 */
    text-decoration: none;
    color: #b50b14;
  }
}
.custom-theme-default {
  .el-input-group__append button.append-btn {
    height: 32px;
    background: #b50b14;
    color: #fff;
    border: 1px solid #b50b14;
  }
}
</style>
<style>
.custom-theme-default .el-tag .el-icon-close {
  top: -7px !important;
}
.relative-tag {
  position: absolute;
  top: 4px;
  left: 4px;
}
</style>
