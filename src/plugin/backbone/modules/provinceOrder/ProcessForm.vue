<template>
  <head-fixed-layout class="draft-eidt-page">
    <template #header>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="15" :md="18" class="head-title"
          >省分故障工单拟稿</el-col
        >
        <el-col :xs="24" :sm="9" :md="6" class="head-handle-wrap">
          <el-button
            type="primary"
            @click="onSheetSave"
            v-loading.fullscreen.lock="sheetSaveLoading"
            >保存</el-button
          >
          <el-button
            type="primary"
            @click="onSheetSubmit"
            v-loading.fullscreen.lock="sheetCommitLoading"
            >提交</el-button
          >
        </el-col>
      </el-row>
    </template>

    <el-form
      :model="sheetForm"
      ref="sheetForm"
      :rules="sheetFormRules"
      label-width="130px"
      label-position="right"
    >
      <el-card shadow="always" :body-style="{ padding: '10px' }">
        <div slot="header" class="card-title">
          <span>工单基本信息</span>
        </div>
        <el-row :gutter="10" type="flex" style="flex-wrap: wrap">
          <el-col :xs="24" :sm="24" :md="24" :offset="0">
            <el-form-item label="工单主题:" prop="sheetTitle">
              <el-input
                v-model="sheetForm.sheetTitle"
                placeholder="请输入内容"
                clearable
                style="width: 100%"
                maxlength="400"
                show-word-limit
                @keyup.native="descTip(400,'sheetTitle','showgdzt')"
              >
              </el-input>
              <div class="el-form-item__error"  v-if="showgdzt">已超过填写字数上限</div>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="建单时间:" prop="sheetCreateTime">
              <el-input v-model="sheetForm.sheetCreateTime" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="建单人:" prop="senderName">
              <el-input
                v-model="sheetForm.senderName"
                placeholder="请输入建人"
                readonly
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="建单人部门:" prop="deptName">
              <el-input
                v-model="sheetForm.deptName"
                placeholder="请输入建单部门"
                readonly
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="10" :md="8" :offset="0">
            <el-form-item label="信息来源:" required>
              <el-select
                v-model="sheetForm.createType"
                placeholder="请选择内容"
                style="width: 100%"
                disabled
              >
                <el-option
                  v-for="(item, i) in createTypeList"
                  :key="i"
                  :label="item.dictName"
                  :value="item.dictCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="归属地市:" prop="regionName">
              <el-select style="width: 100%" v-model="sheetForm.regionName">
                <el-option
                  v-for="(item, i) in faultRegionOptions"
                  :key="i"
                  :label="item.name"
                  :value="item.name"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="发生时间:" prop="alarmCreateTime">
              <el-date-picker
                v-model="sheetForm.alarmCreateTime"
                type="datetime"
                placeholder="请选择时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
                style="width: 100%"
                :picker-options="pickerOptions"
              />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="紧急程度:" prop="emergencyLevel" required>
              <el-radio-group v-model="sheetForm.emergencyLevel">
                <el-radio
                  v-for="item in urgentOption"
                  :key="item.label"
                  :label="item.label"
                  >{{ item.value }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="预估处理时限:" prop="processTimeLimit">
              <el-radio-group v-model="sheetForm.processTimeLimit">
                <el-radio
                  v-for="item in estimatedProTimeLimitOption"
                  :key="item.label"
                  :label="item.label"
                  >{{ item.value }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item
              label="受理时限(分钟):"
              prop="acceptTimeLimit"
              required
            >
              <el-input
                v-model="sheetForm.acceptTimeLimit"
                placeholder="请输入受理时限"
                clearable
                style="width: 100%"
                maxlength="5"
                @keyup.native="descTip(5,'acceptTimeLimit','showslsx')"
              ></el-input>
              <div class="el-form-item__error"  v-if="showslsx">已超过填写字数上限</div>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="所属专业:" prop="professionalType" required>
              <el-select
                v-model="sheetForm.professionalType"
                placeholder="请选择内容"
                style="width: 100%"
                @change="professionalTypeChange"
              >
                <el-option
                  v-for="(item, i) in professionalTypeList"
                  :key="i"
                  :label="item.dictName"
                  :value="item.dictCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="网络类型:" prop="networkType">
              <el-select
                v-model="sheetForm.networkType"
                placeholder="请选择"
                style="width: 100%"
                @focus="
                  getDictData(
                    sheetForm.professionalType + '_pro_network_type',
                    'networkTypeList'
                  )
                "
              >
                <el-option
                  v-for="(item, i) in networkTypeList"
                  :key="i"
                  :label="item.dictName"
                  :value="item.dictCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="业务中断:" prop="businessDown">
              <el-radio-group v-model="sheetForm.businessDown">
                <el-radio :label="'是'">是</el-radio>
                <el-radio :label="'否'">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
<!--          <el-col :xs="24" :sm="8" :md="8" :offset="0">-->
<!--            <el-form-item label="预判故障等级:" prop="faultLevel">-->
<!--              <el-select-->
<!--                v-model="sheetForm.faultLevel"-->
<!--                placeholder="请选择内容"-->
<!--                style="width: 100%"-->
<!--              >-->
<!--                <el-option-->
<!--                  v-for="(item, i) in faultLevelList"-->
<!--                  :key="i"-->
<!--                  :label="item.dictName"-->
<!--                  :value="item.dictCode"-->
<!--                >-->
<!--                </el-option>-->
<!--              </el-select>-->
<!--            </el-form-item>-->
<!--          </el-col>-->
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="经度:" prop="longitude">
              <el-input
                v-model="sheetForm.longitude"
                placeholder="请输入内容"
                style="width: 100%"
                maxlength="10"
                @keyup.native="descTip(10,'longitude','showjd')"
              ></el-input>
              <div class="el-form-item__error"  v-if="showjd">已超过填写字数上限</div>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="纬度:" prop="latitude">
              <el-input
                v-model="sheetForm.latitude"
                placeholder="请输入内容"
                style="width: 100%"
                maxlength="10"
                @keyup.native="descTip(10,'latitude','showwd')"
              ></el-input>
              <div class="el-form-item__error"  v-if="showwd">已超过填写字数上限</div>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="工单优先级:" prop="faultLevel" required>
              <el-select
                v-model="sheetForm.faultLevel"
                placeholder="请选择内容"
                style="width: 100%"
              >
                <el-option
                  v-for="(item, i) in woPriorityList"
                  :key="i"
                  :label="item.dictName"
                  :value="item.dictCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0" v-if="sheetForm.professionalType == 7">
            <el-form-item label="覆盖场景:"
                          prop="coverScene">
              <el-select
                v-model="sheetForm.coverScene"
                filterable
                placeholder="请选择内容"
                style="width: 100%"
              >
                <el-option
                  v-for="(item, i) in coverSceneList"
                  :key="i"
                  :label="item.dictName"
                  :value="item.dictCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="sheetForm.professionalType == 7?16:24" :md="sheetForm.professionalType == 7?16:24" :offset="0">
            <el-form-item label="故障现象:" prop="faultPhenomenon" required>
              <el-input
                v-model="sheetForm.faultPhenomenon"
                placeholder="请输入内容"
                clearable
                style="width: 100%"
                maxlength="3000"
                @keyup.native="descTip(3000,'faultPhenomenon','showgzxx')"
                show-word-limit
              >
              </el-input>
              <div class="el-form-item__error"  v-if="showgzxx">已超过填写字数上限</div>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="物业联系人:" prop="propertyUser">
              <el-input
                v-model="sheetForm.propertyUser"
                placeholder="请输入内容"
                clearable
                style="width: 100%"
                maxlength="255"
                @keyup.native="descTip(255,'propertyUser','showwylxr')"
              >
              </el-input>
              <div class="el-form-item__error"  v-if="showwylxr">已超过填写字数上限</div>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="物业联系电话:" prop="propertyPhone">
              <el-input
                v-model="sheetForm.propertyPhone"
                placeholder="请输入内容"
                clearable
                style="width: 100%"
                maxlength="255"
                @keyup.native="descTip(255,'propertyPhone','showwylxrdh')"
              >
              </el-input>
              <div class="el-form-item__error"  v-if="showwylxrdh">已超过填写字数上限</div>
            </el-form-item>
          </el-col>
          <!-- <el-col :xs="24" :sm="24" :md="24" :offset="0">
            <el-form-item label="附件:" prop="attachmentFile">
              <el-input
                v-model="sheetForm.attachmentFile"
                placeholder="添加附件"
                clearable
                style="width: 100%"
              >
                <el-button type="info" slot="append" @click="attachmentBrowse"
                  >+</el-button
                >
              </el-input>
            </el-form-item>
          </el-col> -->
          <el-col :span="24" :offset="0">
            <el-form-item label="备注:" prop="falutComment">
              <el-input
                v-model="sheetForm.falutComment"
                placeholder="请输入内容"
                clearable
                style="width: 100%"
                show-word-limit
                maxlength="255"
                @keyup.native="descTip(255,'falutComment','showbz')"
              >
              </el-input>
              <div class="el-form-item__error"  v-if="showbz">已超过填写字数上限</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card
        v-if="
          sheetForm.professionalType == 7 ||
          sheetForm.professionalType == 3 ||
          sheetForm.professionalType == 10
        "
        shadow="always"
        :body-style="{ padding: '10px' }"
        style="margin-top: 10px"
      >
        <div slot="header" class="card-title">
          <span>专业故障信息</span>
        </div>
        <!-- 无线网 -->
        <template v-if="sheetForm.professionalType == 7">
          <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
            <el-col :span="8">
              <el-form-item label="基站名称:">
                <el-input
                  v-model="sheetForm.bsName"
                  placeholder="请输入内容"
                  style="width: 100%"
                  maxlength="255"
                  @keyup.native="descTip(255,'bsName','showjzmc')"
                >
                </el-input>
                <div class="el-form-item__error"  v-if="showjzmc">已超过填写字数上限</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="基站级别:">
                <el-input
                  v-model="sheetForm.bsLevel"
                  placeholder="请输入内容"
                  style="width: 100%"
                  maxlength="255"
                  @keyup.native="descTip(255,'bsLevel','showjzjb')"
                >
                </el-input>
                <div class="el-form-item__error"  v-if="showjzjb">已超过填写字数上限</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="基站编号:">
                <el-input
                  v-model="sheetForm.bsCode"
                  placeholder="请输入内容"
                  style="width: 100%"
                  maxlength="255"
                  @keyup.native="descTip(255,'bsCode','showjzbh')"
                >
                </el-input>
                <div class="el-form-item__error"  v-if="showjzbh">已超过填写字数上限</div>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="基站地址:" prop="bsAddress">
                <el-input
                  type="textarea"
                  :rows="2"
                  placeholder="请输入内容"
                  v-model="sheetForm.falutReasonDesc"
                  style="width: 100%"
                  show-word-limit
                  maxlength="255"
                  @keyup.native="descTip(255,'falutReasonDesc','showjzdz')"
                >
                </el-input>
                <div class="el-form-item__error"  v-if="showjzdz">已超过填写字数上限</div>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="规模退服:">
                <el-input
                  type="textarea"
                  :rows="2"
                  placeholder="请输入内容"
                  v-model="sheetForm.scaleService"
                  style="width: 100%"
                  show-word-limit
                  maxlength="255"
                  @keyup.native="descTip(255,'scaleService','showgmtf')"
                >
                </el-input>
                <div class="el-form-item__error"  v-if="showgmtf">已超过填写字数上限</div>
              </el-form-item>
            </el-col>
          </el-row>
        </template>

        <!-- 传输网 -->
        <template v-if="sheetForm.professionalType == 3">
          <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
            <el-col :span="8">
              <el-form-item label="接口速率:">
                <el-input
                  placeholder="请输入内容"
                  v-model="sheetForm.portRate"
                  style="width: 100%"
                  maxlength="255"
                  @keyup.native="descTip(255,'portRate','showjksl')"
                >
                </el-input>
                <div class="el-form-item__error"  v-if="showjksl">已超过填写字数上限</div>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="原因初步分析:">
                <el-input
                  type="textarea"
                  :rows="2"
                  placeholder="请输入内容"
                  v-model="sheetForm.analysis"
                  style="width: 100%"
                  show-word-limit
                  maxlength="500"
                  @keyup.native="descTip(500,'analysis','showyycbfx')"
                >
                </el-input>
                <div class="el-form-item__error"  v-if="showyycbfx">已超过填写字数上限</div>
              </el-form-item>
            </el-col>
          </el-row>
        </template>
        <!-- 接入网 -->
        <template v-if="sheetForm.professionalType == 10">
          <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
            <el-col :span="24">
              <el-form-item label="共性问题:">
                <el-input
                  placeholder="请输入内容"
                  v-model="sheetForm.commonProblems"
                  style="width: 100%"
                  show-word-limit
                  maxlength="255"
                  @keyup.native="descTip(255,'commonProblems','showgxwt')"
                >
                </el-input>
                <div class="el-form-item__error"  v-if="showgxwt">已超过填写字数上限</div>
              </el-form-item>
            </el-col>
          </el-row>
        </template>
      </el-card>
      <el-card
        shadow="always"
        :body-style="{ padding: '10px' }"
        style="margin-top: 10px"
      >
        <div slot="header" class="card-title">
          <span>工单派送信息</span>
        </div>
        <el-row :gutter="10" type="flex" style="flex-wrap: wrap">
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="主送:" prop="agentMan">
              <el-input
                v-model="sheetForm.agentMan"
                placeholder="添加人员"
                style="width: 100%"
                readonly
              >
                <el-button
                  type="info"
                  slot="append"
                  icon="el-icon-user"
                  @click="onOpenPeopleDialog('lordSentDetermine')"
                ></el-button>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="次送:" prop="acceptMan">
              <el-input
                v-model="sheetForm.acceptMan"
                placeholder="添加人员"
                style="width: 100%"
                readonly
              >
                <el-button
                  type="info"
                  slot="append"
                  icon="el-icon-user"
                  @click="onOpenPeopleDialog('secondDetermine')"
                ></el-button>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="抄送:" prop="copyMan">
              <el-input
                v-model="sheetForm.copyMan"
                placeholder="添加人员"
                style="width: 100%"
                readonly
                clearable
                maxlength="0"
              >
                <span
                  slot="suffix"
                  v-show="sheetForm.copyMan"
                  @click="btnClearable('copyMan')"
                >
                  <i
                    class="el-icon-circle-close"
                    style="margin-left: 5px; margin-top: 8px; cursor: pointer"
                  ></i>
                </span>
                <el-button
                  type="info"
                  slot="append"
                  icon="el-icon-user"
                  @click="onOpenPeopleDialog('ccDetermine')"
                ></el-button>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8" :md="8" :offset="0">
            <el-form-item label="是否通知他人:" prop="isSendSms">
              <el-radio-group v-model="sheetForm.isSendSms">
                <el-radio :label="'1'">是</el-radio>
                <el-radio :label="'0'">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col
            :xs="24"
            :sm="16"
            :md="16"
            :offset="0"
            v-if="sheetForm.isSendSms == '1'"
          >
            <el-form-item
              label="接收人:"
              prop="smsToUsername"
              :rules="[
                {
                  required: sheetForm.isSendSms == '1' ? true : false,
                },
              ]"
            >
              <el-input
                v-model="sheetForm.smsToUsername"
                placeholder="添加人员"
                style="width: 100%"
                readonly
              >
                <el-button
                  type="info"
                  slot="append"
                  icon="el-icon-user"
                  @click="onOpenPeopleDialog('smsToUsername')"
                ></el-button>
              </el-input>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :offset="0" v-if="sheetForm.isSendSms == '1'">
            <el-form-item
              label="发送内容:"
              prop="sendContent"
              :rules="[
                {
                  required: sheetForm.isSendSms == '1' ? true : false,
                  message: '请输入内容',
                  trigger: 'blur',
                },
              ]"
            >
              <el-input
                v-model="sheetForm.sendContent"
                placeholder="请输入内容"
                type="textarea"
                :rows="4"
                clearable
                style="width: 100%"
                show-word-limit
                maxlength="1000"
                @keyup.native="descTip(1000,'sendContent','showfsnr')"
              ></el-input>
              <div class="el-form-item__error"  v-if="showfsnr">已超过填写字数上限</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
    </el-form>

<!--    <dia-orgs-user-tree-other-->
<!--      :title="diaPeople.title"-->
<!--      :visible.sync="diaPeople.visible"-->
<!--      :showOrgsTree="false"-->
<!--      :multiple-select-enable="diaPeople.multipleSelectEnable"-->
<!--      :single-select-tip="diaPeople.singleSelectTip"-->
<!--      :professionalType="mainProfessionalType"-->
<!--      @on-save="onSavePeople"-->
<!--      :appendToBody="true"-->
<!--    />-->

    <dia-tissue-tree
      v-if="isDiaOrgsUserTree"
      :title="diaPeople.title"
      :visible.sync="diaPeople.visible"
      :showOrgsTree="false"
      :multiple-select-enable="diaPeople.multipleSelectEnable"
      :single-select-tip="diaPeople.singleSelectTip"
      :professionalType="mainProfessionalType"
      @on-save="onSavePeople"
      :appendToBody="true"
      :userAttribution="userAttribution"
      :show-contact-user-tab="true"
      :show-contact-org-tab="false"
    />
    <el-dialog
      width="420px"
      title="附件选择"
      :visible.sync="attachmentDialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <file-upload
        @change="changeFileData"
        @cancel="closeAttachmentDialog"
      ></file-upload>
    </el-dialog>
  </head-fixed-layout>
</template>

<script>
import { mapGetters } from "vuex";
import moment from "moment";
import HeadFixedLayout from "./../workOrder/components/HeadFixedLayout.vue";
import FileUpload from "./../workOrder/components/FileUpload.vue";

import { apiGetOrgInfo, getCurrentTime } from "./../workOrder/api/CommonApi";
import {
  apiInitOrderDraf,
  apiGetFaultArea,
  apiDict,
  apiGroupCode,
  apiFileUpload,
  apiGetId,
} from "./workOrderWaitDetail/api/CommonApi";
import { apiProvinceBuild } from "./../workOrder/api/WorkOrderDraftEdit";
import DiaTissueTree from "@/plugin/backbone/components/common/DiaTissueTree";
export default {
  name: "WorkOrderDraftEdit",
  components: {
    HeadFixedLayout,
    FileUpload,
    DiaTissueTree,
  },

  data() {
    var validHappenTime = (rule, value, callback) => {
      if (value === "" || value === null) {
        callback(new Error("请选择发生时间"));
      } else {
        let seconds = moment(
          this.sheetForm.sheetCreateTime,
          "YYYY-MM-DD HH:mm:ss"
        ).diff(
          moment(this.sheetForm.happenTime, "YYYY-MM-DD HH:mm:ss"),
          "seconds"
        );
        if (seconds < 0) {
          callback(new Error("“发生时间”不能晚于“建单时间”，请重新选择"));
        } else {
          callback();
        }
      }
    };
    var validRecipient = (rule, value, callback) => {
      if (
        this.sheetForm.isSendSms == "1" &&
        this.sheetForm.recipientDetailUserName == ""
      ) {
        callback(new Error("请选择短信通知人"));
      } else {
        callback();
      }
    };
    return {
      showgdzt:false,
      showslsx:false,
      showjd:false,
      showwd:false,
      showwylxr:false,
      showwylxrdh:false,
      showgzxx:false,
      showbz:false,
      showjksl:false,
      showyycbfx:false,
      showjzmc:false,
      showjzjb:false,
      showjzbh:false,
      showjzdz:false,
      showgmtf:false,
      showgxwt:false,
      showfsnr:false,
      showTipTime:5000,
      userAttribution: "poNigao",
      isDiaOrgsUserTree: false,
      sheetForm: {

        coverScene: "", //覆盖场景
        faultLevel:"6",//工单优先级
        woId: "", //工单id
        linkId: "",
        sender: "", //建单人的登录名
        senderName: "", //建单人真实姓名
        deptName: "", // 创建人部门
        sheetTitle: "", //工单主题
        regionName:'',
        sheetCreateTime: getCurrentTime(Date.now()), // 建单时间
        alarmCreateTime: getCurrentTime(Date.now()), // 发生时间默认
        acceptTimeLimit: "30", // 受理时限默认
        emergencyLevel:'3',//默认
        processTimeLimit:'8',
        businessDown:'是',
        faultPhenomenon:'',
        agentMan: "", //主送人
        acceptMan: "", //次送人
        copyMan: "",//抄送人
        professionalType: "",
        networkType:'',
        createType: "",
        smsToUsername: "", //短信通知人
        isSendSms: 0,
        sendContent:
          "1、光缆：**， ××××光缆在××省××（网元）与××（网元）之间中断，影响××××波分系统，承载的SDH系统保护倒换，已经派单给省公司，并电话通知处理，现在该省正在利用备用纤芯倒接;(该段落OLP正常切换;)(分公司正在协调处理;)2、设备：××， ××××系统中断，初步怀疑是××网元××板卡问题，承载的业务目前不受影响，已经派单给省公司，并电话通知处理，现在分公司正在利用备用波道倒接；(该系统承载的SDH系统正常倒换，业务不受影响；)(该系统承载的SDH系统倒换不正常，已经影响业务；)(影响的业务有（无）××重保电路。)",
      },
      createTypeList: [], // 信息来源 下拉数据
      professionalTypeList: [], // 专业 下拉数据
      networkTypeList: [], // 网络类型 下拉数据
      faultLevelList: [], // 故障级别 下拉数据
      woPriorityList: [], // 工单优先级 下拉数据
      coverSceneList: [], // 覆盖场景 下拉数据
      urgentOption: [
        {
          label: "0",
          value: "一般",
        },
        {
          label: "3",
          value: "紧急",
        },
      ],
      sheetFormRules: {
        sheetTitle: [{ required: true, message: "工单主题不能为空" }],
        createType: [{ required: true, message: "请选择信息来源" }],
        alarmCreateTime: [{ validator: validHappenTime, required: true }],
        emergencyLevel: [{ required: true, message: "请选择紧急程度" }],
        acceptTimeLimit: [{ required: true, message: "请输入受理时限" }],
        professionalType: [{ required: true, message: "请选择所属专业" }],
        businessDown: [{ required: true, message: "请选择" }],
        faultPhenomenon: [{ required: true, message: "请输入故障现象" }],
        agentMan: [{ required: true, message: "主送人不能为空" }],
        acceptMan: [{ required: true, message: "次送人不能为空" }],
        smsToUsername: [{ validator: validRecipient }],
        processTimeLimit: [{ required: true, message: "请选择" }],
        alarmRegion: [{ required: true, message: "请选择" }],
        regionName: [{ required: true, message: "请选择" }],
      },
      diaPeople: {
        visible: false,
        title: "",
        saveName: "",
        saveTitleMap: {
          lordSentDetermine: "建单人主送",
          secondDetermine: "建单人次送",
          ccDetermine: "建单人抄送",
          // notifierDetermine: "通知人选择",
          // recipientDetermine: "接收人选择",
          smsToUsername: "接收人选择",
        },
        multipleSelectEnable:true,
        singleSelectTip:'',
        showUsersTree: true,
        showContactUserTab: false,
        showContactUserTabMap: {
          // recipientDetermine: true,
        },
      },
      attachmentDialogVisible: false,
      importForm: {
        //附件
        attachmentFileList: [],
      },
      sheetCommitLoading: false,
      sheetSaveLoading: false,
      incidenceDialogVisible: false,
      orgInfoData: {}, //组织信息数据缓存
      route: null,
      userInfoData: {},
      areaCode: "",
      category: "",
      faultRegionOptions: [],
      //预估处理时限
      estimatedProTimeLimitOption: [
        { label: "8", value: "8小时" },
        { label: "72", value: "72小时" },
      ],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
    ...mapGetters(["token"]),
    mainProfessionalType() {
      return this.$route.query.mainProfessionalType;
    },
  },
  watch: {
    "sheetForm.emergencyLevel": {
      handler: function (val) {
        if (val == "0") {
          this.sheetForm.processTimeLimit = "72";
        } else if (val == "3") {
          this.sheetForm.processTimeLimit = "8";
        }
      },
    },
  },
  async created() {
    this.route = this.$route;
    await this.getDictData("create_type", "createTypeList");
    this.sheetForm.createType = "2"; // 电子运维新建
    this.getDictData("com_professional_type", "professionalTypeList");
    this.woPriorityList = [
      {dictName:'故障影响3级',dictCode:'3'},
      {dictName:'故障影响4级',dictCode:'4'},
      {dictName:'故障影响5级',dictCode:'5'},
      {dictName:'故障影响6级',dictCode:'6'},
      {dictName:'故障影响7级',dictCode:'7'},
      {dictName:'故障影响8级',dictCode:'8'}];
    //覆盖场景枚举待修改
    this.getDictData("cover_scene", "coverSceneList");
  },
  mounted() {
    this.sheetForm.special = this.mainProfessionalType == "IT云" ? "23" : "3";
    this.userInfoData = JSON.parse(this.userInfo.attr2);
    this.sheetForm.sheetCreateTime = moment(Date.now()).format(
      "YYYY-MM-DD HH:mm:ss"
    );
    this.sheetForm.happenTime = moment(Date.now()).format(
      "YYYY-MM-DD HH:mm:ss"
    );
    // 初始化 信息来源
    this.getOrgInfo();
    if (this.$route.query?.orderType == "caogao") {
      this.sheetForm.woId = this.$route.query.woId;
      this.initOrder();
    } else {
      apiGetId()
        .then(res => {
          if (res.status == "0") {
            this.sheetForm.woId = res.data.linkId;
          }
        })
        .catch(error => {
          console.log(error);
        });
    }
    // 故障级别
    apiDict({ dictType: "fault_level" })
      .then(res => {
        if (res.code == 200) {
          this.faultLevelList = res.data;
        }
      })
      .catch(error => {
        console.log(error);
        return false;
      });
    window["__process_response_from_getDevice"] = value => {
      let objStr = "";
      let result = JSON.parse(value);
      let objArray = JSON.parse(result?.system);
      objArray.forEach(obj => {
        objStr += obj.sysName + ",";
      });
      if (objStr) {
        objStr = objStr.substr(0, objStr.length - 1);
      }

      this.sheetForm.title += this.sheetForm.title ? "," + objStr : objStr;
    };
  },
  methods: {
    descTip(count,name,showName){
      if (this.sheetForm[name].length>=count){
        this[showName] = true;
        setTimeout(() => {
          this[showName] = false;
        }, this.showTipTime);
      }
      else{
        this[showName] = false;
      }
    },
    // 回显
    initOrder() {
      // url 中 获取 sheetNo 参数
      const url = window.location.href;
      const queryStri = url.substring(url.indexOf("?") + 1).split("&");
      let sheetNoUrl = "";
      queryStri.forEach(item => {
        if (item.indexOf("sheetNo") >= 0) {
          sheetNoUrl = decodeURI(item.split("=")[1]);
        }
      });
      let params = {
        woId: this.$route.query.woId,
        sheetNo: this.$route.query.sheetNo || sheetNoUrl,
      };
      apiInitOrderDraf(params)
        .then(async res => {
          if (res.status == "0") {
            let resData = Object.assign(
              res.data.sheetInfo,
              res.data.provinceSheetInfo
            );
            this.sheetForm = resData;
            // 获取网络类型
            await this.getDictData(
              this.sheetForm.professionalType + "_pro_network_type",
              "networkTypeList"
            );
            this.sheetForm.networkType = resData.networkType;
            if (this.sheetForm.sendContent == "") {
              this.sheetForm.sendContent =
                "1、光缆：**， ××××光缆在××省××（网元）与××（网元）之间中断，影响××××波分系统，承载的SDH系统保护倒换，已经派单给省公司，并电话通知处理，现在该省正在利用备用纤芯倒接;(该段落OLP正常切换;)(分公司正在协调处理;)2、设备：××， ××××系统中断，初步怀疑是××网元××板卡问题，承载的业务目前不受影响，已经派单给省公司，并电话通知处理，现在分公司正在利用备用波道倒接；(该系统承载的SDH系统正常倒换，业务不受影响；)(该系统承载的SDH系统倒换不正常，已经影响业务；)(影响的业务有（无）××重保电路。)";
            }
            this.$nextTick(() => {
              this.$refs.sheetForm.clearValidate();
            });
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    getOrgInfo() {
      apiGetOrgInfo()
        .then(res => {
          if (res.status == "0") {
            this.orgInfoData = res.data;
            this.sheetForm.senderName = res.data.trueName;
            this.sheetForm.deptName = res?.data?.orgInfo?.orgName ?? "";
            this.sheetForm.deptId = res?.data?.orgInfo?.orgId ?? "";
            this.sheetForm.senderDept = res?.data?.orgInfo?.orgId ?? "";
            this.sheetForm.sender = res?.data?.userName ?? "";

            this.areaCode = res?.data?.orgInfo?.areaCode ?? "";
            this.category = res?.data?.category ?? "";
            this.getFaultAreaOptions();
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    getFaultAreaOptions() {
      let param = {
        areaCode: this.areaCode,
        category: this.category,
      };
      apiGetFaultArea(param)
        .then(res => {
          if (res.status == "0") {
            this.faultRegionOptions = res?.data ?? [];
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    onSheetSave() {
      if (this.sheetForm.sheetTitle == null || this.sheetForm.sheetTitle == ''){
        this.$message.warning('请填写主题后再做保存');
        return false;
      }

      if (this.sheetForm.acceptTimeLimit == null || this.sheetForm.acceptTimeLimit == ''){
        this.$message.warning('受理时限不能为空');
        return false;
      }
      // else{
        let self = this;
        this.$confirm("是否保存到草稿？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            this.sheetFormRules.agentMan = [{ required: false }];
            this.sheetFormRules.acceptMan = [{ required: false }];
            // this.$refs.sheetForm.validate(valid => {
            //   if (valid) {
                let formData = new FormData();

                this.sheetSaveLoading = true;
                let params = {};

                params.sheetInfo = JSON.parse(JSON.stringify(this.sheetForm));
                if (params.sheetInfo.isSendSms == "0") {
                  params.sheetInfo.smsToUsername = "";
                  params.sheetInfo.sendContent = "";
                }

                params.sheetInfo.sheetStatus = 1; //1是草稿 2是提交到待办
                // 如果有附件，先上传附件
                let isUpload = false;
                if (
                  this.sheetForm.attachmentFile != "" &&
                  this.importForm.attachmentFileList.length > 0
                ) {
                  isUpload = true;
                  for (let item of this.importForm.attachmentFileList) {
                    formData.append("files", item.raw);
                  }
                } else {
                  isUpload = false;
                  formData.append("files", "");
                }
                let saveFormData = new FormData();

                // 获取上传附件id
                if (isUpload) {
                  apiGroupCode()
                    .then(res => {
                      if (res.status == 0) {
                        let processId = res.data?.linkId;

                        let uploadUrl = `/province/attach/upload?groupKey=${this.sheetForm.woId}&processId=${processId}&processNode=拟稿派单&sheetCreateTime=${self.sheetForm.sheetCreateTime}`;
                        apiFileUpload(uploadUrl, formData)
                          .then(res => {
                            if (res.code == 200) {
                              params.sheetInfo.linkId = processId;

                              saveFormData.append(
                                "jsonParam",
                                JSON.stringify(params)
                              );
                              this.submitSave(saveFormData);
                            } else {
                              this.$message.error("上传失败，请重新上传");
                              return false;
                            }
                          })
                          .catch(error => {
                            console.log(error);
                          });
                      }
                    })
                    .catch(error => {
                      console.log(error);
                    });
                } else {
                  saveFormData.append("jsonParam", JSON.stringify(params));
                  this.submitSave(saveFormData);
                }
              // }
              // else {
              //   this.sheetFormRules.agentMan = [
              //     { required: true, message: "主送人不能为空" },
              //   ];
              //   this.sheetFormRules.acceptMan = [
              //     { required: true, message: "次送人不能为空" },
              //   ];
              //   return false;
              // }
            // });
          })
          .catch(() => {});
      // }
    },
    submitSave(formData) {
      apiProvinceBuild(formData)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("保存成功");
            this.$refs.sheetForm.resetFields();
            this.closeAndTurnAroundMyDraft();
          } else {
            this.$message.error("保存失败");
          }
          this.sheetSaveLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("保存失败");
          this.sheetSaveLoading = false;
        });
    },
    onSheetSubmit() {
      let self = this;
      this.$confirm("是否提交工单？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        closeOnClickModal: false,
        type: "warning",
      }).then(() => {
        this.$refs.sheetForm.validate(valid => {
          if (valid) {
            let formData = new FormData();
            this.sheetSaveLoading = true;
            let params = {};
            params.sheetInfo = this.sheetForm;
            params.sheetInfo.sheetStatus = 2; //1是草稿 2是提交到待办
            if (params.sheetInfo.isSendSms == "0") {
              params.sheetInfo.smsToUsername = "";
              params.sheetInfo.sendContent = "";
            }
            // 如果有附件，先上传附件
            let isUpload = false;
            if (
              this.sheetForm.attachmentFile != "" &&
              this.importForm.attachmentFileList.length > 0
            ) {
              isUpload = true;
              for (let item of this.importForm.attachmentFileList) {
                formData.append("files", item.raw);
              }
            } else {
              isUpload = false;
              formData.append("files", "");
            }
            let saveFormData = new FormData();

            // 获取上传附件id
            if (isUpload) {
              apiGroupCode()
                .then(res => {
                  if (res.status == 0) {
                    let processId = res.data?.linkId;

                    let uploadUrl = `/province/attach/upload?groupKey=${this.sheetForm.woId}&processId=${processId}&processNode=拟稿派单&sheetCreateTime=${self.sheetForm.sheetCreateTime}`;
                    apiFileUpload(uploadUrl, formData)
                      .then(res => {
                        if (res.code == 200) {
                          params.sheetInfo.linkId = processId;

                          saveFormData.append(
                            "jsonParam",
                            JSON.stringify(params)
                          );
                          this.submitOrder(saveFormData);
                        } else {
                          this.$message.error("上传失败，请重新上传");
                          return false;
                        }
                      })
                      .catch(error => {
                        console.log(error);
                      });
                  }
                })
                .catch(error => {
                  console.log(error);
                });
            } else {
              saveFormData.append("jsonParam", JSON.stringify(params));
              this.submitOrder(saveFormData);
            }
          } else {
            return false;
          }
        });
      });
    },
    submitOrder(formData) {
      apiProvinceBuild(formData)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("工单提交成功");
            this.$refs.sheetForm.resetFields();
            this.closeAndTurnAroundTo();
          } else {
            this.$message.error("工单提交失败");
          }
          this.sheetCommitLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("工单提交失败");
          this.sheetCommitLoading = false;
        });
    },

    onOpenPeopleDialog(diaSaveName) {
      this.isDiaOrgsUserTree = false;
      if (diaSaveName ==  'lordSentDetermine'){
        this.diaPeople.multipleSelectEnable = false;
        this.diaPeople.singleSelectTip = '请选择单个人员主送';
      }
      else if (diaSaveName == 'secondDetermine'){
        this.diaPeople.multipleSelectEnable = false;
        this.diaPeople.singleSelectTip = '请选择单个人员次送';
      }
      else {
        this.diaPeople.multipleSelectEnable = true;
        this.diaPeople.singleSelectTip = '';
      }
      this.diaPeople.title = this.diaPeople.saveTitleMap[diaSaveName];
      this.diaPeople.saveName = diaSaveName;
      // this.diaPeople.showOrgsTree = true;
      this.diaPeople.showContactUserTab =
        this.diaPeople.showContactUserTabMap[diaSaveName] ?? false;
      this.diaPeople.visible = true;
      this.$nextTick(() => {
        this.isDiaOrgsUserTree = true;
      });
    },
    onSavePeople(val) {
      this[this.diaPeople.saveName](val);
    },
    //建单人主送确定
    lordSentDetermine({ usersChecked, orgsChecked, selectionUser, contactSelectionUser, contactSelectionOrg }) {

      if (contactSelectionUser && contactSelectionUser.length > 0) {
        // let usersCheckedName = contactSelectionUser.map(item => {
        //   return item.trueName;
        // });
        // this.sheetForm.agentManUserName = usersCheckedName.join(",");
        let usersCheckedId = contactSelectionUser.map(item => {
          return item.userName;
        });
        this.sheetForm.agentManId = usersCheckedId.join(",");
        const userDetailName = contactSelectionUser.map(item => {
          return (
            item.trueName +
            "-" +
            item.orgEntity.orgName +
            "-" +
            item.mobilePhone
          );
        });
        this.sheetForm.agentMan = userDetailName.join(",");
      }
      if (selectionUser && selectionUser.length > 0) {
        // let usersCheckedName = selectionUser.map(item => {
        //   return item.trueName;
        // });
        // this.sheetForm.agentManUserName = usersCheckedName.join(",");
        let usersCheckedId = selectionUser.map(item => {
          return item.userName;
        });
        this.sheetForm.agentManId = usersCheckedId.join(",");
        const userDetailName = selectionUser.map(item => {
          return (
            item.trueName +
            "-" +
            item.orgEntity.orgName +
            "-" +
            item.mobilePhone
          );
        });
        this.sheetForm.agentMan = userDetailName.join(",");
      }
      else if (usersChecked && usersChecked.length > 0) {
        let usersCheckedName = usersChecked.map(item => {
          return item.name;
        });
        this.sheetForm.agentManUserName = usersCheckedName.join(",");
        let usersCheckedId = usersChecked.map(item => {
          return item.id;
        });
        this.sheetForm.agentManId = usersCheckedId.join(",");
        const userDetailName = usersChecked.map(item => {
          return item.name + "-" + item.orgName + "-" + item.mobilePhone;
        });
        this.sheetForm.agentMan = userDetailName.join(",");
      }

      // let orgsCheckedName = orgsChecked.map(item => {
      //   return item.name;
      // });
      // this.sheetForm.agentDeptName = orgsCheckedName.join(",");
      // this.sheetForm.agentMan = this.stitchingAlgorithm(
      //   this.sheetForm.agentDeptName,
      //   this.sheetForm.agentManDetail
      // );
      // console.log(this.sheetForm.agentMan, "主");
      // let orgsCheckedId = orgsChecked.map(item => {
      //   return item.id;
      // });
      // this.sheetForm.agentDeptName = orgsCheckedId.join(",");
    },
    //建单人次送确定
    secondDetermine({ usersChecked, orgsChecked, selectionUser, contactSelectionUser, contactSelectionOrg }) {
      if (contactSelectionUser && contactSelectionUser.length > 0) {
        // let usersCheckedName = selectionUser.map(item => {
        //   return item.trueName;
        // });
        // this.sheetForm.acceptManUserName = usersCheckedName.join(",");
        let usersCheckedId = contactSelectionUser.map(item => {
          return item.userName;
        });
        this.sheetForm.acceptManId = usersCheckedId.join(",");
        const userDetailName = contactSelectionUser.map(item => {
          return (
            item.trueName +
            "-" +
            item.orgEntity.orgName +
            "-" +
            item.mobilePhone
          );
        });
        this.sheetForm.acceptMan = userDetailName.join(",");
      }
      if (selectionUser && selectionUser.length > 0) {
        // let usersCheckedName = selectionUser.map(item => {
        //   return item.trueName;
        // });
        // this.sheetForm.acceptManUserName = usersCheckedName.join(",");
        let usersCheckedId = selectionUser.map(item => {
          return item.userName;
        });
        this.sheetForm.acceptManId = usersCheckedId.join(",");
        const userDetailName = selectionUser.map(item => {
          return (
            item.trueName +
            "-" +
            item.orgEntity.orgName +
            "-" +
            item.mobilePhone
          );
        });
        this.sheetForm.acceptMan = userDetailName.join(",");
      }
      else if (usersChecked && usersChecked.length > 0) {
        // let usersCheckedName = usersChecked.map(item => {
        //   return item.name;
        // });
        // this.sheetForm.acceptManUserName = usersCheckedName.join(",");
        let usersCheckedId = usersChecked.map(item => {
          return item.id;
        });
        this.sheetForm.acceptManId = usersCheckedId.join(",");
        const userDetailName = usersChecked.map(item => {
          return item.name + "-" + item.orgName + "-" + item.mobilePhone;
        });
        this.sheetForm.acceptMan = userDetailName.join(",");
      }

      // let orgsCheckedName = orgsChecked.map(item => {
      //   return item.name;
      // });
      // this.sheetForm.acceptDeptName = orgsCheckedName.join(",");
      //
      // this.sheetForm.acceptMan = this.stitchingAlgorithm(
      //   this.sheetForm.acceptDeptName,
      //   this.sheetForm.acceptManDetail
      // );
      //
      // let orgsCheckedId = orgsChecked.map(item => {
      //   return item.id;
      // });
      // this.sheetForm.acceptManCode = orgsCheckedId.join(",");
    },
    //抄送人确定
    ccDetermine({ usersChecked, orgsChecked, selectionUser, contactSelectionUser, contactSelectionOrg })
    {
      let cyUserDetails = '';
      let cyUserIds = '';
      let userDetails = '';
      let userIds = '';
      if (contactSelectionUser && contactSelectionUser.length > 0) {
        // let usersCheckedName = contactSelectionUser.map(item => {
        //   return item.trueName;
        // });
        // this.sheetForm.copyManUserName = usersCheckedName.join(",");
        let usersCheckedId = contactSelectionUser.map(item => {
          return item.userName;
        });
        cyUserIds = usersCheckedId.join(",");
        const userDetailName = contactSelectionUser.map(item => {
          return (
            item.trueName +
            "-" +
            item.orgEntity.orgName +
            "-" +
            item.mobilePhone
          );
        });
        cyUserDetails = userDetailName.join(",");
      }
      if (selectionUser && selectionUser.length > 0) {
        // let usersCheckedName = selectionUser.map(item => {
        //   return item.trueName;
        // });
        // this.sheetForm.copyManUserName = usersCheckedName.join(",");
        let usersCheckedId = selectionUser.map(item => {
          return item.userName;
        });
        userIds = usersCheckedId.join(",");
        const userDetailName = selectionUser.map(item => {
          return (
            item.trueName +
            "-" +
            item.orgEntity.orgName +
            "-" +
            item.mobilePhone
          );
        });
        userDetails = userDetailName.join(",");
      }
      else if (usersChecked && usersChecked.length > 0) {
        // let usersCheckedName = usersChecked.map(item => {
        //   return item.name;
        // });
        // this.sheetForm.copyManUserName = usersCheckedName.join(",");

        let usersCheckedId = usersChecked.map(item => {
          return item.id;
        });
        userIds = usersCheckedId.join(",");
        const userDetailName = usersChecked.map(item => {
          return item.name + "-" + item.orgName + "-" + item.mobilePhone;
        });
        userDetails = userDetailName.join(",");
      }

      // let orgsCheckedName = orgsChecked.map(item => {
      //   return item.name;
      // });
      // this.sheetForm.copyDeptName = orgsCheckedName.join(",");
      this.sheetForm.copyManId = this.stitchingAlgorithm(
        userIds,
        cyUserIds
      );

      this.sheetForm.copyMan = this.stitchingAlgorithm(
        userDetails,
        cyUserDetails
      );

      // let orgsCheckedId = orgsChecked.map(item => {
      //   return item.id;
      // });
      // this.sheetForm.copyDeptCode = orgsCheckedId.join(",");
    },

    //通知人确定--没用到
    // notifierDetermine({ usersChecked, orgsChecked, selectionUser }) {
    //   let userDetailName = "";
    //   if (selectionUser && selectionUser.length > 0) {
    //     let usersCheckedName = selectionUser.map(item => {
    //       return item.trueName;
    //     });
    //     this.sheetForm.notifierUserName = usersCheckedName.join(",");
    //     let usersCheckedId = selectionUser.map(item => {
    //       return item.userName;
    //     });
    //     this.sheetForm.notifierUserId = usersCheckedId.join(",");
    //     userDetailName = selectionUser
    //       .map(item => {
    //         return (
    //           item.trueName +
    //           "-" +
    //           item.orgEntity.orgName +
    //           "-" +
    //           item.mobilePhone
    //         );
    //       })
    //       .join(",");
    //   } else {
    //     let usersCheckedName = usersChecked.map(item => {
    //       return item.name;
    //     });
    //     this.sheetForm.notifierUserName = usersCheckedName.join(",");
    //
    //     let usersCheckedId = usersChecked.map(item => {
    //       return item.id;
    //     });
    //     this.sheetForm.notifierUserId = usersCheckedId.join(",");
    //     userDetailName = usersChecked
    //       .map(item => {
    //         return item.name + "-" + item.orgName + "-" + item.mobilePhone;
    //       })
    //       .join(",");
    //   }
    //
    //   let orgsCheckedName = orgsChecked.map(item => {
    //     return item.name;
    //   });
    //   this.sheetForm.notifierOrgName = orgsCheckedName.join(",");
    //   this.sheetForm.notifier = this.stitchingAlgorithm(
    //     this.sheetForm.notifierOrgName,
    //     userDetailName
    //   );
    //   let orgsCheckedId = orgsChecked.map(item => {
    //     return item.id;
    //   });
    //   this.sheetForm.notifierOrgId = orgsCheckedId.join(",");
    // },

    //接收人确定
    smsToUsername({ usersChecked, orgsChecked, selectionUser, contactSelectionUser, contactSelectionOrg }) {
      // let contactUserName = "";
      // let treeUserName = "";
      let contactUserId = "";
      let contactUserDetailName = "";
      let treeUserId = "";
      let userDetailName = "";
      if (contactSelectionUser && contactSelectionUser.length > 0) {
        // let usersCheckedName = contactSelectionUser.map(item => {
        //   return item.trueName;
        // });
        // contactUserName = usersCheckedName.join(",");
        contactUserId = contactSelectionUser
          .map(item => {
            return item.userName;
          })
          .join(",");
        contactUserDetailName = contactSelectionUser
          .map(item => {
            return (
              item.trueName +
              "-" +
              item.orgEntity.orgName +
              "-" +
              item.mobilePhone
            );
          })
          .join(",");
      }
      if (selectionUser && selectionUser.length > 0) {
        // let usersCheckedName = selectionUser.map(item => {
        //   return item.trueName;
        // });
        // treeUserName = usersCheckedName.join(",");
        let usersCheckedId = selectionUser.map(item => {
          return item.userName;
        });
        treeUserId = usersCheckedId.join(",");
        userDetailName = selectionUser
          .map(item => {
            return (
              item.trueName +
              "-" +
              item.orgEntity.orgName +
              "-" +
              item.mobilePhone
            );
          })
          .join(",");
      } else if (usersChecked && usersChecked.length > 0) {
        // let usersCheckedName = usersChecked.map(item => {
        //   return item.name;
        // });
        // treeUserName = usersCheckedName.join(",");
        let usersCheckedId = usersChecked.map(item => {
          return item.id;
        });
        treeUserId = usersCheckedId.join(",");
        userDetailName = usersChecked
          .map(item => {
            return item.name + "-" + item.orgName + "-" + item.mobilePhone;
          })
          .join(",");
      }

      this.sheetForm.smsToUsername = this.stitchingAlgorithm(
        contactUserDetailName,
        userDetailName
      );
      this.sheetForm.smsToUserid = this.stitchingAlgorithm(
        treeUserId,
        contactUserId
      );
    },
    //光缆名称选择
    opticalCableSelect() {
      let jsonStr =
        '{"userId":"302785","username":"yangxm97","callSysId":"0001","callSysName":"电子运维"}';
      this.$nextTick(() => {
        document.querySelector("#fiberOpticCable").value = jsonStr;
        document.querySelector("#sub__fiberOpticCable").submit();
      });
    },
    //工单主题
    getInterfaceZxData() {
      let jsonStr =
        '{"userId":"302785","username":"yangxm97","callSysId":"0001","callSysName":"电子运维"}';
      this.$nextTick(() => {
        document.querySelector("#requestJson").value = jsonStr;
        document.querySelector("#deploy_simulation_page").submit();
      });
    },
    //附件选择
    attachmentBrowse() {
      this.attachmentDialogVisible = true;
    },
    changeFileData(data) {
      this.sheetForm.attachmentFile = data.fileName;
      this.importForm.attachmentFileList = data.attachmentFileList;
      this.attachmentDialogVisible = false;
    },
    closeAttachmentDialog() {
      this.attachmentDialogVisible = false;
    },
    //影响业务列表选择
    incidenceBrowse() {
      this.incidenceDialogVisible = true;
    },
    changeIncidenceFileData(data) {
      this.sheetForm.incidenceFile = data.fileName;
      this.importForm.incidenceFileList = data.attachmentFileList;
      this.incidenceDialogVisible = false;
    },
    closeIncidenceDialog() {
      this.incidenceDialogVisible = false;
    },
    stitchingAlgorithm(orgName, userName) {
      if (orgName.length !== 0 && userName.length !== 0) {
        return orgName + "," + userName;
      } else {
        if (orgName.length !== 0) {
          return orgName;
        } else if (userName.length !== 0) {
          return userName;
        } else {
          return "";
        }
      }
    },
    filterLordSentOrgNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    closeAndTurnAroundMyDraft() {
      let self = this;
      self.$store.dispatch("tagsView/delView", self.route).then(() => {
        self.$destroy();
      });
      self.$router.push({
        name: "backbone_myDraft",
      });
    },
    closeAndTurnAroundTo() {
      let self = this;
      self.$store.dispatch("tagsView/delView", self.route).then(() => {
        self.$destroy();
      });
      self.$router.push({
        name: "backbone_toDoRepairOrder",
        query: {
          globalUniqueID: sessionStorage.getItem("globalUniqueID"),
          token: this.token,
        },
      });
    },
    professionalTypeChange() {
      this.sheetForm.networkType = "";
    },
    // 下拉查询
    getDictData(dictId, selectName) {
      this[selectName] = [];
      let param = {
        dictType: dictId,
      };
      apiDict(param)
        .then(res => {
          if (res.code == 200) {
            this[selectName] = res.data;
          }
        })
        .catch(error => {
          console.log(error);
          return false;
        });
    },
    // input 清空value
    btnClearable(val) {
      this.sheetForm[val] = "";
    },
  },
};
</script>

<style lang="scss" scoped>
.draft-eidt-page {
  .head-title {
    font-size: 24px;
    line-height: 28px;
  }
  .head-handle-wrap {
    text-align: right;
  }
  .card-title {
    font-size: 15px;
    letter-spacing: 0;
    padding-left: 10px;
  }

  ::v-deep .el-tree {
    padding-top: 15px;
    padding-left: 10px;
    // 不可全选样式
    .el-tree-node {
      .is-leaf + .el-checkbox .el-checkbox__inner {
        display: inline-block;
      }
      .el-checkbox .el-checkbox__inner {
        display: none;
      }
    }
  }

  .select_style {
    background: #b50b14;
    border-radius: 2px;
    border-color: #b50b14;
  }
}
</style>
