import { getJson, postFormData } from "@/utils/axios";
const faultAreaUrl = "backbone/info/faultArea"; //故障发生地区字典数据
const getEvaluationUrl = "/backbone/workflow/getEvaluation"; //是否已评价

const itCloudBackSingleUrl =
  "/commCloud/commonCloudAckDefine/commonCloudFeedback"; //通信云返单

const commCloudQualitativeUrl = "/commCloud/commonCloudAckDefine/commonCloudDefine";

const apiGetFaultArea = params => getJson(faultAreaUrl, params);
const apiItCloudBackSingle = params =>
  postFormData(itCloudBackSingleUrl, params);
const apiGetEvaluation = params => getJson(getEvaluationUrl, params);
const apicommCloudQualitative = params => postFormData(commCloudQualitativeUrl, params);

export { apiGetFaultArea, apiItCloudBackSingle, apiGetEvaluation, apicommCloudQualitative };
