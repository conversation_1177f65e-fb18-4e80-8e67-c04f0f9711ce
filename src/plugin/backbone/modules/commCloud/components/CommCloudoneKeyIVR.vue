<template>
  <el-dialog
    title="一键催办"
    :visible="dialogOneKeyIvrNotice"
    :close-on-click-modal="false"
    :before-close="dialogOneKeyIvrNoticeClose"
    width="800px"
  >
    <el-form :model="oneKeyForm" :rules="rules" label-width="120px">
      <el-form-item label="主送人:" prop="agentPeopleName" required>
        <el-input
          v-model="oneKeyForm.agentPeopleName"
          placeholder="请选择主送人"
          :disabled="true"
        >
          <template v-for="(tag, index) in organizeForm.builderZsList">
            <el-tag
              slot="prefix"
              style="margin-top: 5px"
              :key="index"
              closable
              @close="handleClose('builderZs', tag)"
              v-show="index < 1"
              v-if="tag.bz == 'user'"
            >
              {{ tag.tabName }}
            </el-tag>
            <el-tag
              slot="prefix"
              style="margin-top: 5px"
              :key="index"
              closable
              @close="handleClose(tag)"
              v-show="index < 1"
              v-if="tag.bz == 'org'"
            >
              {{ tag.orgName }}
            </el-tag>
          </template>
          <el-popover
            slot="prefix"
            v-if="organizeForm.builderZsList.length >= 2"
            width="500"
            trigger="click"
          >
            <el-input
              v-model="organizeForm.builderZsName"
              placeholder="请输入主送人员姓名"
            >
              <el-button
                type="info"
                slot="append"
                icon="el-icon-search"
                @click="search('builderZs')"
              />
              <el-button
                type="info"
                slot="append"
                icon="el-icon-close"
                @click="clear('builderZs')"
              />
            </el-input>
            <el-table
              ref="multipleTable"
              tooltip-effect="dark"
              @selection-change="handleSelectionChange"
              :data="organizeForm.builderZsListCopy"
              max-height="240"
            >
              <el-table-column width="30" type="selection" />
              <el-table-column min-width="70" property="name" label="姓名" />
              <el-table-column
                min-width="180"
                property="orgName"
                label="组织"
              />
              <el-table-column
                min-width="120"
                property="mobilePhone"
                label="电话"
              />
              <el-table-column fixed="right" label="操作" width="50">
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    size="small"
                    @click.native.prevent="handleClose('builderZs', scope.row)"
                  >
                    移除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-button
              size="small"
              type="text"
              @click="toggleSelection('builderZs')"
            >
              批量移除
            </el-button>
            <el-tag slot="reference" style="margin-top: 3px; margin-left: 5px">
              +{{ organizeForm.builderZsList.length - 1 }}
            </el-tag>
          </el-popover>
          <el-button
            type="info"
            slot="append"
            icon="el-icon-user"
            @click="onOpenPeopleDialog('lordSentDetermine')"
          />
        </el-input>
      </el-form-item>
      <el-form-item label="抄送人:">
        <el-input placeholder="请选择抄送人" :disabled="true">
          <template v-for="(tag, index) in organizeForm.builderCsList">
            <el-tag
              slot="prefix"
              style="margin-top: 5px"
              :key="index"
              closable
              @close="handleClose('builderCs', tag)"
              v-show="index < 1"
              v-if="tag.bz == 'user'"
            >
              {{ tag.tabName }}
            </el-tag>
            <el-tag
              slot="prefix"
              style="margin-top: 5px"
              :key="index"
              closable
              @close="handleClose('builderCs', tag)"
              v-show="index < 1"
              v-if="tag.bz == 'org'"
            >
              {{ tag.orgName }}
            </el-tag>
          </template>
          <el-popover
            slot="prefix"
            v-if="organizeForm.builderCsList.length >= 2"
            width="500"
            trigger="click"
          >
            <el-input
              v-model="organizeForm.builderCsName"
              placeholder="请输入抄送人员姓名/组织名称"
            >
              <el-button
                type="info"
                slot="append"
                icon="el-icon-search"
                @click="search('builderCs')"
              >
              </el-button>
              <el-button
                type="info"
                slot="append"
                icon="el-icon-close"
                @click="clear('builderCs')"
              >
              </el-button>
            </el-input>
            <el-table
              ref="multipleTable"
              tooltip-effect="dark"
              @selection-change="handleSelectionChange"
              :data="organizeForm.builderCsListCopy"
              max-height="240"
            >
              <el-table-column width="30" type="selection" />
              <el-table-column min-width="70" property="name" label="姓名" />
              <el-table-column
                min-width="180"
                property="orgName"
                label="组织"
              />
              <el-table-column
                min-width="120"
                property="mobilePhone"
                label="电话"
              />
              <el-table-column fixed="right" label="操作" width="50">
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    size="small"
                    @click.native.prevent="handleClose('builderCs', scope.row)"
                  >
                    移除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-button
              size="small"
              type="text"
              @click="toggleSelection('builderCs')"
            >
              批量移除
            </el-button>
            <el-tag slot="reference" style="margin-top: 5px; margin-left: 5px">
              +{{ organizeForm.builderCsList.length - 1 }}
            </el-tag>
          </el-popover>
          <el-button
            type="info"
            slot="append"
            icon="el-icon-user"
            @click="onOpenPeopleDialog('ccDetermine')"
          />
        </el-input>
      </el-form-item>
      <el-form-item label="自定义发送号码:">
        <el-input
          placeholder="请点击【+ 添加号码】按钮进行自定义号码添加"
          v-model="oneKeyForm.customPhone"
          :disabled="true"
        >
          <el-button slot="append" @click="showInput">+ 添加号码</el-button>
        </el-input>
      </el-form-item>
      <el-form-item label="发送内容:" prop="sentRemind">
        <div class="onKeyForm_content">
          <div class="onKeyForm_text_top">
            <p>主送模板:</p>
            <span class="box_top_name"
              >【姓名】{{ oneKeyForm.sentHello }}{{ sheetId }},</span
            >
            <!-- <el-input
              class="box_top_text"
              type="text"
              v-model="oneKeyForm.sentHello"
              :title="oneKeyForm.sentHello"
            /> -->
            <!-- <span class="box_top_name"></span> -->
            <el-input
              class="sentRemind"
              type="text"
              v-model="oneKeyForm.sentRemind"
              :title="oneKeyForm.sentRemind"
            />
          </div>
          <div class="onKeyForm_text_bottom">
            <p>抄送模板:</p>
            <span class="box_top_name"
              >【姓名】{{ oneKeyForm.copyHello }}{{ sheetId }},</span
            >
            <!-- <el-input
              class="box_bottom_text"
              type="text"
              v-model="oneKeyForm.copyHello"
              :title="oneKeyForm.copyHello"
            /> -->
            <!-- <span class="box_top_name">{{ sheetId }}</span> -->
            <el-input
              class="copyRemind"
              type="text"
              v-model="oneKeyForm.copyRemind"
              :title="oneKeyForm.copyRemind"
            />
          </div>
        </div>
      </el-form-item>
      <el-form-item class="foolter_button">
        <el-button type="primary" @click="submitOneKeyIvrNotice"
          >提交</el-button
        >
        <el-button @click="resetOneKeyForm">重置</el-button>
      </el-form-item>
    </el-form>
    <dia-orgs-user-tree
      :title="diaPeople.title"
      :visible.sync="diaPeople.visible"
      :showOrgsTree="diaPeople.showOrgsTree"
      @on-save="onSavePeople"
      :appendToBody="diaPeople.appendToBody"
    />
    <el-dialog
      title="自定义号码"
      :visible.sync="inputVisible"
      append-to-body
      width="600px"
    >
      <el-form
        :model="dynamicValidateForm"
        ref="dynamicValidateForm"
        label-width="110px"
      >
        <el-row
          :gutter="15"
          v-for="(domain, index) in dynamicValidateForm.domains"
          :key="domain.key"
        >
          <el-form-item :label="'自定义号码' + (index + 1)" required>
            <el-col :span="8">
              <el-form-item
                :key="domain.key"
                :prop="'domains.' + index + '.name'"
                :rules="{
                  required: true,
                  message: '姓名不能为空',
                  trigger: 'blur',
                }"
              >
                <el-input
                  v-model="domain.name"
                  placeholder="请输入姓名"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                :key="domain.key"
                :prop="'domains.' + index + '.phone'"
                :rules="{
                  required: true,
                  message: '电话号不能为空',
                  trigger: 'blur',
                }"
              >
                <el-input
                  v-model="domain.phone"
                  placeholder="请输入电话号码"
                  @change="changeMobile(domain.phone)"
                />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-button type="text" @click.prevent="removeDomain(domain)">
                删除
              </el-button>
            </el-col>
          </el-form-item>
        </el-row>
        <el-form-item class="foolter_button">
          <el-button type="primary" @click="submitForm()">确定</el-button>
          <el-button @click="addDomain">新增号码</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </el-dialog>
</template>

<script>
import DiaOrgsUserTree from "../../workOrder/components/DiaOrgsUserTree.vue";
import {
  apioneKeyIvrNotice,
  apiGetUsers,
} from "../../workOrder/workOrderWaitDetail/api/CommonApi";

export default {
  components: {
    DiaOrgsUserTree,
  },
  props: {
    dialogOneKeyIvrNotice: {
      type: Boolean,
      default: false,
    },
    sheetId: {
      type: String,
      default: "",
    },
    sheetCreateTime: {
      type: String,
      default: "",
    },
    common: {
      type: Object,
    },
  },

  data() {
    return {
      //ivr对象
      inputVisible: false,
      inputValue: "",
      oneKeyForm: {
        agentPeopleName: "",
        agentPeople: [],
        copyPeople: [],
        customPhone: "",
        sentHello: "你好,你有一张故障工单",
        sentRemind: "请尽快处理",
        copyHello: "你好,故障工单",
        copyRemind: "请督促相关人员尽快处理",
      },
      organizeForm: {
        builderZsList: [],
        builderZsListCopy: [],
        builderZsName: "",
        builderCsList: [],
        builderCsListCopy: [],
        builderCsName: "",
      },
      dynamicValidateForm: {
        domains: [
          {
            name: "",
            phone: "",
          },
        ],
      },
      //ivr人员数
      diaPeople: {
        title: "",
        visible: false,
        saveName: "", //方法控制
        showOrgsTree: false, //是否显示组织树
        appendToBody: true, //嵌套dialog
        saveTitleMap: {
          lordSentDetermine: "主送人",
          ccDetermine: "抄送人",
        },
      },
      rules: {
        agentPeopleName: [{ required: true, message: "主送人不能为空" }],
      },
    };
  },
  watch: {
    "dynamicValidateForm.domains": {
      handler(newV) {},
    },
    "common.agentManId": {
      handler(newV) {
        if (newV != null && newV != "") {
          let zsId = newV.split(",");
          this.getUsers("zs", zsId);
        }
      },
      deep: true,
    },
    "common.copyManId": {
      handler(newV) {
        if (newV != null && newV != "") {
          let csId = newV.split(",");
          this.getUsers("cs", csId);
        }
      },
      deep: true,
    },
    "organizeForm.builderZsList": {
      handler(newV) {
        if (newV.length > 0) {
          this.oneKeyForm.agentPeopleName = "已选";
        } else {
          this.oneKeyForm.agentPeopleName = "";
        }
      },
      deep: true,
    },
  },
  methods: {
    getUsers(key, param) {
      apiGetUsers(param).then(res => {
        if (res.status == "0") {
          if (key === "zs") {
            let self = this;
            let users = res?.data ?? [];
            users.map(item => {
              self.organizeForm.builderZsList.push({
                bz: "user",
                id: item.userName,
                name: item.trueName,
                tabName:
                  item.trueName + "-" + item.orgName + "-" + item.mobilePhone,
                orgName: item.orgName,
                mobilePhone: item.mobilePhone,
              });
            });
            self.organizeForm.builderZsListCopy = JSON.parse(
              JSON.stringify(this.organizeForm.builderZsList)
            );
          }
          if (key === "cs") {
            let self = this;
            let users = res?.data ?? [];
            users.map(item => {
              self.organizeForm.builderCsList.push({
                bz: "user",
                id: item.userName,
                name: item.trueName,
                tabName:
                  item.trueName + "-" + item.orgName + "-" + item.mobilePhone,
                orgName: item.orgName,
                mobilePhone: item.mobilePhone,
              });
            });
            self.organizeForm.builderCsListCopy = JSON.parse(
              JSON.stringify(this.organizeForm.builderCsList)
            );
          }
        }
      });
    },
    //ivr提交
    submitOneKeyIvrNotice() {
      this.entering();
      if (this.oneKeyForm.agentPeople.length == 0) {
        this.$message({
          type: "warning",
          message: "主送人不可以为空",
        });
        return;
      }
      let param = {
        sheetNo: this.common.sheetNo,
        processInstId: this.common.processInstId,
        sheetCreateTime: this.sheetCreateTime,
        agentMan: this.oneKeyForm.agentPeople,
        copyMan: this.oneKeyForm.copyPeople,
        customPhone: this.oneKeyForm.customPhone,
        agentTouchPlate:
          "{0}" +
          this.oneKeyForm.sentHello +
          this.sheetId +
          "," +
          this.oneKeyForm.sentRemind,
        copyTouchPlate:
          "{0}" +
          this.oneKeyForm.copyHello +
          this.sheetId +
          "," +
          this.oneKeyForm.copyRemind,
      };
      apioneKeyIvrNotice(param)
        .then(res => {
          if (res.status == "0") {
            this.$emit("update:dialogOneKeyIvrNotice", false);
            this.resetOneKeyForm();
            this.$message({
              type: "success",
              message: "一键催办成功",
            });
          } else {
            this.$message.error("一键催办失败");
          }
        })
        .catch(error => {
          if (error.status == "400") {
            this.$message.error(error.msg);
          } else {
            this.$message.error("添加失败");
          }
        });
    },
    //发送内荣处理
    arrToString(arr, helo, remind) {
      let agenN = [];
      arr.forEach(el => {
        let [n] = el.split("-");
        agenN.push(n);
      });
      let str = agenN.join();
      let text = str + helo + this.sheetId + remind;
      let newT = text.replace(/[^a-zA-Z\u4e00-\u9fa50-9]+/g, "");
      return newT;
    },
    //ivr重置
    resetOneKeyForm() {
      this.oneKeyForm = {
        agentPeopleName: "",
        agentPeople: [],
        copyPeople: [],
        customPhone: "",
        sentHello: "你好,你有一张故障工单",
        sentRemind: "请尽快处理",
        copyHello: "你好,故障工单",
        copyRemind: "请督促相关人员尽快处理",
      };
      this.organizeForm = {
        builderZsList: [],
        builderZsListCopy: [],
        builderZsName: "",
        builderCsList: [],
        builderCsListCopy: [],
        builderCsName: "",
      };
      this.dynamicValidateForm = {
        domains: [
          {
            name: "",
            phone: "",
          },
        ],
      };
      if (this.common.agentManId != null && this.common.agentManId != "") {
        let zsId = this.common.agentManId.split(",");
        this.getUsers("zs", zsId);
        this.oneKeyForm.agentPeopleName = "已选";
      }
      if (this.common.copyManId != null && this.common.copyManId != "") {
        let csId = this.common.copyManId.split(",");
        this.getUsers("cs", csId);
      }
    },
    //ivr点击显示天加自定义号码
    showInput() {
      let domains = this.oneKeyForm.customPhone.split(",");
      domains.map(item => {
        if (item != "") {
          let domain = item.split("-");
          this.dynamicValidateForm.domains.push({
            name: domain[0],
            phone: domain[1],
          });
        }
      });
      this.inputVisible = true;
    },
    //ivr人员树总开关
    onOpenPeopleDialog(key) {
      this.diaPeople.visible = true;
      this.diaPeople.saveName = key;
      this.diaPeople.title = this.diaPeople.saveTitleMap[key];
    },
    //ivr人员树确定
    onSavePeople(val) {
      this[this.diaPeople.saveName](val);
    },

    //X号关闭
    dialogOneKeyIvrNoticeClose() {
      this.$emit("update:dialogOneKeyIvrNotice", false);
      this.resetOneKeyForm();
    },

    //建单人主送确定
    lordSentDetermine({ usersChecked, orgsChecked, selectionUser }) {
      if (selectionUser && selectionUser.length > 0) {
        selectionUser.map(item => {
          let zs = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.userName;
          });
          if (zs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderZsList.push({
              bz: "user",
              id: item.userName,
              name: item.trueName,
              tabName:
                item.trueName +
                "-" +
                item.orgEntity.orgName +
                "-" +
                item.mobilePhone,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      } else {
        usersChecked.map(item => {
          let zs = this.organizeForm.builderZsList.findIndex(val => {
            return val.id === item.id;
          });
          if (zs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderZsList.push({
              bz: "user",
              id: item.id,
              name: item.name,
              tabName: item.name + "-" + item.orgName + "-" + item.mobilePhone,
              orgName: item.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      this.organizeForm.builderZsListCopy = JSON.parse(
        JSON.stringify(this.organizeForm.builderZsList)
      );
    },
    //抄送人确定
    ccDetermine({ usersChecked, orgsChecked, selectionUser }) {
      if (selectionUser && selectionUser.length > 0) {
        selectionUser.map(item => {
          let cs = this.organizeForm.builderCsList.findIndex(val => {
            return val.id === item.userName;
          });
          if (cs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderCsList.push({
              bz: "user",
              id: item.userName,
              tabName:
                item.trueName +
                "-" +
                item.orgEntity.orgName +
                "-" +
                item.mobilePhone,
              name: item.trueName,
              orgName: item.orgEntity.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      } else {
        usersChecked.map(item => {
          let cs = this.organizeForm.builderCsList.findIndex(val => {
            return val.id === item.id;
          });
          if (cs > -1) {
            this.$message({
              message: "选择的用户已存在",
              type: "warning",
            });
          } else {
            this.organizeForm.builderCsList.push({
              bz: "user",
              id: item.id,
              tabName: item.name + "-" + item.orgName + "-" + item.mobilePhone,
              name: item.name,
              orgName: item.orgName,
              mobilePhone: item.mobilePhone,
            });
          }
        });
      }
      this.organizeForm.builderCsListCopy = JSON.parse(
        JSON.stringify(this.organizeForm.builderCsList)
      );
    },
    //多选移除
    toggleSelection(val) {
      if (this.multipleSelection == 0) {
        this.$message({
          type: "warning",
          message: "还没有选择移除的选项，请先选择要移除的选项！",
        });
        return;
      } else {
        this.multipleSelection.forEach(row => {
          this.handleClose(val, row);
        });
      }
      this.multipleSelection;
    },
    //移除对象
    handleClose(val, tag) {
      if (val == "builderZs") {
        this.organizeForm.builderZsList.splice(
          this.arrayIndex(this.organizeForm.builderZsList, tag),
          1
        );
        this.organizeForm.builderZsListCopy.splice(
          this.arrayIndex(this.organizeForm.builderZsListCopy, tag),
          1
        );
      }
      if (val == "builderCs") {
        this.organizeForm.builderCsList.splice(
          this.arrayIndex(this.organizeForm.builderCsList, tag),
          1
        );
        this.organizeForm.builderCsListCopy.splice(
          this.arrayIndex(this.organizeForm.builderCsListCopy, tag),
          1
        );
      }
    },
    //找对应对象的索引值
    arrayIndex(val1, val2) {
      for (var i = 0, len = val1.length; i < len; i++) {
        var tag = val1[i];
        if (tag.id == val2.id) {
          return i;
        }
      }
    },
    //搜索
    search(val) {
      if (val == "builderZs" && this.organizeForm.builderZsList != null) {
        this.organizeForm.builderZsListCopy = [];
        this.organizeForm.builderZsList.forEach(row => {
          if (
            row.name == this.organizeForm.builderZsName ||
            row.orgName == this.organizeForm.builderZsName
          ) {
            this.organizeForm.builderZsListCopy.push(row);
          }
        });
      }
      if (val == "builderCs" && this.organizeForm.builderCsList != null) {
        this.organizeForm.builderCsListCopy = [];
        this.organizeForm.builderCsList.forEach(row => {
          if (
            row.name == this.organizeForm.builderCsName ||
            row.orgName == this.organizeForm.builderCsName
          ) {
            this.organizeForm.builderCsListCopy.push(row);
          }
        });
      }
      if (val == "recipient" && this.organizeForm.recipientList != null) {
        this.organizeForm.recipientListCopy = [];
        this.organizeForm.recipientList.forEach(row => {
          if (
            row.name == this.organizeForm.recipientName ||
            row.orgName == this.organizeForm.recipientName
          ) {
            this.organizeForm.recipientListCopy.push(row);
          }
        });
      }
    },
    //清除搜索项
    clear(val) {
      if (val == "builderZs") {
        this.organizeForm.builderZsName = "";
        this.organizeForm.builderZsListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.builderZsList)
        );
      }
      if (val == "builderCs") {
        this.organizeForm.builderCsName = "";
        this.organizeForm.builderCsListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.builderCsList)
        );
      }
      if (val == "recipient") {
        this.organizeForm.recipientName = "";
        this.organizeForm.recipientListCopy = JSON.parse(
          JSON.stringify(this.organizeForm.recipientList)
        );
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    //自定义手机号码确认
    submitForm() {
      var objArr = this.dynamicValidateForm.domains;
      for (var i = 0; i < objArr.length; i++) {
        if (objArr[i].name == null || objArr[i].name == "") {
          this.$message({
            message: "姓名不可以为空",
            type: "warning",
          });
          return;
        }
        if (objArr[i].phone == null || objArr[i].phone == "") {
          this.$message({
            message: "手机号不可以为空",
            type: "warning",
          });
          return;
        } else {
          if (this.changeMobile(objArr[i].phone)) {
            return;
          }
        }
      }
      //自定义号码重复校验
      let newV = this.dynamicValidateForm.domains;
      for (let i = 0; i < newV.length - 1; i++) {
        for (let j = i + 1; j < newV.length; j++) {
          if (newV[i].phone == newV[j].phone) {
            this.$message({
              type: "warning",
              message: "自定义号码中有重复的电话号码",
            });
            return;
          }
        }
      }
      this.oneKeyForm.customPhone = this.dynamicValidateForm.domains
        .map(item => {
          return item.name + "-" + item.phone;
        })
        .join(",");
      this.dynamicValidateForm.domains = [];
      this.inputVisible = false;
    },
    //自定义手机号码删除
    removeDomain(item) {
      var index = this.dynamicValidateForm.domains.indexOf(item);
      if (index !== -1) {
        this.dynamicValidateForm.domains.splice(index, 1);
      }
    },
    //自定义手机号码新增
    addDomain() {
      this.dynamicValidateForm.domains.push({
        name: "",
        phone: "",
        key: Date.now(),
      });
    },
    //电话号码校验
    changeMobile(val) {
      if (val != null && val != "") {
        //联通号码段:
        //130、131、132、145、155、156、166、175、176、185、186、196
        //联通虚拟运营商:
        //1704、1707、1708、1709、171、167
        // var reg = /(^1(3[0-2]|4[5]|5[56]|6[67]|7[156]|8[56]|9[6])\d{8}$)|(^170[4789]\d{7}$)/;
        var reg = /^1[3456789]{1}\d{9}$/;
        if (!reg.test(val)) {
          this.$message({
            message: "您输入的手机号格式有误或不是联通号码！",
            type: "warning",
          });
          return true;
        }
      }
      return false;
    },
    entering() {
      if (
        this.organizeForm.builderZsList &&
        this.organizeForm.builderZsList.length > 0
      ) {
        this.oneKeyForm.agentPeople = this.organizeForm.builderZsList.map(
          item => {
            return item.tabName;
          }
        );
      }
      if (
        this.organizeForm.builderCsList &&
        this.organizeForm.builderCsList.length > 0
      ) {
        this.oneKeyForm.copyPeople = this.organizeForm.builderCsList.map(
          item => {
            return item.tabName;
          }
        );
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.el-input.is-disabled ::v-deep .el-input__inner {
  color: #000;
  background-color: white;
}

::v-deep .el-dialog {
  border: 1px solid #eaeefb;
  margin-top: 6vh;
  .el-dialog__header {
    border-bottom: 1px solid #eaeefb;
  }
  .foolter_button {
    .el-form-item__content {
      float: right;
    }
  }
  .elTagBox {
    display: flex;
    flex-direction: column;
    width: 370px;
    .el-tag {
      margin-bottom: 10px;
    }
  }
  .onKeyForm_content {
    width: 628px;
    height: 173px;
    border: 1px solid rgb(133, 133, 133);
    padding: 10px;
    overflow: hidden;
    .onKeyForm_text_top,
    .onKeyForm_text_bottom {
      p {
        margin: 0;
      }
      .box_top_name {
        color: rgb(133, 133, 133);
      }
      .sentRemind {
        width: 86px;
        .el-input__inner {
          border: none;
          padding: 0;
        }
      }
      .copyRemind {
        width: 150px;
        .el-input__inner {
          border: none;
          padding: 0;
        }
      }
      .box_bottom_text {
        width: 93px;
        .el-input__inner {
          border: none;
          padding: 0;
        }
      }
      .box_top_text {
        width: 146px;
        .el-input__inner {
          border: none;
          padding: 0;
        }
      }
    }
  }
}
</style>
