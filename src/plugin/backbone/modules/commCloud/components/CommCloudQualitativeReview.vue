<template>
  <div class="qualitative-review">
    <el-form
      ref="qualitativeReviewForm"
      :inline="false"
      class="demo-form-inline"
      :model="qualitativeReviewForm"
      label-width="140px"
      :rules="qualitativeReviewFormRules"
    >
      <el-card
        shadow="never"
        header="故障定性信息"
        :body-style="{ padding: '20px 8px' }"
        class="cus-card"
      >
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item
              label="故障所属专业:"
              prop="professionalType"
              :rules="{
                required: true,
                message: '请选择故障所属专业',
              }"
            >
              <dict-select
                :value.sync="qualitativeReviewForm.professionalType"
                :dictId="10002"
                placeholder=""
                style="width: 100%"
                :notSelect="true"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障发生时间:" prop="alarmCreateTime" required>
              {{ qualitativeReviewForm.alarmCreateTime }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障通知时间:" prop="sheetCreateTime" required>
              {{ qualitativeReviewForm.sheetCreateTime }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="业务恢复时间:" prop="busRecoverTime">
              <el-date-picker
                v-model="qualitativeReviewForm.busRecoverTime"
                type="datetime"
                placeholder="请选择时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
                disabled
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="业务恢复历时:">
              {{ second2Time(qualitativeReviewForm.busRecoverDuration) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障结束时间:" prop="faultEndTime">
              <el-date-picker
                v-model="qualitativeReviewForm.faultEndTime"
                type="datetime"
                placeholder="请选择时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
                style="width: 100%"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障处理历时:" required>
              {{ second2Time(this.qualitativeReviewForm.faultDuration) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="故障发生地区:"
              prop="faultRegion"
              :rules="{
                required: true,
                message: '请选择内容',
              }"
            >
              <el-select
                style="width: 100%"
                placeholder="请选择内容"
                v-model="qualitativeReviewForm.faultRegion"
                filterable
                disabled
              >
                <el-option
                  v-for="(item, i) in faultRegionOptions"
                  :key="i"
                  :label="item.name"
                  :value="item.name"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障处理部门:" prop="dept" required>
              {{ qualitativeReviewForm.dept }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="处理人:" prop="person" required>
              {{ qualitativeReviewForm.person }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="挂起历时:" required>
              {{ second2Time(this.qualitativeReviewForm.suspendDuration) }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="故障处理净历时:" required>
              {{ second2Time(this.qualitativeReviewForm.processDuration) }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item label="是否影响业务:" prop="isEffectBusiness">
              <el-radio-group
                v-model="qualitativeReviewForm.isEffectBusiness"
                style="width: 100%"
                disabled
              >
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item
              label="影响范围:"
              v-if="qualitativeReviewForm.isEffectBusiness == '1'"
              prop="effectRange"
              :rules="[
                {
                  required:
                    qualitativeReviewForm.isEffectBusiness == '1'
                      ? true
                      : false,
                  message: '请填写影响范围',
                  trigger: 'blur',
                },
              ]"
            >
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="qualitativeReviewForm.effectRange"
                style="width: 100%"
                disabled
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item label="是否需要定性:" prop="needDefine">
              <el-radio-group
                v-model="qualitativeReviewForm.needDefine"
                style="width: 100%"
                disabled
              >
                <el-radio label="0" disabled>否</el-radio>
                <el-radio label="1" disabled>是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row> -->
      </el-card>
      <el-card
        shadow="never"
        header="故障专业信息"
        :body-style="{ padding: '20px 8px' }"
        class="cus-card"
      >
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8" v-if="qualitativeReviewForm.faultReasonType">
            <el-form-item
              label="故障分类:"
              prop="faultCate"
              :rules="{
                required: true,
                message: '请选择故障分类',
              }"
            >
              <dict-select
                :value.sync="qualitativeReviewForm.faultCate"
                :dictId="10060"
                style="width: 100%"

              />
            </el-form-item>
          </el-col>
          <el-col :span="8"  v-if="qualitativeReviewForm.faultReasonType">
            <el-form-item
              label="故障原因:"
              prop="faultReason"
              :rules="{
                required: true,
                message: '请选择故障原因',
              }"
            >
              <dict-select
                :value.sync="qualitativeReviewForm.faultReason"
                :dictId="10059"
                style="width: 100%"
                placeholder="请选择内容"
              />
            </el-form-item>
          </el-col>

          <el-col :span="8" v-if="!qualitativeReviewForm.faultReasonType">
            <el-form-item
              label="故障分类:"
              prop="faultCate"
              :rules="{
                required: true,
                message: '请选择故障分类',
              }"
            >
              <dict-select
                :value.sync="qualitativeReviewForm.faultCate"
                :dictId="10065"
                style="width: 100%"
                @change="changeFaultCate"
              />
            </el-form-item>
          </el-col>

          <el-col :span="8" v-if="!qualitativeReviewForm.faultReasonType">
            <el-form-item
              label="故障原因:"
              prop="faultReason"
              :rules="{
                required: true,
                message: '请选择故障原因',
              }"
            >

              <el-select
                v-model="qualitativeReviewForm.faultReason"
                placeholder="请选择故障原因"
                style="width: 100%"
              >
                <el-option
                  v-for="item in faultReasonOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>

            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="设备类型:" prop="eqpType">
              <dict-select
                :value.sync="qualitativeReviewForm.eqpType"
                :dictId="10058"
                style="width: 100%"
                placeholder="请选择内容"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="设备名称:">
              <el-input
                placeholder="请输入内容"
                v-model="qualitativeReviewForm.eqpName"
                style="width: 100%"
              >
                <el-button
                  style="
                    background: #b50b14;
                    border-color: #b50b14;
                    color: #fff;
                    padding: 9px 20px;
                    top: -0.5px;
                    position: relative;
                  "
                  slot="append"
                  @click="deviceSelect"
                  >选择</el-button
                >
              </el-input>
              <form
                id="sub__device"
                name="sub__device"
                hidden="true"
                method="post"
                action="/resweb_jituan/union/resAssign.out?method=selectEquipment"
                target="_blank"
              >
                <input type="hidden" name="device" id="device" />
              </form>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="是否硬件故障:"
              prop="isHardFault"
              :rules="{
                required: true,
                message: '请选择是否硬件故障',
              }"
            >
              <el-radio-group
                v-model="qualitativeReviewForm.isHardFault"
                style="width: 100%"
              >
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="业务名称:"
              :rules="{
                required: true,
                message: '请输入业务名称',
              }"
              prop="busName"
            >
              <el-input
                placeholder="请输入内容"
                v-model="qualitativeReviewForm.busName"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="8">
            <el-form-item label="紧急程度:" prop="emergencyLevel">
              <dict-select :value.sync="qualitativeReviewForm.emergencyLevel" :dictId="10001" style="width: 100%" :notSelect="true"/>
            </el-form-item>
          </el-col> -->
          <el-col :span="8">
            <el-form-item label="紧急程度:" prop="emergencyLevel">
              <el-input
                v-model="qualitativeReviewForm.emergencyLevel"
                :disabled="true"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item
              label="故障原因描述:"
              prop="falutReasonDesc"
              :rules="{
                required: true,
                message: '请填写内容',
              }"
            >
              <el-input
                maxlength="500"
                show-word-limit
                type="textarea"
                :rows="2"
                v-model="qualitativeReviewForm.falutReasonDesc"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注:">
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="qualitativeReviewForm.falutComment"
                style="width: 100%"
              >
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="相关附件:">
              <el-tag
                class="fileName_style_download"
                closable
                v-for="(item, index) in fddxFileArr"
                :key="index"
                @close="closeAndDeleteFile(item)"
                @click="downloadAppendixFile(item)"
                :title="item.name"
              >
                <div class="text-truncate">{{ item.name }}</div>
              </el-tag>
              <el-tag
                class="fileName_style"
                closable
                v-for="(item, index) in importForm.relatedFilesFileList"
                :key="index"
                @close="closeFile(item)"
                :title="item.name"
              >
                <div class="text-truncate">{{ item.name }}</div>
              </el-tag>
              <el-button size="mini" type="primary" @click="relatedFilesBrowse"
                >+上传附件</el-button
              >
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card
        shadow="never"
        header="定性审核信息"
        :body-style="{ padding: '20px 8px' }"
        style="margin-top: 20px"
        class="cus-card"
      >
        <el-row type="flex" style="flex-wrap: wrap" :gutter="20">
          <el-col :span="8">
            <el-form-item
              label="审批结果:"
              prop="auditResult"
              :rules="{
                required: true,
                message: '请选择审批结果',
              }"
            >
              <el-radio-group
                v-model="qualitativeReviewForm.auditResult"
                @change="changeAuditResult()"
              >
                <el-radio label="1">同意</el-radio>
                <el-radio label="0">拒绝</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="是否上传故障报告:"
              :rules="{
                required: true,
                message: '请选择是否上传故障报告',
              }"
              prop="isUploadReport"
            >
              <el-radio-group
                v-model="qualitativeReviewForm.isUploadReport"
                @change="changeUploadReport"
                :disabled="qualitativeReviewForm.auditResult == '1'"
              >
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              label="是否故障处理结束:"
              :rules="{
                required: true,
                message: '请选择是否故障处理结束',
              }"
              prop="isExeOver"
            >
              <el-radio-group
                v-model="qualitativeReviewForm.isExeOver"
                :disabled="qualitativeReviewForm.auditResult == '1'"
                @change="changeUploadReport"
              >
                <el-radio label="0">否</el-radio>
                <el-radio label="1">是</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="审批意见:" prop="auditContent">
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model.trim="qualitativeReviewForm.auditContent"
                style="width: 100%"
                show-word-limit
                maxlength="255"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
    </el-form>
    <div slot="footer" style="padding: 10px 20px; text-align: center">
      <el-button
        type="primary"
        @click="handleSubmit('qualitativeReviewForm')"
        v-loading.fullscreen.lock="qualitativeReviewFullscreenLoading"
        >提交</el-button
      >
      <el-button @click="onReset">重 置</el-button>
    </div>

    <el-dialog
      width="420px"
      title="附件选择"
      :visible.sync="relatedFilesDialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      append-to-body
    >
      <file-upload
        @change="changeFileData"
        @cancel="closeAttachmentDialog"
      ></file-upload>
    </el-dialog>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import DictSelect from "../../workOrder/components/DictSelect.vue";

import {
  apiQualitativeDetail,
  apiQualitativeReview,
} from "../api/CommCloudQualitativeReview";
import { apiDict } from "../../workOrder/api/CommonApi";
import FileUpload from "../../workOrder/components/FileUpload.vue";
import {
  apiDownloadAppendixFile,
  apiDeleteFdFile,
} from "../../workOrder/workOrderWaitDetail/api/CommonApi";
import { mixin } from "../../../../../mixins";
import { apiEnumListNew } from "@plugin/backbone/modules/supervise/workOrderWaitDetail/api/BackSingle";
export default {
  name: "QualitativeReview",
  props: {
    common: Object,
  },
  components: { DictSelect, FileUpload },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  mixins: [mixin],
  data() {
    return {
      qualitativeReviewForm: {
        woId: null,
        workItemId: null,
        processInstId: null,
        processDefId: null,
        professionalType: null,
        alarmCreateTime: null,
        sheetCreateTime: null,
        busRecoverTime: null, //业务恢复时间
        busRecoverDuration: 0, //业务恢复历时
        faultEndTime: null,
        faultDuration: 0, //故障处理历时
        faultRegion: null,
        dept: null,
        person: null,
        suspendDuration: 0, //挂起历时 单位秒
        processDuration: 0, //故障处理净历时 单位秒
        isEffectBusiness: null,
        effectRange: null,
        //专业信息
        faultCate: null,
        opticFiber: "",
        relatedFiles: null,
        effectSystem: "",
        falutReasonDesc: null,
        falutComment: null,
        eqpType: null,
        eqpName: "",
        busName: null,
        emergencyLevel: null,
        isHardFault: null,
        //审核信息
        auditResult: null,
        isUploadReport: "0",
        isExeOver: null,
        auditContent: null,
        linkId: null,
        appendix: null,
        faultReason: null,
        faultReasonType:false
      },
      qualitativeReviewFormRules: {
        auditContent: [
          {
            required: true,
            message: "请填写审批意见",
          },
          {
            validator: this.checkLength,
            max: 255,
            form: "qualitativeReviewForm",
            messsage: "已超过填写字数上限",
          },
        ],
      },
      backSingleFullscreenLoading: false,
      faultRegionOptions: [],
      //关联附件
      relatedFilesDialogVisible: false,
      importForm: {
        relatedFilesFileList: [],
      },
      qualitativeReviewFullscreenLoading: false,
      fddxFileArr: [], // 附件
      faultReasonOption: [],
    };
  },
  mounted() {
    this.qualitativeReviewForm.workItemId = this.common.workItemId;
    this.qualitativeReviewForm.woId = this.common.woId;
    this.qualitativeReviewForm.processInstId = this.common.processInstId;
    this.qualitativeReviewForm.processDefId = this.common.processDefId;
    this.qualitativeReviewForm.emergencyLevel = this.common.emergencyLevel;
    this.qualitativeReviewDetail();
    console.log(this.qualitativeReviewForm.emergencyLevel);
    console.log(this.common.emergencyLevel);
    this.resourceBackInit(this.qualitativeReviewForm);
  },
  methods: {
    changeFaultCate(type) {
      let enumMap=  new Map([
        ['6', '12331'],
        ['7', '12332'],
        ['8', '12333'],
        ['9', '12334'],
        ['10', '12335'],
        ['11', '12336'],
        ['12', '12337'],
      ]);
      if (type != "init") {
        this.qualitativeReviewForm.faultReason = "";
      }
      let params = {
        dictTypeCode: "10066",
        parentCode: enumMap.get(type),
      };
      return new Promise(resolve => {
        apiEnumListNew(params).then(res => {
          if (res.code == 200) {
            this.faultReasonOption = res?.data ?? [];
          }
          resolve("success");
        });
      });

    },
    qualitativeReviewDetail() {
      let param = {
        workItemId: this.qualitativeReviewForm.workItemId,
      };
      apiQualitativeDetail(param)
        .then(res => {
          if (res.status == "0") {
            let self = this;
            self.qualitativeReviewForm = res?.data?.rows?.[0] ?? {};
            self.qualitativeReviewForm.emergencyLevel =
              self.common.emergencyLevel;
            if (res.data.rows[0].appendix) {
              this.fddxFileArr = JSON.parse(res.data.rows[0].appendix);
            }

            let sheetCreateTime= res?.data?.rows?.[0]?.sheetCreateTime;
            if(sheetCreateTime>'2025-01-10 00:30:00'){
              this.qualitativeReviewForm.faultReasonType=false;
            }else {
              this.qualitativeReviewForm.faultReasonType=true;
            }
            self.changeFaultCate("init").then(() => {
              self.qualitativeReviewForm.faultReason =
                res?.data?.rows?.[0]?.faultReason ?? "";
            });

            this.$set(self.qualitativeReviewForm, "isExeOver", "1");
            this.$set(self.qualitativeReviewForm, "isUploadReport", "0");
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    relatedFilesBrowse() {
      this.relatedFilesDialogVisible = true;
    },
    changeFileData(data) {
      this.qualitativeReviewForm.relatedFiles = data.fileName;
      this.importForm.relatedFilesFileList = data.attachmentFileList;
      this.relatedFilesDialogVisible = false;
    },
    closeAttachmentDialog() {
      this.relatedFilesDialogVisible = false;
    },
    handleSubmit(formName) {
      this.$refs[formName].validate((valid, a) => {
        if (this.uncheck(a)) {
          this.qualitativeReviewFullscreenLoading = true;
          this.$set(this.qualitativeReviewForm, "actionName", "定性审核");
          let formData = new FormData();
          if (this.importForm.relatedFilesFileList.length > 0) {
            for (let item of this.importForm.relatedFilesFileList) {
              formData.append("files", item.raw);
            }
          } else {
            formData.append("files", "");
          }
          formData.append(
            "jsonParam",
            JSON.stringify(this.qualitativeReviewForm)
          );
          formData.append(
            "checkParam",
            JSON.stringify(this.qualitativeReviewForm)
          );
          apiQualitativeReview(formData)
            .then(res => {
              if (res.status == "0") {
                this.$message.success("故障定性审核完成");
                this.$emit("qualitativeReviewSubmit", res.data);
              } else {
                this.$message.error("故障定性审核失败");
              }
              this.qualitativeReviewFullscreenLoading = false;
            })
            .catch(error => {
              console.log(error);
              this.$message.error("故障定性审核失败");
              this.qualitativeReviewFullscreenLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    closeAndDeleteFile(tag) {
      this.deleteFile(tag);
    },
    deleteFile(tag) {
      let param = {
        attId: tag.id,
        linkId: this.qualitativeReviewForm.linkId,
      };
      apiDeleteFdFile(param)
        .then(res => {
          if (res.status == "0") {
            this.fddxFileArr.splice(this.fdFileArr.indexOf(tag), 1);
            this.qualitativeReviewForm.appendix = JSON.stringify(
              this.fddxFileArr
            );
            this.$message.success("附件删除成功");
          } else {
            this.$$message.error("附件删除失败");
          }
        })
        .catch(error => {
          this.$message.error("附件删除失败");
          console.log(error);
        });
    },
    closeFile(tag) {
      this.importForm.relatedFilesFileList.splice(
        this.importForm.relatedFilesFileList.indexOf(tag),
        1
      );
    },
    //审批结果事件
    changeAuditResult() {
      if (this.qualitativeReviewForm.auditResult == "1") {
        this.qualitativeReviewForm.isUploadReport = "0";
        this.qualitativeReviewForm.isExeOver = "1";
        this.$set(this.qualitativeReviewForm, "auditContent", "");
      } else if (this.qualitativeReviewForm.auditResult == "0") {
        this.qualitativeReviewForm.isUploadReport = "0";
        this.qualitativeReviewForm.isExeOver = "1";
        this.$set(this.qualitativeReviewForm, "auditContent", "驳回");
      }
    },
    changeUploadReport() {
      if (
        this.qualitativeReviewForm.isUploadReport == "1" &&
        this.qualitativeReviewForm.isExeOver == "0" &&
        this.qualitativeReviewForm.auditResult == "0"
      ) {
        this.$set(
          this.qualitativeReviewForm,
          "auditContent",
          "驳回，故障处理未结束,请上传故障报告"
        );
      } else if (
        this.qualitativeReviewForm.isUploadReport == "0" &&
        this.qualitativeReviewForm.isExeOver == "1" &&
        this.qualitativeReviewForm.auditResult == "0"
      ) {
        this.$set(this.qualitativeReviewForm, "auditContent", "驳回");
      } else if (
        this.qualitativeReviewForm.isUploadReport == "0" &&
        this.qualitativeReviewForm.isExeOver == "0" &&
        this.qualitativeReviewForm.auditResult == "0"
      ) {
        this.$set(
          this.qualitativeReviewForm,
          "auditContent",
          "驳回，故障处理未结束"
        );
      } else if (
        this.qualitativeReviewForm.isUploadReport == "1" &&
        this.qualitativeReviewForm.isExeOver == "1" &&
        this.qualitativeReviewForm.auditResult == "0"
      ) {
        this.$set(
          this.qualitativeReviewForm,
          "auditContent",
          "驳回，请上传故障报告"
        );
      }
    },
    downloadAppendixFile(data) {
      let param = {
        attId: data.id,
      };
      apiDownloadAppendixFile(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("文件下载成功");
          } else {
            this.$message.error("文件下载失败");
          }
        })
        .catch(error => {
          console.log(error);
          this.$message.error("文件下载失败");
        });
    },
    onReset() {
      // this.qualitativeReviewForm = {
      //   ...this.$options.data,
      //   woId: this.common.woId,
      //   workItemId: this.workItemId,
      //   processInstId: this.common.processInstId,
      //   processDefId: this.common.processDefId,
      //   faultReason: null,
      // };
      // this.importForm.relatedFilesFileList = [];
      // this.qualitativeReviewDetail();
      this.qualitativeReviewForm.faultCate = null;
      this.qualitativeReviewForm.faultReason = null;
      this.qualitativeReviewForm.eqpType = null;
      this.qualitativeReviewForm.eqpName = null;
      this.qualitativeReviewForm.isHardFault = null;
      this.qualitativeReviewForm.busName = null;
      this.qualitativeReviewForm.falutReasonDesc = null;
      this.qualitativeReviewForm.falutComment = null;
      this.qualitativeReviewForm.auditResult = null;
      this.qualitativeReviewForm.isUploadReport = "0";
      this.qualitativeReviewForm.isExeOver = "1";
      this.qualitativeReviewForm.auditContent = null;
      this.qualitativeReviewForm.emergencyLevel = this.common.emergencyLevel;
      this.importForm.relatedFilesFileList = [];
    },
    //设备名称选择
    deviceSelect() {
      let jsonStr =
        '{"userId":"302785","username":"yangxm97","callSysId":"0001","callSysName":"电子运维"}';
      this.$nextTick(() => {
        document.querySelector("#device").value = jsonStr;
        document.querySelector("#sub__device").submit();
      });
    },
    second2Time(days) {
      return this.showTime(Math.abs(days));
    },
    showTime(val) {
      if (val) {
        if (val == 0) return "0秒";
        var time = "";
        var second = val % 60;
        var minute = parseInt(parseInt(val) / 60) % 60;
        time = second + "秒";
        if (minute >= 1) {
          time = minute + "分" + second + "秒";
        }
        var hour = parseInt(parseInt(val / 60) / 60) % 24;
        if (hour >= 1) {
          time = hour + "小时" + minute + "分" + second + "秒";
        }
        var day = parseInt(parseInt(parseInt(val / 60) / 60) / 24);
        if (day >= 1) {
          time = day + "天" + hour + "小时" + minute + "分" + second + "秒";
        }

        return time;
      } else {
        return "0秒";
      }
    },
    //资源系统回调方法初始化
    resourceBackInit() {
      window["__process_response_from_getDevice"] = value => {
        let form = this.qualitativeReviewForm;
        let result = JSON.parse(value);
        let objArray = JSON.parse(result?.objects);

        if (form.faultCate == "1") {
          let opticFiberArray = form.opticFiber
            ? form.opticFiber.split(",")
            : [];
          let effectSystemArray = form.effectSystem
            ? form.effectSystem.split(",")
            : [];
          let effectSystemArrayTemp = [];

          objArray.forEach(obj => {
            if (opticFiberArray.indexOf(obj.resName) < 0) {
              opticFiberArray.push(obj.resName);
            }

            effectSystemArrayTemp = JSON.parse(obj.affectedSystem);
            effectSystemArrayTemp.forEach(sys => {
              if (sys.sysName && effectSystemArray.indexOf(sys.sysName) < 0) {
                effectSystemArray.push(sys.sysName);
              }
            });
          });

          form.opticFiber = opticFiberArray.join(",");
          form.effectSystem = effectSystemArray.join(",");
          form.eqpName = "";
        } else {
          let eqpNameArray = form.eqpName ? form.eqpName.split(",") : [];

          objArray.forEach(obj => {
            if (eqpNameArray.indexOf(obj.resName) < 0) {
              eqpNameArray.push(obj.resName);
            }
          });

          form.eqpName = eqpNameArray.join(",");
          form.opticFiber = "";
          form.effectSystem = "";
        }
      };
    },
  },
};
</script>
<style lang="scss" scoped>
.qualitative-review {
  max-height: calc(90vh - 150px);
  min-height: 400px;
  margin: 0 4px;
  padding-left: 4px;
  padding-right: 8px;
  overflow-y: auto;

  .cus-card ::v-deep .el-card__header {
    padding: 11px 24px 10px;
    font-weight: 400;

    @include themify {
      background-color: themed("$--background-color-base");
    }
  }

  .fileName_style_download {
    margin-right: 3px;
    cursor: pointer;
    vertical-align: middle;

    div {
      display: inline-block;
      max-width: 120px;
      vertical-align: top;
    }
  }

  .fileName_style {
    margin-right: 3px;
    vertical-align: middle;

    div {
      display: inline-block;
      max-width: 120px;
      vertical-align: top;
    }
  }
}
</style>
