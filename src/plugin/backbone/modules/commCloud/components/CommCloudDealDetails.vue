<template>
  <div>
    <el-card shadow="always" class="card" :body-style="{ padding: '10px 24px' }">
      <div class="header clearfix">
        <span class="header-title">处理详情</span>
      </div>
      <div class="content">
        <el-collapse>
          <el-collapse-item v-if="TerminationOf">
            <span class="collapse-title" slot="title">异常终止</span>

            <div v-for="(abnor, key) of detailsList['abnormalEnd']
              .abnormalEndApplicationInfo" :key="key" class="content__list">
              <p class="detail-p" v-html="abnor"></p>
            </div>
          </el-collapse-item>
          <el-collapse-item v-if="determinApproveInfo">
            <span class="collapse-title" slot="title">定性审核</span>
            <div v-for="(deter, key) of detailsList['defineCheck']
              .determinApproveInfo" :key="key" class="content__list">
              <p class="detail-p" v-html="deter"></p>
            </div>
          </el-collapse-item>
          <el-collapse-item v-if="goback">
            <span class="collapse-title" slot="title">返单/定性</span>

            <div v-for="(deter, key) of detailsList['feedbackAndDefine']
              .determinInfo" :key="key" class="content__list">
              <p class="detail-p" v-html="deter" @click="handleBtnClick($event)"></p>
            </div>
          </el-collapse-item>

          <el-collapse-item v-if="turnToSend">
            <span class="collapse-title" slot="title">转派/重新转派</span>

            <div v-for="(reassign, key) of detailsList['reassign']
              .reassignOrderInfo" :key="key" class="content__list">
              <p class="detail-p" v-html="reassign"></p>
            </div>
          </el-collapse-item>
          <el-collapse-item v-if="clearAlarmShow">
            <span class="collapse-title" slot="title">人工清除告警</span>
            <div v-for="(clearAlarm, key) of detailsList['clearAlarm']
              .clearAlarmInfo" :key="key" class="content__list">
              <template v-if="clearAlarm.str.indexOf('@') == '-1'">
                <p class="detail-p" v-html="clearAlarm.str"></p>
              </template>
              <template v-else>
                <p class="detail-p">
                  <span v-html="clearAlarm.str.split('@')[0]"></span>
                  <el-tag v-for="(item, index) in JSON.parse(clearAlarm.appendix)" class="fileName_style" :key="index"
                    @click="previewAppendixFile(item)" v-loading.fullscreen.lock="appendixFileLoading"
                    :title="item.name">
                    <div class="text-truncate">{{ item.name }}</div>
                  </el-tag>
                  <span v-html="clearAlarm.str.split('@')[1]"> </span>
                </p>
              </template>
            </div>
          </el-collapse-item>

          <el-collapse-item v-if="hang">
            <span class="collapse-title" slot="title">挂起/解挂</span>

            <div v-for="(lication, key) of detailsList['pending']
              .pendingApplicationInfo" :key="key" class="content__list">
              <template v-if="lication.str.indexOf('@') == '-1'">
                <p class="detail-p" v-html="lication.str"></p>
              </template>
              <template v-else>
                <p class="detail-p">
                  <span v-html="lication.str.split('@')[0]"></span>
                  <template v-if="lication.appendix">
                    <el-tag v-for="(item, index) in JSON.parse(lication.appendix)" class="fileName_style" :key="index"
                      @click="previewAppendixFile(item)" v-loading.fullscreen.lock="appendixFileLoading"
                      :title="item.name">
                      <div class="text-truncate">{{ item.name }}</div>
                    </el-tag>
                  </template>
                  <span v-html="lication.str.split('@')[1]"> </span>
                </p>
              </template>
            </div>
          </el-collapse-item>
          <el-collapse-item v-if="chase">
            <span class="collapse-title" slot="title">追单</span>
            <div v-if="senderAddedInfo">
              <div v-for="(sender, key) of detailsList['addInfo'].senderAddedInfo" :key="key" class="content__list">
                <template v-if="sender.str.indexOf('@') == '-1'">
                  <p class="detail-p" v-html="sender.str"></p>
                </template>
                <template v-else>
                  <p class="detail-p">
                    <span v-html="sender.str.split('@')[0]"></span>
                    <!-- <el-link
                    v-for="(item, index) in JSON.parse(sender.appendix)"
                    :key="index"
                    @click="downloadAppendixFile(item)"
                    :title="item.name"
                  >
                    <div
                      class="text-truncate"
                      style="max-width: 140px"
                      v-if="index == 0"
                    >
                      {{ item.name }}
                    </div>
                    <div class="text-truncate" style="max-width: 140px" v-else>
                      ；{{ item.name }}
                    </div>
                  </el-link> -->
                    <el-tag v-for="(item, index) in JSON.parse(sender.appendix)" class="fileName_style" :key="index"
                      @click="previewAppendixFile(item)" v-loading.fullscreen.lock="appendixFileLoading"
                      :title="item.name">
                      <div class="text-truncate">{{ item.name }}</div>
                    </el-tag>
                    <span v-html="sender.str.split('@')[1]"> </span>
                  </p>
                </template>
              </div>
            </div>
          </el-collapse-item>
          <el-collapse-item v-if="stageFeedbackInfo">
            <span class="collapse-title" slot="title">阶段反馈</span>

            <div v-for="(stage, key) of detailsList['stageFeedback']
              .stageFeedbackInfo" :key="key" class="content__list">
              <p class="detail-p" v-html="stage"></p>
            </div>
          </el-collapse-item>
          <el-collapse-item v-if="accept">
            <span class="collapse-title" slot="title">受理情况</span>

            <div v-for="(age, key) of detailsList['accept'].agentAcceptInfo" :key="key" class="content__list">
              <p class="detail-p" v-html="age"></p>
            </div>
          </el-collapse-item>
          <el-collapse-item v-if="pendingInfo">
            <span class="collapse-title" slot="title">工单派发</span>

            <div v-for="(pend, key) of detailsList['waitingAccept'].pendingInfo" :key="key" class="content__list">
              <p class="detail-p" v-html="pend"></p>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-card>
    <el-dialog title="反馈单详情" :visible.sync="feedbackHistoryVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" @close="dialogBackSingleClose" :fullscreen="false" width="83%" top="5vh">
      <div class="content">
        <el-descriptions title="故障定性信息" class="descriptions">
          <template>
            <el-descriptions-item label="故障所属专业">{{
              list.professionalType
              }}</el-descriptions-item>
            <el-descriptions-item label="故障发生时间">{{
              list.alarmCreateTime
              }}</el-descriptions-item>
            <el-descriptions-item label="故障通知时间">
              {{ list.sheetCreateTime }}
            </el-descriptions-item>
            <el-descriptions-item label="业务恢复时间">{{
              list.busRecoverTime
              }}</el-descriptions-item>
            <el-descriptions-item label="业务恢复历时">{{
              busRecoverDuration
              }}</el-descriptions-item>
            <el-descriptions-item label="故障结束时间">
              {{ list.faultEndTime }}</el-descriptions-item>
            <el-descriptions-item label="故障处理历时">
              {{ troubleshootingDuration }}
            </el-descriptions-item>
            <el-descriptions-item label="故障发生地区">
              {{ list.faultRegion }}
            </el-descriptions-item>
            <el-descriptions-item label="故障处理部门">
              {{ list.dept }}
            </el-descriptions-item>
            <el-descriptions-item label="处理人">{{
              list.person
              }}</el-descriptions-item>
            <el-descriptions-item label="挂起历时">{{
              suspendDuration
              }}</el-descriptions-item>
            <el-descriptions-item label="故障处理净历时">
              {{ processDuration }}
            </el-descriptions-item>
            <el-descriptions-item label="是否影响业务">
              <span v-if="list.isEffectBusiness">
                {{ list.isEffectBusiness }}</span><span v-else>否</span></el-descriptions-item>
            <el-descriptions-item label="影响范围" v-if="list.isEffectBusiness == '是'" :span="2">{{ list.effectRange }}
            </el-descriptions-item>
            <el-descriptions-item label="是否需要定性">{{
              list.needDefine
              }}</el-descriptions-item>
          </template>
        </el-descriptions>
        <el-descriptions title="故障专业信息" class="descriptions">
          <el-descriptions-item label="业务名称">
            {{ list.busName }}
          </el-descriptions-item>
          <el-descriptions-item label="紧急程度">
            {{ common.emergencyLevel }}
          </el-descriptions-item>
          <el-descriptions-item label="故障分类">
            {{ list.faultCate }}
          </el-descriptions-item>
          <el-descriptions-item label="故障原因">
            {{ list.faultReason }}</el-descriptions-item>
          <el-descriptions-item label="设备类型">
            {{ list.eqpType }}
          </el-descriptions-item>
          <el-descriptions-item label="设备名称">
            {{ list.eqpName }}
          </el-descriptions-item>
          <el-descriptions-item label="附件">
            <el-tag v-for="(item, index) in attachmentArr" class="fileName_style" :key="index"
              @click="previewAppendixFile(item)" v-loading.fullscreen.lock="appendixFileLoading" :title="item.name">
              <div class="text-truncate">{{ item.name }}</div>
            </el-tag>
            <span v-if="attachmentArr.length <= 0"> 无 </span>
          </el-descriptions-item>
          <el-descriptions-item label="是否硬件故障">
            {{ list.isHardFault }}
          </el-descriptions-item>
          <el-descriptions-item label="故障原因描述" :span="6">
            {{ list.faultReasonDesc }}
          </el-descriptions-item>
          <br />
          <el-descriptions-item label="备注" :span="6">
            {{ list.faultComment }}
          </el-descriptions-item>
        </el-descriptions>
        <el-descriptions title="返单信息" v-if="list.auditResult && list.auditResult != '无'" class="descriptions">
          <el-descriptions-item label="审批结果">
            <span v-if="list.auditResult == 0">拒绝</span><span v-else-if="list.auditResult == 1">同意</span>
            <span v-else>无</span>
          </el-descriptions-item>
          <el-descriptions-item label="审批意见">
            {{ list.auditContent }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>

    <!-- 使用图片预览组件 -->
    <image-preview :visible.sync="imagePreviewVisible" :file-data="currentPreviewFile"
      preview-api="/commonDict/attach/download" :use-custom-download="true" @download="downloadCurrentFile"
      @close="imagePreviewVisible = false"></image-preview>
  </div>
</template>

<script>
//接口出来更换
import {
  apiGetProcessInfo,
  apiGetFeedbackHistory,
} from "../api/CommCloudDealDetails";

import { apiDownloadAppendixFile } from "../../workOrder/workOrderWaitDetail/api/CommonApi";
import ImagePreview from "@plugin/backbone/components/ImagePreview.vue";

export default {
  name: "DealDetails",
  components: {
    ImagePreview
  },
  props: {
    woId: String,
    common: Object,
  },
  data() {
    return {
      detailsList: {
        accept: {
          agentAcceptInfo: [],
        },
        waitingAccept: {
          pendingInfo: [],
        },
        addInfo: {
          senderAddedInfo: [],
        },
        stageFeedback: {
          stageFeedbackInfo: [],
        },
        pending: {
          pendingApplicationInfo: [],
        },
        feedbackAndDefine: {
          determinInfo: [],
        },
        defineCheck: {
          determinApproveInfo: [],
        },
        abnormalEnd: {
          abnormalEndApplicationInfo: [],
        },
        reassign: {
          reassignOrderInfo: [],
        },
        clearAlarm: {
          clearAlarmInfo: [],
        },
      },
      list: {},
      listAll: [],
      attachmentArr: [], //附件
      feedbackHistoryVisible: false,
      busRecoverDuration: null, //业务恢复历时
      troubleshootingDuration: null, //故障处理历时
      suspendDuration: null, //挂起历时
      processDuration: null, //故障处理净历时
      appendixFileLoading: false,
      // 图片预览相关
      imagePreviewVisible: false,
      currentPreviewFile: {},
    };
  },
  mounted() {
    this.getProcessInfo();
  },
  computed: {
    accept() {
      if (this.detailsList["accept"].agentAcceptInfo.length > 0) {
        return true;
      } else {
        return false;
      }
    },
    pendingInfo() {
      if (this.detailsList["waitingAccept"].pendingInfo.length > 0) {
        return true;
      } else {
        return false;
      }
    },
    chase() {
      if (this.detailsList["addInfo"].senderAddedInfo.length > 0) {
        return true;
      } else {
        return false;
      }
    },
    senderAddedInfo() {
      if (this.detailsList["addInfo"].senderAddedInfo.length > 0) {
        return true;
      } else {
        return false;
      }
    },
    stageFeedbackInfo() {
      if (this.detailsList["stageFeedback"].stageFeedbackInfo.length > 0) {
        return true;
      } else {
        return false;
      }
    },
    hang() {
      if (this.detailsList["pending"].pendingApplicationInfo.length > 0) {
        return true;
      } else {
        return false;
      }
    },
    goback() {
      if (this.detailsList["feedbackAndDefine"].determinInfo.length > 0) {
        return true;
      } else {
        return false;
      }
    },
    determinApproveInfo() {
      if (this.detailsList["defineCheck"].determinApproveInfo.length > 0) {
        return true;
      } else {
        return false;
      }
    },
    TerminationOf() {
      if (
        this.detailsList["abnormalEnd"].abnormalEndApplicationInfo.length > 0
      ) {
        return true;
      } else {
        return false;
      }
    },
    turnToSend() {
      if (this.detailsList["reassign"].reassignOrderInfo.length > 0) {
        return true;
      } else {
        return false;
      }
    },
    clearAlarmShow() {
      return this.detailsList["clearAlarm"].clearAlarmInfo.length > 0;
    },
  },
  methods: {
    handleBtnClick(e) {
      if (e.target.id === "btn") {
        const linkId = e.target.dataset.linkid;
        this.getFeedbackHistory(linkId);
        this.feedbackHistoryVisible = true;
      }
    },
    getFeedbackHistory(linkId) {
      let param = {
        linkId: linkId,
      };
      apiGetFeedbackHistory(param).then(res => {
        if (res.status == 0) {
          let self = this;
          self.listAll = res?.data?.rows ?? [];
          if (self.listAll.length > 0) {
            self.list = self.listAll[0];
            self.workItemId = self.list.workItemId;
            self.isUploadReport = self.list.isUploadReport;
            self.busRecoverDuration = self.showTime(
              self.list.busRecoverDuration
            );
            self.troubleshootingDuration = self.showTime(
              self.list.faultDuration
            );
            self.suspendDuration = self.showTime(self.list.suspendDuration);
            self.processDuration = self.showTime(self.list.processDuration);
          }
          if (self.list.appendix) {
            self.attachmentArr = JSON.parse(self.list.appendix);
          }
        }
      });
    },
    showTime(val) {
      if (val == 0 || val == "" || null == val) {
        return "0秒";
      }
      var time = "";
      var second = val % 60;
      var minute = parseInt(parseInt(val) / 60) % 60;
      time = second + "秒";
      if (minute >= 1) {
        time = minute + "分" + second + "秒";
      }
      var hour = parseInt(parseInt(val / 60) / 60) % 24;
      if (hour >= 1) {
        time = hour + "小时" + minute + "分" + second + "秒";
      }
      var day = parseInt(parseInt(parseInt(val / 60) / 60) / 24);
      if (day >= 1) {
        time = day + "天" + hour + "小时" + minute + "分" + second + "秒";
      }
      return time;
    },
    dialogBackSingleClose() {
      this.feedbackHistoryVisible = false;
    },
    getProcessInfo() {
      let param = {
        woId: this.woId,
      };
      apiGetProcessInfo(param)
        .then(res => {
          if (res.status == 0) {
            this.detailsList = res?.data || {};
          }
        })
        .catch(err => {
          console.log(err);
        });
    },
    // 预览附件文件
    previewAppendixFile(data) {
      // 首先检查文件名是否为图片类型
      const fileName = data.name.toLowerCase();
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'];

      // 检查文件扩展名是否为图片
      const isImage = imageExtensions.some(ext => fileName.endsWith(ext));

      if (!isImage) {
        // 如果不是图片，直接下载
        this.downloadAppendixFile(data);
        return;
      }

      // 如果是图片，保存当前文件信息并显示预览对话框
      this.currentPreviewFile = data;
      this.imagePreviewVisible = true;
    },

    // 下载当前预览的文件
    downloadCurrentFile() {
      this.downloadAppendixFile(this.currentPreviewFile);
    },

    // 下载附件文件
    downloadAppendixFile(data) {
      this.appendixFileLoading = true;
      let param = {
        attId: data.id,
      };
      apiDownloadAppendixFile(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("文件下载成功");
          } else {
            this.$message.error("文件下载失败");
          }
          this.appendixFileLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("文件下载失败");
          this.appendixFileLoading = false;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../../workOrder/workOrderWaitDetail/assets/common.scss";

::v-deep .el-collapse-item {
  .el-collapse-item__header {
    background-color: #fafafa;
    padding-left: 20px;
  }
}

::v-deep .content__list {
  // border-bottom: 1px #dcdcdc solid;
  padding-top: 10px;
  // padding-bottom: 5px;
  padding-left: 25px;
}

::v-deep .detail-p {
  display: block;
  padding: 0px 5px;
  margin: 0;
}
</style>
