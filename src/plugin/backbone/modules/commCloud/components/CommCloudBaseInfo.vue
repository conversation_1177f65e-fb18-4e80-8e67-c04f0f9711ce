<template>
  <el-card shadow="always" class="card" :body-style="{ padding: '10px 24px' }">
    <div class="header clearfix">
      <span class="header-title">基础信息</span>
      <div class="header-right">
        <el-button type="primary" size="mini" @click="exportBasicWorkOrderInfo"
          v-loading.fullscreen.lock="exportLoading">导出Excel表</el-button>
      </div>
    </div>
    <div class="content">
      <el-descriptions title="工单基本信息" class="descriptions">
        <el-descriptions-item label="所属专业">{{
          basicWorkOrderData.professionalType
          }}</el-descriptions-item>
        <el-descriptions-item label="告警类别">{{
          basicWorkOrderData.orgType
          }}</el-descriptions-item>
        <el-descriptions-item label="云池类型">{{
          basicWorkOrderData.cloudPoolType
          }}</el-descriptions-item>
        <el-descriptions-item label="故障现象" :span="3">
          <text-collapse :text="basicWorkOrderData.faultPhenomenon" :max-lines="2"></text-collapse>
        </el-descriptions-item>
        <el-descriptions-item label="业务名称"><span v-if="basicWorkOrderData.busName">{{
          basicWorkOrderData.busName
            }}</span>
          <span v-else>无</span>
        </el-descriptions-item>
        <el-descriptions-item label="云平台">{{
          basicWorkOrderData.cloudDC
          }}</el-descriptions-item>
        <el-descriptions-item label="工作内容">{{
          basicWorkOrderData.workContent
          }}</el-descriptions-item>
        <el-descriptions-item label="影响业务列表" v-if="impactServiceArr.length > 0">
          <el-tag class="fileName_style" v-for="(item, index) in impactServiceArr" :key="index"
            @click="previewAppendixFile(item)" v-loading.fullscreen.lock="appendixFileLoading" :title="item.name">
            <div class="text-truncate">{{ item.name }}</div>
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="影响业务列表" v-else>
          无
        </el-descriptions-item>
        <el-descriptions-item label="影响范围">{{
          basicWorkOrderData.effectRange
          }}</el-descriptions-item>
        <!-- <el-descriptions-item label="工单优先级">{{
          basicWorkOrderData.faultLevel
        }}</el-descriptions-item> -->
        <el-descriptions-item label="备注">{{
          basicWorkOrderData.faultComment
          }}</el-descriptions-item>
      </el-descriptions>
      <el-descriptions title="工单派送信息" class="descriptions">
        <el-descriptions-item label="附件" v-if="attachmentArr.length > 0">
          <el-tag v-for="(item, index) in attachmentArr" class="fileName_style" :key="index"
            @click="previewAppendixFile(item)" v-loading.fullscreen.lock="appendixFileLoading" :title="item.name">
            <div class="text-truncate">{{ item.name }}</div>
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="附件" v-else> 无 </el-descriptions-item>
        <el-descriptions-item label="建单人主送">
          {{
            stitchingAlgorithm(
              basicWorkOrderData.agentDeptName,
              basicWorkOrderData.agentMan
            )
          }}
        </el-descriptions-item>
        <el-descriptions-item label="建单人抄送">
          {{
            stitchingAlgorithm(
              basicWorkOrderData.copyDeptName,
              basicWorkOrderData.copyMan
            )
          }}
        </el-descriptions-item>
        <el-descriptions-item label="是否通知他人">{{
          basicWorkOrderData.isSendSms == 1 ? "是" : "否"
          }}</el-descriptions-item>
        <el-descriptions-item :span="2" v-if="basicWorkOrderData.isSendSms == 1" label="接收人">{{
          stitchingAlgorithm(
            basicWorkOrderData.smsToDeptName,
            basicWorkOrderData.smsToUsername
          )
        }}</el-descriptions-item>
        <el-descriptions-item v-if="basicWorkOrderData.isSendSms == 1" label="发送内容">{{ basicWorkOrderData.sendContent }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <el-dialog width="500px" title="文件列表" :visible.sync="impactServiceVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" append-to-body>
      <file-download @cancel="impactServiceVisible = false" :attachmentArr="impactServiceArr"></file-download>
    </el-dialog>
    <el-dialog width="500px" title="文件列表" :visible.sync="attachmentVisible" :close-on-click-modal="false"
      :close-on-press-escape="false" append-to-body>
      <file-download @cancel="attachmentVisible = false" :attachmentArr="attachmentArr"></file-download>
    </el-dialog>

    <!-- 使用图片预览组件 -->
    <image-preview :visible.sync="imagePreviewVisible" :file-data="currentPreviewFile"
      preview-api="/commonDict/attach/download" :use-custom-download="true" @download="downloadCurrentFile"
      @close="imagePreviewVisible = false"></image-preview>
  </el-card>
</template>

<script>
import FileDownload from "../../workOrder/components/FileDownload.vue";
import TextCollapse from "@plugin/backbone/components/TextCollapse.vue";
import ImagePreview from "@plugin/backbone/components/ImagePreview.vue";
import {
  apiExportWorkOrder,
  apiDownloadManualFile,
} from "../../workOrder/workOrderWaitDetail/api/CommonApi";
import { apiDownloadAppendixFile } from "../../workOrder/workOrderWaitDetail/api/CommonApi";
export default {
  components: { FileDownload, TextCollapse, ImagePreview },
  props: {
    basicWorkOrderData: Object,
    woId: String,
    workItemId: [String, Number],
  },
  name: "BaseInfo",
  data() {
    return {
      builderZs: "",
      builderCs: "",
      notifier: "",
      recipient: "",
      exportLoading: false,
      impactServiceArr: [],
      impactServiceVisible: false,
      attachmentArr: [],
      attachmentVisible: false,
      manualFileName: "电路信息",
      manualFileFullscreenLoading: false,
      appendixFileLoading: false,
      // 图片预览相关
      imagePreviewVisible: false,
      currentPreviewFile: {},
    };
  },
  mounted() {
    if (
      this.basicWorkOrderData.appendixFileUrl &&
      this.basicWorkOrderData.appendixFileUrl != "无"
    ) {
      this.impactServiceArr = JSON.parse(
        this.basicWorkOrderData.appendixFileUrl
      );
    }
    if (
      this.basicWorkOrderData.appendix &&
      this.basicWorkOrderData.appendix != "无"
    ) {
      this.attachmentArr = JSON.parse(this.basicWorkOrderData.appendix);
    }
  },
  methods: {
    exportBasicWorkOrderInfo() {
      this.exportLoading = true;
      let param = {
        woId: this.woId,
        workItemId: this.workItemId,
      };
      apiExportWorkOrder(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("导出成功");
          } else {
            this.$message.error("导出失败");
          }
          this.exportLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("导出失败");
          this.exportLoading = false;
        });
    },
    queryImpactServiceList() {
      this.impactServiceVisible = true;
    },
    queryAttachmentList() {
      this.attachmentVisible = true;
    },
    // 预览附件文件
    previewAppendixFile(data) {
      // 首先检查文件名是否为图片类型
      const fileName = data.name.toLowerCase();
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'];

      // 检查文件扩展名是否为图片
      const isImage = imageExtensions.some(ext => fileName.endsWith(ext));

      if (!isImage) {
        // 如果不是图片，直接下载
        this.downloadAppendixFile(data);
        return;
      }

      // 如果是图片，保存当前文件信息并显示预览对话框
      this.currentPreviewFile = data;
      this.imagePreviewVisible = true;
    },

    // 下载当前预览的文件
    downloadCurrentFile() {
      this.downloadAppendixFile(this.currentPreviewFile);
    },

    // 下载附件文件
    downloadAppendixFile(data) {
      this.appendixFileLoading = true;
      let param = {
        attId: data.id,
      };
      apiDownloadAppendixFile(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("文件下载成功");
          } else {
            this.$message.error("文件下载失败");
          }
          this.appendixFileLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("文件下载失败");
          this.appendixFileLoading = false;
        });
    },
    downloadManualFile() {
      this.manualFileFullscreenLoading = true;
      let param = {
        param1: JSON.stringify({
          woId: this.woId,
        }),
      };
      apiDownloadManualFile(param)
        .then(res => {
          if (res.status == "0") {
            this.$message.success("文件下载成功");
          } else {
            this.$message.success("文件下载失败");
          }
          this.manualFileFullscreenLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error("文件下载失败");
          this.manualFileFullscreenLoading = false;
        });
    },
    stitchingAlgorithm(orgName, userName) {
      if (
        null != orgName &&
        orgName.length !== 0 &&
        orgName != "无" &&
        null != userName &&
        userName.length !== 0 &&
        userName != "无"
      ) {
        return orgName + "," + userName;
      } else {
        if (null != orgName && orgName.length !== 0 && orgName != "无") {
          return orgName;
        } else if (
          null != userName &&
          userName.length !== 0 &&
          userName != "无"
        ) {
          return userName;
        } else {
          return "无";
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "../../workOrder/workOrderWaitDetail/assets/common.scss";

::v-deep .el-descriptions-item__content {
  position: relative;
  word-break: break-all;
  padding-right: 18px;
}

::v-deep .el-descriptions-item__cell {
  vertical-align: top;
}
</style>
