<template>
  <div v-if="isShow" :style="vmStyle">
    <otdr-gis :otdrData="otdrData" :type="type"></otdr-gis>
  </div>
</template>
<script>
import { apiGetOtdrData } from "../workOrder/workOrderWaitDetail/api/CommonApi.js";
import { mapGetters } from "vuex";
import OtdrGis from "./components/OtdrGis.vue";

export default {
  components: {
    OtdrGis,
  },
  computed: {
    ...mapGetters(["frameStyle"]),
    vmStyle() {
      return {
        height: `calc(100vh - ${this.frameStyle.headerHeight - 16}px)`,
      };
    },
  },
  data() {
    return {
      otdrData: {},
      isShow: false,
      type: null,
    };
  },
  mounted() {
    console.log("--------alarmUniqueId", this.$route.query.alarmUniqueId);
    console.log("----------endTime", this.$route.query.endTime);
    this.alarmUniqueId = this.$route.query.alarmUniqueId;
    this.endTime = this.$route.query.endTime;
    this.type = this.$route.query.type;
    this.getOtdrData();
  },
  methods: {
    getOtdrData() {
      let formData = new FormData();

      formData.append("alarmUniqueId", this.alarmUniqueId);
      formData.append("endTime", this.endTime);
      apiGetOtdrData(formData)
        .then(res => {
          this.otdrData = res?.data ?? {};
          this.$nextTick(() => {
            this.isShow = true;
          });
        })
        .catch(error => {
          console.log(error);
        });
    },
  },
};
</script>
