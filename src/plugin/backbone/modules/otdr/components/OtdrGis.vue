<template>
  <div class="map-container">
    <div id="container"></div>
    <div
      style="
        width: 110px;
        height: 200px;

        z-index: 20;
        position: absolute;
        bottom: 5px;
        left: 20px;
      "
    >
      <div class="tuli_content">
        <img src="../assets/img/pole.png" width="18px" height="18px" alt="" />
        <span class="tuli_span">电杆</span>
      </div>
      <div class="tuli_content">
        <img
          src="../assets/img/manhole.png"
          width="18px"
          height="18px"
          alt=""
        />
        <span class="tuli_span">人井</span>
      </div>
      <div class="tuli_content">
        <img src="../assets/img/jifang.png" width="18px" height="18px" alt="" />
        <span class="tuli_span">机房</span>
      </div>
      <div class="tuli_content">
        <img src="../assets/img/odf.png" width="18px" height="18px" alt="" />
        <span class="tuli_span">机架ODF</span>
      </div>
      <div class="tuli_content">
        <img src="../assets/img/splice.png" width="18px" height="18px" alt="" />
        <span class="tuli_span">接头</span>
      </div>
      <div class="tuli_content">
        <img
          src="../assets/img/provincial.png"
          width="18px"
          height="18px"
          alt=""
        />
        <span class="tuli_span">省界</span>
      </div>
      <div class="tuli_content">
        <img
          src="../assets/img/terminal.png"
          width="18px"
          height="18px"
          alt=""
        />
        <span class="tuli_span">终端/分纤盒</span>
      </div>
      <div class="tuli_content">
        <img
          src="../assets/img/combinationBox.png"
          width="18px"
          height="18px"
          alt=""
        />
        <span class="tuli_span">综合箱</span>
      </div>
      <div class="tuli_content">
        <img
          src="../assets/img/opticalBox.png"
          width="18px"
          height="18px"
          alt=""
        />
        <span class="tuli_span">光交接箱</span>
      </div>
      <div class="tuli_content">
        <img
          src="../assets/img/outdoorDevice.png"
          width="18px"
          height="18px"
          alt=""
        />
        <span class="tuli_span">设备室外放置点</span>
      </div>
    </div>
  </div>
</template>
<script>
import manholePng from "../assets/img/manhole.png";
import jifangPng from "../assets/img/jifang.png";
import polePng from "../assets/img/pole.png";
import splicePng from "../assets/img/splice.png"; //接头
import terminalPng from "../assets/img/terminal.png"; //终端/分纤盒
import odfPng from "../assets/img/odf.png"; //机架(ODF)
import provincialPng from "../assets/img/provincial.png"; //省界
import opticalBoxPng from "../assets/img/opticalBox.png"; //光交接箱
import combinationBoxPng from "../assets/img/combinationBox.png"; //综合箱
import outdoorDevicePng from "../assets/img/outdoorDevice.png"; //设备室外放置点
export default {
  name: "MapContainer",
  props: {
    woId: String,
    otdrData: Object,
    type: String,
  },
  data() {
    return {
      faultInfo: null,
      defaultCenterPoint: [],
      map: null,

      zr: null,
      curPoint: null,
      canvasWidth: 0,
      canvasHeight: 0,
      pointArr: [],
      setViewport: null,

      faultInfoWgs84X: null,
      faultInfoWgs84Y: null,
      zoomLevel: 18,
      dialogFaultVisible: false,
      opts: {
        width: 320, // 信息窗口宽度
        height: 380, // 信息窗口高度
        title: "故障/预警信息", // 信息窗口标题
      },
      wellRodOpts: {
        width: 220, // 信息窗口宽度
        height: 170, // 信息窗口高度
        title: "资源信息", // 信息窗口标题
      },
      circle: null,
      radius: 15, // 圆的初始半径
      isDraw: true,
    };
  },
  mounted() {
    this.faultInfo = this.otdrData.faultInfo;
    this.initAMap();
  },
  beforeDestroy() {
    this.map.destroy();
  },
  destroyed() {
    this.map.destroy();
  },
  methods: {
    addMarker(point, markData, imgTypeName, size) {
      let cc = manholePng;
      switch (imgTypeName) {
        case "人井":
          cc = manholePng;
          break;
        case "机房":
          cc = jifangPng;
          break;
        case "电杆":
          cc = polePng;
          break;
        case "接头":
          cc = splicePng;
          break;
        case "终端/分纤盒":
          cc = terminalPng;
          break;
        case "机架(ODF)":
          cc = odfPng;
          break;
        case "省界":
          cc = provincialPng;
          break;
        case "光交接箱":
          cc = opticalBoxPng;
          break;
        case "综合箱":
          cc = combinationBoxPng;
          break;
        case "设备室外放置点":
          cc = outdoorDevicePng;
          break;
      }
      let width = size != null ? size : 25;
      let height = size != null ? size : 25;
      const markPoint = point;
      // 点标记
      let _marker = new window.BMapGL.Marker(markPoint, {
        icon: new window.BMapGL.Icon(cc, new window.BMapGL.Size(width, height)),
      });

      _marker.addEventListener("click", () => {
        this.markerEvent(markData);
      });
      this.map.addOverlay(_marker);
    },
    markerEvent(markData) {
      //坐标点
      let content = this.getWellRodWindowContent(markData);
      const infoWindow = new window.BMapGL.InfoWindow(
        content,
        this.wellRodOpts
      );
      this.map.openInfoWindow(
        infoWindow,
        new window.BMapGL.Point(markData.holeWgs84X, markData.holeWgs84Y)
      );
    },

    addPolygonNew(points, polygonData, isFault) {
      let polygonStyle = {};
      if (isFault) {
        polygonStyle = {
          strokeColor: "red",
          strokeWeight: 5,
        };
      } else {
        polygonStyle = {
          strokeColor: "#0081FF",
          strokeWeight: 4,
        };
      }
      let _polyline = new window.BMapGL.Polyline(
        [
          new window.BMapGL.Point(points[0].lng, points[0].lat),
          new window.BMapGL.Point(points[1].lng, points[1].lat),
        ],
        polygonStyle
      );

      this.map.addOverlay(_polyline);
      if (isFault) {
        let content = this.getFaultWindowContent();
        // 创建标注
        let infoWindow = new window.BMapGL.InfoWindow(content, this.opts);
        // 线添加点击事件
        _polyline.addEventListener("click", () => {
          this.map.openInfoWindow(
            infoWindow,
            new window.BMapGL.Point(
              parseFloat(this.faultInfoWgs84X),
              parseFloat(this.faultInfoWgs84Y)
            )
          ); // 开启信息窗口
        });
      }
    },

    wgs84XToBd(x) {
      return (parseFloat(x) + 0.01255).toFixed(6);
    },
    wgs84YToBd(y) {
      return (parseFloat(y) + 0.00328).toFixed(6);
    },

    initAMap() {
      //创建地图实例;
      this.map = new window.BMapGL.Map("container", {
        resizeEnable: true,
      });
      // 创建点坐标
      this.faultInfoWgs84X = parseFloat(this.faultInfo.wgs84X);
      this.faultInfoWgs84Y = parseFloat(this.faultInfo.wgs84Y);
      const point = new window.BMapGL.Point(
        parseFloat(this.faultInfo.wgs84X),
        parseFloat(this.faultInfo.wgs84Y)
      );
      //初始化地图，设置中心点坐标和地图级别
      this.map.centerAndZoom(point, 17);
      // 开启鼠标滚轮缩放
      this.map.enableScrollWheelZoom(true);
      //隐藏3D建筑物
      this.map.setDisplayOptions({
        building: false,
      });
      this.drawMarker();
      //画故障点
      this.addAnimatePoint(
        parseFloat(this.faultInfoWgs84X),
        parseFloat(this.faultInfoWgs84Y)
      );
      var zoomCtrl = new window.BMapGL.ZoomControl({
        anchor: BMAP_ANCHOR_TOP_LEFT,
      }); // 添加缩放控件
      this.map.addControl(zoomCtrl);
      this.map.addEventListener("zoomend", () => {
        if (this.map.getZoom() <= 12) {
          if (this.isDraw) {
            let all = this.map.getOverlays();
            all.map(item => {
              if (item.toString() === "Marker") {
                this.map.removeOverlay(item);
              }
            });
            this.drawAZ();
            this.isDraw = false;
          }
        } else if (this.map.getZoom() >= 15) {
          //重新绘制
          if (!this.isDraw) {
            this.map.clearOverlays();
            this.$nextTick(() => {
              this.drawMarker();
            });

            this.isDraw = true;
          }
        }
      });
    },

    drawMarker() {
      const lineList = this.otdrData.lineList;
      const pointList = [];

      lineList.forEach(item => {
        const points = [];
        points.push({
          lng: item.beginPointInfo.holeWgs84X,
          lat: item.beginPointInfo.holeWgs84Y,
        });
        points.push({
          lng: item.endPointInfo.holeWgs84X,
          lat: item.endPointInfo.holeWgs84Y,
        });

        //画点
        if (item.beginPointInfo.typeName) {
          let point = new window.BMapGL.Point(
            parseFloat(item.beginPointInfo.holeWgs84X),
            parseFloat(item.beginPointInfo.holeWgs84Y)
          );
          pointList.push(point);
          this.addMarker(
            point,
            item.beginPointInfo,
            item.beginPointInfo.typeName
          );
        }
        if (item.endPointInfo.typeName) {
          let point = new window.BMapGL.Point(
            parseFloat(item.endPointInfo.holeWgs84X),
            parseFloat(item.endPointInfo.holeWgs84Y)
          );
          pointList.push(point);
          this.addMarker(point, item.endPointInfo, item.endPointInfo.typeName);
        }

        //画线
        if (this.faultInfo.supportSectName != item.supportSectName) {
          this.addPolygonNew(points);
        } else {
          //画故障线
          this.addPolygonNew(points, null, true);
        }
      });

      // 注意这里的代码需要控制触发一次，因为会触发canvas的update函数
      if (!this.setViewport) {
        // 根据点的位置自动设置地图的视口
        this.map.setViewport(pointList);
        this.setViewport = true;
        const point = new window.BMapGL.Point(
          this.faultInfo.wgs84X,
          this.faultInfo.wgs84Y
        );
        //初始化地图，设置中心点坐标和地图级别
        this.map.centerAndZoom(point, 18);
      }
    },
    drawAZ() {
      const lineList = this.otdrData.lineList;
      lineList.forEach(item => {
        if (item?.beginPointInfo?.azType == "A") {
          this.addMarker(
            new window.BMapGL.Point(
              parseFloat(item.beginPointInfo.holeWgs84X),
              parseFloat(item.beginPointInfo.holeWgs84Y)
            ),
            item.beginPointInfo,
            item.beginPointInfo.typeName,
            35
          );
        }
        if (item?.endPointInfo?.azType == "A") {
          this.addMarker(
            new window.BMapGL.Point(
              parseFloat(item.endPointInfo.holeWgs84X),
              parseFloat(item.endPointInfo.holeWgs84Y)
            ),
            item.endPointInfo,
            item.endPointInfo.typeName
          );
        }
        if (item?.beginPointInfo?.azType == "Z") {
          this.addMarker(
            new window.BMapGL.Point(
              parseFloat(item.beginPointInfo.holeWgs84X),
              parseFloat(item.beginPointInfo.holeWgs84Y)
            ),
            item.beginPointInfo,
            item.beginPointInfo.typeName,
            35
          );
        }
        if (item?.endPointInfo?.azType == "Z") {
          this.addMarker(
            new window.BMapGL.Point(
              parseFloat(item.endPointInfo.holeWgs84X),
              parseFloat(item.endPointInfo.holeWgs84Y)
            ),
            item.endPointInfo,
            item.endPointInfo.typeName
          );
        }
      });
    },

    createDom() {
      var div = document.createElement("div");
      div.style.cursor = "pointer";
      div.innerHTML =
        '<svg id="red-circle" width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">' +
        '<circle cx="18" cy="18" r="18" fill="red"/>' +
        '<circle cx="18" cy="18" r="5" fill="#fff"/></svg>';

      return div;
    },
    addAnimatePoint(wgs84X, wgs84Y) {
      //自定义dom 覆盖物
      this._animateMarker = new window.BMapGL.CustomOverlay(this.createDom, {
        point: new window.BMapGL.Point(wgs84X, wgs84Y),
        properties: {},
      });

      let content = this.getFaultWindowContent();
      // 创建标注
      var infoWindow = new window.BMapGL.InfoWindow(content, this.opts);

      this.map.addOverlay(this._animateMarker);
      this._animateMarker.addEventListener("click", e => {
        this.map.openInfoWindow(
          infoWindow,
          new window.BMapGL.Point(wgs84X, wgs84Y)
        ); // 开启信息窗口
      });

      this.redCircle = document.getElementById("red-circle");
      this.startAnimateMarker();
    },
    // 停止动画设置标记即可
    stopAnimateFlag() {
      if (this._animateMarker) {
        this._animateMarker.hide();
      }
      if (this._requestAnimationId) {
        cancelAnimationFrame(this._requestAnimationId);
      }
    },
    // 开启动画
    startAnimateMarker() {
      // 先停止动画再开启
      this.stopAnimateFlag();
      this.handleAnimateMarker();
      if (this._animateMarker) {
        this._animateMarker.show();
      }
    },
    handleAnimateMarker() {
      let { _animate_scale = 1, _scale_interval = 0.02 } = this;
      _animate_scale += _scale_interval;
      if (_animate_scale >= 1) {
        _animate_scale = 1;
        _scale_interval = -Math.abs(_scale_interval);
        this._scale_interval = _scale_interval;
      }
      if (_animate_scale <= 0) {
        _animate_scale = 0;
        _scale_interval = Math.abs(_scale_interval);
        this._scale_interval = _scale_interval;
      }
      this._animate_scale = _animate_scale;

      if (this._animateMarker) {
        this.redCircle.style.transform = "scale(" + _animate_scale + ")";
        this.redCircle.style.opacity = _animate_scale;
      }

      this._requestAnimationId = requestAnimationFrame(
        this.handleAnimateMarker
      );
    },
    dialogFaultClose() {
      this.dialogFaultVisible = false;
    },
    getFaultWindowContent() {
      return (
        '<div  style="width: 95%;margin-top:10px;margin-left:3px;font-size:12px">' +
        '<div><div style=" width: 10px;height: 10px; background-color: #b50b14;display:inline-block;"></div><div style="display:inline-block;margin-left:5px;font-weight:bold;font-size:14px">资源关联</div></div> ' +
        '<div class="contentL" style = "margin-top:10px;display: flex;justify-content: space-between;flex-direction: row;flex-wrap: wrap;width: 94%;height: 52px;">' +
        '<div class="content-item item1" style = "width: 32%;height: 48px;color: #fff;background-color: #a9a9a9;border-radius:3px">' +
        '<div style = "text-align: center;height:24px;" > 设备端口' +
        "</div>" +
        '<div style="text-align: center;height:24px;font-weight:bold">' +
        this.faultInfo.relSource.portCount +
        "</div>" +
        "</div>" +
        '<div class="content-item item2" style="width: 32%;height: 48px;color: #fff;background-color: #a9a9a9;border-radius:3px">' +
        '<div style = "text-align: center;height:24px;" > 光缆' +
        "</div >" +
        '<div style="text-align: center;font-weight:bold;height:24px;">' +
        this.faultInfo.relSource.optCount +
        "</div>" +
        "</div>" +
        '<div class="content-item item3" style="width: 32%;height: 48px;color: #fff;background-color: #a9a9a9;border-radius:3px">' +
        '<div style = "text-align: center;height:24px;" > 系统' +
        "</div >" +
        '<div style="text-align: center;font-weight:bold;height:24px;">' +
        this.faultInfo.relSource.sysCount +
        "</div></div></div>" +
        '<div style="margin-top:5px"><div style=" width: 10px;height: 10px; background-color: #b50b14;display:inline-block;"></div><div style="display:inline-block;margin-left:5px;font-weight:bold;margin-top:5px;font-size:14px">资源属性</div></div>' +
        '<div style="display: table;width: 94%;margin-top:10px;margin-bottom:10px">' +
        '<div style="display: table-row;">' +
        '<div style="display: table-cell;border: 1px solid #f4f6f7; background-color: #f9fafb;width: 34%;"><span style="margin-left:12px">故障段落</span>' +
        "</div>" +
        '<div style="display: table-cell;border: 1px solid #f4f6f7; "><div style="margin-left:18px">' +
        this.faultInfo.optSectName +
        "</div></div>" +
        "</div>" +
        '<div style="display: table-row;">' +
        '<div style="display: table-cell;border: 1px solid #f4f6f7; background-color: #f9fafb;width: 34%;"><span style="margin-left:12px">故障点定位</span>' +
        "</div>" +
        '<div style="display: table-cell;border: 1px solid #f4f6f7; "><div style="margin-left:18px">' +
        this.faultInfo.supportSectName +
        "</div></div>" +
        "</div>" +
        '<div style="display: table-row;">' +
        '<div style="display: table-cell;border: 1px solid #f4f6f7; background-color: #f9fafb;width: 34%;"><span style="margin-left:12px">经度</span>' +
        "</div>" +
        '<div style="display: table-cell;border: 1px solid #f4f6f7; "><div style="margin-left:18px">' +
        this.faultInfo.wgs84X +
        "</div></div>" +
        "</div>" +
        '<div style="display: table-row;">' +
        '<div style="display: table-cell;border: 1px solid #f4f6f7; background-color: #f9fafb;width: 34%;"><span style="margin-left:12px">纬度</span>' +
        "</div>" +
        '<div style="display: table-cell;border: 1px solid #f4f6f7; "><div style="margin-left:18px">' +
        this.faultInfo.wgs84Y +
        "</div></div>" +
        "</div>" +
        '<div style="display: table-row;">' +
        '<div style="display: table-cell;border: 1px solid #f4f6f7; background-color: #f9fafb;width: 34%;"><span style="margin-left:12px">故障原因</span>' +
        "</div>" +
        '<div style="display: table-cell;border: 1px solid #f4f6f7; "><div style="margin-left:18px">' +
        this.faultInfo.faultReason +
        "</div></div>" +
        "</div>" +
        '<div style="display: table-row;">' +
        '<div style="display: table-cell;border: 1px solid #f4f6f7; background-color: #f9fafb;width: 34%;"><span style="margin-left:12px">告警发生时间</span>' +
        "</div>" +
        '<div style="display: table-cell;border: 1px solid #f4f6f7; "><div style="margin-left:18px">' +
        this.faultInfo.faultTime +
        "</div></div>" +
        "</div>" +
        " </div>"
      );
    },
    getWellRodWindowContent(data) {
      return (
        '<div  style="width: 94%;margin-top:10px;font-size:12px">' +
        '<div><div style=" width: 10px;height: 10px; background-color: #b50b14;display:inline-block;"></div><div style="display:inline-block;margin-left:5px;font-weight:bold;font-size:14px">资源属性</div></div> ' +
        '<div style="display: table; width: 100%; margin-top: 10px;margin-bottom:10px">' +
        ' <div style="display: table-row">' +
        ' <div style="display: table-cell; border: 1px solid #f4f6f7;background-color: #f9fafb; width: 34%;text-align: left;vertical-align: middle; padding-left: 12px;">' +
        "名称" +
        "</div>" +
        '<div style=" display: table-cell; border: 1px solid #f4f6f7; text-align: left; vertical-align: middle; padding-left: 12px;"> ' +
        data.holeName +
        "</div>" +
        "</div>" +
        ' <div style="display: table-row">' +
        ' <div style="display: table-cell; border: 1px solid #f4f6f7;background-color: #f9fafb; width: 34%;text-align: left;vertical-align: middle; padding-left: 12px;">' +
        "承载类型" +
        "</div>" +
        '<div style=" display: table-cell; border: 1px solid #f4f6f7; text-align: left; vertical-align: middle; padding-left: 12px;"> ' +
        data.typeName +
        "</div>" +
        "</div>" +
        ' <div style="display: table-row">' +
        ' <div style="display: table-cell; border: 1px solid #f4f6f7;background-color: #f9fafb; width: 34%;text-align: left;vertical-align: middle; padding-left: 12px;">' +
        "经度" +
        "</div>" +
        '<div style=" display: table-cell; border: 1px solid #f4f6f7; text-align: left; vertical-align: middle; padding-left: 12px;"> ' +
        data.holeWgs84X +
        "</div>" +
        "</div>" +
        ' <div style="display: table-row">' +
        ' <div style="display: table-cell; border: 1px solid #f4f6f7;background-color: #f9fafb; width: 34%;text-align: left;vertical-align: middle; padding-left: 12px;">' +
        "纬度" +
        "</div>" +
        '<div style=" display: table-cell; border: 1px solid #f4f6f7; text-align: left; vertical-align: middle; padding-left: 12px;"> ' +
        data.holeWgs84Y +
        "</div>" +
        "</div>" +
        "</div>" +
        "</div>"
      );
    },

    roundToDecimalPlace(num, decimalPlaces) {
      const factor = Math.pow(10, decimalPlaces);
      return Math.round(num * factor) / factor;
    },
  },
};
</script>
<style lang="less" scoped>
.map-container {
  width: 100%;
  height: 100%;
  position: relative;
}
#container {
  padding: 0px;
  margin: 0px;
  width: 100%;
  height: 100%;
  min-height: 500px;
  overflow: hidden;
}
::v-deep .BMap_bubble_title {
  font-size: 15px !important;
  font-weight: bold !important;
}

::v-deep .BMap_bubble_pop {
  border-radius: unset !important;
}

::v-deep .BMap_bubble_content {
  top: unset !important;
}

.tuli_content {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 20px;
}
.tuli_span {
  margin-left: 5px;
  font-size: 12px;
  font-weight: 400;
}
</style>
