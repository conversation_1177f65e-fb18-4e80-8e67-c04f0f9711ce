import { postJson, getJson } from "@/utils/axios";

const dictUrl = "commonDict/enum/list"; //公共下拉接口地址
const queryOperationManageUrl = "backbone/operation/queryOperationManage"; //运营群管理列表查询
const queryOperatioSupportnUrl = "backbone/operation/queryOperatioSupportn" //运营支撑列表查询
const addRuleUrl = "/backbone/operation/insert"; //添加
const updateUrl = "/backbone/operation/update"; //编辑
const deleteUle = "/backbone/operation/delete"; //删除
const getOperationManageByIdUrl =
  "/backbone/operation/getOperationManageById"; //根据id查询运营管理

const apiDict = params => getJson(dictUrl, params);
const apiQueryOperationManage = params => postJson(queryOperationManageUrl, params);
const apiInsert = params => postJson(addRuleUrl, params);
const apiOperationManageById = params => getJson(getOperationManageByIdUrl, params);
const apiUpdate = params => postJson(updateUrl, params);
const apiDelete = params => getJson(deleteUle, params);
const apiQueryOperatioSupportn = params => postJson(queryOperatioSupportnUrl, params);
export {
  apiDict,
  apiQueryOperationManage,
  apiQueryOperatioSupportn,
  apiInsert,
  apiDelete,
  apiOperationManageById,
  apiUpdate,
};
