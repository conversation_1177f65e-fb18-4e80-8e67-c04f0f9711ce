<template>
  <div style="padding: 0">
    <head-content-layout class="page-wrap">
      <template #header>
        <el-row style="display: flex">
          <el-col :xs="24" :sm="12" :md="14" :lg="18" style="display: flex">
            <div>
              <div style="display: inline-block; margin-left: 5px">群名：</div>
              <el-input
                v-model="seachData.groupName"
                placeholder="请输入群名"
                clearable
                style="width: 270px"
              ></el-input>
              <!-- <dict-select
                :value.sync="seachData.flowType"
                :dictId="10202"
                placeholder="请选择流程类型"
                style="width: 240px"
              /> -->
            </div>
            <div>
              <div style="display: inline-block; margin-left: 20px">
                流程类型：
              </div>
              <dict-select
                :value.sync="seachData.processType"
                :dictId="processTypeDictId"
                placeholder="请选择专业"
                style="width: 270px"
              />
            </div>
            <div>
              <div style="display: inline-block; margin-left: 20px">省份：</div>

              <dict-select
                :value.sync="seachData.province"
                :dictId="provinceId"
                placeholder="请选择省份"
                style="width: 270px"
              />
            </div>
          </el-col>
          <el-col :xs="24" :sm="24" :md="10" :lg="6" style="text-align: right">
            <div>
              <el-button type="primary" @click="seniorQuery">筛选</el-button>
              <el-button type="primary" @click="createSeup">新增</el-button>
            </div>
          </el-col>
        </el-row>
        <!-- <div class="order__menuList">
          <el-button type="primary" @click="createSeup">+ 新建</el-button>
        </div> -->
      </template>

      <template #table>
        <el-table
          ref="table"
          :data="tableData"
          :header-cell-style="{ textAlign: 'left' }"
          :cell-style="{ textAlign: 'left' }"
          :border="false"
          stripe
          @selection-change="onSelectionChange"
          height="100%"
          v-loading="tableLoading"
        >
          <el-table-column type="selection" width="50" :index="indexMethod" />
          <el-table-column prop="groupName" label="群名" width="320" />
          <el-table-column prop="processType" label="流程类型" width="200" />
          <el-table-column prop="province" label="省份" width="180" />
          <el-table-column prop="loadTime" label="添加时间" width="230" />
          <el-table-column prop="type" label="类型" width="230" />
          <el-table-column
            prop="joinOrNot"
            label="是否可加入"
            width="180"
            show-overflow-tooltip
          />
          <el-table-column fixed="right" label="操作" width="150">
            <template slot-scope="scope">
              <el-button
                @click="clickEditor(scope.row.id)"
                type="text"
                size="small"
                >编辑</el-button
              >
              <!-- <el-popconfirm title="确定删除这条规则么?" @confirm="clickDelete(scope.row)"> -->
              <el-button
                @click="clickDelete(scope.row)"
                type="text"
                size="small"
                slot="reference"
                style="margin-left: 10px"
                >删除</el-button
              >
              <!-- </el-popconfirm> -->
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pagination>
        <pagination
          ref="pagination"
          :total="form.total"
          :page.sync="form.pageNum"
          :limit.sync="form.pageSize"
          @change="seniorQuery"
        />
      </template>
      <template #dialog>
        <operation-manage-add :title.sync="title" :visible.sync="visible" />
      </template>
    </head-content-layout>
  </div>
</template>

<script>
import { apiQueryOperationManage, apiDelete } from "./api/Operation.js";
import HeadContentLayout from "./components/HeadContentLayout.vue";
import DictSelect from "./components/DictSelect.vue";
import OperationManageAdd from "./components/OperationManageAdd.vue";
import Pagination from "./components/Pagination.vue";
import Bus from "../workOrder/bus";
export default {
  name: "WorkOrderVoiceremind",
  components: {
    HeadContentLayout,
    DictSelect,
    OperationManageAdd,
    Pagination,
  },
  data() {
    return {
      tableLoading: false,
      tableData: [],

      //查询条件
      seachData: {
        groupName: "", //群名
        province: "", //省份
        processType: "", //流程类型
      },
      form: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      processTypeDictId: 10202,
      provinceId: 10064,
      dictData: [],
      //新增,编辑
      visible: false,
      title: "新增",
    };
  },
  mounted() {
    this.getTablelListData();
    Bus.$on("getList", () => {
      this.getTablelListData();
    });
  },
  methods: {
    //请求数据
    getTablelListData() {
      this.tableLoading = true;
      let seniorParam = {
        groupName: this.seachData.groupName,
        province: this.seachData.province, //省份
        processType: this.seachData.processType,
      };
      let param = {
        pageIndex: this.form.pageNum,
        pageSize: this.form.pageSize,
        param1: JSON.stringify(seniorParam),
      };
      apiQueryOperationManage(param)
        .then(res => {
          if (res.status == "0") {
            this.tableLoading = false;
            let r = res?.data?.rows ?? [];
            this.form.total = res.data.totalElements || 0;
            this.tableData = r;
          } else {
            this.tableLoading = false;
          }
        })
        .catch(err => {
          this.tableLoading = false;
          console.log(err);
        });
    },

    //编辑弹出diglog
    clickEditor(scope) {
      this.visible = true;
      this.title = "编辑";
      Bus.$emit("editor", scope);
    },

    //序号选择处理
    onSelectionChange(row) {
      if (row.length == 0) {
        this.sclectList = [];
      } else {
        let s = [];
        row.forEach(el => {
          s.push(el.ruleId);
        });
        this.sclectList = s;
      }
    },
    //选择框
    indexMethod(index) {
      const currentPage = this.form.pageNum;
      const pageSize = this.form.pageSize;
      return (currentPage - 1) * pageSize + (index + 1);
    },

    //查询
    seniorQuery() {
      this.getTablelListData();
    },
    //新增
    createSeup() {
      this.visible = true;
      this.title = "新增";
    },
    //删除
    clickDelete(row) {
      this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let param = {
            id: row.id,
          };
          apiDelete(param).then(res => {
            if (res.status == "0") {
              this.$message({
                message: "删除成功",
                type: "success",
              });
              this.getTablelListData();
            } else {
              this.$message.error("删除失败");
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "./assets/ivrCommon.scss";
.order__menuList {
  margin-top: 10px;
  padding-bottom: 10px;
}
::v-deep .el-dialog__body {
  padding: 0;
  padding-right: 50px;
  padding-left: 30px;
}
::v-deep .el-form-item__content {
  position: relative;
  .item__unit {
    position: absolute;
    display: block;
    top: 0;
    left: 106px;
    color: #dcdfe6;
  }
}
::v-deep .el-dialog__wrapper {
  .el-dialog {
    margin-top: 6vh !important;
    .el-dialog__body {
      .el-form {
        .el-form-item,
        .el-form-item--mini {
          margin: 0;
          margin-bottom: 14px;
        }
      }
    }
  }
}
</style>
