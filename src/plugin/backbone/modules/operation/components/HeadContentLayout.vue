<template>
  <div class="page-wrap full-main" :style="vmStyle">
    <div class="header-wrap" ref="headerWrap" v-if="$slots.header">
      <slot name="header"></slot>
    </div>

    <div class="content-wrap" :style="conentStyle">
      <el-card
        shadow="always"
        class="content-card"
        :body-style="{ padding: '20px', height: '100%' }"
      >
        <div ref="contentHeader" class="content-header">
          <slot name="contentHeader"></slot>
        </div>
        <div :style="tableWrap">
          <slot name="table"></slot>
        </div>
        <div ref="pagination">
          <slot name="pagination"></slot>
        </div>
        <div ref="dialog">
          <slot name="dialog"></slot>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import {
  addResizeListener,
  removeResizeListener,
} from "element-ui/src/utils/resize-event";
import { mapGetters } from "vuex";

export default {
  name: "HeadContentLayout",
  data() {
    return {
      domAttrs: {
        headerHeight: 58,
        contentHeadHeight: 30,
        contentPagerHeight: 40,
      },
    };
  },
  computed: {
    ...mapGetters(["frameStyle"]),
    vmStyle() {
      return {
        height: `calc(100vh - ${this.frameStyle.headerHeight - 16}px)`,
      };
    },
    conentStyle() {
      return {
        height: `calc(100% - ${this.domAttrs.headerHeight}px)`,
      };
    },
    tableWrap() {
      return {
        height: `calc(100% - ${
          this.domAttrs.contentHeadHeight +
          this.domAttrs.contentPagerHeight +
          10
        }px)`,
      };
    },
  },
  mounted() {
    addResizeListener(this.$refs.headerWrap, this.getHeaderHeight);
    addResizeListener(this.$refs.contentHeader, this.getContentHeadHeight);
  },
  beforeDestroy() {
    removeResizeListener(this.$refs.headerWrap, this.getHeaderHeight);
    removeResizeListener(this.$refs.contentHeader, this.getContentHeadHeight);
  },
  methods: {
    getHeaderHeight() {
      if (this.$refs.headerWrap) {
        this.domAttrs.headerHeight = Math.ceil(
          window
            .getComputedStyle(this.$refs.headerWrap)
            ?.height?.replace?.(/auto|px/, "") ?? 58
        );
      } else {
        this.domAttrs.headerHeight = 58;
      }
    },
    getContentHeadHeight() {
      if (this.$refs.contentHeader) {
        this.domAttrs.contentHeadHeight = Math.ceil(
          window
            .getComputedStyle(this.$refs.contentHeader)
            ?.height?.replace?.(/auto|px/, "") ?? 30
        );
      } else {
        this.domAttrs.contentHeadHeight = 30;
      }
      if (this.$refs.pagination) {
        this.domAttrs.contentPagerHeight = Math.ceil(
          window
            .getComputedStyle(this.$refs.pagination)
            ?.height?.replace?.(/auto|px/, "") ?? 40
        );
      } else {
        this.domAttrs.contentPagerHeight = 40;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.page-wrap {
  .header-wrap {
    padding: 12px 12px 12px;
    @include themify() {
      background-color: themed("$--color-white");
    }
    ::v-deep .search-input-button {
      color: #fff;
      @include themify() {
        background-color: themed("$--color-primary");
      }
    }
  }
  .content-wrap {
    padding: 10px;
  }
  .content-card {
    height: 100%;
    overflow: auto;
    min-height: 300px;
  }
  .content-header {
    margin-bottom: 10px;
  }
  ::v-deep .el-tree {
    padding-top: 15px;
    padding-left: 10px;
    // 不可全选样式
    .el-tree-node {
      .is-leaf + .el-checkbox .el-checkbox__inner {
        display: inline-block;
      }
      .el-checkbox .el-checkbox__inner {
        display: none;
      }
    }
  }
}
</style>
