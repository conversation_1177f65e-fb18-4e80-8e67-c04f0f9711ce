<template>
  <el-dialog
    :title="title"
    :visible="visible"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="600px"
  >
    <el-form ref="form" :model="form" :rules="formRules" label-width="110px">
      <el-form-item label="群名：" prop="groupName" required>
        <el-input v-model="form.groupName"></el-input>
      </el-form-item>
      <el-form-item label="流程类型：" prop="processType" required>
        <dict-select
          :value.sync="form.processType"
          :dictId="10202"
          placeholder="请选择流程类型"
          @change="changeProcessType"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="省份：" prop="province" required>
        <el-select
          v-model="form.province"
          placeholder="请选择省份"
          multiple
          collapse-tags
          style="width: 100%"
        >
          <el-option
            v-for="(item, i) in dictData"
            :key="i"
            :label="item.dictName"
            :value="item.dictCode"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="类型：" prop="type" required>
        <dict-select
          :value.sync="form.type"
          :dictId="71000"
          placeholder="请选择类型"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="是否可加入：" prop="joinOrNot">
        <dict-select
          :value.sync="form.joinOrNot"
          :dictId="71001"
          placeholder="请选择是否可加入"
          style="width: 100%"
        />
      </el-form-item>
      <!-- v-for="(item, index) in filesList" item  :title="item.name" -->
      <el-form-item label="附件：" prop="fileName">
        <el-tag
          :key="tag"
          class="fileName_style"
          v-for="tag in form.fileName"
          closable
          @close="_close(tag)"
        >
          <div class="text-truncate">{{ tag }}</div>
        </el-tag>
        <el-button type="primary" @click="attachmentBrowse"
          >+上传附件</el-button
        >
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="_save()"> 确 定 </el-button>
      <el-button @click="handleClose()">取 消</el-button>
    </span>
    <el-dialog
      title="附件选择"
      :visible.sync="fileForm.attachmentDialogVisible"
      width="25%"
      :close-on-click-modal="false"
      append-to-body
    >
      <el-upload
        class="upload-bus-trans-sys"
        action=""
        :drag="true"
        accept=".jpg,.jpeg,.png"
        :file-list="fileForm.fileList"
        :auto-upload="false"
        :on-change="handleUploadChange"
        :on-remove="upFileRemoveHandler"
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      </el-upload>
      <span slot="footer">
        <el-button @click="fileForm.attachmentDialogVisible = false"
          >取消</el-button
        >
        <el-button type="primary" @click="onFileSelected">确定</el-button>
      </span>
    </el-dialog>
  </el-dialog>
</template>

<script>
import DictSelect from "../../workOrder/components/DictSelect.vue";
import {
  apiInsert,
  apiUpdate,
  apiDict,
  apiOperationManageById,
} from "../api/Operation.js";
import Bus from "../../workOrder/bus";
export default {
  name: "RemindAdd",
  components: {
    DictSelect,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: "新增",
    },
  },

  data() {
    return {
      form: {
        id: "",
        groupName: "",
        processType: "",
        province: [],
        type: "",
        joinOrNot: "",
        fileName: [],
        fileList: [],
        loadTime: "",
      },
      fileForm: {
        attachmentDialogVisible: false,
        fileName: null,
        fileList: [],
      },
      dictData: [],
      professionalDictId: "10002",
      provinceDictId: "10064",
      createUserId: JSON.parse(sessionStorage.userInfo).userName,
      formRules: {
        groupName: [{ required: true, message: "请输入群名" }],
        processType: [{ required: true, message: "请选择流程类型" }],
        province: [{ required: true, message: "请选择省份" }],
        type: [{ required: true, message: "请选择类型" }],
        joinOrNot: [{ required: true, message: "请选择是否可加入" }],
        fileName: [{ required: true, message: "请上传附件" }],
      },
    };
  },
  watch: {},
  mounted() {
    Bus.$on("editor", row => {
      this.setEditor(row);
    });
    this.getDictData(this.provinceDictId);
  },
  methods: {
    onFileSelected() {
      if (this.fileForm.fileList.length == 1) {
        if (this.form.fileList.length > 0) {
          this.$message({
            message: "请将已存在的图片清除后再上传!",
            type: "warning",
          });
          return;
        } else {
          this.form.fileList = this.fileForm.fileList;
          this.form.fileName.push(this.fileForm.fileName);
          this.fileForm.fileList = [];
          this.fileForm.fileName = null;
        }
      } else {
        this.$message({
          message: "只允许上传一张图片!",
          type: "warning",
        });
        return;
      }
      this.fileForm.attachmentDialogVisible = false;
    },
    // 附件清除
    _close(tag) {
      this.form.fileName.splice(this.form.fileName.indexOf(tag), 1);
      this.form.fileList = [];
    },

    //附件选择
    attachmentBrowse() {
      this.fileForm.attachmentDialogVisible = true;
    },

    //上传附件列表改变
    handleUploadChange(file, fileList) {
      let name = file.name;
      let arr = name.split(".");
      let filex = arr[arr.length - 1];
      if (!".jpg.jpeg.png".includes(filex)) {
        this.$alert("文件类型不符合!", "", {
          confirmButtonText: "确定",
        });
        this.fileForm.fileList = fileList.filter(item => item.name != name);
        return;
      }
      this.fileForm.fileName = name;
      this.fileForm.fileList = fileList;
    },

    //文件附件列表移除
    upFileRemoveHandler(file, fileList) {
      this.fileForm.fileList = fileList;
    },

    getDictData(value) {
      let param = {
        dictTypeCode: value,
      };
      apiDict(param)
        .then(res => {
          if (res.code == 200) {
            this.dictData = res?.data ?? [];
          }
        })
        .catch(error => {
          console.log(error);
        });
    },

    changeProcessType() {
      if (this.form.processType == "4") {
        this.form.province = [];
      } else {
        this.form.province = ["全国"];
      }
    },
    //编辑打开弹窗
    setEditor(row) {
      let param = {
        id: row,
      };
      apiOperationManageById(param)
        .then(res => {
          if (res.status == "0") {
            let r = res.data;
            r.province = this.setProvince(r.province);
            // r.fileName = r.appendix.name;
            // this.form = { ...r };
            if (r.appendix) {
              let a = JSON.parse(r.appendix).name;
              this.form.fileName.push(a);
              this.form.fileList = [];
            }
            this.form.id = r.id;
            this.form.groupName = r.groupName;
            this.form.processType = r.processType;
            this.form.province = r.province;
            this.form.type = r.type;
            this.form.joinOrNot = r.joinOrNot;
          }
        })
        .catch(error => {
          if (error.status == "500") {
            this.$message.error(error.msg);
          } else {
            this.$message.error("查询失败");
          }
        });
    },
    //x号关闭
    handleClose() {
      this.$refs.form.resetFields();
      this.form.fileList = [];
      this.$emit("update:visible", false);
    },
    //表单提交
    _save() {
      let formData = new FormData();
      let jsonParam = {
        groupName: this.form.groupName,
        processType: this.form.processType,
        province: this.form.province.join(","),
        type: this.form.type,
        joinOrNot: this.form.joinOrNot,
      };
      if (this.title == "新增") {
        if (this.form.fileList.length > 0) {
          for (let item of this.form.fileList) {
            formData.append("file", item.raw);
          }
        } else {
          this.$message({
            message: "请上传图片",
            type: "warning",
          });
          return;
        }
        formData.append("jsonParam", JSON.stringify(jsonParam));
        apiInsert(formData)
          .then(res => {
            if (res.status == "0") {
              this.$refs.form.resetFields();
              this.form.fileList = [];
              this.$emit("update:visible", false);
              this.$message({
                message: "添加成功",
                type: "success",
              });
              Bus.$emit("getList");
            } else {
              this.$message.error("添加失败");
            }
          })
          .catch(error => {
            if (error.status == "500") {
              this.$message.error(error.msg);
            } else {
              this.$message.error("添加失败");
            }
          });
      } else {
        if (this.form.fileList.length > 0) {
          for (let item of this.form.fileList) {
            formData.append("file", item.raw);
          }
        }
        jsonParam.id = this.form.id;
        formData.append("jsonParam", JSON.stringify(jsonParam));
        apiUpdate(formData).then(res => {
          if (res.status == "0") {
            this.$emit("update:visible", false);
            this.$refs.form.resetFields();
            Bus.$emit("getList");
            this.$message({
              message: "修改成功",
              type: "success",
            });
          } else {
            this.$message.error("修改失败");
          }
        });
      }
    },
    setProvince(arr) {
      let l = [];
      l = arr.split(",");
      return l;
    },
    setFi(arr) {
      let l = [];
      l = arr.split(",");
      return l;
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-dialog {
  .elTagBox {
    display: flex;
    flex-wrap: wrap;
    .el-tag {
      margin-bottom: 10px;
    }
  }
  .alertContent {
    width: 340px;
    border: 1px solid #ebeef5;
    cursor: not-allowed;
    padding: 10px;
    span {
      font-size: 12px;
      color: #909399;
      margin-bottom: 10px;
    }
  }
}
.upload-bus-trans-sys .el-upload {
  width: 100%;
}
.upload-bus-trans-sys .el-upload-dragger {
  width: 100%;
}
.fileName_style {
  margin-right: 3px;
  vertical-align: middle;

  div {
    display: inline-block;
    max-width: 320px;
    vertical-align: top;
  }
}
</style>
