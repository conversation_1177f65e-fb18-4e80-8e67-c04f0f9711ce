<template>
  <div class="page-wrap full-main">
    <div class="header-wrap">
      <el-row style="text-align: right">
        <el-col>
          <span style="color: #b60c0c; font-size: 16px"
            >如遇到问题，可进入对应运营群进行咨询，群内有专业运营人员提供帮助。</span
          >
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="15" :md="6" class="head-title">
          <span>集团一级流程</span>
          <hr color="#b60c0c" width="100%" />
        </el-col>
        <el-col
          :xs="24"
          :sm="12"
          :md="18"
          style="text-align: right; margin-top: 25px"
        >
          <div style="display: inline-block; margin-right: 10px">
            <div style="display: inline-block">群名称：</div>
            <el-input
              placeholder="请输入群名称"
              v-model="seachData.groupName"
              clearable
              style="width: 270px"
            />
          </div>
          <div style="display: inline-block; margin-right: 10px">
            <div style="display: inline-block">省份：</div>
            <dict-select
              :value.sync="seachData.province"
              :dictId="provinceId"
              placeholder="请选择省份"
              style="width: 270px"
            />
          </div>
          <el-button
            type="primary"
            @click="seniorQuery"
            v-loading.fullscreen.lock="sheetSaveLoading"
            >查询</el-button
          >
          <el-button
            type="primary"
            @click="operationManagement"
            v-loading.fullscreen.lock="sheetCommitLoading"
            v-has="'operation:manage:edit'"
            >运营群管理</el-button
          >
        </el-col>
      </el-row>
      <el-row :gutter="20" style="margin-top: 25px; text-align: center">
        <el-col
          v-for="(item, i) in groupProcess"
          :key="i"
          :xs="24"
          :sm="15"
          :md="6"
          style="height: 300px"
        >
          <el-image
            style="width: 180px; height: 180px"
            :src="item.url"
          ></el-image>
          <div style="padding: 14px">
            <span>{{ item.groupName }}</span>

            <div class="bottom clearfix">
              <span
                ><b>{{ item.type }}&emsp;&emsp;&emsp;&emsp;</b></span
              >
              <span v-if="item.joinOrNot == '0'" style="color: green"
                >可加入</span
              >
              <span v-else style="color: #b60c0c">已满</span>
            </div>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="15" :md="6" class="head-title">
          <span>省份流程</span>
          <hr color="#b60c0c" width="100%" />
        </el-col>
        <el-col :xs="24" :sm="9" :md="16" class="head-handle-wrap"> </el-col>
      </el-row>
      <el-row :gutter="20" style="margin-top: 25px; text-align: center">
        <el-col
          v-for="(item, i) in provincialProcess"
          :key="i"
          :xs="24"
          :sm="15"
          :md="6"
          style="height: 300px"
        >
          <img :src="item.url" class="image" />
          <div style="padding: 14px">
            <span>{{ item.groupName }}</span>

            <div class="bottom clearfix">
              <span
                ><b>{{ item.type }}&emsp;&emsp;&emsp;&emsp;</b></span
              >
              <span v-if="item.joinOrNot == '0'" style="color: green"
                >可加入</span
              >
              <span v-else style="color: #b60c0c">已满</span>
              <!-- <el-button type="text" class="button">操作按钮</el-button> -->
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import { apiDict, apiQueryOperatioSupportn } from "./api/Operation.js";
import DictSelect from "./components/DictSelect.vue";
export default {
  name: "WorkOrderVoiceremind",
  components: {
    DictSelect,
  },
  data() {
    return {
      //查询条件
      seachData: {
        province: null, //省份
        groupName: "",
      },
      groupProcess: [],
      provincialProcess: [],
      provinceId: 10064,
      //新增,编辑
      visible: false,
      title: "新增",
    };
  },
  mounted() {
    this.getTablelListData();
  },
  methods: {
    //查询
    seniorQuery() {
      this.getTablelListData();
    },

    //请求数据
    getTablelListData() {
      this.tableLoading = true;
      let formData = new FormData();
      let param = {
        groupName: this.seachData.groupName,
        province: this.seachData.province, //省份
      };
      formData.append("jsonParam", JSON.stringify(param));
      apiQueryOperatioSupportn(formData)
        .then(res => {
          if (res.status == "0") {
            this.tableLoading = false;
            let r = res?.data?.groupProcess ?? [];
            let q = res?.data?.provincialProcess ?? [];
            this.groupProcess = r;
            this.provincialProcess = q;
          } else {
            this.tableLoading = false;
          }
        })
        .catch(err => {
          this.tableLoading = false;
          console.log(err);
        });
    },
    operationManagement() {
      this.$router.push({
        name: "operationManage",
      });
    },
  },
};
</script>
<style lang="scss" scoped>
@import "./assets/ivrCommon.scss";
.page-wrap {
  .header-wrap {
    padding: 30px 20px 0px 35px;
    @include themify() {
      background-color: themed("$--color-white");
    }
    ::v-deep .search-input-button {
      color: #fff;
      @include themify() {
        background-color: themed("$--color-primary");
      }
    }
  }
  .content-wrap {
    padding: 10px;
  }
  .content-card {
    height: 100%;
    overflow: auto;
    min-height: 300px;
  }
  .content-header {
    margin-bottom: 10px;
  }
  ::v-deep .el-tree {
    padding-top: 15px;
    padding-left: 10px;
    // 不可全选样式
    .el-tree-node {
      .is-leaf + .el-checkbox .el-checkbox__inner {
        display: inline-block;
      }
      .el-checkbox .el-checkbox__inner {
        display: none;
      }
    }
  }
}
.head-title {
  span {
    color: #b60c0c;
    font-size: 24px;
  }
  hr {
    border-width: 2px;
  }
  // font-size: 24px;
  // line-height: 28px;
}
.image {
  width: 180px;
  height: 180px;
}
.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}

.bottom {
  margin-top: 13px;
  line-height: 12px;
}
</style>
