<template>
  <head-content-layout class="page-wrap">
    <template #header>
      <el-form
        label-width="110px"
        :inline="false"
        class="demo-form-inline"
        label-position="right"
      >
        <el-row
          type="flex"
          style="flex-wrap: wrap"
          :gutter="20"
        >
          <el-col :span="8">
            <el-form-item label="操作人：">
              <el-input
                v-model="form.personStr"
                placeholder=""
                clearable
                style="width: 100%"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="操作时间：">
              <el-date-picker
                v-model="form.operateTimeRange"
                range-separator="-"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                style="width: 100%"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8" style="text-align: right; padding-right: 10px">
            <el-form-item label="" label-width="0">
              <el-button type="primary" @click="seniorQuery">查询</el-button>
              <el-button type="primary" @click="deleteOrders">删除</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </template>

    <template #table>
      <el-table
        ref="table"
        :data="tableData"
        :border="false"
        stripe
        height="100%"
        v-loading="tableLoading"
      >
        <el-table-column
          type="index"
          label="序号"
          width="55px"
        >
        </el-table-column>
        <el-table-column
          prop="userName"
          label="操作人"
          width="160px"
        ></el-table-column>
        <el-table-column
          prop="executeTime"
          label="操作时间"
          width="160px"
        ></el-table-column>
        <el-table-column
          prop="sheetNos"
          label="删除的工单编号"
        >
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.sheetNos" placement="top">
              <p class="descStyle">{{ scope.row.sheetNos }}</p>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          prop="result"
          label="删除结果"
        >
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.result" placement="top">
              <p class="descStyle">{{ scope.row.result }}</p>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </template>
    <template #pagination>
      <pagination
        ref="pagination"
        :total="form.total"
        :page.sync="form.pageNum"
        :limit.sync="form.pageSize"
        @change="getTableData('senior')"
        v-loading="tableLoading"
        element-loading-spinner=" "
      />
    </template>
<!--    <template #dialog>-->
<!--      <dia-orgs-user-tree-->
<!--        :title="diaPeople.title"-->
<!--        :visible.sync="diaPeople.visible"-->
<!--        :showOrgsTree="diaPeople.showOrgsTree"-->
<!--        @on-save="onSavePeople"-->
<!--      />-->
<!--    </template>-->
    <!--删除工单-->
    <template #dialog>
      <el-dialog
        title="删除"
        :append-to-body="true"
        :visible.sync="dialogDeleteVisiable"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @close="dialogDeleteOrdersClose"
        width="600px"
      >
        <el-form ref="deleteForm" :model="deleteForm" label-width="90px">
          <el-form-item
            label="工单编号:"
            prop="orderNos"
            :rules="{
            required: true,
            message: '请至少输入一条待删除的工单编号',
          }"
          >
            <el-input
              maxlength="400"
              show-word-limit
              @keyup.native="descTipZT(800, 'orderNos', 'showgdzt')"
              type="textarea"
              :rows="4"
              placeholder="请输入一个或者多个工单编号，多工单编号间用;隔开"
              v-model="deleteForm.orderNos"
              style="width: 100%"
            >
            </el-input>
            <div class="el-form-item__error" v-if="showgdzt">
              已超过填写字数上限
            </div>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button
            type="primary"
            @click="handleDeleteSubmit('deleteForm')"
            v-loading.fullscreen.lock="deleteSubmitLoading"
          >确 认</el-button
          >
          <el-button @click="dialogDeleteOrdersClose">取 消</el-button>
        </div>
      </el-dialog>
    </template>

  </head-content-layout>
</template>

<script>
import { mapGetters } from "vuex";
import HeadContentLayout from "../workOrder/components/HeadContentLayout.vue";
import Pagination from "../workOrder/components/Pagination.vue";
import DictSelect from "../workOrder/components/DictSelect.vue";
import {
  apiDeleteList,
  apiOrderDelete,
} from "./api/CommonApi";
import moment from "moment";
export default {
  name: "WorkOrderList",
  components: {
    HeadContentLayout,
    Pagination,
    DictSelect,
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  data() {
    return {
      // 查询参数
      dialogDeleteVisiable: false,
      form: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        personStr:'',
        operateTimeRange:[],
      },
      deleteForm:{
        orderNos:'',
      },
      showgdzt: false,
      showTipTime: 5000,
      deleteSubmitLoading:false,
      // startTime: moment().subtract(30, "days").format("YYYY-MM-DD 00:00:00"),
      // endTime: moment().format("YYYY-MM-DD 23:59:59"),
      tableLoading: false,
      tableData: [],
      // diaPeople: {
      //   visible: false,
      //   title: "",
      //   saveName: "",
      //   saveTitleMap: {
      //     builderDetermine: "建单人选择",
      //     agentDetermine: "主送选择",
      //   },
      //   showOrgsTree: true,
      //   showOrgsTreeMap: {
      //     builderDetermine: false,
      //   },
      // },
      userData: null,
    };
  },
  created() {
    this.userData = JSON.parse(this.userInfo.attr2);
  },
  mounted() {
    this.seniorQuery();
  },
  methods: {
    descTipZT(count, name, showName) {
      if (this.deleteForm[name].length >= count) {
        this[showName] = true;
        setTimeout(() => {
          this[showName] = false;
        }, this.showTipTime);
      } else {
        this[showName] = false;
      }
    },
    //删除工单
    deleteOrders() {
      this.dialogDeleteVisiable = true;
    },
    //删除工单关闭--
    dialogDeleteOrdersClose() {
      this.deleteForm = {
        orderNos: "",
      };
      this.dialogDeleteVisiable = false;
    },
    //删除工单提交
    handleDeleteSubmit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.deleteSubmitLoading = true;
          let param = {
            sheetNo: this.deleteForm.orderNos,
          };
          apiOrderDelete(param)
            .then(res => {
              if (res.status == "0") {
                this.$message.success(res.msg);
                this.dialogDeleteOrdersClose();
                this.seniorQuery();
                // this.dialogDeleteVisiable = false;
              } else {
                this.$message.error(res.msg);
              }
              this.deleteSubmitLoading = false;
            })
            .catch(error => {
              console.log(error);
              this.$message.error(error.msg);
              this.deleteSubmitLoading = false;
            });
        } else {
          return false;
        }
      });
    },

    seniorQuery() {
      this.form.pageNum = 1;
      this.getTableData();
    },
    onResetForm() {
      this.form = {
        pageNum: 1,
        pageSize: 10,
        total: 0,
        personStr:'',
        operateTimeRange:[],
      };
    },

    getTableData() {

      this.tableLoading = true;
      let param = {
        currentPage: this.form.pageNum,
        pageSize: this.form.pageSize,
        user: this.form.personStr,
        startTime: this?.form?.operateTimeRange?.[0] ?? "",
        endTime: this?.form?.operateTimeRange?.[1] ?? "",
      };
      apiDeleteList(param)
        .then(res => {
          if (res.status == "0") {
            this.tableData = res?.data?.info ?? [];
            this.form.total = res?.data?.total ?? 0;
          }
          this.tableLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.tableLoading = false;
        });
    },

    // onOpenPeopleDialog(diaSaveName) {
    //   this.diaPeople.title = this.diaPeople.saveTitleMap[diaSaveName];
    //   this.diaPeople.showOrgsTree = this.diaPeople.showOrgsTreeMap[diaSaveName] ?? true;
    //   this.diaPeople.saveName = diaSaveName;
    //   this.diaPeople.visible = true;
    // },

  },
};
</script>
<style lang="scss" scoped>
@import "../workOrder/workOrderWaitDetail/assets/common.scss";
.filter-total {
  ::v-deep .el-loading-mask .el-loading-spinner {
    text-align: left;
    line-height: 32px;
    margin-left: 150px;
  }
  ::v-deep .el-loading-text {
    display: inline-block;
    margin-left: 10px;
  }
}
.descStyle {
  display: -webkit-box;
  overflow: hidden;
  white-space: normal !important;
  text-overflow: ellipsis;
  word-wrap: break-word;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.acceptStyle {
  display: -webkit-box;
  overflow: hidden;
  white-space: normal !important;
  text-overflow: ellipsis;
  word-wrap: break-word;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

</style>
