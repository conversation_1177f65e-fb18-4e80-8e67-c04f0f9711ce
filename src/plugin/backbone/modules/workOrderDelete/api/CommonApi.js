import {
  getJson,
  postJson,
  getJsonBlob,
  postFormData,
  delJson,
  postJsonBlob,
} from "@/utils/axios";
const apiDeleteListUrl = "integration/delete/queryInfo"; //工单删除记录查询
const orderDeleteUrl = "integration/delete/deleteWorkOrderBySheetNo"; //工单删除

const apiDeleteList = params => postJson(apiDeleteListUrl, params);
const apiOrderDelete = params => postJson(orderDeleteUrl, params);


// 时间戳 ，转化成 日期时间格式。如果是当前时间，传参 Date.now()
function getCurrentTime(timeVal) {
  const time = new Date(timeVal);
  const yy = time.getFullYear();
  const mm =
    time.getMonth() + 1 < 10
      ? "0" + (time.getMonth() + 1)
      : time.getMonth() + 1;
  const dd = time.getDate() < 10 ? "0" + time.getDate() : time.getDate();
  const hh = time.getHours() < 10 ? "0" + time.getHours() : time.getHours();
  const mf =
    time.getMinutes() < 10 ? "0" + time.getMinutes() : time.getMinutes();
  const ss =
    time.getSeconds() < 10 ? "0" + time.getSeconds() : time.getSeconds();
  const gettime = yy + "-" + mm + "-" + dd + " " + hh + ":" + mf + ":" + ss;
  return gettime;
}


export {
  apiDeleteList,
  apiOrderDelete
};
