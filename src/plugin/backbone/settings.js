module.exports = {
  //端口号
  port: 8228,

  //项目名称
  title: "故障工单",

  //项目名称英文
  titleEn: "",

  //项目logo
  logo: "",
  logoMini: "",
  //topmenu布局是否显示logo
  topShowLogo: true,
  //sidemenu布局是否显示logo
  sideShowLogo: true,

  //自定义登录页
  //以src开头
  //loginFile: () => import("./modules/Login/Login.vue"),
  loginFile: "",
  // loginFile: () => import("./modules/Login/Login.vue"), // loginFile: "src/plugin/alarm/modules/Login/Login.vue",
  //登录页背景图
  //为空时使用默认背景图
  //以src开头 示例src/plugin/visual/modules /...
  loginBgUrl: null,

  //项目版权信息
  copyright: "版权声明 Copyright@2022版权所有",

  //layout - 整体布局方式 ['sidemenu', 'topmenu'] 两种布局
  layout: "sidemenu",

  //固定左侧菜单栏: boolean
  fixedAside: true,

  //折叠左侧菜单栏: boolean
  collapseAside: false,

  //固定 Header: boolean
  fixedHeader: true,

  //topmenu布局是否显示面包屑
  topShowBreadcrumb: false,

  //sidemenu布局是否显示面包屑
  sideShowBreadcrumb: true,

  //是否隐藏tab页
  hideTabView: false,

  //全局scss变量
  globalVariablesPath:
    "./src/themes/theme-variables/frame-variables-default.scss",

  //svg图标目录
  svgFiles: [
    "src/assets/icons/svg/",
    "src/plugin/backbone/assets/images/icons/svg/",
  ],

  //可以切换的主题,默认值["default","blue","dark","gray"]
  themeArr: ["default", "blue", "dark"],

  //默认主题名称,如果不在themeArr中,取themeArr第一个
  themeDefault: "default",

  //可以切换的echarts主题
  //默认值["default","light","dark",]
  //ECharts4开始,除了一贯的默认主题外,新内置了两套主题,分别为'light'和'dark',自定义主题另起名称
  echartsThemeArr: ["walden", "chalk", "custom_black"],

  //echarts默认主题名称,如果不在echartsThemeArr中,取echartsThemeArr第一个
  echartsThemeDefault: "custom_black",

  //天宫测试环境
  axiosBasePath: process.env.NODE_ENV === "production" ? "/nfm3/" : "",
  routerMode: "history", //默认：hash
  routerBase: "/nfm3", //站点名
  publicPath: "/nfm3",
  enableMonitoring: false, // 是否对接监控平台

  //天宫生产环境
  // axiosBasePath: process.env.NODE_ENV === "production" ? "/prod-nfm3/" : "",
  // routerMode: "history", //默认：hash
  // routerBase: "/prod-nfm3/", //站点名
  // publicPath: "/prod-nfm3/",
  // enableMonitoring: true, // 是否对接监控平台

  excludeJs: ["./src/assets/js/ht"],

  // timeoutTime: 12 * 60 * 60 * 1000, //12小时
  timeoutTime: 12 * 60 * 1000, //12小时

  //后端服务代理
  proxy: {
    // "/otdrInterface": {
    //   // target: "http://**************:10088/sdms/api/otdrbreak/byalarm",
    //   target: "http://**************:10089/sdms",
    //   changeOrigin: true,
    //   pathRewrite: {
    //     "^/otdrInterface": "",
    //   },
    // },
    "/": {
      target: "http://**************:9080/nfm3",
      changeOrigin: true,
    },
    // "/tianyan": {
    //   target: "http://*************:9664",
    //   changeOrigin: true,
    // },
    // "/framework": {
    //   target: "http://localhost:9903",
    //   changeOrigin: true,
    // },
    // "/login": {
    //   target: "http://localhost:9903",
    //   changeOrigin: true,
    // },
    // "/sso": {
    //   target: "http://localhost:9903",
    //   changeOrigin: true,
    // },
    // "/backbone": {
    //   target: "http://localhost:9903",
    //   changeOrigin: true,
    // },
    // "/commCloud": {
    //   target: "http://localhost:9803",
    //   changeOrigin: true,
    // },
    // "/commonFlow": {
    //   target: "http://localhost:9907",
    //   changeOrigin: true,
    // },
    // "/province": {
    //   target: "http://localhost:9904",
    //   changeOrigin: true,
    // },
    // "/commonDict": {
    //   target: "http://localhost:8785",
    //   pathRewrite: { "^/commonDict": "" },
    //   changeOrigin: true,
    // },
    // "/resweb": {
    //   target: "http://***********:9080",
    //   changeOrigin: true,
    // },
    // "/EOM_LIFE_ADJUST": {
    //   target: "http://************:8088",
    //   changeOrigin: true,
    // },
    // "/wireless": {
    //   target: "http://localhost:9909",
    //   changeOrigin: true,
    // },
    // "/test": {
    //   target:
    //     "https://cos.zz-hqc.cos.tg.ncmp.unicom.local/449140254809:netfm3/img/21976385f7744e189037944ea9fe0335.jpg",
    //   changeOrigin: true,
    // },
  },
};
