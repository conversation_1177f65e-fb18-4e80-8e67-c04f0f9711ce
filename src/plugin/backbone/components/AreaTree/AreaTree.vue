<template>
  <el-dialog
    title="故障发生地区"
    :visible="visible"
    width="460px"
    append-to-body
    :close-on-click-modal="false"
    @close="onClose"
  >
    <el-tabs type="border-card">
      <el-tab-pane label="行政区划树">
        <el-input
          v-model="search"
          placeholder="请输入省/地市/区县"
          clearable
          @clear="onClear"
          @keyup.enter.native="onQuery"
        >
          <template #append>
            <el-button
              type="primary"
              icon="el-icon-search"
              @click="onQuery"
            ></el-button>
            <el-button
              type="primary"
              icon="el-icon-close"
              @click="onClear"
            ></el-button>
          </template>
        </el-input>
        <div style="height: 260px; margin-top: 6px; overflow: auto">
          <el-tree
            v-show="showTree"
            ref="tree"
            :data="treeData"
            node-key="areaCode"
            :props="treeProp"
            :empty-text="loading ? '加载中...' : '暂无数据'"
            :show-checkbox="true"
            :highlight-current="true"
            :check-strictly="true"
            @check="onTreeCheck"
          ></el-tree>
          <el-table
            v-show="!showTree"
            :data="filterTable"
            stripe
            height="100%"
            @row-click="rowClick"
          >
            <el-table-column label="" width="50">
              <template v-slot="{ row }">
                <el-radio v-model="tableRadio" :label="row.id">{{
                  ""
                }}</el-radio>
              </template>
            </el-table-column>
            <el-table-column prop="proName" label="省"></el-table-column>
            <el-table-column prop="cityName" label="地市"></el-table-column>
            <el-table-column
              v-if="level == 3"
              prop="regionName"
              label="区县"
            ></el-table-column>
          </el-table>
        </div>
      </el-tab-pane>
    </el-tabs>
    <span slot="footer">
      <el-button type="primary" @click="onSave">确定</el-button>
      <el-button @click="onClose">取消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { getAreaDataApi } from "./api.js";

export default {
  name: "AreaTree",
  props: {
    visible: Boolean,
    value: {
      type: [String],
    },
    level: {
      type: [String, Number],
      default: 2,
    },
  },
  data() {
    return {
      loading: false,
      search: "",
      treeData: [],
      treeProp: {
        label: "areaName",
        isLeaf: "children",
      },
      showTree: true,
      filterTable: [],
      tableRadio: "",
    };
  },
  computed: {
    tableData() {
      let arr = [];
      this.treeData.forEach(item => {
        const proData = {
          proCode: item.areaCode,
          proName: item.areaName,
        };
        arr.push(proData);
        if (item.children) {
          item.children.forEach(tmp => {
            const cityData = {
              ...proData,
              cityCode: tmp.areaCode,
              cityName: tmp.areaName,
            };
            arr.push(cityData);
            if (tmp.children) {
              tmp.children.forEach(ele => {
                arr.push({
                  ...cityData,
                  regionCode: ele.areaCode,
                  regionName: ele.areaName,
                });
              });
            }
          });
        }
      });
      return arr.map(item => ({
        ...item,
        id: `${item.proCode}${item.cityCode || ""}${item.regionCode || ""}`,
      }));
    },
  },
  mounted() {
    this.getAreaData();
  },
  methods: {
    getAreaData() {
      this.loading = true;
      getAreaDataApi({ areaLevel: this.level })
        .then(res => {
          this.treeData = res.data;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    onTreeCheck(data, checkObj) {
      let self = this;
      const { checkedKeys, checkedNodes, halfCheckedKeys } = checkObj;
      halfCheckedKeys.forEach(item => {
        self.$refs.tree.getNode(item).indeterminate = false;
      });
      if (checkedKeys.includes(data.areaCode)) {
        checkedNodes.forEach(item => {
          if (item.areaCode != data.areaCode) {
            self.$refs.tree.setChecked(item, false);
          }
        });
      }
      const parentHand = function (nodeData) {
        if (nodeData.parentCode) {
          const parent = self.$refs.tree.getNode(nodeData.parentCode);
          parent.indeterminate =
            self.$refs.tree.getCheckedKeys().length > 0 ? true : false;
          if (parent.level > 1) {
            parentHand(parent.data);
          }
        }
      };
      parentHand(data);
    },
    onQuery() {
      if ((this.search ?? "") == 0) return;
      this.showTree = false;
      this.tableRadio = "";
      const search = this.search;
      this.filterTable = this.tableData.filter(
        item =>
          item.proName?.includes?.(search) ||
          item.cityName?.includes?.(search) ||
          item.regionName?.includes?.(search)
      );
    },
    onClear() {
      this.search = "";
      this.filterTable = this.tableData;
      this.showTree = true;
      this.tableRadio = "";
    },
    rowClick(row) {
      this.tableRadio = row.id;
    },
    onClose() {
      this.$emit("update:visible", false);
    },
    onSave() {
      let value = "";
      if (this.showTree) {
        value = this.$refs.tree
          .getCheckedNodes(false, true)
          .reduce((prev, cur) => prev + cur.areaName, "");
      } else {
        const tableSel =
          this.filterTable.find(item => item.id == this.tableRadio) || {};
        value =
          `${tableSel.proName || ""}${tableSel.cityName || ""}${
            tableSel.regionName || ""
          }` || "";
      }
      this.$emit("input", value);
      this.$emit("save", value);
      this.onClose();
    },
  },
};
</script>
