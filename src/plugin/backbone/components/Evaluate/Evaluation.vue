<template>
  <div style="display: inline" v-if="isShowEvaBtn">
    <div class="evaCla">
      <el-button
        v-if="isSatisfied != '1'"
        icon="el-icon-my-default"
        size="mini"
        @click="evaluate('1')"
      >
        准确
      </el-button>
      <el-button
        v-if="isSatisfied == '1'"
        icon="el-icon-my"
        size="mini"
        style="color: #b50b14"
        @click="evaluate('')"
      >
        准确
      </el-button>
      <el-button
        v-if="isSatisfied != '0'"
        icon="el-icon-bmy-default"
        size="mini"
        @click="evaluate('0')"
      >
        不准确
      </el-button>
      <el-button
        v-if="isSatisfied == '0'"
        icon="el-icon-bmy"
        size="mini"
        style="color: #b50b14"
        @click="evaluate('')"
      >
        不准确
      </el-button>
    </div>
    <el-dialog
      width="700px"
      title="功能评价"
      :visible.sync="isShowEvaluateDialog"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      @close="handleClose"
      append-to-body
    >
      <el-form ref="evaluation" :model="evaluationForm" label-width="90px">
        <el-form-item
          label="反馈问题:"
          key="167"
          prop="feedbackProblemCheckList"
          :rules="[
            {
              required: true,
              message: '请选择要反馈的问题',
              trigger: 'blur',
            },
          ]"
        >
          <el-checkbox-group
            v-model="evaluationForm.feedbackProblemCheckList"
            class="check-group"
          >
            <el-checkbox
              v-for="(item, i) in fbProblemArr"
              :label="item"
            ></el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item
          prop="viewsOnContent"
          key="134"
          style="width: 100%"
          label="评价意见:"
          :rules="{
            required:
              evaluationForm.feedbackProblemCheckList
                .join(',')
                .indexOf('其他') != -1
                ? true
                : false,
            message: '请填写评价意见',
            trigger: 'blur',
          }"
        >
          <el-input
            type="textarea"
            :rows="3"
            placeholder="请填写评价意见"
            v-model="evaluationForm.viewsOnContent"
            show-word-limit
            maxlength="500"
            @keyup.native="descTip(500, 'viewsOnContent', 'showTip')"
          >
          </el-input>
          <div class="el-form-item__error" v-if="showTip">
            已超过填写字数上限
          </div>
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button
          type="primary"
          @click="handleSubmit('evaluation')"
          v-loading.fullscreen.lock="evaFullscreenLoading"
          >提 交</el-button
        >
        <el-button @click="resetForm">重置</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import {
  apiEvaluateInitRelationDiagnosis,
  apiEvaluateRelationDiagnosis,
} from "./api";

export default {
  name: "Evaluation",
  components: {},
  props: {
    woId: String,
    relationDiagnosis: Object,
    // fbProblemArr: Array,
  },
  data() {
    return {
      showTip: false,
      showTime: 5000,
      isShowEvaBtn: false,
      evaFullscreenLoading: false,
      fbProblemArr: [],
      evaSaveParam: {},
      evaOrigionValue: "",
      isSatisfied: "", //是否满意
      isShowEvaluateDialog: false, //是否显示评价弹框
      evaluationForm: {
        viewsOnContent: null,
        feedbackProblemCheckList: [],
      },
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  watch: {},
  mounted() {
    this.fbProblemArr = this.relationDiagnosis.problemClass;
    this.initEvaluate();
  },

  methods: {
    descTip(count, name, showName) {
      if (this.evaluationForm[name].length >= count) {
        this[showName] = true;
        setTimeout(() => {
          this[showName] = false;
        }, this.showTime);
      } else {
        this[showName] = false;
      }
    },
    handleClose() {
      this.resetForm();
    },
    resetForm() {
      this.evaluationForm = {
        viewsOnContent: null,
        feedbackProblemCheckList: [],
      };
    },
    //评价初始化
    initEvaluate() {
      let param = {
        woId: this.woId,
        type: "click",
      };

      let self = this;
      apiEvaluateInitRelationDiagnosis(param)
        .then(res => {
          if (res.status == "0") {
            //evaluationStatus = 0 未评价 1已评价； evaluationSatisfied 判断 1-满意。0-不满意
            self.isShowEvaBtn = true;
            if (res.data.evaluationStatus == 0) {
              self.isSatisfied = "";
            } else if (res.data.evaluationStatus == 1) {
              self.isSatisfied = res.data.evaluationSatisfied;
            }
            self.evaSaveParam = res.data;
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    evaluate(type) {
      this.evaOrigionValue = type;
      if (type == "1") {
        //满意
        this.evaSaveParam.evaluationStatus = 1;
        this.evaSaveParam.evaluationSatisfied = 1;
        this.submit();
      } else if (type == "0") {
        //不满意
        if (this.fbProblemArr != null && this.fbProblemArr.length != 0) {
          //弹框
          this.isShowEvaluateDialog = true;
        } else {
          this.evaSaveParam.evaluationStatus = 1;
          this.evaSaveParam.evaluationSatisfied = 0;
          this.submit();
        }
      } else {
        //取消评价
        this.evaSaveParam.evaluationStatus = 2;
        this.evaSaveParam.evaluationSatisfied = "";
        this.submit();
      }
    },
    handleSubmit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.evaFullscreenLoading = true;

          this.evaSaveParam.evaluationStatus = 1;
          this.evaSaveParam.evaluationSatisfied = 0;
          this.evaSaveParam.evaluationOpinions = this.evaluationForm.viewsOnContent;
          this.evaSaveParam.problemClass =
            this.evaluationForm.feedbackProblemCheckList.length > 0
              ? this.evaluationForm.feedbackProblemCheckList.join(",")
              : "";
          this.submit();
        } else {
          return false;
        }
      });
    },
    submit() {
      this.evaSaveParam.evaluationScenario = this.relationDiagnosis.evaluationScenario;
      apiEvaluateRelationDiagnosis(this.evaSaveParam)
        .then(res => {
          if (res.status == "0") {
            this.isSatisfied = this.evaOrigionValue;
            if (this.isSatisfied != "") {
              this.$message.success("已完成评价");
            } else {
              this.$message.warning("已取消评价");
            }
            this.isShowEvaluateDialog = false;
            this.evaluationDialogVisible = false;
          } else {
            this.$message.error(res.msg);
          }
          this.evaFullscreenLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.$message.error(error.msg);
          this.evaFullscreenLoading = false;
        });
    },
  },
};
</script>
<style lang="scss" scoped>
@import "../../modules/commonProvince/workOrderWaitDetail/assets/common.scss";
.evaCla {
  display: inline-flex;
  margin-left: 14px;
}

.evaCla .el-button {
  border: none !important;
  font-size: 12px;
  display: flex;
  align-items: center;
  height: 20px;
  padding: 0px;
}

.evaCla .el-button:focus,
.custom-theme-default .el-button:hover {
  color: #b50b14;
  /*border-color: #e9b6b9;*/
  background-color: #ffffff;
}

::v-deep .el-icon-my {
  background: url("../../assets/my.png") center no-repeat;
  background-size: contain;
  width: 12px;
  height: 12px;
}

::v-deep .el-icon-my-default {
  background: url("../../assets/myDefault.png") center no-repeat;
  background-size: contain;
  width: 12px;
  height: 12px;
}

::v-deep .el-icon-bmy {
  background: url("../../assets/bmy.png") center no-repeat;
  background-size: contain;
  width: 12px;
  height: 12px;
}

::v-deep .el-icon-bmy-default {
  background: url("../../assets/bmyDefault.png") center no-repeat;
  background-size: contain;
  width: 12px;
  height: 12px;
}
.check-group ::v-deep .el-checkbox {
  display: inline-block;
  width: calc(49% - 30px);
}
</style>
