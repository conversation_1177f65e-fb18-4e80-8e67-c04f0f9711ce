import { get, postJson, getJson } from "@/utils/axios";

const commandQueryUrl = "integration/opticalPower/queryInfo"; // 获取TaskId 信息--
const refreshCommandQueryUrl = "integration/opticalPower/queryInfo"; // 指令查询 长连接接口--
const queryHistoryUrl = "integration/opticalPower/queryHistory"; //光功率历史查询 --
const queryHistoryDetailUrl = "integration/opticalPower/queryInfoByTaskId"; //指令历史详情查询
const commandManualQueryUrl = "integration/opticalPower/queryInfoByCard";//手工查询光功率 --
const ggInfoTableUrl = "integration/opticalPower/queryInfoByTaskId";//光功率查询根据任务id --
const cardPortUrl = "integration/opticalPower/queryCardOrPortInfo";//--
const areaUrl = "integration/opticalPower/faultArea";//--
const neUrl = "integration/opticalPower/queryNeInfo";//--

const apiCommandQuery = params =>
  get(commandQueryUrl + "?sheetNo=" + params.sheetNo, { timeout: 0 });
const apiRefreshCommandQuery = params =>
  postJson(refreshCommandQueryUrl, params, { timeout: 20000 });
const apiQueryHistory = params =>
  postJson(queryHistoryUrl, params, { timeout: 0 });
const apiQueryHistoryDetail = params =>
  postJson(queryHistoryDetailUrl, params, { timeout: 0 });
const apiCommandManualQuery = params =>
  postJson(commandManualQueryUrl, params, { timeout: 0 });
const apiQueryGglInfoTableByTaskId = params =>
  postJson(ggInfoTableUrl, params, { timeout: 0 });
const apiQueryCardOrPortInfo = params => postJson(cardPortUrl, params);
const apiArea = params => getJson(areaUrl, params);
const apiNeOptions = params => postJson(neUrl, params);
export {
  apiCommandQuery,
  apiQueryHistory,
  apiRefreshCommandQuery,
  apiQueryHistoryDetail,
  apiCommandManualQuery,
  apiQueryGglInfoTableByTaskId,
  apiQueryCardOrPortInfo,
  apiArea,
  apiNeOptions,
};
