<template>
  <div>
    <el-collapse v-model="activeNames">
      <el-collapse-item name="1">
        <div class="collapse-title" slot="title">
          <span style="margin-right: 10px">状态查询</span>
          <div style="display: inline-block">
            <el-radio-group v-model="radio1" @change="handleClick" size="mini">
              <el-radio-button label="指令查询"></el-radio-button>
              <el-radio-button label="历史查询记录"></el-radio-button>
            </el-radio-group>
          </div>
        </div>

        <div v-if="radio1 == '指令查询'">
          <!--指令按钮-->
          <template v-for="item in commandBtns">
            <div
              :key="item.id"
              class="filter-total-item"
              :class="{ active: selectedCommand.name == item.name }"
            >
              <div class="btnDiv">
                <span @click="selectedFilter(item)"> {{ item.name }} </span>
              </div>
            </div>
          </template>
          <div
            v-if="selectedCommand.name == '实时光功率查询'"
            style="margin-top: 16px; margin-left: 30px"
          >
            <span style="line-height: normal;display: block"
              >涉及传输段：{{ transRelationData.transSegName }}</span
            >
            <div style="display: flex;justify-content: flex-end;margin-top: 4px; margin-right: 20px; margin-bottom: 10px">
              <el-button
                type="primary"
                size="mini"
                @click="seniorQuery"
                :loading="buttonLockLoading"
              >查询</el-button
              >
            </div>
          </div>
          <div
            style="margin-top: 20px"
            v-else-if="selectedCommand.name == '光功率手工查询'"
          >
            <el-form
              ref="queryForm"
              :inline="false"
              class="demo-form-inline"
              :model="queryForm"
              label-width="90px"
              :rules="formRule"
            >
              <el-row type="flex" style="flex-wrap: wrap">
                <el-col :span="8">
                  <el-form-item label="省份:" prop="provinceName">
                    <el-select
                      disabled
                      style="width: 100%"
                      placeholder="请选择省份名称"
                      v-model="queryForm.provinceName"
                      filterable
                    >
                      <el-option
                        v-for="(item, i) in provinceOptions"
                        :key="i"
                        :label="item.areaName"
                        :value="item.areaName"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="地市:" prop="cityName">
                    <el-select
                      style="width: 100%"
                      placeholder="请选择地市名称"
                      v-model="queryForm.cityName"
                      filterable
                      @change="changeRegion"
                      clearable
                    >
                      <el-option
                        v-for="(item, i) in regionOptions"
                        :key="i"
                        :label="item.areaName"
                        :value="item.areaName"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="网元名称:" prop="neName">
                    <el-select
                      style="width: 100%"
                      placeholder="请选择网元名称"
                      v-model="queryForm.neName"
                      filterable
                      @change="changeNeName"
                    >
                      <el-option
                        v-for="(item, i) in neNameOptions"
                        :key="i"
                        :label="item.label"
                        :value="item.value"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="板卡名称:" prop="cardName">
                    <el-select
                      style="width: 100%"
                      placeholder="请选择板卡名称"
                      v-model="queryForm.cardName"
                      filterable
                      @change="getPortOptions"
                    >
                      <el-option
                        v-for="(item, i) in cardNameOptions"
                        :key="i"
                        :label="item.label"
                        :value="item.value"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="端口名称:" prop="portName">
                    <el-select
                      style="width: 100%"
                      placeholder="请选择端口名称"
                      v-model="queryForm.portName"
                      filterable
                    >
                      <el-option
                        v-for="(item, i) in portNameOptions"
                        :key="i"
                        :label="item.label"
                        :value="item.value"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8"> </el-col>
                <el-col :span="24" style="text-align: end;margin-top: -10px">
                  <el-button
                    type="primary"
                    @click="manualQuery"
                    size="mini"
                    :loading="buttonLockLoading"
                    >查询</el-button
                  >
                  <el-button type="primary"
                             size="mini" @click="resetQueryForm('queryForm')"
                    >重置</el-button
                  >
                </el-col>
              </el-row>
            </el-form>
          </div>
<!--          <div v-show="selectedCommand.name == '实时光功率查询'">-->
<!--            -->
<!--          </div>-->
          <div class="tipCla" v-if="showComTable || commandSearchErrorMsg">
            <span style="font-weight: bold; margin-left: 5px">查询结果</span>
            <!--错误提示-->
            <div
              v-if="commandSearchErrorMsg && !showComTable"
              style="margin-top: 5px"
            >
              <span style="color: #b50b14">【查询失败】</span>报错解释：{{
                commandSearchErrorMsg
              }}
            </div>
            <div v-if="showComTable">
              <el-table
                ref="commandTable"
                :data="commandData"
                style="width: 100%; margin-top: 10px"
                v-loading="tableLoading"
              >
                <el-table-column type="index" label="序号" width="60px">
                </el-table-column>
                <el-table-column
                  prop="transSegName"
                  label="传输段名称"
                  min-width="180px"
                  v-if="selectedCommand.name == '实时光功率查询'"
                >
                  <template slot-scope="scope">
                    <span v-if="!scope.row.transSegName"> -- </span>
                    <span v-else>{{ scope.row.transSegName }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="systemName"
                  label="传输系统名称"
                  min-width="180px"
                  v-if="selectedCommand.name == '光功率手工查询'"
                  ><template slot-scope="scope">
                    <span v-if="!scope.row.systemName"> -- </span>
                    <span v-else>{{ scope.row.systemName }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="portName"
                  label="端口名称"
                  min-width="180px"
                >
                </el-table-column>
                <el-table-column
                  prop="outPower"
                  label="输出光功率"
                  min-width="80px"
                >
                </el-table-column>
                <el-table-column
                  prop="inPower"
                  label="输入光功率"
                  min-width="80px"
                >
                </el-table-column>
              </el-table>
              <pagination
                ref="pagination1"
                :total="comForm.total"
                :page.sync="comForm.pageNum"
                :limit.sync="comForm.pageSize"
                layout="->, total, sizes, prev, pager, next"
                @change="refreshCommandData()"
              />
            </div>
          </div>
        </div>
        <div v-else>
          <template v-if="!showHisDetail">
            <div
              style="margin-left: 16px; margin-bottom: 12px; margin-top: 12px"
            >
              <span style="color: #b50b14">提醒：</span
              >仅支持查询近7天的指令查询历史记录。
            </div>
            <!--查询记录-->
            <template>
              <el-table
                :data="historySearchData"
                stripe
                ref="hisTable"
                v-loading="historyTableLoading"
                style="width: 100%"
              >
                <el-table-column type="index" label="序号" width="80">
                </el-table-column>
                <el-table-column prop="commandName" label="指令名称">
                </el-table-column>
                <el-table-column prop="createTime" label="操作时间">
                </el-table-column>
                <el-table-column
                  label="查询状态"
                  width="180"
                >
                  <template slot-scope="scope">
                    <span v-if="scope.row.queryStatus == '查询成功'">查询成功</span>
                    <span v-if="scope.row.queryStatus == '查询失败'" style="color: #3a8ee6">查询失败</span>
                    <span v-if="scope.row.queryStatus == '查询中'">查询中</span>
                  </template>
                </el-table-column>
                <el-table-column label="查询结果" width="180">
                  <template slot-scope="scope">
                    <span v-if="scope.row.queryStatus == '查询中'">查询中</span>
<!--                    <span v-else-if="scope.row.queryStatus == '查询失败'"-->
<!--                      >查询失败</span-->
<!--                    >-->
                    <el-button
                      v-else-if="scope.row.queryStatus != '查询中'"
                      @click="searchResultDetail(scope.row)"
                      class="viewBtn"
                      type="text"
                      size="small"
                      >查看详情</el-button
                    >
                  </template>
                </el-table-column>
              </el-table>
              <pagination
                ref="pagination2"
                :total="hisForm.total"
                :page.sync="hisForm.pageNum"
                :limit.sync="hisForm.pageSize"
                layout="->, total, sizes, prev, pager, next"
                @change="hisPageChange()"
              />
            </template>
          </template>
          <template v-else>
            <el-card
              shadow="never"
              class="detailCard"
              :body-style="{ padding: '10px 24px'}">
              <div class="header clearfix">
                <span class="header-title">查询条件</span>
                <div class="header-right backBtn">
                  <el-button style="border: none" size="mini" @click="closeDetail" icon="el-icon-arrow-left"
                  ></el-button
                  >
                </div>
              </div>

              <div
                v-if="detailType == '实时光功率查询'"
                style="margin-top: 6px"
              >
                <span style="line-height: normal;flex-shrink: 0">
                  涉及传输段：{{ detailTransSegName }}
                </span>
              </div>
              <div v-if="detailType == '光功率手工查询'">
                <el-descriptions :column="3" style="margin-top: 3px">
                  <el-descriptions-item label="省份">{{
                    detailQueryItem.province
                    }}</el-descriptions-item>
                  <el-descriptions-item label="地市">
                    <span v-if="detailQueryItem.region">{{
                      detailQueryItem.region
                    }}</span>
                    <span v-else>--</span></el-descriptions-item
                  >
                  <el-descriptions-item label="网元名称">{{
                    detailQueryItem.neName
                    }}</el-descriptions-item>
                  <el-descriptions-item label="板卡名称">{{
                    detailQueryItem.card
                    }}</el-descriptions-item>
                  <el-descriptions-item label="端口名称">{{
                    detailQueryItem.portName
                    }}</el-descriptions-item>
                </el-descriptions>
              </div>
            </el-card>
            <hr>
            <el-card
              shadow="never"
              class="detailCard"
              :body-style="{ padding: '10px 24px' }"
            >

              <div class="header clearfix">
                <span class="header-title">查询结果</span>
              </div>

              <!--错误提示-->
              <div v-if="detailErrorMsg!=''">
                <span style="color: #b50b14">【查询失败】</span>报错解释：{{detailErrorMsg}}
              </div>
              <!--列表-->
              <template v-if="detailErrorMsg==''">
                <el-table
                  ref="detailTable"
                  :data="detailResultData"
                  style="width: 100%"
                  v-loading="hisDetailLoading"
                >
                  <el-table-column type="index" label="序号" width="60px">
                  </el-table-column>
                  <el-table-column
                    prop="transSegName"
                    label="传输段名称"
                    min-width="180px"
                    v-if="detailType == '实时光功率查询'"
                  >
                    <template slot-scope="scope">
                      <span v-if="!scope.row.transSegName"> -- </span>
                      <span v-else>{{ scope.row.transSegName }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="systemName"
                    label="传输系统名称"
                    min-width="180px"
                    v-if="detailType == '光功率手工查询'"
                  >
                    <template slot-scope="scope">
                      <span v-if="!scope.row.systemName"> -- </span>
                      <span v-else>{{ scope.row.systemName }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="portName"
                    label="端口名称"
                    min-width="180px"
                  >
                  </el-table-column>
                  <el-table-column
                    prop="outPower"
                    label="输出光功率"
                    min-width="80px"
                  >
                  </el-table-column>
                  <el-table-column
                    prop="inPower"
                    label="输入光功率"
                    min-width="80px"
                  >
                  </el-table-column>
                </el-table>
                <pagination
                  ref="pagination3"
                  :total="detailForm.total"
                  :page.sync="detailForm.pageNum"
                  :limit.sync="detailForm.pageSize"
                  layout="->, total, sizes, prev, pager, next"
                  @change="queryDetailData()"
                />
              </template>
            </el-card>
          </template>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script>
import Pagination from "../../modules/workOrder/components/Pagination.vue";
import { mapGetters } from "vuex";
import {
  apiCommandQuery,
  apiQueryHistory,
  apiQueryHistoryDetail,
  apiCommandManualQuery,
  apiQueryGglInfoTableByTaskId,
  apiQueryCardOrPortInfo,
  apiArea,
  apiNeOptions,
} from "./apiGGL";

export default {
  name: "CommandQueryBackbone",
  components: {
    Pagination,
  },
  props: {
    basicWorkOrderData: Object,
    // relationDiagnosis: Object,
    transRelationData: Object,
  },
  data() {
    return {
      hisTimer: null,
      timer: null,
      tableLoading: false,
      radio1: "指令查询",
      activeNames: ["1"],
      selectedCommand: { name: "" },
      commandIcons: [],
      commandIconsSelected: [],
      commandData: [],
      commandSearchErrorMsg: "",
      showComTable: false,
      //历史记录相关
      historySearchData: [],
      hisForm: {
        pageNum: 1,
        pageSize: 5,
        total: 0,
      },
      //详情相关
      showHisDetail: false,

      detailResultData: [],
      historyTableLoading: false,
      hisDetailLoading: false,
      buttonLockLoading: false,
      taskId: null,
      longTimer: null,
      longManualTimer: null,
      queryClickLock: false,
      manualQueryClickLock: false,
      commandBtns: [
        {
          name: "光功率手工查询",
          id: "2",
          hide: false,
        },
      ],
      queryForm: {
        provinceName: "",
        cityName: "",
        neName: "",
        cardName: "",
        portName: "",
        portDn: "",
        systemName: "",
      },
      provinceOptions: [],
      regionOptions: [],
      neNameOptions: [],
      cardNameOptions: [],
      portNameOptions: [],
      formRule: {
        provinceName: [{ required: true, message: "请选择省份" }],
        cityName: [{ required: true, message: "请选择地市" }],
        neName: [{ required: true, message: "请选择网元名称" }],
        cardName: [{ required: true, message: "请选择板卡名称" }],
        portName: [{ required: true, message: "请选择端口名称" }],
      },
      detailQueryItem: {
        province: "",
        region: "",
        neName: "",
        card: "",
        portName: "",
      },
      detailType:'',
      detailTransSegName:'',
      detailErrorMsg:'',
      comForm: {
        pageNum: 1,
        pageSize: 5,
        total: 0,
      },
      detailForm: {
        pageNum: 1,
        pageSize: 5,
        total: 0,
      },
      provinceCode:'',
      cityCode:'',
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  mounted() {
    if (this.transRelationData.transSegName) {
      this.commandBtns.unshift({
        name: "实时光功率查询",
        id: "1",
        hide: false,
      });
      this.selectedCommand.name = "实时光功率查询";
    } else {
      this.selectedCommand.name = "光功率手工查询";
    }
    this.queryForm.provinceName = this.transRelationData.provinceName;
    this.provinceCode = this.transRelationData.provinceCode;
    this.cityCode = this.transRelationData.regionCode
    this.getRegionOptions();
    // this.commandIcons = [require("../../assets/gmkgl.png")];
    //this.commandIconsSelected = [require("../../assets/gmkgl_selected.png")];
  },
  methods: {
    //指令查询
    seniorQuery() {
      if (!this.queryClickLock) {
        this.queryClickLock = true;
        setTimeout(() => {
          // 1分钟后，重置queryClickLock状态
          this.queryClickLock = false;
        }, 60000);

        this.tableLoading = true;
        this.showComTable = true;
        let param = {
          sheetNo: this.basicWorkOrderData.sheetNo,
        };
        apiCommandQuery(param)
          .then(res => {
            if (res.status == "0") {
              this.$message.warning(
                "您可继续等待或稍后进入历史查询记录中查看结果"
              );
              this.taskId = res.data;
              this.refreshCommandData();
            } else {
              this.showComTable = false;
              this.commandSearchErrorMsg = res.msg;
              this.comForm.total = 0;
            }
            this.tableLoading = false;
          })
          .catch(error => {
            console.log(error);
            this.showComTable = false;
            this.commandSearchErrorMsg = error.msg;
            this.comForm.total = 0;
            this.tableLoading = false;
          });
      } else {
        this.$message.warning("一分钟内，请勿多次点击！");
      }
    },
    refreshCommandData() {
      let param = {
        "taskId": this.taskId,
        "pageSize" : this.comForm.pageSize,
        "currentPage" : this.comForm.pageNum
      };

      this.tableLoading = true;
      apiQueryHistoryDetail(param)
        .then(res => {
          if (res.status == "0") {
            if (res.data.result == 'success'){
              this.commandData = res?.data?.data ?? [];
              this.comForm.total = res?.data?.total ?? 0;
              this.$nextTick(() => {
                if (this.$refs.commandTable && this.$refs.commandTable.doLayout) {
                  this.$refs.commandTable.doLayout();
                }
              });
              let flag = this.validateRefresh();
              if (flag && this.longTimer == null) {
                this.longTimer = setInterval(this.refreshCommandData, 1000 * 10);
              }
              if (!flag) {
                this.clearLongTimer();
              }
            }
            else{
              this.clearLongTimer();
              this.showComTable = false;
              this.comForm.total = 0;
              this.commandSearchErrorMsg = res?.data?.msg ?? '';
            }

          } else {
            this.clearLongTimer();
            this.showComTable = false;
            this.comForm.total = 0;
            this.commandSearchErrorMsg = res.msg;
          }
          this.tableLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.tableLoading = false;
          this.showComTable = false;
          this.commandSearchErrorMsg = error.msg;
          this.comForm.total = 0;
          this.clearLongTimer();
        });
    },

    validateRefresh() {
      let flag = false;
      this.commandData.forEach(item => {
        if (item.inPower == "查询中") {
          //有查询中的就定时
          flag = true;
        }
      });
      return flag;
    },
    handleClick(val) {
      this.historySearchData = [];
      this.hisForm = {
        pageNum: 1,
        pageSize: 5,
        total: 0,
      };
      this.showHisDetail = false;

      this.detailResultData = [];
      this.detailErrorMsg = '';
      this.stopHisTimer();
      this.clearLongTimer();
      this.clearLongManualTimer();
      if (val == "历史查询记录") {
        this.showComTable = false;
        this.getHistorySearchData();
      } else if (val == "指令查询") {
        this.commandSearchErrorMsg = null;
        this.showComTable = false;
        this.resetQueryForm("queryForm");
      }
    },
    //查询历史数据
    getHistorySearchData() {
      let param = {
        sheetNo: this.basicWorkOrderData.sheetNo,
        pageSize: this.hisForm.pageSize,
        pageNum: this.hisForm.pageNum,
      };

      this.historyTableLoading = true;

      apiQueryHistory(param)
        .then(res => {
          if (res.status == "0") {
            this.historySearchData = res?.data?.data ?? [];
            this.hisForm.total = res?.data?.total ?? 0;

            let isRefresh = res?.data?.status == "查询中" ? true : false;
            if (isRefresh && this.hisTimer == null) {
              this.hisTimer = setInterval(
                this.getHistorySearchData,
                1000 * 60 * 2
              ); // 刷新频率，单位是毫秒 2分钟
            }
            if (!isRefresh) {
              this.stopHisTimer();
            }
          } else {
            this.hisForm.total = 0;
            this.stopHisTimer();
          }
          this.historyTableLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.historyTableLoading = false;
          this.stopHisTimer();
        });
    },
    //停止历史记录刷新
    stopHisTimer() {
      if (this.hisTimer) {
        // 如果定时器存在，则清除它
        clearInterval(this.hisTimer);
        this.hisTimer = null; // 将定时器引用设置为null，确保下次不再误用
      }
    },
    clearLongTimer() {
      if (this.longTimer) {
        clearInterval(this.longTimer);
        this.longTimer = null;
      }
    },
    clearLongManualTimer() {
      if (this.longManualTimer) {
        clearInterval(this.longManualTimer);
        this.longManualTimer = null;
      }
    },
    //查看查询历史详情
    searchResultDetail(row) {
      this.showHisDetail = true;
      this.detailResultData = [];
      this.detailErrorMsg ='';
      this.detailForm = {
        pageNum: 1,
        pageSize: 5,
        total: 0,
      };
      this.taskId = row.taskId
      this.queryDetailData();
    },
    //查询历史详情
    queryDetailData() {
      let param = {
        "taskId": this.taskId,
        "pageSize" : this.comForm.pageSize,
        "currentPage" : this.comForm.pageNum
      };
      this.hisDetailLoading = true;
      apiQueryHistoryDetail(param)
        .then(res => {
          if (res.status == "0") {
            this.detailType = res?.data?.queryType==0?'实时光功率查询':'光功率手工查询';
            this.detailTransSegName = res?.data?.queryParam.transSegName ?? "";
            this.detailQueryItem.province =
              res?.data?.queryParam.provinceName ?? "";
            this.detailQueryItem.region =
              res?.data?.queryParam.cityName ?? "";
            this.detailQueryItem.card = res?.data?.queryParam.card ?? "";
            this.detailQueryItem.neName =
              res?.data?.queryParam.neName ?? "";
            this.detailQueryItem.portName =
              res?.data?.queryParam.portName ?? "";

            if (res.data.result == 'success'){
              this.detailErrorMsg ='';
              this.detailResultData = res?.data?.data ?? [];
              this.detailForm.total = res?.data?.total ?? 0;

              this.$nextTick(() => {
                if (this.$refs.detailTable && this.$refs.detailTable.doLayout) {
                  this.$refs.detailTable.doLayout();
                }
              });
            }
            else{
              this.detailErrorMsg = res?.data?.msg ?? '';
              this.detailForm.total = 0;
            }

          }
          else{
            this.detailErrorMsg = res?.msg ?? '';
            this.detailForm.total = 0;
          }
          this.hisDetailLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.detailErrorMsg = error?.msg ?? '';
          this.detailForm.total = 0;
          this.hisDetailLoading = false;
        });
    },
    //关闭查询历史详情
    closeDetail() {
      this.showHisDetail = false;
    },
    selectedFilter(val) {
      this.clearLongTimer();
      this.clearLongManualTimer();
      this.selectedCommand.name = val.name;
      this.commandSearchErrorMsg = '';
      this.showComTable = false;
      this.resetQueryForm("queryForm");
    },
    //历史翻页
    hisPageChange() {
      this.stopHisTimer();
      this.getHistorySearchData();
    },
    //光功率手工查询
    manualQuery() {
      this.$refs.queryForm.validate(valid => {
        if (valid) {
          if (!this.manualQueryClickLock) {
            this.manualQueryClickLock = true;
            setTimeout(() => {
              // 1分钟后，重置manualQueryClickLock状态
              this.manualQueryClickLock = false;
            }, 60000);

            this.tableLoading = true;
            this.showComTable = true;
            let provinceName = this.queryForm.provinceName;
            let systemNameArr = this.neNameOptions.filter(item => {
              return item.value === this.queryForm.neName;
            });
            let portDnArr = this.portNameOptions.filter(item => {
              return item.value === this.queryForm.portName;
            });
            let param = {
              provinceName: provinceName,
              cityName: this.queryForm.cityName,
              neName: this.queryForm.neName,
              cardName: this.queryForm.cardName,
              portName: this.queryForm.portName,
              systemName: systemNameArr?.[0]?.systemName ?? "",
              portDn: portDnArr?.[0]?.portDn ?? "",
              sheetNo: this.basicWorkOrderData.sheetNo,
            };
            apiCommandManualQuery(param)
              .then(res => {
                if (res.status == "0") {
                  this.$message.warning(
                    "您可继续等待或稍后进入历史查询记录中查看结果"
                  );
                  this.taskId = res.data;
                  this.refreshCommandManual();
                  // if (res.data.result == "success") {
                  // } else if (res.data.result == "error") {
                  // }
                } else {
                  this.showComTable = false;
                  this.comForm.total = 0;
                  this.commandSearchErrorMsg = res.msg;
                }
                this.tableLoading = false;
              })
              .catch(error => {
                console.log(error);
                this.showComTable = false;
                this.comForm.total = 0;
                this.commandSearchErrorMsg = error.msg;
                this.tableLoading = false;
              });
          } else {
            this.$message.warning("一分钟内，请勿多次点击！");
          }
        } else {
          return false;
        }
      });
    },
    refreshCommandManual() {
      let param = {
        taskId: this.taskId,
      };
      this.tableLoading = true;
      apiQueryGglInfoTableByTaskId(param)
        .then(res => {
          if (res.status == "0") {

            if (res.data.result == 'success'){
              this.commandData = res?.data?.data ?? [];
              this.comForm.total = res?.data?.total ?? 0;

              this.$nextTick(() => {
                if (this.$refs.commandTable && this.$refs.commandTable.doLayout) {
                  this.$refs.commandTable.doLayout();
                }
              });
              let flag = this.validateRefresh();
              if (flag && this.longManualTimer == null) {
                this.longManualTimer = setInterval(
                  this.refreshCommandManual,
                  1000 * 10
                );
              }
              if (!flag) {
                this.clearLongManualTimer();
              }
            }
            else{
              this.clearLongManualTimer();
              this.showComTable = false;
              this.comForm.total = 0;
              this.commandSearchErrorMsg = res?.data?.msg ?? '';
            }


          } else {
            this.clearLongManualTimer();
            this.showComTable = false;
            this.comForm.total = 0;
            this.commandSearchErrorMsg = res.msg;
          }
          this.tableLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.tableLoading = false;
          this.showComTable = false;
          this.comForm.total = 0;
          this.commandSearchErrorMsg = error.msg;
          this.clearLongManualTimer();
        });
    },
    resetQueryForm(formName) {
      this.$refs[formName].resetFields();
      this.queryForm = {
        provinceName: this.transRelationData.provinceName,
        cityName: this.transRelationData.regionName,
        neName: "",
        cardName: "",
        portName: "",
        portDn: "",
        systemName: "",
      };
      this.comForm = {
        pageNum: 1,
        pageSize: 5,
        total: 0
      };
    },
    // getProvinceOptions() {
    //   let param = {
    //     areaCode: "1003315",
    //   };
    //   apiArea(param)
    //     .then(res => {
    //       if (res.status == "0") {
    //         this.provinceOptions = res?.data ?? [];
    //       }
    //     })
    //     .catch(error => {
    //       console.log(error);
    //     });
    // },

    // changeProvince(provinceCode) {
    //   let code = provinceCode;
    //   //获取网元下拉数据
    //   this.getNeOptions();
    //   //获取地市下拉数据
    //   this.getRegionOptions(code);
    //   //获取板卡下拉数据
    //   this.getCardOptions();
    // },
    changeRegion(val) {

      this.regionOptions.forEach(item=>{
        if (item.areaName == val){
          this.cityCode = item.areaCode;
        }
      })
      //获取网元下拉数据
      this.getNeOptions();
      // //获取板卡下拉数据
      // this.getCardOptions();
    },
    getRegionOptions() {
      this.queryForm.cityName = "";
      let params = {
        areaCode: this.provinceCode,
      };
      return new Promise(resolve => {
        apiArea(params).then(res => {
          if (res.status == "0") {
            this.regionOptions = res?.data ?? [];
            this.queryForm.cityName = this.transRelationData.regionName;
            this.getNeOptions();
          }
          resolve("success");
        });
      });
    },
    getNeOptions() {
      this.queryForm.neName = "";
      // let name = this.queryForm.provinceName;
      let params = {
        province: this.provinceCode,
        region: this.cityCode,
      };
      return new Promise(resolve => {
        apiNeOptions(params).then(res => {
          if (res.status == "0") {
            this.neNameOptions = res?.data ?? [];
          }
          resolve("success");
        });
      });
    },
    getCardOptions() {
      this.queryForm.cardName = "";
      let param = {
        province: this.provinceCode,
        // busiType: 1,
        region: this.cityCode,
        eqpName: this.queryForm.neName,
      };
      return new Promise(resolve => {
        apiQueryCardOrPortInfo(param).then(res => {
          if (res.status == "0") {
            this.cardNameOptions = res?.data ?? [];
          }
          resolve("success");
        });
      });
    },
    getPortOptions(cardName) {
      //获取端口下拉数据
      this.queryForm.portName = "";
      let params = {
        province: this.provinceCode,
        // busiType: 1,
        cardName: cardName,
        region: this.cityCode,
        eqpName: this.queryForm.neName,
      };
      apiQueryCardOrPortInfo(params).then(res => {
        if (res.status == "0") {
          this.portNameOptions = res?.data ?? [];
        }
      });
    },
    changeNeName() {
      this.getCardOptions();
    },
  },

  beforeDestroy() {
    this.stopHisTimer();
    this.clearLongTimer();
    this.clearLongManualTimer();
  },
  destroyed() {
    this.stopHisTimer();
    this.clearLongTimer();
    this.clearLongManualTimer();
  },
};
</script>
<style>
.custom-theme-default .el-message--warning .el-message__content {
  color: #000;
}
</style>
<style lang="scss" scoped>
@import "../../modules/commonProvince/workOrderWaitDetail/assets/common.scss";

::v-deep .el-collapse-item {
  .el-collapse-item__header {
    background-color: #fafafa;
    padding-left: 20px;
  }
}

::v-deep .el-table .cell {
  display: flex !important;
  align-items: center;
  justify-content: center;
}

::v-deep .el-table .cell.el-tooltip {
  white-space: normal !important;
  min-width: 50px;
}

.demo-form-inline {
  margin-right: 20px;

  .el-button {
    height: 28px;
  }
  ::v-deep .el-input--small .el-input__inner {
    height: 30px;
    line-height: 30px;
    font-size: 13px;
  }
}

.tipCla {
  margin: 10px 20px 0 30px;
  border-top: 1px solid #c2c2c2;

  padding-top: 10px;
}
.btnDiv {
  display: flex;
  align-items: center;

  img {
    width: 12px;
    height: 12px;
    margin-right: 4px;
  }
}
.filter-total-item {
  display: inline-block;
  padding: 2px 8px;
  font-size: 13px;
  margin-left: 20px;
  margin-top: 16px;
  cursor: pointer;
  background-color: #f8e8e8;
  border: 1px solid #e6e6e6;
  border-radius: 2px;
  // .span-optical {
  //   padding: 2px 8px;
  //   background-color: #f8e8e8;
  //   border: 1px solid #e6e6e6;
  //   border-radius: 2px;
  //   cursor: pointer;
  // }
  span {
    padding-top: 1px;
  }
  &.active {
    border-radius: 2px;
    border: 0px;
    @include themify() {
      background-color: themed("$--color-primary");
      color: themed("$--color-white");
    }
  }
}

.detailCard {
  border: none !important;

  .el-button:focus,
  .el-button:hover {
    color: #b50b14;
    /*border-color: #e9b6b9;*/
    background-color: #ffffff;
  }

  ::v-deep .el-descriptions-item__label{
    flex-shrink: 0 !important;
  }
}

hr {
  background-color: #c2c2c2;
  margin: 0px 30px;
  border: none;
  height: 1px;
}

.header-right {
  .el-button--mini {
    font-size: 18px !important;
  }
  ::v-deep .el-icon-arrow-left {
    font-weight: 800;
  }
}

::v-deep .el-form-item__label {
  font-size: 13px !important;
}

::v-deep .el-form-item__content {
  font-size: 13px !important;
}

::v-deep .el-collapse-item__content {
  font-size: 13px !important;
  padding-bottom: 10px;
}
.op-btn ::v-deep .el-button--small {
  padding: 0 15px;
}
.op-btn .el-button {
  line-height: 26px;
}

.viewBtn {
  text-decoration: underline;
  color: #b50b14;
}

.header-title {
  font-weight: 600;
  line-height: 30px;
}
</style>
