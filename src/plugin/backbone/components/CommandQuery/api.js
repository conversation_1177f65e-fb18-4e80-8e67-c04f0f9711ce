import { postJson } from "@/utils/axios";

const commandQueryUrl = "integration/command/query"; // 指令查询
const refreshCommandQueryUrl = "integration/command/queryRefresh"; // 刷新指令查询
const queryHistoryUrl = "integration/command/queryHistory";//指令历史查询

const apiCommandQuery = params =>
  postJson(commandQueryUrl, params, { timeout: 0 });
const apiRefreshCommandQuery = params =>
  postJson(refreshCommandQueryUrl, params, { timeout: 20000 });
const apiQueryHistory = params =>
  postJson(queryHistoryUrl, params, { timeout: 0 });
export {
  apiCommandQuery,
  apiQueryHistory,
  apiRefreshCommandQuery
};
