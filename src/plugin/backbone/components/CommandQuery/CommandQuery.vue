<template>
  <div>
    <el-collapse v-model="activeNames">
      <el-collapse-item name="1">

        <div class="collapse-title" slot="title">
          <span style="margin-right: 10px">状态查询</span>

          <div style="display: inline-block">
            <el-radio-group
              v-model="radio1"
              @change="handleClick"
              size="mini"
            >
              <el-radio-button label="指令查询"></el-radio-button>
              <el-radio-button label="历史查询记录"></el-radio-button>
            </el-radio-group>
          </div>
        </div>

        <div v-if="radio1 == '指令查询'">

          <!--指令按钮-->
          <template v-for="(item, i) in commandBtns">
            <div class="filter-total-item" :class="{ active: selectedCommand.name == item.name }" v-if="!item.hide">
              <div class="btnDiv">
                <img :src="commandIcons[item.id - 1]" v-if="selectedCommand.name != item.name" alt="">
                <img :src="commandIconsSelected[item.id - 1]" v-if="selectedCommand.name == item.name" alt="">
                <span
                  :key="i"
                  @click="onClickCommand(item)">
                  {{ item.name }}
                </span>
              </div>
            </div>
          </template>

          <!--查询条件-->
          <template>
            <el-form
              label-width="110px"
              :inline="false"
              class="demo-form-inline"
              label-position="right"
              :model="form"
              ref="searchForm"
            >
              <el-row
                type="flex"
                style="flex-wrap: wrap"
                :gutter="20"
              >
                <el-col :span="8">
                  <el-form-item
                    label="网元名称:"
                    prop="NeName"
                    :rules="{
                      required: true,
                      message: '请输入',
                      trigger: 'blur'
                    }">
                    <el-input
                      v-model.trim="form.NeName"
                      placeholder="请输入内容"
                      clearable
                      style="width: 100%"
                      maxlength="255"
                      @keyup.native="descTip(255,'NeName','showwymc')"
                    ></el-input>
                    <div class="el-form-item__error"  v-if="showwymc">已超过填写字数上限</div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item
                    label="设备ID:"
                    prop="Omc"
                    :rules="{
                      required: true,
                      message: '请输入',
                      trigger: 'blur'
                    }">
                    <el-input
                      v-model.trim="form.Omc"
                      placeholder="请输入内容"
                      clearable
                      style="width: 100%"
                      maxlength="255"
                      @keyup.native="descTip(255,'Omc','showsbid')"
                    ></el-input>
                    <div class="el-form-item__error"  v-if="showsbid">已超过填写字数上限</div>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item
                    prop="Vendor"
                    :rules="{
                      required: true,
                      message: '请选择',
                      trigger: 'blur'
                    }">
                      <span slot="label" style="display:inline-block;">
                        <el-tooltip effect="dark" :content="csTip" placement="bottom">
                          <em class='el-icon-question' style="color:#b50b14 "></em>
                        </el-tooltip>
                        厂商:
                      </span>
                    <el-select
                      v-model="form.Vendor"
                      placeholder="请选择内容"
                      style="width: 100%"
                      @change="formClearValidate('searchForm')"
                    >
                      <el-option label="华为" value="华为"></el-option>
                      <el-option label="中兴" value="中兴" v-if="selectedCommand.name!= '风扇转速温度'"></el-option>
                      <el-option label="爱立信" value="爱立信"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <template v-if="selectedCommand.name == '小区状态'">

                  <el-col :span="8">
                    <el-form-item label="小区名称:" prop="CellName">
                      <el-input
                        v-model.trim="form.CellName"
                        placeholder="请输入内容"
                        clearable
                        style="width: 100%"
                        maxlength="255"
                        @keyup.native="descTip(255,'CellName','showxqmc')"
                      ></el-input>
                      <div class="el-form-item__error"  v-if="showxqmc">已超过填写字数上限</div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" v-if="form.networkType != '5G'">
                    <el-form-item label="小区id:"
                      prop="EUTRANCELLID"
                      :rules="[{
                        required: form.Vendor =='中兴' && form.networkType == '4G' ? true : false,
                        message: '请输入',
                        trigger: 'blur'
                      }]">
                      <el-input
                        v-model.trim="form.EUTRANCELLID"
                        placeholder="请输入内容"
                        clearable
                        style="width: 100%"
                        maxlength="255"
                        @keyup.native="descTip(255,'EUTRANCELLID','showxqid')"
                      ></el-input>
                      <div class="el-form-item__error"  v-if="showxqid">已超过填写字数上限</div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item
                      label="网络类型:"
                      prop="networkType"
                      :rules="{
                        required: true,
                        message: '请选择',
                        trigger: 'blur'
                      }">
                      <span slot="label" style="display:inline-block;">
                          <el-tooltip effect="dark" :content="wllxTip" placement="bottom">
                            <em class='el-icon-question' style="color:#b50b14 "></em>
                          </el-tooltip>
                          网络类型:
                      </span>
                      <el-select
                        v-model="form.networkType"
                        placeholder="请选择内容"
                        style="width: 100%"
                        @change="formClearValidate('searchForm')"
                      >
                        <el-option label="4G" value="4G"></el-option>
                        <el-option label="5G" value="5G"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="告警子网:"
                    prop="SBN"
                    :rules="[{
                      required: form.Vendor=='中兴' ? true : false,
                      message: '请输入',
                      trigger: 'blur'
                    }]">
                      <el-input
                        v-model.trim="form.SBN"
                        placeholder="请输入内容"
                        clearable
                        style="width: 100%"
                        maxlength="255"
                        @keyup.native="descTip(255,'SBN','showgjzw')"
                      ></el-input>
                      <div class="el-form-item__error"  v-if="showgjzw">已超过填写字数上限</div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" v-if="form.networkType=='5G'">
                    <el-form-item
                      label="小区类型:"
                      prop="CellType"
                      :rules="[
                        {
                          required: form.networkType=='5G' ? true : false,
                          message: '请选择',
                          trigger: 'blur'
                        },
                    ]">
                      <el-select
                        v-model="form.CellType"
                        placeholder="请选择内容"
                        style="width: 100%"
                      >
                        <el-option label="CU" value="CU"></el-option>
                        <el-option label="DU" value="DU"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </template>

                <template v-if="selectedCommand.name == '光模块功率' || selectedCommand.name == '驻波比'">
                  <el-col :span="8">
                    <el-form-item label="告警子网:"
                      prop="SBN"
                      :rules="[{
                        required: form.Vendor=='中兴' ? true : false,
                        message: '请输入',
                        trigger: 'blur'
                      }]">
                      <el-input
                        v-model.trim="form.SBN"
                        placeholder="请输入内容"
                        clearable
                        style="width: 100%"
                        maxlength="255"
                        @keyup.native="descTip(255,'SBN','showgjzw')"
                      ></el-input>
                      <div class="el-form-item__error"  v-if="showgjzw">已超过填写字数上限</div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="框:"
                      prop="SRN"
                      :rules="[{
                        required: form.Vendor=='中兴' ? true : false,
                        message: '请输入',
                        trigger: 'blur'
                      }]">
                      <el-input
                        v-model.trim="form.SRN"
                        placeholder="请输入内容"
                        clearable
                        style="width: 100%"
                        maxlength="255"
                        @keyup.native="descTip(255,'SRN','showkuang')"
                      ></el-input>
                      <div class="el-form-item__error"  v-if="showkuang">已超过填写字数上限</div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="槽:"
                      prop="SN"
                      :rules="[{
                        required: form.Vendor=='中兴' ? true : false,
                        message: '请输入',
                        trigger: 'blur'
                      }]">
                      <el-input
                        v-model.trim="form.SN"
                        placeholder="请输入内容"
                        clearable
                        style="width: 100%"
                        maxlength="255"
                        @keyup.native="descTip(255,'SN','showcao')"
                      ></el-input>
                      <div class="el-form-item__error"  v-if="showcao">已超过填写字数上限</div>
                    </el-form-item>
                  </el-col>
                </template>

                <template v-if="selectedCommand.name == '历史告警'">
                  <el-col :span="8">
                    <el-form-item
                      label="网络类型:"
                      prop="networkType"
                      :rules="[
                        {
                          required: form.Vendor=='中兴' ? true : false,
                          message: '请选择',
                          trigger: 'blur'
                        }]">
                      <span slot="label" style="display:inline-block;">
                          <el-tooltip effect="dark" :content="wllxTip" placement="bottom">
                            <em class='el-icon-question' style="color:#b50b14 "></em>
                          </el-tooltip>
                          网络类型:
                      </span>
                      <el-select
                        v-model="form.networkType"
                        placeholder="请选择内容"
                        style="width: 100%"
                      >
                        <el-option label="4G" value="4G"></el-option>
                        <el-option label="5G" value="5G"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="告警子网:"
                      prop="SBN"
                      :rules="[{
                        required: form.Vendor == '中兴' ? true : false,
                        message: '请输入',
                        trigger: 'blur'
                      }]">
                      <el-input
                        v-model.trim="form.SBN"
                        placeholder="请输入内容"
                        clearable
                        style="width: 100%"
                        maxlength="255"
                        @keyup.native="descTip(255,'SBN','showgjzw')"
                      ></el-input>
                      <div class="el-form-item__error"  v-if="showgjzw">已超过填写字数上限</div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="告警发生时间:">
                      <el-date-picker
                        v-model="form.alarmCreateTimeRange"
                        type="datetimerange"
                        range-separator="-"
                        :start-placeholder="startTime"
                        :end-placeholder="endTime"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        :default-time="['00:00:00', '23:59:59']"
                        style="width: 100%"
                      >
                      </el-date-picker>
                    </el-form-item>
                  </el-col>

                </template>

                <template v-if="selectedCommand.name == '活动告警'">
                  <el-col :span="8">
                    <el-form-item label="告警子网:"
                      prop="SBN"
                      :rules="[{
                        required: form.Vendor=='中兴' ? true : false,
                        message: '请输入',
                        trigger: 'blur'
                      }]">
                      <el-input
                        v-model.trim="form.SBN"
                        placeholder="请输入内容"
                        clearable
                        style="width: 100%"
                        maxlength="255"
                        @keyup.native="descTip(255,'SBN','showgjzw')"
                      ></el-input>
                      <div class="el-form-item__error"  v-if="showgjzw">已超过填写字数上限</div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item
                      prop="networkType"
                      :rules="{
                        required: true,
                        message: '请选择',
                        trigger: 'blur'
                      }">
                      <span slot="label" style="display:inline-block;">
                          <el-tooltip effect="dark" :content="wllxTip" placement="bottom">
                            <em class='el-icon-question' style="color:#b50b14 "></em>
                          </el-tooltip>
                          网络类型:
                      </span>
                      <el-select
                        v-model="form.networkType"
                        placeholder="请选择内容"
                        style="width: 100%"
                      >
                        <el-option label="4G" value="4G"></el-option>
                        <el-option label="5G" value="5G"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>

                </template>

                <template v-if="selectedCommand.name == '单板状态'">
                  <el-col :span="8">
                    <el-form-item label="告警子网:"
                      prop="SBN"
                      :rules="[{
                        required: form.Vendor=='中兴' ? true : false,
                        message: '请输入',
                        trigger: 'blur'
                      }]">
                      <el-input
                        v-model.trim="form.SBN"
                        placeholder="请输入内容"
                        clearable
                        style="width: 100%"
                        maxlength="255"
                        @keyup.native="descTip(255,'SBN','showgjzw')"
                      ></el-input>
                      <div class="el-form-item__error"  v-if="showgjzw">已超过填写字数上限</div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="框号:"
                      prop="SRN"
                      :rules="[{
                        required: form.Vendor=='中兴' ? true : false,
                        message: '请输入',
                        trigger: 'blur'
                      }]">
                      <el-input
                        v-model.trim="form.SRN"
                        placeholder="请输入内容"
                        clearable
                        style="width: 100%"
                        maxlength="255"
                        @keyup.native="descTip(255,'SRN','showkuang')"
                      ></el-input>
                      <div class="el-form-item__error"  v-if="showkuang">已超过填写字数上限</div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="槽号:"
                      prop="SN"
                      :rules="[{
                        required: form.Vendor=='中兴' ? true : false,
                        message: '请输入',
                        trigger: 'blur'
                      }]">
                      <el-input
                        v-model.trim="form.SN"
                        placeholder="请输入内容"
                        clearable
                        style="width: 100%"
                        maxlength="255"
                        @keyup.native="descTip(255,'SN','showcao')"
                      ></el-input>
                      <div class="el-form-item__error"  v-if="showcao">已超过填写字数上限</div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="柜号:"
                      prop="CN"
                      :rules="[{
                        required: form.Vendor=='中兴' ? true : false,
                        message: '请输入',
                        trigger: 'blur'
                      }]">
                      <el-input
                        v-model.trim="form.CN"
                        placeholder="请输入内容"
                        clearable
                        style="width: 100%"
                        maxlength="255"
                        @keyup.native="descTip(255,'CN','showgui')"
                      ></el-input>
                      <div class="el-form-item__error"  v-if="showcao">已超过填写字数上限</div>
                    </el-form-item>
                  </el-col>
                </template>

                <template v-if="selectedCommand.name == 'GPS状态'">
                  <el-col :span="8">
                    <el-form-item
                      label="设备类型:"
                      prop="DeviceType"
                      :rules="[
                        {
                          required: form.Vendor=='中兴' ? true : false,
                          message: '请选择',
                          trigger: 'blur',
                        },
                    ]">
                      <el-select
                        v-model="form.DeviceType"
                        placeholder="请选择内容"
                        style="width: 100%"
                      >
                        <el-option label="4G" value="4G"></el-option>
                        <el-option label="5G" value="5G"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item
                      label="告警子网:"
                      prop="SBN"
                      :rules="[
                        {
                          required: form.Vendor=='中兴' ? true : false,
                          message: '请输入',
                          trigger: 'blur',
                        },
                    ]">
                      <el-input
                        v-model.trim="form.SBN"
                        placeholder="请输入内容"
                        clearable
                        style="width: 100%"
                        maxlength="255"
                        @keyup.native="descTip(255,'SBN','showgjzw')"
                      ></el-input>
                      <div class="el-form-item__error"  v-if="showgjzw">已超过填写字数上限</div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item
                      label="LDN:"
                      prop="LDN"
                      :rules="[
                        {
                          required: form.Vendor=='中兴' ? true : false,
                          message: '请输入',
                          trigger: 'blur',
                        },
                    ]">
                      <el-input
                        v-model.trim="form.LDN"
                        placeholder="请输入内容"
                        clearable
                        style="width: 100%"
                        maxlength="255"
                        @keyup.native="descTip(255,'LDN','showldn')"
                      ></el-input>
                      <div class="el-form-item__error"  v-if="showldn">已超过填写字数上限</div>
                    </el-form-item>
                  </el-col>
                </template>

                <el-col :span="24" class="op-btn" style="text-align: right; padding-right: 10px; margin-bottom:2px">

                  <el-button type="primary" @click="seniorQuery">查询</el-button>
                  <el-button type="primary" @click="onResetForm">重置</el-button>

                </el-col>
              </el-row>
            </el-form>
          </template>

          <div class="tipCla" v-if="showComTable || commandSearchErrorMsg!=''">查询结果</div>
          <!--错误提示-->
          <div v-if="commandSearchErrorMsg!='' && !showComTable" style="margin-left: 20px">
            <span style="color: #b50b14">【查询失败】</span>{{commandSearchErrorMsg}}
          </div>

          <!--列表-->
          <div v-if="showComTable" class="tableHHHH" style="margin-left: 16px;margin-right: 16px;margin-top: 10px">
            <el-table
              ref="commandTable"
              :data="commandData"
              style="width: 100%"
              border
              v-loading="tableLoading"
              v-if="selectedCommand.name == '小区状态'"
            >
              <el-table-column
                show-overflow-tooltip
                type="index"
                label="序号"
                width="60px">
              </el-table-column>
              <template v-if="tempNetworkType=='4G' && tempVendor=='华为'">
                <el-table-column
                  show-overflow-tooltip
                  prop="statusChangeReason"
                  label="最近一次状态变化的原因"
                  min-width="200px"
                >
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="communityName"
                  label="小区名称"
                  min-width="380px">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="communityInstStatus"
                  label="小区的实例状态"
                  min-width="140px">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="comAvaToUnTime"
                  label="小区从可用变为不可用时间"
                  min-width="210px">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="comUnOperation"
                  label="最近一次引起小区不可用的操作类型"
                  min-width="260px">
                </el-table-column>
              </template>
              <template v-if="tempNetworkType=='4G' && tempVendor=='爱立信'">
                <el-table-column
                  show-overflow-tooltip
                  prop="communityName"
                  label="小区名称"
                  min-width="380px">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="comAvaStatus"
                  label="小区可用状态"
                  min-width="140px">
                </el-table-column>
              </template>
              <template v-if="tempNetworkType=='4G' && tempVendor=='中兴'">
                <el-table-column
                  show-overflow-tooltip
                  prop="communityId"
                  label="小区ID"
                  min-width="140px">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="comAvaStatus"
                  label="小区可用状态"
                  min-width="140px">
                </el-table-column>
              </template>
              <template v-if="tempNetworkType=='5G' && tempCellType=='DU'">
                <el-table-column
                  show-overflow-tooltip
                  prop="netElementName"
                  label="网元名称"
                  min-width="150px">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="duCommunityName"
                  label="DU小区名称"
                  min-width="380px">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="duComAvaStatus"
                  label="DU小区可用状态"
                  min-width="150px">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="statusChangeReason"
                  label="最近一次状态变化的原因"
                  min-width="200px">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="comAvaToUnTime"
                  label="小区从可用变为不可用时间"
                  min-width="210px">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="comUnOperation"
                  label="最近一次引起小区不可用的操作类型"
                  min-width="260px">
                </el-table-column>
              </template>
              <template v-if="tempNetworkType=='5G' && tempCellType=='CU' && tempVendor=='华为'">
                <el-table-column
                  show-overflow-tooltip
                  prop="netElementName"
                  label="网元名称"
                  min-width="150px">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="cuCommunityName"
                  label="CU小区名称"
                  min-width="380px">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="cuComAvaStatus"
                  label="CU小区可用状态"
                  min-width="150px">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="statusChangeReason"
                  label="最近一次状态变化的原因"
                  min-width="200px">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="comAvaToUnTime"
                  label="小区从可用变为不可用时间"
                  min-width="210px">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="comUnOperation"
                  label="最近一次引起小区不可用的操作类型"
                  min-width="260px">
                </el-table-column>
              </template>
              <template v-if="tempNetworkType=='5G' && tempCellType=='CU' && tempVendor=='爱立信'">
                <el-table-column
                  show-overflow-tooltip
                  prop="netElementName"
                  label="网元名称"
                  min-width="150px">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="cuCommunityName"
                  label="CU小区名称"
                  min-width="380px">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="cuComAvaStatus"
                  label="CU小区可用状态"
                  min-width="150px">
                </el-table-column>
              </template>
              <template v-if="tempNetworkType=='5G' && tempCellType=='CU' && tempVendor=='中兴'">
                <el-table-column
                  show-overflow-tooltip
                  prop="lastUpdateTime"
                  label="上次修改时间"
                  min-width="160px">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="netElementType"
                  label="网元类型"
                  min-width="150px">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="serviceStatus"
                  label="服务状态"
                  min-width="130px">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="ldn"
                  label="ldn"
                  min-width="100px">
                </el-table-column>
              </template>
            </el-table>
            <el-table
              ref="commandTable"
              :data="commandData"
              style="width: 100%"
              border
              v-loading="tableLoading"
              v-if="selectedCommand.name == '光模块功率'"
            >
              <el-table-column
                type="index"
                label="序号"
                width="60px">
              </el-table-column>
              <template v-if="tempVendor=='华为'">
                <el-table-column
                  show-overflow-tooltip
                  prop="placeBar"
                  label="所在柜"
                  min-width="90px">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="placeSRN"
                  label="所在框"
                  min-width="90px">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="placeSN"
                  label="所在槽"
                  min-width="90px">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="portType"
                  label="端口类型"
                  min-width="100px">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="placePort"
                  label="所在端口号"
                  min-width="110px">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="beatPort"
                  label="在位状态"
                  min-width="100px">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="opticalModuleType"
                  label="光模块类型"
                  min-width="110px">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="opticalModuleVendor"
                  label="光模块厂家"
                  min-width="110px">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="transferMode"
                  label="传输模式"
                  min-width="100px">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="sendOpticalPower"
                  label="发送光功率（0.01dBm）"
                  min-width="200px">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="reciveOpticalPower"
                  label="接收光功率（0.01dBm）"
                  min-width="200px">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="RRUName"
                  label="RRU名称"
                  min-width="250px">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="localCommunityId"
                  label="本地小区标识"
                  min-width="125px">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="RFUnitInformation"
                  label="射频单元信息"
                  min-width="125px">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="serviceBoardId"
                  label="服务基带板主辅标识"
                  min-width="170px">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="transferCodeRate"
                  label="传输码速率"
                  min-width="110px">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="ofTransferDistance"
                  label="单模光纤的传输距离（100米）"
                  min-width="230px">
                </el-table-column>
              </template>
              <template v-if="tempVendor=='中兴'">
                <el-table-column
                  show-overflow-tooltip
                  prop="portId"
                  label="端口ID">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="sendPower"
                  label="接收功率">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="recivePower"
                  label="发送功率">
                </el-table-column>
              </template>
              <template v-if="tempVendor=='爱立信'">
                <el-table-column
                  show-overflow-tooltip
                  prop="linkId"
                  label="链路ID">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="sendOpticalPower1"
                  label="发送光功率1">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="reciveOpticalPower1"
                  label="接收光功率1">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="sendOpticalPower2"
                  label="发送光功率2">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="reciveOpticalPower2"
                  label="接收光功率2">
                </el-table-column>
              </template>
            </el-table>
            <el-table
              ref="commandTable"
              :data="commandData"
              style="width: 100%"
              border
              v-loading="tableLoading"
              v-if="selectedCommand.name == '驻波比'"
            >
              <el-table-column
                type="index"
                label="序号"
                width="60px">
              </el-table-column>
              <template v-if="tempVendor=='华为'">
                <el-table-column
                  show-overflow-tooltip
                  prop="BAR"
                  label="柜"
                  min-width="90px">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="SRN"
                  label="框"
                  min-width="90px">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="SN"
                  label="槽"
                  min-width="90px">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="tranChannelNo"
                  label="发射通道号"
                  min-width="110px">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="radioPort"
                  label="射频端口"
                  min-width="100px">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="standingWaveRatio"
                  label="驻波比（0.01）"
                  min-width="140px">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="RRUName"
                  label="RRU名称"
                  min-width="250px">
                </el-table-column>
              </template>
              <template v-if="tempVendor=='爱立信'">
                <el-table-column
                  show-overflow-tooltip
                  prop="boardName"
                  label="单板名称">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="RRUName"
                  label="RRU名称"
                  min-width="250px">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="radioPort"
                  label="射频端口">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="standingWaveRatio"
                  label="驻波比（0.01）">
                </el-table-column>
              </template>
              <template v-if="tempVendor=='中兴'">
                <el-table-column
                  prop="number"
                  label="编号">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="band"
                  label="频带">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="standingWaveRatio"
                  label="驻波比">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="alarmStatus"
                  label="告警状态">
                </el-table-column>
              </template>
            </el-table>
            <el-table
              ref="commandTable"
              :data="commandData"
              style="width: 100%"
              border
              v-loading="tableLoading"
              v-if="selectedCommand.name == '历史告警'"
            >
              <el-table-column
                type="index"
                label="序号"
                width="60px">
              </el-table-column>
              <template v-if="tempNetworkType=='4G' && tempVendor=='中兴'">
                <el-table-column
                  show-overflow-tooltip
                  prop="equipmentName"
                  label="设备名称"
                  min-width="160px">
                </el-table-column>
              </template>
              <el-table-column
                show-overflow-tooltip
                prop="alarmTitle"
                label="告警标题"
                min-width="160px">
              </el-table-column>
              <el-table-column
                show-overflow-tooltip
                prop="alarmCreateTime"
                label="告警发生时间"
                min-width="160px">
              </el-table-column>
              <template v-if="tempVendor=='中兴'">
                <el-table-column
                  show-overflow-tooltip
                  prop="alarmRecoverTime"
                  label="告警恢复时间"
                  min-width="160px">
                </el-table-column>
              </template>
              <el-table-column
                show-overflow-tooltip
                prop="positionInfo"
                label="定位信息"
                min-width="900px">
              </el-table-column>
              <template v-if="tempVendor=='华为'">
                <el-table-column
                  show-overflow-tooltip
                  prop="alarmClearTime"
                  label="告警清除时间"
                  min-width="160px">
                </el-table-column>
              </template>
            </el-table>
            <el-table
              ref="commandTable"
              :data="commandData"
              style="width: 100%"
              border
              v-loading="tableLoading"
              v-if="selectedCommand.name == '活动告警'"
            >
              <el-table-column
                type="index"
                label="序号"
                width="60px">
              </el-table-column>
              <template v-if="tempNetworkType=='4G' && tempVendor=='中兴'">
                <el-table-column
                  show-overflow-tooltip
                  prop="equipmentName"
                  label="设备名称"
                  min-width="160px">
                </el-table-column>
              </template>
              <el-table-column
                show-overflow-tooltip
                prop="alarmTitle"
                label="告警标题"
                min-width="160px">
              </el-table-column>
              <el-table-column
                show-overflow-tooltip
                prop="alarmCreateTime"
                label="告警发生时间"
                min-width="160px">
              </el-table-column>
              <el-table-column
                show-overflow-tooltip
                prop="positionInfo"
                label="定位信息"
                min-width="900px">
              </el-table-column>
            </el-table>
            <el-table
              ref="commandTable"
              :data="commandData"
              style="width: 100%"
              border
              v-loading="tableLoading"
              v-if="selectedCommand.name == '单板状态'"
            >
              <el-table-column
                type="index"
                label="序号"
                width="60px"
              >
              </el-table-column>
              <el-table-column
                show-overflow-tooltip
                prop="placeBar"
                label="所在柜">
              </el-table-column>
              <el-table-column
                show-overflow-tooltip
                prop="placeSRN"
                label="所在框">
              </el-table-column>
              <el-table-column
                show-overflow-tooltip
                prop="placeSN"
                label="所在槽">
              </el-table-column>
              <template v-if="tempVendor=='华为' || tempVendor=='爱立信'">
                <el-table-column
                  show-overflow-tooltip
                  prop="boardType"
                  label="单板类型">
                </el-table-column>
              </template>
              <el-table-column
                show-overflow-tooltip
                prop="boardAvaStatus"
                label="单板可用状态">
              </el-table-column>
              <template v-if="tempVendor=='华为' || tempVendor=='爱立信'">
                <el-table-column
                  show-overflow-tooltip
                  prop="RRUName"
                  label="RRU名称"
                  min-width="250px">
                </el-table-column>
              </template>
            </el-table>
            <el-table
              ref="commandTable"
              :data="commandData"
              style="width: 100%"
              border
              v-loading="tableLoading"
              v-if="selectedCommand.name == '风扇转速温度'"
            >
              <el-table-column
                type="index"
                label="序号"
                width="60px">
              </el-table-column>
              <template v-if="tempVendor=='华为'">
                <el-table-column
                  show-overflow-tooltip
                  prop="speedRatio"
                  label="转速比">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="temperature"
                  label="温度">
                </el-table-column>
              </template>
              <template v-if="tempVendor=='爱立信'">
                <el-table-column
                  show-overflow-tooltip
                  prop="temperature"
                  label="温度">
                </el-table-column>
              </template>
            </el-table>
            <el-table
              ref="commandTable"
              :data="commandData"
              style="width: 100%"
              border
              v-loading="tableLoading"
              v-if="selectedCommand.name == 'GPS状态'"
            >
              <el-table-column
                type="index"
                label="序号"
                width="60px">
              </el-table-column>
              <template v-if="tempVendor=='华为' || tempVendor=='爱立信'">
                <el-table-column
                  show-overflow-tooltip
                  prop="clockNo"
                  label="GPS/GNSS时钟编号">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="status"
                  label="状态">
                </el-table-column>
              </template>
              <template v-if="tempVendor=='中兴' || tempDeviceType=='5G'">

                <el-table-column
                  show-overflow-tooltip
                  prop="PPSStatus"
                  label="PPS状态">
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  prop="initStatus"
                  label="接收机初始化状态">
                </el-table-column>
              </template>
            </el-table>
            <pagination
              ref="pagination1"
              :total="comForm.total"
              :page.sync="comForm.pageNum"
              :limit.sync="comForm.pageSize"
              layout="->, total, sizes, prev, pager, next"
              @change="refreshCommandData()"
            />
          </div>
        </div>

        <div v-else>
          <template v-if="!showHisDetail">
            <div style="margin-left: 16px;margin-bottom: 12px;margin-top: 12px"><span style="color: #b50b14">提醒：</span>仅支持查询近7天的指令查询历史记录。</div>
            <!--查询记录-->
            <template>
              <el-table
                :data="historySearchData"
                stripe
                ref="hisTable"
                v-loading="tableLoading"
                style="width: 100%">
                <el-table-column
                  type="index"
                  label="序号"
                  width="80">
                </el-table-column>
                <el-table-column
                  label="指令名称">
                  <template slot-scope="scope">
                    {{ filterCommandFn(scope.row)}}
                  </template>
                </el-table-column>
                <el-table-column
                  prop="call_time"
                  label="操作时间">
                </el-table-column>
                <el-table-column
                  label="查询状态"
                  width="180">
                  <template slot-scope="scope">
                    <span v-if="scope.row.is_ok == '1'">查询成功</span>
                    <span v-if="scope.row.is_ok == '0'" style="color: #3a8ee6">查询失败</span>
                    <span v-if="scope.row.is_ok == '2'">查询中</span>
                  </template>
                </el-table-column>
                <el-table-column
                  label="查询结果"
                  width="180">
                  <template slot-scope="scope">
                    <span v-if="scope.row.is_ok == '2'">查询中</span>
                    <el-button v-if="scope.row.is_ok != '2'"
                               @click="searchResultDetail(scope.row)"
                               class="viewBtn"
                               type="text"
                               size="small">查看详情</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <pagination
                ref="pagination2"
                :total="hisForm.total"
                :page.sync="hisForm.pageNum"
                :limit.sync="hisForm.pageSize"
                layout="->, total, sizes, prev, pager, next"
                @change="hisPageChange()"
              />
            </template>
          </template>
          <template v-else>
            <!-- 查询详情-->
            <el-card
              shadow="never"
              class="detailCard"
              :body-style="{ padding: '10px 24px'}">
              <div class="header clearfix">
                <span class="header-title">查询条件</span>
                <div class="header-right backBtn">
                  <el-button style="border: none" size="mini" @click="closeDetail" icon="el-icon-arrow-left"
                  ></el-button
                  >
                </div>
              </div>

              <el-descriptions class="margin-top" :column="3">
                <el-descriptions-item label="网元名称">
                  {{ detailSearchInfo.NeName }}
                </el-descriptions-item>
                <el-descriptions-item label="设备ID">
                  {{ detailSearchInfo.Omc }}
                </el-descriptions-item>
                <el-descriptions-item label="厂商">
                  {{ detailSearchInfo.Vendor }}
                </el-descriptions-item>
                <template v-if="detailSearchInfo.command == '1'">
                  <el-descriptions-item label="小区名称">
                    {{ detailSearchInfo.CellName }}
                  </el-descriptions-item>
                  <el-descriptions-item label="小区ID">
                    {{ detailSearchInfo.EUTRANCELLID }}
                  </el-descriptions-item>
                  <el-descriptions-item label="网络类型">
                    {{ detailSearchInfo.networkType }}
                  </el-descriptions-item>
                  <el-descriptions-item label="告警子网">
                    {{ detailSearchInfo.SBN }}
                  </el-descriptions-item>
                  <el-descriptions-item label="小区类型" v-if="detailSearchInfo.networkType=='5G'">
                    {{ detailSearchInfo.CellType }}
                  </el-descriptions-item>
                </template>
                <template v-if="detailSearchInfo.command == '2' || detailSearchInfo.command == '3'">
                  <el-descriptions-item label="告警子网">
                    {{ detailSearchInfo.SBN }}
                  </el-descriptions-item>
                  <el-descriptions-item label="框">
                    {{ detailSearchInfo.SRN }}
                  </el-descriptions-item>
                  <el-descriptions-item label="槽">
                    {{ detailSearchInfo.SN }}
                  </el-descriptions-item>
                </template>
                <template v-if="detailSearchInfo.command == '4'">
                  <el-descriptions-item label="告警子网">
                    {{ detailSearchInfo.SBN }}
                  </el-descriptions-item>
                  <el-descriptions-item label="告警开始时间">
                    {{ detailSearchInfo.BeginTime }}
                  </el-descriptions-item>
                  <el-descriptions-item label="告警结束时间">
                    {{ detailSearchInfo.EndTime }}
                  </el-descriptions-item>
                </template>
                <template v-if="detailSearchInfo.command == '5'">
                  <el-descriptions-item label="告警子网">
                    {{ detailSearchInfo.SBN }}
                  </el-descriptions-item>
                  <el-descriptions-item label="网络类型">
                    {{ detailSearchInfo.networkType }}
                  </el-descriptions-item>
                </template>
                <template v-if="detailSearchInfo.command == '6'">
                  <el-descriptions-item label="告警子网">
                    {{ detailSearchInfo.SBN }}
                  </el-descriptions-item>
                  <el-descriptions-item label="框号">
                    {{ detailSearchInfo.SRN }}
                  </el-descriptions-item>
                  <el-descriptions-item label="槽号">
                    {{ detailSearchInfo.SN }}
                  </el-descriptions-item>
                  <el-descriptions-item label="柜号">
                    {{ detailSearchInfo.CN }}
                  </el-descriptions-item>
                </template>
                <template v-if="detailSearchInfo.command == '8'">
                  <el-descriptions-item label="设备类型">
                    {{ detailSearchInfo.DeviceType }}
                  </el-descriptions-item>
                  <el-descriptions-item label="告警子网">
                    {{ detailSearchInfo.SBN }}
                  </el-descriptions-item>
                  <el-descriptions-item label="LDN">
                    {{ detailSearchInfo.LDN }}
                  </el-descriptions-item>
                </template>
              </el-descriptions>
            </el-card>
            <hr>
            <el-card
              shadow="never"
              class="detailCard"
              :body-style="{ padding: '10px 24px' }">
              <div class="header clearfix">
                <span class="header-title">查询结果</span>
              </div>
              <!--错误提示-->
              <div v-if="detailErrorMsg!=''">
                <span style="color: #b50b14">【查询失败】</span>{{detailErrorMsg}}
              </div>

              <!--列表-->
              <template v-if="detailErrorMsg==''" style="margin-left: 16px;margin-right: 16px">
                <el-table
                  ref="detailTable"
                  :data="detailResultData"
                  style="width: 100%"
                  border
                  v-loading="tableLoading"
                  v-if="detailSearchInfo.command == '1'"
                >
                  <el-table-column
                    type="index"
                    label="序号"
                    width="60px">
                  </el-table-column>
                  <template v-if="detailSearchInfo.networkType=='4G' && detailSearchInfo.Vendor=='华为'">
                    <el-table-column
                      show-overflow-tooltip
                      prop="statusChangeReason"
                      label="最近一次状态变化的原因"
                      min-width="200px">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="communityName"
                      label="小区名称"
                      min-width="380px">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="communityInstStatus"
                      label="小区的实例状态"
                      min-width="140px">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="comAvaToUnTime"
                      label="小区从可用变为不可用时间"
                      min-width="p210x">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="comUnOperation"
                      label="最近一次引起小区不可用的操作类型"
                      min-width="260px">
                    </el-table-column>
                  </template>
                  <template v-if="detailSearchInfo.networkType=='4G' && detailSearchInfo.Vendor=='爱立信'">
                    <el-table-column
                      show-overflow-tooltip
                      prop="communityName"
                      label="小区名称"
                      min-width="380px">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="comAvaStatus"
                      label="小区可用状态"
                      min-width="140px">
                    </el-table-column>
                  </template>
                  <template v-if="detailSearchInfo.networkType=='4G' && detailSearchInfo.Vendor=='中兴'">
                    <el-table-column
                      show-overflow-tooltip
                      prop="communityId"
                      label="小区ID"
                      min-width="140px">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="comAvaStatus"
                      label="小区可用状态"
                      min-width="140px">
                    </el-table-column>
                  </template>
                  <template v-if="detailSearchInfo.networkType=='5G' && detailSearchInfo.CellType=='DU'">
                    <el-table-column
                      show-overflow-tooltip
                      prop="netElementName"
                      label="网元名称"
                      min-width="150px">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="duCommunityName"
                      label="DU小区名称"
                      min-width="380px">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="duComAvaStatus"
                      label="DU小区可用状态"
                      min-width="150px">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="statusChangeReason"
                      label="最近一次状态变化的原因"
                      min-width="200px">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="comAvaToUnTime"
                      label="小区从可用变为不可用时间"
                      min-width="210px">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="comUnOperation"
                      label="最近一次引起小区不可用的操作类型"
                      min-width="260px">
                    </el-table-column>
                  </template>
                  <template v-if="detailSearchInfo.networkType=='5G' && detailSearchInfo.CellType=='CU' && detailSearchInfo.Vendor=='华为'">
                    <el-table-column
                      show-overflow-tooltip
                      prop="netElementName"
                      label="网元名称"
                      min-width="150px">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="cuCommunityName"
                      label="CU小区名称"
                      min-width="380px">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="cuComAvaStatus"
                      label="CU小区可用状态"
                      min-width="150px">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="statusChangeReason"
                      label="最近一次状态变化的原因"
                      min-width="200px">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="comAvaToUnTime"
                      label="小区从可用变为不可用时间"
                      min-width="210px">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="comUnOperation"
                      label="最近一次引起小区不可用的操作类型"
                      min-width="260px">
                    </el-table-column>
                  </template>
                  <template v-if="detailSearchInfo.networkType=='5G' && detailSearchInfo.CellType=='CU' && detailSearchInfo.Vendor=='爱立信'">
                    <el-table-column
                      show-overflow-tooltip
                      prop="netElementName"
                      label="网元名称"
                      min-width="150px">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="cuCommunityName"
                      label="CU小区名称"
                      min-width="380px">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="cuComAvaStatus"
                      label="CU小区可用状态"
                      min-width="150px">
                    </el-table-column>
                  </template>
                  <template v-if="detailSearchInfo.networkType=='5G' && detailSearchInfo.CellType=='CU' && detailSearchInfo.Vendor=='中兴'">
                    <el-table-column
                      show-overflow-tooltip
                      prop="lastUpdateTime"
                      label="上次修改时间"
                      min-width="160px">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="netElementType"
                      label="网元类型"
                      min-width="150px">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="serviceStatus"
                      label="服务状态"
                      min-width="130px">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="ldn"
                      label="ldn"
                      min-width=100"px">
                    </el-table-column>
                  </template>
                </el-table>
                <el-table
                  ref="detailTable"
                  :data="detailResultData"
                  style="width: 100%"
                  border
                  v-loading="tableLoading"
                  v-if="detailSearchInfo.command == '2'"
                >
                  <el-table-column
                    type="index"
                    label="序号"
                    width="60px">
                  </el-table-column>
                  <template v-if="detailSearchInfo.Vendor=='华为'">
                    <el-table-column
                      show-overflow-tooltip
                      prop="placeBar"
                      label="所在柜"
                      min-width="90px">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="placeSRN"
                      label="所在框"
                      min-width="90px">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="placeSN"
                      label="所在槽"
                      min-width="90px">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="portType"
                      label="端口类型"
                      min-width="100px">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="placePort"
                      label="所在端口号"
                      min-width="110px">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="beatPort"
                      label="在位状态"
                      min-width="100px">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="opticalModuleType"
                      label="光模块类型"
                      min-width="110px">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="opticalModuleVendor"
                      label="光模块厂家"
                      min-width="110px">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="transferMode"
                      label="传输模式"
                      min-width="100px">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="sendOpticalPower"
                      label="发送光功率（0.01dBm）"
                      min-width="200px">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="reciveOpticalPower"
                      label="接收光功率（0.01dBm）"
                      min-width="200px">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="RRUName"
                      label="RRU名称"
                      min-width="250px">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="localCommunityId"
                      label="本地小区标识"
                      min-width="125px">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="RFUnitInformation"
                      label="射频单元信息"
                      min-width="125px">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="serviceBoardId"
                      label="服务基带板主辅标识"
                      min-width="170px">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="transferCodeRate"
                      label="传输码速率"
                      min-width="110px">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="ofTransferDistance"
                      label="单模光纤的传输距离（100米）"
                      min-width="230px">
                    </el-table-column>
                  </template>
                  <template v-if="detailSearchInfo.Vendor=='中兴'">
                    <el-table-column
                      show-overflow-tooltip
                      prop="portId"
                      label="端口ID">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="sendPower"
                      label="接收功率">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="recivePower"
                      label="发送功率">
                    </el-table-column>
                  </template>
                  <template v-if="detailSearchInfo.Vendor=='爱立信'">
                    <el-table-column
                      show-overflow-tooltip
                      prop="linkId"
                      label="链路ID">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="sendOpticalPower1"
                      label="发送光功率1">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="reciveOpticalPower1"
                      label="接收光功率1">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="sendOpticalPower2"
                      label="发送光功率2">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="reciveOpticalPower2"
                      label="接收光功率2">
                    </el-table-column>
                  </template>
                </el-table>
                <el-table
                  ref="detailTable"
                  :data="detailResultData"
                  style="width: 100%"
                  border
                  v-loading="tableLoading"
                  v-if="detailSearchInfo.command == '3'"
                >
                  <el-table-column
                    type="index"
                    label="序号"
                    width="60px">
                  </el-table-column>
                  <template v-if="detailSearchInfo.Vendor=='华为'">
                    <el-table-column
                      show-overflow-tooltip
                      prop="BAR"
                      label="柜"
                      min-width="90px">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="SRN"
                      label="框"
                      min-width="90px">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="SN"
                      label="槽"
                      min-width="90px">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="tranChannelNo"
                      label="发射通道号"
                      min-width="110px">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="radioPort"
                      label="射频端口"
                      min-width="100px">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="standingWaveRatio"
                      label="驻波比（0.01）"
                      min-width="140px">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="RRUName"
                      label="RRU名称"
                      min-width="250px">
                    </el-table-column>
                  </template>
                  <template v-if="detailSearchInfo.Vendor=='爱立信'">
                    <el-table-column
                      show-overflow-tooltip
                      prop="boardName"
                      label="单板名称">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="RRUName"
                      label="RRU名称"
                      min-width="250px">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="radioPort"
                      label="射频端口">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="standingWaveRatio"
                      label="驻波比（0.01）">
                    </el-table-column>
                  </template>
                  <template v-if="detailSearchInfo.Vendor=='中兴'">
                    <el-table-column
                      show-overflow-tooltip
                      prop="number"
                      label="编号">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="band"
                      label="频带">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="standingWaveRatio"
                      label="驻波比">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="alarmStatus"
                      label="告警状态">
                    </el-table-column>
                  </template>
                </el-table>
                <el-table
                  ref="detailTable"
                  :data="detailResultData"
                  style="width: 100%"
                  border
                  v-loading="tableLoading"
                  v-if="detailSearchInfo.command == '4'"
                >
                  <el-table-column
                    type="index"
                    label="序号"
                    width="60px">
                  </el-table-column>
                  <template v-if="detailSearchInfo.networkType=='4G' && detailSearchInfo.Vendor=='中兴'">
                    <el-table-column
                      show-overflow-tooltip
                      prop="equipmentName"
                      label="设备名称"
                      min-width="160px">
                    </el-table-column>
                  </template>
                  <el-table-column
                    show-overflow-tooltip
                    prop="alarmTitle"
                    label="告警标题"
                    min-width="160px">
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    prop="alarmCreateTime"
                    label="告警发生时间"
                    min-width="160px">
                  </el-table-column>
                  <template v-if="detailSearchInfo.Vendor=='中兴'">
                    <el-table-column
                      show-overflow-tooltip
                      prop="alarmRecoverTime"
                      label="告警恢复时间"
                      min-width="160px">
                    </el-table-column>
                  </template>
                  <el-table-column
                    show-overflow-tooltip
                    prop="positionInfo"
                    label="定位信息"
                    min-width="900px">
                  </el-table-column>
                  <template v-if="detailSearchInfo.Vendor=='华为'">
                    <el-table-column
                      show-overflow-tooltip
                      prop="alarmClearTime"
                      label="告警清除时间"
                      min-width="160px">
                    </el-table-column>
                  </template>
                </el-table>
                <el-table
                  ref="detailTable"
                  :data="detailResultData"
                  style="width: 100%"
                  border
                  v-loading="tableLoading"
                  v-if="detailSearchInfo.command == '5'"
                >
                  <el-table-column
                    type="index"
                    label="序号"
                    width="60px">
                  </el-table-column>
                  <template v-if="detailSearchInfo.networkType=='4G' && detailSearchInfo.Vendor=='中兴'">
                    <el-table-column
                      show-overflow-tooltip
                      prop="equipmentName"
                      label="设备名称"
                      min-width="160px">
                    </el-table-column>
                  </template>
                  <el-table-column
                    show-overflow-tooltip
                    prop="alarmTitle"
                    label="告警标题"
                    min-width="160px">
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    prop="alarmCreateTime"
                    label="告警发生时间"
                    min-width="160px">
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    prop="positionInfo"
                    label="定位信息"
                    min-width="900px">
                  </el-table-column>
                </el-table>
                <el-table
                  ref="detailTable"
                  :data="detailResultData"
                  style="width: 100%"
                  border
                  v-loading="tableLoading"
                  v-if="detailSearchInfo.command == '6'"
                >
                  <el-table-column
                    type="index"
                    label="序号"
                    width="60px"
                  >
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    prop="placeBar"
                    label="所在柜">
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    prop="placeSRN"
                    label="所在框">
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    prop="placeSN"
                    label="所在槽">
                  </el-table-column>
                  <template v-if="detailSearchInfo.Vendor=='华为' || detailSearchInfo.Vendor=='爱立信'">
                    <el-table-column
                      show-overflow-tooltip
                      prop="boardType"
                      label="单板类型">
                    </el-table-column>
                  </template>
                  <el-table-column
                    show-overflow-tooltip
                    prop="boardAvaStatus"
                    label="单板可用状态">
                  </el-table-column>
                  <template v-if="detailSearchInfo.Vendor=='华为' || detailSearchInfo.Vendor=='爱立信'">
                    <el-table-column
                      show-overflow-tooltip
                      prop="RRUName"
                      label="RRU名称"
                      min-width="250">
                    </el-table-column>
                  </template>
                </el-table>
                <el-table
                  ref="detailTable"
                  :data="detailResultData"
                  style="width: 100%"
                  border
                  v-loading="tableLoading"
                  v-if="detailSearchInfo.command == '7'"
                >
                  <el-table-column
                    type="index"
                    label="序号"
                    width="60px">
                  </el-table-column>
                  <template v-if="detailSearchInfo.Vendor=='华为'">
                    <el-table-column
                      show-overflow-tooltip
                      prop="speedRatio"
                      label="转速比">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="temperature"
                      label="温度">
                    </el-table-column>
                  </template>
                  <template v-if="detailSearchInfo.Vendor=='爱立信'">
                    <el-table-column
                      show-overflow-tooltip
                      prop="temperature"
                      label="温度">
                    </el-table-column>
                  </template>
                </el-table>
                <el-table
                  ref="detailTable"
                  :data="detailResultData"
                  style="width: 100%"
                  border
                  v-loading="tableLoading"
                  v-if="detailSearchInfo.command == '8'"
                >
                  <el-table-column
                    type="index"
                    label="序号"
                    width="60px">
                  </el-table-column>
                  <template v-if="detailSearchInfo.Vendor=='华为' || detailSearchInfo.Vendor=='爱立信'">
                    <el-table-column
                      show-overflow-tooltip
                      prop="clockNo"
                      label="GPS/GNSS时钟编号">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="status"
                      label="状态">
                    </el-table-column>
                  </template>
                  <template v-if="detailSearchInfo.Vendor=='中兴' || detailSearchInfo.DeviceType=='5G'">
                    <el-table-column
                      type="index"
                      label="序号"
                      width="180">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="PPSStatus"
                      label="PPS状态">
                    </el-table-column>
                    <el-table-column
                      show-overflow-tooltip
                      prop="initStatus"
                      label="接收机初始化状态">
                    </el-table-column>
                  </template>
                </el-table>
                <pagination
                  ref="pagination1"
                  :total="detailForm.total"
                  :page.sync="detailForm.pageNum"
                  :limit.sync="detailForm.pageSize"
                  layout="->, total, sizes, prev, pager, next"
                  @change="queryDetailData()"
                />
              </template>
            </el-card>
          </template>
        </div>

      </el-collapse-item>
    </el-collapse>
  </div>

</template>

<script>
  import Pagination from "../../modules/workOrder/components/Pagination.vue";
  import { mapGetters } from "vuex";
  import {
    apiCommandQuery,
    apiQueryHistory,
    apiRefreshCommandQuery
  } from "./api";

export default {
  name: "CommandQuery",
  components: {
    Pagination,
  },
  props: {
    basicWorkOrderData: Object,
  },
  data() {
    return {
      csTip:'',
      wllxTip:'',
      showwymc:false,
      showsbid:false,
      showxqmc:false,
      showxqid:false,
      showgjzw:false,
      showkuang:false,
      showcao:false,
      showgui:false,
      showldn:false,
      showTime:5000,
      hisTimer:null,
      logId:'',
      timer: null,
      tableLoading: false,
      startTime:'',
      endTime:'',
      radio1:'指令查询',
      activeNames: ['1'],
      selectedCommand:{},
      commandBtns:[],
      commandIcons:[],
      commandIconsSelected:[],
      form:{
        NeName:null,//网元名称
        Omc:'',//设备ID
        CellName:'',//小区名称
        Vendor:'',//厂商
        EUTRANCELLID:'',//小区id
        networkType:'',//网络类型
        SBN:'',//告警子网
        CellType:'',//小区类型
        alarmCreateTimeRange:[],//BeginTime EndTime
        DeviceType:'',//设备类型
        LDN:'',//LDN
      },
      formRules:{

      },
      tempVendor:'', //接收选中的厂商
      tempNetworkType:'',//接收网络类型
      tempCellType:'', // 接收小区类型
      tempDeviceType:'', // 接收小区类型
      commandData:[],
      commandSearchErrorMsg:'',
      comForm: {
        pageNum: 1,
        pageSize: 5,
        total: 0,
      },
      showComTable:false,
      //历史记录相关
      historySearchData:[],
      hisForm: {
        pageNum: 1,
        pageSize: 5,
        total: 0,
      },
      //详情相关
      showHisDetail:false,
      detailForm: {
        pageNum: 1,
        pageSize: 5,
        total: 0,
      },
      detailSearchInfo:{},
      detailResultData:[],
      detailErrorMsg:'',

    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  watch: {

  },
  mounted() {
    this.userInfoData = JSON.parse(this.userInfo.attr2);
    this.csTip = '仅支持厂商为华为/爱立信/中兴';
    this.wllxTip = '仅支持网络类型为4G/5G';

    //command 1：小区状态 2：光模块功率 3：驻波比 4：历史告警 5：活动告警 6：单板状态 7：风扇转速温度 8：GPS状态
    this.commandBtns = [{
      name:'小区状态',
      id:'1',
      hide: false
    },{
      name:'光模块功率',
      id:'2',
      hide: false
    },{
      name:'驻波比',
      id:'3',
      hide: false
    },{
      name:'历史告警',
      id:'4',
      hide: false
    },{
      name:'活动告警',
      id:'5',
      hide: false
    },{
      name:'单板状态',
      id:'6',
      hide: false
    },{
      name:'风扇转速温度',
      id:'7',
      hide: false
    },{
      name:'GPS状态',
      id:'8',
      hide: false
    }]

    this.commandIcons = [
      require('../../assets/xqzt.png'),
      require('../../assets/gmkgl.png'),
      require('../../assets/zbb.png'),
      require('../../assets/lsgj.png'),
      require('../../assets/hdgj.png'),
      require('../../assets/dbzt.png'),
      require('../../assets/fszswd.png'),
      require('../../assets/gps.png')];
    this.commandIconsSelected = [
      require('../../assets/xqzt_selected.png'),
      require('../../assets/gmkgl_selected.png'),
      require('../../assets/zbb_selected.png'),
      require('../../assets/lsgj_selected.png'),
      require('../../assets/hdgj_selected.png'),
      require('../../assets/dbzt_selected.png'),
      require('../../assets/fszswd_selected.png'),
      require('../../assets/gps_selected.png')];
    this.selectedCommand = this.commandBtns[0];
    //设置默认值
    this.form.NeName = this.basicWorkOrderData.commandNeName;//网元名称
    this.form.Omc = this.basicWorkOrderData.commandOmc;//设备ID
    this.form.Vendor = this.basicWorkOrderData.commandVendor;//厂商
    this.form.networkType= this.basicWorkOrderData.networkType;//网络类型
    if (this.basicWorkOrderData.commandVendor != '华为' && this.basicWorkOrderData.commandVendor != '中兴' && this.basicWorkOrderData.commandVendor != '爱立信'){
      this.form.Vendor = '华为';//厂商
    }
    if (this.basicWorkOrderData.networkType != '4G' && this.basicWorkOrderData.networkType != '5G'){
      this.form.networkType = '4G';//厂商
    }
  },
  beforeDestroy() {
    this.clearTimer();
    this.stopHisTimer();
  },
  destroyed() {
    this.clearTimer();
    this.stopHisTimer();
  },
  methods: {

    descTip(count,name,showName){
      if (this.form[name].length>=count){
        this[showName] = true;
        setTimeout(() => {
          this[showName] = false;
        }, this.showTime);
      }
      else{
        this[showName] = false;
      }
    },

    clearTimer() {
      clearTimeout(this.timer);
      this.timer = null;
    },

    // 表头部重新渲染
    // renderHeader(h, { column, $index }) {
    //   // 新建一个 span
    //   let span = document.createElement('span');
    //   // 设置表头名称
    //   span.innerText = column.label;
    //   // 临时插入 document
    //   document.body.appendChild(span);
    //   // 重点：获取 span 最小宽度，设置当前列，注意这里加了 20，字段较多时还是有挤压，且渲染后的 div 内左右 padding 都是 10，所以 +20 。（可能还有边距/边框等值，需要根据实际情况加上）
    //   if (column.label.indexOf('小区名称') != -1){
    //     column.minWidth = 380;
    //   }
    //   else if (column.label == 'RRU名称'){
    //     column.minWidth = 250;
    //   }
    //   else
    //     column.minWidth = (span.getBoundingClientRect().width) + 40;
    //   // this.headerLableWidth[column.property] = column.minWidth;
    //   // 移除 document 中临时的 span
    //
    //   console.log('-------column.minWidth------');
    //   console.log(column.label);
    //   console.log(column.minWidth);
    //   document.body.removeChild(span);
    //   return h('span', column.label);
    // },

    //点击tab切换
    handleClick(val) {
      this.onResetForm();
      this.historySearchData = [];
      this.hisForm = {
        pageNum: 1,
        pageSize: 5,
        total: 0,
      };
      this.showHisDetail = false;
      this.detailForm = {
        pageNum: 1,
        pageSize: 5,
        total: 0,
      };
      this.detailSearchInfo = {};
      this.detailResultData = [];
      this.detailErrorMsg = '';

      this.stopHisTimer();

      if (val == '历史查询记录'){
        this.getHistorySearchData();
      }
    },

    //切换指令查询
    onClickCommand(val){
      this.selectedCommand = val;
      this.onResetForm();
    },

    //指令查询条件重置
    onResetForm(){
      this.clearTimer();
      this.form = {
        NeName:'',//网元名称
        Omc:'',//设备ID
        CellName:'',//小区名称
        Vendor:'',//厂商
        EUTRANCELLID:'',//小区id
        networkType:'',//网络类型
        SBN:'',//告警子网
        CellType:'',//小区类型
        alarmCreateTimeRange:[],//BeginTime EndTime
        DeviceType:'',//设备类型
        LDN:'',//LDN
      };
      this.comForm = {
        pageNum: 1,
        pageSize: 5,
        total: 0
      };
      this.commandSearchErrorMsg = '';
      this.commandData = [];
      this.timer = null;
      this.showComTable = false;

      //超长提示全部去掉
      this.showwymc = false;
      this.showsbid = false;
      this.showxqmc = false;
      this.showxqid = false;
      this.showgjzw = false;
      this.showkuang = false;
      this.showcao = false;
      this.showgui = false;
      this.showldn = false;

      //设置默认值
      this.form.NeName = this.basicWorkOrderData.commandNeName;//网元名称
      this.form.Omc = this.basicWorkOrderData.commandOmc;//设备ID
      this.form.Vendor = this.basicWorkOrderData.commandVendor;//厂商
      this.form.networkType= this.basicWorkOrderData.networkType;//网络类型
      if (this.basicWorkOrderData.commandVendor != '华为' && this.basicWorkOrderData.commandVendor != '中兴' && this.basicWorkOrderData.commandVendor != '爱立信'){
        this.form.Vendor = '华为';//厂商
      }
      if (this.basicWorkOrderData.networkType != '4G' && this.basicWorkOrderData.networkType != '5G'){
        this.form.networkType = '4G';//厂商
      }

      if(this.radio1 == '指令查询'){
        // 移除校验
        this.formClearValidate('searchForm')
      }

    },

    //指令查询
    seniorQuery(){
      this.$refs['searchForm'].validate(valid => {
        if (valid) {
          this.tempVendor = this.form.Vendor
          this.tempNetworkType = this.form.networkType
          this.tempCellType = this.form.CellType
          this.tempDeviceType = this.form.DeviceType
          this.tableLoading = true;
          this.showComTable = true;

          let param = JSON.parse(JSON.stringify(this.form));
          param.BeginTime = this?.form?.alarmCreateTimeRange?.[0] ?? this.startTime
          param.EndTime = this?.form?.alarmCreateTimeRange?.[1] ?? this.endTime
          this.$delete(param, "alarmCreateTimeRange");
          param.sheetNo = this.basicWorkOrderData.sheetNo;
          param.woId = this.basicWorkOrderData.woId;
          param.command = this.selectedCommand.id;
          param.pageSize = this.comForm.pageSize;
          param.currentPage = 0;

          this.comForm = {
            pageNum: 1,
            pageSize: 5,
            total: 0,
          }
          apiCommandQuery(param)
            .then(res => {
              if (res.status == "0") {
                //走定时刷新接口获取数据
                this.$message.warning('您可继续等待或稍后进入历史查询记录中查看结果');
                this.logId = res.data;
                this.refreshCommandData();
              }
              else{
                this.showComTable = false;
                this.commandSearchErrorMsg = res?.data ?? '';
                this.comForm.total = 0;
                this.tableLoading = false;
              }
            })
            .catch(error => {
              console.log(error);
              this.tableLoading = false;
            });
        } else {
          return false;
        }
      });

    },

    //定时刷新数据接口
    refreshCommandData(){
      let param = {
        "logId" : this.logId,
        "pageSize" : this.comForm.pageSize,
        "currentPage" : this.comForm.pageNum
      };

      this.tableLoading = true;
      apiRefreshCommandQuery(param)
        .then(res => {
          //状态码返回为 100 就继续等待刷新 0是成功，400 是失败
          if (res.status == "0") {
            this.commandSearchErrorMsg ='';
            this.commandData = res?.data?.data ?? [];
            this.comForm.total = res?.data?.total ?? 0;

            this.$nextTick(() => {
              if (this.$refs.commandTable && this.$refs.commandTable.doLayout) {
                this.$refs.commandTable.doLayout();
              }
            });

          }
          else{
            this.showComTable = false;
            this.commandSearchErrorMsg = res?.data ?? '';
            this.comForm.total = 0;
          }
          this.tableLoading = false;
        })
        .catch(error => {
          console.log(error);
          if (error.status == "100") {
            this.timer = setTimeout(this.refreshCommandData, 1000 * 10);//10s
          }
          else{
            this.tableLoading = false;
            this.showComTable = false;
            this.commandSearchErrorMsg = error?.data ?? '';
            this.comForm.total = 0;
          }
        });
    },

    //停止历史记录刷新
    stopHisTimer(){
      if (this.hisTimer) { // 如果定时器存在，则清除它
        clearInterval(this.hisTimer);
        this.hisTimer = null; // 将定时器引用设置为null，确保下次不再误用
      }
    },

    //历史翻页
    hisPageChange(){
      this.stopHisTimer();
      this.getHistorySearchData();
    },

    //查询历史数据
    getHistorySearchData(){
      let param = {
        "woId" : this.basicWorkOrderData.woId,
        "pageSize" : this.hisForm.pageSize,
        "currentPage" : this.hisForm.pageNum
      };

      this.tableLoading = true;

      apiQueryHistory(param)
        .then(res => {
          if (res.status == "0") {

            this.historySearchData = res?.data?.data ?? [];
            this.hisForm.total = res?.data?.total ?? 0;

            var isRefresh = false;
            this.historySearchData.forEach(item =>{
              if (item.is_ok == '2'){//有查询中的就定时
                isRefresh = true;
              }
            });
            if (isRefresh && this.hisTimer == null){
              this.hisTimer = setInterval(() => {
                this.getHistorySearchData();
              }, 1000*60*2); // 刷新频率，单位是毫秒 2分钟
            }
          }
          else{
            this.hisForm.total = 0;
          }
          this.tableLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.tableLoading = false;
        });
    },

    //查看查询历史详情
    searchResultDetail(row){
      this.showHisDetail = true;
      console.log(row);
      this.detailSearchInfo = JSON.parse(row.param);
      this.logId = row.log_id;
      this.detailResultData = [];
      this.detailErrorMsg ='';
      this.detailForm = {
        pageNum: 1,
        pageSize: 5,
        total: 0,
      };
      this.queryDetailData();
    },

    //查询历史详情
    queryDetailData(){
      let param = {
        "logId" : this.logId,
        "pageSize" : this.detailForm.pageSize,
        "currentPage" : this.detailForm.pageNum
      };

      this.tableLoading = true;
      apiRefreshCommandQuery(param)
        .then(res => {
          //状态码返回为 100 就继续等待刷新 0是成功，400 是失败
          if (res.status == "0") {
            this.detailErrorMsg ='';
            this.detailResultData = res?.data?.data ?? [];
            this.detailForm.total = res?.data?.total ?? 0;

            this.$nextTick(() => {
              if (this.$refs.detailTable && this.$refs.detailTable.doLayout) {
                this.$refs.detailTable.doLayout();
              }
            });

          }
          else{
            this.showComTable = false;
            this.detailErrorMsg = res?.data ?? '';
            this.detailForm.total = 0;
          }
          this.tableLoading = false;
        })
        .catch(error => {
          console.log(error);
          this.detailErrorMsg = error?.data ?? '';
          this.tableLoading = false;
        });
    },

    //关闭查询历史详情
    closeDetail(){
      this.showHisDetail = false;
    },

    // 移除校验
    formClearValidate(formName){
      this.$refs[formName].clearValidate()
    },
    filterCommandFn(row){
      let commandObj = this.commandBtns.filter((item)=> {return item.id == row.command})
      return commandObj[0]?.name
    }

  },
};
</script>
<style>
  .custom-theme-default  .el-message--warning .el-message__content{
    color: #000;
  }
</style>
<style lang="scss" scoped>
  @import "../../modules/commonProvince/workOrderWaitDetail/assets/common.scss";
  ::v-deep .el-collapse-item {
    .el-collapse-item__header {
      background-color: #fafafa;
      padding-left: 20px;
    }
  }


  /*::v-deep .el-table th.el-table__cell > .cell {*/
  /*  display: inline-block;*/
  /*  -webkit-box-sizing: border-box;*/
  /*  box-sizing: border-box;*/
  /*  position: relative;*/
  /*  vertical-align: middle;*/
  /*  padding-left: 10px;*/
  /*  padding-right: 10px;*/
  /*  width: 100%;*/
  /*  white-space: nowrap !important;//加入这行代码*/
  /*}*/

  /*::v-deep .el-table th, .el-table td {*/
  /*  white-space: nowrap;*/
  /*}*/

  /*.tableHHHH{*/
  /*  ::v-deep .el-table{*/
  /*    margin: 0px 20px;*/
  /*  }*/
  /*}*/

  ::v-deep .el-table .cell {
    display: flex !important;
    align-items: center;
    justify-content: center;
  }

  ::v-deep .el-table .cell.el-tooltip {
     white-space: normal !important;
    min-width: 50px;
  }
  /*::v-deep .el-table .el-table__body-wrapper {*/
  /*  overflow-x: auto;*/
  /*}*/

  .demo-form-inline{
    margin-right: 20px;

    .el-button{
      height: 28px;
    }
    // ::v-deep .el-form-item--small.el-form-item{
    //   margin-bottom: 10px;
    // }
    ::v-deep .el-input--small .el-input__inner{
      height: 30px;
      line-height: 30px;
      font-size: 13px;
    }
  }

  .tipCla{
    margin:12px 20px 0 20px;
    border-top: 1px solid #c2c2c2;
    font-weight: bold;
    padding-top: 10px;
  }

  .filter-total-item {
    display: inline-block;
    padding: 2px 8px;
    font-size: 13px;
    cursor: pointer;
    margin-left: 20px;
    background-color: #f8e8e8;
    border: 1px solid #e6e6e6;
    border-radius: 2px;
    margin-top: 16px;
    margin-bottom: 16px;

    span{
      padding-top: 1px;
    }

    .btnDiv{
      display: flex;
      align-items: center;

      img{
        width: 12px;
        height: 12px;
        margin-right: 4px;
      }
    }

    &.active {
      border-radius: 2px;
      border: 0px;
      @include themify() {
        background-color: themed("$--color-primary");
        color: themed("$--color-white");
      }
    }

  }

  .viewBtn{
    text-decoration: underline;
    color: #b50b14;
  }

  .header-title{
    font-size: 15px;
    font-weight: 600;
    /*font-weight: bold;*/
  }

  .detailCard{
    border:none !important;

    .el-button:focus, .el-button:hover {
      color: #b50b14;
      /*border-color: #e9b6b9;*/
      background-color: #ffffff;
    }
  }

  hr{
    background-color: #c2c2c2;
    margin: 0px 30px;
    border: none;
    height: 1px;
  }

  .header-right{
    .el-button--mini {
      font-size: 18px !important;
    }
    ::v-deep .el-icon-arrow-left{
      font-weight: 800;
    }
  }

  ::v-deep .el-form-item__label {
    font-size: 13px !important;
  }

  ::v-deep .el-form-item__content {
    font-size: 13px !important;
  }

  ::v-deep .el-collapse-item__content {
    font-size: 13px !important;
    padding-bottom: 10px;
  }
  .op-btn ::v-deep .el-button--small {
    padding: 0 15px;
  }
  .op-btn .el-button{
    line-height: 26px;
  }

</style>
