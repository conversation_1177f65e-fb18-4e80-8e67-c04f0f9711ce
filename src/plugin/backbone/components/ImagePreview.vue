<template>
  <el-dialog
    width="73%"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
    custom-class="image-preview-dialog"
    :show-close="false"
    @close="handleClose"
  >
    <template slot="title">
      <div class="dialog-title-container">
        <span class="dialog-title-text">{{ title || fileName }}</span>
        <div class="dialog-actions">
          <el-button type="primary" size="small" @click="handleDownload" class="action-button">下载</el-button>
          <i class="el-icon-close close-icon" @click="handleClose"></i>
        </div>
      </div>
    </template>
    <div class="image-preview-container">
      <div class="image-preview-content" v-loading="loading">
        <img v-if="imageUrl" :src="imageUrl" class="preview-image" />
        <div v-else-if="!loading && previewFailed" class="preview-error">无法预览该文件</div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
// 导入axiosBasePath
import { axiosBasePath } from '@/plugin/backbone/settings';
import { getToken } from '@/utils/auth';

export default {
  name: "ImagePreview",
  props: {
    // 是否显示对话框
    visible: {
      type: Boolean,
      default: false
    },
    // 文件对象，包含id和name等信息
    fileData: {
      type: Object,
      default: () => ({})
    },
    // 预览API地址，默认为空，需要传入
    previewApi: {
      type: String,
      default: ""
    },
    // 自定义标题
    title: {
      type: String,
      default: ""
    },
    // 是否使用自定义的下载方法
    useCustomDownload: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogVisible: this.visible,
      imageUrl: "",
      loading: false,
      previewFailed: false
    };
  },
  computed: {
    fileName() {
      return this.fileData.name || "图片预览";
    }
  },

  beforeDestroy() {
    // 释放Blob URL，避免内存泄漏
    if (this.imageUrl && this.imageUrl.startsWith('blob:')) {
      URL.revokeObjectURL(this.imageUrl);
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
      if (val && this.fileData.id) {
        this.previewFile();
      }
    },
    dialogVisible(val) {
      if (!val) {
        this.$emit("update:visible", false);
      }
    },
    fileData: {
      handler(val) {
        if (val && val.id && this.dialogVisible) {
          this.previewFile();
        }
      },
      deep: true
    }
  },
  methods: {
    // 关闭对话框
    handleClose() {
      // 释放Blob URL，避免内存泄漏
      if (this.imageUrl && this.imageUrl.startsWith('blob:')) {
        URL.revokeObjectURL(this.imageUrl);
      }
      this.dialogVisible = false;
      this.imageUrl = "";
      this.previewFailed = false;
      this.$emit("close");
    },

    // 下载图片
    handleDownload() {
      if (this.useCustomDownload) {
        // 使用父组件提供的下载方法
        this.$emit("download", this.fileData);
      } else {
        // 使用组件内部的下载方法
        this.downloadFile();
      }
    },

    // 预览文件
    previewFile() {
      if (!this.fileData.id || !this.previewApi) {
        this.$message.error('预览参数不完整');
        return;
      }

      this.loading = true;

      // 使用XMLHttpRequest直接获取文件流，避免触发自动下载
      const xhr = new XMLHttpRequest();

      // 处理URL，按照request.js中的方式添加axiosBasePath前缀
      let url = this.previewApi;
      // 确保URL以/开头
      if (!url.startsWith('/')) {
        url = '/' + url;
      }
      // 添加axiosBasePath前缀
      url = axiosBasePath + url;

      xhr.open('GET', `${url}?attId=${this.fileData.id}`, true);
      xhr.responseType = 'arraybuffer'; // 使用arraybuffer而不是blob

      // 使用getToken获取token
      const token = getToken();
      if (token) {
        xhr.setRequestHeader('Authorization', token);
      }

      // 添加Request-Origin-App头信息，与request.js保持一致
      xhr.setRequestHeader('Request-Origin-App', 'NETFM3_PC');

      xhr.onload = () => {
        if (xhr.status === 200) {
          try {
            // 检查响应是否为JSON格式（可能是错误信息）
            if (xhr.response instanceof ArrayBuffer && xhr.response.byteLength > 0) {
              // 尝试检测是否为JSON响应
              let isJson = false;
              const firstBytes = new Uint8Array(xhr.response.slice(0, 4));
              // 检查是否以 { 或 [ 开头（JSON格式）
              if (firstBytes[0] === 123 || firstBytes[0] === 91) {
                isJson = true;
              }

              if (isJson) {
                // 尝试将ArrayBuffer转换为JSON
                const decoder = new TextDecoder('utf-8');
                const jsonText = decoder.decode(xhr.response);
                try {
                  const jsonData = JSON.parse(jsonText);
                  console.error('服务器返回了JSON而不是图片数据:', jsonData);
                  this.$message.error('获取图片失败: 服务器返回了非图片数据');
                  this.loading = false;
                  this.previewFailed = true;
                  return;
                } catch (e) {
                  // 不是有效的JSON，继续处理为图片
                  console.log('不是有效的JSON，继续处理为图片');
                }
              }

              // 将arraybuffer转换为blob
              const blob = new Blob([xhr.response]);

              // 创建blob URL
              this.imageUrl = URL.createObjectURL(blob);
              this.loading = false;
            } else {
              throw new Error('无效的图片数据');
            }
          } catch (error) {
            console.error('处理图片数据时出错:', error);

            // 尝试使用传统的base64方法
            try {
              // 将arraybuffer转换为base64
              const uInt8Array = new Uint8Array(xhr.response);
              let i = uInt8Array.length;
              const binaryString = new Array(i);
              while (i--) {
                binaryString[i] = String.fromCharCode(uInt8Array[i]);
              }
              const data = binaryString.join('');
              const base64 = window.btoa(data);

              // 根据文件扩展名确定MIME类型
              let mimeType = 'image/jpeg'; // 默认MIME类型
              const fileName = this.fileName.toLowerCase();
              if (fileName.endsWith('.png')) {
                mimeType = 'image/png';
              } else if (fileName.endsWith('.gif')) {
                mimeType = 'image/gif';
              } else if (fileName.endsWith('.svg')) {
                mimeType = 'image/svg+xml';
              } else if (fileName.endsWith('.webp')) {
                mimeType = 'image/webp';
              }

              // 创建base64 URL
              this.imageUrl = `data:${mimeType};base64,${base64}`;
              this.loading = false;
            } catch (e) {
              console.error('备用方法也失败:', e);
              this.$message.error('获取图片失败: 无法处理图片数据');
              this.loading = false;
              this.previewFailed = true;
            }
          }
        } else {
          this.$message.error(`获取图片失败: 状态码 ${xhr.status}`);
          this.loading = false;
          this.previewFailed = true;
        }
      };

      xhr.onerror = (error) => {
        console.error('请求图片时出错:', error);
        this.$message.error('获取图片失败: 网络错误');
        this.loading = false;
        this.previewFailed = true;
      };

      xhr.send();
    },

    // 下载文件
    downloadFile() {
      if (!this.fileData.id || !this.previewApi) {
        this.$message.error('下载参数不完整');
        return;
      }

      this.loading = true;

      // 使用XMLHttpRequest直接获取文件流，避免触发自动下载
      const xhr = new XMLHttpRequest();

      // 处理URL，按照request.js中的方式添加axiosBasePath前缀
      let url = this.previewApi;
      // 确保URL以/开头
      if (!url.startsWith('/')) {
        url = '/' + url;
      }
      // 添加axiosBasePath前缀
      url = axiosBasePath + url;

      xhr.open('GET', `${url}?attId=${this.fileData.id}`, true);
      xhr.responseType = 'blob'; // 使用blob而不是arraybuffer

      // 使用getToken获取token
      const token = getToken();
      if (token) {
        xhr.setRequestHeader('Authorization', token);
      }

      // 添加Request-Origin-App头信息，与request.js保持一致
      xhr.setRequestHeader('Request-Origin-App', 'NETFM3_PC');

      xhr.onload = () => {
        if (xhr.status === 200) {
          try {
            // 创建blob URL
            const blob = new Blob([xhr.response]);
            const url = URL.createObjectURL(blob);

            // 创建一个a标签用于下载
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = this.fileName;
            document.body.appendChild(a);
            a.click();

            // 释放blob URL
            setTimeout(() => {
              URL.revokeObjectURL(url);
              document.body.removeChild(a);
            }, 100);

            this.loading = false;
            this.$message.success('文件下载成功');
          } catch (error) {
            console.error('处理下载数据时出错:', error);
            this.$message.error('下载文件失败: 无法处理文件数据');
            this.loading = false;
          }
        } else {
          this.$message.error(`下载文件失败: 状态码 ${xhr.status}`);
          this.loading = false;
        }
      };

      xhr.onerror = (error) => {
        console.error('请求文件时出错:', error);
        this.$message.error('下载文件失败: 网络错误');
        this.loading = false;
      };

      xhr.send();
    }
  }
};
</script>

<style lang="scss" scoped>
/* 图片预览样式 */
.image-preview-container {
  display: flex;
  flex-direction: column;
}

.dialog-title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.dialog-title-text {
  font-size: 16px;
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 70%;
}

.dialog-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-button {
  margin: 0;
}

.close-icon {
  font-size: 20px;
  color: #909399;
  cursor: pointer;
  padding: 5px;
  transition: color 0.3s;

  // &:hover {
  //   color: #409EFF;
  // }
}

.image-preview-content {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px 0;
  min-height: 400px; /* 设置最小高度，确保加载时有足够的空间 */
}

.preview-image {
  max-width: 100%;
  max-height: 60vh;
  object-fit: contain;
}

.preview-error {
  color: #f56c6c;
  font-size: 16px;
}

::v-deep .image-preview-dialog .el-dialog {
  min-width: 30%;
  max-width: 80%;
  margin-top: 15vh !important; /* 调整弹窗位置，默认是30vh，改为15vh使其更居中 */
}

::v-deep .image-preview-dialog .el-dialog__header {
  padding: 15px 20px;
}

::v-deep .image-preview-dialog .el-dialog__headerbtn {
  top: 15px;
  right: 20px;
}

::v-deep .image-preview-dialog .el-dialog__body {
  padding: 10px 20px;
  overflow: hidden;
}
</style>
