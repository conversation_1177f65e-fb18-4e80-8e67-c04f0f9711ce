import { get, postJson, getJson } from "@/utils/axios";

const commandQueryUrl = "backbone/workflow/queryGGLInfo"; // 获取TaskId 信息
const refreshCommandQueryUrl = "backbone/workflow/queryGGLInfo"; // 指令查询 长连接接口
const queryHistoryUrl = "backbone/workflow/queryGglHistoryTable"; //指令历史查询
const queryHistoryDetailUrl = "backbone/workflow/queryGglInfoTableByTaskId"; //指令历史详情查询
const commandManualQueryUrl = "backbone/workflow/ggl/queryGGlInfoByCard";
const ggInfoTableUrl = "backbone/workflow/ggl/queryGglInfoTableByTaskId";
const cardPortUrl = "backbone/workflow/ggl/queryCardOrPortInfo";
const areaUrl = "commonDict/info/faultArea";
const neUrl = "backbone/workflow/ggl/queryNeInfo";

const apiCommandQuery = params =>
  get(commandQueryUrl + "?sheetNo=" + params.sheetNo, { timeout: 0 });
const apiRefreshCommandQuery = params =>
  postJson(refreshCommandQueryUrl, params, { timeout: 20000 });
const apiQueryHistory = params =>
  postJson(queryHistoryUrl, params, { timeout: 0 });
const apiQueryHistoryDetail = params =>
  get(queryHistoryDetailUrl + "?taskId=" + params.taskId);
const apiCommandManualQuery = params =>
  postJson(commandManualQueryUrl, params, { timeout: 0 });
const apiQueryGglInfoTableByTaskId = params =>
  get(ggInfoTableUrl + "?taskId=" + params.taskId);
const apiQueryCardOrPortInfo = params => postJson(cardPortUrl, params);
const apiArea = params => getJson(areaUrl, params);
const apiNeOptions = params => postJson(neUrl, params);
export {
  apiCommandQuery,
  apiQueryHistory,
  apiRefreshCommandQuery,
  apiQueryHistoryDetail,
  apiCommandManualQuery,
  apiQueryGglInfoTableByTaskId,
  apiQueryCardOrPortInfo,
  apiArea,
  apiNeOptions,
};
