<template>
  <div class="input-tag" :class="[selectSize ? 'el-input--' + selectSize : '']">
    <div
      ref="tags"
      v-if="selected.length"
      class="input__tags"
      :style="{ width: `${tagsWidth}px` }"
    >
      <div ref="tagsFlex" v-popover:tagsPopover>
        <el-tag
          :closable="!selectDisabled"
          :size="collapseTagSize"
          :type="tagType"
          @close="deleteTag(selected[0])"
          disable-transitions
          :style="{ maxWidth: selected.length > 1 ? 'calc(100% - 50px)' : '' }"
        >
          <span class="el-select__tags-text">{{
            isObjectArr
              ? selected[0][labelKey] || selected[0][valueKey]
              : selected[0]
          }}</span>
        </el-tag>
        <el-tag
          v-if="selected.length > 1"
          :closable="false"
          :size="collapseTagSize"
          :type="tagType"
          disable-transitions
        >
          <span class="el-select__tags-text">+ {{ selected.length - 1 }}</span>
        </el-tag>
      </div>
      <el-popover
        ref="tagsPopover"
        :disabled="selected.length < 2"
        trigger="hover"
        :width="popoverWidth"
        popper-class="input-tag-popover"
        placement="bottom-start"
      >
        <slot name="popoverContent" :data="selected.slice(1)">
          <div><el-button type="text" @click="onClearAll">清空</el-button></div>
          <div
            v-for="(item, i) in selected.slice(1)"
            :key="'popTag' + i"
            class="pop-tag-item"
          >
            <span class="pop-tag-lable text-truncate" :title="item">{{
              isObjectArr ? item[labelKey] || item[valueKey] : item
            }}</span>
            <el-button
              type="text"
              icon="el-icon-close"
              :disabled="selectDisabled"
              @click="deleteTag(item)"
            ></el-button>
          </div>
        </slot>
      </el-popover>
    </div>
    <el-input
      ref="reference"
      class="__input"
      v-model="query"
      :readonly="readonly"
      @change="onChange"
      :style="{ width: `calc(100% - ${tagsWidth}px)` }"
    >
      <template v-slot:append>
        <slot name="append">
          <el-button
            icon="el-icon-user"
            :disabled="disabled"
            @click="onSelect"
          ></el-button>
        </slot>
      </template>
    </el-input>
  </div>
</template>

<script>
import {
  addResizeListener,
  removeResizeListener,
} from "element-ui/src/utils/resize-event";
import { isEqual, isPlainObject } from "lodash";

export default {
  name: "InputTag",
  inheritAttrs: false,
  inject: {
    elForm: {
      default: "",
    },

    elFormItem: {
      default: "",
    },
  },
  props: {
    value: {
      type: Array,
      default: () => [],
      require: true,
    },
    valueKey: {
      type: String,
      default: "value",
    },
    labelKey: {
      type: String,
      default: "label",
    },
    disabled: Boolean,
    readonly: Boolean,
    tagType: {
      type: String,
      default: "primary",
    },
    popoverWidth: {
      type: [String, Number],
      default: 200,
    },
  },
  data() {
    return {
      selected: [],
      query: "",
      tagsWidth: 0,
      isWrap: false,
    };
  },
  computed: {
    _elFormItemSize() {
      return (this.elFormItem || {}).elFormItemSize;
    },
    selectDisabled() {
      return this.disabled || (this.elForm || {}).disabled;
    },
    collapseTagSize() {
      return ["small", "mini"].indexOf(this.selectSize) > -1 ? "mini" : "small";
    },
    selectSize() {
      return this.size || this._elFormItemSize || (this.$ELEMENT || {}).size;
    },
    isObjectArr() {
      return this.value.every(item => isPlainObject(item));
    },
  },
  watch: {
    value(newValue = [], oldValue) {
      if (!isEqual(newValue, oldValue)) {
        this.$emit("input", newValue);
      }
      this.selected = [...newValue];
      this.handleResize();
    },
  },
  created() {},
  mounted() {
    addResizeListener(this.$el, this.handleResize);
  },
  beforeDestroy() {
    if (this.$el && this.handleResize)
      removeResizeListener(this.$el, this.handleResize);
  },
  methods: {
    resetInputWidth() {
      let tagsWidth =
        this.$refs?.tagsFlex?.getBoundingClientRect?.().width || 0;
      const elWidth = this.$el?.getBoundingClientRect()?.width || 0;
      const cha = this.readonly ? 50 : 86;
      if (elWidth - tagsWidth < cha || this.readonly) {
        tagsWidth = Math.ceil(elWidth - cha);
      }
      if (this.selected.length == 0) {
        tagsWidth = 0;
      }
      this.tagsWidth = tagsWidth;
      this.isWrap = true;
    },
    handleResize() {
      this.isWrap = false;
      this.$nextTick(() => {
        this.resetInputWidth();
      });
    },
    onChange() {
      const resultStr = this.query;
      this.selected.push(resultStr);
      this.$emit("input", this.selected);
      this.query = "";
      this.handleResize();
    },
    deleteTag(tag) {
      let index = -1;
      if (this.isObjectArr) {
        index = this.selected.findIndex(
          item => item[this.valueKey] == tag[this.valueKey]
        );
      } else {
        index = this.selected.indexOf(tag);
      }
      if (index > -1 && !this.selectDisabled) {
        this.selected.splice(index, 1);
        this.$emit("input", this.selected);
        this.handleResize();
        this.$emit("remove-tag", tag);
      }
    },
    onClearAll() {
      this.selected = [];
      this.$emit("input", this.selected);
      this.$emit("clear");
      this.handleResize();
    },
    onSelect() {
      this.$emit("select", this.selected);
    },
  },
};
</script>

<style lang="scss" scoped>
.input-tag {
  position: relative;
  // display: flex;
  // flex-wrap: nowrap;
  white-space: nowrap;
  overflow: hidden;
  @include themify() {
    background-color: themed("$--input-background-color");
    border: themed("$--input-border");
    border-radius: themed("$--input-border-radius");
    &:hover,
    &:focus {
      border-color: themed("$--input-border-color-hover");
    }
  }
  .input__tags {
    display: inline-block;
    > div {
      display: flex;
      height: 100%;
      align-items: center;
      flex-wrap: nowrap;
    }
    ::v-deep {
      .el-tag {
        box-sizing: border-box;
        border-color: transparent;
        margin: 2px 0 2px 6px;
        display: flex;
        max-width: 100%;
        align-items: center;
      }
    }
  }
  > .__input {
    ::v-deep {
      .el-input__inner {
        border: none;
        outline: none;
        &[readonly="readonly"] {
          padding: 0;
        }
      }
      .el-input-group__append {
        border: none;
        @include themify() {
          border-left: themed("$--input-border");
        }
      }
    }
  }
}
</style>

<style lang="scss">
.input-tag-popover {
  max-height: 50vh;
  overflow-y: auto;
  .pop-tag-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    > span {
      display: inline-block;
    }
  }
}
</style>
