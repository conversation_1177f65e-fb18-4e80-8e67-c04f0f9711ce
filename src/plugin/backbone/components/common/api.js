import {
  getJson,
  postJson
} from "@/utils/axios";

const orgTreeLazyUrl = "backbone/tree/orgTreeNew"; // 组织树的懒加载接口地址  新接口
const userTreeUrl = "backbone/tree/userTreeNew"; //用户树的查询 新接口
const userFilterUrl = "backbone/tree/userFilter"; //用户树过滤的接口 通用
const userFilterByCurrOrgIdUrl = "backbone/tree/userFilterByCurrOrgId"; //用户树过滤的接口 转办专用
const redployUserTreeUrl = "backbone/tree/searchRedeployUser";
const redployOrgTreeUrl = "backbone/tree/searchRedeployOrg";
const contactUserUrl = "backbone/tree/contactUsers";
const saveContactUserUrl = "backbone/tree/saveContactUsers";
const delContactUserUrl = "backbone/tree/delContactUsers";
const redeployUserTreeProvinceUrl = "commonprovince/tree/searchRedeployUser";
const redeployOrgTreeProvinceUrl = "commonprovince/tree/searchRedeployOrg";

const apiGetOrgLazyTree = params =>
  getJson(orgTreeLazyUrl, params, {
    timeout: 0
  });
const apiGetUserTree = params => getJson(userTreeUrl, params, {
  timeout: 0
});
const apiRedeployUserTree = params =>
  getJson(redployUserTreeUrl, params, {
    timeout: 0
  });
const apiRedeployOrgTree = params =>
  getJson(redployOrgTreeUrl, params, {
    timeout: 0
  });
const apiUserFilter = params => postJson(userFilterUrl, params, {
  timeout: 0
});

const userFilterByCurrOrgIdFilter = params => postJson(userFilterByCurrOrgIdUrl, params, { timeout: 0 });
const apiGetContactUser = params =>
  postJson(contactUserUrl, params, {
    timeout: 0
  });
const apiSaveContactUser = params =>
  postJson(saveContactUserUrl, params, {
    timeout: 0
  });
const apiDelContactUser = params =>
  postJson(delContactUserUrl, params, {
    timeout: 0
  });
const apiRedeployUserTreeByProvince = params =>
  getJson(redeployUserTreeProvinceUrl, params, {
    timeout: 0
  });
const apiRedeployOrgTreeByProvince = params =>
  getJson(redeployOrgTreeProvinceUrl, params, {
    timeout: 0
  });

const apiGetTurnToContactUser = params =>
  postJson(`backbone/tree/contactUsersByProCode`, params, {
    timeout: 0
  });
export {
  apiGetOrgLazyTree,
  apiGetUserTree,
  apiUserFilter,
  apiRedeployUserTree,
  apiRedeployOrgTree,
  apiGetContactUser,
  apiSaveContactUser,
  apiDelContactUser,
  apiRedeployUserTreeByProvince,
  apiRedeployOrgTreeByProvince,
  apiGetTurnToContactUser,
  userFilterByCurrOrgIdFilter
};
