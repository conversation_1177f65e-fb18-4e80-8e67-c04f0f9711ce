<template>
  <el-dialog :title="title" :visible="visible" :close-on-click-modal="false" :close-on-press-escape="false"
    width="600px" :before-close="closeFunc" top="5vh" :append-to-body="appendToBody">
    <div style="height: 480px">
      <el-tabs type="border-card" v-model="activeName">
        <el-tab-pane v-if="showOrgsTree" label="组织树" name="orgs">
          <el-tree v-if="initOrgsTree" ref="orgsTree" show-checkbox check-strictly node-key="id" :props="orgsProps"
            class="diaOrg" style="height: 405px; overflow-y: auto" :load="loadLazyFuncOrg"
            :default-checked-keys="orgDefaultChecked" lazy v-loading="orgsTreeLoading"
            :render-content="orgRenderContent" @check="itCloudCheckLimit">
          </el-tree></el-tab-pane>
        <el-tab-pane v-if="showUsersTree" label="人员树" name="users">
          <el-input v-model="userFilterText" clearable>
            <el-button type="info" slot="append" @click="userFilter" icon="el-icon-search" title="搜索"></el-button>
            <el-button type="info" slot="append" icon="el-icon-close" title="取消搜索" @click="closeSearch"></el-button>
          </el-input>
          <el-table v-if="isShowFilterUserTable" :data="userData" border :show-header="false" style="
              width: 100%;
              margin-top: 5px;
              height: 400px;
              overflow-y: auto;
              max-height: 450px;
            " @selection-change="handleUserSelectionChange" v-loading="userFilterTableLoading">
            <el-table-column type="selection" width="55"> </el-table-column>
            <el-table-column prop="trueName" label="姓名" width="100"></el-table-column>
            <el-table-column prop="mobilePhone" label="手机号" width="120"></el-table-column>
            <el-table-column label="所属组织">
              <template slot-scope="scope">
                {{ scope.row.orgEntity.fullOrgName }}
              </template>
            </el-table-column>
          </el-table>
          <el-tree class="diaUsers" v-if="!isShowFilterUserTable" ref="usersTree" show-checkbox node-key="id"
            :props="usersProps" style="height: 405px; overflow-y: auto" :load="loadLazyFunc"
            :default-checked-keys="userDefaultChecked" lazy v-loading="userTreeLoading"
            :render-content="userRenderContent">
            <!-- :render-content="userRenderContent" -->
          </el-tree>
        </el-tab-pane>
        <!-- v-if="showContactOrgTab" -->
        <el-tab-pane v-if="showContactOrgTab" label="常用群组" name="contactOrg">
          <el-table :data="contactOrgData" border style="
              width: 100%;
              margin-top: 5px;
              height: 400px;
              overflow-y: auto;
              max-height: 450px;
            " ref="contactOrgTable" @selection-change="contactOrgSelectionChange" v-loading="contactOrgTableLoading">
            <el-table-column type="selection" width="55"> </el-table-column>
            <el-table-column label="组织名称">
              <template slot-scope="scope">
                {{ scope.row.fullOrgName }}
                <base-icon icon-class="collect_solid" style="cursor: pointer" @click="
                  removeToCommonContact(null, scope.row, 'org', 'contactList')
                  " />
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <!-- v-if="showContactUserTab" -->
        <el-tab-pane v-if="showContactUserTab" label="常用联系人" name="contactUser">
          <el-table :data="contactUserData" border style="
              width: 100%;
              margin-top: 5px;
              height: 400px;
              overflow-y: auto;
              max-height: 450px;
            " ref="contactUserTable" @selection-change="contactUserSelectionChange"
            v-loading="contactUserTableLoading">
            <el-table-column type="selection" width="55"> </el-table-column>
            <el-table-column prop="trueName" label="姓名"></el-table-column>
            <el-table-column label="组织名称">
              <template slot-scope="scope">
                {{ scope.row.orgEntity.fullOrgName }}
                <base-icon icon-class="collect_solid" style="cursor: pointer" @click="
                  removeToCommonContact(
                    null,
                    scope.row,
                    'user',
                    'contactList'
                  )
                  " />
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="onSave">确 定</el-button>
      <el-button @click="closeFunc">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import cloneDeep from "lodash/cloneDeep";
import { mapGetters } from "vuex";
import {
  apiRedeployUserTree,
  apiRedeployOrgTree,
  apiUserFilter,
  apiGetContactUser,
  apiSaveContactUser,
  apiDelContactUser,
  apiGetUserTree,
  apiGetOrgLazyTree,
  apiRedeployUserTreeByProvince,
  apiRedeployOrgTreeByProvince,
  apiGetTurnToContactUser,
  userFilterByCurrOrgIdFilter
} from "./api";

export default {
  name: "DiaTissueTree",
  props: {
    title: String,
    visible: Boolean,
    appendToBody: Boolean,
    showOrgsTree: {
      type: Boolean,
      default: true,
    },
    showUsersTree: {
      type: Boolean,
      default: true,
    },
    multipleSelectEnable: {
      type: Boolean,
      default: true,
    },
    showContactUserTab: {
      type: Boolean,
      default: false,
    },
    showContactOrgTab: {
      type: Boolean,
      default: false,
    },
    userAttribution: {
      type: String,
      default: "Group", //默认集团，枚举：Province 省份，Region 地市，County 区县
    },
    singleSelectTip: String,
    orgDefaultChecked: Array,
    userDefaultChecked: Array,
    professionalType: String,
    checkUserLimit: Number,
    reassignmentType: {
      type: String,
      default: "Group", //默认值 ，特殊的为省分转派 "province" else判断
    },
    currentIsTurnTo: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      activeName: this.showOrgsTree ? "orgs" : "users",
      orgsCommonData: [],
      orgsProps: {
        children: "children",
        label: "name",
        isLeaf: "isLeaf",
      },
      usersProps: {
        children: "children",
        label: "name",
        isLeaf: "isLeaf",
      },
      userFilterText: "",
      userData: [],
      isShowFilterUserTable: false,
      multipleSelectionsUser: [],
      userFilterTableLoading: false,
      orgsTreeLoading: false,
      userTreeLoading: false,
      userInfoData: [],

      contactUserData: [],
      contactSelectionUser: [],
      contactUserTableLoading: false,
      contactOrgData: [],
      contactSelectionOrg: [],
      contactOrgTableLoading: false,
      initOrgsTree: true,
      currentIsTurnToOneOrgLoading: true,
      currentIsTurnToOneUsrLoading: true,
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  watch: {
    showOrgsTree(newVal) {
      if (newVal) {
        this.activeName = "orgs";
      } else {
        this.activeName = "users";
      }
    },
  },
  mounted() {
    this.userInfoData = JSON.parse(this.userInfo.attr2);
    this.getContactUserData();
  },
  methods: {
    closeFunc() {
      this.$emit("update:visible", false);
      this.$refs?.orgsTree?.setCheckedKeys?.([]);
      this.$refs?.usersTree?.setCheckedKeys?.([]);
      this.closeSearch();
      this.contactSelectionUser = [];
      this.contactSelectionOrg = [];
      this.$refs?.contactUserTable?.clearSelection();
      this.$refs?.contactOrgTable?.clearSelection();
    },
    onSave() {
      let self = this;
      let orgsChecked =
        cloneDeep(self.$refs?.orgsTree?.getCheckedNodes?.()) ?? [];
      let usersChecked =
        cloneDeep(self.$refs?.usersTree?.getCheckedNodes?.()) ?? [];
      if (this.checkUserLimit && usersChecked.length > this.checkUserLimit) {
        this.$message.info("最多选择10人,请重新选择！");
        return;
      }
      let selectionUser = self.multipleSelectionsUser;
      let contactSelectionUser = self.contactSelectionUser;
      let contactSelectionOrg = self.contactSelectionOrg;
      let conflict = false;
      for (let index = 0; index < orgsChecked.length; index++) {
        const element = orgsChecked[index];
        if (
          usersChecked.some(item => {
            const res = self.getBranchOrg(item);
            if (res.headquarters) {
              return item.orgId == element.id;
            } else {
              return res.secondId == element.id;
            }
          })
        ) {
          conflict = true;
          self.$message({
            message: `组织树已选“${element.name}”，人员树“${element.name}”下不能选择人员！`,
            type: "warning",
            showClose: true,
            duration: 6000,
          });
          break;
        }
      }
      if (conflict) {
        return false;
      }

      //单选提示（建单主送、次送；转派）--hq
      if (
        this.multipleSelectEnable == false &&
        orgsChecked.length + usersChecked.length + selectionUser.length + contactSelectionUser.length + contactSelectionOrg.length != 1
      ) {
        this.$message.error(this.singleSelectTip);
        return false;
      }
      this.$emit("on-save", {
        orgsChecked,
        usersChecked,
        selectionUser,
        contactSelectionUser,
        contactSelectionOrg,
      });
      this.closeFunc();
    },
    getBranchOrg(user) {
      let self = this;
      const userNode = self.$refs.usersTree.getNode(user);
      let level = userNode.level;
      let parent;
      if (this.userAttribution == "Group") {
        while (level > 1) {
          parent = parent?.parent ?? userNode.parent;
          level--;
        }
        if (userNode.level == 1 || /集团|大区|其他/.test(parent.data.name)) {
          return {
            headquarters: true,
          };
        } else {
          return {
            headquarters: false,
            secondId: parent.data.id,
          };
        }
      } else {
        while (level > 2) {
          parent = parent?.parent ?? userNode.parent;
          level--;
        }
        if (userNode.level == 2 || parent.data.name.includes("本部")) {
          return {
            headquarters: true,
          };
        } else {
          return {
            headquarters: false,
            secondId: parent.data.id,
          };
        }
      }
    },
    loadLazyFunc(node, resolve) {
      let param = {
        level: node.level + 1,
        nodeId: node?.data?.id ?? "",
      };

      if (this.currentIsTurnTo) {
        param = {
          level: 3,
          nodeId: node?.data?.id ?? "141689",
        };
        if (this.currentIsTurnToOneUsrLoading) {
          this.currentIsTurnToOneUsrLoading = false;
          resolve([{
            "id": "141689",
            "name": "联通数字科技有限公司",
            "code": null,
            "isLeaf": false,
            "disabled": null,
            "parentOrgId": null,
            "orgId": null,
            "orgName": null,
            "mobilePhone": null,
            "isCommonContanct": null
          }])
          return;
        }
      }
      if (0 == node.level) {
        this.userTreeLoading = true;
      }
      if (this.userAttribution == "Group") {
        apiGetUserTree(param)
          .then(res => {
            if (res.status == 0) {
              let treeData = res?.data?.data ?? [];
              return resolve(treeData);
            }
          })
          .catch(e => {
            console.log("报错了，", e);
            return resolve([]);
          })
          .finally(() => {
            this.userTreeLoading = false;
          });
      } else {
        if (this.reassignmentType == "Group") {
          apiRedeployUserTree(param)
            .then(res => {
              if (res.status == 0) {
                let treeData = res?.data?.data ?? [];
                return resolve(treeData);
              }
            })
            .catch(e => {
              console.log("报错了，", e);
              return resolve([]);
            })
            .finally(() => {
              this.userTreeLoading = false;
            });
        } else {
          apiRedeployUserTreeByProvince(param)
            .then(res => {
              if (res.status == 0) {
                let treeData = res?.data?.data ?? [];
                return resolve(treeData);
              }
            })
            .catch(e => {
              console.log("报错了，", e);
              return resolve([]);
            })
            .finally(() => {
              this.userTreeLoading = false;
            });
        }
      }
    },
    loadLazyFuncOrg(node, resolve) {
      let param = {
        level: node.level + 1,
        nodeId: node?.data?.id ?? "",
      };
      if (this.currentIsTurnTo) {
        param = {
          level: 3,
          nodeId: node?.data?.id ?? "141689",
        };
        if (this.currentIsTurnToOneOrgLoading) {
          this.currentIsTurnToOneOrgLoading = false;
          resolve([{
            "id": "141689",
            "name": "联通数字科技有限公司",
            "code": null,
            "isLeaf": false,
            "disabled": null,
            "parentOrgId": null,
            "orgId": null,
            "orgName": null,
            "mobilePhone": null,
            "isCommonContanct": null
          }])
          return;
        }
      }

      if (0 == node.level) {
        this.orgsTreeLoading = true;
      }
      if (this.userAttribution == "Group") {
        apiGetOrgLazyTree(param)
          .then(res => {
            if (res.status == 0) {
              let treeData = res?.data?.data ?? [];
              return resolve(treeData);
            }
          })
          .catch(e => {
            console.log("报错了，", e);
            return resolve([]);
          })
          .finally(() => {
            this.orgsTreeLoading = false;
          });
      } else {
        if (this.reassignmentType == "Group") {
          apiRedeployOrgTree(param)
            .then(res => {
              if (res.status == 0) {
                let treeData = res?.data?.data ?? [];
                return resolve(treeData);
              }
            })
            .catch(e => {
              console.log("报错了，", e);
              return resolve([]);
            })
            .finally(() => {
              this.orgsTreeLoading = false;
            });
        } else {
          apiRedeployOrgTreeByProvince(param)
            .then(res => {
              if (res.status == 0) {
                let treeData = res?.data?.data ?? [];
                return resolve(treeData);
              }
            })
            .catch(e => {
              console.log("报错了，", e);
              return resolve([]);
            })
            .finally(() => {
              this.orgsTreeLoading = false;
            });
        }
      }
    },
    userFilter() {
      if (this.userFilterText) {
        this.isShowFilterUserTable = true;
        this.userFilterTableLoading = true;
        if(this.currentIsTurnTo){
          userFilterByCurrOrgIdFilter({
            userName: this.userFilterText,
            orgId: '141689',
          })
            .then(res => {
              if (res.code == "200") {
                this.userData = res?.data ?? [];
              }
              this.userFilterTableLoading = false;
            })
            .catch(error => {
              console.log(error);
              this.userFilterTableLoading = false;
            });
        }else{
          apiUserFilter({
            userName: this.userFilterText,
            orgId: this.userInfoData.orgInfo.orgId,
          })
            .then(res => {
              if (res.code == "200") {
                this.userData = res?.data ?? [];
              }
              this.userFilterTableLoading = false;
            })
            .catch(error => {
              console.log(error);
              this.userFilterTableLoading = false;
            });
        }
        
      } else {
        this.$message.warning("人员名称不能为空");
      }
    },

    handleUserSelectionChange(selection) {
      this.multipleSelectionsUser = selection;
    },
    closeSearch() {
      this.isShowFilterUserTable = false;
      this.userFilterText = "";
      this.multipleSelectionsUser = [];
      if (this.currentIsTurnTo) {
        this.currentIsTurnToOneUsrLoading = true;
      }
    },
    getContactUserData() {
      this.contactUserTableLoading = true;
      this.contactOrgTableLoading = true;
      if (this.currentIsTurnTo) {
        let param = {
          userName: this.userInfoData.userName,
          proCode: 869
        };
        apiGetTurnToContactUser(param)
          .then(res => {
            if (res.code == 200) {
              this.contactUserData = res?.data?.user ?? [];
              this.contactOrgData = res?.data?.org ?? [];
            }
          })
          .catch(error => {
            console.log(error);
          })
          .finally(() => {
            this.contactUserTableLoading = false;
            this.contactOrgTableLoading = false;
          });
      } else {
        let param = {
          userName: this.userInfoData.userName,
        };
        apiGetContactUser(param)
          .then(res => {
            if (res.code == 200) {
              this.contactUserData = res?.data?.user ?? [];
              this.contactOrgData = res?.data?.org ?? [];
            }
          })
          .catch(error => {
            console.log(error);
          })
          .finally(() => {
            this.contactUserTableLoading = false;
            this.contactOrgTableLoading = false;
          });

      }

    },
    contactUserSelectionChange(selection) {
      this.contactSelectionUser = selection;
    },
    contactOrgSelectionChange(selection) {
      this.contactSelectionOrg = selection;
    },
    orgRenderContent(h, { node, data, store }) {
      if (node.level > 1) {
        if (null == data.isCommonContanct || data.isCommonContanct == "0") {
          return (
            <span class="custom-tree-node">
              <span>{node.label}</span>
              <span style="margin-left:3px">
                <base-icon
                  icon-class="collect_hollow"
                  on-click={() => this.addToCommonContact(node, data, "org")}
                />
              </span>
            </span>
          );
        } else {
          return (
            <span class="custom-tree-node">
              <span>{node.label}</span>
              <span style="margin-left:3px">
                <base-icon
                  icon-class="collect_solid"
                  on-click={() =>
                    this.removeToCommonContact(node, data, "org", "tissueTree")
                  }
                />
              </span>
            </span>
          );
        }
      } else {
        return (
          <span class="custom-tree-node">
            <span>{node.label}</span>
          </span>
        );
      }
    },
    userRenderContent(h, { node, data, store }) {
      if (node.isLeaf) {
        if (null == data.isCommonContanct || data.isCommonContanct == "0") {
          return (
            <span class="custom-tree-node">
              <span>{node.label}</span>
              <span style="margin-left:3px">
                <base-icon
                  icon-class="collect_hollow"
                  on-click={() => this.addToCommonContact(node, data, "user")}
                />
              </span>
            </span>
          );
        } else {
          return (
            <span class="custom-tree-node">
              <span>{node.label}</span>
              <span style="margin-left:3px">
                <base-icon
                  icon-class="collect_solid"
                  on-click={() =>
                    this.removeToCommonContact(node, data, "user", "tissueTree")
                  }
                />
              </span>
            </span>
          );
        }
      } else {
        return (
          <span class="custom-tree-node">
            <span>{node.label}</span>
          </span>
        );
      }
    },
    addToCommonContact(node, data, type) {
      apiSaveContactUser({
        operateUser: this.userInfoData.userName,
        userNames: type == "user" ? data.id : "",
        orgId: type == "org" ? data.id : "",
        isOrgOrUser: type, //区分是增加常用联系人 user 还是常用群组 org
      })
        .then(res => {
          if (res.code == 200) {
            this.$set(data, "isCommonContanct", "1");
            this.getContactUserData();
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    removeToCommonContact(node, data, type, listType) {
      let contactValue;
      if (listType == "contactList") {
        if (type == "user") {
          contactValue = data.userName;
        } else if (type == "org") {
          contactValue = data.orgId + "";
        }
      } else {
        contactValue = data.id;
      }
      apiDelContactUser({
        operateUser: this.userInfoData.userName,
        userNames: type == "user" ? contactValue : "",
        orgId: type == "org" ? contactValue : "",
        isOrgOrUser: type, //区分是增加常用联系人 还是常用群组
      })
        .then(res => {
          if (res.code == 200) {
            this.$set(data, "isCommonContanct", "0");
            this.getContactUserData();
            if (type == "user") {
              this.showUsersTree = false;
              this.$nextTick(() => {
                this.showUsersTree = true;
              });
            } else if (type == "org") {
              this.initOrgsTree = false;
              this.$nextTick(() => {
                this.initOrgsTree = true;
              });
            }
          }
        })
        .catch(error => {
          console.log(error);
        });
    },
    //it云专业（组织树限制选一个组织）
    itCloudCheckLimit() {
      if (this.professionalType == "IT云") {
        const getList = this.$refs.orgsTree.getCheckedNodes();
        if (getList.length > 1) {
          this.$message.warning("请只选择一个组织");
          this.$refs?.orgsTree?.setCheckedKeys([]);
        }
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.diaOrg {
  &.el-tree ::v-deep {
    padding-top: 15px;
    padding-left: 10px;

    // 不可全选样式
    .el-tree-node {
      .is-leaf+.el-checkbox .el-checkbox__inner {
        display: inline-block;
      }

      .el-checkbox .el-checkbox__inner {
        display: inline-block !important;
      }
    }
  }
}

.diaUsers {
  &.el-tree ::v-deep {
    padding-top: 15px;
    padding-left: 10px;

    // 不可全选样式
    .el-tree-node {
      .is-leaf+.el-checkbox .el-checkbox__inner {
        display: inline-block;
      }

      .el-checkbox .el-checkbox__inner {
        display: none;
      }
    }
  }
}
</style>
