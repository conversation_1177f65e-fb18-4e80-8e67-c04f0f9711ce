module.exports = {
  //端口号
  port: 8090,

  //项目名称
  title: "管理系统",

  //项目名称英文
  titleEn: "Smart",

  //项目logo
  logo: "framework-text",
  logoMini: "framework",
  //topmenu布局是否显示logo
  topShowLogo: true,
  //sidemenu布局是否显示logo
  sideShowLogo: true,

  //自定义登录页
  //示例 () => import("./")
  loginFile: null,

  //登录页背景图
  //为空时使用默认背景图
  //示例 () => require("./...")
  loginBgUrl: null,

  //项目版权信息
  copyright: "版权声明 Copyright@2020版权所有",

  //layout - 整体布局方式 ['sidemenu', 'topmenu'] 两种布局
  layout: "sidemenu",

  //是否只有一种整体布局方式
  singleLayout: false,

  //固定左侧菜单栏: boolean
  fixedAside: true,

  //折叠左侧菜单栏: boolean
  collapseAside: true,

  //固定 Header: boolean
  fixedHeader: true,

  //topmenu布局是否显示面包屑
  topShowBreadcrumb: false,

  //sidemenu布局是否显示面包屑
  sideShowBreadcrumb: true,

  //右上角是否显示欢迎您文字
  personalPanelShowText: false,

  //是否隐藏tab页
  hideTabView: false,

  //全局scss变量
  globalVariablesPath:
    "./src/themes/theme-variables/frame-variables-default.scss",

  //svg图标目录
  svgFiles: ["src/assets/icons/svg/"],

  //不经过webpack编译的js,具体文件或所在文件夹
  excludeJs: [],

  //可以切换的主题,默认值["default", "blue", "dark", "gray","inkblue"]
  themeArr: ["default", "blue", "dark", "gray", "inkblue"],

  //默认主题名称,如果不在 themeArr 中,取 themeArr 第一个
  themeDefault: "default",

  //可以切换的echarts主题
  //默认值 ["default","light","dark",]
  //ECharts4 开始,除了一贯的默认主题外,新内置了两套主题,分别为 'light' 和 'dark',自定义主题另起名称
  echartsThemeArr: [
    "default",
    "light",
    "dark",
    "bigdata_dark",
    "bigdata_macarorns",
    "macarons",
  ],

  //echarts 默认主题名称,如果不在 echartsThemeArr 中,取 echartsThemeArr 第一个
  echartsThemeDefault: "default",

  //是否增加页面超时退出功能 默认false
  timeoutExit: false,
  //页面超时时间(s) 默认 30 * 60 * 1000 秒
  timeoutTime: null,

  //axios请求的basePath
  axiosBasePath: "",

  //后端服务代理
  proxy: {
    "/framework": {
      target: "http://localhost:8080/dap",
      changeOrigin: true,
    },
    "/login": {
      target: "http://localhost:8080/dap",
      changeOrigin: true,
    },
  },
};
