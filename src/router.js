import Vue from "vue";
import Router from "vue-router";
const Layout = () => import("./framework/views/layouts/index.vue");
// import RouterView from "./components/RouterView";
import settings from "./settings.js";

const {
  loginFile,
  consRoutes = [],
  routerMode = "hash",
  routerBase = "/",
} = settings;
const isProduction = process.env.NODE_ENV === "production";

const LoginView = getLoginView(loginFile);

Vue.use(Router);

export const constantRoutes = [
  {
    path: "/redirect",
    name: "layoutRedirect",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "index",
        name: "redirect",
        component: () =>
          import(
            /* webpackChunkName: "redirect" */ "@/framework/views/redirect/Redirect.vue"
          ),
        meta: {
          noCache: true,
        },
      },
    ],
  },
  // {
  //   path: "/logon",
  //   name: "Login",
  //   component: LoginView,
  //   hidden: true,
  // },
  ...consRoutes,
  {
    path: "/404",
    name: "exception404",
    component: () =>
      import(
        /* webpackChunkName: "fail" */ "@/framework/views/exception/404.vue"
      ),
    hidden: true,
  },
  {
    path: "/500",
    name: "exception500",
    component: () =>
      import(
        /* webpackChunkName: "fail" */ "@/framework/views/exception/500.vue"
      ),
    hidden: true,
  },
  {
    path: "/layout404",
    name: "layout404",
    component: Layout,
    children: [
      {
        path: "index",
        name: "layout404Index",
        component: () =>
          import(
            /* webpackChunkName: "fail" */ "@/framework/views/exception/404.vue"
          ),
        meta: { title: "404", openTarget: true },
        hidden: true,
      },
    ],
  },
  {
    path: "/tags404",
    name: "tags404",
    component: () =>
      import(
        /* webpackChunkName: "tagsfail" */ "./framework/views/TagsLayout/index.vue"
      ),
    children: [
      {
        path: "index",
        name: "tags404Index",
        component: () =>
          import(
            /* webpackChunkName: "fail" */ "@/framework/views/exception/404.vue"
          ),
        meta: { title: "404", openTarget: true },
        hidden: true,
      },
    ],
  },
  {
    path: "/401",
    name: "exception401",
    component: () =>
      import(
        /* webpackChunkName: "fail" */ "@/framework/views/exception/401.vue"
      ),
    hidden: true,
  },
];

const createRouter = () =>
  new Router({
    mode: isProduction ? routerMode : "hash", // require service support
    base: isProduction ? (routerMode == "history" ? routerBase : "/") : "/",
    scrollBehavior: () => ({ y: 0 }),
    routes: constantRoutes,
  });

const router = createRouter();

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter();
  router.matcher = newRouter.matcher; // reset router
}

function getLoginView(url) {
  return url
    ? url
    : () =>
        import(/* webpackChunkName: "login" */ "@/framework/views/Login.vue");
}

export default router;
