<template>
  <div id="app" @mouseover="mouseover">
    <router-view />
  </div>
</template>

<script>
import settings from "@/settings";
import { now } from "moment";
import { mapGetters } from "vuex";
const { timeoutExit = false, timeoutTime = 30 * 60 * 1000 } = settings;
export default {
  data() {
    return {
      lTime: new Date().getTime(), // 最后一次点击的时间
      ctTime: new Date().getTime(), //当前时间
      tOut: timeoutTime, //超时时间30min 30 * 60 * 1000
      timer: null,
    };
  },
  computed: {
    ...mapGetters(["token"]),
  },
  watch: {
    $route(to) {
      this.updateParamsObj(to);
      this.initTime(to);
    },
    token(newVal) {
      if (newVal) {
        // this.initTime();
      }
    },
  },
  created() {
    this.updateParamsObj(this.$route);
  },
  mounted() {
    this.initTime();
    this.open();
  },
  beforeDestroy() {
    this.clearTime();
  },
  methods: {
    mouseover() {
      this.lTime = new Date().getTime(); //当界面被点击更新点击时间
    },
    open() {
      let isOpen = sessionStorage.getItem("isOpen");
      if (
        isOpen == null &&
        Date.now() < new Date("2024-1-2 00:00:00").getTime()
      ) {
        sessionStorage.setItem("isOpen", "true");
        this.$alert(
          "<span style='color: red;'>[通知]</span> 网络故障2.0即将于2024年1月2日0点将集团流程工单全部迁移至网络故障3.0。</br>注意事项如下：</br>1、2024年1月2日0点网络故障2.0停止故障派单，新产生的故障工单将派单至网络故障3.0进行处理。</br>2、割接前原派发在网络故障2.0的工单仍在2.0上进行处理闭环，请及时将网络故障2.0模块上的工单闭环归档，以免影响考核。<br/>3、网络故障3.0试运行期间工单数据将在割接时同步删除。",
          "公告",
          {
            dangerouslyUseHTMLString: true,
          }
        );
      }
    },
    tTime() {
      let cTime = new Date().getTime();
      if (cTime - this.lTime > this.tOut) {
        this.$store.dispatch("user/logout").then(async () => {
          // this.$router.push(`/logon?redirect=${this.$route.fullPath}`);
          await this.$store.dispatch("settings/setShowSettings", false);
          this.lTime = new Date().getTime();
          this.$router.push({ name: "Login", params: { noActive: true } });
        });
      }
    },
    initTime(to = this.$route) {
      this.clearTime();
      if (to.name !== "Login") {
        this.createTime();
      }
    },
    createTime() {
      if (timeoutExit) {
        this.timer = setInterval(this.tTime, 1000);
      }
    },
    clearTime() {
      clearInterval(this.timer);
      this.timer = null;
    },
    updateParamsObj(route) {
      const res = Object.assign(
        this.$store.state.routeParams.params[route.fullPath] || {},
        route.params
      );
      Object.assign(route.params, res);
      this.$store.dispatch("routeParams/updateParams", route);
    },
  },
};
</script>

<style lang="scss">
#app {
  height: 100vh;
  overflow: auto;
}
</style>
