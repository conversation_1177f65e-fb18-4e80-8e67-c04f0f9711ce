const settings = require("@/settings");
const themes = require("@/utils/theme");

const layout = localStorage.getItem("layout");
// const fixedAside = localStorage.getItem("fixedAside");
const collapseAside = localStorage.getItem("collapseAside");
const sideShowLogo = localStorage.getItem("sideShowLogo");
const topShowLogo = localStorage.getItem("topShowLogo");
// const fixedHeader = localStorage.getItem("fixedHeader");
const topShowBreadcrumb = localStorage.getItem("topShowBreadcrumb");
const sideShowBreadcrumb = localStorage.getItem("sideShowBreadcrumb");
const hideTabView = localStorage.getItem("hideTabView");
const themeDefault = localStorage.getItem("themeDefault");
const echartsThemeDefault = localStorage.getItem("echartsThemeDefault");

module.exports = {
  // layout - 整体布局方式 ['sidemenu', 'topmenu'] 两种布局
  layout: layout ? layout : settings.layout,

  //是否可以切换整体布局方式
  singleLayout: settings.singleLayout,

  // 固定左侧菜单栏: boolean
  // fixedAside: fixedAside
  //   ? fixedAside === "true"
  //     ? true
  //     : false
  //   : settings.fixedAside,
  fixedAside: true,

  // 折叠左侧菜单栏: boolean
  collapseAside: collapseAside
    ? collapseAside === "true"
      ? true
      : false
    : settings.collapseAside,

  // 显示左侧logo: boolean
  sideShowLogo: sideShowLogo
    ? sideShowLogo === "true"
      ? true
      : false
    : typeof settings.sideShowLogo === "undefined"
    ? true
    : settings.sideShowLogo,

  // 显示顶部logo: boolean
  topShowLogo: topShowLogo
    ? topShowLogo === "true"
      ? true
      : false
    : typeof settings.topShowLogo === "undefined"
    ? true
    : settings.topShowLogo,

  // 固定 Header: boolean
  // fixedHeader: fixedHeader
  //   ? fixedHeader === "true"
  //     ? true
  //     : false
  //   : settings.fixedHeader,
  fixedHeader: true,

  // topmenu布局是否显示面包屑
  topShowBreadcrumb: topShowBreadcrumb
    ? topShowBreadcrumb === "true"
      ? true
      : false
    : typeof settings.topShowBreadcrumb === "undefined"
    ? true
    : settings.topShowBreadcrumb,

  // sidemenu布局是否显示面包屑
  sideShowBreadcrumb: sideShowBreadcrumb
    ? sideShowBreadcrumb === "true"
      ? true
      : false
    : typeof settings.sideShowBreadcrumb === "undefined"
    ? true
    : settings.sideShowBreadcrumb,

  //右上角是否显示欢迎您文字
  personalPanelShowText: settings.personalPanelShowText,

  // 是否隐藏tab页
  hideTabView: hideTabView
    ? hideTabView === "true"
      ? true
      : false
    : typeof settings.hideTabView === "undefined"
    ? false
    : settings.hideTabView,

  // 大屏页是否只有第一次打开全屏
  openOnceFullscreen:
    typeof settings.openOnceFullscreen === "undefined"
      ? false
      : settings.openOnceFullscreen,

  // 默认主题
  themeDefault: !themeDefault ? themes.defaultTheme : themeDefault,

  // echarts默认主题
  echartsThemeDefault: !echartsThemeDefault
    ? themes.defaultEchartsTheme
    : echartsThemeDefault,
};
