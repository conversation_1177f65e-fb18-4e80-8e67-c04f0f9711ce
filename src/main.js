import Vue from "vue";
import App from "./App.vue";
import router from "./router.js";
import "./permission.js";
import store from "./store/index.js";
import * as filters from "./utils/filter.js";

import "normalize.css/normalize.css"; // a modern alternative to CSS resets
import ElementUI from "element-ui";
// import "element-ui/lib/theme-chalk/index.css";
import "./utils/theme-import.js";
import "./themes/global.scss";
import "font-awesome/css/font-awesome.css";
Vue.use(ElementUI, { size: "small" });

import "./assets/icons/index.js";
import "./components/BaseIcon/index.js";

//自定义指令
import directives from "./utils/directives.js";
Vue.use(directives);

import * as axios from "./utils/axios.js";
Vue.prototype.$axios = axios;

import "./utils/version";

import BaiduMap from 'vue-baidu-map'
Vue.use(BaiduMap, {
  ak: 'SWi3uV0m014xqVFHSFkStCdAeL38AG1o'
})

Object.keys(filters).forEach(item => {
  Vue.filter(item, filters[item]);
});
Vue.config.productionTip = false;
new Vue({
  router,
  store,
  render: h => h(App),
}).$mount("#app");
