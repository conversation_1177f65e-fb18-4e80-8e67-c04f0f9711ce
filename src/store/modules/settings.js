// import variables from "@/themes/frame-variables.scss";
import defaultSettings from "@/defaultSettings";

const {
  layout,
  singleLayout,
  fixedAside,
  collapseAside,
  sideShowLogo,
  topShowLogo,
  fixedHeader,
  topShowBreadcrumb,
  sideShowBreadcrumb,
  personalPanelShowText,
  hideTabView,
  openOnceFullscreen,
  themeDefault,
  echartsThemeDefault,
} = defaultSettings;

const scssVars = require(`../../themes/theme-export/${themeDefault}.scss`);
let themeVarsList = {};
themeVarsList[themeDefault] = scssVars;

const state = () => ({
  // theme: variables.theme,
  layout: layout, // 布局
  singleLayout: singleLayout,
  // 侧边栏属性
  sidebar: {
    collapse: collapseAside,
    logo: sideShowLogo,
    fixed: fixedAside,
    sideShowBreadcrumb,
  },
  // topmenu布局是否显示logo
  topShowLogo: topShowLogo,
  // 是否固定header
  fixedHeader: fixedHeader,
  // 是否显示设置drawer
  showSettings: false,
  // 是否全屏
  fullScreen: false,
  // topmenu布局是否显示面包屑
  topShowBreadcrumb: topShowBreadcrumb,
  // 右上角是否显示欢迎您文字
  personalPanelShowText: personalPanelShowText,
  // 是否隐藏tab页
  hideTabView: hideTabView,
  // 大屏页是否只有第一次打开全屏
  openOnceFullscreen: openOnceFullscreen,
  // 当前主题名称
  themeName: themeDefault,
  // 不同主题的scss变量集合
  themeVarsList: themeVarsList,
  // 当前主题的scss变量
  themeVars: scssVars,
  // 当前echarts主题名称 主题设置用
  echartsThemeName: echartsThemeDefault,
  // 当前echarts主题名称 页面echarts用
  echartsName: echartsThemeDefault !== "default" ? echartsThemeDefault : "",
  // 页面内容区域样式
  frameStyle: {
    headerHeight: 0,
    wrapHeight: 0,
  },
});

const mutations = {
  TOGGLE_LAYOUT: (state, layout) => {
    state.layout = layout;
    localStorage.setItem("layout", layout);
  },
  TOGGLE_SIDEBAR_COLLAPSE: state => {
    state.sidebar.collapse = !state.sidebar.collapse;
    localStorage.setItem("collapseAside", state.sidebar.collapse);
  },
  TOGGLE_SIDEBAR_FIXED: state => {
    state.sidebar.fixed = !state.sidebar.fixed;
    localStorage.setItem("fixedAside", state.sidebar.fixed);
  },
  TOGGLE_FIXEDHEADER: state => {
    state.fixedHeader = !state.fixedHeader;
    localStorage.setItem("fixedHeader", state.fixedHeader);
  },
  TOGGLE_SHOWSETTINGS: state => {
    state.showSettings = !state.showSettings;
  },
  SET_SHOWSETTINGS: (state, showSettings) => {
    state.showSettings = showSettings;
  },
  TOGGLE_FULLSCREEN: (state, fullScreen) => {
    if (typeof fullScreen === "undefined") {
      state.fullScreen = !state.fullScreen;
    } else {
      state.fullScreen = fullScreen;
    }
  },
  toggleThemeName: (state, themeName) => {
    state.themeName = themeName;
    localStorage.setItem("themeDefault", themeName);
  },
  updateThemeVars: (state, themeVars) => {
    state.themeVars = themeVars;
  },
  toggleEchartsThemeName: (state, echartsThemeName) => {
    state.echartsThemeName = echartsThemeName;
    state.echartsName = echartsThemeName !== "default" ? echartsThemeName : "";
    localStorage.setItem("echartsThemeDefault", echartsThemeName);
  },
  UPDATE_FRAMESTYLE: (state, height) => {
    let headerHeight =
      (height == "auto" ? 0 : height.replace("px", "") - 0) + 16;
    let wrapHeight = window.innerHeight - headerHeight;
    state.frameStyle.headerHeight = headerHeight;
    state.frameStyle.wrapHeight = wrapHeight;
  },
};

const actions = {
  toggleLayoutMode({ commit }, layout) {
    commit("TOGGLE_LAYOUT", layout);
  },
  toggleSidebarCollapse({ commit }) {
    commit("TOGGLE_SIDEBAR_COLLAPSE");
  },
  toggleSidebarFixed({ commit }) {
    commit("TOGGLE_SIDEBAR_FIXED");
  },
  toggleFixedHeader({ commit }) {
    commit("TOGGLE_FIXEDHEADER");
  },
  toggleShowSettings({ commit }) {
    commit("TOGGLE_SHOWSETTINGS");
  },
  setShowSettings({ commit }, showSettings) {
    commit("SET_SHOWSETTINGS", showSettings);
  },
  toggleFullScreen({ commit }, fullScreen) {
    commit("TOGGLE_FULLSCREEN", fullScreen);
  },
  async toggleThemeName({ commit, state }, themeName) {
    if (Object.prototype.hasOwnProperty.call(themeVarsList, themeName)) {
      commit("updateThemeVars", state.themeVarsList[themeName]);
    } else {
      const vars = await import(`../../themes/theme-export/${themeName}.scss`);
      themeVarsList[themeName] = vars;
      commit("updateThemeVars", vars);
    }
    commit("toggleThemeName", themeName);
  },
  toggleEchartsThemeName({ commit }, echartsThemeName) {
    commit("toggleEchartsThemeName", echartsThemeName);
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
