const state = {
  params: {},
};

const mutations = {
  UPDATE_PARAMS: (state, route) => {
    if (route.name !== "redirect")
      state.params[route["fullPath"]] = route.params;
  },
  CLEAR_PARAMS: state => {
    state.params = {};
  },
};

const actions = {
  updateParams({ commit }, route) {
    commit("UPDATE_PARAMS", route);
  },
  clearParams({ commit }) {
    commit("CLEAR_PARAMS");
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
