import { constantRoutes } from "@/router";

const state = {
  routes: [],
  addRoutes: [],
};

const mutations = {
  SET_ROUTES: (state, addRoutes) => {
    state.addRoutes = addRoutes;
    state.routes = constantRoutes.concat(addRoutes);
  },
  RESET_ROUTES: state => {
    state.routes = constantRoutes.routes;
    state.addRoutes = [];
  },
};

export default {
  namespaced: true,
  state,
  mutations,
};
