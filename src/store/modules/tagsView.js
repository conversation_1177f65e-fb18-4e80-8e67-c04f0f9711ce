import settings from "@/settings";
import Vue from "vue";

function handleCachedView(state) {
  let newCached = [];
  state.visitedViews
    .filter(tag => !tag.meta.noCache)
    .forEach(element => {
      newCached.push(element.name);
    });
  return newCached;
}

function delViewsInstance(views, keepAliveInstance) {
  let cache = keepAliveInstance.cache; // 缓存的组件
  let keys = keepAliveInstance.keys; // 缓存的组件名
  if (!cache) return false;
  views.forEach(view => {
    const { fullPath: key } = view;
    if (cache[key] != null) {
      cache[key].componentInstance.$destroy();
      // cache[key] = null;
      Vue.delete(cache, key);
      let index = keys.indexOf(key);
      if (index > -1) {
        keys.splice(index, 1);
      }
    }
  });
}

const state = {
  visitedViews: [],
  cachedViews: [],
  isleave: [],
};

const mutations = {
  addIsLeave: (state, view) => {
    if (state.isleave.indexOf(view) == -1) {
      state.isleave.push(view);
    }
  },
  ADD_VISITED_VIEW: (state, view) => {
    const { params, query } = view;
    const title =
      params.frameTabTitle || query.frameTabTitle || view.meta.title;
    document.title = title + " - " + settings.title || "大数据平台";
    if (state.visitedViews.some(v => v.fullPath === view.fullPath)) {
      const tagIndex = state.visitedViews.findIndex(
        v => v.fullPath === view.fullPath
      );
      state.visitedViews[tagIndex] = Object.assign(
        {},
        state.visitedViews[tagIndex],
        view,
        {
          title: title,
        }
      );
      return;
    }
    state.visitedViews.push(
      Object.assign({}, view, {
        title: title || "no-name",
      })
    );
    //
    // if (state.isleave.indexOf(view.fullPath) == -1) {
    //   state.isleave.push(view.fullPath);
    // }
  },
  ADD_CACHED_VIEW: (state, view) => {
    if (state.cachedViews.includes(view.name)) return;
    if (!view.meta.noCache) {
      state.cachedViews.push(view.name);
    }
  },

  DEL_VISITED_VIEW: (state, { view, keepAliveInstance }) => {
    //页面init修改
    if (state.isleave.indexOf(view.fullPath) != -1) {
      state.isleave.splice(state.isleave.indexOf(view.fullPath), 1);
    }
    for (const [i, v] of state.visitedViews.entries()) {
      if (v.fullPath === view.fullPath) {
        state.visitedViews.splice(i, 1);
        break;
      }
    }
    /* const { fullPath: key } = view;
    let cache = keepAliveInstance.cache; // 缓存的组件
    let keys = keepAliveInstance.keys; // 缓存的组件名
    if (cache[key] != null) {
      cache[key].componentInstance.$destroy();
      cache[key] = null;
      let index = keys.indexOf(key);
      if (index > -1) {
        keys.splice(index, 1);
      }
    } */
    delViewsInstance([view], keepAliveInstance);
  },
  DEL_CACHED_VIEW: (state, view) => {
    for (const i of state.cachedViews) {
      if (i === view.name) {
        const index = state.cachedViews.indexOf(i);
        state.cachedViews.splice(index, 1);
        break;
      }
    }
  },

  DEL_OTHERS_VISITED_VIEWS: (state, { view, keepAliveInstance }) => {
    const delViews = state.visitedViews.filter(v => {
      return v.fullPath !== view.fullPath && !v.meta.affix;
    });
    delViewsInstance(delViews, keepAliveInstance);
    state.visitedViews = state.visitedViews.filter(v => {
      return v.meta.affix || v.fullPath === view.fullPath;
    });
    //页面init修改
    state.isleave = state.isleave.filter(v => {
      return v === view.fullPath;
    });
  },
  DEL_OTHERS_CACHED_VIEWS: (state /* , view */) => {
    /* for (const i of state.cachedViews) {
      if (i === view.name) {
        const index = state.cachedViews.indexOf(i);
        state.cachedViews = state.cachedViews.slice(index, index + 1);
        break;
      }
    } */
    state.cachedViews = handleCachedView(state);
  },

  DEL_ALL_VISITED_VIEWS: state => {
    // keep affix tags
    // const affixTags = state.visitedViews.filter(tag => tag.meta.affix);
    // state.visitedViews = affixTags;
    state.visitedViews = [];
  },
  DEL_ALL_CACHED_VIEWS: (state /* , view */) => {
    // state.cachedViews = [];
    state.cachedViews = handleCachedView(state);
  },

  UPDATE_VISITED_VIEW: (state, view) => {
    for (let v of state.visitedViews) {
      if (v.fullPath === view.fullPath) {
        v = Object.assign(v, view);
        break;
      }
    }
  },

  DEL_LEFT_VISITED_VIEWS: (state, { view, keepAliveInstance }) => {
    let ahind = []; //前面
    let behind = []; //后面
    for (const [i, v] of state.visitedViews.entries()) {
      if (v.fullPath === view.fullPath) {
        behind = state.visitedViews.slice(i);
        ahind = state.visitedViews.slice(0, i);
        break;
      }
    }
    const delViews = ahind.filter(tag => !tag.meta.affix);
    delViewsInstance(delViews, keepAliveInstance);
    ahind = ahind.filter(tag => tag.meta.affix);
    state.visitedViews = ahind.concat(behind);
    //页面init修改
    let ahind2 = []; //前面
    let behind2 = []; //后面
    for (const [i, v] of state.isleave.entries()) {
      if (v.fullPath === view.fullPath) {
        behind2 = state.isleave.slice(i);
        ahind2 = state.isleave.slice(0, i);
        break;
      }
    }
    ahind2 = ahind2.filter(tag => tag.meta.affix);
    state.isleave = ahind2.concat(behind2);
  },
  DEL_LEFT_CACHED_VIEWS: state => {
    state.cachedViews = handleCachedView(state);
  },

  DEL_RIGHT_VISITED_VIEWS: (state, { view, keepAliveInstance }) => {
    let ahind = []; //前面
    let behind = []; //后面
    for (const [i, v] of state.visitedViews.entries()) {
      if (v.fullPath === view.fullPath) {
        ahind = state.visitedViews.slice(0, i + 1);
        behind = state.visitedViews.slice(i + 1);
        break;
      }
    }
    const delViews = behind.filter(tag => !tag.meta.affix);
    delViewsInstance(delViews, keepAliveInstance);
    behind = behind.filter(tag => tag.meta.affix);
    state.visitedViews = ahind.concat(behind);
    //页面init修改
    let ahind2 = []; //前面
    let behind2 = []; //后面
    for (const [i, v] of state.isleave.entries()) {
      if (v.fullPath === view.fullPath) {
        ahind2 = state.isleave.slice(0, i + 1);
        behind2 = state.isleave.slice(i + 1);
        break;
      }
    }
    behind2 = behind2.filter(tag => tag.meta.affix);
    state.isleave = ahind2.concat(behind2);
  },
  DEL_RIGHT_CACHED_VIEWS: (state /* , view */) => {
    state.cachedViews = handleCachedView(state);
  },
};

const actions = {
  addView({ dispatch }, view) {
    dispatch("addVisitedView", view);
    dispatch("addCachedView", view);
  },
  addVisitedView({ commit }, view) {
    commit("ADD_VISITED_VIEW", view);
  },
  addCachedView({ commit }, view) {
    commit("ADD_CACHED_VIEW", view);
  },

  async delView(
    {
      dispatch,
      state,
      rootState: {
        keepAlive: { instance: keepAliveInstance },
      },
    },
    view
  ) {
    await dispatch("delVisitedView", { view, keepAliveInstance });
    await dispatch("delCachedView", view);
    return {
      visitedViews: [...state.visitedViews],
      cachedViews: [...state.cachedViews],
    };
  },
  delVisitedView({ commit, state }, view) {
    return new Promise(resolve => {
      commit("DEL_VISITED_VIEW", view);
      resolve([...state.visitedViews]);
    });
  },
  delCachedView({ commit, state }, view) {
    return new Promise(resolve => {
      commit("DEL_CACHED_VIEW", view);
      resolve([...state.cachedViews]);
    });
  },

  async delOthersViews(
    {
      dispatch,
      state,
      rootState: {
        keepAlive: { instance: keepAliveInstance },
      },
    },
    view
  ) {
    await dispatch("delOthersVisitedViews", { view, keepAliveInstance });
    await dispatch("delOthersCachedViews", view);
    return {
      visitedViews: [...state.visitedViews],
      cachedViews: [...state.cachedViews],
    };
  },
  delOthersVisitedViews({ commit, state }, view) {
    return new Promise(resolve => {
      commit("DEL_OTHERS_VISITED_VIEWS", view);
      resolve([...state.visitedViews]);
    });
  },
  delOthersCachedViews({ commit, state }, view) {
    return new Promise(resolve => {
      commit("DEL_OTHERS_CACHED_VIEWS", view);
      resolve([...state.cachedViews]);
    });
  },

  async delAllViews({ dispatch, state }, view) {
    await dispatch("delAllVisitedViews", view);
    await dispatch("delAllCachedViews", view);
    return {
      visitedViews: [...state.visitedViews],
      cachedViews: [...state.cachedViews],
    };
  },
  delAllVisitedViews({ commit, state }) {
    return new Promise(resolve => {
      commit("DEL_ALL_VISITED_VIEWS");
      resolve([...state.visitedViews]);
    });
  },
  delAllCachedViews({ commit, state }) {
    return new Promise(resolve => {
      commit("DEL_ALL_CACHED_VIEWS");
      resolve([...state.cachedViews]);
    });
  },

  updateVisitedView({ commit }, view) {
    commit("UPDATE_VISITED_VIEW", view);
  },

  async delLeftViews(
    {
      dispatch,
      state,
      rootState: {
        keepAlive: { instance: keepAliveInstance },
      },
    },
    view
  ) {
    await dispatch("delLeftVisitedViews", { view, keepAliveInstance });
    await dispatch("delLeftCachedViews", view);
    return {
      visitedViews: [...state.visitedViews],
      cachedViews: [...state.cachedViews],
    };
  },
  delLeftVisitedViews({ commit, state }, view) {
    return new Promise(resolve => {
      commit("DEL_LEFT_VISITED_VIEWS", view);
      resolve({
        visitedViews: [...state.visitedViews],
        cachedViews: [...state.cachedViews],
      });
    });
  },
  delLeftCachedViews({ commit, state }, view) {
    return new Promise(resolve => {
      commit("DEL_LEFT_CACHED_VIEWS", view);
      resolve({
        visitedViews: [...state.visitedViews],
        cachedViews: [...state.cachedViews],
      });
    });
  },

  async delRightViews(
    {
      dispatch,
      state,
      rootState: {
        keepAlive: { instance: keepAliveInstance },
      },
    },
    view
  ) {
    await dispatch("delRightVisitedViews", { view, keepAliveInstance });
    await dispatch("delRightCachedViews", view);
    return {
      visitedViews: [...state.visitedViews],
      cachedViews: [...state.cachedViews],
    };
  },
  delRightVisitedViews({ commit, state }, view) {
    return new Promise(resolve => {
      commit("DEL_RIGHT_VISITED_VIEWS", view);
      resolve({
        visitedViews: [...state.visitedViews],
        cachedViews: [...state.cachedViews],
      });
    });
  },
  delRightCachedViews({ commit, state }, view) {
    return new Promise(resolve => {
      commit("DEL_RIGHT_CACHED_VIEWS", view);
      resolve({
        visitedViews: [...state.visitedViews],
        cachedViews: [...state.cachedViews],
      });
    });
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
