const state = {
  dict: {},
  dropList: {},
};

const mutations = {
  CHANGE_DICT: (state, dict) => {
    state.dict = dict;
  },
  CHANGE_DROPLIST: (state, drop) => {
    state.dropList[drop.field] = drop.list;
  },
};

const actions = {
  updatedict({ commit }, dict) {
    commit("CHANGE_DICT", dict);
  },
  updatedrop({ commit }, drop) {
    commit("CHANGE_DROPLIST", drop);
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
