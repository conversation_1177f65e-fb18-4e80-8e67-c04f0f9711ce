import { login /* , logout */ } from "@/framework/api/login";
import { getToken, setToken, refreshToken } from "@/utils/auth";
import { resetRouter } from "@/router";
import crypto from "crypto";
import { sm3 } from "sm-crypto";
const state = {
  token: getToken(),
  userInfo: {},
  roles: [],
  menus: [],
};

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token;
  },
  SET_USERINFO: (state, info) => {
    state.userInfo = info;
  },
};

const actions = {
  login({ commit }, loginData) {
    var md5 = crypto.createHash("md5");
    md5.update(loginData.checkPass);
    var p = md5.digest("hex");

    let pwd = sm3(loginData.checkPass);
    return new Promise((resolve, reject) => {
      var param = {
        u: loginData.account,
        p: pwd,
        p2: p,
        c: loginData.verifyCode,
        r: loginData.rnd,
      };
      login(param)
        .then(res => {
          if (res.status === "Succeed") {
            commit("SET_TOKEN", res.data);
            setToken(res.data);
            if (res.msg != "") {
              let userInfo = JSON.parse(res.msg);
              commit("SET_USERINFO", userInfo);
              sessionStorage.setItem("userInfo", res.msg);
            }
          }
          refreshToken();
          resolve(res);
        })
        .catch(res => {
          if (res.status == "PasswordExpired") {
            commit("SET_TOKEN", res.data);
            setToken(res.data);
          }
          reject(res);
        });
    });
  },
  async logout({ dispatch, commit }) {
    commit("SET_TOKEN", "");
    commit("SET_USERINFO", {});
    sessionStorage.removeItem("userInfo");
    commit("permission/RESET_ROUTES", null, { root: true });
    await dispatch("tagsView/delAllViews", null, { root: true });
    await dispatch("routeParams/clearParams", null, { root: true });
    // removeToken();
    resetRouter();
    // logout(getToken())
    //   .then(() => {
    //     commit("SET_TOKEN", "");
    //     removeToken();
    //     resolve();
    //   })
    //   .catch(error => {
    //     reject(error);
    //   });
  },
  getUserInfo() {},

  getMenu() {},
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
