export const mixin = {
  // data() {
  //   var checkLength = (rule, value, callback) => {
  //     if (value.length > rule.max) {
  //       setTimeout(() => {
  //         this.$refs[rule.form].clearValidate(rule.field);
  //       }, 5000);
  //       callback(new Error(rule.messsage))
  //     } else {
  //       callback();
  //     }
  //   };
  // },
  methods: {
    checkLength(rule, value, callback) {
      if (value != null && value != "") {
        if (value.length >= rule.max) {
          setTimeout(() => {
            // this.$message.error("1");
            this.$refs[rule.form].clearValidate(rule.field);
            // this.$message.error("2");
          }, 5000);
          callback(new Error(rule.messsage));
        } else {
          callback();
        }
      } else {
        callback();
      }
    },
    uncheck(errorList) {
      var valid1 = true;
      for (var i in errorList) {
        if (errorList[i][0].message != "已超过填写字数上限") {
          valid1 = false;
          break;
        }
      }
      return valid1;
    },
  },
}