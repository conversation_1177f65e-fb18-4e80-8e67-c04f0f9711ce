#user  nobody;
worker_processes 2;
#error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;
#pid        logs/nginx.pid;
events {
  worker_connections 1024;
}
http {
  include mime.types;
  default_type application/octet-stream;
  #log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
  #                  '$status $body_bytes_sent "$http_referer" '
  #                  '"$http_user_agent" "$http_x_forwarded_for"';
  #access_log  logs/access.log  main;
  sendfile on;
  #tcp_nopush     on;
  #keepalive_timeout  0;
  keepalive_timeout 65;
  client_max_body_size 50m;
  #代理websocket map $http_upgrade $connection_upgrade {
  #default upgrade;
  #'' close;
  #}
  #gzip  on;
  gzip on;
  #开启或关闭gzip on off
  gzip_disable "msie6";
  #不使用gzip IE6
  gzip_min_length 100k;
  #gzip压缩最小文件大小，超出进行压缩（自行调节）
  gzip_buffers 4 16k;
  #buffer 不用修改
  gzip_comp_level 4;
  #压缩级别:1-10，数字越大压缩的越好，时间也越长，建议4
  gzip_types text/plain
    application/x-javascript
    application/javascript
    text/css
    application/xml
    text/javascript
    application/x-httpd-php
    application/json
    image/jpeg
    image/gif
    image/png;
  #  压缩文件类型
  gzip_vary on;
  #给CDN和代理服务器使用，针对相同url，可以根据头信息返回压缩和非压缩副本

  upstream backend {
    keepalive 100;
    server *************:9664 max_fails=4 weight=1 fail_timeout=5s;
  }

  upstream netfm3_gateway {
    server netfm3-gateway-svc:8000 weight=1;
  }

  server {
    listen 9080;
    server_name localhost;
    add_header Access-Control-Allow-Origin http://************;
    #charset koi8-r;
    #access_log  logs/host.access.log  main;

    # 对接监控平台
    location ^~ /tianyan {
      proxy_pass http://backend;
      proxy_connect_timeout 1s;
      proxy_read_timeout 1s;
      proxy_send_timeout 1s;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
    location ^~ /prod-nfm3/tianyan {
      proxy_pass http://backend/tianyan/;
      proxy_connect_timeout 1s;
      proxy_read_timeout 1s;
      proxy_send_timeout 1s;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    location /login {
      proxy_pass http://netfm3_gateway/netfm3-backbone-flow/login;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
    }
    location /commApi/v1/login {
      proxy_pass http://netfm3_gateway/netfm3-backbone-flow/login;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
    }
    location /framework {
      proxy_pass http://netfm3_gateway/netfm3-backbone-flow/framework;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
    }
    location /backbone {
      proxy_pass http://netfm3_gateway/netfm3-backbone-flow/backbone;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }
    location /commonFlow {
      proxy_pass http://netfm3_gateway/netfm3-common-flow/commonFlow;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }
    location /commApi/v1 {
      proxy_pass http://netfm3_gateway/netfm3-backbone-flow/backbone;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }
    location /commApi/v1/process {
      proxy_pass http://netfm3_gateway/netfm3-api/commApi/v1/process;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }
    location /commApi/v1/attach/ {
      proxy_pass http://netfm3_gateway/netfm3-common/attach/;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }
    location /commApi/v1/enum/ {
      proxy_pass http://netfm3_gateway/netfm3-common/enum/;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }
    location /commCloud {
      proxy_pass http://netfm3_gateway/netfm3-commcloud-flow/commCloud;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }
    location /province {
      proxy_pass http://netfm3_gateway/netfm3-province-flow/province;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }

    location /common/monitor/ {
      proxy_pass http://netfm3_gateway/netfm3-common/monitor/;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }

    location /commonDict/enum/ {
      proxy_pass http://netfm3_gateway/netfm3-common/enum/;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }

    location /commonDict/info/ {
      proxy_pass http://netfm3_gateway/netfm3-common/info/;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }

    location /commonDict/attach/ {
      proxy_pass http://netfm3_gateway/netfm3-common/attach/;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }

    location /commonDict/analyze/ {
      proxy_pass http://netfm3_gateway/netfm3-common/analyze/;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }

    location /resweb_jituan {
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header REMOTE-HOST $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_pass http://************;
    }

    location /resweb/ {
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header REMOTE-HOST $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_pass http://************/resweb_jituan/;
    }

    location /uflow-api {
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header REMOTE-HOST $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_pass http://************;
    }

    location /netfm3topo/ {
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header REMOTE-HOST $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_pass http://**************:1502/;
    }

    location /topoapi {
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header REMOTE-HOST $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_pass http://**************:1502;
    }

    location /chatOps/chatUrl {
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header REMOTE-HOST $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_pass http://**************:8888/chatOps/chatUrl;
    }
    location /uod {
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header REMOTE-HOST $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_pass http://**************:7300/uod;
    }

    location /prod-nfm3/login {
      proxy_pass http://netfm3_gateway/netfm3-backbone-flow/login;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
    }
    location /prod-nfm3/commApi/v1/login {
      proxy_pass http://netfm3_gateway/netfm3-backbone-flow/login;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
    }
    location /prod-nfm3/commApi/v1 {
      proxy_pass http://netfm3_gateway/netfm3-backbone-flow/backbone;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }
    location /prod-nfm3/commApi/v1/process {
      proxy_pass http://netfm3_gateway/netfm3-api/commApi/v1/process;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }
    location /prod-nfm3/commApi/v1/enum/ {
      proxy_pass http://netfm3_gateway/netfm3-common/enum/;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }
    location /prod-nfm3/commApi/v1/attach/ {
      proxy_pass http://netfm3_gateway/netfm3-common/attach/;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }

    location /prod-nfm3/framework {
      proxy_pass http://netfm3_gateway/netfm3-backbone-flow/framework;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
    }
    location /prod-nfm3/backbone {
      proxy_pass http://netfm3_gateway/netfm3-backbone-flow/backbone;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }
    location /prod-nfm3/commonFlow {
      proxy_pass http://netfm3_gateway/netfm3-common-flow/commonFlow;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }
    location /prod-nfm3/superviseFlow {
      proxy_pass http://netfm3_gateway/netfm3-supervise-flow/supervise;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }
    location /prod-nfm3/ipMajorFlow {
      proxy_pass http://netfm3_gateway/netfm3-ip-major-flow/ipMajorFlow;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }
    location /prod-nfm3/commCloud {
      proxy_pass http://netfm3_gateway/netfm3-commcloud-flow/commCloud;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }
    location /prod-nfm3/province {
      proxy_pass http://netfm3_gateway/netfm3-province-flow/province;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }
    location /prod-nfm3/common/monitor/ {
      proxy_pass http://netfm3_gateway/netfm3-common/monitor/;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }
    location /prod-nfm3/commonDict/enum/ {
      proxy_pass http://netfm3_gateway/netfm3-common/enum/;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }

    location /prod-nfm3/commonDict/info/ {
      proxy_pass http://netfm3_gateway/netfm3-common/info/;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }

    location /prod-nfm3/commonDict/attach/ {
      proxy_pass http://netfm3_gateway/netfm3-common/attach/;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }

    location /prod-nfm3/commonDict/analyze/ {
      proxy_pass http://netfm3_gateway/netfm3-common/analyze/;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }

    location /prod-nfm3/resweb_jituan {
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header REMOTE-HOST $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_pass http://************;
    }

    location /prod-nfm3/resweb/ {
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header REMOTE-HOST $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_pass http://************/resweb_jituan/;
    }

    location /prod-nfm3/uflow-api/ {
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header REMOTE-HOST $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_pass http://************/uflow-api/;
    }

    location /prod-nfm3/netfm3topo/ {
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header REMOTE-HOST $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_pass http://**************:1502/;
    }

    location /prod-nfm3/topoapi/ {
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header REMOTE-HOST $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_pass http://**************:1502/topoapi/;
    }

    location /prod-nfm3/chatOps/chatUrl {
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header REMOTE-HOST $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_pass http://**************:8888/chatOps/chatUrl;
    }

    location /prod-nfm3/uod/ {
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header REMOTE-HOST $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_pass http://**************:7300/uod/;
    }
    location /gt {
      proxy_pass http://netfm3_gateway/netfm3-gt-flow/gt;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }
    location /prod-nfm3/commonprovince {
      proxy_pass http://netfm3_gateway/netfm3-province-common-flow/commonprovince;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }
    location /prod-nfm3/integration {
      proxy_pass http://netfm3_gateway/netfm3-common-integration/integration;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }
    location /prod-nfm3/commonIntegration {
      proxy_pass http://netfm3_gateway/netfm3-common-integration;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }
    location /prod-nfm3/interface {
      proxy_pass http://netfm3_gateway/netfm3-province-common-interface/interface;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }
    location /prod-nfm3/gt {
      proxy_pass http://netfm3_gateway/netfm3-gt-flow/gt;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }
    location /prod-nfm3/netfm3-gt-dispatch {
      proxy_pass http://netfm3_gateway/netfm3-gt-dispatch/netfm3-gt-dispatch;
      proxy_set_header Host $host:8088;
      proxy_set_header REMOTE-HOST $remote_addr;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header Via "nginx";
    }
    location /prod-nfm3/netfm3-dispatch {
      proxy_pass http://netfm3_gateway/netfm3-dispatch/netfm3-dispatch;
      proxy_set_header Host $host:8088;
      proxy_set_header REMOTE-HOST $remote_addr;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header Via "nginx";
    }
    location /prod-nfm3/ser {
      proxy_pass http://netfm3_gateway/netfm3-province-common-interface/ser;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }
    location /prod-nfm3/commonDict/participant/ {
      proxy_pass http://netfm3_gateway/netfm3-common/participant/;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }

    location /prod-nfm3/QRcode {
      proxy_pass https://cos.zz-hqc-z01.cos.tg.ncmp.unicom.local/449140254809:netfm3/img/QRcode;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }

    location /prod-nfm3/ws {
      proxy_pass http://netfm3_gateway/netfm3-province-common-interface/ws;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }

    location /prod-nfm3/fed-collect/fedCollect {
      proxy_pass http://netfm3_gateway/netfm3-fed-collect/fedCollect;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header Host $host;
      proxy_redirect default;
      proxy_http_version 1.1;
      proxy_set_header Connection "";
      proxy_connect_timeout 600;
      proxy_read_timeout 600;
      proxy_send_timeout 600;
    }
    # location /{
    #   root /etc/nginx/html;
    #   index index.html index.htm;
    #   if    (!-e    $request_filename){
    #     rewrite ^(.*)$ /index.html?s=$1 last;
    #   }
    # }
    location /prod-nfm3 {
      alias /etc/nginx/html/nfm3/;
      index index.html index.htm;
      try_files $uri $uri/ /index.html =404;
    }

    #error_page404              /404.html;
    # redirect server error pages to the static page /50x.html
    # error_page 500 502 503 504 /50x.html;
    location = /50x.html {
      root html;
    }
  }
}
